using System;
using System.Collections;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Windows.Forms;

namespace DCSDEV.DCSDesigner
{
	/// <summary>
	/// Summary description for DCSRotationProperties.
	/// </summary>
    internal class DCSRotationProperties : System.Windows.Forms.UserControl
	{
		private System.Drawing.RotateFlipType m_rotateFlip = System.Drawing.RotateFlipType.RotateNoneFlipNone;

		private System.Windows.Forms.PictureBox pictureBox1;
		private System.Windows.Forms.ImageList imageList1;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.ToolTip toolTip1;
		private System.ComponentModel.IContainer components;

		public DCSRotationProperties()
		{
			// This call is required by the Windows.Forms Form Designer.
			InitializeComponent();

			// TODO: Add any initialization after the InitializeComponent call
			this.toolTip1.SetToolTip(this.pictureBox1, "Click on arrow head to rotate the image inside its frame.");
		}

		/// <summary> 
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Component Designer generated code
		/// <summary> 
		/// Required method for Designer support - do not modify 
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			this.components = new System.ComponentModel.Container();
			System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(DCSRotationProperties));
			this.pictureBox1 = new System.Windows.Forms.PictureBox();
			this.imageList1 = new System.Windows.Forms.ImageList(this.components);
			this.label1 = new System.Windows.Forms.Label();
			this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
			((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
			this.SuspendLayout();
			// 
			// pictureBox1
			// 
			this.pictureBox1.Image = ((System.Drawing.Image)(resources.GetObject("pictureBox1.Image")));
			this.pictureBox1.Location = new System.Drawing.Point(16, 18);
			this.pictureBox1.Name = "pictureBox1";
			this.pictureBox1.Size = new System.Drawing.Size(50, 48);
			this.pictureBox1.TabIndex = 0;
			this.pictureBox1.TabStop = false;
			this.pictureBox1.MouseMove += new System.Windows.Forms.MouseEventHandler(this.pictureBox1_MouseMove);
			this.pictureBox1.MouseUp += new System.Windows.Forms.MouseEventHandler(this.pictureBox1_MouseUp);
			// 
			// imageList1
			// 
			this.imageList1.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList1.ImageStream")));
			this.imageList1.TransparentColor = System.Drawing.Color.White;
			this.imageList1.Images.SetKeyName(0, "");
			this.imageList1.Images.SetKeyName(1, "");
			this.imageList1.Images.SetKeyName(2, "");
			this.imageList1.Images.SetKeyName(3, "");
			// 
			// label1
			// 
			this.label1.Location = new System.Drawing.Point(0, 0);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(80, 15);
			this.label1.TabIndex = 1;
			this.label1.Text = "Orientation";
			this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
			// 
			// DCSRotationProperties
			// 
			this.Controls.Add(this.label1);
			this.Controls.Add(this.pictureBox1);
			this.Name = "DCSRotationProperties";
			this.Size = new System.Drawing.Size(79, 73);
			((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
			this.ResumeLayout(false);

		}
		#endregion

		private System.Drawing.RotateFlipType RealizeRotation(System.Drawing.RotateFlipType rotateFlipIn)
		{
			System.Drawing.RotateFlipType rotateFlip = rotateFlipIn;
			if (rotateFlip == System.Drawing.RotateFlipType.RotateNoneFlipNone) this.pictureBox1.Image = this.imageList1.Images[0];
			else if (rotateFlip == System.Drawing.RotateFlipType.Rotate90FlipNone) this.pictureBox1.Image = this.imageList1.Images[1];
			else if (rotateFlip == System.Drawing.RotateFlipType.Rotate180FlipNone) this.pictureBox1.Image = this.imageList1.Images[2];
			else if (rotateFlip == System.Drawing.RotateFlipType.Rotate270FlipNone) this.pictureBox1.Image = this.imageList1.Images[3];
			else 
			{
				rotateFlip = System.Drawing.RotateFlipType.RotateNoneFlipNone;
				this.pictureBox1.Image = this.imageList1.Images[0];
			}
			return rotateFlip;
		}

		private int GetRotationPoint(Point point)
		{
			// Control is 48x48, centers are at 12,24 36,24, 24,12 24,36, size should be 12x12
			Point[] points = {	new Point(24-6,12-6), 
								 new Point(36-6,24-6), 
								 new Point(24-6,36-6),
								 new Point(12-6,24-6)}; 

			Rectangle rectBase = new Rectangle(0,0,12,12);
			Rectangle rect;
			for (int i=0; i<4; i++)
			{
				rect = rectBase;
				rect.Offset(points[i]);
				if (rect.Contains(point)) return i ;
			}
			return -1;
		}

		private void pictureBox1_MouseUp(object sender, System.Windows.Forms.MouseEventArgs e)
		{
			Point point = new Point(e.X,e.Y);
			int i = GetRotationPoint(point);
			if (i == -1) return;

			RotateFlipType[] rotations = {	RotateFlipType.RotateNoneFlipNone,
											 RotateFlipType.Rotate90FlipNone,
											 RotateFlipType.Rotate180FlipNone,
											 RotateFlipType.Rotate270FlipNone};

			m_rotateFlip = rotations[i];
			RealizeRotation(m_rotateFlip);
			return;
		}

		private void pictureBox1_MouseMove(object sender, System.Windows.Forms.MouseEventArgs e)
		{
			Point point = new Point(e.X,e.Y);
			int i = GetRotationPoint(point);
			if (i != -1) this.Cursor = Cursors.SizeAll;
			else this.Cursor = Cursors.Default;
		}

		public System.Drawing.RotateFlipType RotateFlip
		{
			get { return m_rotateFlip; }
			set { m_rotateFlip = RealizeRotation(value); }
		}

		public void Rotate90()
		{
			if (m_rotateFlip == System.Drawing.RotateFlipType.RotateNoneFlipNone) m_rotateFlip = System.Drawing.RotateFlipType.Rotate90FlipNone;
			else if (m_rotateFlip == System.Drawing.RotateFlipType.Rotate90FlipNone) m_rotateFlip = System.Drawing.RotateFlipType.Rotate180FlipNone;
			else if (m_rotateFlip == System.Drawing.RotateFlipType.Rotate180FlipNone) m_rotateFlip = System.Drawing.RotateFlipType.Rotate270FlipNone;
			else if (m_rotateFlip == System.Drawing.RotateFlipType.Rotate270FlipNone) m_rotateFlip = System.Drawing.RotateFlipType.RotateNoneFlipNone;
			RealizeRotation(m_rotateFlip);
		}

		public void Rotate270()
		{
			if (m_rotateFlip == System.Drawing.RotateFlipType.RotateNoneFlipNone) m_rotateFlip = System.Drawing.RotateFlipType.Rotate270FlipNone;
			else if (m_rotateFlip == System.Drawing.RotateFlipType.Rotate90FlipNone) m_rotateFlip = System.Drawing.RotateFlipType.RotateNoneFlipNone;
			else if (m_rotateFlip == System.Drawing.RotateFlipType.Rotate180FlipNone) m_rotateFlip = System.Drawing.RotateFlipType.Rotate90FlipNone;
			else if (m_rotateFlip == System.Drawing.RotateFlipType.Rotate270FlipNone) m_rotateFlip = System.Drawing.RotateFlipType.Rotate180FlipNone;
			RealizeRotation(m_rotateFlip);
		}
	}
}
