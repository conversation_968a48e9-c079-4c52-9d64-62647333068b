// Microsoft Visual C++ generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include "afxres.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE DISCARDABLE 
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE DISCARDABLE 
BEGIN
    "#include ""afxres.h""\r\n"
    "\0"
END

3 TEXTINCLUDE DISCARDABLE 
BEGIN
    "#define _AFX_NO_SPLITTER_RESOURCES\r\n"
    "#define _AFX_NO_OLE_RESOURCES\r\n"
    "#define _AFX_NO_TRACKER_RESOURCES\r\n"
    "#define _AFX_NO_PROPERTY_RESOURCES\r\n"
	"\r\n"
	"#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)\r\n"
	"#ifdef _WIN32\r\n"
	"LANGUAGE 9, 1\r\n"
	"#pragma code_page(1252)\r\n"
	"#endif //_WIN32\r\n"
	"#include ""res\\FingerBiometricIF.rc2""  // non-Microsoft Visual C++ edited resources\r\n"
	"#include ""afxres.rc""  	// Standard components\r\n"
	"#endif\r\n"
    "\0"
END

/////////////////////////////////////////////////////////////////////////////
#endif    // APSTUDIO_INVOKED




#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
#ifdef _WIN32
LANGUAGE 9, 1
#pragma code_page(1252)
#endif //_WIN32

/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO     VERSIONINFO
  FILEVERSION       1,0,0,1
  PRODUCTVERSION    1,0,0,1
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x4L
 FILETYPE 0x2L
 FILESUBTYPE 0x0L
BEGIN
	BLOCK "StringFileInfo"
	BEGIN
        BLOCK "040904B0"
		BEGIN 
			VALUE "CompanyName",     "\0"
			VALUE "FileDescription", "FingerBiometricIF DLL\0"
			VALUE "FileVersion",     "1, 0, 0, 1\0"
			VALUE "InternalName",    "FingerBiometricIF\0"
			VALUE "LegalCopyright",  "Copyright (C) 2006\0"
			VALUE "LegalTrademarks", "\0"
			VALUE "OriginalFilename","FingerBiometricIF.DLL\0"
			VALUE "ProductName",     "FingerBiometricIF Dynamic Link Library\0"
			VALUE "ProductVersion",  "1, 0, 0, 1\0"
		END
	END
	BLOCK "VarFileInfo" 
	BEGIN 
		VALUE "Translation", 0x409, 1200
    END
END

#endif

#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//
#define _AFX_NO_SPLITTER_RESOURCES
#define _AFX_NO_OLE_RESOURCES
#define _AFX_NO_TRACKER_RESOURCES
#define _AFX_NO_PROPERTY_RESOURCES

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
#ifdef _WIN32
LANGUAGE 9, 1
#pragma code_page(1252)
#endif //_WIN32
#include "res\\FingerBiometricIF.rc2"  // non-Microsoft Visual C++ edited resources
#include "afxres.rc"  	// Standard components
#endif

/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED

