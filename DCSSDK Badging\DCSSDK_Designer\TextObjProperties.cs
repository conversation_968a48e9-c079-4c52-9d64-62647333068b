using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;

using DCSDEV;
namespace DCSDEV.DCSDesigner
{
	/// <summary>
	/// Summary description for TextObjProperties.
	/// </summary>
    internal class TextObjProperties : System.Windows.Forms.Form
	{
		private DCSDEV.DCSDatatypes m_dummy = new DCSDEV.DCSDatatypes();
		private ArrayList m_DesignObjectsSelected;
		private DCSDEV.DCSDesigner.DCSDesignerView m_view;
		private DCSDEV.DCSDesign.DCSDesign m_design;
		private DCSSDK.DCSBarcodeIF.DCSBarcodeTypesList m_barcodeTypesList;

		private System.Windows.Forms.Button buttonCancel;
		private System.Windows.Forms.Button buttonAccept;
		private System.Windows.Forms.Button buttonApply;
		private DCSDEV.DCSDesigner.DCSSourceProperties dcsSourceProperties1;
		private System.Windows.Forms.TabControl tabControl1;
		private System.Windows.Forms.TabPage tabPage1;
        private DCSDEV.DCSDesigner.DCSFontProperties dcsFontProperties1;
		private System.Windows.Forms.TabPage tabPage2;
		private DCSDEV.DCSDesigner.DCSBackGroundProperties dcsBackGroundProperties1;
        private DCSDEV.DCSDesigner.DCSAlignmentProperties dcsAlignmentProperties1;
		private System.Windows.Forms.TabPage tabPage3;
        private DCSDEV.DCSDesigner.DCSFrameProperties1 dcsFrameProperties1;
        private DCSDEV.DCSDesigner.DCSPositionSizeProperties dcsPositionSizeProperties1;
		private System.Windows.Forms.Button buttonLockAspectRatio;
		private System.Windows.Forms.TextBox tbRatio;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.ImageList imageList1;
		private System.Windows.Forms.Label labelBarcodeType;
		private System.Windows.Forms.ComboBox comboBoxBarcodeTypes;
		private System.Windows.Forms.CheckBox checkShowBarcodeText;
        private DCSDEV.DCSDesigner.DCSRotationProperties dcsRotationProperties1;
		private System.Windows.Forms.Button buttonRotateCCW;
		private System.Windows.Forms.Button buttonRotateCW;
		private System.Windows.Forms.ToolTip toolTip1;
		private System.Windows.Forms.ComboBox comboBoxCase;
		private System.Windows.Forms.Label label1;
        private Label labelPanel;
        private ComboBox comboBoxPanel;
		private ComboBox comboBoxDateFormat;
		private TabPage tabPage4;
		private GroupBox groupBoxLabeledText;
		private CheckBox checkBoxLabeledText;
		private Label labelLabelFont;
		private Label labelLabelOffset;
		private DCSSDK_Utilities.UnitizedNumberBox unitizedNumberBoxLabelOffset;
		private Label labelLabelOrientation;
		private Label labelLabelText;
		private TextBox textBoxLabelText;
		private ComboBox comboBoxLabelFont;
		private ComboBox comboBoxLabelOrientation;
		private ComboBox comboBoxFont;
		private System.ComponentModel.IContainer components;

		public TextObjProperties(DCSDEV.DCSDesign.DCSDesign activeDoc, DCSDEV.DCSDesigner.DCSDesignerView activeView, ArrayList designObjectsSelected, bool bNew)
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			// DCSMsg.Show("InitializeComponent finished");
			m_view = activeView;
			m_design = activeDoc;
			m_DesignObjectsSelected = designObjectsSelected;
			if (m_DesignObjectsSelected.Count <= 0)
			{
				DCSDEV.DCSMsg.Show("ERROR: TextObjProperties() - no objects selected");
				return;
			}
			DCSDEV.DCSDesign.DCSDesignObject designObject = (DCSDEV.DCSDesign.DCSDesignObject)m_DesignObjectsSelected[0];

			// only text or barcode or 2DBarcode modes
			this.InitializeForObjectType();

			if (bNew && designObject.SourceType == DCSDEV.DCSDatatypes.SourceTypes.StaticValue)
			{
				if (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.TextObj)
					designObject.Text = "enter static text";
				else if (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode)
					designObject.Text = "123456789";
			}
			this.MoveDataToDlg(designObject);
			groupBoxLabeledText.Visible = designObject.LabelOn;
			bool bViz = (this.comboBoxFont.SelectedIndex == 0);
			this.dcsFontProperties1.Visible = bViz;
			this.dcsRotationProperties1.Visible = !designObject.LabelOn;

            DCSDEV.PrintProperties.PrinterTypeDatum bcDatum = (DCSDEV.PrintProperties.PrinterTypeDatum)this.m_view.m_mainWin.m_printertypeArray[m_design.PrinterTypeIndex];
            if (!bcDatum.m_IfKpanel) this.comboBoxPanel.Visible = this.labelPanel.Visible = false;

			this.toolTip1.SetToolTip(this.buttonRotateCCW, "Rotate object and its frame 90 degrees counter clockwise");
			this.toolTip1.SetToolTip(this.buttonRotateCW,  "Rotate object and its frame 90 degrees clockwise");
			this.toolTip1.SetToolTip(this.buttonLockAspectRatio, "Toggle preservation of aspect ratio while resizing");
			this.toolTip1.SetToolTip(this.buttonApply,  "Save changes, apply changes to display, and keep dialog open.");
			this.toolTip1.SetToolTip(this.buttonAccept,  "Save changes, close dialog, and apply changes to display.");
			this.toolTip1.SetToolTip(this.buttonCancel,  "Undo changes and close dialog.");
			this.toolTip1.SetToolTip(this.comboBoxDateFormat, "Date format is text from app if null, system date format, or format selected.");

			// SYH NOTE: this meaningless code is added because the Aladdin protected version throws an error if it is not here.
			// almost any code except nothing does the trick.
			// Before Apr-May 2009 there was no such issue.
			if (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode)
			{
				this.dcsFontProperties1.Visible = bViz;
			}
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(TextObjProperties));
            this.buttonCancel = new System.Windows.Forms.Button();
            this.buttonAccept = new System.Windows.Forms.Button();
            this.buttonApply = new System.Windows.Forms.Button();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.comboBoxFont = new System.Windows.Forms.ComboBox();
            this.comboBoxDateFormat = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.comboBoxCase = new System.Windows.Forms.ComboBox();
            this.dcsRotationProperties1 = new DCSDEV.DCSDesigner.DCSRotationProperties();
            this.checkShowBarcodeText = new System.Windows.Forms.CheckBox();
            this.labelBarcodeType = new System.Windows.Forms.Label();
            this.comboBoxBarcodeTypes = new System.Windows.Forms.ComboBox();
            this.dcsAlignmentProperties1 = new DCSDEV.DCSDesigner.DCSAlignmentProperties();
            this.dcsFontProperties1 = new DCSDEV.DCSDesigner.DCSFontProperties();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.dcsBackGroundProperties1 = new DCSDEV.DCSDesigner.DCSBackGroundProperties();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.dcsFrameProperties1 = new DCSDEV.DCSDesigner.DCSFrameProperties1();
            this.tabPage4 = new System.Windows.Forms.TabPage();
            this.checkBoxLabeledText = new System.Windows.Forms.CheckBox();
            this.groupBoxLabeledText = new System.Windows.Forms.GroupBox();
            this.comboBoxLabelOrientation = new System.Windows.Forms.ComboBox();
            this.comboBoxLabelFont = new System.Windows.Forms.ComboBox();
            this.labelLabelFont = new System.Windows.Forms.Label();
            this.labelLabelOffset = new System.Windows.Forms.Label();
            this.unitizedNumberBoxLabelOffset = new DCSSDK_Utilities.UnitizedNumberBox();
            this.labelLabelOrientation = new System.Windows.Forms.Label();
            this.labelLabelText = new System.Windows.Forms.Label();
            this.textBoxLabelText = new System.Windows.Forms.TextBox();
            this.buttonLockAspectRatio = new System.Windows.Forms.Button();
            this.imageList1 = new System.Windows.Forms.ImageList(this.components);
            this.tbRatio = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.buttonRotateCCW = new System.Windows.Forms.Button();
            this.buttonRotateCW = new System.Windows.Forms.Button();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.labelPanel = new System.Windows.Forms.Label();
            this.comboBoxPanel = new System.Windows.Forms.ComboBox();
            this.dcsSourceProperties1 = new DCSDEV.DCSDesigner.DCSSourceProperties();
            this.dcsPositionSizeProperties1 = new DCSDEV.DCSDesigner.DCSPositionSizeProperties();
            this.tabControl1.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.tabPage3.SuspendLayout();
            this.tabPage4.SuspendLayout();
            this.groupBoxLabeledText.SuspendLayout();
            this.SuspendLayout();
            // 
            // buttonCancel
            // 
            this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            resources.ApplyResources(this.buttonCancel, "buttonCancel");
            this.buttonCancel.Name = "buttonCancel";
            this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
            // 
            // buttonAccept
            // 
            resources.ApplyResources(this.buttonAccept, "buttonAccept");
            this.buttonAccept.Name = "buttonAccept";
            this.buttonAccept.Click += new System.EventHandler(this.buttonAccept_Click);
            // 
            // buttonApply
            // 
            resources.ApplyResources(this.buttonApply, "buttonApply");
            this.buttonApply.Name = "buttonApply";
            this.buttonApply.Click += new System.EventHandler(this.buttonApply_Click);
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Controls.Add(this.tabPage3);
            this.tabControl1.Controls.Add(this.tabPage4);
            resources.ApplyResources(this.tabControl1, "tabControl1");
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.comboBoxFont);
            this.tabPage1.Controls.Add(this.comboBoxDateFormat);
            this.tabPage1.Controls.Add(this.label1);
            this.tabPage1.Controls.Add(this.comboBoxCase);
            this.tabPage1.Controls.Add(this.dcsRotationProperties1);
            this.tabPage1.Controls.Add(this.checkShowBarcodeText);
            this.tabPage1.Controls.Add(this.labelBarcodeType);
            this.tabPage1.Controls.Add(this.comboBoxBarcodeTypes);
            this.tabPage1.Controls.Add(this.dcsAlignmentProperties1);
            this.tabPage1.Controls.Add(this.dcsFontProperties1);
            resources.ApplyResources(this.tabPage1, "tabPage1");
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // comboBoxFont
            // 
            this.comboBoxFont.FormattingEnabled = true;
            this.comboBoxFont.Items.AddRange(new object[] {
            resources.GetString("comboBoxFont.Items"),
            resources.GetString("comboBoxFont.Items1"),
            resources.GetString("comboBoxFont.Items2"),
            resources.GetString("comboBoxFont.Items3")});
            resources.ApplyResources(this.comboBoxFont, "comboBoxFont");
            this.comboBoxFont.Name = "comboBoxFont";
            this.comboBoxFont.SelectedIndexChanged += new System.EventHandler(this.comboBoxFont_SelectedIndexChanged);
            // 
            // comboBoxDateFormat
            // 
            this.comboBoxDateFormat.Items.AddRange(new object[] {
            resources.GetString("comboBoxDateFormat.Items"),
            resources.GetString("comboBoxDateFormat.Items1"),
            resources.GetString("comboBoxDateFormat.Items2"),
            resources.GetString("comboBoxDateFormat.Items3"),
            resources.GetString("comboBoxDateFormat.Items4"),
            resources.GetString("comboBoxDateFormat.Items5"),
            resources.GetString("comboBoxDateFormat.Items6"),
            resources.GetString("comboBoxDateFormat.Items7"),
            resources.GetString("comboBoxDateFormat.Items8"),
            resources.GetString("comboBoxDateFormat.Items9"),
            resources.GetString("comboBoxDateFormat.Items10"),
            resources.GetString("comboBoxDateFormat.Items11"),
            resources.GetString("comboBoxDateFormat.Items12"),
            resources.GetString("comboBoxDateFormat.Items13"),
            resources.GetString("comboBoxDateFormat.Items14"),
            resources.GetString("comboBoxDateFormat.Items15"),
            resources.GetString("comboBoxDateFormat.Items16")});
            resources.ApplyResources(this.comboBoxDateFormat, "comboBoxDateFormat");
            this.comboBoxDateFormat.Name = "comboBoxDateFormat";
            // 
            // label1
            // 
            resources.ApplyResources(this.label1, "label1");
            this.label1.Name = "label1";
            // 
            // comboBoxCase
            // 
            this.comboBoxCase.Items.AddRange(new object[] {
            resources.GetString("comboBoxCase.Items"),
            resources.GetString("comboBoxCase.Items1"),
            resources.GetString("comboBoxCase.Items2")});
            resources.ApplyResources(this.comboBoxCase, "comboBoxCase");
            this.comboBoxCase.Name = "comboBoxCase";
            // 
            // dcsRotationProperties1
            // 
            resources.ApplyResources(this.dcsRotationProperties1, "dcsRotationProperties1");
            this.dcsRotationProperties1.Name = "dcsRotationProperties1";
            this.dcsRotationProperties1.RotateFlip = System.Drawing.RotateFlipType.RotateNoneFlipNone;
            // 
            // checkShowBarcodeText
            // 
            this.checkShowBarcodeText.Checked = true;
            this.checkShowBarcodeText.CheckState = System.Windows.Forms.CheckState.Checked;
            resources.ApplyResources(this.checkShowBarcodeText, "checkShowBarcodeText");
            this.checkShowBarcodeText.Name = "checkShowBarcodeText";
            // 
            // labelBarcodeType
            // 
            resources.ApplyResources(this.labelBarcodeType, "labelBarcodeType");
            this.labelBarcodeType.Name = "labelBarcodeType";
            // 
            // comboBoxBarcodeTypes
            // 
            resources.ApplyResources(this.comboBoxBarcodeTypes, "comboBoxBarcodeTypes");
            this.comboBoxBarcodeTypes.Name = "comboBoxBarcodeTypes";
            // 
            // dcsAlignmentProperties1
            // 
            resources.ApplyResources(this.dcsAlignmentProperties1, "dcsAlignmentProperties1");
            this.dcsAlignmentProperties1.Name = "dcsAlignmentProperties1";
            // 
            // dcsFontProperties1
            // 
            this.dcsFontProperties1.DCSColor = System.Drawing.Color.Black;
            this.dcsFontProperties1.DCSFont = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dcsFontProperties1.DCSShadow = false;
            this.dcsFontProperties1.DCSShadowColor = System.Drawing.Color.Black;
            this.dcsFontProperties1.DCSSimple = 'T';
            this.dcsFontProperties1.DCSSizeToFit = true;
            this.dcsFontProperties1.DCSWordWrap = false;
            this.dcsFontProperties1.LineSpacing = 0;
            resources.ApplyResources(this.dcsFontProperties1, "dcsFontProperties1");
            this.dcsFontProperties1.Name = "dcsFontProperties1";
            this.dcsFontProperties1.Units = 0;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.dcsBackGroundProperties1);
            resources.ApplyResources(this.tabPage2, "tabPage2");
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // dcsBackGroundProperties1
            // 
            this.dcsBackGroundProperties1.AllDBFieldNames = null;
            this.dcsBackGroundProperties1.BackFillType = DCSDEV.DCSDatatypes.BackFillTypes.FILL_COLOR;
            this.dcsBackGroundProperties1.Color = System.Drawing.Color.White;
            this.dcsBackGroundProperties1.Color2 = System.Drawing.Color.Yellow;
            this.dcsBackGroundProperties1.ColorCondition1 = null;
            this.dcsBackGroundProperties1.ColorCondition2 = null;
            this.dcsBackGroundProperties1.ConditionalColor1 = System.Drawing.Color.Empty;
            this.dcsBackGroundProperties1.ConditionalColor2 = System.Drawing.Color.Empty;
            this.dcsBackGroundProperties1.ConditionalColor3 = System.Drawing.Color.Empty;
            this.dcsBackGroundProperties1.Filename = "";
            this.dcsBackGroundProperties1.GradientType = System.Drawing.Drawing2D.LinearGradientMode.Horizontal;
            resources.ApplyResources(this.dcsBackGroundProperties1, "dcsBackGroundProperties1");
            this.dcsBackGroundProperties1.Name = "dcsBackGroundProperties1";
            this.dcsBackGroundProperties1.RestrictGradiantAndImage = false;
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.dcsFrameProperties1);
            resources.ApplyResources(this.tabPage3, "tabPage3");
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // dcsFrameProperties1
            // 
            this.dcsFrameProperties1.Color = System.Drawing.Color.Gray;
            resources.ApplyResources(this.dcsFrameProperties1, "dcsFrameProperties1");
            this.dcsFrameProperties1.Name = "dcsFrameProperties1";
            this.dcsFrameProperties1.Radius = 0;
            this.dcsFrameProperties1.Thickness = 3;
            // 
            // tabPage4
            // 
            this.tabPage4.Controls.Add(this.checkBoxLabeledText);
            this.tabPage4.Controls.Add(this.groupBoxLabeledText);
            resources.ApplyResources(this.tabPage4, "tabPage4");
            this.tabPage4.Name = "tabPage4";
            this.tabPage4.UseVisualStyleBackColor = true;
            // 
            // checkBoxLabeledText
            // 
            resources.ApplyResources(this.checkBoxLabeledText, "checkBoxLabeledText");
            this.checkBoxLabeledText.Name = "checkBoxLabeledText";
            this.checkBoxLabeledText.UseVisualStyleBackColor = true;
            this.checkBoxLabeledText.Click += new System.EventHandler(this.checkBoxLabeledText_Click);
            // 
            // groupBoxLabeledText
            // 
            this.groupBoxLabeledText.Controls.Add(this.comboBoxLabelOrientation);
            this.groupBoxLabeledText.Controls.Add(this.comboBoxLabelFont);
            this.groupBoxLabeledText.Controls.Add(this.labelLabelFont);
            this.groupBoxLabeledText.Controls.Add(this.labelLabelOffset);
            this.groupBoxLabeledText.Controls.Add(this.unitizedNumberBoxLabelOffset);
            this.groupBoxLabeledText.Controls.Add(this.labelLabelOrientation);
            this.groupBoxLabeledText.Controls.Add(this.labelLabelText);
            this.groupBoxLabeledText.Controls.Add(this.textBoxLabelText);
            resources.ApplyResources(this.groupBoxLabeledText, "groupBoxLabeledText");
            this.groupBoxLabeledText.Name = "groupBoxLabeledText";
            this.groupBoxLabeledText.TabStop = false;
            // 
            // comboBoxLabelOrientation
            // 
            this.comboBoxLabelOrientation.FormattingEnabled = true;
            this.comboBoxLabelOrientation.Items.AddRange(new object[] {
            resources.GetString("comboBoxLabelOrientation.Items"),
            resources.GetString("comboBoxLabelOrientation.Items1"),
            resources.GetString("comboBoxLabelOrientation.Items2"),
            resources.GetString("comboBoxLabelOrientation.Items3")});
            resources.ApplyResources(this.comboBoxLabelOrientation, "comboBoxLabelOrientation");
            this.comboBoxLabelOrientation.Name = "comboBoxLabelOrientation";
            this.comboBoxLabelOrientation.SelectedIndexChanged += new System.EventHandler(this.comboBoxLabelOrientation_SelectedIndexChanged);
            // 
            // comboBoxLabelFont
            // 
            this.comboBoxLabelFont.FormattingEnabled = true;
            this.comboBoxLabelFont.Items.AddRange(new object[] {
            resources.GetString("comboBoxLabelFont.Items"),
            resources.GetString("comboBoxLabelFont.Items1"),
            resources.GetString("comboBoxLabelFont.Items2")});
            resources.ApplyResources(this.comboBoxLabelFont, "comboBoxLabelFont");
            this.comboBoxLabelFont.Name = "comboBoxLabelFont";
            // 
            // labelLabelFont
            // 
            resources.ApplyResources(this.labelLabelFont, "labelLabelFont");
            this.labelLabelFont.Name = "labelLabelFont";
            // 
            // labelLabelOffset
            // 
            resources.ApplyResources(this.labelLabelOffset, "labelLabelOffset");
            this.labelLabelOffset.Name = "labelLabelOffset";
            // 
            // unitizedNumberBoxLabelOffset
            // 
            resources.ApplyResources(this.unitizedNumberBoxLabelOffset, "unitizedNumberBoxLabelOffset");
            this.unitizedNumberBoxLabelOffset.Name = "unitizedNumberBoxLabelOffset";
            this.unitizedNumberBoxLabelOffset.ReadOnly = false;
            this.unitizedNumberBoxLabelOffset.UnitizedValue = 15;
            this.unitizedNumberBoxLabelOffset.Units = 0;
            // 
            // labelLabelOrientation
            // 
            resources.ApplyResources(this.labelLabelOrientation, "labelLabelOrientation");
            this.labelLabelOrientation.Name = "labelLabelOrientation";
            // 
            // labelLabelText
            // 
            resources.ApplyResources(this.labelLabelText, "labelLabelText");
            this.labelLabelText.Name = "labelLabelText";
            // 
            // textBoxLabelText
            // 
            resources.ApplyResources(this.textBoxLabelText, "textBoxLabelText");
            this.textBoxLabelText.Name = "textBoxLabelText";
            // 
            // buttonLockAspectRatio
            // 
            resources.ApplyResources(this.buttonLockAspectRatio, "buttonLockAspectRatio");
            this.buttonLockAspectRatio.ImageList = this.imageList1;
            this.buttonLockAspectRatio.Name = "buttonLockAspectRatio";
            this.buttonLockAspectRatio.Click += new System.EventHandler(this.buttonLockAspectRatio_Click);
            // 
            // imageList1
            // 
            this.imageList1.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList1.ImageStream")));
            this.imageList1.TransparentColor = System.Drawing.Color.Transparent;
            this.imageList1.Images.SetKeyName(0, "UNLOCKED.BMP");
            this.imageList1.Images.SetKeyName(1, "LOCKED.BMP");
            // 
            // tbRatio
            // 
            resources.ApplyResources(this.tbRatio, "tbRatio");
            this.tbRatio.Name = "tbRatio";
            this.tbRatio.ReadOnly = true;
            // 
            // label2
            // 
            resources.ApplyResources(this.label2, "label2");
            this.label2.Name = "label2";
            // 
            // buttonRotateCCW
            // 
            resources.ApplyResources(this.buttonRotateCCW, "buttonRotateCCW");
            this.buttonRotateCCW.Name = "buttonRotateCCW";
            this.buttonRotateCCW.Click += new System.EventHandler(this.buttonRotate_Click);
            // 
            // buttonRotateCW
            // 
            resources.ApplyResources(this.buttonRotateCW, "buttonRotateCW");
            this.buttonRotateCW.Name = "buttonRotateCW";
            this.buttonRotateCW.Click += new System.EventHandler(this.buttonRotate_Click);
            // 
            // labelPanel
            // 
            resources.ApplyResources(this.labelPanel, "labelPanel");
            this.labelPanel.Name = "labelPanel";
            // 
            // comboBoxPanel
            // 
            this.comboBoxPanel.FormattingEnabled = true;
            this.comboBoxPanel.Items.AddRange(new object[] {
            resources.GetString("comboBoxPanel.Items"),
            resources.GetString("comboBoxPanel.Items1")});
            resources.ApplyResources(this.comboBoxPanel, "comboBoxPanel");
            this.comboBoxPanel.Name = "comboBoxPanel";
            // 
            // dcsSourceProperties1
            // 
            this.dcsSourceProperties1.DBField = "";
            this.dcsSourceProperties1.DesignObjectType = DCSDEV.DCSDatatypes.DCSDesignObjectTypes.TextObj;
            this.dcsSourceProperties1.ForegroundImageName = "";
            this.dcsSourceProperties1.Formula = "";
            this.dcsSourceProperties1.Instance = 0;
            resources.ApplyResources(this.dcsSourceProperties1, "dcsSourceProperties1");
            this.dcsSourceProperties1.MaxInstances = 10;
            this.dcsSourceProperties1.Name = "dcsSourceProperties1";
            this.dcsSourceProperties1.SourceType = -1;
            this.dcsSourceProperties1.StaticText = "";
            this.dcsSourceProperties1.VisibleIf = false;
            this.dcsSourceProperties1.VisibleIfCondition = null;
            // 
            // dcsPositionSizeProperties1
            // 
            this.dcsPositionSizeProperties1.DisplayBounds = new System.Drawing.Rectangle(0, 0, 0, 0);
            resources.ApplyResources(this.dcsPositionSizeProperties1, "dcsPositionSizeProperties1");
            this.dcsPositionSizeProperties1.Name = "dcsPositionSizeProperties1";
            this.dcsPositionSizeProperties1.Units = 0;
            this.dcsPositionSizeProperties1.UnitsChanged += new DCSDEV.DCSDesigner.DCSPositionSizeProperties.UnitsChangedEventHandler(this.dcsPositionSizeProperties1_UnitsChanged);
            // 
            // TextObjProperties
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            resources.ApplyResources(this, "$this");
            this.Controls.Add(this.labelPanel);
            this.Controls.Add(this.comboBoxPanel);
            this.Controls.Add(this.buttonRotateCW);
            this.Controls.Add(this.buttonRotateCCW);
            this.Controls.Add(this.buttonLockAspectRatio);
            this.Controls.Add(this.tbRatio);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.dcsPositionSizeProperties1);
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.dcsSourceProperties1);
            this.Controls.Add(this.buttonApply);
            this.Controls.Add(this.buttonCancel);
            this.Controls.Add(this.buttonAccept);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "TextObjProperties";
            this.ShowInTaskbar = false;
            this.tabControl1.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.tabPage2.ResumeLayout(false);
            this.tabPage3.ResumeLayout(false);
            this.tabPage4.ResumeLayout(false);
            this.tabPage4.PerformLayout();
            this.groupBoxLabeledText.ResumeLayout(false);
            this.groupBoxLabeledText.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

		}
		#endregion

		private bool ApplyData()
		{
			// check validity of the data
			if (this.dcsBackGroundProperties1.BackFillType == DCSDEV.DCSDatatypes.BackFillTypes.FILL_IMAGE)
			{
				string strFullname = DCSDEV.DCSDesignDataAccess.ExpandImageName(this.dcsBackGroundProperties1.Filename, true);
				if (strFullname == null)
				{
					DCSDEV.DCSMsg.Show(String.Format("Background image file '{0}' does not exist", this.dcsBackGroundProperties1.Filename));
					return false;
				}
			}

			// All check pass .  It should be safe to procede
			DCSDEV.DCSDesign.DCSDesignObject designObject = (DCSDEV.DCSDesign.DCSDesignObject)m_DesignObjectsSelected[0];
			this.MoveDataFromDlg();
			if (designObject.BackImage != null) designObject.BackImage.Dispose();
			if (designObject.BackFillType == DCSDEV.DCSDatatypes.BackFillTypes.FILL_IMAGE)
			{
				try
				{
					designObject.BackImage = (Bitmap)DCSDEV.DCSDesignDataAccess.GetImage(designObject.BackImageName, false);
				}
				catch (Exception ex)
				{
					DCSDEV.DCSMsg.Show(ex);
					return false;
				}
			}
			return true;
		}

		// set up visibilities for barcode vs text objects
		private void InitializeForObjectType()
		{
			DCSDEV.DCSDesign.DCSDesignObject designObject = (DCSDEV.DCSDesign.DCSDesignObject)m_DesignObjectsSelected[0];

			if (m_DesignObjectsSelected.Count > 1)
			{
				//this.dcsPositionSizeProperties1.
				this.buttonRotateCCW.Visible = false;
				this.buttonRotateCW.Visible = false;
				this.dcsRotationProperties1.Visible = false;
				this.dcsSourceProperties1.Visible = false;
			}

			switch (designObject.DCSDesignObjectType)
			{
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.TextObj:
					// set dialog title bar
					this.Text = ((m_DesignObjectsSelected.Count > 1)?"Multi " : "") + "Text Object Properties";

					this.labelBarcodeType.Visible  = false;
					this.comboBoxBarcodeTypes.Visible = false;
					this.checkShowBarcodeText.Visible = false;
					this.comboBoxCase.Visible = true;
                    this.labelPanel.Visible = this.comboBoxPanel.Visible = true;
					this.dcsFontProperties1.DCSSimple = 'T';
					this.comboBoxFont.Visible = true;
					this.checkBoxLabeledText.Visible = true;
					break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode:
					// set dialog title bar
					this.Text = ((m_DesignObjectsSelected.Count > 1)?"Multi " : "") + "Barcode Object Properties";

					this.labelBarcodeType.Visible  = true;
					this.comboBoxBarcodeTypes.Visible = true;
					this.checkShowBarcodeText.Visible = true;
					this.comboBoxCase.Visible = true;
					this.labelPanel.Visible = this.comboBoxPanel.Visible = true;

					m_barcodeTypesList = new DCSSDK.DCSBarcodeIF.DCSBarcodeTypesList();
					//this.comboBoxBarcodeTypes.Items = m_barcodeTypesList;
					this.comboBoxBarcodeTypes.DataSource = m_barcodeTypesList.List;
					this.comboBoxBarcodeTypes.DisplayMember = "BarcodeName";
					this.comboBoxBarcodeTypes.ValueMember = "BarcodeCode";
                    this.dcsBackGroundProperties1.RestrictGradiantAndImage = true;
					this.dcsFontProperties1.DCSSimple = 'B';
					this.comboBoxFont.Visible = false;
					designObject.FontIndex = -1;
					this.checkBoxLabeledText.Visible = false;
					break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode2D:
					// set dialog title bar
					this.Text = ((m_DesignObjectsSelected.Count > 1)?"Multi " : "") + "2D Barcode Object Properties";

					this.comboBoxBarcodeTypes.Visible = false;
					this.checkShowBarcodeText.Visible = false;
					this.labelBarcodeType.Visible = false;
					this.comboBoxCase.Visible = false;

					this.dcsFontProperties1.Visible = false;
					this.dcsAlignmentProperties1.Visible = true;
					this.dcsAlignmentProperties1.Visible = false;
					this.dcsRotationProperties1.Visible = false;
					this.buttonRotateCCW.Visible = true;	//false;
					this.buttonRotateCW.Visible = true;	//false;

                    this.labelPanel.Visible = this.comboBoxPanel.Visible = false;
					this.dcsFontProperties1.DCSSimple = 'B';
					this.comboBoxFont.Visible = false;
					this.checkBoxLabeledText.Visible = false;
					break;
			}
		}

		private void MoveDataToDlg(DCSDEV.DCSDesign.DCSDesignObject designObject)
		{
			// source
			this.dcsSourceProperties1.DesignObjectType = designObject.DCSDesignObjectType;
			this.dcsSourceProperties1.SourceType = (int)designObject.SourceType;
			this.dcsSourceProperties1.StaticText = designObject.Text;
			this.dcsSourceProperties1.DBField = designObject.SourceName;
			this.dcsSourceProperties1.Formula = designObject.Formula;
			this.dcsSourceProperties1.VisibleIf = designObject.VisibleIf;
			this.dcsSourceProperties1.VisibleIfCondition = designObject.VisibleIfCondition;
			this.dcsSourceProperties1.AllDBFieldNames = this.m_view.m_mainWin.m_AllDBFieldNames;

			// position size
			this.dcsPositionSizeProperties1.DisplayBounds = designObject.Bounds;
			this.dcsPositionSizeProperties1.Units = DCSDEV.DCSDesignDataAccess.GetUnits();
			this.dcsRotationProperties1.RotateFlip = designObject.RotateFlip;
			
			// background properties
			this.dcsBackGroundProperties1.BackFillType = designObject.BackFillType;
			this.dcsBackGroundProperties1.Filename = designObject.BackImageName;
			this.dcsBackGroundProperties1.Color = designObject.BackColor;

			this.dcsBackGroundProperties1.Color2 = designObject.BackColor2;
			this.dcsBackGroundProperties1.GradientType = designObject.BackGradientType;
			this.dcsBackGroundProperties1.AllDBFieldNames = this.m_view.m_mainWin.m_AllDBFieldNames;
			this.dcsBackGroundProperties1.ConditionalColor1 = designObject.ColorChoice1;
			this.dcsBackGroundProperties1.ConditionalColor2 = designObject.ColorChoice2;
			this.dcsBackGroundProperties1.ConditionalColor3 = designObject.ColorChoice3;
			this.dcsBackGroundProperties1.ColorCondition1 = designObject.ColorCondition1;
			this.dcsBackGroundProperties1.ColorCondition2 = designObject.ColorCondition2;

			// frame properties
			this.dcsFrameProperties1.Thickness = designObject.LineWidth;
			this.dcsFrameProperties1.Radius = designObject.Radius;
			this.dcsFrameProperties1.Color = designObject.LineColor;

			// alignment
			this.dcsAlignmentProperties1.SetAlignment(designObject.Justification, designObject.Alignment);

			// other
			this.comboBoxFont.SelectedIndex = designObject.FontIndex + 1;
			this.comboBoxCase.SelectedIndex = designObject.CaseIndex;
			this.comboBoxDateFormat.Text = designObject.TxtFormat;
			this.dcsFontProperties1.Units = DCSDEV.DCSDesignDataAccess.GetUnits();
			this.dcsFontProperties1.DCSFont = designObject.FontEx.Font;
			this.dcsFontProperties1.LineSpacing = designObject.FontEx.FontLineSpacing;
			this.dcsFontProperties1.DCSColor = designObject.FontEx.ForeColor;
			this.dcsFontProperties1.DCSSizeToFit = designObject.FontEx.SizeToFit;
			this.dcsFontProperties1.DCSWordWrap = designObject.FontEx.Wrap;
			this.dcsFontProperties1.DCSShadow = designObject.FontEx.Shadow;
			this.dcsFontProperties1.DCSShadowColor = designObject.FontEx.ShadowColor;

			this.buttonLockAspectRatio.ImageIndex = designObject.IfLockAspect ? 1 : 0;
            this.comboBoxPanel.Text = designObject.SpecialKPanel == 0 ? "default" : "K panel";
            this.tbRatio.Text = ((double)designObject.Bounds.Width / (double)designObject.Bounds.Height).ToString("0.000");

			// LabeledText
			this.checkBoxLabeledText.Checked = designObject.LabelOn;
			this.unitizedNumberBoxLabelOffset.Units = DCSDEV.DCSDesignDataAccess.GetUnits();
			this.unitizedNumberBoxLabelOffset.UnitizedValue = designObject.LabelOffset;
			this.comboBoxLabelOrientation.SelectedIndex = (int)designObject.LabelOrientation;
			this.textBoxLabelText.Text = designObject.LabelText;
			this.comboBoxLabelFont.SelectedIndex = designObject.LabelFontIndex;

			// barcode
			if (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode)
			{
				this.checkShowBarcodeText.Checked = designObject.BarcodeShowText;
				int index = m_barcodeTypesList.GetCodeIndex(designObject.BarcodeCode);
				this.comboBoxBarcodeTypes.SelectedIndex = index;
			}
			else if (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode2D)
			{
			}
		}
		private void MoveDataFromDlg()
		{
			DCSDEV.DCSDesignDataAccess.SetUnits(this.dcsPositionSizeProperties1.Units);

			DCSDEV.DCSDesign.DCSDesignObject designFirstObject = (DCSDEV.DCSDesign.DCSDesignObject)m_DesignObjectsSelected[0];
			int i;
			DCSDEV.DCSDesign.DCSDesignObject designObject;
			for (i=this.m_DesignObjectsSelected.Count-1; i>=0; i--)
			{
				designObject = (DCSDEV.DCSDesign.DCSDesignObject)m_DesignObjectsSelected[i];
				if (i == 0)
				{
					// source
					designObject.SourceType = (DCSDEV.DCSDatatypes.SourceTypes)this.dcsSourceProperties1.SourceType;
					designObject.Text = this.dcsSourceProperties1.StaticText;
					designObject.SourceName = this.dcsSourceProperties1.DBField;
					designObject.Formula = this.dcsSourceProperties1.Formula;
					designObject.VisibleIf = this.dcsSourceProperties1.VisibleIf;
					designObject.VisibleIfCondition = this.dcsSourceProperties1.VisibleIfCondition;

					// position size
					designObject.RotateFlip = this.dcsRotationProperties1.RotateFlip;
					designObject.Bounds = this.dcsPositionSizeProperties1.DisplayBounds;

					// background properties
					designObject.BackFillType	= this.dcsBackGroundProperties1.BackFillType;
					designObject.BackImageName = this.dcsBackGroundProperties1.Filename;
					designObject.BackColor		= this.dcsBackGroundProperties1.Color;

					designObject.BackColor2 = this.dcsBackGroundProperties1.Color2;
					designObject.BackGradientType   = this.dcsBackGroundProperties1.GradientType;
					designObject.ColorChoice1 = this.dcsBackGroundProperties1.ConditionalColor1;
					designObject.ColorChoice2 = this.dcsBackGroundProperties1.ConditionalColor2;
					designObject.ColorChoice3 = this.dcsBackGroundProperties1.ConditionalColor3;
					designObject.ColorCondition1 = this.dcsBackGroundProperties1.ColorCondition1;
					designObject.ColorCondition2 = this.dcsBackGroundProperties1.ColorCondition2;

					// frame properties
					designObject.LineWidth		= this.dcsFrameProperties1.Thickness;
					designObject.Radius		= this.dcsFrameProperties1.Radius;
					designObject.LineColor		= this.dcsFrameProperties1.Color;

					// alignment
					int hj, vj;
					this.dcsAlignmentProperties1.GetAlignment(out hj, out vj);
					designObject.Justification = (DCSDEV.DCSDatatypes.Justifications)vj;
					designObject.Alignment = (DCSDEV.DCSDatatypes.Alignments)hj;

					// other
					designObject.FontIndex = this.comboBoxFont.SelectedIndex - 1;
					designObject.FontEx.Font = this.dcsFontProperties1.DCSFont;
					designObject.FontEx.FontLineSpacing = this.dcsFontProperties1.LineSpacing;
					designObject.FontEx.ForeColor = this.dcsFontProperties1.DCSColor;
					designObject.FontEx.SizeToFit = this.dcsFontProperties1.DCSSizeToFit;
					designObject.FontEx.Wrap = this.dcsFontProperties1.DCSWordWrap;
					designObject.FontEx.Shadow = this.dcsFontProperties1.DCSShadow;
					designObject.FontEx.ShadowColor = this.dcsFontProperties1.DCSShadowColor;

					designObject.CaseIndex = this.comboBoxCase.SelectedIndex;
					designObject.TxtFormat = this.comboBoxDateFormat.Text;
					
					designObject.IfLockAspect = (this.buttonLockAspectRatio.ImageIndex == 1);
                    designObject.SpecialKPanel = (this.comboBoxPanel.Text == "default") ? 0 : 1;

					// LabeledText
					designObject.LabelOn = this.checkBoxLabeledText.Checked;
					designObject.LabelText = this.textBoxLabelText.Text;
					designObject.LabelFontIndex = this.comboBoxLabelFont.SelectedIndex;
					designObject.LabelOrientation = (DCSDatatypes.DCSLabeledTextOrientations)(this.comboBoxLabelOrientation.SelectedIndex);
					designObject.LabelOffset = this.unitizedNumberBoxLabelOffset.UnitizedValue;

					// barcode
					if (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode)
					{
						designObject.BarcodeShowText = this.checkShowBarcodeText.Checked;
				
						int index = this.comboBoxBarcodeTypes.SelectedIndex;
						DCSSDK.DCSBarcodeIF.DCSBarcodeType tl = (DCSSDK.DCSBarcodeIF.DCSBarcodeType)m_barcodeTypesList.List[index];
						designObject.BarcodeCode = tl.BarcodeCode;
					}
					else if (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode2D)
					{
					}
				}
				else
				{
					// position size
					if (this.dcsPositionSizeProperties1.DisplayBounds.X != designFirstObject.Bounds.X)
						designObject.Bounds.X = this.dcsPositionSizeProperties1.DisplayBounds.X;
					if (this.dcsPositionSizeProperties1.DisplayBounds.Y != designFirstObject.Bounds.Y)
						designObject.Bounds.Y = this.dcsPositionSizeProperties1.DisplayBounds.Y;
					if (this.dcsPositionSizeProperties1.DisplayBounds.Width != designFirstObject.Bounds.Width)
						designObject.Bounds.Width = this.dcsPositionSizeProperties1.DisplayBounds.Width;
					if (this.dcsPositionSizeProperties1.DisplayBounds.Height != designFirstObject.Bounds.Height)
						designObject.Bounds.Height = this.dcsPositionSizeProperties1.DisplayBounds.Height;

					// background properties
					if (this.dcsBackGroundProperties1.BackFillType != designFirstObject.BackFillType)
						designObject.BackFillType	= this.dcsBackGroundProperties1.BackFillType;
					if (this.dcsBackGroundProperties1.Filename != designFirstObject.BackImageName)
						designObject.BackImageName = this.dcsBackGroundProperties1.Filename;
					if (this.dcsBackGroundProperties1.Color != designFirstObject.BackColor)
						designObject.BackColor		= this.dcsBackGroundProperties1.Color;

					if (this.dcsBackGroundProperties1.Color2 != designFirstObject.BackColor2)
						designObject.BackColor2 = this.dcsBackGroundProperties1.Color2;
					if (this.dcsBackGroundProperties1.GradientType != designFirstObject.BackGradientType)
						designObject.BackGradientType   = this.dcsBackGroundProperties1.GradientType;

					// frame properties
					if (this.dcsFrameProperties1.Thickness != designFirstObject.LineWidth)
						designObject.LineWidth		= this.dcsFrameProperties1.Thickness;
					if (this.dcsFrameProperties1.Radius != designFirstObject.Radius)
						designObject.Radius		= this.dcsFrameProperties1.Radius;
					if (this.dcsFrameProperties1.Color != designFirstObject.LineColor)
						designObject.LineColor		= this.dcsFrameProperties1.Color;

					// alignment
					int hj, vj;
					this.dcsAlignmentProperties1.GetAlignment(out hj, out vj);
					if ((DCSDEV.DCSDatatypes.Justifications)vj != designFirstObject.Justification)
						designObject.Justification = (DCSDEV.DCSDatatypes.Justifications)vj;
					if ((DCSDEV.DCSDatatypes.Alignments)hj != designFirstObject.Alignment)
						designObject.Alignment = (DCSDEV.DCSDatatypes.Alignments)hj;

					// other
					if (designFirstObject.CaseIndex != this.comboBoxCase.SelectedIndex)
						designObject.CaseIndex = this.comboBoxCase.SelectedIndex;
					if (designFirstObject.TxtFormat != this.comboBoxDateFormat.Text)
						designObject.TxtFormat = this.comboBoxDateFormat.Text;
					if (designFirstObject.FontEx.Font != this.dcsFontProperties1.DCSFont)
						designObject.FontEx.Font = this.dcsFontProperties1.DCSFont;
					if (designFirstObject.FontEx.FontLineSpacing != this.dcsFontProperties1.LineSpacing)
						designObject.FontEx.FontLineSpacing = this.dcsFontProperties1.LineSpacing;
					if (designFirstObject.FontEx.ForeColor != this.dcsFontProperties1.DCSColor)
						designObject.FontEx.ForeColor = this.dcsFontProperties1.DCSColor;

					if (designFirstObject.FontEx.SizeToFit != this.dcsFontProperties1.DCSSizeToFit)
						designObject.FontEx.SizeToFit = this.dcsFontProperties1.DCSSizeToFit;
					if (designFirstObject.FontEx.Wrap != this.dcsFontProperties1.DCSWordWrap)
						designObject.FontEx.Wrap = this.dcsFontProperties1.DCSWordWrap;
					if (designFirstObject.FontEx.Shadow != this.dcsFontProperties1.DCSShadow)
						designObject.FontEx.Shadow = this.dcsFontProperties1.DCSShadow;
					if (designFirstObject.FontEx.ShadowColor != this.dcsFontProperties1.DCSShadowColor)
						designObject.FontEx.ShadowColor = this.dcsFontProperties1.DCSShadowColor;

					if (designFirstObject.IfLockAspect != (this.buttonLockAspectRatio.ImageIndex == 1))
						designObject.IfLockAspect = (this.buttonLockAspectRatio.ImageIndex == 1);

                    // LabeledText
                    if (designFirstObject.LabelOn != this.checkBoxLabeledText.Checked)
                        designObject.LabelOn = this.checkBoxLabeledText.Checked;
                    // designObject.LabelText = this.textBoxLabelText.Text;
                    if (designFirstObject.LabelFontIndex != this.comboBoxLabelFont.SelectedIndex)
                        designObject.LabelFontIndex = this.comboBoxLabelFont.SelectedIndex;
                    if (designFirstObject.LabelOrientation != (DCSDatatypes.DCSLabeledTextOrientations)(this.comboBoxLabelOrientation.SelectedIndex))
                        designObject.LabelOrientation = (DCSDatatypes.DCSLabeledTextOrientations)(this.comboBoxLabelOrientation.SelectedIndex);
                    if (designFirstObject.LabelOffset != this.unitizedNumberBoxLabelOffset.UnitizedValue)
                        designObject.LabelOffset = this.unitizedNumberBoxLabelOffset.UnitizedValue;

					// barcode
					if (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode)
					{
						if (designFirstObject.BarcodeShowText != this.checkShowBarcodeText.Checked)
							designObject.BarcodeShowText = this.checkShowBarcodeText.Checked;
				
						int index = this.comboBoxBarcodeTypes.SelectedIndex;
						DCSSDK.DCSBarcodeIF.DCSBarcodeType tl = (DCSSDK.DCSBarcodeIF.DCSBarcodeType)m_barcodeTypesList.List[index];
						if (designFirstObject.BarcodeCode != tl.BarcodeCode)
							designObject.BarcodeCode = tl.BarcodeCode;
					}
					else if (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode2D)
					{
					}
				}
			}
		}

		private void buttonAccept_Click(object sender, System.EventArgs e)
		{
			bool bRet = this.ApplyData();
			if (bRet) 
			{
				this.DialogResult = System.Windows.Forms.DialogResult.OK;
				this.Close();
			}
		}

		private void buttonCancel_Click(object sender, System.EventArgs e)
		{
			this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.Close();
		}

		private void buttonApply_Click(object sender, System.EventArgs e)
		{
			this.ApplyData();
			m_design.m_isDirty = true;
			m_design.m_isViewDirty = true;
			m_view.Invalidate(true);
		}

		private void buttonLockAspectRatio_Click(object sender, System.EventArgs e)
		{
			this.buttonLockAspectRatio.ImageIndex = (this.buttonLockAspectRatio.ImageIndex+1) % 2;		
		}

		private void buttonRotate_Click(object sender, System.EventArgs e)
		{
			Rectangle rect = this.dcsPositionSizeProperties1.DisplayBounds;
			int width = rect.Width;
			rect.Width = rect.Height;
			rect.Height = width;
			this.dcsPositionSizeProperties1.DisplayBounds = rect;
			if (sender == this.buttonRotateCCW)
				this.dcsRotationProperties1.Rotate270();
			else
				this.dcsRotationProperties1.Rotate90();
			this.dcsRotationProperties1.Invalidate();
			this.tabControl1.SelectedIndex = 0;
		}

		private void dcsPositionSizeProperties1_UnitsChanged(object sender, System.EventArgs ev)
		{
			int iSpace = this.dcsFontProperties1.LineSpacing;
			this.dcsFontProperties1.Units = this.dcsPositionSizeProperties1.Units;
			this.dcsFontProperties1.LineSpacing = iSpace;
			this.unitizedNumberBoxLabelOffset.Units = this.dcsPositionSizeProperties1.Units;
		}
	
		public int LastTab
		{
			get { return this.tabControl1.SelectedIndex; }
			set { this.tabControl1.SelectedIndex = value; }
		}

		private void checkBoxLabeledText_Click(object sender, EventArgs e)
		{
			DCSDEV.DCSDesign.DCSDesignObject designFirstObject = (DCSDEV.DCSDesign.DCSDesignObject)m_DesignObjectsSelected[0];
			if (checkBoxLabeledText.Checked && designFirstObject.RotateFlip != RotateFlipType.RotateNoneFlipNone)
			{
				DCSMsg.Show("Cannot apply label option when the text object is rotated.");
				checkBoxLabeledText.Checked = false;
				return;
			}

			bool bViz = checkBoxLabeledText.Checked;
			groupBoxLabeledText.Visible = bViz;
			this.dcsRotationProperties1.Visible = !bViz;

			return;
		}

		private void comboBoxFont_SelectedIndexChanged(object sender, EventArgs e)
		{
			bool bViz = (this.comboBoxFont.SelectedIndex == 0);
			this.dcsFontProperties1.Visible = bViz;
		}

		private void comboBoxLabelOrientation_SelectedIndexChanged(object sender, EventArgs e)
		{
			DCSDatatypes.DCSLabeledTextOrientations orient = (DCSDatatypes.DCSLabeledTextOrientations)this.comboBoxLabelOrientation.SelectedIndex;
			if (orient == DCSDatatypes.DCSLabeledTextOrientations.BOTTOM || orient == DCSDatatypes.DCSLabeledTextOrientations.TOP)
				this.labelLabelOffset.Text = "Label height";
			else
				this.labelLabelOffset.Text = "Label width";
		}
	}
}
