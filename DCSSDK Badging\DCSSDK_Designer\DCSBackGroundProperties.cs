using System;
using System.Collections;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Windows.Forms;

using DCSDEV;

namespace DCSDEV.DCSDesigner
{
	/// <summary>
	/// Summary description for DCSBackGroundProperties.
	/// </summary>
    internal class DCSBackGroundProperties : System.Windows.Forms.UserControl
	{
		private Bitmap m_bitmap = null;
		private string m_bitmapName;
		private Color m_color = Color.LightBlue;
		private Color m_color2 = Color.MediumSlateBlue;
		private Rectangle m_rectOriginalPicture;
        private bool m_bRestrictGradiantAndImage = false;

		private ArrayList m_AllDBFieldNames;
		private Color m_colorChoice1;
		private Color m_colorChoice2;
		private Color m_colorChoice3;
		private string m_strColorCondition1;
		private string m_strColorCondition2;

		private System.Windows.Forms.ComboBox cbFillType;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.Button buttonChoose;
		private System.Windows.Forms.ColorDialog colorDialog1;
		private System.Windows.Forms.PictureBox pictureBox1;
		private System.Windows.Forms.TextBox textBoxImageName;
		private System.Windows.Forms.Label labelImageName;
		private System.Windows.Forms.Button buttonChoose2;
		private System.Windows.Forms.ComboBox comboBoxGradient;
		private System.Windows.Forms.Label labelChoose1;
		private System.Windows.Forms.Label labelChoose2;
		/// <summary> 
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		public DCSBackGroundProperties()
		{
			// This call is required by the Windows.Forms Form Designer.
			InitializeComponent();

			// TODO: Add any initialization after the InitializeComponent call
			m_AllDBFieldNames = new ArrayList();
			m_rectOriginalPicture = this.pictureBox1.Bounds;
			AdjustVisibilities();
		}

		/// <summary> 
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
				if (m_bitmap != null) 
				{
					m_bitmap.Dispose();
					m_bitmap = null;
				}
			}
			base.Dispose( disposing );
		}

		#region Component Designer generated code
		/// <summary> 
		/// Required method for Designer support - do not modify 
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			this.cbFillType = new System.Windows.Forms.ComboBox();
			this.label2 = new System.Windows.Forms.Label();
			this.buttonChoose = new System.Windows.Forms.Button();
			this.colorDialog1 = new System.Windows.Forms.ColorDialog();
			this.pictureBox1 = new System.Windows.Forms.PictureBox();
			this.textBoxImageName = new System.Windows.Forms.TextBox();
			this.labelImageName = new System.Windows.Forms.Label();
			this.buttonChoose2 = new System.Windows.Forms.Button();
			this.comboBoxGradient = new System.Windows.Forms.ComboBox();
			this.labelChoose1 = new System.Windows.Forms.Label();
			this.labelChoose2 = new System.Windows.Forms.Label();
			((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
			this.SuspendLayout();
			// 
			// cbFillType
			// 
			this.cbFillType.Items.AddRange(new object[] {
            "Color",
            "None / Transparent",
            "Image",
            "Color Gradiant",
            "Conditional Color"});
			this.cbFillType.Location = new System.Drawing.Point(8, 16);
			this.cbFillType.Name = "cbFillType";
			this.cbFillType.Size = new System.Drawing.Size(120, 21);
			this.cbFillType.TabIndex = 1;
			this.cbFillType.Text = "Color";
			this.cbFillType.SelectedValueChanged += new System.EventHandler(this.cbFillType_SelectedValueChanged);
			// 
			// label2
			// 
			this.label2.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
			this.label2.Location = new System.Drawing.Point(8, 0);
			this.label2.Name = "label2";
			this.label2.Size = new System.Drawing.Size(120, 16);
			this.label2.TabIndex = 0;
			this.label2.Text = "Fill type";
			this.label2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// buttonChoose
			// 
			this.buttonChoose.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonChoose.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.buttonChoose.Location = new System.Drawing.Point(144, 16);
			this.buttonChoose.Name = "buttonChoose";
			this.buttonChoose.Size = new System.Drawing.Size(24, 21);
			this.buttonChoose.TabIndex = 4;
			this.buttonChoose.Text = ">";
			this.buttonChoose.Click += new System.EventHandler(this.buttonChoose_Click);
			// 
			// pictureBox1
			// 
			this.pictureBox1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
			this.pictureBox1.Location = new System.Drawing.Point(128, 80);
			this.pictureBox1.Name = "pictureBox1";
			this.pictureBox1.Size = new System.Drawing.Size(80, 80);
			this.pictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
			this.pictureBox1.TabIndex = 15;
			this.pictureBox1.TabStop = false;
			this.pictureBox1.Paint += new System.Windows.Forms.PaintEventHandler(this.pictureBox1_Paint);
			// 
			// textBoxImageName
			// 
			this.textBoxImageName.BorderStyle = System.Windows.Forms.BorderStyle.None;
			this.textBoxImageName.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Italic, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.textBoxImageName.Location = new System.Drawing.Point(8, 105);
			this.textBoxImageName.Name = "textBoxImageName";
			this.textBoxImageName.ReadOnly = true;
			this.textBoxImageName.Size = new System.Drawing.Size(104, 13);
			this.textBoxImageName.TabIndex = 16;
			this.textBoxImageName.Text = "select name";
			// 
			// labelImageName
			// 
			this.labelImageName.Location = new System.Drawing.Point(8, 80);
			this.labelImageName.Name = "labelImageName";
			this.labelImageName.Size = new System.Drawing.Size(104, 22);
			this.labelImageName.TabIndex = 17;
			this.labelImageName.Text = "Image name:";
			// 
			// buttonChoose2
			// 
			this.buttonChoose2.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonChoose2.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.buttonChoose2.Location = new System.Drawing.Point(184, 16);
			this.buttonChoose2.Name = "buttonChoose2";
			this.buttonChoose2.Size = new System.Drawing.Size(24, 21);
			this.buttonChoose2.TabIndex = 5;
			this.buttonChoose2.Text = ">";
			this.buttonChoose2.Click += new System.EventHandler(this.buttonChoose2_Click);
			// 
			// comboBoxGradient
			// 
			this.comboBoxGradient.Items.AddRange(new object[] {
            "Horizontal",
            "Vertical",
            "Fore Diagonal",
            "Back Diagonal"});
			this.comboBoxGradient.Location = new System.Drawing.Point(8, 48);
			this.comboBoxGradient.Name = "comboBoxGradient";
			this.comboBoxGradient.Size = new System.Drawing.Size(120, 21);
			this.comboBoxGradient.TabIndex = 6;
			this.comboBoxGradient.Text = "Horizontal";
			this.comboBoxGradient.SelectedValueChanged += new System.EventHandler(this.comboBoxGradient_SelectedValueChanged);
			// 
			// labelChoose1
			// 
			this.labelChoose1.Location = new System.Drawing.Point(108, 0);
			this.labelChoose1.Name = "labelChoose1";
			this.labelChoose1.Size = new System.Drawing.Size(92, 16);
			this.labelChoose1.TabIndex = 2;
			this.labelChoose1.Text = "color 1";
			this.labelChoose1.TextAlign = System.Drawing.ContentAlignment.TopCenter;
			// 
			// labelChoose2
			// 
			this.labelChoose2.Location = new System.Drawing.Point(184, 0);
			this.labelChoose2.Name = "labelChoose2";
			this.labelChoose2.Size = new System.Drawing.Size(16, 16);
			this.labelChoose2.TabIndex = 3;
			this.labelChoose2.Text = "2";
			this.labelChoose2.TextAlign = System.Drawing.ContentAlignment.TopRight;
			// 
			// DCSBackGroundProperties
			// 
			this.Controls.Add(this.labelChoose2);
			this.Controls.Add(this.labelChoose1);
			this.Controls.Add(this.comboBoxGradient);
			this.Controls.Add(this.buttonChoose2);
			this.Controls.Add(this.labelImageName);
			this.Controls.Add(this.textBoxImageName);
			this.Controls.Add(this.pictureBox1);
			this.Controls.Add(this.buttonChoose);
			this.Controls.Add(this.cbFillType);
			this.Controls.Add(this.label2);
			this.Name = "DCSBackGroundProperties";
			this.Size = new System.Drawing.Size(212, 163);
			this.VisibleChanged += new System.EventHandler(this.DCSBackGroundProperties_VisibleChanged);
			((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
			this.ResumeLayout(false);
			this.PerformLayout();

		}
		#endregion

		private void AdjustVisibilities()
		{
			if (this.cbFillType.SelectedIndex == (int)DCSDEV.DCSDatatypes.BackFillTypes.FILL_COLOR)
			{
				this.labelChoose1.Text = "color";
				this.labelChoose2.Visible = false;
				this.buttonChoose.Visible = true;
				//this.buttonChoose.Text = "Color";
				this.buttonChoose2.Visible = false;
				this.comboBoxGradient.Visible = false;
				this.pictureBox1.Image = null;
				this.pictureBox1.Visible = true;
				this.textBoxImageName.Visible = false;
				this.labelImageName.Visible = true;
				this.labelImageName.Text = "Color:";
				this.pictureBox1.BackColor = m_color;
				this.pictureBox1.Bounds = m_rectOriginalPicture;
			}
			else if (this.cbFillType.SelectedIndex == (int)DCSDEV.DCSDatatypes.BackFillTypes.FILL_IMAGE)
			{
				this.labelChoose1.Text = "file";
				this.labelChoose2.Visible = false;
				this.buttonChoose.Visible = true;
				//this.buttonChoose.Text = "Image";
				this.buttonChoose2.Visible = false;
				this.comboBoxGradient.Visible = false;
				this.textBoxImageName.Visible = true;
				this.labelImageName.Visible = true;
				this.labelImageName.Text = "Image name:";
				this.pictureBox1.Bounds = m_rectOriginalPicture;
				if (this.pictureBox1.Image != null && this.textBoxImageName.Text != m_bitmapName)
				{
					// dispose of old image so it can be replaced
					if (m_bitmap != null) m_bitmap.Dispose();
					m_bitmap = null;
					this.pictureBox1.Image = null;
				}
				if (this.pictureBox1.Image == null && this.textBoxImageName.Text != "")
				{
					try 
					{ 
						m_bitmap = (Bitmap)DCSDEV.DCSDesignDataAccess.GetImage(this.textBoxImageName.Text, false);
						m_bitmapName = this.textBoxImageName.Text; 
						this.pictureBox1.Image = m_bitmap; 
						if (this.pictureBox1.Image != null)
							this.pictureBox1.Bounds = DCSMath.GetBiggestInnerRect(this.pictureBox1.Image.Size, m_rectOriginalPicture);
					}
					catch 
					{ 
						this.pictureBox1.Visible = false;	// done early so invisible while showing msg
						this.pictureBox1.Image = null;
						DCSDEV.DCSMsg.Show(String.Format("Image '{0}' cannot be opened.", this.textBoxImageName.Text)); 
					}
				}
				if (this.pictureBox1.Image == null) this.pictureBox1.Visible = false;
				else this.pictureBox1.Visible = true;
			}
			else if (this.cbFillType.SelectedIndex == (int)DCSDEV.DCSDatatypes.BackFillTypes.FILL_GRADIENT)
			{
				this.labelChoose1.Text = "color 1";
				this.labelChoose2.Visible = true;
				this.buttonChoose.Visible = true;
				//this.buttonChoose.Text = "Color1";
				this.buttonChoose2.Visible = true;
				this.comboBoxGradient.Visible = true;
				this.pictureBox1.Image = null;
				this.pictureBox1.Visible = true;
				this.textBoxImageName.Visible = false;
				this.labelImageName.Visible = true;
				this.labelImageName.Text = "Gradient:";
				this.pictureBox1.BackColor = m_color;
				this.pictureBox1.Bounds = m_rectOriginalPicture;
				// use LinearGradientMode
			}
			else if (this.cbFillType.SelectedIndex == (int)DCSDEV.DCSDatatypes.BackFillTypes.CONDITIONAL_COLOR)
			{
				this.labelChoose1.Text = "conditional color";
				this.labelChoose2.Visible = false;
				this.buttonChoose.Visible = true;
				//this.buttonChoose.Text = "Color";
				this.buttonChoose2.Visible = false;
				this.comboBoxGradient.Visible = false;
				this.pictureBox1.Image = null;
				this.pictureBox1.Visible = false;
				this.textBoxImageName.Visible = false;
				this.labelImageName.Visible = false;
				this.labelImageName.Text = "Color:";
				this.pictureBox1.BackColor = m_color;
				this.pictureBox1.Bounds = m_rectOriginalPicture;
			}
			else	// transparent
			{
				this.labelChoose1.Text = "";
				this.labelChoose2.Visible = false;
				this.buttonChoose.Visible = false;
				this.buttonChoose2.Visible = false;
				this.comboBoxGradient.Visible = false;
				this.pictureBox1.Visible = false;
				this.pictureBox1.Image = null;
				this.pictureBox1.Bounds = m_rectOriginalPicture;
				this.textBoxImageName.Visible = false;
				this.labelImageName.Visible = false;
			}
		}

		private void buttonChoose_Click(object sender, System.EventArgs e)
		{
			if (this.cbFillType.SelectedIndex == (int)DCSDEV.DCSDatatypes.BackFillTypes.FILL_COLOR)
			{
				this.colorDialog1.Color = Color.FromArgb(255, m_color);  // set alpha code to opaque
				System.Windows.Forms.DialogResult result = this.colorDialog1.ShowDialog(this);
				if (result != DialogResult.OK) return;
				m_color = this.colorDialog1.Color;
				AdjustVisibilities();
			}
			else if (this.cbFillType.SelectedIndex == (int)DCSDEV.DCSDatatypes.BackFillTypes.FILL_IMAGE)
			{
				string strName = DCSDEV.DCSDesignDataAccess.SelectOpenImageName(this.textBoxImageName.Text);
				if (strName == null) return;

				if (this.textBoxImageName.Text != strName && m_bitmap != null) 
				{
					m_bitmap.Dispose();
					m_bitmap = null;
				}
				this.textBoxImageName.Text = strName;
				AdjustVisibilities();
			}
			else if (this.cbFillType.SelectedIndex == (int)DCSDEV.DCSDatatypes.BackFillTypes.FILL_GRADIENT)
			{
				this.colorDialog1.Color = m_color;
				System.Windows.Forms.DialogResult result = this.colorDialog1.ShowDialog(this);
				if (result != DialogResult.OK) return;
				m_color = this.colorDialog1.Color;
				AdjustVisibilities();
			}
			else if (this.cbFillType.SelectedIndex == (int)DCSDEV.DCSDatatypes.BackFillTypes.CONDITIONAL_COLOR)
			{
				DCSDEV.DCSDesigner.DCSConditionalColor dlg = new DCSConditionalColor();

				dlg.Color1 = m_colorChoice1;
				dlg.Color2 = m_colorChoice2;
				dlg.Color3 = m_colorChoice3;
				dlg.ColorCondition1 = m_strColorCondition1;
				dlg.ColorCondition2 = m_strColorCondition2;
				dlg.AllDBFieldNames = m_AllDBFieldNames;

				System.Windows.Forms.DialogResult result = dlg.ShowDialog(this);

				m_colorChoice1 = dlg.Color1;
				m_colorChoice2 = dlg.Color2;
				m_colorChoice3 = dlg.Color3;
				m_strColorCondition1 = dlg.ColorCondition1;
				m_strColorCondition2 = dlg.ColorCondition2;
			}
		}

		private void comboBoxGradient_SelectedValueChanged(object sender, System.EventArgs e)
		{
			AdjustVisibilities();
		}

		private void buttonChoose2_Click(object sender, System.EventArgs e)
		{
			this.colorDialog1.Color = m_color2;
			System.Windows.Forms.DialogResult result = this.colorDialog1.ShowDialog(this);
			if (result != DialogResult.OK) return;
			m_color2 = this.colorDialog1.Color;
			AdjustVisibilities();
		}

		private void cbFillType_SelectedValueChanged(object sender, System.EventArgs e)
		{
            if (this.cbFillType.SelectedIndex == (int)DCSDEV.DCSDatatypes.BackFillTypes.CONDITIONAL_COLOR)
            {
                if (!DCSLicensing.IsLicensedOK(LicensedFeatures.ConditionalsOrFormulas, true))
                    this.cbFillType.SelectedIndex = 0;
            }
            AdjustVisibilities();
		}

		private void DCSBackGroundProperties_VisibleChanged(object sender, System.EventArgs e)
		{
			AdjustVisibilities();
		}

		private void pictureBox1_Paint(object sender, System.Windows.Forms.PaintEventArgs e)
		{
			if (this.cbFillType.SelectedIndex == (int)DCSDEV.DCSDatatypes.BackFillTypes.FILL_GRADIENT)
			{
				System.Drawing.Drawing2D.LinearGradientMode lgm = (System.Drawing.Drawing2D.LinearGradientMode)this.comboBoxGradient.SelectedIndex;
				Rectangle rect = new Rectangle(0,0,this.pictureBox1.Width, this.pictureBox1.Height);
				System.Drawing.Drawing2D.LinearGradientBrush lgbrush = new System.Drawing.Drawing2D.LinearGradientBrush(
					rect, m_color, m_color2, lgm);
				e.Graphics.FillRectangle(lgbrush, rect);
			}
		}

		public Color Color
		{
			get { return m_color; }
			set { m_color = value; }
		}
		public string Filename
		{
			get { return this.textBoxImageName.Text; }
			set 
			{ 
				this.pictureBox1.Image = null;
				this.textBoxImageName.Text = value; 
				try 
				{
                    if (this.BackFillType == DCSDatatypes.BackFillTypes.FILL_IMAGE)
                    {
                        m_bitmap = (Bitmap)DCSDEV.DCSDesignDataAccess.GetImage(this.textBoxImageName.Text, false);
                        m_bitmapName = this.textBoxImageName.Text;
                        this.pictureBox1.Image = m_bitmap;
                        if (this.pictureBox1.Image != null)
                            this.pictureBox1.Bounds = DCSMath.GetBiggestInnerRect(this.pictureBox1.Image.Size, m_rectOriginalPicture);
                    }
				}
				catch 
				{ 
					this.pictureBox1.Visible = false;	// done early so invisible while showing msg
					this.pictureBox1.Image = null;
					//DCSDEV.DCSMsg.Show(String.Format("Image '{0}' cannot be opened.", this.textBoxImageName.Text)); 
				}
			}
		}
		public DCSDatatypes.BackFillTypes BackFillType
		{
			get { return (DCSDatatypes.BackFillTypes)this.cbFillType.SelectedIndex; }
			set 
            {
                if ((int)value < 0) value = (DCSDatatypes.BackFillTypes)0;
                if (this.cbFillType.SelectedIndex == (int)DCSDEV.DCSDatatypes.BackFillTypes.CONDITIONAL_COLOR)
                {
                    if (!DCSLicensing.IsLicensedOK(LicensedFeatures.ConditionalsOrFormulas, false)) 
                        return;
                }
                this.cbFillType.SelectedIndex = (int)value;

            }
		}
		public Color Color2
		{
			get { return m_color2; }
			set { m_color2 = value; }
		}
		public System.Drawing.Drawing2D.LinearGradientMode GradientType
		{
			get { return (System.Drawing.Drawing2D.LinearGradientMode)this.comboBoxGradient.SelectedIndex; }
			set { this.comboBoxGradient.SelectedIndex = (int)value; }
		}
        public bool RestrictGradiantAndImage
        {
            get { return m_bRestrictGradiantAndImage; }
            set 
            { 
                m_bRestrictGradiantAndImage = value;
                int intIndex = this.cbFillType.SelectedIndex;
                this.cbFillType.Items.Clear();
                if (!m_bRestrictGradiantAndImage)
                {
                    this.cbFillType.Items.AddRange(new object[] {
                        "Color",
                        "None / Transparent",
                        "Image",
                        "Color Gradient",
						"Conditional Color"});
                }
                else
                {
                    this.cbFillType.Items.AddRange(new object[] {
                        "Color",
                        "None / Transparent"});
                    if (intIndex >= 2) intIndex = 0;
                }
                this.cbFillType.SelectedIndex = intIndex;
                this.AdjustVisibilities();
            }
        }
		public ArrayList AllDBFieldNames
		{
			get { return null; }
			set { if (value == null) return; foreach (string str in value) m_AllDBFieldNames.Add(str); }
		}
		public Color ConditionalColor1
		{
			get { return m_colorChoice1; }
			set { m_colorChoice1 = value; }
		}
		public Color ConditionalColor2
		{
			get { return m_colorChoice2; }
			set { m_colorChoice2 = value; }
		}
		public Color ConditionalColor3
		{
			get { return m_colorChoice3; }
			set { m_colorChoice3 = value; }
		}
		public string ColorCondition1
		{
			get { return m_strColorCondition1; }
			set { m_strColorCondition1 = value; }
		}
		public string ColorCondition2
		{
			get { return m_strColorCondition2; }
			set { m_strColorCondition2 = value; }
		}
	}
}
