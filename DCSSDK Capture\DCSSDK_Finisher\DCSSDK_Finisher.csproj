﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="12.0">
  <PropertyGroup>
    <ProjectType>Local</ProjectType>
    <ProductVersion>8.0.50727</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{4D22800C-2DB5-450B-A8A0-A130A3D50BD6}</ProjectGuid>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ApplicationIcon>
    </ApplicationIcon>
    <AssemblyKeyContainerName>
    </AssemblyKeyContainerName>
    <AssemblyName>DCSSDK_Finisher</AssemblyName>
    <AssemblyOriginatorKeyFile>
    </AssemblyOriginatorKeyFile>
    <DefaultClientScript>JScript</DefaultClientScript>
    <DefaultHTMLPageLayout>Grid</DefaultHTMLPageLayout>
    <DefaultTargetSchema>IE50</DefaultTargetSchema>
    <DelaySign>false</DelaySign>
    <OutputType>Library</OutputType>
    <RootNamespace>DCSFinisher</RootNamespace>
    <RunPostBuildEvent>OnBuildSuccess</RunPostBuildEvent>
    <StartupObject>
    </StartupObject>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <OldToolsVersion>2.0</OldToolsVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <BaseAddress>285212672</BaseAddress>
    <CheckForOverflowUnderflow>false</CheckForOverflowUnderflow>
    <ConfigurationOverrideFile>
    </ConfigurationOverrideFile>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DocumentationFile>
    </DocumentationFile>
    <DebugSymbols>true</DebugSymbols>
    <FileAlignment>4096</FileAlignment>
    <NoStdLib>false</NoStdLib>
    <NoWarn>
    </NoWarn>
    <Optimize>false</Optimize>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RemoveIntegerChecks>false</RemoveIntegerChecks>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningLevel>4</WarningLevel>
    <DebugType>full</DebugType>
    <ErrorReport>prompt</ErrorReport>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <BaseAddress>285212672</BaseAddress>
    <CheckForOverflowUnderflow>false</CheckForOverflowUnderflow>
    <ConfigurationOverrideFile>
    </ConfigurationOverrideFile>
    <DefineConstants>TRACE</DefineConstants>
    <DocumentationFile>
    </DocumentationFile>
    <DebugSymbols>false</DebugSymbols>
    <FileAlignment>4096</FileAlignment>
    <NoStdLib>false</NoStdLib>
    <NoWarn>
    </NoWarn>
    <Optimize>true</Optimize>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RemoveIntegerChecks>false</RemoveIntegerChecks>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningLevel>4</WarningLevel>
    <DebugType>none</DebugType>
    <ErrorReport>prompt</ErrorReport>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Emgu.CV, Version=2.3.0.1416, Culture=neutral, PublicKeyToken=7281126722ab4438, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Referenced Files\Emgu\Emgu.CV.dll</HintPath>
    </Reference>
    <Reference Include="Emgu.CV.GPU, Version=2.3.0.1416, Culture=neutral, PublicKeyToken=7281126722ab4438, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Referenced Files\Emgu\Emgu.CV.GPU.dll</HintPath>
    </Reference>
    <Reference Include="Emgu.CV.UI, Version=2.3.0.1416, Culture=neutral, PublicKeyToken=7281126722ab4438, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Referenced Files\Emgu\Emgu.CV.UI.dll</HintPath>
    </Reference>
    <Reference Include="Emgu.Util, Version=2.3.0.1416, Culture=neutral, PublicKeyToken=7281126722ab4438, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Referenced Files\Emgu\Emgu.Util.dll</HintPath>
    </Reference>
    <Reference Include="hasp_net_windows">
      <Name>hasp_net_windows</Name>
      <HintPath>..\Referenced Files\HASP\hasp_net_windows.dll</HintPath>
    </Reference>
    <Reference Include="LEAD, Version=13.0.0.50, Culture=neutral, PublicKeyToken=9cf889f53ea9b907">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Referenced Files\Lead.Net\LEAD.dll</HintPath>
    </Reference>
    <Reference Include="LEAD.Drawing, Version=13.0.0.50, Culture=neutral, PublicKeyToken=9cf889f53ea9b907">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Referenced Files\Lead.Net\LEAD.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="LEAD.Drawing.Imaging.Codecs, Version=13.0.0.50, Culture=neutral, PublicKeyToken=9cf889f53ea9b907">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Referenced Files\Lead.Net\LEAD.Drawing.Imaging.Codecs.dll</HintPath>
    </Reference>
    <Reference Include="LEAD.Drawing.Imaging.ImageProcessing, Version=13.0.0.50, Culture=neutral, PublicKeyToken=9cf889f53ea9b907">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Referenced Files\Lead.Net\LEAD.Drawing.Imaging.ImageProcessing.dll</HintPath>
    </Reference>
    <Reference Include="LEAD.Wrapper, Version=13.0.0.50, Culture=neutral, PublicKeyToken=9cf889f53ea9b907">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Referenced Files\Lead.Net\LEAD.Wrapper.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <Name>System</Name>
    </Reference>
    <Reference Include="System.Data">
      <Name>System.Data</Name>
    </Reference>
    <Reference Include="System.Drawing">
      <Name>System.Drawing</Name>
    </Reference>
    <Reference Include="System.Windows.Forms">
      <Name>System.Windows.Forms</Name>
    </Reference>
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
    </Reference>
    <ProjectReference Include="..\DCSSDK_FinisherProperties\DCSSDK_FinisherProperties.csproj">
      <Name>DCSSDK_FinisherProperties</Name>
      <Project>{65E7D81F-BD45-423C-9DE9-A9D66849B163}</Project>
      <Package>{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</Package>
    </ProjectReference>
    <ProjectReference Include="..\DCSSDK_Utilities\DCSSDK_Utilities.csproj">
      <Name>DCSSDK_Utilities</Name>
      <Project>{6EF6073A-143A-497A-9FFA-21DD39EBC9ED}</Project>
      <Package>{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</Package>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AboutForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="CropControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="DCSFaceFinder.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DCSImageControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="DCSImageProcessing.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DCSRotatorControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="FinisherMain.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ImgProcDlg.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="QCReportControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <EmbeddedResource Include="AboutForm.resx">
      <DependentUpon>AboutForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="CropControl.resx">
      <DependentUpon>CropControl.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSImageControl.resx">
      <DependentUpon>DCSImageControl.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSRotatorControl.resx">
      <DependentUpon>DCSRotatorControl.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FinisherMain.resx">
      <DependentUpon>FinisherMain.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ImgProcDlg.resx">
      <DependentUpon>ImgProcDlg.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="QCReportControl.resx">
      <DependentUpon>QCReportControl.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Content Include="NotOK.bmp" />
    <Content Include="OK.bmp" />
    <Content Include="UnknownOK.bmp" />
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PreBuildEvent>
    </PreBuildEvent>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
</Project>