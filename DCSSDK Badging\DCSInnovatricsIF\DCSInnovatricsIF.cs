using System;
using System.Drawing;
using System.Windows.Forms;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Runtime.InteropServices;
using Innovatrics.IEngine;

namespace DCSDEV.DCSInnovatricsIF
{
	public class DCSInnovatricsIF
	{
		private static Innovatrics.IEngine.IDKit m_idkit = null;
		private static string FindUser_LastImageID = null;
		private static int FindUser_LastUserID = -1;
		private static string m_strDataRootDir;

		public DCSInnovatricsIF()
		{
		}

		/// <summary>
		/// Initialize innovatrics IF - as static functions
		/// </summary>
		/// <param name="strDataRootDir"></param>
		/// <returns>true if successful</returns>
		public static bool Init(string strDataRootDir)
		{
			if (m_idkit != null) return true;
			m_strDataRootDir = strDataRootDir;
			
			try
			{
				// Initialize IDKit SDK
				m_idkit = Innovatrics.IEngine.IDKit.GetInstance();

				// Connect to database
				string strDatabase = System.IO.Path.Combine(m_strDataRootDir, "InnovatricsFingers.db"); 
				m_idkit.Connect(strDatabase);

				m_idkit.StoreImages = false;	// do not store finger images in the database
				m_idkit.CandidatesCount = 5;
//syh 1/1/10				m_idkit.Compression = true;		// might make GetuserIDS faster with ExpressID server
// m_idkit.ClearDatabase();
				return true;
			}
			catch (Innovatrics.IEngine.IEngineException iex)
			{
				MessageBox.Show(iex.Message);
				return false;
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message);
				m_idkit = null;
				return false;
			}
		}

		/// <summary>
		/// Add a finger to the user in the database identified by User ImageID Tag = strImageID
		/// </summary>
		/// <param name="strImageID">DCS Image ID</param>
		/// <param name="iDCSIndex">DCS finger index</param>
		/// <param name="strFingerFile">Image file</param>
		/// <returns>true if success</returns>
		public static bool AddFinger(string strImageID, int iDCSIndex, string strFingerFile)
		{
			if (m_idkit == null) return false;
			
			try
			{
				bool bExists = false;
				Innovatrics.IEngine.User user = null;
				int id;
				Innovatrics.IEngine.FingerPosition fingerposition = DCS2InnovatricsPosition(iDCSIndex);

				// find user if he already exists otherwise make him
				bool bRet = FindUser(strImageID, out user, out id);
				if (!bRet)
				{
					bExists = false;
					user = new User();
					user.Tags.Add("ImageID", strImageID);
				}
				else
				{
					bExists = true;
					// remove older instance of the same finger
					// assume each finger has only one instance
					foreach (Innovatrics.IEngine.Fingerprint fp in user.Fingerprints)
					{
						if (fp.Position == fingerposition)
						{
							user.Remove(fp);
							break;
						}
					}
				}

				// add new finger
				Bitmap bitmap = new Bitmap(strFingerFile);
				Fingerprint newfp = new Fingerprint(bitmap, fingerposition);
				user.Add(newfp);

				if (bExists)
				{
					m_idkit.UpdateUser(user, id);
				}
				else
				{
					id = m_idkit.RegisterUser(user);
				}
				bitmap.Dispose();
				return true;
			}
			catch (Innovatrics.IEngine.IEngineException iex)
			{
				MessageBox.Show(iex.Message);
				return false;
			}
		}

		/// <summary>
		/// Verify that the finger file is valid and enrollable
		/// </summary>
		/// <param name="strFingerFile"></param>
		/// <returns>true if the finger is ok.</returns>
		public static bool CheckFinger(string strFingerFile)
		{
			if (m_idkit == null) return false;

			try
			{
				Bitmap bitmap = new Bitmap(strFingerFile);
				Fingerprint fp = new Fingerprint(bitmap);
				// SYH TODO
				fp.Dispose();
				bitmap.Dispose();
				return true;
			}
			catch (Innovatrics.IEngine.IEngineException iex)
			{
				MessageBox.Show(iex.Message);
				return false;
			}
		}

		/// <summary>
		/// Look for fingers for the person with DCS Image ID.  
		/// Return the first 2 finger sub class (DCS index) numbers. 
		/// If there is no second finger set its index to -1.
		/// </summary>
		/// <param name="strImageID">DCS Image ID</param>
		/// <param name="iDCSIdx1">output DCS finger index</param>
		/// <param name="iDCSIdx2">output DCS finger index</param>
		/// <returns>Return false if there is an error or if there are no finger entires in the database.</returns>
		public static bool GetFingerIndices(string strImageID, out int iDCSIdx1, out int iDCSIdx2)
		{
			iDCSIdx1 = iDCSIdx2 = -1;
			if (m_idkit == null) return false;

			try
			{
				Innovatrics.IEngine.User user = null;
				int id;

				// find user. If he doesn't exist fingerprints were not processed for him
				bool bRet = FindUser(strImageID, out user, out id);
				if (!bRet)
				{
					// ERROR the indicates user has not been enrolled
					return false;
				}
				int iCount = 0;
				foreach (Innovatrics.IEngine.Fingerprint fp in user.Fingerprints)
				{
					if (iCount == 0)
					{
						iDCSIdx1 = Innovatrics2DCSIndex(fp.Position);
						iCount++;
					}
					else if (iCount == 1)
					{
						iDCSIdx2 = Innovatrics2DCSIndex(fp.Position);
						iCount++;
						break;
					}
				}
				return (iCount > 0);
			}
			catch (Innovatrics.IEngine.IEngineException iex)
			{
				MessageBox.Show(iex.Message);
				return false;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="strFingerFile"></param>
		/// <param name="iDCSFingerIndex"></param>
		/// <param name="arrayImageIDs"></param>
		/// <returns>Return false if there is an error.</returns>
		public static bool SearchForMatches(string strFingerFile, int iDCSFingerIndex, ArrayList arrayImageIDs)
		{
			arrayImageIDs.Clear();
			if (m_idkit == null) return false;

			Innovatrics.IEngine.FingerPosition position;
			Bitmap bitmap = null;
			Fingerprint fp = null;
			try
			{
				position = DCS2InnovatricsPosition(iDCSFingerIndex);
				bitmap = new Bitmap(strFingerFile);
				fp = new Fingerprint(bitmap);
				fp.Position = position;
				Innovatrics.IEngine.SearchResult searchResult = m_idkit.Find(fp);
				if (searchResult.Count == 0) return true;
				if (searchResult.Count > 0)
				{
					for (int i = 0; i < searchResult.Count; i++)
					{
						Innovatrics.IEngine.MatchResult mr = searchResult[i];
						Innovatrics.IEngine.User user = m_idkit.GetUser(mr.Id);
						string strImageID;
						user.Tags.TryGetValue("ImageID", out strImageID);
						arrayImageIDs.Add(strImageID);
					}
				}
			}
			catch (Innovatrics.IEngine.IEngineException iex)
			{
				MessageBox.Show(iex.Message);
				return false;
			}
			finally
			{
				if (fp != null) fp.Dispose();
				if (bitmap != null) bitmap.Dispose();
			}
			return true;
		}

		/// <summary>
		/// Verify that the two finger files match 
		/// </summary>
		/// <param name="strFingerFile1"></param>
		/// <param name="strFingerFile2"></param>
		/// <returns>true if the fingers match.</returns>
		public static bool MatchFinger(string strFingerFile1, string strFingerFile2)
		{
			if (m_idkit == null) return false;

			try
			{
				Bitmap bitmap1 = new Bitmap(strFingerFile1);
				Bitmap bitmap2 = new Bitmap(strFingerFile2);
				Fingerprint fp1 = new Fingerprint(bitmap1);
				Fingerprint fp2 = new Fingerprint(bitmap2);
				Innovatrics.IEngine.MatchResult mr = m_idkit.Match(fp1, fp2);
				fp1.Dispose();
				fp2.Dispose();
				bitmap1.Dispose();
				bitmap2.Dispose();
				return (mr.Score > 1000) ; 
			}
			catch (Innovatrics.IEngine.IEngineException iex)
			{
				MessageBox.Show(iex.Message);
				return false;
			}
		}

		/// <summary>
		/// Verify the finger versus the user in the Innovatrics database identified by user ImageID tag = strImageID 
		/// </summary>
		/// <param name="strImageID"></param>
		/// <param name="iDCSIndex"></param>
		/// <param name="strFingerFile"></param>
		/// <returns>matching score.</returns>
		public static int VerifyFinger(string strImageID, int iDCSIndex, string strFingerFile)
		{
			if (m_idkit == null) return -1;
			
			try
			{
				Innovatrics.IEngine.User userIn = null;
				Innovatrics.IEngine.User userDB = null;
				int id;
				Innovatrics.IEngine.FingerPosition fingerposition = DCS2InnovatricsPosition(iDCSIndex);

				// find user. If he doesn't exist fingerprints were not processed for him
				bool bRet = FindUser(strImageID, out userDB, out id);
				if (!bRet)
				{
					// ERROR the indicated user has not been enrolled
					return -1;
				}
				else
				{
					userIn = new User();
					// add new finger
					Bitmap bitmap = new Bitmap(strFingerFile);
					Fingerprint newfp = new Fingerprint(bitmap, fingerposition);
					userIn.Add(newfp);

					Innovatrics.IEngine.MatchResult mr = m_idkit.Match(userIn, userDB);
					userIn.Clear();
					userIn.Dispose();
					newfp.Dispose();
					bitmap.Dispose();

					return mr.Score;
				}
			}
			catch (Innovatrics.IEngine.IEngineException iex)
			{
				MessageBox.Show(iex.Message);
				return -1;
			}
		}

		// return Innovatrics user given DCS ImageID
		private static bool FindUser(string strImageID, out Innovatrics.IEngine.User user, out int id)
		{
			user = null;
			id = -1;
			if (m_idkit == null) return false;

			try
			{
				// find the user if he exists
				int[] ids;
				if (strImageID == FindUser_LastImageID)
				{
					user = m_idkit.GetUser(FindUser_LastUserID);
					id = FindUser_LastUserID;
					return true;
				}
				else
				{
					FindUser_LastImageID = null;
					ids = m_idkit.GetUserIDs("SELECT USERID FROM TAG_CACHE WHERE ImageID = '" + strImageID + "'");
					if (ids.Length == 0)
					{
						return false;
					}
					else
					{
						user = m_idkit.GetUser(ids[0]);		// there should only be one user with strImageID
						id = ids[0];
						FindUser_LastImageID = strImageID;
						FindUser_LastUserID = ids[0];
						return true;
					}
				}
			}
			catch (Innovatrics.IEngine.IEngineException iex)
			{
				MessageBox.Show(iex.Message);
				throw (iex);
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message);
				throw (ex);
			}
		}
		private static Innovatrics.IEngine.FingerPosition DCS2InnovatricsPosition(int iDCSFingerIndex)
		{
			switch (iDCSFingerIndex)
			{
				case 0:
					return FingerPosition.RightIndex;
				case 1:
					return FingerPosition.RightMiddle;
				case 2:
					return FingerPosition.RightRing;
				case 3:
					return FingerPosition.RightLittle;
				case 4:
					return FingerPosition.RightThumb;
				case 5:
					return FingerPosition.LeftIndex;
				case 6:
					return FingerPosition.LeftMiddle;
				case 7:
					return FingerPosition.LeftRing;
				case 8:
					return FingerPosition.LeftLittle;
				case 9:
					return FingerPosition.LeftThumb;
				default:
					return FingerPosition.Unknown;
			}
		}
		private static int Innovatrics2DCSIndex(Innovatrics.IEngine.FingerPosition fingerposition)
		{
			switch (fingerposition)
			{
				case FingerPosition.RightIndex:
					return 0;
				case FingerPosition.RightMiddle:
					return 1;
				case FingerPosition.RightRing:
					return 2;
				case FingerPosition.RightLittle:
					return 3;
				case FingerPosition.RightThumb:
					return 4;
				case FingerPosition.LeftIndex:
					return 5;
				case FingerPosition.LeftMiddle:
					return 6;
				case FingerPosition.LeftRing:
					return 7;
				case FingerPosition.LeftLittle:
					return 8;
				case FingerPosition.LeftThumb:
					return 9;
				case FingerPosition.Unknown:
				default:
					return -1;
			}
		}
	}
}
