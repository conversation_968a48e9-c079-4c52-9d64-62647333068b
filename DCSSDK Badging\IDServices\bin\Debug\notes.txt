
INSTALLATION INSTRUCTIONS:

1) Install thetest software by copying into DCSApp directory.

2) You must install the right version of <PERSON>'s ValCam driver. This may take some trial and error.

3) Run TestApp_DCS8000
goto DCS8000 capture properties
select DCS8010 landscape oriented camera, VGA resolution, <PERSON><PERSON>
click on the Enable PTZ FaceTracking check box

4)In the capture screen click on H=home in the uppper right corner set of controls.  this centers the camera.
Now you need to set a starting zoom amount that the camera will return to when it needs to get a 
wide angle view. Because of Cognitec limitations, this must not be too wide or the face will not be found.
Zoom to a scale so the person is about half of the size you would use to take a picture.  You can experiment with different zoom amounts, but until we get the Cognitec size limitation resolved, yo woon't have a lot of range in the zoom. 
Click on the read Finder Zoom Button.  This sets the starting zoom amount.

5)The automatic pan-tilt-zoom (PTZ) starts automatically except when you perform the setup operations. To start the PTZ finder, click on the Start Finder button.  If there is a person in the camera's view it will find it
and pan and tilt and zoom to the face.  Click Take when ready to take the picture.


MORE NOTES: 

The power to the camera must be disconnected before the firmware is updated in the camera. The NTSC and PAL version use different firmware and if the camera is not powered down, the new firmware will not be updated. This applies even if the computer is turned off and the USB cable is unpluged.

This program runs with USB_SDK.dll 3.0.1.0


