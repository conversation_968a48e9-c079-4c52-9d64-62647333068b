<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonDelete.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonDelete.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="buttonDelete.Location" type="System.Drawing.Point, System.Drawing">
    <value>130, 63</value>
  </data>
  <data name="buttonDelete.Size" type="System.Drawing.Size, System.Drawing">
    <value>102, 24</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonDelete.TabIndex" type="System.Int32, mscorlib">
    <value>77</value>
  </data>
  <data name="buttonDelete.Text" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="&gt;&gt;buttonDelete.Name" xml:space="preserve">
    <value>buttonDelete</value>
  </data>
  <data name="&gt;&gt;buttonDelete.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonDelete.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonDelete.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="textBoxChipScanFile.Location" type="System.Drawing.Point, System.Drawing">
    <value>200, 353</value>
  </data>
  <data name="textBoxChipScanFile.Size" type="System.Drawing.Size, System.Drawing">
    <value>151, 20</value>
  </data>
  <data name="textBoxChipScanFile.TabIndex" type="System.Int32, mscorlib">
    <value>76</value>
  </data>
  <data name="textBoxChipScanFile.Text" xml:space="preserve">
    <value>ChipScan.Dat</value>
  </data>
  <data name="&gt;&gt;textBoxChipScanFile.Name" xml:space="preserve">
    <value>textBoxChipScanFile</value>
  </data>
  <data name="&gt;&gt;textBoxChipScanFile.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxChipScanFile.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;textBoxChipScanFile.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label10.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label10.Location" type="System.Drawing.Point, System.Drawing">
    <value>200, 309</value>
  </data>
  <data name="label10.Size" type="System.Drawing.Size, System.Drawing">
    <value>140, 14</value>
  </data>
  <data name="label10.TabIndex" type="System.Int32, mscorlib">
    <value>74</value>
  </data>
  <data name="label10.Text" xml:space="preserve">
    <value>Chip data file</value>
  </data>
  <data name="&gt;&gt;label10.Name" xml:space="preserve">
    <value>label10</value>
  </data>
  <data name="&gt;&gt;label10.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label10.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;label10.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="textBoxChipEncodeFile.Location" type="System.Drawing.Point, System.Drawing">
    <value>200, 327</value>
  </data>
  <data name="textBoxChipEncodeFile.Size" type="System.Drawing.Size, System.Drawing">
    <value>151, 20</value>
  </data>
  <data name="textBoxChipEncodeFile.TabIndex" type="System.Int32, mscorlib">
    <value>72</value>
  </data>
  <data name="textBoxChipEncodeFile.Text" xml:space="preserve">
    <value>C:\DCSApp\Badge.Dat</value>
  </data>
  <data name="&gt;&gt;textBoxChipEncodeFile.Name" xml:space="preserve">
    <value>textBoxChipEncodeFile</value>
  </data>
  <data name="&gt;&gt;textBoxChipEncodeFile.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxChipEncodeFile.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;textBoxChipEncodeFile.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="buttonScanChip.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonScanChip.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 351</value>
  </data>
  <data name="buttonScanChip.Size" type="System.Drawing.Size, System.Drawing">
    <value>136, 24</value>
  </data>
  <data name="buttonScanChip.TabIndex" type="System.Int32, mscorlib">
    <value>70</value>
  </data>
  <data name="buttonScanChip.Text" xml:space="preserve">
    <value>Scan Chip Card</value>
  </data>
  <data name="&gt;&gt;buttonScanChip.Name" xml:space="preserve">
    <value>buttonScanChip</value>
  </data>
  <data name="&gt;&gt;buttonScanChip.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonScanChip.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonScanChip.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="buttonScan2DBarcode.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonScan2DBarcode.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 271</value>
  </data>
  <data name="buttonScan2DBarcode.Size" type="System.Drawing.Size, System.Drawing">
    <value>136, 24</value>
  </data>
  <data name="buttonScan2DBarcode.TabIndex" type="System.Int32, mscorlib">
    <value>69</value>
  </data>
  <data name="buttonScan2DBarcode.Text" xml:space="preserve">
    <value>Scan 2D Barcode</value>
  </data>
  <data name="&gt;&gt;buttonScan2DBarcode.Name" xml:space="preserve">
    <value>buttonScan2DBarcode</value>
  </data>
  <data name="&gt;&gt;buttonScan2DBarcode.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonScan2DBarcode.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonScan2DBarcode.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="buttonEncodeChip.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonEncodeChip.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 321</value>
  </data>
  <data name="buttonEncodeChip.Size" type="System.Drawing.Size, System.Drawing">
    <value>136, 24</value>
  </data>
  <data name="buttonEncodeChip.TabIndex" type="System.Int32, mscorlib">
    <value>68</value>
  </data>
  <data name="buttonEncodeChip.Text" xml:space="preserve">
    <value>Encode Chip</value>
  </data>
  <data name="&gt;&gt;buttonEncodeChip.Name" xml:space="preserve">
    <value>buttonEncodeChip</value>
  </data>
  <data name="&gt;&gt;buttonEncodeChip.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonEncodeChip.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonEncodeChip.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="tbImageTitle.Location" type="System.Drawing.Point, System.Drawing">
    <value>471, 93</value>
  </data>
  <data name="tbImageTitle.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 20</value>
  </data>
  <data name="tbImageTitle.TabIndex" type="System.Int32, mscorlib">
    <value>65</value>
  </data>
  <data name="&gt;&gt;tbImageTitle.Name" xml:space="preserve">
    <value>tbImageTitle</value>
  </data>
  <data name="&gt;&gt;tbImageTitle.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbImageTitle.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;tbImageTitle.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="label7.Location" type="System.Drawing.Point, System.Drawing">
    <value>471, 77</value>
  </data>
  <data name="label7.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 16</value>
  </data>
  <data name="label7.TabIndex" type="System.Int32, mscorlib">
    <value>64</value>
  </data>
  <data name="label7.Text" xml:space="preserve">
    <value>Optional Image Title</value>
  </data>
  <data name="&gt;&gt;label7.Name" xml:space="preserve">
    <value>label7</value>
  </data>
  <data name="&gt;&gt;label7.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label7.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;label7.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="buttonEditTxt.Location" type="System.Drawing.Point, System.Drawing">
    <value>360, 209</value>
  </data>
  <data name="buttonEditTxt.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 24</value>
  </data>
  <data name="buttonEditTxt.TabIndex" type="System.Int32, mscorlib">
    <value>63</value>
  </data>
  <data name="buttonEditTxt.Text" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="&gt;&gt;buttonEditTxt.Name" xml:space="preserve">
    <value>buttonEditTxt</value>
  </data>
  <data name="&gt;&gt;buttonEditTxt.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonEditTxt.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonEditTxt.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="buttonStartDCSPrinter.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonStartDCSPrinter.Location" type="System.Drawing.Point, System.Drawing">
    <value>456, 239</value>
  </data>
  <data name="buttonStartDCSPrinter.Size" type="System.Drawing.Size, System.Drawing">
    <value>136, 24</value>
  </data>
  <data name="buttonStartDCSPrinter.TabIndex" type="System.Int32, mscorlib">
    <value>62</value>
  </data>
  <data name="buttonStartDCSPrinter.Text" xml:space="preserve">
    <value>Start Printer</value>
  </data>
  <data name="&gt;&gt;buttonStartDCSPrinter.Name" xml:space="preserve">
    <value>buttonStartDCSPrinter</value>
  </data>
  <data name="&gt;&gt;buttonStartDCSPrinter.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonStartDCSPrinter.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonStartDCSPrinter.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="buttonSetupBadges.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonSetupBadges.Location" type="System.Drawing.Point, System.Drawing">
    <value>21, 164</value>
  </data>
  <data name="buttonSetupBadges.Size" type="System.Drawing.Size, System.Drawing">
    <value>278, 24</value>
  </data>
  <data name="buttonSetupBadges.TabIndex" type="System.Int32, mscorlib">
    <value>61</value>
  </data>
  <data name="buttonSetupBadges.Text" xml:space="preserve">
    <value>Setup Badges</value>
  </data>
  <data name="&gt;&gt;buttonSetupBadges.Name" xml:space="preserve">
    <value>buttonSetupBadges</value>
  </data>
  <data name="&gt;&gt;buttonSetupBadges.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonSetupBadges.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonSetupBadges.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="buttonSetupCapture.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonSetupCapture.Location" type="System.Drawing.Point, System.Drawing">
    <value>21, 33</value>
  </data>
  <data name="buttonSetupCapture.Size" type="System.Drawing.Size, System.Drawing">
    <value>211, 24</value>
  </data>
  <data name="buttonSetupCapture.TabIndex" type="System.Int32, mscorlib">
    <value>60</value>
  </data>
  <data name="buttonSetupCapture.Text" xml:space="preserve">
    <value>Setup Capture</value>
  </data>
  <data name="&gt;&gt;buttonSetupCapture.Name" xml:space="preserve">
    <value>buttonSetupCapture</value>
  </data>
  <data name="&gt;&gt;buttonSetupCapture.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonSetupCapture.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonSetupCapture.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="cbBadgeDat.Items" xml:space="preserve">
    <value>Badge.dat</value>
  </data>
  <data name="cbBadgeDat.Items1" xml:space="preserve">
    <value>Test1.Dat</value>
  </data>
  <data name="cbBadgeDat.Items2" xml:space="preserve">
    <value>Test2.Dat</value>
  </data>
  <data name="cbBadgeDat.Items3" xml:space="preserve">
    <value>Test3.Dat</value>
  </data>
  <data name="cbBadgeDat.Items4" xml:space="preserve">
    <value>Test4.Dat</value>
  </data>
  <data name="cbBadgeDat.Location" type="System.Drawing.Point, System.Drawing">
    <value>203, 209</value>
  </data>
  <data name="cbBadgeDat.Size" type="System.Drawing.Size, System.Drawing">
    <value>133, 21</value>
  </data>
  <data name="cbBadgeDat.TabIndex" type="System.Int32, mscorlib">
    <value>59</value>
  </data>
  <data name="cbBadgeDat.Text" xml:space="preserve">
    <value>Badge.dat</value>
  </data>
  <data name="&gt;&gt;cbBadgeDat.Name" xml:space="preserve">
    <value>cbBadgeDat</value>
  </data>
  <data name="&gt;&gt;cbBadgeDat.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbBadgeDat.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;cbBadgeDat.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="label6.Location" type="System.Drawing.Point, System.Drawing">
    <value>200, 232</value>
  </data>
  <data name="label6.Size" type="System.Drawing.Size, System.Drawing">
    <value>216, 32</value>
  </data>
  <data name="label6.TabIndex" type="System.Int32, mscorlib">
    <value>58</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>test1.dat ... reference document designs named test1.Bdg ...</value>
  </data>
  <data name="&gt;&gt;label6.Name" xml:space="preserve">
    <value>label6</value>
  </data>
  <data name="&gt;&gt;label6.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label6.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;label6.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="buttonClose.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonClose.Location" type="System.Drawing.Point, System.Drawing">
    <value>21, 124</value>
  </data>
  <data name="buttonClose.Size" type="System.Drawing.Size, System.Drawing">
    <value>102, 24</value>
  </data>
  <data name="buttonClose.TabIndex" type="System.Int32, mscorlib">
    <value>57</value>
  </data>
  <data name="buttonClose.Text" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="&gt;&gt;buttonClose.Name" xml:space="preserve">
    <value>buttonClose</value>
  </data>
  <data name="&gt;&gt;buttonClose.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonClose.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonClose.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="tbImageID.Location" type="System.Drawing.Point, System.Drawing">
    <value>375, 93</value>
  </data>
  <data name="tbImageID.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 20</value>
  </data>
  <data name="tbImageID.TabIndex" type="System.Int32, mscorlib">
    <value>43</value>
  </data>
  <data name="tbImageID.Text" xml:space="preserve">
    <value>test</value>
  </data>
  <data name="&gt;&gt;tbImageID.Name" xml:space="preserve">
    <value>tbImageID</value>
  </data>
  <data name="&gt;&gt;tbImageID.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbImageID.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;tbImageID.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="buttonDisplay.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonDisplay.Location" type="System.Drawing.Point, System.Drawing">
    <value>21, 94</value>
  </data>
  <data name="buttonDisplay.Size" type="System.Drawing.Size, System.Drawing">
    <value>102, 24</value>
  </data>
  <data name="buttonDisplay.TabIndex" type="System.Int32, mscorlib">
    <value>54</value>
  </data>
  <data name="buttonDisplay.Text" xml:space="preserve">
    <value>Display</value>
  </data>
  <data name="&gt;&gt;buttonDisplay.Name" xml:space="preserve">
    <value>buttonDisplay</value>
  </data>
  <data name="&gt;&gt;buttonDisplay.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonDisplay.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonDisplay.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>200, 193</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>192, 16</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>51</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>Badge DAT file name</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="buttonPreview.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonPreview.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 194</value>
  </data>
  <data name="buttonPreview.Size" type="System.Drawing.Size, System.Drawing">
    <value>136, 24</value>
  </data>
  <data name="buttonPreview.TabIndex" type="System.Int32, mscorlib">
    <value>50</value>
  </data>
  <data name="buttonPreview.Text" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="&gt;&gt;buttonPreview.Name" xml:space="preserve">
    <value>buttonPreview</value>
  </data>
  <data name="&gt;&gt;buttonPreview.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPreview.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonPreview.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="buttonPrint.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonPrint.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 220</value>
  </data>
  <data name="buttonPrint.Size" type="System.Drawing.Size, System.Drawing">
    <value>136, 24</value>
  </data>
  <data name="buttonPrint.TabIndex" type="System.Int32, mscorlib">
    <value>49</value>
  </data>
  <data name="buttonPrint.Text" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="&gt;&gt;buttonPrint.Name" xml:space="preserve">
    <value>buttonPrint</value>
  </data>
  <data name="&gt;&gt;buttonPrint.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPrint.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonPrint.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="buttonLayout.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonLayout.Location" type="System.Drawing.Point, System.Drawing">
    <value>456, 209</value>
  </data>
  <data name="buttonLayout.Size" type="System.Drawing.Size, System.Drawing">
    <value>136, 24</value>
  </data>
  <data name="buttonLayout.TabIndex" type="System.Int32, mscorlib">
    <value>48</value>
  </data>
  <data name="buttonLayout.Text" xml:space="preserve">
    <value>Design Layouts</value>
  </data>
  <data name="&gt;&gt;buttonLayout.Name" xml:space="preserve">
    <value>buttonLayout</value>
  </data>
  <data name="&gt;&gt;buttonLayout.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonLayout.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonLayout.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>239, 77</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>136, 16</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>Image classes</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="cbImageClass.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="cbImageClass.Items1" xml:space="preserve">
    <value>Portrait</value>
  </data>
  <data name="cbImageClass.Items2" xml:space="preserve">
    <value>Signature</value>
  </data>
  <data name="cbImageClass.Items3" xml:space="preserve">
    <value>Fingerprint</value>
  </data>
  <data name="cbImageClass.Items4" xml:space="preserve">
    <value>TenPrint</value>
  </data>
  <data name="cbImageClass.Location" type="System.Drawing.Point, System.Drawing">
    <value>239, 93</value>
  </data>
  <data name="cbImageClass.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 21</value>
  </data>
  <data name="cbImageClass.TabIndex" type="System.Int32, mscorlib">
    <value>46</value>
  </data>
  <data name="cbImageClass.Text" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="&gt;&gt;cbImageClass.Name" xml:space="preserve">
    <value>cbImageClass</value>
  </data>
  <data name="&gt;&gt;cbImageClass.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbImageClass.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;cbImageClass.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="buttonAllCapture.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonAllCapture.Location" type="System.Drawing.Point, System.Drawing">
    <value>21, 64</value>
  </data>
  <data name="buttonAllCapture.Size" type="System.Drawing.Size, System.Drawing">
    <value>102, 24</value>
  </data>
  <data name="buttonAllCapture.TabIndex" type="System.Int32, mscorlib">
    <value>45</value>
  </data>
  <data name="buttonAllCapture.Text" xml:space="preserve">
    <value>Capture</value>
  </data>
  <data name="&gt;&gt;buttonAllCapture.Name" xml:space="preserve">
    <value>buttonAllCapture</value>
  </data>
  <data name="&gt;&gt;buttonAllCapture.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAllCapture.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonAllCapture.ZOrder" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>375, 77</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 16</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>42</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>Image ID</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>25</value>
  </data>
  <data name="groupBoxTestControls.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 16</value>
  </data>
  <data name="groupBoxTestControls.Size" type="System.Drawing.Size, System.Drawing">
    <value>592, 383</value>
  </data>
  <data name="groupBoxTestControls.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="groupBoxTestControls.Text" xml:space="preserve">
    <value>Test controls</value>
  </data>
  <data name="&gt;&gt;groupBoxTestControls.Name" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;groupBoxTestControls.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBoxTestControls.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBoxTestControls.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label8.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label8.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 411</value>
  </data>
  <data name="label8.Size" type="System.Drawing.Size, System.Drawing">
    <value>55, 13</value>
  </data>
  <data name="label8.TabIndex" type="System.Int32, mscorlib">
    <value>67</value>
  </data>
  <data name="label8.Text" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="&gt;&gt;label8.Name" xml:space="preserve">
    <value>label8</value>
  </data>
  <data name="&gt;&gt;label8.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label8.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label8.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="comboBoxLanguage.Items" xml:space="preserve">
    <value />
  </data>
  <data name="comboBoxLanguage.Items1" xml:space="preserve">
    <value>fr-FR</value>
  </data>
  <data name="comboBoxLanguage.Location" type="System.Drawing.Point, System.Drawing">
    <value>263, 408</value>
  </data>
  <data name="comboBoxLanguage.Size" type="System.Drawing.Size, System.Drawing">
    <value>153, 21</value>
  </data>
  <data name="comboBoxLanguage.TabIndex" type="System.Int32, mscorlib">
    <value>66</value>
  </data>
  <data name="&gt;&gt;comboBoxLanguage.Name" xml:space="preserve">
    <value>comboBoxLanguage</value>
  </data>
  <data name="&gt;&gt;comboBoxLanguage.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxLanguage.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;comboBoxLanguage.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="buttonExit.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonExit.Location" type="System.Drawing.Point, System.Drawing">
    <value>448, 405</value>
  </data>
  <data name="buttonExit.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 24</value>
  </data>
  <data name="buttonExit.TabIndex" type="System.Int32, mscorlib">
    <value>50</value>
  </data>
  <data name="buttonExit.Text" xml:space="preserve">
    <value>Exit</value>
  </data>
  <data name="&gt;&gt;buttonExit.Name" xml:space="preserve">
    <value>buttonExit</value>
  </data>
  <data name="&gt;&gt;buttonExit.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonExit.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonExit.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleBaseSize" type="System.Drawing.Size, System.Drawing">
    <value>5, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>634, 438</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Badging Test Application</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>formTestBadging</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>