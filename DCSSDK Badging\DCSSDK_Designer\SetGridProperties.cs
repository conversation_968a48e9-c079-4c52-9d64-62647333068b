using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;

namespace DCSDEV.DCSDesigner
{
	/// <summary>
	/// Summary description for SetGridProperties.
	/// </summary>
    internal class SetGridProperties : System.Windows.Forms.Form
	{
		DCSDesignerDoc m_activeDoc;

		private System.Windows.Forms.Label label4;
		private System.Windows.Forms.Label label5;
		private System.Windows.Forms.NumericUpDown numericUpDownGridX;
		private System.Windows.Forms.NumericUpDown numericUpDownGridY;
		private System.Windows.Forms.Button buttonCancel;
		private System.Windows.Forms.Button buttonAccept;
		private Label label1;
		private Label labelDocumentName;
		private Label label2;
		private Label label3;
		private ListBox listBoxTabStopsH;
		private ListBox listBoxTabStopsV;
		private NumericUpDown numericUpDowntoAdd;
		private Button buttonAddToH;
		private Button buttonAddToV;
		private Button buttonRemoveFromH;
		private Button buttonRemoveFromV;
		private Label label6;
		private NumericUpDown numericUpDownTabRange;
		private Label label8;
		private Label label9;
		private Label label10;
		private Label label11;
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		public SetGridProperties(DCSDesignerDoc activeDoc)
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			m_activeDoc = activeDoc;
 
			this.labelDocumentName.Text = activeDoc.m_strDesignName;
			this.numericUpDownGridX.Value = (decimal)(activeDoc.m_GridSpacingX / 100.0);
			this.numericUpDownGridY.Value = (decimal)(activeDoc.m_GridSpacingY / 100.0);

			this.numericUpDownTabRange.Value = (decimal)(activeDoc.m_TabRange / 100.0);

			this.listBoxTabStopsH.Items.Clear();
			if (activeDoc.m_arrayTabStopsH != null)
			{
				foreach (int coord in activeDoc.m_arrayTabStopsH)
					this.listBoxTabStopsH.Items.Add(((decimal)coord / 100).ToString("00.00"));
			}
			this.listBoxTabStopsV.Items.Clear();
			if (activeDoc.m_arrayTabStopsV != null)
			{
				foreach (int coord in activeDoc.m_arrayTabStopsV)
					this.listBoxTabStopsV.Items.Add(((decimal)coord / 100).ToString("00.00"));
			}
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(SetGridProperties));
			this.label4 = new System.Windows.Forms.Label();
			this.label5 = new System.Windows.Forms.Label();
			this.numericUpDownGridX = new System.Windows.Forms.NumericUpDown();
			this.numericUpDownGridY = new System.Windows.Forms.NumericUpDown();
			this.buttonCancel = new System.Windows.Forms.Button();
			this.buttonAccept = new System.Windows.Forms.Button();
			this.label1 = new System.Windows.Forms.Label();
			this.labelDocumentName = new System.Windows.Forms.Label();
			this.label2 = new System.Windows.Forms.Label();
			this.label3 = new System.Windows.Forms.Label();
			this.listBoxTabStopsH = new System.Windows.Forms.ListBox();
			this.listBoxTabStopsV = new System.Windows.Forms.ListBox();
			this.numericUpDowntoAdd = new System.Windows.Forms.NumericUpDown();
			this.buttonAddToH = new System.Windows.Forms.Button();
			this.buttonAddToV = new System.Windows.Forms.Button();
			this.buttonRemoveFromH = new System.Windows.Forms.Button();
			this.buttonRemoveFromV = new System.Windows.Forms.Button();
			this.label6 = new System.Windows.Forms.Label();
			this.numericUpDownTabRange = new System.Windows.Forms.NumericUpDown();
			this.label8 = new System.Windows.Forms.Label();
			this.label9 = new System.Windows.Forms.Label();
			this.label10 = new System.Windows.Forms.Label();
			this.label11 = new System.Windows.Forms.Label();
			((System.ComponentModel.ISupportInitialize)(this.numericUpDownGridX)).BeginInit();
			((System.ComponentModel.ISupportInitialize)(this.numericUpDownGridY)).BeginInit();
			((System.ComponentModel.ISupportInitialize)(this.numericUpDowntoAdd)).BeginInit();
			((System.ComponentModel.ISupportInitialize)(this.numericUpDownTabRange)).BeginInit();
			this.SuspendLayout();
			// 
			// label4
			// 
			resources.ApplyResources(this.label4, "label4");
			this.label4.Name = "label4";
			// 
			// label5
			// 
			resources.ApplyResources(this.label5, "label5");
			this.label5.Name = "label5";
			// 
			// numericUpDownGridX
			// 
			this.numericUpDownGridX.DecimalPlaces = 2;
			this.numericUpDownGridX.Increment = new decimal(new int[] {
            1,
            0,
            0,
            131072});
			resources.ApplyResources(this.numericUpDownGridX, "numericUpDownGridX");
			this.numericUpDownGridX.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            65536});
			this.numericUpDownGridX.Minimum = new decimal(new int[] {
            2,
            0,
            0,
            131072});
			this.numericUpDownGridX.Name = "numericUpDownGridX";
			this.numericUpDownGridX.Value = new decimal(new int[] {
            5,
            0,
            0,
            131072});
			// 
			// numericUpDownGridY
			// 
			this.numericUpDownGridY.DecimalPlaces = 2;
			this.numericUpDownGridY.Increment = new decimal(new int[] {
            1,
            0,
            0,
            131072});
			resources.ApplyResources(this.numericUpDownGridY, "numericUpDownGridY");
			this.numericUpDownGridY.Maximum = new decimal(new int[] {
            1,
            0,
            0,
            0});
			this.numericUpDownGridY.Minimum = new decimal(new int[] {
            2,
            0,
            0,
            131072});
			this.numericUpDownGridY.Name = "numericUpDownGridY";
			this.numericUpDownGridY.Value = new decimal(new int[] {
            5,
            0,
            0,
            131072});
			// 
			// buttonCancel
			// 
			this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			resources.ApplyResources(this.buttonCancel, "buttonCancel");
			this.buttonCancel.Name = "buttonCancel";
			this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
			// 
			// buttonAccept
			// 
			this.buttonAccept.DialogResult = System.Windows.Forms.DialogResult.OK;
			resources.ApplyResources(this.buttonAccept, "buttonAccept");
			this.buttonAccept.Name = "buttonAccept";
			this.buttonAccept.Click += new System.EventHandler(this.buttonAccept_Click);
			// 
			// label1
			// 
			resources.ApplyResources(this.label1, "label1");
			this.label1.Name = "label1";
			// 
			// labelDocumentName
			// 
			resources.ApplyResources(this.labelDocumentName, "labelDocumentName");
			this.labelDocumentName.Name = "labelDocumentName";
			// 
			// label2
			// 
			resources.ApplyResources(this.label2, "label2");
			this.label2.Name = "label2";
			// 
			// label3
			// 
			resources.ApplyResources(this.label3, "label3");
			this.label3.Name = "label3";
			// 
			// listBoxTabStopsH
			// 
			this.listBoxTabStopsH.BackColor = System.Drawing.SystemColors.Control;
			this.listBoxTabStopsH.FormattingEnabled = true;
			resources.ApplyResources(this.listBoxTabStopsH, "listBoxTabStopsH");
			this.listBoxTabStopsH.Name = "listBoxTabStopsH";
			this.listBoxTabStopsH.SelectedIndexChanged += new System.EventHandler(this.listBoxTabStopsH_SelectedIndexChanged);
			// 
			// listBoxTabStopsV
			// 
			this.listBoxTabStopsV.BackColor = System.Drawing.SystemColors.Control;
			this.listBoxTabStopsV.FormattingEnabled = true;
			resources.ApplyResources(this.listBoxTabStopsV, "listBoxTabStopsV");
			this.listBoxTabStopsV.Name = "listBoxTabStopsV";
			this.listBoxTabStopsV.SelectedIndexChanged += new System.EventHandler(this.listBoxTabStopsV_SelectedIndexChanged);
			// 
			// numericUpDowntoAdd
			// 
			this.numericUpDowntoAdd.DecimalPlaces = 2;
			this.numericUpDowntoAdd.Increment = new decimal(new int[] {
            1,
            0,
            0,
            131072});
			resources.ApplyResources(this.numericUpDowntoAdd, "numericUpDowntoAdd");
			this.numericUpDowntoAdd.Maximum = new decimal(new int[] {
            9999,
            0,
            0,
            131072});
			this.numericUpDowntoAdd.Name = "numericUpDowntoAdd";
			this.numericUpDowntoAdd.Value = new decimal(new int[] {
            100,
            0,
            0,
            131072});
			// 
			// buttonAddToH
			// 
			resources.ApplyResources(this.buttonAddToH, "buttonAddToH");
			this.buttonAddToH.Name = "buttonAddToH";
			this.buttonAddToH.UseVisualStyleBackColor = true;
			this.buttonAddToH.Click += new System.EventHandler(this.buttonAddToH_Click);
			// 
			// buttonAddToV
			// 
			resources.ApplyResources(this.buttonAddToV, "buttonAddToV");
			this.buttonAddToV.Name = "buttonAddToV";
			this.buttonAddToV.UseVisualStyleBackColor = true;
			this.buttonAddToV.Click += new System.EventHandler(this.buttonAddToV_Click);
			// 
			// buttonRemoveFromH
			// 
			resources.ApplyResources(this.buttonRemoveFromH, "buttonRemoveFromH");
			this.buttonRemoveFromH.Name = "buttonRemoveFromH";
			this.buttonRemoveFromH.UseVisualStyleBackColor = true;
			this.buttonRemoveFromH.Click += new System.EventHandler(this.buttonRemoveFromH_Click);
			// 
			// buttonRemoveFromV
			// 
			resources.ApplyResources(this.buttonRemoveFromV, "buttonRemoveFromV");
			this.buttonRemoveFromV.Name = "buttonRemoveFromV";
			this.buttonRemoveFromV.UseVisualStyleBackColor = true;
			this.buttonRemoveFromV.Click += new System.EventHandler(this.buttonRemoveFromV_Click);
			// 
			// label6
			// 
			resources.ApplyResources(this.label6, "label6");
			this.label6.Name = "label6";
			// 
			// numericUpDownTabRange
			// 
			this.numericUpDownTabRange.DecimalPlaces = 2;
			this.numericUpDownTabRange.Increment = new decimal(new int[] {
            1,
            0,
            0,
            131072});
			resources.ApplyResources(this.numericUpDownTabRange, "numericUpDownTabRange");
			this.numericUpDownTabRange.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            65536});
			this.numericUpDownTabRange.Minimum = new decimal(new int[] {
            2,
            0,
            0,
            131072});
			this.numericUpDownTabRange.Name = "numericUpDownTabRange";
			this.numericUpDownTabRange.Value = new decimal(new int[] {
            5,
            0,
            0,
            131072});
			// 
			// label8
			// 
			resources.ApplyResources(this.label8, "label8");
			this.label8.Name = "label8";
			// 
			// label9
			// 
			resources.ApplyResources(this.label9, "label9");
			this.label9.Name = "label9";
			// 
			// label10
			// 
			resources.ApplyResources(this.label10, "label10");
			this.label10.Name = "label10";
			// 
			// label11
			// 
			resources.ApplyResources(this.label11, "label11");
			this.label11.Name = "label11";
			// 
			// SetGridProperties
			// 
			resources.ApplyResources(this, "$this");
			this.Controls.Add(this.label11);
			this.Controls.Add(this.label10);
			this.Controls.Add(this.label9);
			this.Controls.Add(this.numericUpDownTabRange);
			this.Controls.Add(this.label8);
			this.Controls.Add(this.label6);
			this.Controls.Add(this.buttonRemoveFromV);
			this.Controls.Add(this.buttonRemoveFromH);
			this.Controls.Add(this.buttonAddToV);
			this.Controls.Add(this.buttonAddToH);
			this.Controls.Add(this.numericUpDowntoAdd);
			this.Controls.Add(this.listBoxTabStopsV);
			this.Controls.Add(this.listBoxTabStopsH);
			this.Controls.Add(this.label3);
			this.Controls.Add(this.label2);
			this.Controls.Add(this.labelDocumentName);
			this.Controls.Add(this.label1);
			this.Controls.Add(this.buttonCancel);
			this.Controls.Add(this.buttonAccept);
			this.Controls.Add(this.numericUpDownGridY);
			this.Controls.Add(this.numericUpDownGridX);
			this.Controls.Add(this.label4);
			this.Controls.Add(this.label5);
			this.Name = "SetGridProperties";
			((System.ComponentModel.ISupportInitialize)(this.numericUpDownGridX)).EndInit();
			((System.ComponentModel.ISupportInitialize)(this.numericUpDownGridY)).EndInit();
			((System.ComponentModel.ISupportInitialize)(this.numericUpDowntoAdd)).EndInit();
			((System.ComponentModel.ISupportInitialize)(this.numericUpDownTabRange)).EndInit();
			this.ResumeLayout(false);

		}
		#endregion

		private void buttonAccept_Click(object sender, System.EventArgs e)
		{
			decimal dCoord;
			int iCoord;
			m_activeDoc.m_GridSpacingX = (int)(this.numericUpDownGridX.Value * (decimal)100.0);
			m_activeDoc.m_GridSpacingY = (int)(this.numericUpDownGridY.Value * (decimal)100.0);

			m_activeDoc.m_TabRange = (int)(this.numericUpDownTabRange.Value * (decimal)100.0);
			if (this.listBoxTabStopsH.Items.Count > 0)
			{
				if (m_activeDoc.m_arrayTabStopsH != null) m_activeDoc.m_arrayTabStopsH.Clear();
				else m_activeDoc.m_arrayTabStopsH = new ArrayList();

				foreach (string str in this.listBoxTabStopsH.Items)
				{
					dCoord = System.Convert.ToDecimal(str);
					iCoord = (int)(dCoord * 100);
					m_activeDoc.m_arrayTabStopsH.Add(iCoord);
				}
			}
			else
			{
				if (m_activeDoc.m_arrayTabStopsH != null) m_activeDoc.m_arrayTabStopsH.Clear();
			}

			if (this.listBoxTabStopsV.Items.Count > 0)
			{
				if (m_activeDoc.m_arrayTabStopsV != null) m_activeDoc.m_arrayTabStopsV.Clear();
				else m_activeDoc.m_arrayTabStopsV = new ArrayList();

				foreach (string str in this.listBoxTabStopsV.Items)
				{
					dCoord = System.Convert.ToDecimal(str);
					iCoord = (int)(dCoord * 100);
					m_activeDoc.m_arrayTabStopsV.Add(iCoord);
				}
			}
			else
			{
				if (m_activeDoc.m_arrayTabStopsV != null) m_activeDoc.m_arrayTabStopsV.Clear();
			}
		}

		private void buttonCancel_Click(object sender, System.EventArgs e)
		{
			this.Close();
		}

		private void buttonAddToH_Click(object sender, EventArgs e)
		{
			this.AddToList(this.listBoxTabStopsH.Items, m_activeDoc.Bounds.Width);
		}
		private void buttonAddToV_Click(object sender, EventArgs e)
		{
			this.AddToList(this.listBoxTabStopsV.Items, m_activeDoc.Bounds.Height);
		}

		private void AddToList(ListBox.ObjectCollection list, int max)
		{
			string str;
			if (this.numericUpDowntoAdd.Value < 0 || (int)(this.numericUpDowntoAdd.Value * 100) > max)
			{
				DCSMsg.Show(this.numericUpDowntoAdd.Value.ToString() + "\nValue must be within the bounds of the document design.");
				return;
			}
			str = this.numericUpDowntoAdd.Value.ToString("00.00");
			if (list.Contains(str))
			{
				DCSMsg.Show(this.numericUpDowntoAdd.Value.ToString() + "\nValue is already in the list.");
				return;
			}
			list.Add(str);
		}

		private void buttonRemoveFromH_Click(object sender, EventArgs e)
		{
			if (this.listBoxTabStopsH.SelectedIndex < 0) return;
			this.listBoxTabStopsH.Items.RemoveAt(this.listBoxTabStopsH.SelectedIndex);
		}

		private void buttonRemoveFromV_Click(object sender, EventArgs e)
		{
			if (this.listBoxTabStopsV.SelectedIndex < 0) return;
			this.listBoxTabStopsV.Items.RemoveAt(this.listBoxTabStopsV.SelectedIndex);
		}

		private void listBoxTabStopsH_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.buttonRemoveFromH.Enabled = (this.listBoxTabStopsH.SelectedIndex >= 0);
		}

		private void listBoxTabStopsV_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.buttonRemoveFromV.Enabled = (this.listBoxTabStopsV.SelectedIndex >= 0);
		}
	}
}
