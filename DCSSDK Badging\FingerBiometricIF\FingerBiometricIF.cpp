// FingerBiometricIF.cpp : Defines the initialization routines for the DLL.
//

#include "stdafx.h"
#include "FingerBiometricIF.h"
#include "AISFingerBiometric.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

//
//	Note!
//
//		If this DLL is dynamically linked against the MFC
//		DLLs, any functions exported from this DLL which
//		call into MFC must have the AFX_MANAGE_STATE macro
//		added at the very beginning of the function.
//
//		For example:
//
//		extern "C" BOOL PASCAL EXPORT ExportedFunction()
//		{
//			AFX_MANAGE_STATE(AfxGetStaticModuleState());
//			// normal function body here
//		}
//
//		It is very important that this macro appear in each
//		function, prior to any calls into MFC.  This means that
//		it must appear as the first statement within the 
//		function, even before any object variable declarations
//		as their constructors may generate calls into the MFC
//		DLL.
//
//		Please see MFC Technical Notes 33 and 58 for additional
//		details.
//

extern "C" __declspec (dllexport) bool DCSExtractFeatures(char* szFingerImage, char* szFingerFeaturesFile, long instance)
{
	AFX_MANAGE_STATE(AfxGetStaticModuleState());
	bool bRet = DoExtractFeatures(szFingerImage, szFingerFeaturesFile, instance);
	return bRet;
}
extern "C" __declspec (dllexport) long DCSVerifyFeatures(char* szFingerFeaturesFile1, char* szFingerFeaturesFile2)
{
	AFX_MANAGE_STATE(AfxGetStaticModuleState());
	return DoVerifyFeatures(szFingerFeaturesFile1, szFingerFeaturesFile2);
}
extern "C" __declspec (dllexport) long DCSMinutiaMatch(char* pFingerFeaturesArray1, char* pFingerFeaturesArray2)
{
	AFX_MANAGE_STATE(AfxGetStaticModuleState());
	return DoMinutiaMatch(pFingerFeaturesArray1, pFingerFeaturesArray2);
}
extern "C" __declspec (dllexport) long DCSVerifyFinger(char* szFingerImage, char* szFingerFeaturesFile, long instance)
{
	AFX_MANAGE_STATE(AfxGetStaticModuleState());
	return DoVerifyFinger(szFingerImage, szFingerFeaturesFile, instance);
}
extern "C" __declspec (dllexport) bool DCSMeasureQuality(char* szFingerImage, long* lDryness, long* lSmudginess, long* lOrientation, long* lRoll, long* lQuality)
{
	AFX_MANAGE_STATE(AfxGetStaticModuleState());
	return DoMeasureQuality(szFingerImage, lDryness, lSmudginess, lOrientation, lRoll, lQuality);
}

