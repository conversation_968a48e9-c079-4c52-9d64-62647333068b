using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace DCSDEV.DCSDesigner
{
	public partial class DCSFormulaAddField : Form
	{
		public DCSFormulaAddField(ArrayList arrayAllDBFieldNames)
		{
			InitializeComponent();

				foreach(string str in arrayAllDBFieldNames)
					this.listBoxFieldToAppend.Items.Add(str);
		}

		private void buttonCancel_Click(object sender, EventArgs e)
		{
			this.Close();
		}

		private void buttonOK_Click(object sender, EventArgs e)
		{
			if (this.listBoxFieldToAppend.Text == null || this.listBoxFieldToAppend.Text == "")
			{
				DialogResult = DialogResult.Cancel;
				DCSDEV.DCSMsg.Show("A database field must be selected.");
				return;
			}
			DialogResult = DialogResult.OK;
			this.Close();
		}

		public string FieldToAppend
		{
			get { return this.listBoxFieldToAppend.Text; }
			set { this.listBoxFieldToAppend.Text = value; }
		}
	}
}