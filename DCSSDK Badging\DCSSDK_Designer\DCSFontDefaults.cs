using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace DCSDEV.DCSDesigner
{
	public partial class DCSFontDefaults : Form
	{
		private int m_iCurrent;
		private DCSDesignerView m_view;
		private DCSDEV.DCSDesign.DCSDesign m_design;

		private GroupBox groupBox1;
		private DCSFontProperties dcsFontPropertiesLabelFont;
		private Button buttonOK;
		private Button buttonCancel;
		private GroupBox groupBoxFontIndex;
		private RadioButton radioButtonFont3;
		private RadioButton radioButtonFont2;
		private RadioButton radioButtonFont1;

		internal DCSFontDefaults(DCSDEV.DCSDesign.DCSDesign activeDoc, DCSDesignerView activeView)
		{
			InitializeComponent();

			m_view = activeView;
			m_design = activeDoc;
			m_iCurrent = 0;
			this.radioButtonFont1.Checked = true;
			this.MoveDataToDlg(0);
		}
		private void AdjustVisibilities()
		{
		}

		private void MoveDataToDlg(int index)
		{
			DCSDEV.DCSDesign.DCSFontEx defaultfont = ((DCSDEV.DCSDesign.DCSFontEx)this.m_design.m_arrayDocFontExs[index]);

			this.dcsFontPropertiesLabelFont.Units = DCSDEV.DCSDesignDataAccess.GetUnits();
			this.dcsFontPropertiesLabelFont.DCSFont = defaultfont.Font;
			this.dcsFontPropertiesLabelFont.LineSpacing = defaultfont.FontLineSpacing;
			this.dcsFontPropertiesLabelFont.DCSColor = defaultfont.ForeColor;
			this.dcsFontPropertiesLabelFont.DCSWordWrap = defaultfont.Wrap;
			this.dcsFontPropertiesLabelFont.DCSSizeToFit = defaultfont.SizeToFit;
			this.dcsFontPropertiesLabelFont.DCSShadow = defaultfont.Shadow;
			this.dcsFontPropertiesLabelFont.DCSShadowColor = defaultfont.ShadowColor;
		}

		private void MoveDataFromDlg(int index)
		{
			// position size
			DCSDEV.DCSDesign.DCSFontEx defaultfont = ((DCSDEV.DCSDesign.DCSFontEx)this.m_design.m_arrayDocFontExs[index]);
			
			defaultfont.Font = this.dcsFontPropertiesLabelFont.DCSFont;
			defaultfont.FontLineSpacing = this.dcsFontPropertiesLabelFont.LineSpacing;
			defaultfont.ForeColor = this.dcsFontPropertiesLabelFont.DCSColor;
			defaultfont.Wrap = this.dcsFontPropertiesLabelFont.DCSWordWrap;
			defaultfont.SizeToFit = this.dcsFontPropertiesLabelFont.DCSSizeToFit;
			defaultfont.Shadow = this.dcsFontPropertiesLabelFont.DCSShadow;
			defaultfont.ShadowColor = this.dcsFontPropertiesLabelFont.DCSShadowColor;
		}

		private void InitializeComponent()
		{
			this.groupBox1 = new System.Windows.Forms.GroupBox();
			this.buttonOK = new System.Windows.Forms.Button();
			this.buttonCancel = new System.Windows.Forms.Button();
			this.groupBoxFontIndex = new System.Windows.Forms.GroupBox();
			this.radioButtonFont3 = new System.Windows.Forms.RadioButton();
			this.radioButtonFont2 = new System.Windows.Forms.RadioButton();
			this.radioButtonFont1 = new System.Windows.Forms.RadioButton();
			this.dcsFontPropertiesLabelFont = new DCSDEV.DCSDesigner.DCSFontProperties();
			this.groupBox1.SuspendLayout();
			this.groupBoxFontIndex.SuspendLayout();
			this.SuspendLayout();
			// 
			// groupBox1
			// 
			this.groupBox1.Controls.Add(this.dcsFontPropertiesLabelFont);
			this.groupBox1.Location = new System.Drawing.Point(322, 51);
			this.groupBox1.Name = "groupBox1";
			this.groupBox1.Size = new System.Drawing.Size(214, 269);
			this.groupBox1.TabIndex = 1;
			this.groupBox1.TabStop = false;
			// 
			// buttonOK
			// 
			this.buttonOK.DialogResult = System.Windows.Forms.DialogResult.OK;
			this.buttonOK.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonOK.Location = new System.Drawing.Point(236, 374);
			this.buttonOK.Name = "buttonOK";
			this.buttonOK.Size = new System.Drawing.Size(133, 28);
			this.buttonOK.TabIndex = 2;
			this.buttonOK.Text = "O&K";
			this.buttonOK.UseVisualStyleBackColor = true;
			this.buttonOK.Click += new System.EventHandler(this.buttonOK_Click);
			// 
			// buttonCancel
			// 
			this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.buttonCancel.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonCancel.Location = new System.Drawing.Point(406, 374);
			this.buttonCancel.Name = "buttonCancel";
			this.buttonCancel.Size = new System.Drawing.Size(130, 28);
			this.buttonCancel.TabIndex = 3;
			this.buttonCancel.Text = "&Cancel";
			this.buttonCancel.UseVisualStyleBackColor = true;
			this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
			// 
			// groupBoxFontIndex
			// 
			this.groupBoxFontIndex.Controls.Add(this.radioButtonFont3);
			this.groupBoxFontIndex.Controls.Add(this.radioButtonFont2);
			this.groupBoxFontIndex.Controls.Add(this.radioButtonFont1);
			this.groupBoxFontIndex.Location = new System.Drawing.Point(92, 51);
			this.groupBoxFontIndex.Name = "groupBoxFontIndex";
			this.groupBoxFontIndex.Size = new System.Drawing.Size(214, 88);
			this.groupBoxFontIndex.TabIndex = 0;
			this.groupBoxFontIndex.TabStop = false;
			this.groupBoxFontIndex.Text = "Select document font";
			// 
			// radioButtonFont3
			// 
			this.radioButtonFont3.AutoSize = true;
			this.radioButtonFont3.Location = new System.Drawing.Point(23, 62);
			this.radioButtonFont3.Name = "radioButtonFont3";
			this.radioButtonFont3.Size = new System.Drawing.Size(104, 17);
			this.radioButtonFont3.TabIndex = 3;
			this.radioButtonFont3.TabStop = true;
			this.radioButtonFont3.Text = "Document font 2";
			this.radioButtonFont3.UseVisualStyleBackColor = true;
			this.radioButtonFont3.Click += new System.EventHandler(this.radioButtonFont3_Click);
			// 
			// radioButtonFont2
			// 
			this.radioButtonFont2.AutoSize = true;
			this.radioButtonFont2.Location = new System.Drawing.Point(23, 43);
			this.radioButtonFont2.Name = "radioButtonFont2";
			this.radioButtonFont2.Size = new System.Drawing.Size(104, 17);
			this.radioButtonFont2.TabIndex = 2;
			this.radioButtonFont2.TabStop = true;
			this.radioButtonFont2.Text = "Document font 1";
			this.radioButtonFont2.UseVisualStyleBackColor = true;
			this.radioButtonFont2.Click += new System.EventHandler(this.radioButtonFont2_Click);
			// 
			// radioButtonFont1
			// 
			this.radioButtonFont1.AutoSize = true;
			this.radioButtonFont1.Location = new System.Drawing.Point(23, 24);
			this.radioButtonFont1.Name = "radioButtonFont1";
			this.radioButtonFont1.Size = new System.Drawing.Size(72, 17);
			this.radioButtonFont1.TabIndex = 1;
			this.radioButtonFont1.TabStop = true;
			this.radioButtonFont1.Text = "Label font";
			this.radioButtonFont1.UseVisualStyleBackColor = true;
			this.radioButtonFont1.Click += new System.EventHandler(this.radioButtonFont1_Click);
			// 
			// dcsFontPropertiesLabelFont
			// 
			this.dcsFontPropertiesLabelFont.DCSColor = System.Drawing.Color.Empty;
			this.dcsFontPropertiesLabelFont.DCSFont = null;
			this.dcsFontPropertiesLabelFont.DCSShadow = false;
			this.dcsFontPropertiesLabelFont.DCSShadowColor = System.Drawing.Color.Black;
			this.dcsFontPropertiesLabelFont.DCSSimple = 'T';
			this.dcsFontPropertiesLabelFont.DCSSizeToFit = false;
			this.dcsFontPropertiesLabelFont.DCSWordWrap = false;
			this.dcsFontPropertiesLabelFont.LineSpacing = 0;
			this.dcsFontPropertiesLabelFont.Location = new System.Drawing.Point(12, 19);
			this.dcsFontPropertiesLabelFont.Name = "dcsFontPropertiesLabelFont";
			this.dcsFontPropertiesLabelFont.Size = new System.Drawing.Size(195, 240);
			this.dcsFontPropertiesLabelFont.TabIndex = 0;
			this.dcsFontPropertiesLabelFont.Units = 0;
			// 
			// DCSFontDefaults
			// 
			this.AcceptButton = this.buttonOK;
			this.CancelButton = this.buttonCancel;
			this.ClientSize = new System.Drawing.Size(632, 446);
			this.Controls.Add(this.groupBoxFontIndex);
			this.Controls.Add(this.buttonCancel);
			this.Controls.Add(this.buttonOK);
			this.Controls.Add(this.groupBox1);
			this.Name = "DCSFontDefaults";
			this.Text = "Document Fonts";
			this.groupBox1.ResumeLayout(false);
			this.groupBoxFontIndex.ResumeLayout(false);
			this.groupBoxFontIndex.PerformLayout();
			this.ResumeLayout(false);

		}

		private void buttonOK_Click(object sender, EventArgs e)
		{
			this.MoveDataFromDlg(m_iCurrent);
			this.Close();
		}

		private void buttonCancel_Click(object sender, EventArgs e)
		{
			this.Close();
		}

		private void radioButtonFont1_Click(object sender, EventArgs e)
		{
			if (!this.radioButtonFont1.Checked) this.radioButtonFont1.Checked = true;
			this.radioButtonFont2.Checked = false;
			this.radioButtonFont3.Checked = false;
			this.MoveDataFromDlg(m_iCurrent);
			m_iCurrent = 0;
			this.MoveDataToDlg(m_iCurrent);

		}

		private void radioButtonFont2_Click(object sender, EventArgs e)
		{
			if (!this.radioButtonFont2.Checked) this.radioButtonFont2.Checked = true;
			this.radioButtonFont1.Checked = false;
			this.radioButtonFont3.Checked = false;
			this.MoveDataFromDlg(m_iCurrent);
			m_iCurrent = 1;
			this.MoveDataToDlg(m_iCurrent);

		}

		private void radioButtonFont3_Click(object sender, EventArgs e)
		{
			if (!this.radioButtonFont3.Checked) this.radioButtonFont3.Checked = true;
			this.radioButtonFont1.Checked = false;
			this.radioButtonFont2.Checked = false;
			this.MoveDataFromDlg(m_iCurrent);
			m_iCurrent = 2;
			this.MoveDataToDlg(m_iCurrent);

		}
	}
}