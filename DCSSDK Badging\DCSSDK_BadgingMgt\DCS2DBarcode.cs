using System;
using System.Runtime.InteropServices;
using System.Drawing;

using DCSSDK;

namespace DCSSDK.BadgingMgt
{
	/// <summary>
	/// Summary description for DCS2DBarcode.
	/// </summary>
	public class DCS2DBarcode
	{
		// BIN2 in a PDF expects the following format
		// string strFormat = "LastName={0}&FirstName={1}&BIN2={2}#";	// lastname,firstname.bin 
		// strFull = String.Format(strFormat, "last", "first", strBIN);

		[DllImport("PDF417.Dll")]
		public static extern int Init(int nBarW, int nBarH, int nColumn);	
		//nBarW, nBarH: used for adjusting the width and height of each element in the 2D barcode
		//              If this value is too samll, the speed of reading will be reduced.
		//              the minimum value is 1, respectively.
																						  
		//nColumn: the column of generated 2D barcode. If it is 0, the system will automatically
		//         adjust the column.

		[DllImport("PDF417.Dll")]
		public static extern int GenBarcode(string strData, string strBmpFile);
		//strData:  the original data string, for example "abcdefg1234567890"
		//strBmpFile: the generated barcode Bmpfile pathe and name, for example " c:\\aaa.bmp"

		public DCS2DBarcode()
		{
			//
			// TODO: Add constructor logic here
			//
		}

		public static void DrawPDF417(System.Drawing.Graphics gr, string strToRip, System.Drawing.Rectangle rectDesignObjectAdjusted, double dScale, DCSSDK.DCSDatatypes.Alignments alignment, DCSSDK.DCSDatatypes.Justifications justification, int barcodeColumns, bool bLayout)
		{
			if (strToRip == null || strToRip == "") return;

			Bitmap bitmap2D = null;
			Rectangle rectObjectAdjustedScaled = DCSMath.RectTimesDouble(rectDesignObjectAdjusted, dScale);
			try
			{
				int nBarW = 2;
				int nBarH = 4;
				int nReturn = DCSSDK.BadgingMgt.DCS2DBarcode.Init(nBarW, nBarH, barcodeColumns);
				if (nReturn == 0)
				{
					string filename = ".\\PDF417.BMP";
					nReturn = DCSSDK.BadgingMgt.DCS2DBarcode.GenBarcode(strToRip, filename);
					if (nReturn == 0) bitmap2D = new Bitmap(filename);
				}
			}
			catch (Exception ex)
			{
				DCSMsg.Show("ERROR: in DoCreatePDF417()", ex);
			}
			if (bitmap2D != null)
			{
				Rectangle rect;
				double dbScaleW = (double)rectObjectAdjustedScaled.Width / (double)bitmap2D.Width;
				double dbScaleH = (double)rectObjectAdjustedScaled.Height / (double)bitmap2D.Height;
				double dbScale = Math.Min(dbScaleW, dbScaleH);

				// draw - let the scale become what it will
				rect = DCSSDK.DCSMath.RectTimesDouble(new Rectangle(Point.Empty, bitmap2D.Size), dbScale);

				int dx, dy;
				switch(alignment)
				{
					default:
					case DCSSDK.DCSDatatypes.Alignments.TOP:
						dy = 0;
						break;
					case DCSSDK.DCSDatatypes.Alignments.MIDDLE:
						dy = (rectObjectAdjustedScaled.Height - rect.Height) / 2;
						break;
					case DCSSDK.DCSDatatypes.Alignments.BOTTOM:
						dy = rectObjectAdjustedScaled.Height - rect.Height;
						break;
				}
				switch(justification)
				{
					default:
					case DCSSDK.DCSDatatypes.Justifications.LEFT:
						dx = 0;
						break;
					case DCSSDK.DCSDatatypes.Justifications.CENTER:
						dx = (rectObjectAdjustedScaled.Width - rect.Width) / 2;
						break;
					case DCSSDK.DCSDatatypes.Justifications.RIGHT:
						dx = rectObjectAdjustedScaled.Width - rect.Width;
						break;
				}
				rect.Location = rectObjectAdjustedScaled.Location + new Size(dx, dy);
				gr.DrawImage(bitmap2D, rect, new Rectangle(Point.Empty, bitmap2D.Size), GraphicsUnit.Pixel);
				
				// if must scale down (does not fit) show a gray x
				if (bLayout && dbScale < 1.0)
				{
					Pen pen = new Pen(Color.Gray, 2.0f);
					gr.DrawLine(pen, rectObjectAdjustedScaled.Location, rectObjectAdjustedScaled.Location + rectObjectAdjustedScaled.Size);
					gr.DrawLine(pen, rectObjectAdjustedScaled.Location + new Size(rectObjectAdjustedScaled.Width, 0), rectObjectAdjustedScaled.Location + new Size(0, rectObjectAdjustedScaled.Height));
				}

				bitmap2D.Dispose();
				bitmap2D = null;
			}
		}
	}
}
