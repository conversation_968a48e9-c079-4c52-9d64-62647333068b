using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;

namespace DCSDEV.DCSDesigner
{
	/// <summary>
	/// Summary description for BadgeDesignProperties.
	/// </summary>
    public class BadgeDesignProperties : System.Windows.Forms.Form
	{
        private DCSDesignerView m_view;
        private DCSDEV.DCSDesign.DCSDesign m_design;
		private int m_iSide = -1;
		private bool m_bNewDesign;
		private int m_iSelectedIndex = -1;

		private System.Windows.Forms.GroupBox groupBoxBackground;
		private System.Windows.Forms.Button buttonCancel;
		private System.Windows.Forms.Button buttonAccept;
		private System.Windows.Forms.Button buttonApply;
		private System.Windows.Forms.GroupBox groupBox2;
        private DCSDEV.DCSDesigner.DCSPositionSizeProperties dcsPositionSizeProperties1;
		private System.Windows.Forms.CheckBox checkBoxDoubleSided;
		private System.Windows.Forms.RadioButton radioSide2;
		private System.Windows.Forms.RadioButton radioSide1;
		private System.Windows.Forms.ComboBox comboPrinterType;
		private System.Windows.Forms.Label labelPreferredPrinterType;
		private System.Windows.Forms.CheckBox checkHasMagStripe;
		private System.Windows.Forms.GroupBox groupBoxMagStripe;
        private DCSDEV.DCSDesigner.DCSMagStripeProperties dcsMagStripeProperties1;
		private System.Windows.Forms.Button buttonPrinterTypes;
		private System.Windows.Forms.CheckBox checkBoxLandscape1;
		private System.Windows.Forms.CheckBox checkBoxLandscape2;
		private System.Windows.Forms.CheckBox checkHasChip;
		private System.Windows.Forms.GroupBox groupBoxChip;
		private System.Windows.Forms.TextBox tbFormulaChip;
		private System.Windows.Forms.Button buttonEditChipFormula;
		private System.Windows.Forms.ToolTip toolTip1;
		private DCSBackGroundProperties dcsBackGroundProperties1;
		private System.ComponentModel.IContainer components;

        internal BadgeDesignProperties(DCSDEV.DCSDesign.DCSDesign activeDoc, DCSDesignerView activeView, int iSide, bool bNew)
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			this.toolTip1.SetToolTip(this.buttonEditChipFormula, "Edit the formula that specifies which data will be put in the chip during printing (if supported) or in a separate encode operation.");
			this.toolTip1.SetToolTip(this.buttonPrinterTypes, "Edit properties of existing printer types or create new ones.");
			this.toolTip1.SetToolTip(this.checkHasChip, "Check if this design corresponds to a document that has a smart chip in it.");
			this.toolTip1.SetToolTip(this.checkHasMagStripe, "Only available when Printer Type supports magnetic strip encoding, check to encode when printing with this design.");
			this.toolTip1.SetToolTip(this.checkBoxLandscape1, "Indicates that the front side is viewed in the horizontal orientation.");
			this.toolTip1.SetToolTip(this.checkBoxLandscape2, "Indicates that the back side is viewed in the horizontal orientation. The Front and Back can have different settings.");
			this.toolTip1.SetToolTip(this.radioSide1, "Show properties of the front side of the document.");
			this.toolTip1.SetToolTip(this.radioSide2, "Show properties of the back side of the document.");
			this.toolTip1.SetToolTip(this.buttonApply,  "Save changes, apply changes to display, and keep dialog open.");
			this.toolTip1.SetToolTip(this.buttonAccept,  "Save changes, close dialog, and apply changes to display.");
			this.toolTip1.SetToolTip(this.buttonCancel,  "Undo changes and close dialog.");

			m_bNewDesign = bNew;
			m_view = activeView;
			m_design = activeDoc;

            this.PopulatePrinterTypeCombo();

            if (!m_bNewDesign)
            {
                int idx = this.m_design.PrinterTypeIndex;
                if (idx < 0 || idx >= this.comboPrinterType.Items.Count) idx = 0;
                DCSDEV.PrintProperties.PrinterTypeDatum bcDatum = (DCSDEV.PrintProperties.PrinterTypeDatum)this.m_view.m_mainWin.m_printertypeArray[idx];
                this.m_design.HasMagStripe = bcDatum.m_IfMagStripe;
            }
            else
            {
                this.m_design.HasMagStripe = false;
            }
            
			this.MoveDataToDlg();
			this.MoveDataToSideDlg(iSide);

			this.radioSide1.Checked = (iSide == 0);
			this.radioSide2.Checked = (iSide == 1);
			this.ShowSide(iSide);
			m_iSide = iSide;
			this.AdjustVisibilities();
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(BadgeDesignProperties));
            this.groupBoxBackground = new System.Windows.Forms.GroupBox();
            this.dcsBackGroundProperties1 = new DCSDEV.DCSDesigner.DCSBackGroundProperties();
            this.checkBoxLandscape2 = new System.Windows.Forms.CheckBox();
            this.checkBoxLandscape1 = new System.Windows.Forms.CheckBox();
            this.radioSide2 = new System.Windows.Forms.RadioButton();
            this.radioSide1 = new System.Windows.Forms.RadioButton();
            this.buttonCancel = new System.Windows.Forms.Button();
            this.buttonAccept = new System.Windows.Forms.Button();
            this.buttonApply = new System.Windows.Forms.Button();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.dcsPositionSizeProperties1 = new DCSDEV.DCSDesigner.DCSPositionSizeProperties();
            this.checkBoxDoubleSided = new System.Windows.Forms.CheckBox();
            this.comboPrinterType = new System.Windows.Forms.ComboBox();
            this.labelPreferredPrinterType = new System.Windows.Forms.Label();
            this.checkHasMagStripe = new System.Windows.Forms.CheckBox();
            this.groupBoxMagStripe = new System.Windows.Forms.GroupBox();
            this.dcsMagStripeProperties1 = new DCSDEV.DCSDesigner.DCSMagStripeProperties();
            this.buttonPrinterTypes = new System.Windows.Forms.Button();
            this.checkHasChip = new System.Windows.Forms.CheckBox();
            this.groupBoxChip = new System.Windows.Forms.GroupBox();
            this.tbFormulaChip = new System.Windows.Forms.TextBox();
            this.buttonEditChipFormula = new System.Windows.Forms.Button();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.groupBoxBackground.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.groupBoxMagStripe.SuspendLayout();
            this.groupBoxChip.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBoxBackground
            // 
            this.groupBoxBackground.Controls.Add(this.dcsBackGroundProperties1);
            this.groupBoxBackground.Controls.Add(this.checkBoxLandscape2);
            this.groupBoxBackground.Controls.Add(this.checkBoxLandscape1);
            this.groupBoxBackground.Controls.Add(this.radioSide2);
            this.groupBoxBackground.Controls.Add(this.radioSide1);
            this.groupBoxBackground.FlatStyle = System.Windows.Forms.FlatStyle.System;
            resources.ApplyResources(this.groupBoxBackground, "groupBoxBackground");
            this.groupBoxBackground.Name = "groupBoxBackground";
            this.groupBoxBackground.TabStop = false;
            // 
            // dcsBackGroundProperties1
            // 
            this.dcsBackGroundProperties1.AllDBFieldNames = null;
            this.dcsBackGroundProperties1.BackFillType = DCSDEV.DCSDatatypes.BackFillTypes.FILL_COLOR;
            this.dcsBackGroundProperties1.Color = System.Drawing.Color.LightBlue;
            this.dcsBackGroundProperties1.Color2 = System.Drawing.Color.MediumSlateBlue;
            this.dcsBackGroundProperties1.ColorCondition1 = null;
            this.dcsBackGroundProperties1.ColorCondition2 = null;
            this.dcsBackGroundProperties1.ConditionalColor1 = System.Drawing.Color.Empty;
            this.dcsBackGroundProperties1.ConditionalColor2 = System.Drawing.Color.Empty;
            this.dcsBackGroundProperties1.ConditionalColor3 = System.Drawing.Color.Empty;
            this.dcsBackGroundProperties1.Filename = "select name";
            this.dcsBackGroundProperties1.GradientType = System.Drawing.Drawing2D.LinearGradientMode.Horizontal;
            resources.ApplyResources(this.dcsBackGroundProperties1, "dcsBackGroundProperties1");
            this.dcsBackGroundProperties1.Name = "dcsBackGroundProperties1";
            this.dcsBackGroundProperties1.RestrictGradiantAndImage = false;
            // 
            // checkBoxLandscape2
            // 
            resources.ApplyResources(this.checkBoxLandscape2, "checkBoxLandscape2");
            this.checkBoxLandscape2.Name = "checkBoxLandscape2";
            this.checkBoxLandscape2.Click += new System.EventHandler(this.checkBoxLandscape2_Click);
            // 
            // checkBoxLandscape1
            // 
            resources.ApplyResources(this.checkBoxLandscape1, "checkBoxLandscape1");
            this.checkBoxLandscape1.Name = "checkBoxLandscape1";
            this.checkBoxLandscape1.Click += new System.EventHandler(this.checkBoxLandscape1_Click);
            // 
            // radioSide2
            // 
            resources.ApplyResources(this.radioSide2, "radioSide2");
            this.radioSide2.Name = "radioSide2";
            this.radioSide2.Click += new System.EventHandler(this.radioSide2_Click);
            // 
            // radioSide1
            // 
            resources.ApplyResources(this.radioSide1, "radioSide1");
            this.radioSide1.Name = "radioSide1";
            this.radioSide1.Click += new System.EventHandler(this.radioSide1_Click);
            // 
            // buttonCancel
            // 
            resources.ApplyResources(this.buttonCancel, "buttonCancel");
            this.buttonCancel.Name = "buttonCancel";
            this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
            // 
            // buttonAccept
            // 
            resources.ApplyResources(this.buttonAccept, "buttonAccept");
            this.buttonAccept.Name = "buttonAccept";
            this.buttonAccept.Click += new System.EventHandler(this.buttonAccept_Click);
            // 
            // buttonApply
            // 
            this.buttonApply.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.buttonApply, "buttonApply");
            this.buttonApply.Name = "buttonApply";
            this.buttonApply.UseVisualStyleBackColor = false;
            this.buttonApply.Click += new System.EventHandler(this.buttonApply_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.dcsPositionSizeProperties1);
            this.groupBox2.FlatStyle = System.Windows.Forms.FlatStyle.System;
            resources.ApplyResources(this.groupBox2, "groupBox2");
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.TabStop = false;
            // 
            // dcsPositionSizeProperties1
            // 
            this.dcsPositionSizeProperties1.DisplayBounds = new System.Drawing.Rectangle(0, 0, 0, 0);
            resources.ApplyResources(this.dcsPositionSizeProperties1, "dcsPositionSizeProperties1");
            this.dcsPositionSizeProperties1.Name = "dcsPositionSizeProperties1";
            this.dcsPositionSizeProperties1.Units = 0;
            this.dcsPositionSizeProperties1.Leave += new System.EventHandler(this.dcsPositionSizeProperties1_Leave);
            // 
            // checkBoxDoubleSided
            // 
            resources.ApplyResources(this.checkBoxDoubleSided, "checkBoxDoubleSided");
            this.checkBoxDoubleSided.Name = "checkBoxDoubleSided";
            this.checkBoxDoubleSided.Click += new System.EventHandler(this.checkBoxDoubleSided_Click);
            // 
            // comboPrinterType
            // 
            resources.ApplyResources(this.comboPrinterType, "comboPrinterType");
            this.comboPrinterType.Name = "comboPrinterType";
            this.comboPrinterType.SelectedIndexChanged += new System.EventHandler(this.comboPrinterType_SelectedIndexChanged);
            // 
            // labelPreferredPrinterType
            // 
            resources.ApplyResources(this.labelPreferredPrinterType, "labelPreferredPrinterType");
            this.labelPreferredPrinterType.Name = "labelPreferredPrinterType";
            // 
            // checkHasMagStripe
            // 
            resources.ApplyResources(this.checkHasMagStripe, "checkHasMagStripe");
            this.checkHasMagStripe.Name = "checkHasMagStripe";
            this.checkHasMagStripe.Click += new System.EventHandler(this.checkHasMagStripe_Click);
            // 
            // groupBoxMagStripe
            // 
            this.groupBoxMagStripe.Controls.Add(this.dcsMagStripeProperties1);
            this.groupBoxMagStripe.FlatStyle = System.Windows.Forms.FlatStyle.System;
            resources.ApplyResources(this.groupBoxMagStripe, "groupBoxMagStripe");
            this.groupBoxMagStripe.Name = "groupBoxMagStripe";
            this.groupBoxMagStripe.TabStop = false;
            // 
            // dcsMagStripeProperties1
            // 
            this.dcsMagStripeProperties1.FormulaTrack1 = "";
            this.dcsMagStripeProperties1.FormulaTrack2 = "";
            this.dcsMagStripeProperties1.FormulaTrack3 = "";
            this.dcsMagStripeProperties1.IfTrack1 = false;
            this.dcsMagStripeProperties1.IfTrack1Visible = true;
            this.dcsMagStripeProperties1.IfTrack2 = false;
            this.dcsMagStripeProperties1.IfTrack2Visible = true;
            this.dcsMagStripeProperties1.IfTrack3 = false;
            this.dcsMagStripeProperties1.IfTrack3Visible = false;
            resources.ApplyResources(this.dcsMagStripeProperties1, "dcsMagStripeProperties1");
            this.dcsMagStripeProperties1.Name = "dcsMagStripeProperties1";
            this.dcsMagStripeProperties1.SourceTrack1 = "MagTrack1";
            this.dcsMagStripeProperties1.SourceTrack2 = "MagTrack2";
            this.dcsMagStripeProperties1.SourceTrack3 = "MagTrack3";
            // 
            // buttonPrinterTypes
            // 
            resources.ApplyResources(this.buttonPrinterTypes, "buttonPrinterTypes");
            this.buttonPrinterTypes.Name = "buttonPrinterTypes";
            this.buttonPrinterTypes.Click += new System.EventHandler(this.buttonPrinterTypes_Click);
            // 
            // checkHasChip
            // 
            resources.ApplyResources(this.checkHasChip, "checkHasChip");
            this.checkHasChip.Name = "checkHasChip";
            this.checkHasChip.Click += new System.EventHandler(this.checkHasChip_Click);
            // 
            // groupBoxChip
            // 
            this.groupBoxChip.Controls.Add(this.tbFormulaChip);
            this.groupBoxChip.Controls.Add(this.buttonEditChipFormula);
            resources.ApplyResources(this.groupBoxChip, "groupBoxChip");
            this.groupBoxChip.Name = "groupBoxChip";
            this.groupBoxChip.TabStop = false;
            // 
            // tbFormulaChip
            // 
            resources.ApplyResources(this.tbFormulaChip, "tbFormulaChip");
            this.tbFormulaChip.Name = "tbFormulaChip";
            // 
            // buttonEditChipFormula
            // 
            resources.ApplyResources(this.buttonEditChipFormula, "buttonEditChipFormula");
            this.buttonEditChipFormula.Name = "buttonEditChipFormula";
            this.buttonEditChipFormula.Click += new System.EventHandler(this.buttonEditChipFormula_Click);
            // 
            // BadgeDesignProperties
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            resources.ApplyResources(this, "$this");
            this.Controls.Add(this.groupBoxChip);
            this.Controls.Add(this.checkHasChip);
            this.Controls.Add(this.buttonPrinterTypes);
            this.Controls.Add(this.groupBoxMagStripe);
            this.Controls.Add(this.checkHasMagStripe);
            this.Controls.Add(this.labelPreferredPrinterType);
            this.Controls.Add(this.comboPrinterType);
            this.Controls.Add(this.checkBoxDoubleSided);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.buttonApply);
            this.Controls.Add(this.buttonCancel);
            this.Controls.Add(this.buttonAccept);
            this.Controls.Add(this.groupBoxBackground);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "BadgeDesignProperties";
            this.ShowInTaskbar = false;
            this.groupBoxBackground.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            this.groupBoxMagStripe.ResumeLayout(false);
            this.groupBoxChip.ResumeLayout(false);
            this.groupBoxChip.PerformLayout();
            this.ResumeLayout(false);

		}
		#endregion

		private void AdjustVisibilities()
		{
			if (this.m_design.NumBadgeSides == 1)
			{
				this.radioSide2.Enabled = false;
				this.radioSide1.Checked = true;
				this.radioSide2.Checked = false;
				//ShowSide(0);
			}
			else
			{
				this.radioSide2.Enabled = true;
			}

			// mag strip in a document requires mag stripe is enabled in the PrinterTypeData
			this.groupBoxChip.Visible = (this.checkHasChip.Checked);

			// chip in a document does not require it in the PrinterTypeData, because it can/is encoded external to printer
			if (m_iSelectedIndex != -1)
			{
                DCSDEV.PrintProperties.PrinterTypeDatum bcDatum = (DCSDEV.PrintProperties.PrinterTypeDatum)this.m_view.m_mainWin.m_printertypeArray[m_iSelectedIndex];

				this.checkHasMagStripe.Visible = bcDatum.m_IfMagStripe;
				this.groupBoxMagStripe.Visible = bcDatum.m_IfMagStripe && this.checkHasMagStripe.Checked;

				this.dcsMagStripeProperties1.IfTrack1Visible = bcDatum.m_IfMagTrack1;
				this.dcsMagStripeProperties1.IfTrack2Visible = bcDatum.m_IfMagTrack2;
				this.dcsMagStripeProperties1.IfTrack3Visible = bcDatum.m_IfMagTrack3;
			}
			else
			{
				this.checkHasMagStripe.Visible = false;
				this.groupBoxMagStripe.Visible = false;
			}

			this.checkBoxLandscape1.Visible = (this.m_iSide == 0);
			this.checkBoxLandscape2.Visible = (this.m_iSide == 1);
		}

		private void MoveDataToDlg()
		{
			int idx;

			if (m_bNewDesign)
			{
				idx = -1;
				// position size
				this.dcsPositionSizeProperties1.DisplayBounds = Rectangle.Empty;
			}
			else
			{
				idx = this.m_design.PrinterTypeIndex;
				if (idx < 0 || idx >= this.comboPrinterType.Items.Count) idx = 0;
				// position size
				this.dcsPositionSizeProperties1.DisplayBounds = this.m_design.Bounds;
			}

			this.comboPrinterType.SelectedIndex = m_iSelectedIndex = idx;
			this.dcsPositionSizeProperties1.Units = DCSDEV.DCSDesignDataAccess.GetUnits();

			this.checkBoxDoubleSided.Checked = (this.m_design.NumBadgeSides > 1);

			this.checkHasChip.Checked = this.m_design.HasChip;
			this.tbFormulaChip.Text = this.m_design.FormulaChip;

			this.checkHasMagStripe.Checked =  this.m_design.HasMagStripe;
			if (this.m_design.SourceTrack1 != "")
			{
				this.dcsMagStripeProperties1.IfTrack1 = true;
				this.dcsMagStripeProperties1.SourceTrack1 = this.m_design.SourceTrack1;
				this.dcsMagStripeProperties1.FormulaTrack1 = this.m_design.FormulaTrack1;
			}
			else this.dcsMagStripeProperties1.IfTrack1 = false;
			if (this.m_design.SourceTrack2 != "")
			{
				this.dcsMagStripeProperties1.IfTrack2 = true;
				this.dcsMagStripeProperties1.SourceTrack2 = this.m_design.SourceTrack2;
				this.dcsMagStripeProperties1.FormulaTrack2 = this.m_design.FormulaTrack2;
			}
			else this.dcsMagStripeProperties1.IfTrack2 = false;
			if (this.m_design.SourceTrack3 != "")
			{
				this.dcsMagStripeProperties1.IfTrack3 = true;
				this.dcsMagStripeProperties1.SourceTrack3 = this.m_design.SourceTrack3;
				this.dcsMagStripeProperties1.FormulaTrack3 = this.m_design.FormulaTrack3;
			}
			else this.dcsMagStripeProperties1.IfTrack3 = false;

            this.dcsMagStripeProperties1.AllDBFieldNames = this.m_view.m_mainWin.m_AllDBFieldNames;

			this.dcsBackGroundProperties1.AllDBFieldNames = this.m_view.m_mainWin.m_AllDBFieldNames;

			// change landscape check box if necessary to conform to actual width::height ratio
			DCSDEV.DCSDesign.DCSDesignSide designSide = (DCSDEV.DCSDesign.DCSDesignSide)this.m_design.m_designSides[0];
			if (this.dcsPositionSizeProperties1.DisplayBounds.Width < this.dcsPositionSizeProperties1.DisplayBounds.Height)
				designSide.SideIsLandscape = false; 
			else if (this.dcsPositionSizeProperties1.DisplayBounds.Width > this.dcsPositionSizeProperties1.DisplayBounds.Height)
				designSide.SideIsLandscape = true; 
			this.checkBoxLandscape1.Checked = designSide.SideIsLandscape;

			if (this.m_design.m_designSides.Count > 1)
			{
				designSide = (DCSDEV.DCSDesign.DCSDesignSide)this.m_design.m_designSides[1];
				this.checkBoxLandscape2.Checked = designSide.SideIsLandscape;
			}
        }

		private void MoveDataFromDlg()
		{
			this.m_design.PrinterTypeIndex = this.comboPrinterType.SelectedIndex;

			// position size
			this.m_design.Bounds = this.dcsPositionSizeProperties1.DisplayBounds;
			DCSDEV.DCSDesignDataAccess.SetUnits(this.dcsPositionSizeProperties1.Units);

			this.m_design.NumBadgeSides = this.checkBoxDoubleSided.Checked ? 2 : 1;

			this.m_design.HasChip = this.checkHasChip.Checked;
			this.m_design.FormulaChip = this.tbFormulaChip.Text;

			this.m_design.HasMagStripe = this.checkHasMagStripe.Checked;
			if (this.dcsMagStripeProperties1.IfTrack1)
			{
				this.m_design.SourceTrack1 = this.dcsMagStripeProperties1.SourceTrack1;
				this.m_design.FormulaTrack1 = this.dcsMagStripeProperties1.FormulaTrack1;
			}
			else this.m_design.FormulaTrack1 = this.m_design.SourceTrack1 = "";
			if (this.dcsMagStripeProperties1.IfTrack2)
			{
				this.m_design.SourceTrack2 = this.dcsMagStripeProperties1.SourceTrack2;
				this.m_design.FormulaTrack2 = this.dcsMagStripeProperties1.FormulaTrack2;
			}
			else this.m_design.FormulaTrack2 = this.m_design.SourceTrack2 = "";
			if (this.dcsMagStripeProperties1.IfTrack3)
			{
				this.m_design.SourceTrack3 = this.dcsMagStripeProperties1.SourceTrack3;
				this.m_design.FormulaTrack3 = this.dcsMagStripeProperties1.FormulaTrack3;
			}
			else this.m_design.FormulaTrack3 = this.m_design.SourceTrack3 = "";
		}
		private void MoveDataToSideDlg(int iSide)
		{
			DCSDEV.DCSDesign.DCSDesignSide designSide = (DCSDEV.DCSDesign.DCSDesignSide)this.m_design.m_designSides[iSide];
//			this.dcsBackGroundProperties1.BackFillType = designSide.SideFillType;
			this.dcsBackGroundProperties1.Filename = designSide.SideBackImageName;
			this.dcsBackGroundProperties1.BackFillType = designSide.SideFillType;

			this.dcsBackGroundProperties1.Color = designSide.SideBackColor;
			this.dcsBackGroundProperties1.Color2 = designSide.SideBackColor2;
			this.dcsBackGroundProperties1.GradientType = designSide.SideBackGradientType;
			this.dcsBackGroundProperties1.ConditionalColor1 = designSide.SideColorChoice1;
			this.dcsBackGroundProperties1.ConditionalColor2 = designSide.SideColorChoice2;
			this.dcsBackGroundProperties1.ConditionalColor3 = designSide.SideColorChoice3;
			this.dcsBackGroundProperties1.ColorCondition1 = designSide.SideColorCondition1;
			this.dcsBackGroundProperties1.ColorCondition2 = designSide.SideColorCondition2;
		}
		private void MoveDataFromSideDlg(int iSide)
		{
			DCSDEV.DCSDesign.DCSDesignSide designSide = (DCSDEV.DCSDesign.DCSDesignSide)this.m_design.m_designSides[iSide];
			designSide.SideFillType = this.dcsBackGroundProperties1.BackFillType;
			designSide.SideBackImageName = this.dcsBackGroundProperties1.Filename;

			designSide.SideBackColor = this.dcsBackGroundProperties1.Color;
			designSide.SideBackColor2 = this.dcsBackGroundProperties1.Color2;
			designSide.SideBackGradientType   = this.dcsBackGroundProperties1.GradientType;
			designSide.SideColorChoice1 = this.dcsBackGroundProperties1.ConditionalColor1;
			designSide.SideColorChoice2 = this.dcsBackGroundProperties1.ConditionalColor2;
			designSide.SideColorChoice3 = this.dcsBackGroundProperties1.ConditionalColor3;
			designSide.SideColorCondition1 = this.dcsBackGroundProperties1.ColorCondition1;
			designSide.SideColorCondition2 = this.dcsBackGroundProperties1.ColorCondition2;

			if (iSide == 0)
				designSide.SideIsLandscape = this.checkBoxLandscape1.Checked;
			else
				designSide.SideIsLandscape = this.checkBoxLandscape2.Checked;
		}

		private void PopulatePrinterTypeCombo()
		{
			this.comboPrinterType.Items.Clear();
            foreach (DCSDEV.PrintProperties.PrinterTypeDatum bcDatum in this.m_view.m_mainWin.m_printertypeArray) 
			{
				this.comboPrinterType.Items.Add(bcDatum.m_PrinterTypeName);
			}
		}

		private void ShowSide(int iSide)
		{
			if (m_iSide == iSide) return;
			if (m_iSide != -1 && m_iSide != iSide)
				MoveDataFromSideDlg(m_iSide);	// save to side you are leaving
			MoveDataToSideDlg(iSide);
			m_iSide = iSide;
		}

		private bool DoApply()
		{
			// check validity of printer type
			if (this.comboPrinterType.SelectedIndex < 0)
			{
				DCSDEV.DCSMsg.Show("Printer Type must be selected");
				this.comboPrinterType.Focus();
				return false;
			}

			Rectangle rectBounds = new Rectangle(Point.Empty, this.dcsPositionSizeProperties1.DisplayBounds.Size);
			bool bObjectAreOutside = false;
			int i;
			for (i=0; i<m_design.NumBadgeSides; i++)
			{
				if (i == 0)
				{
					if (!m_design.AreAllObjectsInside(i, rectBounds)) bObjectAreOutside = true;
				}
				else
				{
					// if back side orientation is not same as front swap dimensions
					Rectangle r = rectBounds;
					if (this.checkBoxLandscape1.Checked != this.checkBoxLandscape2.Checked) DCSMath.SwapWandH(ref r);
					if (!m_design.AreAllObjectsInside(i, r)) bObjectAreOutside = true;
				}
			}
			if (bObjectAreOutside)
			{
				DialogResult result = DCSDEV.DCSMsg.ShowYNC("One or more objects lie outside the document boundaries.\nDo you want them automatically moved?");
				if (result == DialogResult.Cancel)
					return false;
				else if (result == DialogResult.Yes)
				{
					for (i=0; i<m_design.NumBadgeSides; i++)
					{
						if (i == 0)
						{
							m_design.MoveAllObjectsInside(i, rectBounds);
						}
						else
						{
							// if back side orientation is not same as front swap dimensions
							Rectangle r = rectBounds;
							if (this.checkBoxLandscape1.Checked != this.checkBoxLandscape2.Checked) DCSMath.SwapWandH(ref r);
							m_design.MoveAllObjectsInside(i, r);
						}
					}
				}
			}

			this.MoveDataFromDlg();
			this.MoveDataFromSideDlg(this.m_iSide);

			// check that printer type and size correlate
			if (this.comboPrinterType.SelectedIndex == 0)	// cards
			{
				if (this.m_design.Bounds.Width > 4*100 || this.m_design.Bounds.Height > 4*100)
				{
					DCSDEV.DCSMsg.Show(String.Format("Printer Type {0} requires sizes less than 4 inches.", this.comboPrinterType.Text));
					return false;
				}
			}
			if (this.comboPrinterType.SelectedIndex == 1)	// large size
			{
				if (this.m_design.Bounds.Width <= 4*100 && this.m_design.Bounds.Height <= 4*100)
				{
					DCSDEV.DCSMsg.Show(String.Format("Printer Type '{0}' requires sizes larger than 4 inches.", this.comboPrinterType.Text));
					return false;
				}
			}

			// load the images that will be used
			foreach (DCSDEV.DCSDesign.DCSDesignSide designSide in this.m_design.m_designSides)
			{
				if (designSide.SideFillType == DCSDEV.DCSDatatypes.BackFillTypes.FILL_IMAGE)
					try
					{
						if (designSide.SideBackImage != null) designSide.SideBackImage.Dispose();
						designSide.SideBackImage = DCSDEV.DCSDesignDataAccess.GetImage(designSide.SideBackImageName, false);
					}
					catch (Exception ex)
					{
						DCSDEV.DCSMsg.Show(ex);
					}
			}
			return true;
		}

		private void radioSide1_Click(object sender, System.EventArgs e)
		{
			this.ShowSide( (this.radioSide1.Checked) ? 0 : 1);
			this.AdjustVisibilities();
		}

		private void radioSide2_Click(object sender, System.EventArgs e)
		{
			this.ShowSide( (this.radioSide2.Checked) ? 1 : 0);
		}

		private void buttonAccept_Click(object sender, System.EventArgs e)
		{
			bool bRet;
			bRet = DoApply();
			if (!bRet) return;
			this.DialogResult = DialogResult.OK;
			this.Close();
		}

		private void buttonCancel_Click(object sender, System.EventArgs e)
		{
			this.DialogResult = DialogResult.Cancel;
			this.Close();
		}

		private void buttonApply_Click(object sender, System.EventArgs e)
		{
			bool bRet;
			bRet = DoApply();
			if (!bRet) return;

			m_design.m_isDirty = true;
			m_design.m_isViewDirty = true;
			m_view.Invalidate(true);
		}

		// the sides code is semi-prepared to handle more than two sides - which can equate to
		// layers for special multi-pass printer effects.  Sorry it is a little wierd for now.
		private void checkBoxDoubleSided_Click(object sender, System.EventArgs e)
		{
			if (this.checkBoxDoubleSided.Checked == false && this.m_design.NumBadgeSides > 1)
			{
				DCSDEV.DCSMsg.Show("Cannot reduce number of sides this way. Use Eliminate Empty Sides menu item.");
				this.checkBoxDoubleSided.Checked = true;;
			}
			else if (this.checkBoxDoubleSided.Checked == true && this.m_design.NumBadgeSides == 1)
			{
				DCSDEV.DCSDesign.DCSDesignSide designSide = new DCSDEV.DCSDesign.DCSDesignSide();
				this.m_design.m_designSides.Add(designSide);
				this.m_design.NumBadgeSides = this.m_design.m_designSides.Count;
				this.checkBoxLandscape2.Checked = designSide.SideIsLandscape;
				this.AdjustVisibilities();
			}
		}

		private void checkHasMagStripe_Click(object sender, System.EventArgs e)
		{
			this.AdjustVisibilities();
		}

		private void buttonPrinterTypes_Click(object sender, System.EventArgs e)
		{
			// extract data from controls
			this.MoveDataFromDlg();
			for (int iSide=0; iSide<m_design.NumBadgeSides; iSide++)
				this.MoveDataFromSideDlg(iSide);

            DCSDEV.PrintProperties.BadgingMgtForm printProp = new DCSDEV.PrintProperties.BadgingMgtForm();
            printProp.ShowDialog();
            printProp.Dispose();

			this.m_view.m_mainWin.m_printertypeArray.Clear();
			this.m_view.m_mainWin.m_printertypeArray.LoadPrinterTypeArray();
			this.PopulatePrinterTypeCombo();

			this.MoveDataToDlg();
			this.AdjustVisibilities();
		}

		// the document bounds displayed follows the orientation of the front of the card
		private void checkBoxLandscape1_Click(object sender, System.EventArgs e)
		{
			bool bIsLandscape = (this.dcsPositionSizeProperties1.DisplayBounds.Height < this.dcsPositionSizeProperties1.DisplayBounds.Width);
			if (this.checkBoxLandscape1.Checked != bIsLandscape)
			{
				// landscape is requested and bounds is portrait or portrait is requested and bounds is landscape
				Rectangle rect = this.dcsPositionSizeProperties1.DisplayBounds;
				DCSMath.SwapWandH(ref rect);
				this.dcsPositionSizeProperties1.DisplayBounds = rect;
			}
		}

		// The back orientation does not show up in the displayed bounds.
		private void checkBoxLandscape2_Click(object sender, System.EventArgs e)
		{
		}

		private void dcsPositionSizeProperties1_Leave(object sender, System.EventArgs e)
		{
			// the displayed width and height refer to the front dimensions
			DCSDEV.DCSDesign.DCSDesignSide designSide = (DCSDEV.DCSDesign.DCSDesignSide)this.m_design.m_designSides[0];
			// change landscape check box if necessary to conform to actual width::height ratio
			if (this.dcsPositionSizeProperties1.DisplayBounds.Width < this.dcsPositionSizeProperties1.DisplayBounds.Height)
				designSide.SideIsLandscape = false; 
			else if (this.dcsPositionSizeProperties1.DisplayBounds.Width > this.dcsPositionSizeProperties1.DisplayBounds.Height)
				designSide.SideIsLandscape = true; 
			this.checkBoxLandscape1.Checked = designSide.SideIsLandscape;
		}

		private void comboPrinterType_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			m_iSelectedIndex = this.comboPrinterType.SelectedIndex;
			if (this.m_bNewDesign)
			{
                DCSDEV.PrintProperties.PrinterTypeDatum bcDatum = (DCSDEV.PrintProperties.PrinterTypeDatum)this.m_view.m_mainWin.m_printertypeArray[m_iSelectedIndex];
				this.m_design.Bounds.Width = bcDatum.m_DefaultW;
				this.m_design.Bounds.Height = bcDatum.m_DefaultH;
				this.dcsPositionSizeProperties1.DisplayBounds = this.m_design.Bounds;
				m_bNewDesign = false;
			}
			this.AdjustVisibilities();
		}

		private void checkHasChip_Click(object sender, System.EventArgs e)
		{
			this.AdjustVisibilities();
		}

		private void buttonEditChipFormula_Click(object sender, System.EventArgs e)
		{
			string strFormula = this.tbFormulaChip.Text;
            DCSDEV.DCSDesigner.DCSFormulaDesigner.CallFormulaBuilder(this.m_view.m_mainWin.m_AllDBFieldNames, ref strFormula, DCSFormulaDesigner.FormulaModeType.ALL_MODES);
			if (strFormula != null) this.tbFormulaChip.Text = strFormula;
		}

		private void dcsBackGroundProperties1_Load(object sender, EventArgs e)
		{

		}
	}
}
