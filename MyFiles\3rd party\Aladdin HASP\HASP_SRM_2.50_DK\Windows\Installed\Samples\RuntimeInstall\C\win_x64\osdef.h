/*  $Id: osdef.h,v 1.2.2.1 2007/05/31 16:04:03 horatiu Exp $
**
**  Aladdin Knowledge Systems Ltd. (c) 1985-2007. All rights reserved.
**
**  osdef.h - OS Version definitions
**
*/

/*
 * OS bit definitions
 */
#define WIN_95      0x00000001	
#define WIN_98      0x00000002	
#define WIN_ME      0x00000004	
#define WIN_NT      0x00000008	
#define WIN_2K      0x00000010	
#define WIN_XP      0x00000020	
#define WIN_2K3     0x00000040	
#define VISTA       0x00000080

#define VALID_OS  	(WIN_98 | WIN_ME | WIN_NT | WIN_2K | WIN_XP | WIN_2K3 | VISTA)
#define VALID_OS_W9X 	(WIN_98 | WIN_ME)
#define VALID_OS_NT     (WIN_NT | WIN_2K | WIN_XP | WIN_2K3 | VISTA)

#define WINX64		0x80000000
#define WINX64_XP   	(WINX64 | WIN_XP)
#define WINX64_2K3   	(WINX64 | WIN_2K3)
#define WINX64_VISTA 	(WINX64 | VISTA)

#define WINX64_ALL  	(WINX64_2K3 | WINX64_VISTA)



