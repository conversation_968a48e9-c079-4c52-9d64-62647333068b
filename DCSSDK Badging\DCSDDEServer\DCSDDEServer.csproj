﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <ProjectType>Local</ProjectType>
    <ProductVersion>8.0.50727</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{B7A813F6-B017-4476-A1A4-2907A3642930}</ProjectGuid>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ApplicationIcon>
    </ApplicationIcon>
    <AssemblyKeyContainerName>
    </AssemblyKeyContainerName>
    <AssemblyName>DCSDDEServer</AssemblyName>
    <AssemblyOriginatorKeyFile>
    </AssemblyOriginatorKeyFile>
    <DefaultClientScript>JScript</DefaultClientScript>
    <DefaultHTMLPageLayout>Grid</DefaultHTMLPageLayout>
    <DefaultTargetSchema>IE50</DefaultTargetSchema>
    <DelaySign>false</DelaySign>
    <OutputType>WinExe</OutputType>
    <RootNamespace>DCSDDEServer</RootNamespace>
    <RunPostBuildEvent>OnOutputUpdated</RunPostBuildEvent>
    <StartupObject>
    </StartupObject>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <ManifestCertificateThumbprint>1D82DDBB09578D4FA794CFCAF6479C22DCE3AAC3</ManifestCertificateThumbprint>
    <ManifestKeyFile>DCSDDEServer_TemporaryKey.pfx</ManifestKeyFile>
    <GenerateManifests>true</GenerateManifests>
    <SignManifests>true</SignManifests>
    <SignAssembly>false</SignAssembly>
    <TargetFrameworkVersion>v2.0</TargetFrameworkVersion>
    <OldToolsVersion>2.0</OldToolsVersion>
    <PublishUrl>C:\test publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <PublisherName>Digital Card Systems</PublisherName>
    <OpenBrowserOnPublish>false</OpenBrowserOnPublish>
    <AutorunEnabled>true</AutorunEnabled>
    <ApplicationRevision>3</ApplicationRevision>
    <ApplicationVersion>1.3.4.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <BaseAddress>285212672</BaseAddress>
    <CheckForOverflowUnderflow>false</CheckForOverflowUnderflow>
    <ConfigurationOverrideFile>
    </ConfigurationOverrideFile>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DocumentationFile>
    </DocumentationFile>
    <DebugSymbols>true</DebugSymbols>
    <FileAlignment>4096</FileAlignment>
    <NoStdLib>false</NoStdLib>
    <NoWarn>
    </NoWarn>
    <Optimize>false</Optimize>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RemoveIntegerChecks>false</RemoveIntegerChecks>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningLevel>4</WarningLevel>
    <DebugType>full</DebugType>
    <ErrorReport>prompt</ErrorReport>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <BaseAddress>285212672</BaseAddress>
    <CheckForOverflowUnderflow>false</CheckForOverflowUnderflow>
    <ConfigurationOverrideFile>
    </ConfigurationOverrideFile>
    <DefineConstants>TRACE</DefineConstants>
    <DocumentationFile>
    </DocumentationFile>
    <DebugSymbols>false</DebugSymbols>
    <FileAlignment>4096</FileAlignment>
    <NoStdLib>false</NoStdLib>
    <NoWarn>
    </NoWarn>
    <Optimize>true</Optimize>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RemoveIntegerChecks>false</RemoveIntegerChecks>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningLevel>4</WarningLevel>
    <DebugType>none</DebugType>
    <ErrorReport>prompt</ErrorReport>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System">
      <Name>System</Name>
    </Reference>
    <Reference Include="System.Data">
      <Name>System.Data</Name>
    </Reference>
    <Reference Include="System.Drawing">
      <Name>System.Drawing</Name>
    </Reference>
    <Reference Include="System.Windows.Forms">
      <Name>System.Windows.Forms</Name>
    </Reference>
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AboutDDEServer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DCSDDEServer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DCSSetup.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ServerOptions.cs">
      <SubType>Form</SubType>
    </Compile>
    <EmbeddedResource Include="AboutDDEServer.resx">
      <DependentUpon>AboutDDEServer.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSDDEServer.resx">
      <DependentUpon>DCSDDEServer.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSSetup.resx">
      <DependentUpon>DCSSetup.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="licenses.licx" />
    <EmbeddedResource Include="ServerOptions.resx">
      <DependentUpon>ServerOptions.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <None Include="DCSDDEServer.config" />
    <None Include="DCSDDEServer_TemporaryKey.pfx" />
    <Content Include="Images\DCSIDServices.ico">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.2.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 2.0</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Capture\DCSSDK_Utilities\DCSSDK_Utilities.csproj">
      <Project>{6EF6073A-143A-497A-9FFA-21DD39EBC9ED}</Project>
      <Name>DCSSDK_Utilities</Name>
    </ProjectReference>
    <ProjectReference Include="..\DCSSDK_BadgingMgt\DCSSDK_BadgingMgt.csproj">
      <Project>{24AF110C-4B71-4C05-AED7-FAF73946BF4C}</Project>
      <Name>DCSSDK_BadgingMgt</Name>
    </ProjectReference>
    <ProjectReference Include="..\DCSSDK_CaptureMgt\DCSSDK_CaptureMgt.csproj">
      <Project>{89EC02EF-B16D-4884-A894-37F4CB62B24E}</Project>
      <Name>DCSSDK_CaptureMgt</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PreBuildEvent>
    </PreBuildEvent>
    <PostBuildEvent>copy  "..\..\..\Referenced Files\AIS_Finger\*.DLL"   $(TargetDir)
copy  "..\..\..\Referenced Files\Chip\*.DLL"   $(TargetDir)
copy  "..\..\..\..\Capture\Referenced Files\HASP\*.DLL"   $(TargetDir)
copy  "..\..\..\..\Capture\Referenced Files\LeadTools\*.DLL"   $(TargetDir)

</PostBuildEvent>
  </PropertyGroup>
</Project>