namespace DCSDEV.DCSDesigner
{
	partial class DCSFormulaAddField
	{
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		/// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
		protected override void Dispose(bool disposing)
		{
			if (disposing && (components != null))
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		#region Windows Form Designer generated code

		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			this.label1 = new System.Windows.Forms.Label();
			this.buttonCancel = new System.Windows.Forms.Button();
			this.buttonOKay = new System.Windows.Forms.Button();
			this.listBoxFieldToAppend = new System.Windows.Forms.ListBox();
			this.SuspendLayout();
			// 
			// label1
			// 
			this.label1.ImeMode = System.Windows.Forms.ImeMode.NoControl;
			this.label1.Location = new System.Drawing.Point(79, 22);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(128, 16);
			this.label1.TabIndex = 8;
			this.label1.Text = "Database field to add:";
			this.label1.TextAlign = System.Drawing.ContentAlignment.TopCenter;
			// 
			// buttonCancel
			// 
			this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.buttonCancel.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonCancel.ImeMode = System.Windows.Forms.ImeMode.NoControl;
			this.buttonCancel.Location = new System.Drawing.Point(160, 216);
			this.buttonCancel.Name = "buttonCancel";
			this.buttonCancel.Size = new System.Drawing.Size(81, 24);
			this.buttonCancel.TabIndex = 2;
			this.buttonCancel.Text = "&Cancel";
			this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
			// 
			// buttonOKay
			// 
			this.buttonOKay.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonOKay.ImeMode = System.Windows.Forms.ImeMode.NoControl;
			this.buttonOKay.Location = new System.Drawing.Point(58, 216);
			this.buttonOKay.Name = "buttonOKay";
			this.buttonOKay.Size = new System.Drawing.Size(87, 24);
			this.buttonOKay.TabIndex = 1;
			this.buttonOKay.Text = "&OK";
			this.buttonOKay.Click += new System.EventHandler(this.buttonOK_Click);
			// 
			// listBoxFieldToAppend
			// 
			this.listBoxFieldToAppend.FormattingEnabled = true;
			this.listBoxFieldToAppend.Location = new System.Drawing.Point(58, 49);
			this.listBoxFieldToAppend.Name = "listBoxFieldToAppend";
			this.listBoxFieldToAppend.Size = new System.Drawing.Size(183, 147);
			this.listBoxFieldToAppend.TabIndex = 9;
			// 
			// DCSFormulaAddField
			// 
			this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
			this.CancelButton = this.buttonCancel;
			this.ClientSize = new System.Drawing.Size(292, 266);
			this.Controls.Add(this.listBoxFieldToAppend);
			this.Controls.Add(this.buttonCancel);
			this.Controls.Add(this.buttonOKay);
			this.Controls.Add(this.label1);
			this.MaximizeBox = false;
			this.MinimizeBox = false;
			this.Name = "DCSFormulaAddField";
			this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
			this.Text = "DCSFormulaAddField";
			this.ResumeLayout(false);

		}

		#endregion

		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.Button buttonCancel;
		private System.Windows.Forms.Button buttonOKay;
		private System.Windows.Forms.ListBox listBoxFieldToAppend;
	}
}