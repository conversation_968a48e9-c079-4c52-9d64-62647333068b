using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using System.IO;

namespace DCSDEV
{
	class AgoraIF
	{
        // return -1, 0 or 1: if error no match or match
        public static int AgoraCompareFingerToBIN(string strFinger_Captured, int iSubClass_Captured, string strBINTest, out int iScore)
        {
            Antheus.Analysis.MinutiaeInfo mMinutiaeInfo_Test;
            bool bRet = AgoraExtractFeatures(strFinger_Captured, iSubClass_Captured, out mMinutiaeInfo_Test);
            if (!bRet)
            {
                iScore = -1;
                return -1;
            }

            Antheus.Analysis.MinutiaeInfo mMinutiaeInfo_DB = new Antheus.Analysis.MinutiaeInfo();
            mMinutiaeInfo_DB.m_minutiae = DCSDEV.DCSMath.String2Buffer(strBINTest, 6);
            Antheus.Matching.SimilarityInfo info = Antheus.Matching.AgrVerify.Verification(mMinutiaeInfo_DB.m_minutiae, mMinutiaeInfo_Test.m_minutiae);

            if (info.m_decision == 0)   // error
            {
                iScore = -1;
                return -1;
            }
            else if (info.m_decision == 1)	// different
            {
                iScore = info.m_score;
                return 0;
            }
            else    // 2=same
            {
                iScore = info.m_score;
                return 1;
            }
        }

		public static bool AgoraExtractFeatures(string strFinger1, int iClass, out string strBIN)
		{
            strBIN = null;
			Antheus.Analysis.MinutiaeInfo mMinutiaeInfo;
            bool bRet = AgoraExtractFeatures(strFinger1, iClass, out mMinutiaeInfo);
            if (bRet)
            {
                byte[] bufTemplate;
                bufTemplate = (byte[])mMinutiaeInfo.m_minutiae;
                strBIN = "AG01X" + "0123456789".Substring(iClass, 1) + DCSMath.Buffer2String(bufTemplate, 0, bufTemplate.Length);

                // DCSMsg.Show(strBIN);
            }
            return bRet;
		}

        public static bool AgoraExtractFeatures(string strFinger1, int iClass, out Antheus.Analysis.MinutiaeInfo mMinutiaeInfo)
		{
			string extension = Path.GetExtension(strFinger1);
			extension = extension.ToUpper();

			FileInfo fi = new FileInfo(strFinger1);
			FileStream fs = fi.OpenRead();

			int nBytes = (int)fi.Length;
			byte[] streaming = new byte[nBytes];
			int nBytesRead = fs.Read(streaming, 0, nBytes);
			fs.Close();

			mMinutiaeInfo = null;
			if (extension == ".BMP" || extension == ".JPG" || extension == ".GIF")
			{
				Bitmap mBitmap;
				MemoryStream ms = new MemoryStream(streaming);
				mBitmap = new Bitmap(ms);

				mMinutiaeInfo = Antheus.Analysis.AgrFeature.Encode(0, 0, mBitmap);

				if (mMinutiaeInfo.code == Antheus.Analysis.ErrorCode.Success)
				{
					return true;
				}
			}
			else if (extension == ".WSQ")
			{
				DCSMsg.Show("WSQ is not handled with Agora");
			}
			return false;
		}
	}
}
