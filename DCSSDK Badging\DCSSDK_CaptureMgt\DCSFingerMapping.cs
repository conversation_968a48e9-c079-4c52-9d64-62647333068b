using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;

namespace DCSDEV.DCSFingerMapping
{
	/// <summary>
	/// Summary description for DCSFingerMapping.
	/// </summary>
	internal class DCSFingerMapping : System.Windows.Forms.Form
	{
		private enum Modes {DISPLAY, RENAME, R<PERSON><PERSON><PERSON>R};
		Modes m_mode = Modes.DISPLAY;
		private ArrayList m_controls;

		private int m_current = 0;
		private ArrayList m_arrayFingerMapNames = null;

		private System.Windows.Forms.Button buttonClose;
		private System.Windows.Forms.PictureBox pictureBox1;
		private System.Windows.Forms.PictureBox pictureBox2;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.Label label4;
		private System.Windows.Forms.Label label5;
		private System.Windows.Forms.Label label6;
		private System.Windows.Forms.Label label7;
		private System.Windows.Forms.Label label8;
		private System.Windows.Forms.Label label9;
		private System.Windows.Forms.Label label0;
		private System.Windows.Forms.TextBox tbFingerMap;
		private System.Windows.Forms.Label label11;
		private System.Windows.Forms.Label label12;
		private System.Windows.Forms.NumericUpDown numericUpDownFingerIndex;
		private System.Windows.Forms.Button buttonRename;
		private System.Windows.Forms.GroupBox groupBoxRename;
		private System.Windows.Forms.TextBox textBoxFingerName;
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		public DCSFingerMapping()
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			m_controls = new ArrayList();
			m_controls.Add(this.label0);
			m_controls.Add(this.label1);
			m_controls.Add(this.label2);
			m_controls.Add(this.label3);
			m_controls.Add(this.label4);
			m_controls.Add(this.label5);
			m_controls.Add(this.label6);
			m_controls.Add(this.label7);
			m_controls.Add(this.label8);
			m_controls.Add(this.label9);

		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(DCSFingerMapping));
			this.buttonClose = new System.Windows.Forms.Button();
			this.pictureBox1 = new System.Windows.Forms.PictureBox();
			this.pictureBox2 = new System.Windows.Forms.PictureBox();
			this.label1 = new System.Windows.Forms.Label();
			this.label2 = new System.Windows.Forms.Label();
			this.label3 = new System.Windows.Forms.Label();
			this.label4 = new System.Windows.Forms.Label();
			this.label5 = new System.Windows.Forms.Label();
			this.label6 = new System.Windows.Forms.Label();
			this.label7 = new System.Windows.Forms.Label();
			this.label8 = new System.Windows.Forms.Label();
			this.label9 = new System.Windows.Forms.Label();
			this.label0 = new System.Windows.Forms.Label();
			this.tbFingerMap = new System.Windows.Forms.TextBox();
			this.label11 = new System.Windows.Forms.Label();
			this.label12 = new System.Windows.Forms.Label();
			this.numericUpDownFingerIndex = new System.Windows.Forms.NumericUpDown();
			this.buttonRename = new System.Windows.Forms.Button();
			this.groupBoxRename = new System.Windows.Forms.GroupBox();
			this.textBoxFingerName = new System.Windows.Forms.TextBox();
			((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
			((System.ComponentModel.ISupportInitialize)(this.pictureBox2)).BeginInit();
			((System.ComponentModel.ISupportInitialize)(this.numericUpDownFingerIndex)).BeginInit();
			this.groupBoxRename.SuspendLayout();
			this.SuspendLayout();
			// 
			// buttonCancel
			// 
			resources.ApplyResources(this.buttonClose, "buttonClose");
			this.buttonClose.Name = "buttonClose";
			this.buttonClose.Click += new System.EventHandler(this.buttonClose_Click);
			// 
			// pictureBox1
			// 
			resources.ApplyResources(this.pictureBox1, "pictureBox1");
			this.pictureBox1.Name = "pictureBox1";
			this.pictureBox1.TabStop = false;
			// 
			// pictureBox2
			// 
			resources.ApplyResources(this.pictureBox2, "pictureBox2");
			this.pictureBox2.Name = "pictureBox2";
			this.pictureBox2.TabStop = false;
			// 
			// label1
			// 
			resources.ApplyResources(this.label1, "label1");
			this.label1.Name = "label1";
			this.label1.Click += new System.EventHandler(this.label1_Click);
			// 
			// label2
			// 
			resources.ApplyResources(this.label2, "label2");
			this.label2.Name = "label2";
			this.label2.Click += new System.EventHandler(this.label2_Click);
			// 
			// label3
			// 
			resources.ApplyResources(this.label3, "label3");
			this.label3.Name = "label3";
			this.label3.Click += new System.EventHandler(this.label3_Click);
			// 
			// label4
			// 
			resources.ApplyResources(this.label4, "label4");
			this.label4.Name = "label4";
			this.label4.Click += new System.EventHandler(this.label4_Click);
			// 
			// label5
			// 
			resources.ApplyResources(this.label5, "label5");
			this.label5.Name = "label5";
			this.label5.Click += new System.EventHandler(this.label5_Click);
			// 
			// label6
			// 
			resources.ApplyResources(this.label6, "label6");
			this.label6.Name = "label6";
			this.label6.Click += new System.EventHandler(this.label6_Click);
			// 
			// label7
			// 
			resources.ApplyResources(this.label7, "label7");
			this.label7.Name = "label7";
			this.label7.Click += new System.EventHandler(this.label7_Click);
			// 
			// label8
			// 
			resources.ApplyResources(this.label8, "label8");
			this.label8.Name = "label8";
			this.label8.Click += new System.EventHandler(this.label8_Click);
			// 
			// label9
			// 
			resources.ApplyResources(this.label9, "label9");
			this.label9.Name = "label9";
			this.label9.Click += new System.EventHandler(this.label9_Click);
			// 
			// label0
			// 
			resources.ApplyResources(this.label0, "label0");
			this.label0.Name = "label0";
			this.label0.Click += new System.EventHandler(this.label0_Click);
			// 
			// tbFingerMap
			// 
			resources.ApplyResources(this.tbFingerMap, "tbFingerMap");
			this.tbFingerMap.Name = "tbFingerMap";
			// 
			// label11
			// 
			resources.ApplyResources(this.label11, "label11");
			this.label11.Name = "label11";
			// 
			// label12
			// 
			resources.ApplyResources(this.label12, "label12");
			this.label12.Name = "label12";
			// 
			// numericUpDownFingerIndex
			// 
			resources.ApplyResources(this.numericUpDownFingerIndex, "numericUpDownFingerIndex");
			this.numericUpDownFingerIndex.Maximum = new decimal(new int[] {
            9,
            0,
            0,
            0});
			this.numericUpDownFingerIndex.Name = "numericUpDownFingerIndex";
			this.numericUpDownFingerIndex.ValueChanged += new System.EventHandler(this.numericUpDownFingerIndex_ValueChanged);
			// 
			// buttonRename
			// 
			this.buttonRename.Cursor = System.Windows.Forms.Cursors.Default;
			resources.ApplyResources(this.buttonRename, "buttonRename");
			this.buttonRename.Name = "buttonRename";
			this.buttonRename.Click += new System.EventHandler(this.buttonRename_Click);
			// 
			// groupBoxRename
			// 
			this.groupBoxRename.Controls.Add(this.textBoxFingerName);
			resources.ApplyResources(this.groupBoxRename, "groupBoxRename");
			this.groupBoxRename.Name = "groupBoxRename";
			this.groupBoxRename.TabStop = false;
			// 
			// textBoxFingerName
			// 
			resources.ApplyResources(this.textBoxFingerName, "textBoxFingerName");
			this.textBoxFingerName.Name = "textBoxFingerName";
			// 
			// DCSFingerMapping
			// 
			resources.ApplyResources(this, "$this");
			this.Controls.Add(this.groupBoxRename);
			this.Controls.Add(this.buttonRename);
			this.Controls.Add(this.numericUpDownFingerIndex);
			this.Controls.Add(this.label12);
			this.Controls.Add(this.label11);
			this.Controls.Add(this.tbFingerMap);
			this.Controls.Add(this.label0);
			this.Controls.Add(this.label9);
			this.Controls.Add(this.label8);
			this.Controls.Add(this.label7);
			this.Controls.Add(this.label6);
			this.Controls.Add(this.label5);
			this.Controls.Add(this.label4);
			this.Controls.Add(this.label3);
			this.Controls.Add(this.label2);
			this.Controls.Add(this.label1);
			this.Controls.Add(this.pictureBox2);
			this.Controls.Add(this.pictureBox1);
			this.Controls.Add(this.buttonClose);
			this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
			this.Name = "DCSFingerMapping";
			this.ShowInTaskbar = false;
			((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
			((System.ComponentModel.ISupportInitialize)(this.pictureBox2)).EndInit();
			((System.ComponentModel.ISupportInitialize)(this.numericUpDownFingerIndex)).EndInit();
			this.groupBoxRename.ResumeLayout(false);
			this.groupBoxRename.PerformLayout();
			this.ResumeLayout(false);
			this.PerformLayout();

		}
		#endregion

		private void AdjustVisibilities()
		{
			int i=0;
			foreach (Control control in m_controls)
			{
				control.Text = i.ToString() + "-" + (string)this.m_arrayFingerMapNames[i];

				if (control == m_controls[m_current]) control.ForeColor = Color.Red;
				else control.ForeColor = Color.Black;

				if (m_mode == Modes.REORDER)
				{
					control.Enabled = false;
				}
				else if (m_mode == Modes.RENAME)
				{
					control.Enabled = false;
				}
				else
				{
					control.Enabled = true;
				}
				i++;
			}
			
			if (m_mode == Modes.REORDER)
			{
				this.buttonClose.Text = "Done";
				this.groupBoxRename.Visible = false;
				//this.textBoxFingerName.Visible = false;
				this.buttonRename.Visible = false;
			}
			else if (m_mode == Modes.RENAME)
			{
				this.buttonClose.Text = "Done";
				this.groupBoxRename.Visible = true;
				//this.textBoxFingerName.Visible = true;
				this.buttonRename.Visible = false;
			}
			else
			{
				this.buttonClose.Text = "Close";
				this.groupBoxRename.Visible = false;
				//this.textBoxFingerName.Visible = false;
				this.buttonRename.Visible = true;
			}
		}

		private void buttonClose_Click(object sender, System.EventArgs e)
		{
			if (m_mode == Modes.REORDER)
			{
				m_mode = Modes.DISPLAY;
				this.AdjustVisibilities();
			}
			else if (m_mode == Modes.RENAME)
			{
				m_mode = Modes.DISPLAY;
				this.m_arrayFingerMapNames[m_current] = this.textBoxFingerName.Text;
				//this.m_arrayFingerMapNames.RemoveAt(m_current);
				//this.m_arrayFingerMapNames.Insert(m_current, this.textBoxFingerName.Text);
				this.AdjustVisibilities();
			}
			else
			{
				this.Close();
			}
		}

		private void numericUpDownFingerIndex_ValueChanged(object sender, System.EventArgs e)
		{
			m_current = (int)this.numericUpDownFingerIndex.Value;
			this.textBoxFingerName.Text = (string)this.m_arrayFingerMapNames[m_current];
			AdjustVisibilities();
		}

		private void label0_Click(object sender, System.EventArgs e)
		{
			this.numericUpDownFingerIndex.Value = 0;		
		}

		private void label1_Click(object sender, System.EventArgs e)
		{
			this.numericUpDownFingerIndex.Value = 1;		
		}

		private void label2_Click(object sender, System.EventArgs e)
		{
			this.numericUpDownFingerIndex.Value = 2;		
		}

		private void label3_Click(object sender, System.EventArgs e)
		{
			this.numericUpDownFingerIndex.Value = 3;		
		}

		private void label4_Click(object sender, System.EventArgs e)
		{
			this.numericUpDownFingerIndex.Value = 4;		
		}

		private void label5_Click(object sender, System.EventArgs e)
		{
			this.numericUpDownFingerIndex.Value = 5;		
		}

		private void label6_Click(object sender, System.EventArgs e)
		{
			this.numericUpDownFingerIndex.Value = 6;		
		}

		private void label7_Click(object sender, System.EventArgs e)
		{
			this.numericUpDownFingerIndex.Value = 7;		
		}

		private void label8_Click(object sender, System.EventArgs e)
		{
			this.numericUpDownFingerIndex.Value = 8;		
		}

		private void label9_Click(object sender, System.EventArgs e)
		{
			this.numericUpDownFingerIndex.Value = 9;		
		}

		private void buttonRename_Click(object sender, System.EventArgs e)
		{
			m_mode = Modes.RENAME;
			this.AdjustVisibilities();
		}

		public string FingerMap
		{
			get { return this.tbFingerMap.Text; }
			set { this.tbFingerMap.Text = value; }
		}
		public ArrayList FingerMapNames
		{
			get { return this.m_arrayFingerMapNames; }
			set 
			{ 
				this.m_arrayFingerMapNames = value; 
				this.textBoxFingerName.Text = (string)this.m_arrayFingerMapNames[m_current];
				AdjustVisibilities();
			}
		}
	}
}
