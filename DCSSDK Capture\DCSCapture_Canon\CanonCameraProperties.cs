
using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;
using System.Runtime.InteropServices;

namespace DCSDEV.CanonCamera
{
	/// <summary>
	/// Summary description for CanonCameraProperties.
	/// </summary>
	public class CanonCameraProperties : System.Windows.Forms.Form
	{
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_Init();

		// finish the sdk
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_Term();

		// connect to first canon camera found
		[DllImport("CanonSDKIF.Dll")]
		internal static extern int CanonSDKCall_Connect();

		// disconnect canon camera
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_Disconnect();
		// turn off live video and cature image
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_Capture(string szSavePath);

		// turn On/Off view finder
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_ViewFinderOn(IntPtr hWnd);	// pass view finder window handle
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_ViewFinderOff();

		// zoom functions
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_ZoomGet(out int MaxZoomPos, out int MaxOpticalZoomPos, out int CurrentZoomPos, out int ZoomStepSize);
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_ZoomSet(int ZoomPos);

		// data retrieval functions
		[DllImport("CanonSDKIF.Dll")]
		internal static extern string CanonSDKCall_GetModelName();
		[DllImport("CanonSDKIF.Dll")]
		internal static extern int CanonSDKCall_GetIfConnected();
		[DllImport("CanonSDKIF.Dll")]
		internal static extern int CanonSDKCall_GetIfStarted();
		[DllImport("CanonSDKIF.Dll")]
		internal static extern int CanonSDKCall_GetIfSupportsReleaseControl();
		[DllImport("CanonSDKIF.Dll")]
		internal static extern int CanonSDKCall_GetIfSupportsViewFinder();
		[DllImport("CanonSDKIF.Dll")]
		internal static extern int CanonSDKCall_GetIfSupportsZoom();
		[DllImport("CanonSDKIF.Dll")]
		internal static extern int CanonSDKCall_GetIfSupportsPhotoEffect();

		//=============================================================================//
		private PopulateCombos m_pc = null;
		private DCSDEV.ParameterStore m_ps = null;

		private bool m_bConnectOK = false;
		private bool m_bSDKOK = false;
			
		private int m_ZoomPos;
		private int m_ExpoComp;
		private int m_FlashMode;
		private int m_ImageFormat;
		private int m_ISOSpeed;
		private int m_PhotoEffect;
		private int m_ShootingMode;
		private int m_WhiteBalance;

		private bool m_bEnableGetFile;
		private bool m_bShowPreview;
		private bool m_bAllowPropEdit;
		private bool m_bSaveCaptureParams;
		private bool m_bSaveZoom;

		private System.Windows.Forms.Label labelWhiteBalance;
		private System.Windows.Forms.ComboBox comboBoxWhiteBalance;
		private System.Windows.Forms.Label labelExpoComp;
		private System.Windows.Forms.ComboBox comboBoxExpoComp;
		private System.Windows.Forms.Label labelFlashMode;
		private System.Windows.Forms.ComboBox comboBoxFlashMode;
		private System.Windows.Forms.TextBox tbCameraModel;
		private System.Windows.Forms.TextBox tbCameraStatus;
		private System.Windows.Forms.Button buttonReset;
		private System.Windows.Forms.Button buttonAbout;
		private System.Windows.Forms.PictureBox pictureBoxPreview;
		private System.Windows.Forms.TrackBar trackBarZoom;
		private System.Windows.Forms.Label labelZoom;
		private System.Windows.Forms.Label labelShootingMode;
		private System.Windows.Forms.ComboBox comboBoxShootingMode;
		private System.Windows.Forms.Label labelISOSpeed;
		private System.Windows.Forms.ComboBox comboBoxISOSpeed;
		private System.Windows.Forms.Label labelImageFormat;
		private System.Windows.Forms.ComboBox comboBoxImageFormat;
		private System.Windows.Forms.Label labelPhotoEffect;
		private System.Windows.Forms.Button buttonOK;
		private System.Windows.Forms.Button buttonCancel;
		private System.Windows.Forms.CheckBox checkBoxShowPreview;
		private System.Windows.Forms.CheckBox checkBoxAllowPropEdit;
		private System.Windows.Forms.GroupBox groupBox2;
		private System.Windows.Forms.CheckBox checkBoxSaveZoom;
		private System.Windows.Forms.CheckBox checkBoxSaveCaptureParams;
		private System.Windows.Forms.CheckBox checkBoxEnableGetFile;
		private System.Windows.Forms.ComboBox comboBoxPhotoEffect;
		/// <summary>
		/// Required designer variable.
		/// </summary>
//syh		private System.ComponentModel.Container components = null;
		

		public CanonCameraProperties()
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			m_ps = new DCSDEV.ParameterStore("Canon Camera");
			m_pc = new PopulateCombos();

			// disconnect if currently connected
			this.DisconnectCanon();

			// re establish connection
			this.ConnectCanon();
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				//MessageBox.Show("Disconnect");
				// disconnect if currently connected
				DisconnectCanon();

				//if (components != null) 
				//{
				//	components.Dispose();
				//}
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            this.labelWhiteBalance = new System.Windows.Forms.Label();
            this.comboBoxWhiteBalance = new System.Windows.Forms.ComboBox();
            this.labelExpoComp = new System.Windows.Forms.Label();
            this.comboBoxExpoComp = new System.Windows.Forms.ComboBox();
            this.labelFlashMode = new System.Windows.Forms.Label();
            this.comboBoxFlashMode = new System.Windows.Forms.ComboBox();
            this.tbCameraModel = new System.Windows.Forms.TextBox();
            this.tbCameraStatus = new System.Windows.Forms.TextBox();
            this.buttonReset = new System.Windows.Forms.Button();
            this.buttonAbout = new System.Windows.Forms.Button();
            this.pictureBoxPreview = new System.Windows.Forms.PictureBox();
            this.trackBarZoom = new System.Windows.Forms.TrackBar();
            this.labelZoom = new System.Windows.Forms.Label();
            this.labelShootingMode = new System.Windows.Forms.Label();
            this.comboBoxShootingMode = new System.Windows.Forms.ComboBox();
            this.labelISOSpeed = new System.Windows.Forms.Label();
            this.comboBoxISOSpeed = new System.Windows.Forms.ComboBox();
            this.labelImageFormat = new System.Windows.Forms.Label();
            this.comboBoxImageFormat = new System.Windows.Forms.ComboBox();
            this.labelPhotoEffect = new System.Windows.Forms.Label();
            this.comboBoxPhotoEffect = new System.Windows.Forms.ComboBox();
            this.buttonOK = new System.Windows.Forms.Button();
            this.buttonCancel = new System.Windows.Forms.Button();
            this.checkBoxShowPreview = new System.Windows.Forms.CheckBox();
            this.checkBoxAllowPropEdit = new System.Windows.Forms.CheckBox();
            this.checkBoxSaveCaptureParams = new System.Windows.Forms.CheckBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.checkBoxEnableGetFile = new System.Windows.Forms.CheckBox();
            this.checkBoxSaveZoom = new System.Windows.Forms.CheckBox();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBoxPreview)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.trackBarZoom)).BeginInit();
            this.groupBox2.SuspendLayout();
            this.SuspendLayout();
            // 
            // labelWhiteBalance
            // 
            this.labelWhiteBalance.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.labelWhiteBalance.Location = new System.Drawing.Point(360, 208);
            this.labelWhiteBalance.Name = "labelWhiteBalance";
            this.labelWhiteBalance.Size = new System.Drawing.Size(104, 16);
            this.labelWhiteBalance.TabIndex = 43;
            this.labelWhiteBalance.Text = "White balance";
            // 
            // comboBoxWhiteBalance
            // 
            this.comboBoxWhiteBalance.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxWhiteBalance.ItemHeight = 13;
            this.comboBoxWhiteBalance.Location = new System.Drawing.Point(472, 208);
            this.comboBoxWhiteBalance.Name = "comboBoxWhiteBalance";
            this.comboBoxWhiteBalance.Size = new System.Drawing.Size(144, 21);
            this.comboBoxWhiteBalance.TabIndex = 6;
            this.comboBoxWhiteBalance.SelectedIndexChanged += new System.EventHandler(this.comboBoxWhiteBalance_SelectedIndexChanged);
            // 
            // labelExpoComp
            // 
            this.labelExpoComp.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.labelExpoComp.Location = new System.Drawing.Point(360, 176);
            this.labelExpoComp.Name = "labelExpoComp";
            this.labelExpoComp.Size = new System.Drawing.Size(104, 16);
            this.labelExpoComp.TabIndex = 41;
            this.labelExpoComp.Text = "Exposure comp.";
            // 
            // comboBoxExpoComp
            // 
            this.comboBoxExpoComp.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxExpoComp.ItemHeight = 13;
            this.comboBoxExpoComp.Location = new System.Drawing.Point(472, 176);
            this.comboBoxExpoComp.Name = "comboBoxExpoComp";
            this.comboBoxExpoComp.Size = new System.Drawing.Size(144, 21);
            this.comboBoxExpoComp.TabIndex = 5;
            this.comboBoxExpoComp.SelectedIndexChanged += new System.EventHandler(this.comboBoxExpoComp_SelectedIndexChanged);
            // 
            // labelFlashMode
            // 
            this.labelFlashMode.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.labelFlashMode.Location = new System.Drawing.Point(360, 144);
            this.labelFlashMode.Name = "labelFlashMode";
            this.labelFlashMode.Size = new System.Drawing.Size(104, 16);
            this.labelFlashMode.TabIndex = 39;
            this.labelFlashMode.Text = "Flash";
            // 
            // comboBoxFlashMode
            // 
            this.comboBoxFlashMode.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxFlashMode.ItemHeight = 13;
            this.comboBoxFlashMode.Location = new System.Drawing.Point(472, 144);
            this.comboBoxFlashMode.Name = "comboBoxFlashMode";
            this.comboBoxFlashMode.Size = new System.Drawing.Size(144, 21);
            this.comboBoxFlashMode.TabIndex = 4;
            this.comboBoxFlashMode.SelectedIndexChanged += new System.EventHandler(this.comboBoxFlashMode_SelectedIndexChanged);
            // 
            // tbCameraModel
            // 
            this.tbCameraModel.BackColor = System.Drawing.SystemColors.Control;
            this.tbCameraModel.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.tbCameraModel.Location = new System.Drawing.Point(376, 24);
            this.tbCameraModel.Name = "tbCameraModel";
            this.tbCameraModel.Size = new System.Drawing.Size(200, 13);
            this.tbCameraModel.TabIndex = 37;
            this.tbCameraModel.TabStop = false;
            this.tbCameraModel.Text = "Unknown model";
            // 
            // tbCameraStatus
            // 
            this.tbCameraStatus.BackColor = System.Drawing.SystemColors.Control;
            this.tbCameraStatus.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.tbCameraStatus.Location = new System.Drawing.Point(376, 48);
            this.tbCameraStatus.Name = "tbCameraStatus";
            this.tbCameraStatus.Size = new System.Drawing.Size(200, 13);
            this.tbCameraStatus.TabIndex = 36;
            this.tbCameraStatus.TabStop = false;
            this.tbCameraStatus.Text = "Uninitialized";
            // 
            // buttonReset
            // 
            this.buttonReset.AccessibleDescription = "";
            this.buttonReset.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.buttonReset.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.buttonReset.Location = new System.Drawing.Point(528, 368);
            this.buttonReset.Name = "buttonReset";
            this.buttonReset.Size = new System.Drawing.Size(88, 24);
            this.buttonReset.TabIndex = 2;
            this.buttonReset.Text = "Reset";
            this.buttonReset.Click += new System.EventHandler(this.buttonReset_Click);
            // 
            // buttonAbout
            // 
            this.buttonAbout.AccessibleDescription = "shut down camera and exit";
            this.buttonAbout.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.buttonAbout.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.buttonAbout.Location = new System.Drawing.Point(328, 400);
            this.buttonAbout.Name = "buttonAbout";
            this.buttonAbout.Size = new System.Drawing.Size(88, 24);
            this.buttonAbout.TabIndex = 14;
            this.buttonAbout.Text = "About";
            this.buttonAbout.Click += new System.EventHandler(this.buttonAbout_Click);
            // 
            // pictureBoxPreview
            // 
            this.pictureBoxPreview.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.pictureBoxPreview.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.pictureBoxPreview.Location = new System.Drawing.Point(24, 32);
            this.pictureBoxPreview.Name = "pictureBoxPreview";
            this.pictureBoxPreview.Size = new System.Drawing.Size(320, 240);
            this.pictureBoxPreview.TabIndex = 46;
            this.pictureBoxPreview.TabStop = false;
            // 
            // trackBarZoom
            // 
            this.trackBarZoom.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.trackBarZoom.LargeChange = 1;
            this.trackBarZoom.Location = new System.Drawing.Point(368, 80);
            this.trackBarZoom.Name = "trackBarZoom";
            this.trackBarZoom.Size = new System.Drawing.Size(256, 45);
            this.trackBarZoom.TabIndex = 3;
            this.trackBarZoom.TickStyle = System.Windows.Forms.TickStyle.TopLeft;
            this.trackBarZoom.Scroll += new System.EventHandler(this.trackBarZoom_Scroll);
            // 
            // labelZoom
            // 
            this.labelZoom.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.labelZoom.Location = new System.Drawing.Point(464, 112);
            this.labelZoom.Name = "labelZoom";
            this.labelZoom.Size = new System.Drawing.Size(40, 16);
            this.labelZoom.TabIndex = 34;
            this.labelZoom.Text = "Zoom";
            // 
            // labelShootingMode
            // 
            this.labelShootingMode.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.labelShootingMode.Location = new System.Drawing.Point(360, 240);
            this.labelShootingMode.Name = "labelShootingMode";
            this.labelShootingMode.Size = new System.Drawing.Size(104, 16);
            this.labelShootingMode.TabIndex = 48;
            this.labelShootingMode.Text = "Shooting Mode";
            // 
            // comboBoxShootingMode
            // 
            this.comboBoxShootingMode.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxShootingMode.ItemHeight = 13;
            this.comboBoxShootingMode.Location = new System.Drawing.Point(472, 240);
            this.comboBoxShootingMode.Name = "comboBoxShootingMode";
            this.comboBoxShootingMode.Size = new System.Drawing.Size(144, 21);
            this.comboBoxShootingMode.TabIndex = 7;
            this.comboBoxShootingMode.SelectedIndexChanged += new System.EventHandler(this.comboBoxShootingMode_SelectedIndexChanged);
            // 
            // labelISOSpeed
            // 
            this.labelISOSpeed.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.labelISOSpeed.Location = new System.Drawing.Point(360, 272);
            this.labelISOSpeed.Name = "labelISOSpeed";
            this.labelISOSpeed.Size = new System.Drawing.Size(104, 16);
            this.labelISOSpeed.TabIndex = 50;
            this.labelISOSpeed.Text = "ISO Speed";
            // 
            // comboBoxISOSpeed
            // 
            this.comboBoxISOSpeed.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxISOSpeed.ItemHeight = 13;
            this.comboBoxISOSpeed.Location = new System.Drawing.Point(472, 272);
            this.comboBoxISOSpeed.Name = "comboBoxISOSpeed";
            this.comboBoxISOSpeed.Size = new System.Drawing.Size(144, 21);
            this.comboBoxISOSpeed.TabIndex = 8;
            this.comboBoxISOSpeed.SelectedIndexChanged += new System.EventHandler(this.comboBoxISOSpeed_SelectedIndexChanged);
            // 
            // labelImageFormat
            // 
            this.labelImageFormat.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.labelImageFormat.Location = new System.Drawing.Point(360, 304);
            this.labelImageFormat.Name = "labelImageFormat";
            this.labelImageFormat.Size = new System.Drawing.Size(104, 16);
            this.labelImageFormat.TabIndex = 52;
            this.labelImageFormat.Text = "Compression/Size";
            // 
            // comboBoxImageFormat
            // 
            this.comboBoxImageFormat.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxImageFormat.DropDownWidth = 200;
            this.comboBoxImageFormat.ItemHeight = 13;
            this.comboBoxImageFormat.Location = new System.Drawing.Point(472, 304);
            this.comboBoxImageFormat.MaxDropDownItems = 10;
            this.comboBoxImageFormat.Name = "comboBoxImageFormat";
            this.comboBoxImageFormat.Size = new System.Drawing.Size(144, 21);
            this.comboBoxImageFormat.TabIndex = 9;
            this.comboBoxImageFormat.SelectedIndexChanged += new System.EventHandler(this.comboBoxImageFormat_SelectedIndexChanged);
            // 
            // labelPhotoEffect
            // 
            this.labelPhotoEffect.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.labelPhotoEffect.Location = new System.Drawing.Point(360, 336);
            this.labelPhotoEffect.Name = "labelPhotoEffect";
            this.labelPhotoEffect.Size = new System.Drawing.Size(104, 16);
            this.labelPhotoEffect.TabIndex = 54;
            this.labelPhotoEffect.Text = "Photo effect";
            // 
            // comboBoxPhotoEffect
            // 
            this.comboBoxPhotoEffect.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.comboBoxPhotoEffect.ItemHeight = 13;
            this.comboBoxPhotoEffect.Location = new System.Drawing.Point(472, 336);
            this.comboBoxPhotoEffect.Name = "comboBoxPhotoEffect";
            this.comboBoxPhotoEffect.Size = new System.Drawing.Size(144, 21);
            this.comboBoxPhotoEffect.TabIndex = 10;
            this.comboBoxPhotoEffect.SelectedIndexChanged += new System.EventHandler(this.comboBoxPhotoEffect_SelectedIndexChanged);
            // 
            // buttonOK
            // 
            this.buttonOK.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.buttonOK.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.buttonOK.Location = new System.Drawing.Point(432, 400);
            this.buttonOK.Name = "buttonOK";
            this.buttonOK.Size = new System.Drawing.Size(88, 24);
            this.buttonOK.TabIndex = 0;
            this.buttonOK.Text = "OK";
            this.buttonOK.Click += new System.EventHandler(this.buttonOK_Click);
            // 
            // buttonCancel
            // 
            this.buttonCancel.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.buttonCancel.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.buttonCancel.Location = new System.Drawing.Point(528, 400);
            this.buttonCancel.Name = "buttonCancel";
            this.buttonCancel.Size = new System.Drawing.Size(88, 24);
            this.buttonCancel.TabIndex = 1;
            this.buttonCancel.Text = "Cancel";
            this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
            // 
            // checkBoxShowPreview
            // 
            this.checkBoxShowPreview.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.checkBoxShowPreview.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.checkBoxShowPreview.Location = new System.Drawing.Point(32, 312);
            this.checkBoxShowPreview.Name = "checkBoxShowPreview";
            this.checkBoxShowPreview.Size = new System.Drawing.Size(168, 16);
            this.checkBoxShowPreview.TabIndex = 11;
            this.checkBoxShowPreview.Text = "Show preview window";
            // 
            // checkBoxAllowPropEdit
            // 
            this.checkBoxAllowPropEdit.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.checkBoxAllowPropEdit.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.checkBoxAllowPropEdit.Location = new System.Drawing.Point(32, 336);
            this.checkBoxAllowPropEdit.Name = "checkBoxAllowPropEdit";
            this.checkBoxAllowPropEdit.Size = new System.Drawing.Size(168, 16);
            this.checkBoxAllowPropEdit.TabIndex = 12;
            this.checkBoxAllowPropEdit.Text = "Allow access to this dialog";
            // 
            // checkBoxSaveCaptureParams
            // 
            this.checkBoxSaveCaptureParams.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.checkBoxSaveCaptureParams.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.checkBoxSaveCaptureParams.Location = new System.Drawing.Point(32, 360);
            this.checkBoxSaveCaptureParams.Name = "checkBoxSaveCaptureParams";
            this.checkBoxSaveCaptureParams.Size = new System.Drawing.Size(168, 16);
            this.checkBoxSaveCaptureParams.TabIndex = 13;
            this.checkBoxSaveCaptureParams.Text = "Save parameter changes";
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.checkBoxEnableGetFile);
            this.groupBox2.Controls.Add(this.checkBoxSaveZoom);
            this.groupBox2.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.groupBox2.Location = new System.Drawing.Point(24, 288);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(208, 144);
            this.groupBox2.TabIndex = 65;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "Portrait Capture Dialog";
            // 
            // checkBoxEnableGetFile
            // 
            this.checkBoxEnableGetFile.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.checkBoxEnableGetFile.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.checkBoxEnableGetFile.Location = new System.Drawing.Point(8, 120);
            this.checkBoxEnableGetFile.Name = "checkBoxEnableGetFile";
            this.checkBoxEnableGetFile.Size = new System.Drawing.Size(168, 16);
            this.checkBoxEnableGetFile.TabIndex = 1;
            this.checkBoxEnableGetFile.Text = "Enable Get File";
            // 
            // checkBoxSaveZoom
            // 
            this.checkBoxSaveZoom.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.checkBoxSaveZoom.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.checkBoxSaveZoom.Location = new System.Drawing.Point(8, 96);
            this.checkBoxSaveZoom.Name = "checkBoxSaveZoom";
            this.checkBoxSaveZoom.Size = new System.Drawing.Size(168, 16);
            this.checkBoxSaveZoom.TabIndex = 0;
            this.checkBoxSaveZoom.Text = "Save zoom changes";
            // 
            // CanonCameraProperties
            // 
            this.AutoScaleBaseSize = new System.Drawing.Size(5, 13);
            this.ClientSize = new System.Drawing.Size(632, 446);
            this.Controls.Add(this.checkBoxSaveCaptureParams);
            this.Controls.Add(this.checkBoxAllowPropEdit);
            this.Controls.Add(this.checkBoxShowPreview);
            this.Controls.Add(this.buttonCancel);
            this.Controls.Add(this.buttonOK);
            this.Controls.Add(this.labelPhotoEffect);
            this.Controls.Add(this.comboBoxPhotoEffect);
            this.Controls.Add(this.labelImageFormat);
            this.Controls.Add(this.comboBoxImageFormat);
            this.Controls.Add(this.labelISOSpeed);
            this.Controls.Add(this.comboBoxISOSpeed);
            this.Controls.Add(this.labelShootingMode);
            this.Controls.Add(this.comboBoxShootingMode);
            this.Controls.Add(this.labelZoom);
            this.Controls.Add(this.pictureBoxPreview);
            this.Controls.Add(this.buttonReset);
            this.Controls.Add(this.buttonAbout);
            this.Controls.Add(this.labelWhiteBalance);
            this.Controls.Add(this.comboBoxWhiteBalance);
            this.Controls.Add(this.labelExpoComp);
            this.Controls.Add(this.comboBoxExpoComp);
            this.Controls.Add(this.labelFlashMode);
            this.Controls.Add(this.comboBoxFlashMode);
            this.Controls.Add(this.tbCameraModel);
            this.Controls.Add(this.tbCameraStatus);
            this.Controls.Add(this.trackBarZoom);
            this.Controls.Add(this.groupBox2);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.Fixed3D;
            this.Name = "CanonCameraProperties";
            this.ShowInTaskbar = false;
            this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Canon Camera Properties Dialog";
            ((System.ComponentModel.ISupportInitialize)(this.pictureBoxPreview)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.trackBarZoom)).EndInit();
            this.groupBox2.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

		}
		#endregion

		// disconnect if currently connected
		private void DisconnectCanon()
		{
			if (m_bConnectOK) 
			{
				if (CanonSDKCall_GetIfSupportsViewFinder()!=0) CanonSDKCall_ViewFinderOff();
				CanonSDKCall_Disconnect();
				m_bConnectOK = false;
			}
			if (m_bSDKOK) 
			{
				CanonSDKCall_Term();
				m_bSDKOK = false;
			}
		}

		private void ConnectCanon()
		{
			try
			{
				DisplayStatus(true, "Initializing CanonCamera");

				if (!m_bSDKOK)
				{
					//MessageBox.Show("InitializeCanonSDK");
					m_bSDKOK = CanonSDKCall_Init();
				}
				if (m_bSDKOK && !m_bConnectOK)
				{
					//MessageBox.Show("Connect");
					int iRet = CanonSDKCall_Connect();
					if (iRet != 0)
					{
						DCSDEV.DCSMsg.Show(
							"Cannot connect to camera.\n\n" +
							"Check that camera is:\n" +
							"    Plugged in to USB\n" +
							"    Powered on\n" +
							"    In replay mode\n" +
							"    Check batteries or power supply\n\n" +
							"If this doesn't correct the problem, try cycling the camera power off then back on.");
					}
					m_bConnectOK = (iRet == 0);
				}

				DisplayStatus(m_bConnectOK, m_bConnectOK ? "Camera connected" : "Camera cannot connect");

				// get stored canon properties and install property values into controls
				this.GetCanonCameraProperties();
				this.InstallCanonCameraProperties();

				if (m_bConnectOK)
				{
					// turn on preview
					if (CanonSDKCall_GetIfSupportsViewFinder()!=0) CanonSDKCall_ViewFinderOn(pictureBoxPreview.Handle);
				}	

				DisplayStatus(m_bConnectOK, m_bConnectOK ? "Camera ready" : "Camera not OK");
			}
			catch(System.Exception ex)
			{	
				DCSMsg.Show(ex);
				return;
			}
		}

		// display in black is bOK is true
		private void DisplayStatus(bool bOK, string strStatus)
		{
			this.tbCameraStatus.Text = strStatus;
			this.tbCameraStatus.ForeColor = bOK ? System.Drawing.Color.Black : System.Drawing.Color.Red;
			Application.DoEvents();		// need to force a refresh
		}

		private void GetCanonCameraProperties()
		{
			const int cdCOMP_000_PLUS			= 0x0018;
			const int cdFLASH_MODE_AUTO			= 0x0001;
			const int cdREL_VAL_ISO_AUTO		= 0x0000;
			const int cdPHOTO_EFFECT_OFF		= 0x0000;
			const int cdSHOOTING_MODE_PROGRAM	= 0x0001;
			const int cdWB_AUTO					= 0x0000;
			const int cdCOMP_QUALITY_NORMAL		= 0x0002;
			const int cdIMAGE_SIZE_LARGE		= 0x0000;

			m_ExpoComp = m_ps.GetIntParameter("ExpoComp", cdCOMP_000_PLUS);
			m_FlashMode = m_ps.GetIntParameter("FlashMode", cdFLASH_MODE_AUTO);
			m_ImageFormat = m_ps.GetIntParameter("ImageFormat", cdCOMP_QUALITY_NORMAL*1000 + cdIMAGE_SIZE_LARGE);
			m_ISOSpeed = m_ps.GetIntParameter("ISOSpeed", cdREL_VAL_ISO_AUTO);
			m_PhotoEffect = m_ps.GetIntParameter("PhotoEffect", cdPHOTO_EFFECT_OFF);
			m_ShootingMode = m_ps.GetIntParameter("ShootingMode", cdSHOOTING_MODE_PROGRAM);
			m_WhiteBalance = m_ps.GetIntParameter("WhiteBalance", cdWB_AUTO);

			m_ZoomPos = m_ps.GetIntParameter("ZoomPos", 0);
			m_bEnableGetFile = m_ps.GetBoolParameter("EnableGetFile", true);
			m_bShowPreview = m_ps.GetBoolParameter("ShowPreview", true);
			m_bAllowPropEdit = m_ps.GetBoolParameter("AllowPropEdit", true);
			m_bSaveCaptureParams = m_ps.GetBoolParameter("SaveCaptureParams", true);
			m_bSaveZoom = m_ps.GetBoolParameter("SaveZoom", true);
		}
		private void InstallCanonCameraProperties()
		{
			bool bRet;
			int ExpoCompCurr = m_ExpoComp;
			int FlashModeCurr = m_FlashMode;
			int ImageFormatCurr = m_ImageFormat;
			int ISOSpeedCurr = m_ISOSpeed;
			int PhotoEffectCurr = m_PhotoEffect;
			int ShootingModeCurr = m_ShootingMode;
			int WhiteBalanceCurr = m_WhiteBalance;
			int ExpoCompDefault;
			int FlashModeDefault;
			int ImageFormatDefault;
			int ISOSpeedDefault;
			int PhotoEffectDefault;
			int ShootingModeDefault;
			int WhiteBalanceDefault;

			if (m_bConnectOK)
				this.tbCameraModel.Text = CanonSDKCall_GetModelName();

			// populate must be followed by install
			m_pc.PopulateExpoComp(comboBoxExpoComp, ref ExpoCompCurr, out ExpoCompDefault);
			m_pc.PopulateFlashMode(comboBoxFlashMode, ref FlashModeCurr, out FlashModeDefault);
			m_pc.PopulateImageFormat(comboBoxImageFormat, ref ImageFormatCurr, out ImageFormatDefault);
			m_pc.PopulateISOSpeed(comboBoxISOSpeed, ref ISOSpeedCurr, out ISOSpeedDefault);
			m_pc.PopulateShootingMode(comboBoxShootingMode, ref ShootingModeCurr, out ShootingModeDefault);
			m_pc.PopulateWhiteBalance(comboBoxWhiteBalance, ref WhiteBalanceCurr, out WhiteBalanceDefault);

			m_pc.InstallExpoComp(comboBoxExpoComp, m_ExpoComp);
			m_pc.InstallFlashMode(comboBoxFlashMode, m_FlashMode);
			m_pc.InstallImageFormat(comboBoxImageFormat, ref m_ImageFormat);
			m_pc.InstallISOSpeed(comboBoxISOSpeed, m_ISOSpeed);
			m_pc.InstallShootingMode(comboBoxShootingMode, m_ShootingMode);
			m_pc.InstallWhiteBalance(comboBoxWhiteBalance, m_WhiteBalance);

			if (CanonSDKCall_GetIfSupportsPhotoEffect() != 0)
			{
				this.comboBoxPhotoEffect.Visible = true;
				this.labelPhotoEffect.Visible = true;
				m_pc.PopulatePhotoEffect(comboBoxPhotoEffect, ref PhotoEffectCurr, out PhotoEffectDefault);
				m_pc.InstallPhotoEffect(comboBoxPhotoEffect, m_PhotoEffect);
			}
			else
			{
				this.comboBoxPhotoEffect.Visible = false;
				this.labelPhotoEffect.Visible = false;
			}

			// set up zoom slider
			this.trackBarZoom.Visible = (!m_bConnectOK || (CanonSDKCall_GetIfSupportsZoom()!=0));
			int ZoomPosCurr = 0;
			int ZoomDigitalMax = 12;
			int ZoomOpticalMax = 6;
			int ZoomStepSize = 1;
			if (m_bConnectOK && (CanonSDKCall_GetIfSupportsZoom()!=0))
			{
				DisplayStatus(true, "Camera connected - zooming");
				bRet = CanonSDKCall_ZoomGet(out ZoomDigitalMax, out ZoomOpticalMax, out ZoomPosCurr, out ZoomStepSize);
				if (bRet && ZoomPosCurr != m_ZoomPos)
				{
					if (m_bConnectOK) bRet = CanonSDKCall_ZoomSet(m_ZoomPos);
				}
			}
            int iStep = Math.Max(1, ZoomOpticalMax / 10);
            trackBarZoom.Minimum = 0;
            trackBarZoom.Maximum = ZoomOpticalMax;
            trackBarZoom.SmallChange = ZoomStepSize;
            trackBarZoom.LargeChange = iStep;
            trackBarZoom.TickFrequency = iStep;
            if (m_ZoomPos > ZoomOpticalMax) m_ZoomPos = ZoomOpticalMax;
            trackBarZoom.Value = m_ZoomPos;

			this.checkBoxEnableGetFile.Checked = m_bEnableGetFile; 
			this.checkBoxShowPreview.Checked = m_bShowPreview; 
			this.checkBoxAllowPropEdit.Checked = m_bAllowPropEdit; 
			this.checkBoxSaveCaptureParams.Checked = m_bSaveCaptureParams; 
			this.checkBoxSaveZoom.Checked = m_bSaveZoom; 
		}
		private void WriteCanonCameraProperties()
		{
			m_ps.WriteIntParameter("ExpoComp", m_ExpoComp);
			m_ps.WriteIntParameter("FlashMode", m_FlashMode);
			m_ps.WriteIntParameter("ImageFormat", m_ImageFormat);
			m_ps.WriteIntParameter("ISOSpeed", m_ISOSpeed);
			m_ps.WriteIntParameter("PhotoEffect", m_PhotoEffect);
			m_ps.WriteIntParameter("ShootingMode", m_ShootingMode);
			m_ps.WriteIntParameter("WhiteBalance", m_WhiteBalance);

			m_ZoomPos = this.trackBarZoom.Value;
			m_ps.WriteIntParameter("ZoomPos", m_ZoomPos);

			m_bEnableGetFile = this.checkBoxEnableGetFile.Checked;
			m_ps.WriteBoolParameter("EnableGetFile", m_bEnableGetFile);
			m_bShowPreview = this.checkBoxShowPreview.Checked;
			m_ps.WriteBoolParameter("ShowPreview", m_bShowPreview);
			m_bAllowPropEdit = this.checkBoxAllowPropEdit.Checked;
			m_ps.WriteBoolParameter("AllowPropEdit", m_bAllowPropEdit);
			m_bSaveCaptureParams = this.checkBoxSaveCaptureParams.Checked;
			m_ps.WriteBoolParameter("SaveCaptureParams", m_bSaveCaptureParams);
			m_bSaveZoom = this.checkBoxSaveZoom.Checked;
			m_ps.WriteBoolParameter("SaveZoom", m_bSaveZoom);
		}

		private void trackBarZoom_Scroll(object sender, System.EventArgs e)
		{
			m_ZoomPos = this.trackBarZoom.Value;
			if (m_bConnectOK && (CanonSDKCall_GetIfSupportsZoom()!=0))
				CanonSDKCall_ZoomSet(m_ZoomPos);		
		}

		private void buttonReset_Click(object sender, System.EventArgs e)
		{
			Cursor saveCursor = this.Cursor;
			this.Cursor = Cursors.WaitCursor;

			// get status from non dot-net canon interface
			// syh - THESE FUNCTIONS ARE NOT RELIABLE wwhen they return bool
			m_bConnectOK = (CanonSDKCall_GetIfConnected()!=0);
			m_bSDKOK = (CanonSDKCall_GetIfStarted()!=0);

			// disconnect if currently connected
			this.DisconnectCanon();

			// re establish connection
			this.ConnectCanon();
			this.Cursor = saveCursor;
			return;
		}

		private void buttonAbout_Click(object sender, System.EventArgs e)
		{
			DCSDEV.CanonCamera.AboutCanonCamera aboutDlg = new DCSDEV.CanonCamera.AboutCanonCamera();
			aboutDlg.ShowDialog(this);
		}

		private void comboBoxExpoComp_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			Cursor saveCursor = this.Cursor;
			this.Cursor = Cursors.WaitCursor;
			m_pc.ExtractExpoComp(comboBoxExpoComp, out m_ExpoComp);
			m_pc.InstallExpoComp(null, m_ExpoComp);	// install in camera and not in control
			this.Cursor = saveCursor;
		}
		private void comboBoxFlashMode_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			Cursor saveCursor = this.Cursor;
			this.Cursor = Cursors.WaitCursor;
			m_pc.ExtractFlashMode(comboBoxFlashMode, out m_FlashMode);
			m_pc.InstallFlashMode(null, m_FlashMode);	// install in camera and not in control
			this.Cursor = saveCursor;
		}
		private void comboBoxImageFormat_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			Cursor saveCursor = this.Cursor;
			this.Cursor = Cursors.WaitCursor;
			m_pc.ExtractImageFormat(comboBoxImageFormat.Text, out m_ImageFormat);
			m_pc.InstallImageFormat(null, ref m_ImageFormat);	// install in camera and not in control
			this.Cursor = saveCursor;
		}
		private void comboBoxISOSpeed_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			Cursor saveCursor = this.Cursor;
			this.Cursor = Cursors.WaitCursor;
			m_pc.ExtractISOSpeed(comboBoxISOSpeed, out m_ISOSpeed);
			m_pc.InstallISOSpeed(null, m_ISOSpeed);	// install in camera and not in control
			this.Cursor = saveCursor;
		}
		private void comboBoxPhotoEffect_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			Cursor saveCursor = this.Cursor;
			this.Cursor = Cursors.WaitCursor;
			m_pc.ExtractPhotoEffect(comboBoxPhotoEffect, out m_PhotoEffect);
			m_pc.InstallPhotoEffect(null, m_PhotoEffect);	// install in camera and not in control
			this.Cursor = saveCursor;
		}
		private void comboBoxShootingMode_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			Cursor saveCursor = this.Cursor;
			this.Cursor = Cursors.WaitCursor;
			m_pc.ExtractShootingMode(comboBoxShootingMode, out m_ShootingMode);
			m_pc.InstallShootingMode(null, m_ShootingMode);	// install in camera and not in control
			this.Cursor = saveCursor;
		}
		private void comboBoxWhiteBalance_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			Cursor saveCursor = this.Cursor;
			this.Cursor = Cursors.WaitCursor;
			m_pc.ExtractWhiteBalance(comboBoxWhiteBalance, out m_WhiteBalance);
			m_pc.InstallWhiteBalance(null, m_WhiteBalance);	// install in camera and not in control
			this.Cursor = saveCursor;
		}

		private void buttonOK_Click(object sender, System.EventArgs e)
		{
			WriteCanonCameraProperties();
			buttonCancel_Click(null, null);
		}

		private void buttonCancel_Click(object sender, System.EventArgs e)
		{
			// disconnect if currently connected
			this.DisconnectCanon();
			this.Close();
		}
	}
}
