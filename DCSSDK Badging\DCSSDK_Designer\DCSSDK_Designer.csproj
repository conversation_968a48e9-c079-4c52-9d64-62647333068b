﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.50727</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{89F5730B-B502-4084-B87D-E40FA6526BB8}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DCSSDK_DCSDesigner</RootNamespace>
    <AssemblyName>DCSSDK_Designer</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>2.0</OldToolsVersion>
    <UpgradeBackupLocation />
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DCSSDK_FinisherProperties, Version=1.2.709.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Capture\DCSSDK_FinisherProperties\bin\Debug\DCSSDK_FinisherProperties.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AboutForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="BadgeDesignProperties.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DCSAlignmentProperties.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="DCSBackGroundProperties.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="DCSConditionalColor.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DCSConditionalColor.Designer.cs">
      <DependentUpon>DCSConditionalColor.cs</DependentUpon>
    </Compile>
    <Compile Include="DCSDesigner.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DCSDesignerDoc.cs" />
    <Compile Include="DCSDesignerView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DCSFontDefaults.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DCSFontProperties.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="DCSFormulaAddField.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DCSFormulaAddField.Designer.cs">
      <DependentUpon>DCSFormulaAddField.cs</DependentUpon>
    </Compile>
    <Compile Include="DCSFormulaAddFunction.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DCSFormulaAddFunction.Designer.cs">
      <DependentUpon>DCSFormulaAddFunction.cs</DependentUpon>
    </Compile>
    <Compile Include="DCSFormulaAddText.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DCSFormulaAddText.Designer.cs">
      <DependentUpon>DCSFormulaAddText.cs</DependentUpon>
    </Compile>
    <Compile Include="DCSFormulaDesigner.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DCSFrameProperties1.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="DCSMagStripeProperties.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="DCSPositionSizeProperties.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="DCSRotationProperties.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="DCSSourceProperties.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="DrawingObjProperties.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IcaoObjProperties.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ImageObjProperties.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="SetGridProperties.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="TextObjProperties.cs">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Icon1.ico" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\DCSSDK Capture\DCSSDK_Finisher\DCSSDK_Finisher.csproj">
      <Project>{4D22800C-2DB5-450B-A8A0-A130A3D50BD6}</Project>
      <Name>DCSSDK_Finisher</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\DCSSDK Capture\DCSSDK_Utilities\DCSSDK_Utilities.csproj">
      <Project>{6EF6073A-143A-497A-9FFA-21DD39EBC9ED}</Project>
      <Name>DCSSDK_Utilities</Name>
    </ProjectReference>
    <ProjectReference Include="..\DCSBarcodeIF\DCSBarcodeIF.csproj">
      <Project>{7AEC24F7-21A9-480E-B59F-FE18F943154C}</Project>
      <Name>DCSBarcodeIF</Name>
    </ProjectReference>
    <ProjectReference Include="..\DCSSDK_Design\DCSSDK_Design.csproj">
      <Project>{070D3ED6-2209-4AFA-B015-64137A56DAF3}</Project>
      <Name>DCSSDK_Design</Name>
    </ProjectReference>
    <ProjectReference Include="..\DCSSDK_PrintProperties\DCSSDK_PrintProperties.csproj">
      <Project>{347DA4DB-3618-45A0-B87C-18F6D39F6048}</Project>
      <Name>DCSSDK_PrintProperties</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="AboutForm.resx">
      <DependentUpon>AboutForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="BadgeDesignProperties.resx">
      <DependentUpon>BadgeDesignProperties.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSAlignmentProperties.resx">
      <DependentUpon>DCSAlignmentProperties.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSBackGroundProperties.resx">
      <DependentUpon>DCSBackGroundProperties.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSConditionalColor.resx">
      <DependentUpon>DCSConditionalColor.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSDesigner.resx">
      <DependentUpon>DCSDesigner.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSDesignerView.resx">
      <DependentUpon>DCSDesignerView.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSFontDefaults.resx">
      <DependentUpon>DCSFontDefaults.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSFontProperties.resx">
      <DependentUpon>DCSFontProperties.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSFormulaAddField.resx">
      <SubType>Designer</SubType>
      <DependentUpon>DCSFormulaAddField.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSFormulaAddFunction.resx">
      <SubType>Designer</SubType>
      <DependentUpon>DCSFormulaAddFunction.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSFormulaAddText.resx">
      <SubType>Designer</SubType>
      <DependentUpon>DCSFormulaAddText.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSFormulaDesigner.resx">
      <DependentUpon>DCSFormulaDesigner.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSFrameProperties1.resx">
      <DependentUpon>DCSFrameProperties1.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSMagStripeProperties.resx">
      <DependentUpon>DCSMagStripeProperties.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSPositionSizeProperties.resx">
      <DependentUpon>DCSPositionSizeProperties.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSRotationProperties.resx">
      <DependentUpon>DCSRotationProperties.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSSourceProperties.resx">
      <DependentUpon>DCSSourceProperties.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DrawingObjProperties.resx">
      <DependentUpon>DrawingObjProperties.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="IcaoObjProperties.resx">
      <DependentUpon>IcaoObjProperties.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ImageObjProperties.resx">
      <DependentUpon>ImageObjProperties.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <SubType>Designer</SubType>
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="SetGridProperties.resx">
      <DependentUpon>SetGridProperties.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="TextObjProperties.resx">
      <DependentUpon>TextObjProperties.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="ClassDiagram1.cd" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.3.1">
      <Visible>False</Visible>
      <ProductName>Windows Installer 3.1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>