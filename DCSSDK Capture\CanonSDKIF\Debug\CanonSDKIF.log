﻿  StdAfx.cpp
  _WIN32_WINNT not defined. Defaulting to _WIN32_WINNT_MAXVER (see WinSDKVer.h)
  CanonSDKIF.cpp
  push, _PR_TYPE_H_PACK_, 1
  pop, _PR_TYPE_H_PACK_
  push, _CD_TYPE_H_PACK_, 1
  pop, _CD_TYPE_H_PACK_
D:\repos_D\SDS Collection\DCSSDK Capture\CanonSDKIF\CanonSDKIF.cpp(809,3): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file '/CanonSDKIF.cpp')
  
D:\repos_D\SDS Collection\DCSSDK Capture\CanonSDKIF\CanonSDKIF.cpp(812,3): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file '/CanonSDKIF.cpp')
  
D:\repos_D\SDS Collection\DCSSDK Capture\CanonSDKIF\CanonSDKIF.cpp(813,3): warning C4996: 'strcpy': This function or variable may be unsafe. Consider using strcpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file '/CanonSDKIF.cpp')
  
D:\repos_D\SDS Collection\DCSSDK Capture\CanonSDKIF\CanonSDKIF.cpp(904,4): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file '/CanonSDKIF.cpp')
  
D:\repos_D\SDS Collection\DCSSDK Capture\CanonSDKIF\CanonSDKIF.cpp(905,4): warning C4996: 'strcat': This function or variable may be unsafe. Consider using strcat_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file '/CanonSDKIF.cpp')
  
D:\repos_D\SDS Collection\DCSSDK Capture\CanonSDKIF\CanonSDKIF.cpp(915,3): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file '/CanonSDKIF.cpp')
  
D:\repos_D\SDS Collection\DCSSDK Capture\CanonSDKIF\CanonSDKIF.cpp(930,3): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file '/CanonSDKIF.cpp')
  
D:\repos_D\SDS Collection\DCSSDK Capture\CanonSDKIF\CanonSDKIF.cpp(1021,4): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file '/CanonSDKIF.cpp')
  
D:\repos_D\SDS Collection\DCSSDK Capture\CanonSDKIF\CanonSDKIF.cpp(1022,4): warning C4996: 'strcat': This function or variable may be unsafe. Consider using strcat_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file '/CanonSDKIF.cpp')
  
D:\repos_D\SDS Collection\DCSSDK Capture\CanonSDKIF\CanonSDKIF.cpp(1025,3): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file '/CanonSDKIF.cpp')
  
D:\repos_D\SDS Collection\DCSSDK Capture\CanonSDKIF\CanonSDKIF.cpp(1060,3): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file '/CanonSDKIF.cpp')
  
D:\repos_D\SDS Collection\DCSSDK Capture\CanonSDKIF\CanonSDKIF.cpp(2709,2): warning C4996: 'strcpy': This function or variable may be unsafe. Consider using strcpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file '/CanonSDKIF.cpp')
  
  MYSTREAM.CPP
  push, _CD_TYPE_H_PACK_, 1
  pop, _CD_TYPE_H_PACK_
D:\repos_D\SDS Collection\DCSSDK Capture\CanonSDKIF\MYSTREAM.CPP(518,2): warning C4996: 'strcpy': This function or variable may be unsafe. Consider using strcpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file '/MYSTREAM.CPP')
  
CanonSDKIF.obj : warning LNK4075: ignoring '/EDITANDCONTINUE' due to '/SAFESEH' specification
JpegLib.lib(jerror.obj) : error LNK2005: "struct jpeg_error_mgr * __cdecl jpeg_std_error(struct jpeg_error_mgr *)" (?jpeg_std_error@@YAPAUjpeg_error_mgr@@PAU1@@Z) already defined in JpegLib.lib(jerror.obj)
JpegLib.lib(jmemmgr.obj) : error LNK2005: "void __cdecl jinit_memory_mgr(struct jpeg_common_struct *)" (?jinit_memory_mgr@@YAXPAUjpeg_common_struct@@@Z) already defined in JpegLib.lib(jmemmgr.obj)
     Creating library .\Debug/CanonSDKIF.lib and object .\Debug/CanonSDKIF.exp
LINK : warning LNK4098: defaultlib 'LIBCMT' conflicts with use of other libs; use /NODEFAULTLIB:library
JpegLib.lib(jerror.obj) : error LNK2019: unresolved external symbol ___iob_func referenced in function "void __cdecl output_message(struct jpeg_common_struct *)" (?output_message@@YAXPAUjpeg_common_struct@@@Z)
.\Debug\CanonSDKIF.dll : fatal error LNK1120: 1 unresolved externals
