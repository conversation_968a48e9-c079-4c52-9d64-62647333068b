using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;

namespace DCSSDK
{
	/// <summary>
	/// Summary description for DCSDesignDataSelect.
	/// </summary>
	public class DCSDesignDataSelect : System.Windows.Forms.Form
	{
		string m_DataDirectory;
		DCSSDK.DCSDatatypes.DCSDataFileTypes m_typeFile;
		string m_strFilename;

		private System.Windows.Forms.ListBox listBox1;
		private System.Windows.Forms.PictureBox pictureBox1;
		private System.Windows.Forms.Button buttonOK;
		private System.Windows.Forms.Button buttonCancel;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.TextBox tbStorageLoc;
		private System.Windows.Forms.Button buttonBrowse;
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		public DCSDesignDataSelect(string strDataDirectory, DCSSDK.DCSDatatypes.DCSDataFileTypes typeFile)
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			m_DataDirectory = strDataDirectory;
			this.tbStorageLoc.Text = m_DataDirectory;
			m_typeFile = typeFile;

			if (m_typeFile == DCSSDK.DCSDatatypes.DCSDataFileTypes.DCS_DESIGN_FILE)
			{
				this.Text = "Select badge design";
				// get list of badge designs
				this.listBox1.Items.Clear();
				string[] fileNames = System.IO.Directory.GetFiles(m_DataDirectory, "*.Bdg");
				foreach (string str in fileNames)
					this.listBox1.Items.Add(System.IO.Path.GetFileNameWithoutExtension(str));
			}
			else		// (m_typeFile = DCSSDK.DCSDatatypes.DCSDataFileTypes.DCS_BDG_IMG_FILE)
			{
				this.Text = "Select image";
				// get list of image file names
				this.listBox1.Items.Clear();
				string[] fileNames = System.IO.Directory.GetFiles(m_DataDirectory);
				foreach (string str in fileNames)
				{
					string ext = System.IO.Path.GetExtension(str).ToUpper();
					switch (ext)
					{
						case ".BMP":
						case ".JPG":
						case ".JPEG":
						case ".GIF":
						case ".PNG":
							this.listBox1.Items.Add(System.IO.Path.GetFileName(str));
							break;
					}
				}
			}
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			this.listBox1 = new System.Windows.Forms.ListBox();
			this.pictureBox1 = new System.Windows.Forms.PictureBox();
			this.buttonOK = new System.Windows.Forms.Button();
			this.buttonCancel = new System.Windows.Forms.Button();
			this.label1 = new System.Windows.Forms.Label();
			this.tbStorageLoc = new System.Windows.Forms.TextBox();
			this.buttonBrowse = new System.Windows.Forms.Button();
			this.SuspendLayout();
			// 
			// listBox1
			// 
			this.listBox1.Location = new System.Drawing.Point(32, 64);
			this.listBox1.Name = "listBox1";
			this.listBox1.Size = new System.Drawing.Size(152, 134);
			this.listBox1.TabIndex = 0;
			this.listBox1.SelectedIndexChanged += new System.EventHandler(this.listBox1_SelectedIndexChanged);
			// 
			// pictureBox1
			// 
			this.pictureBox1.Location = new System.Drawing.Point(216, 64);
			this.pictureBox1.Name = "pictureBox1";
			this.pictureBox1.Size = new System.Drawing.Size(152, 136);
			this.pictureBox1.TabIndex = 1;
			this.pictureBox1.TabStop = false;
			// 
			// buttonOK
			// 
			this.buttonOK.DialogResult = System.Windows.Forms.DialogResult.OK;
			this.buttonOK.Location = new System.Drawing.Point(232, 232);
			this.buttonOK.Name = "buttonOK";
			this.buttonOK.Size = new System.Drawing.Size(80, 24);
			this.buttonOK.TabIndex = 2;
			this.buttonOK.Text = "OK";
			// 
			// buttonCancel
			// 
			this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.buttonCancel.Location = new System.Drawing.Point(336, 232);
			this.buttonCancel.Name = "buttonCancel";
			this.buttonCancel.TabIndex = 3;
			this.buttonCancel.Text = "Cancel";
			// 
			// label1
			// 
			this.label1.Location = new System.Drawing.Point(32, 8);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(384, 16);
			this.label1.TabIndex = 4;
			this.label1.Text = "Location of data storage collection";
			// 
			// tbStorageLoc
			// 
			this.tbStorageLoc.BorderStyle = System.Windows.Forms.BorderStyle.None;
			this.tbStorageLoc.Location = new System.Drawing.Point(32, 32);
			this.tbStorageLoc.Name = "tbStorageLoc";
			this.tbStorageLoc.ReadOnly = true;
			this.tbStorageLoc.Size = new System.Drawing.Size(376, 13);
			this.tbStorageLoc.TabIndex = 5;
			this.tbStorageLoc.Text = "textBox1";
			// 
			// buttonBrowse
			// 
			this.buttonBrowse.Location = new System.Drawing.Point(40, 232);
			this.buttonBrowse.Name = "buttonBrowse";
			this.buttonBrowse.Size = new System.Drawing.Size(120, 24);
			this.buttonBrowse.TabIndex = 6;
			this.buttonBrowse.Text = "Browse files";
			this.buttonBrowse.Click += new System.EventHandler(this.buttonBrowse_Click);
			// 
			// DCSDesignDataSelect
			// 
			this.AcceptButton = this.buttonOK;
			this.AutoScaleBaseSize = new System.Drawing.Size(5, 13);
			this.CancelButton = this.buttonCancel;
			this.ClientSize = new System.Drawing.Size(424, 266);
			this.Controls.Add(this.buttonBrowse);
			this.Controls.Add(this.tbStorageLoc);
			this.Controls.Add(this.label1);
			this.Controls.Add(this.buttonCancel);
			this.Controls.Add(this.buttonOK);
			this.Controls.Add(this.pictureBox1);
			this.Controls.Add(this.listBox1);
			this.Name = "DCSDesignDataSelect";
			this.Text = "DCSDesignDataSelect";
			this.ResumeLayout(false);

		}
		#endregion

		private void listBox1_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if (m_typeFile != DCSSDK.DCSDatatypes.DCSDataFileTypes.DCS_BDG_IMG_FILE) return;
			string strFilename = this.listBox1.Text;
			string strFullPath = DCSSDK.DCSDesignDataAccess.ExpandImageName(strFilename, false);

		}

		private void buttonBrowse_Click(object sender, System.EventArgs e)
		{
			try
			{
				DCSSDK.ParameterStore ps = new ParameterStore("DCSDesigner");

				OpenFileDialog openDlg = new OpenFileDialog();
				openDlg.Filter = "Image Files(*.BMP;*.JPG;*.JPEG;*.GIF;*.PNG)|*.BMP;*.JPG;*.JPEG;*.GIF,*.PNG|All files (*.*)|*.*";
				//openDlg.FileName = "";
				openDlg.InitialDirectory = ps.GetStringParameter("BrowseDir", "");
				openDlg.CheckFileExists = true;
				openDlg.CheckPathExists = true;
				openDlg.Title = "Select image to import into badging data collection";
		
				DialogResult res = openDlg.ShowDialog ();
				if (res == DialogResult.OK)
				{
					ps.WriteStringParameter("BrowseDir", System.IO.Path.GetPathRoot(openDlg.FileName));
					if (System.IO.Path.GetDirectoryName(openDlg.FileName).ToUpper() == m_DataDirectory.ToUpper())
					{
						// image is selected from the badge data collection - no copy necessary
						m_strFilename = System.IO.Path.GetFileName(openDlg.FileName);
					}
					else
					{
						m_strFilename = System.IO.Path.GetFileName(openDlg.FileName);
						string fullnameDst = DCSSDK.DCSDesignDataAccess.ExpandImageName(m_strFilename, true);
						if (fullnameDst != null)
						{
							// new image name is already in the badge data collection
							if (DCSSDK.DCSMsg.ShowOKC("OK to replace existing image?") == DialogResult.Cancel)
								return;
						}
						else
						{
							fullnameDst = DCSSDK.DCSDesignDataAccess.ExpandImageName(m_strFilename, false);
							this.listBox1.Items.Add(m_strFilename);
						}
						System.IO.File.Copy(openDlg.FileName, fullnameDst, true);
					}
					int index = this.listBox1.Items.IndexOf(m_strFilename);
					this.listBox1.SelectedIndex = index;
				}
			}
			catch (Exception ex)
			{
				DCSSDK.DCSMsg.Show("Cannot Browse", ex);
			}
		}

		public string Filename
		{
			get 
			{ 
				if (this.listBox1.Items.Count == 0) return null;
				if (this.listBox1.SelectedIndex == -1) return null;
				return (string)this.listBox1.Text;
			}
			set 
			{ 
				m_strFilename = value;
				this.listBox1.SelectedIndex = -1;
				if (this.listBox1.Items.Count == 0) return;
				int index = this.listBox1.Items.IndexOf(m_strFilename);
				if (index < 0) return;
				this.listBox1.SelectedIndex = index;
			}
		}
	}
}
