using System;
using System.Drawing;
using System.Drawing.Printing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;
using DCSDEV;

namespace DCSDEV
{
	/// <summary>
	/// Summary description for Form1.
	/// </summary>
	internal class DCSImageDisplay : System.Windows.Forms.Form
	{
        internal static int HeaderHeight = 34;
        private DCSSDK.CaptureMgt.DCSSDK_CaptureMgt m_parent;
		internal static ArrayList m_arrayListImageDisplay = new ArrayList();
		private double m_dCertsScale = 1.0;		// scale of zero is fit width to window; -1 is fit image to window preserving aspect ratio
        private double m_dDisplayScale;
        double m_dCertsScanRes = 300.0;

		private DCSDatabaseIF.ImageClass m_ImageClass;
		private int m_ImageSubClass;
		private bool m_IsFirstSubclass = false;
		internal System.Windows.Forms.PictureBox pbDisplayImage;
		private bool m_bSaveNewRect = false;
		private string m_ImageID;
		private int m_ArchiveInstance;
		private string m_ImageTitle;
		RectangleF m_rectPrintable;

        private ContextMenuStrip contextMenuStripCert;
		private ToolStripMenuItem zoom50ToolStripMenuItem;
		private ToolStripMenuItem zoom100ToolStripMenuItem;
		private ToolStripMenuItem zoom200ToolStripMenuItem;
		private ToolStripMenuItem zoomToFitToolStripMenuItem;
		private ToolStripMenuItem zoomToWidthToolStripMenuItem;
		private ToolStripMenuItem closeToolStripMenuItem;
		private ContextMenuStrip contextMenuStripPhoto;
		private ToolStripMenuItem closePhotoMenuItem;
		private ToolStripMenuItem closeAllPhotosMenuItem;
		private ToolStripMenuItem closeAllToolStripMenuItem;
		private ToolStripMenuItem exportToolStripMenuItem;
		private ToolStripMenuItem exportToolStripMenuItem1;
		private System.Drawing.Printing.PrintDocument printDocument1;
		private ToolStripMenuItem printToolStripMenuItem;
		private ToolStripMenuItem printToolStripMenuItem1;
        private ToolStripMenuItem imageHistoryToolStripMenuItem;
		private IContainer components;

        internal DCSImageDisplay(DCSSDK.CaptureMgt.DCSSDK_CaptureMgt parent)
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			m_parent = parent;
			m_ImageClass = DCSDatabaseIF.ImageClass.Portrait;
			m_ImageSubClass = 0;
			m_ArchiveInstance = 0;
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if (this.pbDisplayImage.Image != null)
				{
					this.pbDisplayImage.Image.Dispose();
					this.pbDisplayImage.Image = null;
				}
				if (components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.pbDisplayImage = new System.Windows.Forms.PictureBox();
            this.contextMenuStripCert = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.closeToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.closeAllToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.zoomToFitToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.zoomToWidthToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.zoom100ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.zoom200ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.zoom50ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.exportToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.printToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.contextMenuStripPhoto = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.closePhotoMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.closeAllPhotosMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.exportToolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.printToolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.printDocument1 = new System.Drawing.Printing.PrintDocument();
            this.imageHistoryToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.pbDisplayImage)).BeginInit();
            this.contextMenuStripCert.SuspendLayout();
            this.contextMenuStripPhoto.SuspendLayout();
            this.SuspendLayout();
            // 
            // pbDisplayImage
            // 
            this.pbDisplayImage.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.pbDisplayImage.Location = new System.Drawing.Point(0, 0);
            this.pbDisplayImage.Name = "pbDisplayImage";
            this.pbDisplayImage.Size = new System.Drawing.Size(248, 224);
            this.pbDisplayImage.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
            this.pbDisplayImage.TabIndex = 0;
            this.pbDisplayImage.TabStop = false;
            this.pbDisplayImage.DoubleClick += new System.EventHandler(this.pbDisplayImage_DoubleClick);
            this.pbDisplayImage.Paint += new System.Windows.Forms.PaintEventHandler(this.pbDisplayImage_Paint);
            // 
            // contextMenuStripCert
            // 
            this.contextMenuStripCert.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.closeToolStripMenuItem,
            this.closeAllToolStripMenuItem,
            this.zoomToFitToolStripMenuItem,
            this.zoomToWidthToolStripMenuItem,
            this.zoom100ToolStripMenuItem,
            this.zoom200ToolStripMenuItem,
            this.zoom50ToolStripMenuItem,
            this.exportToolStripMenuItem,
            this.printToolStripMenuItem});
            this.contextMenuStripCert.Name = "contextMenuStrip1";
            this.contextMenuStripCert.Size = new System.Drawing.Size(154, 202);
            // 
            // closeToolStripMenuItem
            // 
            this.closeToolStripMenuItem.Name = "closeToolStripMenuItem";
            this.closeToolStripMenuItem.Size = new System.Drawing.Size(153, 22);
            this.closeToolStripMenuItem.Text = "Close";
            this.closeToolStripMenuItem.Click += new System.EventHandler(this.closeImageDisplay);
            // 
            // closeAllToolStripMenuItem
            // 
            this.closeAllToolStripMenuItem.Name = "closeAllToolStripMenuItem";
            this.closeAllToolStripMenuItem.Size = new System.Drawing.Size(153, 22);
            this.closeAllToolStripMenuItem.Text = "Close all";
            this.closeAllToolStripMenuItem.Click += new System.EventHandler(this.closeAllImageDisplays);
            // 
            // zoomToFitToolStripMenuItem
            // 
            this.zoomToFitToolStripMenuItem.Name = "zoomToFitToolStripMenuItem";
            this.zoomToFitToolStripMenuItem.Size = new System.Drawing.Size(153, 22);
            this.zoomToFitToolStripMenuItem.Text = "Zoom to fit";
            this.zoomToFitToolStripMenuItem.Click += new System.EventHandler(this.zoomToFitToolStripMenuItem_Click);
            // 
            // zoomToWidthToolStripMenuItem
            // 
            this.zoomToWidthToolStripMenuItem.Name = "zoomToWidthToolStripMenuItem";
            this.zoomToWidthToolStripMenuItem.Size = new System.Drawing.Size(153, 22);
            this.zoomToWidthToolStripMenuItem.Text = "Zoom to width";
            this.zoomToWidthToolStripMenuItem.Click += new System.EventHandler(this.zoomToWidthToolStripMenuItem_Click);
            // 
            // zoom100ToolStripMenuItem
            // 
            this.zoom100ToolStripMenuItem.Name = "zoom100ToolStripMenuItem";
            this.zoom100ToolStripMenuItem.Size = new System.Drawing.Size(153, 22);
            this.zoom100ToolStripMenuItem.Text = "Zoom 100%";
            this.zoom100ToolStripMenuItem.Click += new System.EventHandler(this.zoom100ToolStripMenuItem_Click);
            // 
            // zoom200ToolStripMenuItem
            // 
            this.zoom200ToolStripMenuItem.Name = "zoom200ToolStripMenuItem";
            this.zoom200ToolStripMenuItem.Size = new System.Drawing.Size(153, 22);
            this.zoom200ToolStripMenuItem.Text = "Zoom 2 x";
            this.zoom200ToolStripMenuItem.Click += new System.EventHandler(this.zoom200ToolStripMenuItem_Click);
            // 
            // zoom50ToolStripMenuItem
            // 
            this.zoom50ToolStripMenuItem.Name = "zoom50ToolStripMenuItem";
            this.zoom50ToolStripMenuItem.Size = new System.Drawing.Size(153, 22);
            this.zoom50ToolStripMenuItem.Text = "Zoom .5 x";
            this.zoom50ToolStripMenuItem.Click += new System.EventHandler(this.zoom50ToolStripMenuItem_Click);
            // 
            // exportToolStripMenuItem
            // 
            this.exportToolStripMenuItem.Name = "exportToolStripMenuItem";
            this.exportToolStripMenuItem.Size = new System.Drawing.Size(153, 22);
            this.exportToolStripMenuItem.Text = "Export to File";
            this.exportToolStripMenuItem.Click += new System.EventHandler(this.exportImageDisplay);
            // 
            // printToolStripMenuItem
            // 
            this.printToolStripMenuItem.Name = "printToolStripMenuItem";
            this.printToolStripMenuItem.Size = new System.Drawing.Size(153, 22);
            this.printToolStripMenuItem.Text = "Print";
            this.printToolStripMenuItem.Click += new System.EventHandler(this.printImageDisplay);
            // 
            // contextMenuStripPhoto
            // 
            this.contextMenuStripPhoto.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.closePhotoMenuItem,
            this.closeAllPhotosMenuItem,
            this.imageHistoryToolStripMenuItem,
            this.exportToolStripMenuItem1,
            this.printToolStripMenuItem1});
            this.contextMenuStripPhoto.Name = "contextMenuStripPhoto";
            this.contextMenuStripPhoto.Size = new System.Drawing.Size(181, 136);
            // 
            // closePhotoMenuItem
            // 
            this.closePhotoMenuItem.Name = "closePhotoMenuItem";
            this.closePhotoMenuItem.Size = new System.Drawing.Size(152, 22);
            this.closePhotoMenuItem.Text = "Close";
            this.closePhotoMenuItem.Click += new System.EventHandler(this.closeImageDisplay);
            // 
            // closeAllPhotosMenuItem
            // 
            this.closeAllPhotosMenuItem.Name = "closeAllPhotosMenuItem";
            this.closeAllPhotosMenuItem.Size = new System.Drawing.Size(152, 22);
            this.closeAllPhotosMenuItem.Text = "Close all";
            this.closeAllPhotosMenuItem.Click += new System.EventHandler(this.closeAllImageDisplays);
            // 
            // exportToolStripMenuItem1
            // 
            this.exportToolStripMenuItem1.Name = "exportToolStripMenuItem1";
            this.exportToolStripMenuItem1.Size = new System.Drawing.Size(152, 22);
            this.exportToolStripMenuItem1.Text = "Export to File";
            this.exportToolStripMenuItem1.Click += new System.EventHandler(this.exportImageDisplay);
            // 
            // printToolStripMenuItem1
            // 
            this.printToolStripMenuItem1.Name = "printToolStripMenuItem1";
            this.printToolStripMenuItem1.Size = new System.Drawing.Size(152, 22);
            this.printToolStripMenuItem1.Text = "Print";
            this.printToolStripMenuItem1.Click += new System.EventHandler(this.printImageDisplay);
            // 
            // printDocument1
            // 
            this.printDocument1.PrintPage += new System.Drawing.Printing.PrintPageEventHandler(this.eh_PrintPage);
            // 
            // imageHistoryToolStripMenuItem
            // 
            this.imageHistoryToolStripMenuItem.Name = "imageHistoryToolStripMenuItem";
            this.imageHistoryToolStripMenuItem.Size = new System.Drawing.Size(180, 22);
            this.imageHistoryToolStripMenuItem.Text = "Cycle Image History";
            this.imageHistoryToolStripMenuItem.Click += new System.EventHandler(this.imageHistoryToolStripMenuItem_Click);
            // 
            // DCSImageDisplay
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.ClientSize = new System.Drawing.Size(292, 266);
            this.Controls.Add(this.pbDisplayImage);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.MinimumSize = new System.Drawing.Size(50, 50);
            this.Name = "DCSImageDisplay";
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.Manual;
            this.Text = "Image Display";
            this.Closing += new System.ComponentModel.CancelEventHandler(this.ImageDisplayForm_Closing);
            this.LocationChanged += new System.EventHandler(this.DCSImageDisplay_LocationChanged);
            ((System.ComponentModel.ISupportInitialize)(this.pbDisplayImage)).EndInit();
            this.contextMenuStripCert.ResumeLayout(false);
            this.contextMenuStripPhoto.ResumeLayout(false);
            this.ResumeLayout(false);

		}
		#endregion

		private void ImageDisplayForm_Closing(object sender, System.ComponentModel.CancelEventArgs e)
		{
			bool bFingerMapping = false;
			string strFingerMap = "";
			if (m_ImageClass == DCSDatabaseIF.ImageClass.Fingerprint)
			{
				DCSDEV.ParameterStore ps0 = new DCSDEV.ParameterStore("DCSSDK_Mgt");
				bFingerMapping = ps0.GetBoolParameter("FingerMapping", true);
				strFingerMap = ps0.GetStringParameter("FingerMapString", "0549");
				if (strFingerMap.Length < 1) bFingerMapping = false;
			}

			if (m_bSaveNewRect)
			{
				bool bSave;
				//if (bFingerMapping) bSave = (strFingerMap[0] - '0' == m_ImageSubClass);
				//else bSave = (m_ImageSubClass == 0);
				bSave = this.m_IsFirstSubclass || m_ImageClass == DCSDatabaseIF.ImageClass.Certificate;
				if (bSave)
				{
					DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore(m_ImageClass.ToString() + "_Display");
					if (m_ImageClass != DCSDatabaseIF.ImageClass.Certificate)
						ps.WriteRectParameter("DisplayRect", new Rectangle(this.Location, this.ClientRectangle.Size));
					else
						ps.WriteRectParameter("DisplayRect", new Rectangle(this.Location, this.ClientSize));
				}
			}
		}
		protected override void OnClosed(EventArgs e)
		{
			{
				DCSDEV.DCSImageDisplay form;
				for (int i=0; i<m_arrayListImageDisplay.Count; i++)
				{
					form = (DCSDEV.DCSImageDisplay)m_arrayListImageDisplay[i];
					if (form == this)
					{
						m_arrayListImageDisplay.RemoveAt(i);
						break;
					}
				}
			}
			base.OnClosed (e);
		}

		protected override void OnResize(EventArgs e)
		{
            base.OnResize(e);

            if (this.Visible) m_bSaveNewRect = true;
            if (this.m_ImageClass == DCSDatabaseIF.ImageClass.Certificate)
            {
                this.SetCertsScale(m_dCertsScale);
                return;
            }

			if (this.pbDisplayImage.Image != null)
			{
				double dImageRatio = (double)this.pbDisplayImage.Image.Width / (double)pbDisplayImage.Image.Height;
				Size sizeThisClient = this.ClientSize;
				
				double dWindowRatio = (double)sizeThisClient.Width / (double)sizeThisClient.Height;
				if (dWindowRatio < dImageRatio)
					sizeThisClient.Height = DCSMath.IntDivDouble(sizeThisClient.Width, dImageRatio);
				else
					sizeThisClient.Width = DCSMath.IntTimesDouble(sizeThisClient.Height, dImageRatio);
				this.ClientSize = sizeThisClient;
				this.pbDisplayImage.Size = sizeThisClient;
			}
		}

		private void DCSImageDisplay_LocationChanged(object sender, System.EventArgs e)
		{
			if (this.Visible) m_bSaveNewRect = true;
		}

		private void pbDisplayImage_Paint(object sender, System.Windows.Forms.PaintEventArgs e)
		{
			if (this.pbDisplayImage.Image == null)
			{
				System.Drawing.Pen pen = new Pen(System.Drawing.Color.Red, 5.0F);
				e.Graphics.DrawLine(pen, this.pbDisplayImage.Bounds.Location, this.pbDisplayImage.Bounds.Location + this.pbDisplayImage.Bounds.Size);
			}
		}

		private void pbDisplayImage_DoubleClick(object sender, System.EventArgs e)
		{
			m_parent.CycleArchiveImages(this);
		}

		internal DCSDatabaseIF.ImageClass ImageClass
		{
			get
			{
				return m_ImageClass;
			}
			set
			{
				m_ImageClass = value;
				if (m_ImageClass == DCSDatabaseIF.ImageClass.Certificate)
				{
					this.AutoScroll = true;
					this.pbDisplayImage.ContextMenuStrip = this.contextMenuStripCert;
					this.MaximizeBox = true;
				}
				else
				{
					this.pbDisplayImage.ContextMenuStrip = this.contextMenuStripPhoto;
				}
			}
		}
		internal int ImageSubClass
		{
			get
			{
				return m_ImageSubClass;
			}
			set
			{
				m_ImageSubClass = value;
			}
		}
		internal bool IsFirstSubclass
		{
			get
			{
				return m_IsFirstSubclass;
			}
			set
			{
				m_IsFirstSubclass = value;
			}
		}
		internal int ArchiveInstance
		{
			get
			{
				return m_ArchiveInstance;
			}
			set
			{
				m_ArchiveInstance = value;
			}
		}
		internal string ImageID
		{
			get
			{
				return m_ImageID;
			}
			set
			{
				m_ImageID = value;
				bool bEnabled = (this.pbDisplayImage.Image != null);
				this.exportToolStripMenuItem.Enabled = bEnabled;
				this.exportToolStripMenuItem1.Enabled = bEnabled;
				this.printToolStripMenuItem.Enabled = bEnabled;
				this.printToolStripMenuItem1.Enabled = bEnabled;
			}
		}
		internal string ImageTitle
		{
			get
			{
				return m_ImageTitle;
			}
			set
			{
				m_ImageTitle = value;
				this.Text = m_ImageTitle;
			}
		}

		internal double CertsDisplayScale
		{
			get
			{
				return m_dCertsScale;
			}
			set
			{
				this.SetCertsScale(value);
			}
		}

		// assume certificate is scanned at 300 ppi
		// scale of zero is fit width to window
		// scale of -1 is fit image to window preserving aspect ratio
		private void SetCertsScale(double scale)
		{
			m_dCertsScale = scale;
            double dRatio;
			if (this.pbDisplayImage.Image != null)
			{
				if (m_dCertsScale == 0.0)
				{
                    dRatio = ((double)this.ClientSize.Width / (double)this.pbDisplayImage.Image.Width);
                    m_dDisplayScale = dRatio * m_dCertsScanRes / 100.0;
                }
				else if (m_dCertsScale < 0)
				{
					double scaleW = ((double)this.ClientSize.Width / (double)this.pbDisplayImage.Image.Width);
					double scaleH = ((double)this.ClientSize.Height / (double)this.pbDisplayImage.Image.Height);
                    dRatio = Math.Min(scaleW, scaleH);
                    m_dDisplayScale = dRatio * m_dCertsScanRes / 100.0;
                }
				else
				{
					// assume certs image is scanned at m_dCertsScanRes dpi
                    m_dDisplayScale = scale;
                    dRatio = scale * 100.0 / m_dCertsScanRes;
                }
                this.pbDisplayImage.Width = (int)((double)this.pbDisplayImage.Image.Width * dRatio);
                this.pbDisplayImage.Height = (int)((double)this.pbDisplayImage.Image.Height * dRatio);
                this.Text = this.m_ImageTitle + " - scale=" + m_dDisplayScale.ToString("00.000");
			}
		}

		private void zoom50ToolStripMenuItem_Click(object sender, EventArgs e)
		{
            SetCertsScale(m_dDisplayScale * 0.5);
		}

		private void zoom100ToolStripMenuItem_Click(object sender, EventArgs e)
		{
			SetCertsScale(1.00);
		}

		private void zoom200ToolStripMenuItem_Click(object sender, EventArgs e)
		{
            SetCertsScale(m_dDisplayScale * 2.0);
		}

		private void zoomToFitToolStripMenuItem_Click(object sender, EventArgs e)
		{
			SetCertsScale(-1.0);
		}

		private void zoomToWidthToolStripMenuItem_Click(object sender, EventArgs e)
		{
			SetCertsScale(0.0);
		}

		private void closeImageDisplay(object sender, EventArgs e)
		{
			this.Close();
		}

		private void closeAllImageDisplays(object sender, EventArgs e)
		{
			this.m_parent.CloseDisplays();
		}

        private void imageHistoryToolStripMenuItem_Click(object sender, EventArgs e)
        {
            m_parent.CycleArchiveImages(this);
        }

		private void exportImageDisplay(object sender, EventArgs e)
		{
			string strFilename = "";

			System.Windows.Forms.OpenFileDialog openFileDialog1;
			openFileDialog1 = new System.Windows.Forms.OpenFileDialog();
			openFileDialog1.FileName = strFilename;
			//openFileDialog1.InitialDirectory = "";
			openFileDialog1.Filter = "Image Files(*.BMP;*.JPG)|*.BMP;*.JPG";
			openFileDialog1.Title = "Enter - select output file name";
			openFileDialog1.CheckFileExists = false;
			if (openFileDialog1.ShowDialog(this) != DialogResult.OK) return;
			strFilename = openFileDialog1.FileName;

			bool bBadname = false;
			System.Drawing.Imaging.ImageFormat imageType = null;
			if (strFilename == null || strFilename.Length < 5) 
				bBadname = true;
			else if (strFilename.ToUpper().EndsWith(".BMP"))
				imageType = System.Drawing.Imaging.ImageFormat.Bmp;
			else if (strFilename.ToUpper().EndsWith(".JPG"))
				imageType = System.Drawing.Imaging.ImageFormat.Jpeg;
			else
				bBadname = true;

			if (bBadname)
			{
				DCSMsg.Show("The file name must have extension BMP or JPG.");
				return;
			}
			try
			{
				this.pbDisplayImage.Image.Save(strFilename, imageType);
				DCSMsg.Show(String.Format("Image exported to file {0}.", strFilename));
			}
			catch (Exception ex)
			{
				DCSMsg.Show(String.Format("Error exporting image to file {0}.", strFilename), ex);
				return;
			}
		}

		private void printImageDisplay(object sender, EventArgs e)
		{
			printDocument1.PrinterSettings.PrinterName = null;
			printDocument1.PrinterSettings.PrintToFile = true;

            System.Windows.Forms.PrintDialog pdlg = new System.Windows.Forms.PrintDialog();
            pdlg.PrinterSettings = new System.Drawing.Printing.PrinterSettings();
            DialogResult result = pdlg.ShowDialog();
            if (result == DialogResult.Cancel)
                return;
			printDocument1.PrinterSettings = (System.Drawing.Printing.PrinterSettings)pdlg.PrinterSettings.Clone();
			m_rectPrintable = printDocument1.PrinterSettings.DefaultPageSettings.PrintableArea;
			printDocument1.Print();
		}

		private void eh_PrintPage(object sender, System.Drawing.Printing.PrintPageEventArgs ev)
		{
			Font font = new Font("Arial", 12);
			RectangleF rectfText = new RectangleF(100, 100, m_rectPrintable.Width-200, 100);
			Rectangle rectRemainder = new Rectangle(100, 220, (int)m_rectPrintable.Width-200, (int)m_rectPrintable.Height - 320);
			Rectangle rectDraw = DCSMath.GetBiggestInnerRect(this.pbDisplayImage.Image.Size, rectRemainder);
			ev.Graphics.DrawString(this.m_ImageTitle, font, Brushes.Black, rectfText);
			ev.Graphics.DrawImage(this.pbDisplayImage.Image, rectDraw);
			//ev.Graphics.DrawImage(this.pbDisplayImage.Image, rectDst, rectSrc, System.Drawing.GraphicsUnit.Pixel);
			ev.HasMorePages = false;
		}
	}
}
