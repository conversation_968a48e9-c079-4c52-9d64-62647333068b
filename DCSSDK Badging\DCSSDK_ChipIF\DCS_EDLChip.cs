using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;

namespace DCSDEV.DCSChipIF
{
	class DCS_EDLChip
	{
		private string m_strCOM;
		private int m_nBPS;
		private System.IO.Ports.SerialPort m_port;
		private int m_bytesReceived;
		private int m_iStatus;	//Thingmagic status error
		private const uint MSG_CRC_INIT = 0xFFFF;
		private const uint MSG_CCITT_CRC_POLY = 0x1021;

		public enum ErrorCode
		{
			OK = 0,
			ERROR_WRONG_RESPONSE_CODE = 1,
			ERROR_WRONG_LENGTH = 2,
			ERROR_STATUS = 3,
			ERROR_BAD_RESPONSE_FORMAT = 4,
			ERROR_BAD_CRC_CODE = 5,
			ERROR_INIT_DEVICE = 6,
			ERROR_NOT_IMPLEMENTED = 7,
			ERROR_NO_CARD = 8,
			ERROR_OPEN_PORT = 9,
			ERROR_NO_RFID = 10,
			ERROR_IMPROPER_RFID = 11,
			ERROR_TOO_MANY_CARDS = 12,
			ERROR_NO_TID = 13,
			ERROR_CARD_LOCKED = 14,
			ERROR_CANNOT_WRITE_RFID = 15
		};

		public enum OpCode
		{
			OPCODE_WRITE_FLASH = 0x01,
			OPCODE_VERSION = 0x03,
			OPCODE_BOOT_FIRMWARE = 0x04,
			OPCODE_ERASE_FLASH = 0x07,
			OPCODE_READ_TAG_SINGLE = 0x21,
			OPCODE_READ_TAG_MULTIPLE = 0x22,
			OPCODE_WRITE_TAG_EPC = 0x23,
			OPCODE_LOCK_TAG = 0x25,
			OPCODE_CLEAR = 0x2A,
			OPCODE_SET_REGION = 0x97,
			OPCODE_SET_PROTOCOL = 0x93
		};

		public enum TIDApproach
		{
			TIDApproach_Unknown, TIDApproach_E0, TIDApproach_E2_64Bit, TIDApproach_E2_112Bit, TIDApproach_E2_3Bank
		};
		
		public DCS_EDLChip(string strCOMPort, int iSpeed)
		{
			m_strCOM = strCOMPort;
			m_nBPS = iSpeed;

			try
			{
				m_port = new System.IO.Ports.SerialPort(m_strCOM, m_nBPS);
				m_port.ReadTimeout = 4000;
			}
			catch (Exception ex)
			{
				DCSMsg.Show("ERROR in DCSDEV.DCSChipIF.DCS_EDLChip \n\n" + ex.Message);
			}
		}

		// return 0 if OK; else return error code
		public ErrorCode InitDevice()
		{
			ErrorCode eRet;

			try
			{
				if (!m_port.IsOpen) m_port.Open();
			}
			catch (Exception ex)
			{
				DCSMsg.Show("ERROR opening port in DCSDEV.DCSChipIF.DCS_EDLChip.InitDevice \n\n" + ex.Message);
				return ErrorCode.ERROR_OPEN_PORT;
			}
			try
			{
				// flush any junk in the port
				while (m_port.BytesToRead > 0)
				{
					int iJunk = m_port.ReadByte();
				}
				// SDK sample had a sleep 
				System.Threading.Thread.Sleep(30);

				int nBytes;
				byte[] bufSend = { 0, 0 };
				byte[] bufRet = new byte[256];

				// test IO by reading version
				eRet = this.CommandSend(OpCode.OPCODE_VERSION, bufSend, 0);
				if (eRet != ErrorCode.OK) goto Exit_InitDevice;
				eRet = this.CommandReceive(OpCode.OPCODE_VERSION, bufRet, out nBytes);
				if (eRet != ErrorCode.OK) goto Exit_InitDevice;

				// go - 
				eRet = this.CommandSend(OpCode.OPCODE_BOOT_FIRMWARE, bufSend, 0);
				if (eRet != ErrorCode.OK) goto Exit_InitDevice;
				eRet = this.CommandReceive(OpCode.OPCODE_BOOT_FIRMWARE, bufRet, out nBytes);
				// documentation says this should be called but often there is an error received;
				// but ignoring it causes no problem
				// if (eRet != ErrorCode.OK) goto Exit_InitDevice;

				// set region
				bufSend[0] = 0x02;
				eRet = this.CommandSend(OpCode.OPCODE_SET_REGION, bufSend, 1);
				if (eRet != ErrorCode.OK) goto Exit_InitDevice;
				eRet = this.CommandReceive(OpCode.OPCODE_SET_REGION, bufRet, out nBytes);
				if (eRet != ErrorCode.OK) goto Exit_InitDevice;

				// set protocol
				bufSend[0] = 0x00;
				bufSend[1] = 0x05;	// Gen2 protocol
				eRet = this.CommandSend(OpCode.OPCODE_SET_PROTOCOL, bufSend, 2);
				if (eRet != ErrorCode.OK) goto Exit_InitDevice;
				eRet = this.CommandReceive(OpCode.OPCODE_SET_PROTOCOL, bufRet, out nBytes);
				if (eRet != ErrorCode.OK) goto Exit_InitDevice;

				//string str = DCSMath.Buffer2String(bufRet, 0, 20);
				//DCSMsg.Show.Show(str);

			}
			catch (Exception ex)
			{
				DCSMsg.Show("ERROR in DCS_EDLChip.InitDevice \n\n" + ex.Message);
				eRet = ErrorCode.ERROR_INIT_DEVICE;
			}
		Exit_InitDevice:
			// keep port open??
			m_port.Close();
			return eRet;
		}
		public ErrorCode CloseDevice()
		{
			m_port.Close();
			return ErrorCode.OK;
		}

		// very similar to GetChipTID - bufRet is initialized to send codes to read from the RFID memory=1 
		// with different offset=2 and length=6.
		public ErrorCode GetChipRFID(out string strRFID)
		{
			string strRet = "error";
			ErrorCode eRet;
			int nBytes;
			byte[] bufRet = new byte[256];
			// 00 00 04 00 FA 01 08 28 00 00 01(memory) 00 00 00 02(offset)     06(len)
			byte[] bufSend = 
			{ 
				0x00,						// option 
				0x00, 0x04,					// search flags 
				0x03, 0xE8,					// time out 
				0x01, 0x08, 0x28, 0x00,		// password
				0x00,						// select data length 
				0x01,						// memory bank 
				0x00, 0x00, 0x00, 0x02,		// offset 
				0x06						// length 
			};

			// keep port open??
			m_port.Open();

			eRet = this.CommandClear();
			if (eRet != ErrorCode.OK) goto Exit_GetChipRFID;

			eRet = this.CommandSend(OpCode.OPCODE_READ_TAG_MULTIPLE, bufSend, bufSend.Length);
			if (eRet != ErrorCode.OK) goto Exit_GetChipRFID;

			eRet = this.CommandReceive(OpCode.OPCODE_READ_TAG_MULTIPLE, bufRet, out nBytes);
			if (eRet != ErrorCode.OK) goto Exit_GetChipRFID;

			// this call has 10 bytes extra returned.  There are 10 bytes even if there is no card
			if (nBytes <= 10) { eRet = ErrorCode.ERROR_NO_CARD; goto Exit_GetChipRFID;}

			strRet = DCSMath.Buffer2String(bufRet, 10, nBytes - 10);
			//DCSMsg.Show.Show("ChipID=" + str);
			eRet = ErrorCode.OK;

		Exit_GetChipRFID:
			// keep port open??
			m_port.Close();
			strRFID = strRet;
			return eRet;
		}

		// return OK if one card is present.  Otherwise return an error code.
		// countOut is set to number of cards.  Set countOut to zero if there is a device error
		public ErrorCode CheckChipCount(out uint countOut)
		{
			uint count = 0;
			ErrorCode eRet;
			int nBytes;
			byte[] bufRet = new byte[256];
			byte[] bufSend = 
			{ 
				0x00, 0x00, 0x00, 0xFA,
			};

			// keep port open??
			m_port.Open();

			eRet = this.CommandClear();
			if (eRet != ErrorCode.OK) goto Exit_GetCount;

			eRet = this.CommandSend(OpCode.OPCODE_READ_TAG_MULTIPLE, bufSend, bufSend.Length);
			if (eRet != ErrorCode.OK) goto Exit_GetCount;

			eRet = this.CommandReceive(OpCode.OPCODE_READ_TAG_MULTIPLE, bufRet, out nBytes);
			if (eRet != ErrorCode.OK) goto Exit_GetCount;

			count = bufRet[0];
			if (count > 1) eRet = ErrorCode.ERROR_TOO_MANY_CARDS;

		Exit_GetCount:
			// keep port open??
			m_port.Close();
			countOut = count;
			return eRet;
		}

		public ErrorCode LockChip()
		{
			ErrorCode eRet;
			int nBytes;
			byte[] bufRet = new byte[256];
			// 00 00 04 00 FA 01 08 28 00 00 02(mem) 00 00 00 01(offset) 04(len)
			//byte[] bufSend = 
			//{ 
			//	0x03, 0xE8,					// time out;  0x03e8 = 1000 
			//	0x01,						// option 
			//	0x01, 0x08, 0x28, 0x00,		// password
			//	0x00, 0x10,					// mask bits 
			//	0x00, 0x10					// action bits - perm lock EPC memory 
			//};
			byte[] bufSend = 
			{ 
				0x03, 0xE8,					// time out;  0x03e8 = 1000 
				0x01,						// option 
				0x00, 0x00, 0x00, 0x00,		// password
				0x00, 0x30,					// mask bits 
				0x00, 0x30,					// action bits - perm lock EPC memory 
				0x00
			};
			eRet = this.CommandSend(OpCode.OPCODE_LOCK_TAG, bufSend, bufSend.Length);
			if (eRet != ErrorCode.OK) return eRet;

			eRet = this.CommandReceive(OpCode.OPCODE_LOCK_TAG, bufRet, out nBytes);
			if (eRet == ErrorCode.ERROR_STATUS && m_iStatus == 0x424) return ErrorCode.ERROR_CARD_LOCKED;
			return eRet;
		}

		// very similar to GetChipRFID - bufRet is initialized to send codes to read form the TID memory=2 
		// with different offset=1 and length=4.
		public ErrorCode GetChipTID(out string strTID)
		{
			TIDApproach approach = TIDApproach.TIDApproach_Unknown;
			string strRet = "error";
			ErrorCode eRet;
			int nBytes;
			byte[] bufRet = new byte[256];

			// reads 7 words (112 bits)  of Memory Bank 2 = TID
			byte[] bufSend112 = 
			{ 
				0x00,						// option 
				0x00, 0x04,					// search flags 
				0x03, 0xE8,					// time out;  0x03e8 = 1000 
				0x01, 0x08, 0x28, 0x00,		// password
				0x00,						// select data length 
				0x02,						// memory bank 
				0x00, 0x00, 0x00, 0x00,		// offset 
				0x07						// length 
			};
			// reads 4 words (or 64 bits) of Memory Bank 2 = TID
			byte[] bufSend64 = 
			{ 
				0x00,						// option 
				0x00, 0x04,					// search flags 
				0x03, 0xE8,					// time out;  0x03e8 = 1000 
				0x01, 0x08, 0x28, 0x00,		// password
				0x00,						// select data length 
				0x02,						// memory bank 
				0x00, 0x00, 0x00, 0x00,		// offset 
				0x04						// length 
			};
			// reads the first 2 16bit words of Memory Bank 2=TID
			byte[] bufSend2 = 
			{ 
				0x00,						// option 
				0x00, 0x04,					// search flags 
				0x03, 0xE8,					// time out;  0x03e8 = 1000 
				0x01, 0x08, 0x28, 0x00,		// password
				0x00,						// select data length 
				0x02,						// memory bank 
				0x00, 0x00, 0x00, 0x00,		// offset 
				0x02						// length 
			};
			// reads 4 words of User Memory Bank 3
			byte[] bufSend3 = 
			{ 
				0x00,						// option 
				0x00, 0x04,					// search flags 
				0x03, 0xE8,					// time out;  0x03e8 = 1000 
				0x01, 0x08, 0x28, 0x00,		// password
				0x00,						// select data length 
				0x03,						// memory bank 
				0x00, 0x00, 0x00, 0x00,		// offset 
				0x04						// length 
			};

			// keep port open??
			m_port.Open();
			
			// read first two words of TID (Bank 2) - determine TID Approach
			eRet = this.CommandClear();
			if (eRet != ErrorCode.OK) goto Err_GetChipTID;
			eRet = this.CommandSend(OpCode.OPCODE_READ_TAG_MULTIPLE, bufSend2, bufSend2.Length);
			if (eRet != ErrorCode.OK) goto Err_GetChipTID;
			eRet = this.CommandReceive(OpCode.OPCODE_READ_TAG_MULTIPLE, bufRet, out nBytes);
			if (eRet != ErrorCode.OK) goto Err_GetChipTID;

			// this call has 10 bytes extra returned.  There are 10 bytes even if there is no card
			// but we already verified that there is one card present.
			if (nBytes < 14) { eRet = ErrorCode.ERROR_WRONG_LENGTH; goto Err_GetChipTID; }
			// if (nBytes == 10) { eRet = ErrorCode.ERROR_NO_TID; goto Err_GetChipTID; }

			strRet = DCSMath.Buffer2String(bufRet, 10, nBytes - 10);
			if (strRet.Substring(0, 7) == "E200106" || strRet.Substring(0, 7) == "E200108")
				approach = TIDApproach.TIDApproach_E2_3Bank;
			else if (strRet.Substring(0, 2) == "E2")
				approach = TIDApproach.TIDApproach_E2_112Bit;		// or TIDApproach.TIDApproach_E2_64Bit	
			else
			{
				eRet = ErrorCode.ERROR_NO_TID;
				goto Err_GetChipTID;
			}

			// if 112 Bit TID read the rest of TID memory bank 2 - see if it is actually 64 bit.
			// if 3Bank TID read user memory bank 1.
			if (approach == TIDApproach.TIDApproach_E2_112Bit)
			{
				eRet = this.CommandClear();
				if (eRet != ErrorCode.OK) goto Err_GetChipTID;
				eRet = this.CommandSend(OpCode.OPCODE_READ_TAG_MULTIPLE, bufSend112, bufSend112.Length);
				if (eRet != ErrorCode.OK) goto Err_GetChipTID;
				eRet = this.CommandReceive(OpCode.OPCODE_READ_TAG_MULTIPLE, bufRet, out nBytes);
				if (eRet != ErrorCode.OK) goto Err_GetChipTID;

				if (nBytes == 10)
				{
					// try 64 bit TID
					approach = TIDApproach.TIDApproach_E2_64Bit;
					if (eRet != ErrorCode.OK) goto Err_GetChipTID;
					eRet = this.CommandSend(OpCode.OPCODE_READ_TAG_MULTIPLE, bufSend64, bufSend64.Length);
					if (eRet != ErrorCode.OK) goto Err_GetChipTID;
					eRet = this.CommandReceive(OpCode.OPCODE_READ_TAG_MULTIPLE, bufRet, out nBytes);
					if (eRet != ErrorCode.OK) goto Err_GetChipTID;
					if (nBytes < 18) { eRet = ErrorCode.ERROR_WRONG_LENGTH; goto Err_GetChipTID; }
					strRet = DCSMath.Buffer2String(bufRet, 10, nBytes - 10);
				}
				else
				{
					if (nBytes < 24) { eRet = ErrorCode.ERROR_WRONG_LENGTH; goto Err_GetChipTID; }
					strRet = DCSMath.Buffer2String(bufRet, 10, nBytes - 10);
				}
			}
			else if (approach == TIDApproach.TIDApproach_E2_3Bank)
			{
				eRet = this.CommandClear();
				if (eRet != ErrorCode.OK) goto Err_GetChipTID;
				eRet = this.CommandSend(OpCode.OPCODE_READ_TAG_MULTIPLE, bufSend3, bufSend3.Length);
				if (eRet != ErrorCode.OK) goto Err_GetChipTID;
				eRet = this.CommandReceive(OpCode.OPCODE_READ_TAG_MULTIPLE, bufRet, out nBytes);
				if (eRet != ErrorCode.OK) goto Err_GetChipTID;

				if (nBytes < 18) { eRet = ErrorCode.ERROR_WRONG_LENGTH; goto Err_GetChipTID; }
				strRet = strRet + DCSMath.Buffer2String(bufRet, 10, nBytes - 10);
			}
			else
			{
				eRet = ErrorCode.ERROR_NO_TID;
				goto Err_GetChipTID;
			}

			//DCSMsg.Show.Show("ChipID=" + str);
			eRet = ErrorCode.OK;

		Exit_GetChipTID:
			// keep port open??
			m_port.Close();
			strTID = strRet;
			return eRet;

		Err_GetChipTID:
			strRet = "error";
			goto Exit_GetChipTID;
		}

		// Pulls ChipFRID field from input text file and writes it to the EDL (or ETC) chip
		// return 0 if OK; else return error code
		public ErrorCode File2Chip(string strFilename, bool bLockChipAfterWrite)
		{
			string strRFID = "";
			System.IO.StreamReader sr = null;
			string line;
			try
			{
				sr = new System.IO.StreamReader(strFilename);

				while ((line = sr.ReadLine()) != null)
				{
					if (line.ToUpper().StartsWith("CHIPRFID ") || line.ToUpper().StartsWith("[CHIPRFID] "))
					{
						strRFID = line.Substring(9);
						break;
					}
				}
			}
			catch
			{
				return ErrorCode.ERROR_NO_RFID;
			}
			finally
			{
				if (sr != null) sr.Close();
			}
			if (strRFID == "") return ErrorCode.ERROR_NO_RFID;
			string strWork = strRFID.ToUpper().Trim();
			if (strWork.Length > 24 || (strWork.Length)%2 != 0) return ErrorCode.ERROR_IMPROPER_RFID;

			byte[] bufSend = 
			{ 
				0x03, 0xE8,					// time out;  0x03e8 = 1000 
				0x00, 0x00,					// reserved  
				0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00		// new RFID tag
			};

			// put RFID string into bufSend
			int i = 0;
			int num;
			const string Hexes = "0123456789ABCDEF";
			while (i < strWork.Length)
			{
				num = Hexes.IndexOf(strWork[i]) * 16 + Hexes.IndexOf(strWork[i+1]);
				bufSend[4 + i / 2] = (byte)num;
				i += 2;
			}

			ErrorCode eRet;
			int nBytes;
			byte[] bufRet = new byte[256];

			// now write the RFID to the chip

			// keep port open??
			m_port.Open();
			
			eRet = this.CommandSend(OpCode.OPCODE_WRITE_TAG_EPC, bufSend, bufSend.Length);
			if (eRet != ErrorCode.OK) goto Exit_File2Chip;

			eRet = this.CommandReceive(OpCode.OPCODE_WRITE_TAG_EPC, bufRet, out nBytes);
			if (eRet == ErrorCode.ERROR_STATUS && m_iStatus == 0x406) eRet = ErrorCode.ERROR_CANNOT_WRITE_RFID;
			if (eRet != ErrorCode.OK) goto Exit_File2Chip;

			if (bLockChipAfterWrite)
			{
				eRet = LockChip();
				if (eRet != ErrorCode.OK) goto Exit_File2Chip;
			}
		Exit_File2Chip:
			// keep port open??
			m_port.Close();
			return eRet;
		}

		// return 0 if OK; else return error code
		public ErrorCode Chip2File(string strFilename)
		{
			ErrorCode eRet;
			
			string strTID = "none";
			eRet = this.GetChipTID(out strTID);
			if (eRet != ErrorCode.OK) return eRet;

			string strRFID = "none";
			eRet = this.GetChipRFID(out strRFID);
			if (eRet != ErrorCode.OK) return eRet;

			// write the data to file
			using (System.IO.StreamWriter stream = new System.IO.StreamWriter(strFilename))
			{
				stream.Write("ChipTID " + strTID + "\r\n");
				stream.Write("ChipRFID " + strRFID + "\r\n");
				stream.Close();
			}
			return ErrorCode.OK;
		}
		public string GetErrormessage(ErrorCode eError)
		{
			string str = "";
			if ((int)eError > 0) 
				str = eError.ToString();
			else 
				str = "ERROR " + ((int)eError).ToString();
			if (eError == ErrorCode.ERROR_STATUS)
			{
				if (m_iStatus == 0x0400) str = ErrorCode.ERROR_NO_CARD.ToString();
				else str = str + " - " + String.Format("{0:x4}", m_iStatus);	// xxx
			}
			return str;
		}

		private ErrorCode CommandClear()
		{
			ErrorCode eRet;
			int nBytes;
			byte[] bufSend = { 0 };
			byte[] bufRet = new byte[256];
			eRet = this.CommandSend(OpCode.OPCODE_CLEAR, bufSend, 0);
			if (eRet != ErrorCode.OK) return eRet;

			eRet = this.CommandReceive(OpCode.OPCODE_CLEAR, bufRet, out nBytes);
			if (eRet != ErrorCode.OK) return eRet;
			return ErrorCode.OK;
		}

		private ErrorCode CommandSend(OpCode opcode, byte[] bufData, int nBytes)
		{
			byte[] bufSend = new byte[256];

			bufSend[0] = 0xFF;
			bufSend[1] = (byte)nBytes;
			bufSend[2] = (byte)opcode;
			for (int i = 0; i < nBytes; i++)
				bufSend[i + 3] = bufData[i];
			uint crc = this.CalcCRC(bufSend, nBytes + 3);
			bufSend[nBytes + 3] = (byte)(crc >> 8);
			bufSend[nBytes + 4] = (byte)(crc & 0xFF);

			m_port.Write(bufSend, 0, nBytes + 5);
			return ErrorCode.OK;
		}
		private ErrorCode CommandReceive(OpCode opcode, byte[] bufData, out int nBytes)
		{
			nBytes = 0;
			m_bytesReceived = 0;
			byte[] bufReceive = new byte[256];
			while (true)
			{
				int len = m_port.Read(bufReceive, m_bytesReceived, bufReceive.Length - m_bytesReceived);
				if (bufReceive[0] != 0xff && m_bytesReceived == 0)
					continue;
				m_bytesReceived += len;
				if (m_bytesReceived >= 2)
				{
					if (m_bytesReceived >= bufReceive[1] + 7)
					{
						break;
					}
				}
			}

			
			int crc;
			if (bufReceive[0] != 0xFF) return ErrorCode.ERROR_BAD_RESPONSE_FORMAT;
			if (bufReceive[2] != (int)opcode) return ErrorCode.ERROR_WRONG_RESPONSE_CODE;
			if (bufReceive[1] != m_bytesReceived - 7) return ErrorCode.ERROR_WRONG_LENGTH;
			m_iStatus = (bufReceive[3] << 8) + bufReceive[4];
			if (m_iStatus != 0) return ErrorCode.ERROR_STATUS;
			for (int i = 5; i < m_bytesReceived - 2; i++)
				bufData[i - 5] = bufReceive[i];
			nBytes = m_bytesReceived - 7;
			crc = (bufReceive[m_bytesReceived - 2] << 8) + bufReceive[m_bytesReceived - 1];
			//syh			if (crc != this.CalcCRC(bufReceive, m_bytesReceived - 2)) return ErrorCode.ERROR_BAD_CRC_CODE;
			return ErrorCode.OK;
		}

		private uint CalcCRC(byte[] buffer, int nBytes)
		{
			uint crcReg = MSG_CRC_INIT;
			// skip the first 0xFF byte
			for (int i = 1; i < nBytes; i++)
				CRC_calcCrc8(ref crcReg, MSG_CCITT_CRC_POLY, buffer[i]);
			return crcReg;
		}

		// void CRC_calcCrc8(u16 *crcReg, u16 poly, u16 u8Data)
		private void CRC_calcCrc8(ref uint crcReg, uint poly, uint u8Data)
		{
			int i;
			uint xorFlag;
			uint bit;
			uint dcdBitMask = 0x80;

			for (i = 0; i < 8; i++)
			{
				// Get the carry bit.  This determines if the polynomial should be xor'd
				//	with the CRC register.
				xorFlag = crcReg & 0x8000;

				// Shift the bits over by one.
				crcReg <<= 1;

				// Shift in the next bit in the data byte
				bit = ((u8Data & dcdBitMask) == dcdBitMask) ? (uint)1 : 0;
				crcReg |= bit;

				// XOR the polynomial
				if (xorFlag != 0)
				{
					crcReg = crcReg ^ poly;
				}

				// Shift over the dcd mask
				dcdBitMask >>= 1;
			}
		}
	}
}
