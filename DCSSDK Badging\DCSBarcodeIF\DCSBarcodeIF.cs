using System;
using System.Drawing;
using System.Collections;
using System.Windows.Forms;

using CommStudio.Barcodes;

namespace DCSSDK.DCSBarcodeIF
{
	/// <summary>
	/// Summary description for Class1.
	/// </summary>
	public class SaxIF
	{
		// return null string if no error. Other return error message.
		private string TestRipPDF(string strToRip, CommStudio.Barcodes.Pdf417 pdf, int rows, int cols, int nMW, int nMH, Graphics g, Bitmap bm)
		{
			pdf.Rows = rows;
			pdf.Columns = cols;
			pdf.ModuleWidth = nMW;		// SYH: seems we need to set feature size after columns and rows
			pdf.ModuleHeight = nMH;
			g.Clear(Color.White);
			try 
			{ 
				pdf.Render(strToRip, g, new Rectangle(Point.Empty, bm.Size)); 
			}
			catch (Exception ex)
			{
				return ex.Message;
			}
			if (pdf.Bounds.Width > bm.Width)        return "Width is too narrow";
			else if (pdf.Bounds.Height > bm.Height) return "Height is too short";
			return null;
		}

        public Bitmap RipPDF417(string strToRip, Size sizePrintTarget, double dPrinterScale, int nMW, int nMH)
		{
			CommStudio.Barcodes.Pdf417 pdf = new CommStudio.Barcodes.Pdf417();
			pdf.Layout = CommStudio.Barcodes.Pdf417.SymbolLayout.Manual;
			pdf.ErrorCorrectionLevel = 4;

			int nRows, nColumns, nRowsMax, nColumnsMax;
			nRowsMax = sizePrintTarget.Height / nMH;
			nColumnsMax = (sizePrintTarget.Width - 69*nMW) / (17*nMW);
			if (nColumnsMax <= 0) nColumnsMax = 1;
			if (nRowsMax > 90) nRowsMax = 90;
			if (nColumnsMax > 30) nColumnsMax = 30;
			int nColumnsMin = 3;
			int nRowsMin = 12;
			//iterator seems the product must be more than 30!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
			if (nRowsMax < nRowsMin) nRowsMax = nRowsMin;
			if (nColumnsMax < nColumnsMin) nColumnsMax = nColumnsMin;

			Bitmap bm = new Bitmap(sizePrintTarget.Width, sizePrintTarget.Height);
			Graphics g = Graphics.FromImage(bm);

			// compute PDF417 rows and columns - using best shape to fill available area
			double K, N, A, H;
			N = strToRip.Length + 36;	// length of string to encode
			N = N * .80;	// apply compression factor for text
			//if (N < 36) N = 36;
			K = (double)sizePrintTarget.Width / (double)sizePrintTarget.Height;
			A = 17 * nMW;	// size of a column in pixels
			H = nMH;		// size of row in pixels
			// nrow = y / H; ncol = x / A; nrow * ncol >= N
			// y = x / K                      x = y * K
			// (x * y) / (H * A) >= N;
			// (x * x) >= N * H * A * K)     (y * y) * (K / (H * A)) >= N 
			// x >= sqrt(N * H * A * K)       y >= sqrt(N * H * A / K)
			// 
			//double x0 = (double)sizePrintTarget.Width - 4 * A;	// width available less 4 required columns
			double y0 = (double)sizePrintTarget.Height;
			double x1 = Math.Sqrt(N * H * K * A);
			y0 = x1 / K;

			nRows = (int)Math.Round(y0 / H);
			nColumns = (int)Math.Round(x1 / A);

			// reduce rows and columns to be no more the the max allowed
			if (nRows > nRowsMax) nRows = nRowsMax;
			if (nColumns > nColumnsMax) nColumns = nColumnsMax;
			if (nRows < nRowsMin) nRows = nRowsMin;
			if (nColumns < nColumnsMin) nColumns = nColumnsMin;

			// see if the barcode really fits
			string strRet = TestRipPDF(strToRip, pdf, nRows, nColumns, nMW, nMH, g, bm);
			if (strRet != null)
			{
				// try without applying compression factor
				N = strToRip.Length + 36;	// length of string to encode
				x1 = Math.Sqrt(N * H * K * A);
				y0 = x1 / K;

				nRows = (int)Math.Round(y0 / H);
				nColumns = (int)Math.Round(x1 / A);

				// reduce rows and columns to be no more the the max allowed
				if (nRows > nRowsMax) nRows = nRowsMax;
				if (nColumns > nColumnsMax) nColumns = nColumnsMax;

				if (nRows < nRowsMin) nRows = nRowsMin;
				if (nColumns < nColumnsMin) nColumns = nColumnsMin;

				strRet = TestRipPDF(strToRip, pdf, nRows, nColumns, nMW, nMH, g, bm);
				if (strRet != null)
				{
					MessageBox.Show(String.Format("Error {0} \n\nColumns x Rows = {1} x {2}+4 \nModule width x height = {3} x {4} \nstring length = {5}", strRet, nColumns, nRows, nMW, nMH, strToRip.Length));
					// draw barcode in automatic mode so something is shown
					try
					{
						pdf.Layout = CommStudio.Barcodes.Pdf417.SymbolLayout.Auto;
						pdf.Render(strToRip, g, new Rectangle(Point.Empty, bm.Size));
					}
					catch { ; }

					System.Drawing.Pen pen = new Pen(System.Drawing.Color.Red, 5.0F);
					g.DrawLine(pen, 0, 0, bm.Width, bm.Height);
					g.DrawLine(pen, 0, bm.Height, bm.Width, 0);
					if (pdf.Bounds.Width > bm.Width)
					{
						g.DrawLine(pen, 0, bm.Height / 2, bm.Width, bm.Height / 2);
					}
					if (pdf.Bounds.Height > bm.Height)
					{
						g.DrawLine(pen, bm.Width / 2, 0, bm.Width / 2, bm.Height);
					}
				}
			}
			return bm;
		}

		// Justifications {LEFT, CENTER, RIGHT, WRAP, NONE};
		public bool Rip1DBarcode(string strToRip, Graphics gr, Rectangle rectObjectAdjustedScaled, RotateFlipType RotateFlip, int Justification, short BarcodeCode, bool BarcodeShowText, Color BackColor, Color ForeColor, Font Font, double dScale, ref string strError)
		{
            bool bOK = true;
			//Barcode may throw ArgumentExceptions if there are invalid characters
			CommStudio.Barcodes.Barcode bc = null;
			Font fontCaption = null;
			try
			{
				DCSSDK.DCSBarcodeIF.DCSBarcodeTypesList tl = new DCSSDK.DCSBarcodeIF.DCSBarcodeTypesList();
				//bc.Style = tl.GetCodeType(designObject.BarcodeCode).BarcodeStyle;
				bc = new CommStudio.Barcodes.Barcode(tl.GetCodeType(BarcodeCode).BarcodeStyle);
				bc.DisplayCaption = BarcodeShowText;
				bc.BackColor = BackColor;
				bc.ForeColor = ForeColor;

				switch(RotateFlip)
				{
					default:
					case System.Drawing.RotateFlipType.RotateNoneFlipNone:
						bc.Orientation = CommStudio.Barcodes.BarcodeOrientation.Right;
						break;
					case System.Drawing.RotateFlipType.Rotate90FlipNone:
						bc.Orientation = CommStudio.Barcodes.BarcodeOrientation.Down;
						break;
					case System.Drawing.RotateFlipType.Rotate180FlipNone:
						bc.Orientation = CommStudio.Barcodes.BarcodeOrientation.Left;
						break;
					case System.Drawing.RotateFlipType.Rotate270FlipNone:
						bc.Orientation = CommStudio.Barcodes.BarcodeOrientation.Up;
						break;
				}
				switch(Justification)
				{
					case 0:	//DCSDEV.DCSDatatypes.Justifications.LEFT:
						bc.Alignment = CommStudio.Barcodes.BarcodeAlignment.Near;
						break;
					default:
                    case 1:	//DCSDEV.DCSDatatypes.Justifications.CENTER:
						bc.Alignment = CommStudio.Barcodes.BarcodeAlignment.Center;
						break;
                    case 2:	//DCSDEV.DCSDatatypes.Justifications.RIGHT:
						bc.Alignment = CommStudio.Barcodes.BarcodeAlignment.Far;
						break;
				}
				bc.LayoutMode = CommStudio.Barcodes.BarcodeLayoutMode.Auto;
				fontCaption = null;
				if (BarcodeShowText)
				{
					fontCaption = new System.Drawing.Font(Font.FontFamily, Font.Size*(float)dScale, Font.Style);
					bc.CaptionFont = fontCaption;
				}
				//Render the bar code
				bc.Render(strToRip, gr, rectObjectAdjustedScaled);
				if (bc.Bounds.Width == 0 || bc.Bounds.Height == 0)
				{
                    bOK = false;
                    strError = "Error drawing barcode. Make width bigger.";
                }
			}
			catch (Exception ex)
			{
                bOK = false;
                strError = "Error drawing barcode. " + ex.Message;
			}
			finally
			{
                if (!bOK) gr.FillRectangle(new SolidBrush(Color.LightGray), rectObjectAdjustedScaled);

				if (fontCaption != null)
				{
					fontCaption.Dispose();
					fontCaption = null;
				}
				if (bc != null)
				{
					bc.Dispose();
					bc = null;
				}
			}
        return bOK;
		}
	}

	internal class DCSGlobals
	{
		internal static string Msg(string str)
		{
			return str;
		}
	}
	public class DCSBarcodeType
	{
		short prop_BarcodeCode=-1;
		string prop_BarcodeName="";
		string prop_BarcodeTip="";
		BarcodeStyle prop_BarcodeStyle = BarcodeStyle.Code39;

		public string BarcodeName
		{
			get 
			{
				return prop_BarcodeName;
			}
		}
		public string BarcodeTip
		{
			get 
			{
				return prop_BarcodeTip;
			}
		}
		public BarcodeStyle BarcodeStyle
		{
			get 
			{
				return prop_BarcodeStyle;
			}
		}

		/// <summary>
		/// Represents the internal DCS barcode for a certain barcode style.
		/// So we stay barcode component vendor-independent. This is what we store in the DB
		/// </summary>
		public short BarcodeCode
		{
			get 
			{
				return prop_BarcodeCode;
			}
		}

		
		public DCSBarcodeType(short code, string name, string tip, CommStudio.Barcodes.BarcodeStyle style)
		{
			prop_BarcodeCode = code;
			prop_BarcodeName = name;
			prop_BarcodeTip = tip;
			prop_BarcodeStyle = style;
		}
		public override string ToString()
		{
			return this.BarcodeName;
		}
	}
	public class DCSBarcodeTypesList
	{
		public ArrayList List;
		public DCSBarcodeTypesList()
		{
			List = new ArrayList();

			List.Add(new DCSBarcodeType(
				1,
				"MSI/Plessey",
				DCSGlobals.Msg("BCH-001"),
				CommStudio.Barcodes.BarcodeStyle.MsiPlessey
				));

			List.Add(new DCSBarcodeType(
				2,
				"Interleaved 2 of 5",
				DCSGlobals.Msg("BCH-002"),
				CommStudio.Barcodes.BarcodeStyle.Interleaved2of5
				));

			List.Add(new DCSBarcodeType(
				3,
				"Interleaved 2 of 5 w/CD",
				DCSGlobals.Msg("BCH-003"),
				CommStudio.Barcodes.BarcodeStyle.Interleaved2of5Chk
				));

			List.Add(new DCSBarcodeType(
				4,
				"Codabar",
				DCSGlobals.Msg("BCH-004"),
				CommStudio.Barcodes.BarcodeStyle.Codabar
				));

			List.Add(new DCSBarcodeType(
				5,
				"Code 39",
				DCSGlobals.Msg("BCH-005"),
				CommStudio.Barcodes.BarcodeStyle.Code39
				));

			List.Add(new DCSBarcodeType(
				6,
				"Code 39 w/CD",
				DCSGlobals.Msg("BCH-006"),
				CommStudio.Barcodes.BarcodeStyle.Code39Chk
				));

			List.Add(new DCSBarcodeType(
				7,
				"Code 39 Extended",
				DCSGlobals.Msg("BCH-007"),
				CommStudio.Barcodes.BarcodeStyle.Code39Ext
				));

			List.Add(new DCSBarcodeType(
				8,
				"Code 39 Extended w/CD",
				DCSGlobals.Msg("BCH-008"),
				CommStudio.Barcodes.BarcodeStyle.Code39ExtChk
				));

			List.Add(new DCSBarcodeType(
				9,
				"Code 93",
				DCSGlobals.Msg("BCH-009"),
				CommStudio.Barcodes.BarcodeStyle.Code93
				));

			List.Add(new DCSBarcodeType(
				10,
				"Extended Code 93",
				DCSGlobals.Msg("BCH-010"),
				CommStudio.Barcodes.BarcodeStyle.Code93Ext
				));
			List.Add(new DCSBarcodeType(
				11,
				"Code 128-A",
				DCSGlobals.Msg("BCH-011"),
				CommStudio.Barcodes.BarcodeStyle.Code128A
				));
			List.Add(new DCSBarcodeType(
				12,
				"Code 128-B",
				DCSGlobals.Msg("BCH-012"),
				CommStudio.Barcodes.BarcodeStyle.Code128B
				));
			List.Add(new DCSBarcodeType(
				13,
				"Code 128-C",
				DCSGlobals.Msg("BCH-013"),
				CommStudio.Barcodes.BarcodeStyle.Code128C
				));
			List.Add(new DCSBarcodeType(
				14,
				"UPC A",
				DCSGlobals.Msg("BCH-014"),
				CommStudio.Barcodes.BarcodeStyle.UpcA
				));
			List.Add(new DCSBarcodeType(
				15,
				"UPC E",
				DCSGlobals.Msg("BCH-015"),
				CommStudio.Barcodes.BarcodeStyle.UpcE
				));

			List.Add(new DCSBarcodeType(
				16,
				"EAN 13",
				DCSGlobals.Msg("BCH-016"),
				CommStudio.Barcodes.BarcodeStyle.Ean13
				));

			List.Add(new DCSBarcodeType(
				17,
				"EAN 8",
				DCSGlobals.Msg("BCH-017"),
				CommStudio.Barcodes.BarcodeStyle.Ean8
				));

			List.Add(new DCSBarcodeType(
				18,
				"Postnet",
				DCSGlobals.Msg("BCH-018"),
				CommStudio.Barcodes.BarcodeStyle.Postnet
				));
		}
		public short FindPos(CommStudio.Barcodes.BarcodeStyle style)
		{
			short i = -1, j=0;
			foreach(DCSBarcodeType bt in List) 
			{
				if (bt.BarcodeStyle == style) 
				{
					i = j;
				}
				j++;
			}
			return i;
		}
		public short FindPos(short code)
		{
			short i = -1, j=0;
			foreach(DCSBarcodeType bt in List) 
			{
				if (bt.BarcodeCode == code) 
				{
					i = j;
					break;
				}
				j++;
			}
			return i;
		}

		public DCSBarcodeType GetCodeType(CommStudio.Barcodes.BarcodeStyle style)
		{
			int p = FindPos(style);
			if (p > 0) return (DCSBarcodeType)this.List[p];
			return null;
		}
		public DCSBarcodeType GetCodeType(short code)
		{
			int p = FindPos(code);
			if (p >= 0) return (DCSBarcodeType)this.List[p];
			return null;
		}
		public int GetCodeIndex(short code)
		{
			int p = FindPos(code);
			if (p >= 0) return p;
			return 0;
		}
	}
}
