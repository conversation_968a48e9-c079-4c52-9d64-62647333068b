using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;
using System.Data;

using DCSSDK;
using DCSSDK.CaptureMgt;

namespace TestApp_Badging
{
	/// <summary>
	/// Summary description for Form1.
	/// </summary>
	public class formTestBadging : System.Windows.Forms.Form
	{
		private DCSSDK.CaptureMgt.DCSSDK_CaptureMgt m_dlgCaptureMgt;
		private DCSSDK.BadgingMgt.DCSSDK_BadgingMgt m_dlgBadgingMgt;

		private System.Windows.Forms.GroupBox groupBoxTestControls;
		private System.Windows.Forms.ComboBox cbBadgeDat;
		private System.Windows.Forms.Label label6;
		private System.Windows.Forms.Button buttonClose;
		private System.Windows.Forms.TextBox tbImageID;
		private System.Windows.Forms.Button buttonDisplay;
		private System.Windows.Forms.Label label4;
		private System.Windows.Forms.Button buttonPreview;
		private System.Windows.Forms.Button buttonPrint;
		private System.Windows.Forms.Button buttonLayout;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.ComboBox cbImageClass;
		private System.Windows.Forms.Button buttonAllCapture;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.Button buttonSetupBadges;
		private System.Windows.Forms.Button buttonSetupCapture;
		private System.Windows.Forms.Button buttonExit;
		private System.Windows.Forms.Button buttonStartDCSPrinter;
		private System.Windows.Forms.Button buttonEditTxt;
		private System.Windows.Forms.TextBox tbImageTitle;
		private System.Windows.Forms.Label label7;
        private ComboBox comboBoxLanguage;
        private Label label8;
        private Button buttonEncodeChip;
        private Button buttonScan2DBarcode;
        private Button buttonScanChip;
        private TextBox textBoxChipEncodeFile;
        private Label label10;
        private TextBox textBoxChipScanFile;
        private Button buttonDelete;
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		public formTestBadging()
		{
			//
			// Required for Windows Form Designer support
			//
            InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			m_dlgCaptureMgt = new DCSSDK.CaptureMgt.DCSSDK_CaptureMgt();
			m_dlgBadgingMgt = new DCSSDK.BadgingMgt.DCSSDK_BadgingMgt();

            // The following 2 lines will open the DDE server window and enable communication with
            // an MS Access DDE application.  It requires a reference to IDServicesDDE. 
            //
            // DCSDEV.DDEServer.DDEServer server = new DCSDEV.DDEServer.DDEServer();
            // server.ShowDialog();
        }

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if (components != null) 
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(formTestBadging));
            this.groupBoxTestControls = new System.Windows.Forms.GroupBox();
            this.buttonDelete = new System.Windows.Forms.Button();
            this.textBoxChipScanFile = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.textBoxChipEncodeFile = new System.Windows.Forms.TextBox();
            this.buttonScanChip = new System.Windows.Forms.Button();
            this.buttonScan2DBarcode = new System.Windows.Forms.Button();
            this.buttonEncodeChip = new System.Windows.Forms.Button();
            this.tbImageTitle = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.buttonEditTxt = new System.Windows.Forms.Button();
            this.buttonStartDCSPrinter = new System.Windows.Forms.Button();
            this.buttonSetupBadges = new System.Windows.Forms.Button();
            this.buttonSetupCapture = new System.Windows.Forms.Button();
            this.cbBadgeDat = new System.Windows.Forms.ComboBox();
            this.label6 = new System.Windows.Forms.Label();
            this.buttonClose = new System.Windows.Forms.Button();
            this.tbImageID = new System.Windows.Forms.TextBox();
            this.buttonDisplay = new System.Windows.Forms.Button();
            this.label4 = new System.Windows.Forms.Label();
            this.buttonPreview = new System.Windows.Forms.Button();
            this.buttonPrint = new System.Windows.Forms.Button();
            this.buttonLayout = new System.Windows.Forms.Button();
            this.label3 = new System.Windows.Forms.Label();
            this.cbImageClass = new System.Windows.Forms.ComboBox();
            this.buttonAllCapture = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.comboBoxLanguage = new System.Windows.Forms.ComboBox();
            this.buttonExit = new System.Windows.Forms.Button();
            this.groupBoxTestControls.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBoxTestControls
            // 
            this.groupBoxTestControls.Controls.Add(this.buttonDelete);
            this.groupBoxTestControls.Controls.Add(this.textBoxChipScanFile);
            this.groupBoxTestControls.Controls.Add(this.label10);
            this.groupBoxTestControls.Controls.Add(this.textBoxChipEncodeFile);
            this.groupBoxTestControls.Controls.Add(this.buttonScanChip);
            this.groupBoxTestControls.Controls.Add(this.buttonScan2DBarcode);
            this.groupBoxTestControls.Controls.Add(this.buttonEncodeChip);
            this.groupBoxTestControls.Controls.Add(this.tbImageTitle);
            this.groupBoxTestControls.Controls.Add(this.label7);
            this.groupBoxTestControls.Controls.Add(this.buttonEditTxt);
            this.groupBoxTestControls.Controls.Add(this.buttonStartDCSPrinter);
            this.groupBoxTestControls.Controls.Add(this.buttonSetupBadges);
            this.groupBoxTestControls.Controls.Add(this.buttonSetupCapture);
            this.groupBoxTestControls.Controls.Add(this.cbBadgeDat);
            this.groupBoxTestControls.Controls.Add(this.label6);
            this.groupBoxTestControls.Controls.Add(this.buttonClose);
            this.groupBoxTestControls.Controls.Add(this.tbImageID);
            this.groupBoxTestControls.Controls.Add(this.buttonDisplay);
            this.groupBoxTestControls.Controls.Add(this.label4);
            this.groupBoxTestControls.Controls.Add(this.buttonPreview);
            this.groupBoxTestControls.Controls.Add(this.buttonPrint);
            this.groupBoxTestControls.Controls.Add(this.buttonLayout);
            this.groupBoxTestControls.Controls.Add(this.label3);
            this.groupBoxTestControls.Controls.Add(this.cbImageClass);
            this.groupBoxTestControls.Controls.Add(this.buttonAllCapture);
            this.groupBoxTestControls.Controls.Add(this.label1);
            this.groupBoxTestControls.FlatStyle = System.Windows.Forms.FlatStyle.System;
            resources.ApplyResources(this.groupBoxTestControls, "groupBoxTestControls");
            this.groupBoxTestControls.Name = "groupBoxTestControls";
            this.groupBoxTestControls.TabStop = false;
            // 
            // buttonDelete
            // 
            resources.ApplyResources(this.buttonDelete, "buttonDelete");
            this.buttonDelete.Name = "buttonDelete";
            this.buttonDelete.Click += new System.EventHandler(this.buttonDelete_Click);
            // 
            // textBoxChipScanFile
            // 
            resources.ApplyResources(this.textBoxChipScanFile, "textBoxChipScanFile");
            this.textBoxChipScanFile.Name = "textBoxChipScanFile";
            // 
            // label10
            // 
            resources.ApplyResources(this.label10, "label10");
            this.label10.Name = "label10";
            // 
            // textBoxChipEncodeFile
            // 
            resources.ApplyResources(this.textBoxChipEncodeFile, "textBoxChipEncodeFile");
            this.textBoxChipEncodeFile.Name = "textBoxChipEncodeFile";
            // 
            // buttonScanChip
            // 
            resources.ApplyResources(this.buttonScanChip, "buttonScanChip");
            this.buttonScanChip.Name = "buttonScanChip";
            this.buttonScanChip.Click += new System.EventHandler(this.buttonScanChip_Click);
            // 
            // buttonScan2DBarcode
            // 
            resources.ApplyResources(this.buttonScan2DBarcode, "buttonScan2DBarcode");
            this.buttonScan2DBarcode.Name = "buttonScan2DBarcode";
            this.buttonScan2DBarcode.Click += new System.EventHandler(this.buttonScan2DBarcode_Click);
            // 
            // buttonEncodeChip
            // 
            resources.ApplyResources(this.buttonEncodeChip, "buttonEncodeChip");
            this.buttonEncodeChip.Name = "buttonEncodeChip";
            this.buttonEncodeChip.Click += new System.EventHandler(this.buttonEncodeChip_Click);
            // 
            // tbImageTitle
            // 
            resources.ApplyResources(this.tbImageTitle, "tbImageTitle");
            this.tbImageTitle.Name = "tbImageTitle";
            // 
            // label7
            // 
            resources.ApplyResources(this.label7, "label7");
            this.label7.Name = "label7";
            // 
            // buttonEditTxt
            // 
            resources.ApplyResources(this.buttonEditTxt, "buttonEditTxt");
            this.buttonEditTxt.Name = "buttonEditTxt";
            this.buttonEditTxt.Click += new System.EventHandler(this.buttonEditTxt_Click);
            // 
            // buttonStartDCSPrinter
            // 
            this.buttonStartDCSPrinter.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            resources.ApplyResources(this.buttonStartDCSPrinter, "buttonStartDCSPrinter");
            this.buttonStartDCSPrinter.Name = "buttonStartDCSPrinter";
            this.buttonStartDCSPrinter.Click += new System.EventHandler(this.buttonStartDCSPrinter_Click);
            // 
            // buttonSetupBadges
            // 
            this.buttonSetupBadges.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            resources.ApplyResources(this.buttonSetupBadges, "buttonSetupBadges");
            this.buttonSetupBadges.Name = "buttonSetupBadges";
            this.buttonSetupBadges.Click += new System.EventHandler(this.buttonSetupBadges_Click);
            // 
            // buttonSetupCapture
            // 
            this.buttonSetupCapture.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            resources.ApplyResources(this.buttonSetupCapture, "buttonSetupCapture");
            this.buttonSetupCapture.Name = "buttonSetupCapture";
            this.buttonSetupCapture.Click += new System.EventHandler(this.buttonSetupCapture_Click);
            // 
            // cbBadgeDat
            // 
            this.cbBadgeDat.Items.AddRange(new object[] {
            resources.GetString("cbBadgeDat.Items"),
            resources.GetString("cbBadgeDat.Items1"),
            resources.GetString("cbBadgeDat.Items2"),
            resources.GetString("cbBadgeDat.Items3"),
            resources.GetString("cbBadgeDat.Items4")});
            resources.ApplyResources(this.cbBadgeDat, "cbBadgeDat");
            this.cbBadgeDat.Name = "cbBadgeDat";
            // 
            // label6
            // 
            resources.ApplyResources(this.label6, "label6");
            this.label6.Name = "label6";
            // 
            // buttonClose
            // 
            resources.ApplyResources(this.buttonClose, "buttonClose");
            this.buttonClose.Name = "buttonClose";
            this.buttonClose.Click += new System.EventHandler(this.buttonClose_Click);
            // 
            // tbImageID
            // 
            resources.ApplyResources(this.tbImageID, "tbImageID");
            this.tbImageID.Name = "tbImageID";
            // 
            // buttonDisplay
            // 
            resources.ApplyResources(this.buttonDisplay, "buttonDisplay");
            this.buttonDisplay.Name = "buttonDisplay";
            this.buttonDisplay.Click += new System.EventHandler(this.buttonDisplay_Click);
            // 
            // label4
            // 
            resources.ApplyResources(this.label4, "label4");
            this.label4.Name = "label4";
            // 
            // buttonPreview
            // 
            resources.ApplyResources(this.buttonPreview, "buttonPreview");
            this.buttonPreview.Name = "buttonPreview";
            this.buttonPreview.Click += new System.EventHandler(this.buttonPreview_Click);
            // 
            // buttonPrint
            // 
            resources.ApplyResources(this.buttonPrint, "buttonPrint");
            this.buttonPrint.Name = "buttonPrint";
            this.buttonPrint.Click += new System.EventHandler(this.buttonPrint_Click);
            // 
            // buttonLayout
            // 
            resources.ApplyResources(this.buttonLayout, "buttonLayout");
            this.buttonLayout.Name = "buttonLayout";
            this.buttonLayout.Click += new System.EventHandler(this.buttonLayout_Click);
            // 
            // label3
            // 
            resources.ApplyResources(this.label3, "label3");
            this.label3.Name = "label3";
            // 
            // cbImageClass
            // 
            this.cbImageClass.Items.AddRange(new object[] {
            resources.GetString("cbImageClass.Items"),
            resources.GetString("cbImageClass.Items1"),
            resources.GetString("cbImageClass.Items2"),
            resources.GetString("cbImageClass.Items3"),
            resources.GetString("cbImageClass.Items4")});
            resources.ApplyResources(this.cbImageClass, "cbImageClass");
            this.cbImageClass.Name = "cbImageClass";
            // 
            // buttonAllCapture
            // 
            resources.ApplyResources(this.buttonAllCapture, "buttonAllCapture");
            this.buttonAllCapture.Name = "buttonAllCapture";
            this.buttonAllCapture.Click += new System.EventHandler(this.buttonAllCapture_Click);
            // 
            // label1
            // 
            resources.ApplyResources(this.label1, "label1");
            this.label1.Name = "label1";
            // 
            // label8
            // 
            resources.ApplyResources(this.label8, "label8");
            this.label8.Name = "label8";
            // 
            // comboBoxLanguage
            // 
            this.comboBoxLanguage.FormattingEnabled = true;
            this.comboBoxLanguage.Items.AddRange(new object[] {
            resources.GetString("comboBoxLanguage.Items"),
            resources.GetString("comboBoxLanguage.Items1")});
            resources.ApplyResources(this.comboBoxLanguage, "comboBoxLanguage");
            this.comboBoxLanguage.Name = "comboBoxLanguage";
            this.comboBoxLanguage.SelectedIndexChanged += new System.EventHandler(this.comboBoxLanguage_SelectedIndexChanged);
            // 
            // buttonExit
            // 
            resources.ApplyResources(this.buttonExit, "buttonExit");
            this.buttonExit.Name = "buttonExit";
            this.buttonExit.Click += new System.EventHandler(this.buttonExit_Click);
            // 
            // formTestBadging
            // 
            resources.ApplyResources(this, "$this");
            this.Controls.Add(this.groupBoxTestControls);
            this.Controls.Add(this.buttonExit);
            this.Controls.Add(this.comboBoxLanguage);
            this.Controls.Add(this.label8);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.Name = "formTestBadging";
            this.groupBoxTestControls.ResumeLayout(false);
            this.groupBoxTestControls.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

		}
		#endregion

		/// <summary>
		/// The main entry point for the application.
		/// </summary>
		[STAThread]
		static void Main() 
		{
			Application.EnableVisualStyles();
			Application.Run(new formTestBadging());
		}

		private bool DoCapture(string strImageID, string strImageClass, int iSubClass, string strImageTitle)
		{
			// close all image and badge preview displays
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			if (strImageClass == "All")
			{
				m_dlgCaptureMgt.CaptureAllImages(strImageID, strImageTitle, true);
			}
			else 
			{
				if (strImageClass == "Portrait")
				{
					m_dlgCaptureMgt.CapturePortrait(strImageID, strImageTitle, iSubClass, true);
				}
				else if (strImageClass == "Signature")
				{
					m_dlgCaptureMgt.CaptureSignature(strImageID, strImageTitle, iSubClass, true);
				}
				else if (strImageClass == "Fingerprint")
				{
					m_dlgCaptureMgt.CaptureFingerprint(strImageID, strImageTitle, iSubClass, true);
				}
                else if (strImageClass == "TenPrint")
                {
                    m_dlgCaptureMgt.Capture10Print(strImageID, strImageTitle, true);
                }
            }
			return true;
		}

		private bool DoDelete(string strImageID, string strImgClass)
		{
			if (strImgClass == "All")
			{
				m_dlgCaptureMgt.DeleteAllImageClasses(strImageID);
			}
			else 
			{
				if (strImgClass == "Portrait")
				{
					m_dlgCaptureMgt.DeletePortrait(strImageID, -1);
				}
				else if (strImgClass == "Signature")
				{
					m_dlgCaptureMgt.DeleteSignature(strImageID, -1);
				}
				else if (strImgClass == "Fingerprint")
				{
					m_dlgCaptureMgt.DeleteFingerprint(strImageID, -1);
				}
				else if (strImgClass == "Certificate")
				{
					m_dlgCaptureMgt.DeleteCerts(strImageID, -1);
				}
			}
			return true;
		}

		private void DoDisplay(string strImageID, string strDisplayLabel, string strImageClass)
		{
			// get image to display
			if (strImageClass == "All")
			{
				m_dlgCaptureMgt.DisplayAllImageClasses(strImageID, strDisplayLabel);
			}
			else 
			{
				if (strImageClass == "Portrait")
				{
					m_dlgCaptureMgt.DisplayPortrait(strImageID, -1, strDisplayLabel);
				}
				else if (strImageClass == "Signature")
				{
					m_dlgCaptureMgt.DisplaySignature(strImageID, -1, strDisplayLabel);
				}
				else if (strImageClass == "Fingerprint")
				{
					m_dlgCaptureMgt.DisplayFingerprint(strImageID, -1, strDisplayLabel);
				}
			}
		}

		private void DoPreview(string strBadgeDatFileName, string strLabel)
		{
			// close all image and badge preview displays
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			m_dlgBadgingMgt.PreviewBadge(strBadgeDatFileName, strLabel);
		}

		private void DoPrint(string strBadgeDatFileName)
		{
			// close all image and badge preview displays
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			// start printer process - or do nothing if aleady running
			m_dlgBadgingMgt.StartDCSPrinter();
            m_dlgBadgingMgt.PrintQueueStart();

			int iRefNo = m_dlgBadgingMgt.EnqueueBadge(strBadgeDatFileName);
			// SYH NOTE: need a way to detect user cancel or op failure.
			// iRefNo is planned to be used to make status requests about a specific badge
		}

		private int DoEncodeChip(string strDataFileName, out string strChipID)
        {
            // close all image and badge preview displays
            m_dlgCaptureMgt.CloseDisplays();
            m_dlgBadgingMgt.ClosePreview();

			int iRet = m_dlgBadgingMgt.EncodeChip(strDataFileName, out strChipID);
            return iRet;
        }

		private void buttonAllCapture_Click(object sender, System.EventArgs e)
		{
			DoCapture(this.tbImageID.Text, this.cbImageClass.Text, -1, this.tbImageTitle.Text);
		}

		private void buttonExit_Click(object sender, System.EventArgs e)
		{
			Application.Exit();
		}

		private void buttonLayout_Click(object sender, System.EventArgs e)
		{
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			m_dlgBadgingMgt.DesignBadge(null);
		}

		private void buttonPrint_Click(object sender, System.EventArgs e)
		{
			DoPrint(this.cbBadgeDat.Text);
		}

		private void buttonPreview_Click(object sender, System.EventArgs e)
		{
			DoPreview(this.cbBadgeDat.Text, this.cbBadgeDat.Text);	// set display label same as badge design name
		}
		private void buttonDisplay_Click(object sender, System.EventArgs e)
		{
			DoDisplay(this.tbImageID.Text, null, this.cbImageClass.Text);
		}

		private void buttonClose_Click(object sender, System.EventArgs e)
		{
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();
		}

		private void buttonSetupCapture_Click(object sender, System.EventArgs e)
		{
			m_dlgCaptureMgt.ShowDialog(this);
		}

		private void buttonSetupBadges_Click(object sender, System.EventArgs e)
		{
			m_dlgBadgingMgt.EditBadgingProperties();

			// data root dir may have been changed
			// get Badge Data Directory and reset it and the paths dependent on it
			DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("DCSSDK_Mgt");
			m_dlgCaptureMgt.DataRootDir = ps.GetStringParameter("DataRootDir", ps.m_strDCSInstallDirectory);
		}

		private void buttonStartDCSPrinter_Click(object sender, System.EventArgs e)
		{
			// start printer process - or do nothing if aleady running
			m_dlgBadgingMgt.StartDCSPrinter();
            m_dlgBadgingMgt.PrintQueueStart();
        }

		private void buttonEditTxt_Click(object sender, System.EventArgs e)
		{
            DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("");
			string strFullname = System.IO.Path.Combine(ps.m_strDCSInstallDirectory, this.cbBadgeDat.Text);
            if (!System.IO.File.Exists(strFullname))
            {
                DCSDEV.DCSMsg.Show("Badge data file :" + strFullname + " does not exist.");
                return;
            }
			TestApp_Badging.EditTxtFile dlg = new EditTxtFile(strFullname);
			dlg.Show();
		}

        private void comboBoxLanguage_SelectedIndexChanged(object sender, EventArgs e)
        {
            //set the user interface language to be used for all controls loaded in the future
            System.Threading.Thread.CurrentThread.CurrentUICulture = new System.Globalization.CultureInfo(this.comboBoxLanguage.Text);

            //relead the resources for the current form
            this.Controls.Clear();
            this.InitializeComponent();

            //force reload of all ID Services SDK components so the resources for the language are loaded
            //syh WARNING: this might have bad consequences if low level child controls are currently loaded.
            m_dlgCaptureMgt.Dispose();
            m_dlgCaptureMgt = new DCSSDK.CaptureMgt.DCSSDK_CaptureMgt();
            m_dlgBadgingMgt.Dispose();
            m_dlgBadgingMgt = new DCSSDK.BadgingMgt.DCSSDK_BadgingMgt();
        }

        private void buttonEncodeChip_Click(object sender, EventArgs e)
        {
            int iRet;
			string strChipID;
			Cursor cursorSave = this.Cursor;
            this.Cursor = Cursors.WaitCursor;
			iRet = this.DoEncodeChip(this.textBoxChipEncodeFile.Text, out strChipID);
            if (iRet < 0)
                DCSDEV.DCSMsg.Show("Encoding failed.");
            else if (iRet == 0)
                DCSDEV.DCSMsg.Show("Encoding cancelled.");
            else
				DCSDEV.DCSMsg.Show(String.Format("Encoded to chip, ID = {0}.", strChipID));
            this.Cursor = cursorSave;
        }

        private void buttonScan2DBarcode_Click(object sender, EventArgs e)
        {
            DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("");
            string strFilename = System.IO.Path.Combine(ps.m_strDCSInstallDirectory, "_BarRead.Dat");
            m_dlgCaptureMgt.ScanData("2DBAR", strFilename);
        }

        private void buttonScanChip_Click(object sender, EventArgs e)
        {
            int iRet;
            Cursor cursorSave = this.Cursor;
            this.Cursor = Cursors.WaitCursor;

            iRet = m_dlgCaptureMgt.ScanData("CHIP", this.textBoxChipScanFile.Text);
            if (iRet < 0)
                DCSDEV.DCSMsg.Show("Scanning failed.");
            else if (iRet == 0)
                DCSDEV.DCSMsg.Show("Scanning cancelled.");
            else
				DCSDEV.DCSMsg.Show(String.Format("Scanning finished. ChipID={0}", m_dlgCaptureMgt.GetLastChipID()));
            this.Cursor = cursorSave;
        }

		private void buttonDelete_Click(object sender, EventArgs e)
		{
			this.DoDelete(this.tbImageID.Text, this.cbImageClass.Text);
		}
	}
}
