<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="pictureBox1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        Qk02DQAAAAAAADYEAAAoAAAAMAAAADAAAAABAAgAAAAAAAAAAADEDgAAxA4AAAABAAAAAQAAAAAA/wAA
        gP8AgAD/AICA/4AAAP+AAID/gIAA/8DAwP/A3MD/8Mik/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/8Pv//6SgoP+AgID/AAD//wD/AP8A/////wAA//8A/////wD/////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        //////////////////////////////////////////////////////////8EBAQEBP//////////////
        ////////////////////////////////////BAQEBAQEBAQEBAQEBAQE////////////////////////
        /////////////////wQEBAQEBAT//////wQEBAQEBAT/////////////////////////////////////
        BAQE////////////////////BAQE/////////////////////////////////wQEBP//////////////
        //////////8EBAT/////////////////////////////BAT//////////////////////////////wQE
        //////////////////////////8EBP///////////////wT///////////////8EBP//////////////
        /////////wT//////////////////wT//////////////////wT/////////////////////BP//////
        ////////////BAQE//////////////////8E//////////////////8EBP//////////////////BAQE
        //////////////////8EBP////////////////8E//////////////////8EBAQEBP//////////////
        ////BP///////////////wT///////////////////8EBAQEBP///////////////////wT/////////
        ////BAT//////////////////wQEBAQEBAT//////////////////wQE////////////BP//////////
        //////////////////////////////////////8E//////////8EBP///////////////////////wT/
        //////////////////////8EBP////////8EBP///////////////////////wT/////////////////
        //////8EBP////////8EBP///////////////////////wT///////////////////////8EBP//////
        //8EBP///////////wT//////////wT//////////wT///////////8EBP////////8E//////////8E
        BAT//////////wT//////////wQEBP//////////BP///////wQE////////BAQEBAT//////////wT/
        /////////wQEBAQE////////BAT//////wQE/////wQEBAQEBAT/BAQEBAQEBAQEBAQEBAQE/wQEBAQE
        BAT/////BAT//////wQE////////BAQEBAT/////////+fn5/////////wQEBAQE////////BAT/////
        /wQE//////////8EBAT/////////+fn5/////////wQEBP//////////BAT///////8EBP//////////
        /wT/////////+fn5/////////wT///////////8EBP////////8EBP//////////////////////+fn5
        //////////////////////8EBP////////8EBP//////////////////////+fn5////////////////
        //////8EBP////////8EBP//////////////////////+fn5//////////////////////8EBP//////
        //8EBP//////////////////////+fn5//////////////////////8EBP//////////BAT/////////
        ///////5+fn5+fn5+fn5+f///////////////wQE////////////BAT/////////////////+fn5+fn5
        +fn5/////////////////wQE/////////////wQE//////////////////n5+fn5+fn/////////////
        ////BAT///////////////8EBP/////////////////5+fn5+f////////////////8EBP//////////
        //////8EBP/////////////////5+fn5+f////////////////8EBP//////////////////BAT/////
        ////////////+fn5/////////////////wQE/////////////////////wQE//////////////////n/
        ////////////////BAT///////////////////////8EBP////////////////n///////////////8E
        BP///////////////////////////wQE////////////////////////////BAT/////////////////
        //////////////8EBP////////////////////////8EBP//////////////////////////////////
        /wQE////////////////////BAT/////////////////////////////////////////BAQEBAT/////
        /wQEBAQE//////////////////////////////////////////////////8EBAQEBP//////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        /////////////////////////////w==
</value>
  </data>
  <metadata name="imageList1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="imageList1.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj0yLjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAABU
        FQAAAk1TRnQBSQFMAgEBBAEAAQkBAAEEAQABMAEAATABAAT/AQkBAAj/AUIBTQE2AQQGAAE2AQQCAAEo
        AwABwAMAAWADAAEBAQABCAYAAUgYAAGAAgABgAMAAoABAAGAAwABgAEAAYABAAKAAgADwAEAAcAB3AHA
        AQAB8AHKAaYBAAEzBQABMwEAATMBAAEzAQACMwIAAxYBAAMcAQADIgEAAykBAANVAQADTQEAA0IBAAM5
        AQABgAF8Af8BAAJQAf8BAAGTAQAB1gEAAf8B7AHMAQABxgHWAe8BAAHWAucBAAGQAakBrQIAAf8BMwMA
        AWYDAAGZAwABzAIAATMDAAIzAgABMwFmAgABMwGZAgABMwHMAgABMwH/AgABZgMAAWYBMwIAAmYCAAFm
        AZkCAAFmAcwCAAFmAf8CAAGZAwABmQEzAgABmQFmAgACmQIAAZkBzAIAAZkB/wIAAcwDAAHMATMCAAHM
        AWYCAAHMAZkCAALMAgABzAH/AgAB/wFmAgAB/wGZAgAB/wHMAQABMwH/AgAB/wEAATMBAAEzAQABZgEA
        ATMBAAGZAQABMwEAAcwBAAEzAQAB/wEAAf8BMwIAAzMBAAIzAWYBAAIzAZkBAAIzAcwBAAIzAf8BAAEz
        AWYCAAEzAWYBMwEAATMCZgEAATMBZgGZAQABMwFmAcwBAAEzAWYB/wEAATMBmQIAATMBmQEzAQABMwGZ
        AWYBAAEzApkBAAEzAZkBzAEAATMBmQH/AQABMwHMAgABMwHMATMBAAEzAcwBZgEAATMBzAGZAQABMwLM
        AQABMwHMAf8BAAEzAf8BMwEAATMB/wFmAQABMwH/AZkBAAEzAf8BzAEAATMC/wEAAWYDAAFmAQABMwEA
        AWYBAAFmAQABZgEAAZkBAAFmAQABzAEAAWYBAAH/AQABZgEzAgABZgIzAQABZgEzAWYBAAFmATMBmQEA
        AWYBMwHMAQABZgEzAf8BAAJmAgACZgEzAQADZgEAAmYBmQEAAmYBzAEAAWYBmQIAAWYBmQEzAQABZgGZ
        AWYBAAFmApkBAAFmAZkBzAEAAWYBmQH/AQABZgHMAgABZgHMATMBAAFmAcwBmQEAAWYCzAEAAWYBzAH/
        AQABZgH/AgABZgH/ATMBAAFmAf8BmQEAAWYB/wHMAQABzAEAAf8BAAH/AQABzAEAApkCAAGZATMBmQEA
        AZkBAAGZAQABmQEAAcwBAAGZAwABmQIzAQABmQEAAWYBAAGZATMBzAEAAZkBAAH/AQABmQFmAgABmQFm
        ATMBAAGZATMBZgEAAZkBZgGZAQABmQFmAcwBAAGZATMB/wEAApkBMwEAApkBZgEAA5kBAAKZAcwBAAKZ
        Af8BAAGZAcwCAAGZAcwBMwEAAWYBzAFmAQABmQHMAZkBAAGZAswBAAGZAcwB/wEAAZkB/wIAAZkB/wEz
        AQABmQHMAWYBAAGZAf8BmQEAAZkB/wHMAQABmQL/AQABzAMAAZkBAAEzAQABzAEAAWYBAAHMAQABmQEA
        AcwBAAHMAQABmQEzAgABzAIzAQABzAEzAWYBAAHMATMBmQEAAcwBMwHMAQABzAEzAf8BAAHMAWYCAAHM
        AWYBMwEAAZkCZgEAAcwBZgGZAQABzAFmAcwBAAGZAWYB/wEAAcwBmQIAAcwBmQEzAQABzAGZAWYBAAHM
        ApkBAAHMAZkBzAEAAcwBmQH/AQACzAIAAswBMwEAAswBZgEAAswBmQEAA8wBAALMAf8BAAHMAf8CAAHM
        Af8BMwEAAZkB/wFmAQABzAH/AZkBAAHMAf8BzAEAAcwC/wEAAcwBAAEzAQAB/wEAAWYBAAH/AQABmQEA
        AcwBMwIAAf8CMwEAAf8BMwFmAQAB/wEzAZkBAAH/ATMBzAEAAf8BMwH/AQAB/wFmAgAB/wFmATMBAAHM
        AmYBAAH/AWYBmQEAAf8BZgHMAQABzAFmAf8BAAH/AZkCAAH/AZkBMwEAAf8BmQFmAQAB/wKZAQAB/wGZ
        AcwBAAH/AZkB/wEAAf8BzAIAAf8BzAEzAQAB/wHMAWYBAAH/AcwBmQEAAf8CzAEAAf8BzAH/AQAC/wEz
        AQABzAH/AWYBAAL/AZkBAAL/AcwBAAJmAf8BAAFmAf8BZgEAAWYC/wEAAf8CZgEAAf8BZgH/AQAC/wFm
        AQABIQEAAaUBAANfAQADdwEAA4YBAAOWAQADywEAA7IBAAPXAQAD3QEAA+MBAAPqAQAD8QEAA/gBAAHw
        AfsB/wEAAaQCoAEAA4ADAAH/AgAB/wMAAv8BAAH/AwAB/wEAAf8BAAL/AgAD//8A/wD/AP8A/wD/AP8A
        /wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A
        /wB9AAUEKwAFBCsABQQrAAUEJgAPBCEADwQhAA8EIQAPBB8ABwQFAAcEHQAHBAUABwQdAAcEBQAHBB0A
        BwQFAAcEHAADBA8AAwQbAAMEDwADBBsAAwQPAAMEGwADBA8AAwQZAAMEEwADBBcAAwQTAAMEFwADBBMA
        AwQXAAMEEwADBBYAAgQXAAIEFQACBBcAAgQVAAIEFwACBBUAAgQXAAIEFAACBAwAAQQMAAIEEwACBAwA
        AQQMAAIEEwACBAwAAfkMAAIEEwACBAwAAQQMAAIEEgABBA4AAQQOAAEEEQABBA4AAQQOAAEEEQABBA4A
        AfkOAAEEEQABBA4AAQQOAAEEEAABBA4AAwQOAAEEDwABBA4AAwQOAAEEDwABBA4AA/kOAAEEDwABBA4A
        AwQOAAEEDgACBA4AAwQOAAIEDQACBA4AAwQOAAIEDQACBA0ABfkNAAIEDQACBA4AAwQOAAIEDQABBA4A
        BQQOAAEEDQABBA4ABQQOAAEEDQABBA0ABvkOAAEEDQABBA4ABQQOAAEEDAABBA8ABQQPAAEECwABBA8A
        BQQPAAEECwABBA4AB/kOAAEECwABBA8ABQQPAAEECgACBA4ABwQOAAIECQACBA4ABwQOAAIECQACBA0A
        CfkNAAIECQACBA4ABwQOAAIECQABBCUAAQQJAAEEJQABBAkAAQQNAAv5DQABBAkAAQQlAAEECAACBBIA
        AQQSAAIEBwACBBIAAQQSAAIEBwACBBEAA/kRAAIEBwACBBIAAQQSAAIEBwACBBIAAQQSAAIEBwACBBIA
        AQQHAAH5CgACBAcAAgQRAAP5EQACBAcAAgQKAAH5BwABBBIAAgQHAAIEEgABBBIAAgQHAAIEEgABBAcA
        AvkJAAIEBwACBBEAA/kRAAIEBwACBAkAAvkHAAEEEgACBAcAAgQJAAEECAABBAgAAQQJAAIEBwACBAkA
        AQQIAAEEBwAD+QgAAgQHAAIECQABBAcAA/kHAAEECQACBAcAAgQHAAT5BwABBAgAAQQJAAIEBwABBAgA
        AwQIAAEECAADBAgAAQQHAAEECAADBAgAAQQHAAX5BwABBAcAAQQIAAMEBwAD+QcAAwQIAAEEBwABBAcA
        BfkHAAEECAADBAgAAQQGAAIEBgAFBAgAAQQIAAUEBgACBAUAAgQGAAUECAABBA35BgACBAUAAgQGAAUE
        BwAD+QcABQQGAAIEBQACBAYADfkBBAgABQQGAAIEBQACBAQABwQBAA8EAQAHBAQAAgQFAAIEBAAHBAEA
        CAQP+QQAAgQFAAIEBAAHBAEADwQBAAcEBAACBAUAAgQEAA/5CAQBAAcEBAACBAUAAgQGAAUEBwAD+QcA
        BQQGAAIEBQACBAYABQQIAAEEDfkGAAIEBQACBAYABQQIAAEECAAFBAYAAgQFAAIEBgAN+QEECAAFBAYA
        AgQFAAIECAADBAcAA/kHAAMECAACBAUAAgQIAAMECAABBAcABfkHAAIEBQACBAgAAwQIAAEECAADBAgA
        AgQFAAIEBwAF+QcAAQQIAAMECAACBAYAAgQJAAEEBwAD+QcAAQQJAAIEBwACBAkAAQQIAAEEBwAD+QgA
        AgQHAAIECQABBAgAAQQIAAEECQACBAcAAgQHAAT5BwABBAgAAQQJAAIEBwACBBEAA/kRAAIEBwACBBIA
        AQQHAAL5CQACBAcAAgQSAAEEEgACBAcAAgQJAAL5BwABBBIAAgQHAAIEEQAD+REAAgQHAAIEEgABBAcA
        AfkKAAIEBwACBBIAAQQSAAIEBwACBAoAAfkHAAEEEgACBAcAAgQRAAP5EQACBAcAAgQSAAEEEgACBAcA
        AgQSAAEEEgACBAcAAgQSAAEEEgACBAcAAgQRAAP5EQACBAcAAgQSAAEEEgACBAcAAgQSAAEEEgACBAcA
        AgQSAAEEEgACBAgAAgQMAAv5DAACBAkAAgQjAAIECQACBCMAAgQJAAIEIwACBAkAAgQNAAn5DQACBAkA
        AgQOAAcEDgACBAkAAgQOAAcEDgACBAkAAgQOAAcEDgACBAoAAgQNAAf5DQACBAsAAgQOAAUEDgACBAsA
        AgQOAAUEDgACBAsAAgQOAAUEDgACBAwAAgQNAAX5DQACBA0AAgQNAAUEDQACBA0AAgQNAAUEDQACBA0A
        AgQNAAUEDQACBA0AAgQNAAX5DQACBA0AAgQOAAMEDgACBA0AAgQOAAMEDgACBA0AAgQOAAMEDgACBA4A
        AgQNAAP5DQACBA8AAgQNAAMEDQACBA8AAgQNAAMEDQACBA8AAgQNAAMEDQACBBAAAgQNAAH5DQACBBEA
        AgQNAAEEDQACBBEAAgQNAAEEDQACBBEAAgQNAAEEDQACBBIAAgQMAAH5DAACBBMAAgQMAAEEDAACBBMA
        AgQMAAEEDAACBBMAAgQMAAEEDAACBBUAAgQVAAIEFwACBBUAAgQXAAIEFQACBBcAAgQVAAIEGAACBBMA
        AgQZAAIEEwACBBkAAgQTAAIEGQACBBMAAgQbAAIEDwACBB0AAgQPAAIEHQACBA8AAgQdAAIEDwACBB8A
        BQQFAAUEIQAFBAUABQQhAAUEBQAFBCEABQQFAAUEJgAFBCsABQQrAAUEKwAFBP8A/wD/ABgAAUIBTQE+
        BwABPgMAASgDAAHAAwABYAMAAQEBAAEBBgABCRYAA///AP8A/wD/AIUASv8B/AEfBP8B/AEfBP8B/AEf
        BP8B/AEfBP8BgAEABP8BgAEABP8BgAEABP8BgAEAA/8B/gEDAeABPwL/Af4BAwHgAT8C/wH+AQMB4AE/
        Av8B/gEDAeABPwL/AfwBfwH/AR8C/wH8AX8B/wEfAv8B/AF/Af8BHwL/AfwBfwH/AR8C/wHxAv8BxwL/
        AfEC/wHHAv8B8QL/AccC/wHxAv8BxwL/AecC/wHzAv8B5wL/AfMC/wHnAv8B8wL/AecC/wHzAv8BzwH/
        AX8B+QL/Ac8B/wF/AfkC/wHPAf8BfwH5Av8BzwH/AX8B+QL/Ab8B/wF/Af4C/wG/Af8BfwH+Av8BvwH/
        AX8B/gL/Ab8B/wF/Af4C/wF/Af4BPwH/AX8B/wF/Af4BPwH/AX8B/wF/Af4BPwH/AX8B/wF/Af4BPwH/
        AX8B/gF/Af4BPwH/AT8B/gF/Af4BPwH/AT8B/gF/AfwBHwH/AT8B/gF/Af4BPwH/AT8B/gH/AfwBHwH/
        Ab8B/gH/AfwBHwH/Ab8B/gH/AfgBHwH/Ab8B/gH/AfwBHwH/Ab8B/QH/AfwBHwH/Ad8B/QH/AfwBHwH/
        Ad8B/QH/AfgBDwH/Ad8B/QH/AfwBHwH/Ad8B+QH/AfgBDwH/Ac8B+QH/AfgBDwH/Ac8B+QH/AfABBwH/
        Ac8B+QH/AfgBDwH/Ac8B+wT/Ae8B+wT/Ae8B+wH/AeABAwH/Ae8B+wT/Ae8B8wL/AX8B/wHnAfMC/wF/
        Af8B5wHzAf8B/gE/Af8B5wHzAv8BfwH/AecB8wL/AX8B/wHnAfMC/wJ/AecB8wH/Af4BPwH/AecB8wH/
        An8B/wHnAfMC/wF/Af8B5wHzAv8BfwE/AecB8wH/Af4BPwH/AecB8wH+An8B/wHnAfMB/gH/AX8BvwHn
        AfMB/gH/AX8BHwHnAfMC/gE/Ab8B5wHzAfgCfwG/AecB9wH4Af8BfwGPAvcB+AH/AX8BBwL3AfgB/gE/
        AY8C9wHwAn8BjwH3AecB4AH/AX8BgwHzAecB4AH/AQABAwHzAecB4AH+AT8BgwHzAecB4AEAAX8BgwHz
        AecCgAEAAYAB8wHnAoACAAHzAecCgAEAAYAB8wHnAYACAAGAAfMB5wHgAf4BPwGDAfMB5wHgAf8BAAED
        AfMB5wHgAf8BfwGDAfMB5wHgAQABfwGDAfMB5wH4Af4BPwGPAfMB5wH4Af8BfwEHAfMB5wH4Af8BfwGP
        AfMB5wHwAn8BjwLzAv4BPwG/AecB8wH+Af8BfwEfAecB8wH+Af8BfwG/AecB8wH4An8BvwHnAfMB/wH+
        AT8B/wHnAfMC/wF/AT8B5wHzAv8BfwH/AecB8wH+An8B/wHnAfMB/wH+AT8B/wHnAfMC/wJ/AecB8wL/
        AX8B/wHnAfMB/wJ/Af8B5wHzAf8B/gE/Af8B5wHzAv8BfwH/AecB8wL/AX8B/wHnAfMC/wF/Af8B5wHz
        Af8B/gE/Af8B5wHzAv8BfwH/AecB8wL/AX8B/wHnAfMC/wF/Af8B5wH5Af8B4AEDAf8BzwH5BP8BzwH5
        BP8BzwH5BP8BzwH5Af8B8AEHAf8BzwH5Af8B+AEPAf8BzwH5Af8B+AEPAf8BzwH5Af8B+AEPAf8BzwH8
        Af8B+AEPAf8BnwH8Af8B/AEfAf8BnwH8Af8B/AEfAf8BnwH8Af8B/AEfAf8BnwH+AX8B/AEfAf8BPwH+
        AX8B/AEfAf8BPwH+AX8B/AEfAf8BPwH+AX8B/AEfAf8BPwH+AX8B/AEfAf8BPwH+AX8B/gE/Af8BPwH+
        AX8B/gE/Af8BPwH+AX8B/gE/Af8BPwH/AT8B/gE/Af4BfwH/AT8B/gE/Af4BfwH/AT8B/gE/Af4BfwH/
        AT8B/gE/Af4BfwH/AZ8B/wF/AfwC/wGfAf8BfwH8Av8BnwH/AX8B/AL/AZ8B/wF/AfwC/wHPAf8BfwH5
        Av8BzwH/AX8B+QL/Ac8B/wF/AfkC/wHPAf8BfwH5Av8B8wL/AecC/wHzAv8B5wL/AfMC/wHnAv8B8wL/
        AecC/wH5Av8BzwL/AfkC/wHPAv8B+QL/Ac8C/wH5Av8BzwL/Af4BfwH/AT8C/wH+AX8B/wE/Av8B/gF/
        Af8BPwL/Af4BfwH/AT8D/wGDAeAE/wGDAeAE/wGDAeAE/wGDAeAE/wH8AR8E/wH8AR8E/wH8AR8E/wH8
        AR9i/ws=
</value>
  </data>
  <metadata name="toolTip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>122, 17</value>
  </metadata>
</root>