<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>400, 72</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 24</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>Columns / Rows</value>
  </data>
  <data name="label1.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>TopRight</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="tbColumns.Location" type="System.Drawing.Point, System.Drawing">
    <value>512, 72</value>
  </data>
  <data name="tbColumns.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="tbColumns.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="tbColumns.Text" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;tbColumns.Name" xml:space="preserve">
    <value>tbColumns</value>
  </data>
  <data name="&gt;&gt;tbColumns.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbColumns.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbColumns.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="tbRows.Location" type="System.Drawing.Point, System.Drawing">
    <value>568, 72</value>
  </data>
  <data name="tbRows.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="tbRows.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="tbRows.Text" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;tbRows.Name" xml:space="preserve">
    <value>tbRows</value>
  </data>
  <data name="&gt;&gt;tbRows.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbRows.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbRows.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="tbFirstY.Location" type="System.Drawing.Point, System.Drawing">
    <value>568, 104</value>
  </data>
  <data name="tbFirstY.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="tbFirstY.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="tbFirstY.Text" xml:space="preserve">
    <value>0.000</value>
  </data>
  <data name="&gt;&gt;tbFirstY.Name" xml:space="preserve">
    <value>tbFirstY</value>
  </data>
  <data name="&gt;&gt;tbFirstY.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbFirstY.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbFirstY.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="tbFirstX.Location" type="System.Drawing.Point, System.Drawing">
    <value>512, 104</value>
  </data>
  <data name="tbFirstX.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="tbFirstX.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="tbFirstX.Text" xml:space="preserve">
    <value>0.000</value>
  </data>
  <data name="&gt;&gt;tbFirstX.Name" xml:space="preserve">
    <value>tbFirstX</value>
  </data>
  <data name="&gt;&gt;tbFirstX.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbFirstX.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbFirstX.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>400, 104</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 24</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>Top Left  X / Y</value>
  </data>
  <data name="label2.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>TopRight</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="tbSpaceY.Location" type="System.Drawing.Point, System.Drawing">
    <value>568, 136</value>
  </data>
  <data name="tbSpaceY.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="tbSpaceY.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="tbSpaceY.Text" xml:space="preserve">
    <value>3.500</value>
  </data>
  <data name="&gt;&gt;tbSpaceY.Name" xml:space="preserve">
    <value>tbSpaceY</value>
  </data>
  <data name="&gt;&gt;tbSpaceY.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbSpaceY.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbSpaceY.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="tbSpaceX.Location" type="System.Drawing.Point, System.Drawing">
    <value>512, 136</value>
  </data>
  <data name="tbSpaceX.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="tbSpaceX.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="tbSpaceX.Text" xml:space="preserve">
    <value>3.500</value>
  </data>
  <data name="&gt;&gt;tbSpaceX.Name" xml:space="preserve">
    <value>tbSpaceX</value>
  </data>
  <data name="&gt;&gt;tbSpaceX.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbSpaceX.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbSpaceX.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>400, 136</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 24</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>Column  W / Row H</value>
  </data>
  <data name="label3.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>TopRight</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="tbBadgeY.Location" type="System.Drawing.Point, System.Drawing">
    <value>568, 168</value>
  </data>
  <data name="tbBadgeY.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="tbBadgeY.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="tbBadgeY.Text" xml:space="preserve">
    <value>2.250</value>
  </data>
  <data name="&gt;&gt;tbBadgeY.Name" xml:space="preserve">
    <value>tbBadgeY</value>
  </data>
  <data name="&gt;&gt;tbBadgeY.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbBadgeY.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbBadgeY.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="tbBadgeX.Location" type="System.Drawing.Point, System.Drawing">
    <value>512, 168</value>
  </data>
  <data name="tbBadgeX.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="tbBadgeX.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="tbBadgeX.Text" xml:space="preserve">
    <value>3.000</value>
  </data>
  <data name="&gt;&gt;tbBadgeX.Name" xml:space="preserve">
    <value>tbBadgeX</value>
  </data>
  <data name="&gt;&gt;tbBadgeX.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbBadgeX.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbBadgeX.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>400, 168</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 24</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>Document  W / H</value>
  </data>
  <data name="label4.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>TopRight</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonCancel.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>456, 320</value>
  </data>
  <data name="buttonCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonCancel.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="buttonCancel.Text" xml:space="preserve">
    <value>&amp;Cancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Name" xml:space="preserve">
    <value>buttonCancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonCancel.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="buttonAccept.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonAccept.Location" type="System.Drawing.Point, System.Drawing">
    <value>456, 288</value>
  </data>
  <data name="buttonAccept.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonAccept.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="buttonAccept.Text" xml:space="preserve">
    <value>&amp;OK</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Name" xml:space="preserve">
    <value>buttonAccept</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAccept.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="buttonPreview.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonPreview.Location" type="System.Drawing.Point, System.Drawing">
    <value>456, 232</value>
  </data>
  <data name="buttonPreview.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonPreview.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="buttonPreview.Text" xml:space="preserve">
    <value>&amp;Preview</value>
  </data>
  <data name="&gt;&gt;buttonPreview.Name" xml:space="preserve">
    <value>buttonPreview</value>
  </data>
  <data name="&gt;&gt;buttonPreview.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPreview.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonPreview.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="pictureBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 48</value>
  </data>
  <data name="pictureBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>368, 384</value>
  </data>
  <data name="pictureBox1.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="pictureBox1.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;pictureBox1.Name" xml:space="preserve">
    <value>pictureBox1</value>
  </data>
  <data name="&gt;&gt;pictureBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pictureBox1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pictureBox1.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="tbPrinterH.Location" type="System.Drawing.Point, System.Drawing">
    <value>567, 40</value>
  </data>
  <data name="tbPrinterH.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 20</value>
  </data>
  <data name="tbPrinterH.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="tbPrinterH.Text" xml:space="preserve">
    <value>11.000</value>
  </data>
  <data name="&gt;&gt;tbPrinterH.Name" xml:space="preserve">
    <value>tbPrinterH</value>
  </data>
  <data name="&gt;&gt;tbPrinterH.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbPrinterH.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbPrinterH.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="tbPrinterW.Location" type="System.Drawing.Point, System.Drawing">
    <value>512, 40</value>
  </data>
  <data name="tbPrinterW.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="tbPrinterW.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="tbPrinterW.Text" xml:space="preserve">
    <value>8.500</value>
  </data>
  <data name="&gt;&gt;tbPrinterW.Name" xml:space="preserve">
    <value>tbPrinterW</value>
  </data>
  <data name="&gt;&gt;tbPrinterW.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbPrinterW.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbPrinterW.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label5.Location" type="System.Drawing.Point, System.Drawing">
    <value>400, 40</value>
  </data>
  <data name="label5.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 24</value>
  </data>
  <data name="label5.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>Printer  W / H</value>
  </data>
  <data name="label5.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>TopRight</value>
  </data>
  <data name="&gt;&gt;label5.Name" xml:space="preserve">
    <value>label5</value>
  </data>
  <data name="&gt;&gt;label5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label5.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label5.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="comboBoxUnits.Items" xml:space="preserve">
    <value>Inch</value>
  </data>
  <data name="comboBoxUnits.Items1" xml:space="preserve">
    <value>MM</value>
  </data>
  <data name="comboBoxUnits.Location" type="System.Drawing.Point, System.Drawing">
    <value>513, 8</value>
  </data>
  <data name="comboBoxUnits.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 21</value>
  </data>
  <data name="comboBoxUnits.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="comboBoxUnits.Text" xml:space="preserve">
    <value>Inch</value>
  </data>
  <data name="&gt;&gt;comboBoxUnits.Name" xml:space="preserve">
    <value>comboBoxUnits</value>
  </data>
  <data name="&gt;&gt;comboBoxUnits.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxUnits.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;comboBoxUnits.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelLayoutError.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 9.75pt, style=Bold</value>
  </data>
  <data name="labelLayoutError.Location" type="System.Drawing.Point, System.Drawing">
    <value>432, 200</value>
  </data>
  <data name="labelLayoutError.Size" type="System.Drawing.Size, System.Drawing">
    <value>184, 24</value>
  </data>
  <data name="labelLayoutError.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="labelLayoutError.Text" xml:space="preserve">
    <value>Bounds Overflow</value>
  </data>
  <data name="labelLayoutError.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="&gt;&gt;labelLayoutError.Name" xml:space="preserve">
    <value>labelLayoutError</value>
  </data>
  <data name="&gt;&gt;labelLayoutError.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelLayoutError.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelLayoutError.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.Locked" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>631, 445</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterParent</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Document Sheet Layout</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>SheetLayout</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>