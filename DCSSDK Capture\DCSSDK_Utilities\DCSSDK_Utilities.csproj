﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="12.0">
  <PropertyGroup>
    <ProjectType>Local</ProjectType>
    <ProductVersion>8.0.50727</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{6EF6073A-143A-497A-9FFA-21DD39EBC9ED}</ProjectGuid>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ApplicationIcon>
    </ApplicationIcon>
    <AssemblyKeyContainerName>
    </AssemblyKeyContainerName>
    <AssemblyName>DCSSDK_Utilities</AssemblyName>
    <AssemblyOriginatorKeyFile>
    </AssemblyOriginatorKeyFile>
    <DefaultClientScript>JScript</DefaultClientScript>
    <DefaultHTMLPageLayout>Grid</DefaultHTMLPageLayout>
    <DefaultTargetSchema>IE50</DefaultTargetSchema>
    <DelaySign>false</DelaySign>
    <OutputType>Library</OutputType>
    <RootNamespace>DCSSDK_Utilities</RootNamespace>
    <RunPostBuildEvent>OnBuildSuccess</RunPostBuildEvent>
    <StartupObject>
    </StartupObject>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <OldToolsVersion>2.0</OldToolsVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <BaseAddress>285212672</BaseAddress>
    <CheckForOverflowUnderflow>false</CheckForOverflowUnderflow>
    <ConfigurationOverrideFile>
    </ConfigurationOverrideFile>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DocumentationFile>
    </DocumentationFile>
    <DebugSymbols>true</DebugSymbols>
    <FileAlignment>4096</FileAlignment>
    <NoStdLib>false</NoStdLib>
    <NoWarn>
    </NoWarn>
    <Optimize>false</Optimize>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RemoveIntegerChecks>false</RemoveIntegerChecks>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningLevel>4</WarningLevel>
    <DebugType>full</DebugType>
    <ErrorReport>prompt</ErrorReport>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <BaseAddress>285212672</BaseAddress>
    <CheckForOverflowUnderflow>false</CheckForOverflowUnderflow>
    <ConfigurationOverrideFile>
    </ConfigurationOverrideFile>
    <DefineConstants>TRACE</DefineConstants>
    <DocumentationFile>
    </DocumentationFile>
    <DebugSymbols>false</DebugSymbols>
    <FileAlignment>4096</FileAlignment>
    <NoStdLib>false</NoStdLib>
    <NoWarn>
    </NoWarn>
    <Optimize>true</Optimize>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RemoveIntegerChecks>false</RemoveIntegerChecks>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningLevel>4</WarningLevel>
    <DebugType>none</DebugType>
    <ErrorReport>prompt</ErrorReport>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="hasp_net_windows, Version=2.50.1.3662, Culture=neutral, PublicKeyToken=56120be447701319" />
    <Reference Include="LEAD, Version=*********, Culture=neutral, PublicKeyToken=9cf889f53ea9b907">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Referenced Files\Lead.Net\LEAD.dll</HintPath>
    </Reference>
    <Reference Include="LEAD.Drawing, Version=*********, Culture=neutral, PublicKeyToken=9cf889f53ea9b907">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Referenced Files\Lead.Net\LEAD.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="LEAD.Drawing.Imaging.ImageProcessing, Version=*********, Culture=neutral, PublicKeyToken=9cf889f53ea9b907">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Referenced Files\Lead.Net\LEAD.Drawing.Imaging.ImageProcessing.dll</HintPath>
    </Reference>
    <Reference Include="LEAD.Wrapper, Version=*********, Culture=neutral, PublicKeyToken=9cf889f53ea9b907">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Referenced Files\Lead.Net\LEAD.Wrapper.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <Name>System</Name>
    </Reference>
    <Reference Include="System.Data">
      <Name>System.Data</Name>
    </Reference>
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing">
      <Name>System.Drawing</Name>
    </Reference>
    <Reference Include="System.Windows.Forms">
      <Name>System.Windows.Forms</Name>
    </Reference>
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DCSAboutControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="DCSCaptureCommandParser.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DCSDatabaseIF.cs" />
    <Compile Include="DCSDesignDataAccess.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DCSLicensing.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DCSMath.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DCSMsgBox.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="DCSOleDBConnection.cs" />
    <Compile Include="Encryption.cs" />
    <Compile Include="OleImageDB.cs" />
    <Compile Include="DCSServerStuff.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="OleImageInUsersDB.cs" />
    <Compile Include="ParameterStore.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="PrinterTypeData.cs" />
    <Compile Include="Processing.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="SDSLicensing.cs" />
    <Compile Include="UnitizedNumberBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <EmbeddedResource Include="DCSAboutControl.resx">
      <DependentUpon>DCSAboutControl.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSDesignDataAccess.resx">
      <DependentUpon>DCSDesignDataAccess.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="UnitizedNumberBox.resx">
      <DependentUpon>UnitizedNumberBox.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PreBuildEvent>
    </PreBuildEvent>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
</Project>