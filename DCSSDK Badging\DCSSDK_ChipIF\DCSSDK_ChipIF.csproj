﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.50727</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{C5F4E046-FD68-4042-AA10-19DADD20191B}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DCSSDK_ChipIF</RootNamespace>
    <AssemblyName>DCSSDK_ChipIF</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>2.0</OldToolsVersion>
    <UpgradeBackupLocation />
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="LEAD, Version=*********, Culture=neutral, PublicKeyToken=9cf889f53ea9b907">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Capture\Referenced Files\Lead.Net\LEAD.dll</HintPath>
    </Reference>
    <Reference Include="LEAD.Drawing, Version=*********, Culture=neutral, PublicKeyToken=9cf889f53ea9b907">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Capture\Referenced Files\Lead.Net\LEAD.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="LEAD.Drawing.Imaging.Codecs, Version=*********, Culture=neutral, PublicKeyToken=9cf889f53ea9b907">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Capture\Referenced Files\Lead.Net\LEAD.Drawing.Imaging.Codecs.dll</HintPath>
    </Reference>
    <Reference Include="LEAD.Drawing.Imaging.ImageProcessing, Version=*********, Culture=neutral, PublicKeyToken=9cf889f53ea9b907">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Capture\Referenced Files\Lead.Net\LEAD.Drawing.Imaging.ImageProcessing.dll</HintPath>
    </Reference>
    <Reference Include="LEAD.Wrapper, Version=*********, Culture=neutral, PublicKeyToken=9cf889f53ea9b907">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Capture\Referenced Files\Lead.Net\LEAD.Wrapper.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DCSDEV_ChipIF.cs" />
    <Compile Include="DCS_EDLChip.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SmartChipIO.cs">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\DCSSDK Capture\DCSSDK_Utilities\DCSSDK_Utilities.csproj">
      <Project>{6EF6073A-143A-497A-9FFA-21DD39EBC9ED}</Project>
      <Name>DCSSDK_Utilities</Name>
    </ProjectReference>
    <ProjectReference Include="..\DCSSDK_Design\DCSSDK_Design.csproj">
      <Project>{070D3ED6-2209-4AFA-B015-64137A56DAF3}</Project>
      <Name>DCSSDK_Design</Name>
    </ProjectReference>
    <ProjectReference Include="..\DCSSDK_PrintProperties\DCSSDK_PrintProperties.csproj">
      <Project>{347DA4DB-3618-45A0-B87C-18F6D39F6048}</Project>
      <Name>DCSSDK_PrintProperties</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="ClassDiagram1.cd" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="SmartChipIO.resx">
      <DependentUpon>SmartChipIO.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.3.1">
      <Visible>False</Visible>
      <ProductName>Windows Installer 3.1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>