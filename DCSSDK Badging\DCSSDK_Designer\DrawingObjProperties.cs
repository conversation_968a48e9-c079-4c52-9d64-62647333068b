using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;

using DCSDEV;
namespace DCSDEV.DCSDesigner
{
	/// <summary>
	/// Summary description for DrawingObjProperties.
	/// </summary>
    internal class DrawingObjProperties : System.Windows.Forms.Form
	{
		private ArrayList m_DesignObjectsSelected;
		//private Form m_view;
		private DCSDEV.DCSDesigner.DCSDesignerView m_view;
		private DCSDEV.DCSDesign.DCSDesign m_design;

		private System.Windows.Forms.Button buttonCancel;
		private System.Windows.Forms.Button buttonAccept;
		private System.Windows.Forms.Button buttonApply;
		private System.Windows.Forms.ColorDialog colorDialog1;
        private DCSDEV.DCSDesigner.DCSPositionSizeProperties dcsPositionSizeProperties1;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.TextBox tbObjectType;
		private DCSDEV.DCSDesigner.DCSBackGroundProperties dcsBackGroundProperties1;
		private System.Windows.Forms.NumericUpDown numericUpDownTransparency;
		private System.Windows.Forms.Label label4;
		private Button buttonEditVisibleIf;
		private TextBox textBoxVisibleIf;
		private CheckBox checkBoxVisibleIf;
		private System.ComponentModel.IContainer components;

		public DrawingObjProperties(DCSDEV.DCSDesign.DCSDesign activeDoc, DCSDEV.DCSDesigner.DCSDesignerView activeView, ArrayList designObjectsSelected, bool bNew)
		{
			//
			// Required for Windows Form Designer support
			//
			components = null;
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			m_view = activeView;
			m_design = activeDoc;

			m_DesignObjectsSelected = designObjectsSelected;
			if (m_DesignObjectsSelected.Count > 0)
			{
				DCSDEV.DCSDesign.DCSDesignObject designObject = (DCSDEV.DCSDesign.DCSDesignObject)m_DesignObjectsSelected[0];
				if (bNew &&designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ImageObj 
					&& designObject.SourceType == DCSDEV.DCSDatatypes.SourceTypes.StaticValue)
					designObject.DesignObjectImageName = "select image";
				this.MoveDataToDlg(designObject);

				this.AdjustVisibilities();
                if (!DCSLicensing.IsLicensedOK(LicensedFeatures.ConditionalsOrFormulas, false))
                {
                    this.checkBoxVisibleIf.Checked = false;
                    this.checkBoxVisibleIf.Enabled = false;
                }

				if (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ImageObj)
					this.Text = "Image Object Properties";
				else if (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Portrait)
					this.Text = "Portrait Object Properties";
				else if (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Signature)
					this.Text = "Signature Object Properties";
				else if (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Fingerprint)
					this.Text = "Fingerprint Object Properties";
			}
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(DrawingObjProperties));
            this.buttonCancel = new System.Windows.Forms.Button();
            this.buttonAccept = new System.Windows.Forms.Button();
            this.buttonApply = new System.Windows.Forms.Button();
            this.colorDialog1 = new System.Windows.Forms.ColorDialog();
            this.dcsPositionSizeProperties1 = new DCSDEV.DCSDesigner.DCSPositionSizeProperties();
            this.label2 = new System.Windows.Forms.Label();
            this.tbObjectType = new System.Windows.Forms.TextBox();
            this.dcsBackGroundProperties1 = new DCSDEV.DCSDesigner.DCSBackGroundProperties();
            this.numericUpDownTransparency = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.buttonEditVisibleIf = new System.Windows.Forms.Button();
            this.textBoxVisibleIf = new System.Windows.Forms.TextBox();
            this.checkBoxVisibleIf = new System.Windows.Forms.CheckBox();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownTransparency)).BeginInit();
            this.SuspendLayout();
            // 
            // buttonCancel
            // 
            this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            resources.ApplyResources(this.buttonCancel, "buttonCancel");
            this.buttonCancel.Name = "buttonCancel";
            this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
            // 
            // buttonAccept
            // 
            resources.ApplyResources(this.buttonAccept, "buttonAccept");
            this.buttonAccept.Name = "buttonAccept";
            this.buttonAccept.Click += new System.EventHandler(this.buttonAccept_Click);
            // 
            // buttonApply
            // 
            resources.ApplyResources(this.buttonApply, "buttonApply");
            this.buttonApply.Name = "buttonApply";
            this.buttonApply.Click += new System.EventHandler(this.buttonApply_Click);
            // 
            // dcsPositionSizeProperties1
            // 
            this.dcsPositionSizeProperties1.DisplayBounds = new System.Drawing.Rectangle(0, 0, 0, 0);
            resources.ApplyResources(this.dcsPositionSizeProperties1, "dcsPositionSizeProperties1");
            this.dcsPositionSizeProperties1.Name = "dcsPositionSizeProperties1";
            this.dcsPositionSizeProperties1.Units = 0;
            // 
            // label2
            // 
            resources.ApplyResources(this.label2, "label2");
            this.label2.Name = "label2";
            // 
            // tbObjectType
            // 
            resources.ApplyResources(this.tbObjectType, "tbObjectType");
            this.tbObjectType.Name = "tbObjectType";
            this.tbObjectType.ReadOnly = true;
            // 
            // dcsBackGroundProperties1
            // 
            this.dcsBackGroundProperties1.AllDBFieldNames = null;
            this.dcsBackGroundProperties1.BackFillType = DCSDEV.DCSDatatypes.BackFillTypes.FILL_COLOR;
            this.dcsBackGroundProperties1.Color = System.Drawing.Color.LightBlue;
            this.dcsBackGroundProperties1.Color2 = System.Drawing.Color.MediumSlateBlue;
            this.dcsBackGroundProperties1.ColorCondition1 = null;
            this.dcsBackGroundProperties1.ColorCondition2 = null;
            this.dcsBackGroundProperties1.ConditionalColor1 = System.Drawing.Color.Empty;
            this.dcsBackGroundProperties1.ConditionalColor2 = System.Drawing.Color.Empty;
            this.dcsBackGroundProperties1.ConditionalColor3 = System.Drawing.Color.Empty;
            this.dcsBackGroundProperties1.Filename = "";
            this.dcsBackGroundProperties1.GradientType = System.Drawing.Drawing2D.LinearGradientMode.Horizontal;
            resources.ApplyResources(this.dcsBackGroundProperties1, "dcsBackGroundProperties1");
            this.dcsBackGroundProperties1.Name = "dcsBackGroundProperties1";
            this.dcsBackGroundProperties1.RestrictGradiantAndImage = false;
            // 
            // numericUpDownTransparency
            // 
            resources.ApplyResources(this.numericUpDownTransparency, "numericUpDownTransparency");
            this.numericUpDownTransparency.Name = "numericUpDownTransparency";
            this.numericUpDownTransparency.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // label4
            // 
            resources.ApplyResources(this.label4, "label4");
            this.label4.Name = "label4";
            // 
            // buttonEditVisibleIf
            // 
            resources.ApplyResources(this.buttonEditVisibleIf, "buttonEditVisibleIf");
            this.buttonEditVisibleIf.Name = "buttonEditVisibleIf";
            this.buttonEditVisibleIf.Click += new System.EventHandler(this.buttonEditVisibleIf_Click);
            // 
            // textBoxVisibleIf
            // 
            resources.ApplyResources(this.textBoxVisibleIf, "textBoxVisibleIf");
            this.textBoxVisibleIf.Name = "textBoxVisibleIf";
            // 
            // checkBoxVisibleIf
            // 
            resources.ApplyResources(this.checkBoxVisibleIf, "checkBoxVisibleIf");
            this.checkBoxVisibleIf.Name = "checkBoxVisibleIf";
            this.checkBoxVisibleIf.UseVisualStyleBackColor = true;
            this.checkBoxVisibleIf.CheckedChanged += new System.EventHandler(this.checkBoxVisibleIf_CheckedChanged);
            // 
            // DrawingObjProperties
            // 
            this.AcceptButton = this.buttonAccept;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.CancelButton = this.buttonCancel;
            resources.ApplyResources(this, "$this");
            this.Controls.Add(this.buttonEditVisibleIf);
            this.Controls.Add(this.textBoxVisibleIf);
            this.Controls.Add(this.checkBoxVisibleIf);
            this.Controls.Add(this.numericUpDownTransparency);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.dcsBackGroundProperties1);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.tbObjectType);
            this.Controls.Add(this.dcsPositionSizeProperties1);
            this.Controls.Add(this.buttonApply);
            this.Controls.Add(this.buttonCancel);
            this.Controls.Add(this.buttonAccept);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "DrawingObjProperties";
            this.ShowInTaskbar = false;
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownTransparency)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}
		#endregion

		private bool ApplyData()
		{
			// check validity of the data
			if (this.dcsBackGroundProperties1.BackFillType == DCSDEV.DCSDatatypes.BackFillTypes.FILL_IMAGE)
			{
				string strFullname = DCSDEV.DCSDesignDataAccess.ExpandImageName(this.dcsBackGroundProperties1.Filename, true);
				if (strFullname == null)
				{
					DCSDEV.DCSMsg.Show(String.Format("Background image file '{0}' does not exist", this.dcsBackGroundProperties1.Filename));
					return false;
				}
			}

			// All check pass .  It should be safe to procede
			DCSDEV.DCSDesign.DCSDesignObject designObject = (DCSDEV.DCSDesign.DCSDesignObject)m_DesignObjectsSelected[0];
			this.MoveDataFromDlg(designObject);
			
			if (designObject.BackImage != null) designObject.BackImage.Dispose();	// FILL_IMAGE is the only case where a back image should exist
			if (designObject.BackFillType == DCSDEV.DCSDatatypes.BackFillTypes.FILL_IMAGE)
			{
				try
				{
					designObject.BackImage = (Bitmap)DCSDEV.DCSDesignDataAccess.GetImage(designObject.BackImageName, false);
				}
				catch (Exception ex)
				{
					DCSDEV.DCSMsg.Show(ex);
				}
			}
			if (designObject.DesignObjectImage != null) designObject.DesignObjectImage.Dispose();
			if (designObject.DesignObjectImageName != null && designObject.DesignObjectImageName.Length != 0)
			{
				try
				{
					designObject.DesignObjectImage = (Bitmap)DCSDEV.DCSDesignDataAccess.GetImage(designObject.DesignObjectImageName, false);
				}
				catch (Exception ex)
				{
					DCSDEV.DCSMsg.Show(ex);
					return false;
				}
			}
			return true;
		}

		private void AdjustVisibilities()
		{
			// VisibleIf
			bool visible = this.checkBoxVisibleIf.Checked;
			this.textBoxVisibleIf.Visible = visible;
			this.buttonEditVisibleIf.Visible = visible;
		}

		private void MoveDataToDlg(DCSDEV.DCSDesign.DCSDesignObject designObject)
		{
			// source
			this.tbObjectType.Text = "Graphic Block";

			// position size
			this.dcsPositionSizeProperties1.DisplayBounds = designObject.Bounds;
			this.dcsPositionSizeProperties1.Units = DCSDEV.DCSDesignDataAccess.GetUnits();

			// background properties
			this.dcsBackGroundProperties1.BackFillType = designObject.BackFillType;
			this.dcsBackGroundProperties1.Filename = designObject.BackImageName;

			this.dcsBackGroundProperties1.Color = designObject.BackColor;
			this.dcsBackGroundProperties1.Color2 = designObject.BackColor2;
			this.dcsBackGroundProperties1.GradientType = designObject.BackGradientType;
			this.dcsBackGroundProperties1.AllDBFieldNames = this.m_view.m_mainWin.m_AllDBFieldNames;
			this.dcsBackGroundProperties1.ConditionalColor1 = designObject.ColorChoice1;
			this.dcsBackGroundProperties1.ConditionalColor2 = designObject.ColorChoice2;
			this.dcsBackGroundProperties1.ConditionalColor3 = designObject.ColorChoice3;
			this.dcsBackGroundProperties1.ColorCondition1 = designObject.ColorCondition1;
			this.dcsBackGroundProperties1.ColorCondition2 = designObject.ColorCondition2;

			// other
			this.numericUpDownTransparency.Value = designObject.Transparency;
			this.checkBoxVisibleIf.Checked = designObject.VisibleIf;
			this.textBoxVisibleIf.Text = designObject.VisibleIfCondition;
		}
		private void MoveDataFromDlg(DCSDEV.DCSDesign.DCSDesignObject designObject)
		{
			// source
			//designObject.DCSDesignObjectType = this.dcsSourceProperties1.DesignObjectType; not editable

			// position size
			designObject.Bounds = this.dcsPositionSizeProperties1.DisplayBounds;
			DCSDEV.DCSDesignDataAccess.SetUnits(this.dcsPositionSizeProperties1.Units);

			// background properties
			designObject.BackFillType	= this.dcsBackGroundProperties1.BackFillType;
			designObject.BackImageName = this.dcsBackGroundProperties1.Filename;
			designObject.BackColor		= this.dcsBackGroundProperties1.Color;

			designObject.BackColor2 = this.dcsBackGroundProperties1.Color2;
			designObject.BackGradientType   = this.dcsBackGroundProperties1.GradientType;
			designObject.ColorChoice1 = this.dcsBackGroundProperties1.ConditionalColor1;
			designObject.ColorChoice2 = this.dcsBackGroundProperties1.ConditionalColor2;
			designObject.ColorChoice3 = this.dcsBackGroundProperties1.ConditionalColor3;
			designObject.ColorCondition1 = this.dcsBackGroundProperties1.ColorCondition1;
			designObject.ColorCondition2 = this.dcsBackGroundProperties1.ColorCondition2;

			// other
			designObject.Transparency = (int)this.numericUpDownTransparency.Value;
			designObject.VisibleIf = this.checkBoxVisibleIf.Checked;
			designObject.VisibleIfCondition = this.textBoxVisibleIf.Text;
		}

		private void buttonAccept_Click(object sender, System.EventArgs e)
		{
			bool bRet = this.ApplyData();
			if (bRet) 
			{
				this.DialogResult = System.Windows.Forms.DialogResult.OK;
				this.Close();
			}
		}

		private void buttonCancel_Click(object sender, System.EventArgs e)
		{
			this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.Close();
		}

		private void buttonApply_Click(object sender, System.EventArgs e)
		{
			this.ApplyData();
			m_design.m_isDirty = true;
			m_design.m_isViewDirty = true;
			m_view.Invalidate(true);
		}

		private void comboBoxDetection_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			this.AdjustVisibilities();
		}

		private void buttonEditVisibleIf_Click(object sender, EventArgs e)
		{
			string strCondition = this.textBoxVisibleIf.Text;
			DCSDEV.DCSDesigner.DCSFormulaDesigner.CallFormulaBuilder(this.m_view.m_mainWin.m_AllDBFieldNames, ref strCondition, DCSFormulaDesigner.FormulaModeType.SQL_IF);
			if (strCondition != null) this.textBoxVisibleIf.Text = strCondition;
		}

		private void checkBoxVisibleIf_CheckedChanged(object sender, EventArgs e)
		{
			this.AdjustVisibilities();
		}
	}
}
