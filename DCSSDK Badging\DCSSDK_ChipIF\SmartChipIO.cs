using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;
using System.Runtime.InteropServices;

using DCSDEV.DCSChipIF;

namespace DCSDEV
{
    
    /// <summary>
	/// Summary description for ScanSmartChip.
	/// </summary>
	public class SmartChipIO : System.Windows.Forms.Form
	{
		public enum ChipIOMode { ENCODE, READ_DATA, READ_VERIFY };
		private ChipIOMode m_eChipIOMode;
		public enum ChipStatus { DEVICE_NOT_INITIALIZED, DATA_ERROR, DEVICE_ERROR, OK, CANCEL, AWAITING_READOK, READING_FROM_CHIP, CHIP_IS_READ, AWAITING_WRITEOK, WRITING_TO_CHIP, CHIP_IS_WRIT };
		public ChipStatus m_eChipStatus = ChipStatus.DEVICE_NOT_INITIALIZED;

        private DCSDEV_ChipIF.ChipIFType m_eChipIFType;
        private DCSDEV_ChipIF m_classDCSChipIF;
		private bool m_bEXEType;
		private string m_strDataFileIn = null;
		private string m_strDataFileUsed = null;
		private string m_strChipID;
		private System.Windows.Forms.Label labelMain;
		private System.Windows.Forms.TextBox textBoxTextInChip;
		private System.Windows.Forms.Button buttonClear;
		private System.Windows.Forms.Button buttonCancel;
		private System.Windows.Forms.Button buttonOK;
		private System.Windows.Forms.TextBox textBoxChipID;
		private System.Windows.Forms.Label labelCardID;
		private System.Windows.Forms.Label labelDataFile;
		private System.Windows.Forms.TextBox textBoxDataFile;
		private TextBox textBoxConfigFile;
		private Label label3;
		private TextBox tbChipIOMode;
		private Label label1;
        private TextBox tbChipIFType;
		private Label label4;
		private PictureBox pictureBoxPortrait;
		private System.ComponentModel.IContainer components;

		/// <summary>
		/// Read or write in various Chip IF Types
		/// 
		/// IO Modes are: READ_DATA
		///               READ_VERIFY
		///               ENCODE

		/// What the strDataFile contains depends on the ChipIFType selected in Document Setup
		/// For writing
		/// ChipIFType(s) are: MIFARE_RFID_CHIP_FORMULA - pass Badge.Dat and use chip encoding formula in the badge design
		///                    MIFARE_RFID_CHIP_FILE    - pass a file name
		///                    ICAO_CONTACT_CHIP        - pass Badge.Dat and use first ICAO oblect in badge design for main field of chip.
		///                    MULTIFILE_CONTACT_CHIP(Future) - pass file containing names of multiple data files.
		///                    EDL_RFID_CHIP_FILE		- pass a file name.
		/// 
		/// For reading
		/// ChipIFType(s) are: MIFARE_RFID_CHIP_FORMULA is same as MIFARE_RFID_CHIP_FILE is same as EDL_RFID_CHIP_FILE    
		///                                             - pass an optional file name
		///                    ICAO_CONTACT_CHIP        - pass an optional file which indicates the destination fields and files to read.
		///                    MULTIFILE_CONTACT_CHIP(Future) - pass an optional file which indicates the destination fields and files to read.
		/// 
		/// </summary>
		/// <param name="eChipIOMode">Indicates reading or writing to chip</param>
		/// <param name="strDataFile"></param>
		public SmartChipIO(ChipIOMode eChipIOMode, string strDataFile, bool bStandAlone)
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			ParameterStore ps = new ParameterStore("DCSSDK_Mgt");
			m_eChipIOMode = eChipIOMode;	// indicates reading or writing
			this.tbChipIOMode.Text = m_eChipIOMode.ToString();

			this.textBoxDataFile.Text = m_strDataFileUsed = m_strDataFileIn = strDataFile;
			if (m_strDataFileIn == null)
			{
				m_strDataFileUsed = System.IO.Path.Combine(ps.m_strDCSInstallDirectory, "_TempChip.Dat");
				if (System.IO.File.Exists(m_strDataFileUsed)) System.IO.File.Delete(m_strDataFileUsed);
				this.textBoxDataFile.Text = "default (" + m_strDataFileUsed + ")";
			}

			this.pictureBoxPortrait.Visible = false;
			this.textBoxTextInChip.Text = "";
			m_eChipStatus = ChipStatus.DEVICE_NOT_INITIALIZED;

            // get chip IF type
			m_eChipIFType = (DCSDEV.DCSChipIF.DCSDEV_ChipIF.ChipIFType)ps.GetIntParameter("ChipIFType", 0);
			this.tbChipIFType.Text = m_eChipIFType.ToString();
			switch (m_eChipIFType)
			{
				case DCSDEV_ChipIF.ChipIFType.EDL_RFID_CHIP_FILE:
				case DCSDEV_ChipIF.ChipIFType.MIFARE_RFID_CHIP_FILE:
					m_bEXEType = false;
					this.labelDataFile.Text = "Data file:";
					break;
				case DCSDEV_ChipIF.ChipIFType.MIFARE_RFID_CHIP_FORMULA:
					m_bEXEType = false;
					this.labelDataFile.Text = "Badge data file:";
					break;
				case DCSDEV_ChipIF.ChipIFType.ICAO_CONTACT_CHIP:
					m_bEXEType = true;
					if (m_eChipIOMode == ChipIOMode.ENCODE)
						this.labelDataFile.Text = "Badge data file:";
					else
						this.labelDataFile.Text = "Data directory file:";
					break;
				case DCSDEV_ChipIF.ChipIFType.MULTIFILE_CONTACT_CHIP:
					m_bEXEType = true;
					this.labelDataFile.Text = "Data directory file:";
					break;
			}

            // initialize interface to the DCS.SDK level chip interface routine.
			m_classDCSChipIF = new DCSDEV_ChipIF(m_eChipIFType, m_eChipIOMode, m_strDataFileUsed, bStandAlone);
			if (!m_classDCSChipIF.IsDataInited)
			{
				m_eChipStatus = ChipStatus.DATA_ERROR;
			}
			else if (!m_classDCSChipIF.IsDeviceInited)
			{
				m_eChipStatus = ChipStatus.DEVICE_ERROR;
			}
			else
			{
				if (m_eChipIOMode == ChipIOMode.ENCODE)
				{
					System.IO.StreamReader stream = new System.IO.StreamReader(m_strDataFileUsed);
					this.textBoxTextInChip.Text = stream.ReadToEnd();
					stream.Close();
					m_eChipStatus = ChipStatus.AWAITING_WRITEOK;
				}
				else    // ChipIOMode.READ_DATA, ChipIOMode.READ_VERIFY
				{
					m_eChipStatus = ChipStatus.AWAITING_READOK;
				}
			}

			this.textBoxConfigFile.Text = this.m_classDCSChipIF.ConfigFileName;
			this.AdjustVisibilities();
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if (this.pictureBoxPortrait.Image != null)
				{
					pictureBoxPortrait.Image.Dispose();
					pictureBoxPortrait.Image = null;
				}
				if (components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(SmartChipIO));
			this.labelMain = new System.Windows.Forms.Label();
			this.textBoxTextInChip = new System.Windows.Forms.TextBox();
			this.buttonClear = new System.Windows.Forms.Button();
			this.buttonCancel = new System.Windows.Forms.Button();
			this.buttonOK = new System.Windows.Forms.Button();
			this.textBoxChipID = new System.Windows.Forms.TextBox();
			this.labelCardID = new System.Windows.Forms.Label();
			this.labelDataFile = new System.Windows.Forms.Label();
			this.textBoxDataFile = new System.Windows.Forms.TextBox();
			this.textBoxConfigFile = new System.Windows.Forms.TextBox();
			this.label3 = new System.Windows.Forms.Label();
			this.tbChipIOMode = new System.Windows.Forms.TextBox();
			this.label1 = new System.Windows.Forms.Label();
			this.tbChipIFType = new System.Windows.Forms.TextBox();
			this.label4 = new System.Windows.Forms.Label();
			this.pictureBoxPortrait = new System.Windows.Forms.PictureBox();
			((System.ComponentModel.ISupportInitialize)(this.pictureBoxPortrait)).BeginInit();
			this.SuspendLayout();
			// 
			// labelMain
			// 
			resources.ApplyResources(this.labelMain, "labelMain");
			this.labelMain.Name = "labelMain";
			// 
			// textBoxTextInChip
			// 
			resources.ApplyResources(this.textBoxTextInChip, "textBoxTextInChip");
			this.textBoxTextInChip.Name = "textBoxTextInChip";
			// 
			// buttonClear
			// 
			resources.ApplyResources(this.buttonClear, "buttonClear");
			this.buttonClear.Name = "buttonClear";
			this.buttonClear.Click += new System.EventHandler(this.buttonClear_Click);
			// 
			// buttonCancel
			// 
			resources.ApplyResources(this.buttonCancel, "buttonCancel");
			this.buttonCancel.Name = "buttonCancel";
			this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
			// 
			// buttonOK
			// 
			resources.ApplyResources(this.buttonOK, "buttonOK");
			this.buttonOK.Name = "buttonOK";
			this.buttonOK.Click += new System.EventHandler(this.buttonOK_Click);
			// 
			// textBoxChipID
			// 
			this.textBoxChipID.BackColor = System.Drawing.SystemColors.Control;
			this.textBoxChipID.BorderStyle = System.Windows.Forms.BorderStyle.None;
			resources.ApplyResources(this.textBoxChipID, "textBoxChipID");
			this.textBoxChipID.Name = "textBoxChipID";
			// 
			// labelCardID
			// 
			resources.ApplyResources(this.labelCardID, "labelCardID");
			this.labelCardID.Name = "labelCardID";
			// 
			// labelDataFile
			// 
			resources.ApplyResources(this.labelDataFile, "labelDataFile");
			this.labelDataFile.Name = "labelDataFile";
			// 
			// textBoxDataFile
			// 
			this.textBoxDataFile.BorderStyle = System.Windows.Forms.BorderStyle.None;
			resources.ApplyResources(this.textBoxDataFile, "textBoxDataFile");
			this.textBoxDataFile.Name = "textBoxDataFile";
			this.textBoxDataFile.ReadOnly = true;
			// 
			// textBoxConfigFile
			// 
			this.textBoxConfigFile.BorderStyle = System.Windows.Forms.BorderStyle.None;
			resources.ApplyResources(this.textBoxConfigFile, "textBoxConfigFile");
			this.textBoxConfigFile.Name = "textBoxConfigFile";
			this.textBoxConfigFile.ReadOnly = true;
			// 
			// label3
			// 
			resources.ApplyResources(this.label3, "label3");
			this.label3.Name = "label3";
			// 
			// tbChipIOMode
			// 
			this.tbChipIOMode.BorderStyle = System.Windows.Forms.BorderStyle.None;
			resources.ApplyResources(this.tbChipIOMode, "tbChipIOMode");
			this.tbChipIOMode.Name = "tbChipIOMode";
			this.tbChipIOMode.ReadOnly = true;
			// 
			// label1
			// 
			resources.ApplyResources(this.label1, "label1");
			this.label1.Name = "label1";
			// 
			// tbChipIFType
			// 
			this.tbChipIFType.BorderStyle = System.Windows.Forms.BorderStyle.None;
			resources.ApplyResources(this.tbChipIFType, "tbChipIFType");
			this.tbChipIFType.Name = "tbChipIFType";
			this.tbChipIFType.ReadOnly = true;
			// 
			// label4
			// 
			resources.ApplyResources(this.label4, "label4");
			this.label4.Name = "label4";
			// 
			// pictureBoxPortrait
			// 
			resources.ApplyResources(this.pictureBoxPortrait, "pictureBoxPortrait");
			this.pictureBoxPortrait.Name = "pictureBoxPortrait";
			this.pictureBoxPortrait.TabStop = false;
			// 
			// SmartChipIO
			// 
			resources.ApplyResources(this, "$this");
			this.Controls.Add(this.pictureBoxPortrait);
			this.Controls.Add(this.tbChipIFType);
			this.Controls.Add(this.label4);
			this.Controls.Add(this.tbChipIOMode);
			this.Controls.Add(this.label1);
			this.Controls.Add(this.textBoxConfigFile);
			this.Controls.Add(this.label3);
			this.Controls.Add(this.textBoxDataFile);
			this.Controls.Add(this.labelDataFile);
			this.Controls.Add(this.labelCardID);
			this.Controls.Add(this.textBoxChipID);
			this.Controls.Add(this.labelMain);
			this.Controls.Add(this.textBoxTextInChip);
			this.Controls.Add(this.buttonClear);
			this.Controls.Add(this.buttonCancel);
			this.Controls.Add(this.buttonOK);
			this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
			this.MaximizeBox = false;
			this.MinimizeBox = false;
			this.Name = "SmartChipIO";
			this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
			this.TopMost = true;
			((System.ComponentModel.ISupportInitialize)(this.pictureBoxPortrait)).EndInit();
			this.ResumeLayout(false);
			this.PerformLayout();

		}
		#endregion

		private void AdjustVisibilities()
		{
			switch (m_eChipStatus)
			{
				case ChipStatus.DEVICE_NOT_INITIALIZED:
					this.labelMain.Text = "Initializing Chip Device";
					this.buttonCancel.Visible = true;
					this.buttonOK.Visible = false;
					this.buttonClear.Visible = false;
					this.textBoxTextInChip.Visible = false;
					break;
				case ChipStatus.DATA_ERROR:
					this.pictureBoxPortrait.Visible = false;
					if (m_classDCSChipIF.LastErrorString != null)
						this.labelMain.Text = "Check Input Data\r\n" + m_classDCSChipIF.LastErrorString;
					else
						this.labelMain.Text = "Check Input Data";
					this.buttonCancel.Visible = true;
					this.buttonOK.Visible = false;
					this.buttonClear.Visible = false;
					this.textBoxTextInChip.Visible = false;
					break;
				case ChipStatus.DEVICE_ERROR:
					this.pictureBoxPortrait.Visible = false;
					if (m_classDCSChipIF.LastErrorString != null)
						this.labelMain.Text = "Check Chip Device\r\n" + m_classDCSChipIF.LastErrorString;
					else
						this.labelMain.Text = "Check Chip Device";
					this.buttonCancel.Visible = true;
					this.buttonOK.Visible = false;
					//this.buttonClear.Visible = false;
					this.buttonClear.Visible = true;
					this.textBoxTextInChip.Visible = false;
					break;
				case ChipStatus.AWAITING_READOK:
					this.labelMain.Text = "Click OK when ready to read the chip";
					this.buttonCancel.Visible = true;
					this.buttonOK.Visible = true;
					this.buttonClear.Visible = false;
					this.textBoxTextInChip.Visible = false;
					this.buttonOK.Focus();
					break;
				case ChipStatus.READING_FROM_CHIP:
					this.labelMain.Text = "Reading - please wait";
					this.buttonCancel.Visible = true;
					if (m_bEXEType) this.buttonCancel.Visible = false;
					this.buttonOK.Visible = false;
					this.buttonClear.Visible = false;
					this.textBoxTextInChip.Visible = false;
					break;
				case ChipStatus.CHIP_IS_READ:
					if (m_eChipIOMode == ChipIOMode.READ_DATA)
					{
						this.labelMain.Text = "Chip is read OK";
						this.buttonCancel.Visible = false;
					}
					else
					{
						this.labelMain.Text = "Click OK to accept contents read";
						this.buttonCancel.Visible = true;
					}
					this.buttonOK.Visible = true;
					this.buttonClear.Visible = true;
					this.textBoxTextInChip.Visible = true;
					this.buttonOK.Focus();
					{
						bool bReading = (m_eChipIOMode == ChipIOMode.READ_DATA
									|| m_eChipIOMode == ChipIOMode.READ_VERIFY);
						if (m_eChipIFType == DCSDEV_ChipIF.ChipIFType.ICAO_CONTACT_CHIP && bReading)
							this.pictureBoxPortrait.Visible = true;
					}
					break;
				case ChipStatus.AWAITING_WRITEOK:
					this.labelMain.Text = "Click OK when ready to write the chip";
					this.buttonCancel.Visible = true;
					this.buttonOK.Visible = true;
					this.buttonClear.Visible = false;
					this.textBoxTextInChip.Visible = true;
					this.buttonOK.Focus();
					break;
				case ChipStatus.WRITING_TO_CHIP:
					this.labelMain.Text = "Writing - please wait";
					this.buttonCancel.Visible = true;
					if (m_bEXEType) this.buttonCancel.Visible = false;
					this.buttonOK.Visible = false;
					this.buttonClear.Visible = false;
					this.textBoxTextInChip.Visible = true;
					break;
				case ChipStatus.CHIP_IS_WRIT:
					this.labelMain.Text = "Chip is written - Click OK";
					this.buttonCancel.Visible = false;
					this.buttonOK.Visible = true;
					this.buttonClear.Visible = false;
					this.textBoxTextInChip.Visible = true;
					this.buttonOK.Focus();
					break;
				default:
					this.labelMain.Text = "Software Error";
					break;
			}
			Application.DoEvents();
		}

		private void buttonClear_Click(object sender, System.EventArgs e)
		{
			this.textBoxChipID.Text = null;
			if (this.pictureBoxPortrait.Image != null)
			{
				this.pictureBoxPortrait.Image.Dispose();
				this.pictureBoxPortrait.Image = null;
			}
			this.textBoxTextInChip.Text = "";

			m_classDCSChipIF.DCSDEV_ReInitDevice();

			if (!m_classDCSChipIF.IsDataInited)
			{
				m_eChipStatus = ChipStatus.DATA_ERROR;
				this.AdjustVisibilities();
				return;
			}
			if (!m_classDCSChipIF.IsDeviceInited)
			{
				m_eChipStatus = ChipStatus.DEVICE_ERROR;
				this.AdjustVisibilities();
				return;
			}

			if (m_eChipIOMode == ChipIOMode.ENCODE)
			{
				System.IO.StreamReader stream = new System.IO.StreamReader(m_strDataFileUsed);
				this.textBoxTextInChip.Text = stream.ReadToEnd();
				stream.Close();
				m_eChipStatus = ChipStatus.AWAITING_WRITEOK;
			}
			else    //  ChipIOMode.READ_DATA || ChipIOMode.READ_VERIFY
			{
				m_eChipStatus = ChipStatus.AWAITING_READOK;
			}
			this.AdjustVisibilities();
		}

		private void buttonOK_Click(object sender, System.EventArgs e)
		{
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.EncodeChips, true)) return;

			if (m_eChipIOMode == ChipIOMode.ENCODE)
			{
				// writing
				if (m_eChipStatus == ChipStatus.AWAITING_WRITEOK)
				{
					this.OK_WriteToChip();
				}
				else if (m_eChipStatus == ChipStatus.CHIP_IS_WRIT)
				{
					m_eChipStatus = ChipStatus.OK;
					this.Close();
				}
			}
			else if (m_eChipIOMode == ChipIOMode.READ_DATA || m_eChipIOMode == ChipIOMode.READ_VERIFY)
			{
				// reading
				if (m_eChipStatus == ChipStatus.AWAITING_READOK)
				{
					this.OK_ReadFromChip();
				}
				else if (m_eChipStatus == ChipStatus.CHIP_IS_READ)
				{
					m_eChipStatus = ChipStatus.OK;
					this.Close();
				}
			}
			else return;
		}

		// Output: m_strChipID, m_eChipStatus, this.textBoxChipID.Text
		public void OK_ReadFromChip()
		{
			string strChipID = null;
			bool bRet;
			if (this.pictureBoxPortrait.Image != null)
			{
				this.pictureBoxPortrait.Image.Dispose();
				this.pictureBoxPortrait.Image = null;
			}
			this.textBoxChipID.Text = null;

			m_eChipStatus = ChipStatus.READING_FROM_CHIP;
			this.AdjustVisibilities();
			if (m_eChipIFType == DCSDEV_ChipIF.ChipIFType.MIFARE_RFID_CHIP_FORMULA
				|| m_eChipIFType == DCSDEV_ChipIF.ChipIFType.MIFARE_RFID_CHIP_FILE
				|| m_eChipIFType == DCSDEV_ChipIF.ChipIFType.EDL_RFID_CHIP_FILE)
			{
				if (m_eChipIFType == DCSDEV_ChipIF.ChipIFType.EDL_RFID_CHIP_FILE)
				{
					bRet = m_classDCSChipIF.EDLChip2File(out strChipID);
				}
				else
				{
					bRet = m_classDCSChipIF.Chip2File(out strChipID);
				}

				if (!bRet)
				{
					m_eChipStatus = ChipStatus.DEVICE_ERROR;
				}
				else
				{
					m_eChipStatus = ChipStatus.CHIP_IS_READ;
					m_strChipID = strChipID;
					this.textBoxChipID.Text = m_strChipID;

					if (System.IO.File.Exists(m_strDataFileUsed))
					{
						System.IO.StreamReader stream = new System.IO.StreamReader(m_strDataFileUsed);
						this.textBoxTextInChip.Text = stream.ReadToEnd();
						stream.Close();
					}
				}
			}
			else if (m_eChipIFType == DCSDEV_ChipIF.ChipIFType.ICAO_CONTACT_CHIP)
			{
				bRet = m_classDCSChipIF.Chip2ICAOFile(out strChipID);

				if (!bRet)
				{
					m_eChipStatus = ChipStatus.DEVICE_ERROR;
				}
				else
				{
					m_eChipStatus = ChipStatus.CHIP_IS_READ;
					m_strChipID = strChipID;
					this.textBoxChipID.Text = m_strChipID;

					System.IO.StreamReader stream = new System.IO.StreamReader(m_classDCSChipIF.ICAOTextFile);
					this.textBoxTextInChip.Text = stream.ReadToEnd();
					stream.Close();

					if (m_classDCSChipIF.ICAOPortraitFile != null && System.IO.File.Exists(m_classDCSChipIF.ICAOPortraitFile))
					{
						// display the portrait
						System.Drawing.Bitmap bitmap = null;
						try
						{
							bitmap = DCSDEV.DCSDatabaseIF.OpenImageFile(m_classDCSChipIF.ICAOPortraitFile);
						}
						catch (Exception ex)
						{
							DCSDEV.DCSMsg.Show("error reading scanned portrait.", ex);
							bitmap = null;
						}
						if (bitmap != null)
						{
							this.pictureBoxPortrait.Image = bitmap;		// utilizes the new vs2005 option to preserve aspect ratio, so I dont need to use my own code.
							this.pictureBoxPortrait.Visible = true;
						}
					}
				}
			}
			else if (m_eChipIFType == DCSDEV_ChipIF.ChipIFType.MULTIFILE_CONTACT_CHIP)
			{
				bRet = m_classDCSChipIF.Chip2MultiFile(out strChipID);
				if (!bRet)
				{
					m_eChipStatus = ChipStatus.DEVICE_ERROR;
				}
				else
				{
					m_eChipStatus = ChipStatus.CHIP_IS_READ;
					m_strChipID = strChipID;
					this.textBoxChipID.Text = m_strChipID;
				}
			}

			this.AdjustVisibilities();
			//this.Cursor = cursorSave;
			Application.DoEvents();
		}

		// Output: m_strChipID, m_eChipStatus
		public void OK_WriteToChip()
		{
            bool bRet;
			string strChipID = null;
			this.textBoxChipID.Text = null;

			m_eChipStatus = ChipStatus.WRITING_TO_CHIP;
			this.AdjustVisibilities();
			if (m_eChipIFType == DCSDEV_ChipIF.ChipIFType.MIFARE_RFID_CHIP_FORMULA
				|| m_eChipIFType == DCSDEV_ChipIF.ChipIFType.MIFARE_RFID_CHIP_FILE
				|| m_eChipIFType == DCSDEV_ChipIF.ChipIFType.EDL_RFID_CHIP_FILE)
			{
				if (m_eChipIFType == DCSDEV_ChipIF.ChipIFType.EDL_RFID_CHIP_FILE)
					bRet = m_classDCSChipIF.File2EDLChip(out strChipID);
				else
					bRet = m_classDCSChipIF.File2Chip(out strChipID);

				if (!bRet)
				{
					m_eChipStatus = ChipStatus.DEVICE_ERROR;
				}
				else
				{
					m_eChipStatus = ChipStatus.CHIP_IS_WRIT;
					m_strChipID = strChipID;
					this.textBoxChipID.Text = m_strChipID;

					System.IO.StreamReader stream = new System.IO.StreamReader(m_strDataFileUsed);
					this.textBoxTextInChip.Text = stream.ReadToEnd();
					stream.Close();
				}
			}
			else if (m_eChipIFType == DCSDEV_ChipIF.ChipIFType.ICAO_CONTACT_CHIP)
			{
				bRet = m_classDCSChipIF.ICAOFile2Chip(out strChipID);
				if (!bRet)
				{
					m_eChipStatus = ChipStatus.DEVICE_ERROR;
				}
				else
				{
					m_eChipStatus = ChipStatus.CHIP_IS_WRIT;
					m_strChipID = strChipID;
					this.textBoxChipID.Text = m_strChipID;
				}
			}
			else if (m_eChipIFType == DCSDEV_ChipIF.ChipIFType.MULTIFILE_CONTACT_CHIP)
			{
				bRet = m_classDCSChipIF.MultiFile2Chip(out strChipID);
				if (!bRet)
				{
					m_eChipStatus = ChipStatus.DEVICE_ERROR;
				}
				else
				{
					m_eChipStatus = ChipStatus.CHIP_IS_WRIT;
					m_strChipID = strChipID;
					this.textBoxChipID.Text = m_strChipID;
				}
			}

			this.AdjustVisibilities();
			//this.Cursor = cursorSave;
			Application.DoEvents();
		}

		private void buttonCancel_Click(object sender, System.EventArgs e)
		{
			if (this.pictureBoxPortrait.Image != null)
			{
				this.pictureBoxPortrait.Image.Dispose();
				this.pictureBoxPortrait.Image = null;
			}
			this.textBoxTextInChip.Text = "";
			m_eChipStatus = ChipStatus.CANCEL;
			this.Close();
		}

		public string TextReadFromChip
		{
			get { return this.textBoxTextInChip.Text; }
		}

		public string GetChipID
		{
			get { return m_strChipID; }
		}

		public ChipStatus GetStatus
		{
			get { return m_eChipStatus; }
		}

		private void ShowFinger(string filename)
		{
			try
			{
				System.Diagnostics.Process proc = new System.Diagnostics.Process();
				proc.StartInfo.FileName = "C:\\DCSApp\\WSQViewer.exe";
				proc.StartInfo.Arguments = "" + filename + "";
				proc.Start();
				proc.WaitForExit(5000); // wait to finish
				//SYH find out if there was success.
				proc.Kill();
				proc.Dispose();
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message);
			}
		}
	}
}
