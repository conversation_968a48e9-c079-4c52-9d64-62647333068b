﻿# How to Fix the Missing References Issue

## Problem Identified
The DCSDDEServer project is failing to build because it cannot find these DLL files:
- `D:\repos_D\SDS Collection\DCSSDK Badging\DCSSDK_BadgingMgt\bin\Debug\DCSSDK_BadgingMgt.dll`
- `D:\repos_D\SDS Collection\DCSSDK Badging\DCSSDK_CaptureMgt\bin\Debug\DCSSDK_CaptureMgt.dll`

The project has project references to these projects, but they don't exist in the specified locations.

## Solution Implemented
I've made the following changes to fix the issue:

1. Created a `lib` folder in your DCSDDEServer project directory
2. Copied the following DLLs from the components folder to the lib folder:
   - `DCSSDK_BadgingMgt.dll`
   - `DCSSDK_CaptureMgt.dll`
   - `DCSSDK_Utilities.dll`
3. Created and executed a PowerShell script `UpdateProjectReferences.ps1` to:
   - Add direct references to these DLLs in the lib folder
   - Remove the project references to the missing projects
   - Create a backup of your original project file as `DCSDDEServer.csproj.original`

## Steps to Complete the Fix

For the changes to take effect, you need to:

1. **Close Visual Studio** completely
2. **Reopen** your solution in Visual Studio
3. **Build** the project

If you still encounter build errors after following these steps, please:

1. Check if the DLLs exist in the `lib` folder
2. Verify that the project file was properly updated
3. Try manually editing the project file to use the DLL references instead of project references

## Long-term Recommendations

To prevent this issue in the future, consider:

1. **Using NuGet packages** for these dependencies
2. **Restoring the missing projects** in the correct locations
3. **Updating your build pipeline** to ensure all dependencies are properly built

If you need to revert these changes, you can restore the original project file from the backup created at `DCSDDEServer.csproj.original`.