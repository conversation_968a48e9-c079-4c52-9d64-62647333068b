using System;
using System.Collections;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Windows.Forms;
using System.IO;

using DCSDEV;

namespace DCSDEV.PrintProperties
{
	/// <summary>
	/// Summary description for PrinterTypeInstanceControl.
	/// </summary>
	internal class PrinterTypeInstanceControl : System.Windows.Forms.UserControl
	{
		private int m_iUnits = 0;
        private DCSDEV.PrintProperties.PrinterTypeDatum m_bcDatum;
		private int m_indexDatum;

		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.TextBox tbPrinterTypeName;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.ComboBox cbSelectedPrinter;
		private System.Windows.Forms.CheckBox checkPrintBackground;
		private System.Windows.Forms.Label labelDefaultComment;
		private System.Windows.Forms.CheckBox checkMultiplePerSheet;
		private System.Windows.Forms.TextBox tbYOffset;
		private System.Windows.Forms.TextBox tbXOffset;
		private System.Windows.Forms.Label label5;
		private System.Windows.Forms.Label label4;
		private System.Windows.Forms.GroupBox groupBoxBadgeOffset;
		private System.Windows.Forms.Button buttonSheetLayout;
		private System.Windows.Forms.CheckBox checkLandscape;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.ComboBox comboBoxUnits;
		private System.Windows.Forms.Label labelUnits;
		private System.Windows.Forms.CheckBox checkBoxMirrorImage;
		private System.Windows.Forms.CheckBox checkBoxHasKpanel;
		private System.Windows.Forms.Button buttonMagStripe;
		private System.Windows.Forms.CheckBox checkMagStripe;
		private System.Windows.Forms.TextBox textBoxBarcodeFeatureH;
		private System.Windows.Forms.TextBox textBoxBarcodeFeatureW;
		private System.Windows.Forms.Label labelBarcodePrintResolution;
		private System.Windows.Forms.TextBox textBoxBarcodePrintResolution;
		private System.Windows.Forms.Label label6;
		private System.Windows.Forms.Label label7;
		private System.Windows.Forms.TextBox tbHDefault;
		private System.Windows.Forms.TextBox tbWDefault;
		private System.Windows.Forms.Label label8;
		private System.Windows.Forms.Label label9;
		private CheckBox checkBoxPrinterCanEncode;
		private CheckBox checkBoxEncodeChipWhenPrinting;
		private CheckBox checkBoxGetChipIDWhenPrinting;
		/// <summary> 
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		internal PrinterTypeInstanceControl()
		{
			// This call is required by the Windows.Forms Form Designer.
			InitializeComponent();

			// TODO: Add any initialization after the InitializeComponent call

			// Add list of installed printers found to the combo box.
			string strPrinter;
			this.cbSelectedPrinter.Items.Clear();
			this.cbSelectedPrinter.Items.Add("Always Ask");
			this.cbSelectedPrinter.Items.Add("Windows Default Printer");
			for (int i = 0; i < System.Drawing.Printing.PrinterSettings.InstalledPrinters.Count; i++)
			{
				strPrinter = System.Drawing.Printing.PrinterSettings.InstalledPrinters[i];
				this.cbSelectedPrinter.Items.Add(strPrinter);
			}
			cbSelectedPrinter.SelectedIndex = 0;
			m_bcDatum = null;
			m_indexDatum = 0;

            if (!DCSLicensing.IsLicensedOK(LicensedFeatures.TwoDBarcodes, false))
            {
                this.textBoxBarcodeFeatureH.Visible = false;
                this.textBoxBarcodeFeatureW.Visible = false;
                this.labelBarcodePrintResolution.Visible = false;
                this.textBoxBarcodePrintResolution.Visible = false;
            }
		}

		/// <summary> 
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

        internal void GetPrinterTypeDatum(ref DCSDEV.PrintProperties.PrinterTypeDatum bcDatum)
		{
			bcDatum.m_PrinterTypeName = this.tbPrinterTypeName.Text;
			bcDatum.m_SelectedPrinterName = this.cbSelectedPrinter.Text;
			bcDatum.m_IfPrintBackground = this.checkPrintBackground.Checked;
			bcDatum.m_IfMultiPerSheet = this.checkMultiplePerSheet.Checked;
			bcDatum.m_IfLandscape = this.checkLandscape.Checked;
			bcDatum.m_IfMagStripe = this.checkMagStripe.Checked;
            bcDatum.m_IfKpanel = this.checkBoxHasKpanel.Checked;
            bcDatum.m_IfMirror = checkBoxMirrorImage.Checked;
			bcDatum.m_2DBarPrintResolution = Convert.ToInt32(this.textBoxBarcodePrintResolution.Text);
			bcDatum.m_2DBarFeatureW = Convert.ToInt32(this.textBoxBarcodeFeatureW.Text);
			bcDatum.m_2DBarFeatureH = Convert.ToInt32(this.textBoxBarcodeFeatureH.Text);
			if (this.comboBoxUnits.SelectedIndex == 0)	// Inch
			{
				bcDatum.m_OffsetX = (int)(Convert.ToDouble(this.tbXOffset.Text) * 100.0);
				bcDatum.m_OffsetY = (int)(Convert.ToDouble(this.tbYOffset.Text) * 100.0);
				bcDatum.m_DefaultW = (int)(Convert.ToDouble(this.tbWDefault.Text) * 100.0);
				bcDatum.m_DefaultH = (int)(Convert.ToDouble(this.tbHDefault.Text) * 100.0);
			}
			else	// MM
			{
				bcDatum.m_OffsetX = (int)(Convert.ToDouble(this.tbXOffset.Text) * 100.0 / 25.4 );
				bcDatum.m_OffsetY = (int)(Convert.ToDouble(this.tbYOffset.Text) * 100.0 / 25.4 );
				bcDatum.m_DefaultW = (int)(Convert.ToDouble(this.tbWDefault.Text) * 100.0 / 25.4 );
				bcDatum.m_DefaultH = (int)(Convert.ToDouble(this.tbHDefault.Text) * 100.0 / 25.4 );
			}
			bcDatum.m_bPrinterHasChipEncoder = this.checkBoxPrinterCanEncode.Checked;
			bcDatum.m_bEncodeChipWhenPrinting = this.checkBoxEncodeChipWhenPrinting.Checked;
			bcDatum.m_bScanChipIDWhenPrinting = this.checkBoxGetChipIDWhenPrinting.Checked;
		}

        internal void SetPrinterTypeDatum(DCSDEV.PrintProperties.PrinterTypeDatum bcDatum, int index)
		{
			m_bcDatum = bcDatum;
			m_indexDatum = index;

			this.tbPrinterTypeName.Text = bcDatum.m_PrinterTypeName;
			this.cbSelectedPrinter.Text = bcDatum.m_SelectedPrinterName;
			this.checkPrintBackground.Checked = bcDatum.m_IfPrintBackground;
			this.checkLandscape.Checked = bcDatum.m_IfLandscape;
			this.checkBoxMirrorImage.Checked = bcDatum.m_IfMirror;
			this.textBoxBarcodePrintResolution.Text = bcDatum.m_2DBarPrintResolution.ToString();
			this.textBoxBarcodeFeatureW.Text = bcDatum.m_2DBarFeatureW.ToString();
			this.textBoxBarcodeFeatureH.Text = bcDatum.m_2DBarFeatureH.ToString();

			if (this.comboBoxUnits.SelectedIndex == 0)	// Inch
			{
				this.tbXOffset.Text = ((double)(bcDatum.m_OffsetX) / 100.0).ToString("0.000");
				this.tbYOffset.Text = ((double)(bcDatum.m_OffsetY) / 100.0).ToString("0.000");
				this.tbWDefault.Text = ((double)(bcDatum.m_DefaultW) / 100.0).ToString("0.000");
				this.tbHDefault.Text = ((double)(bcDatum.m_DefaultH) / 100.0).ToString("0.000");
			}
			else		// MM
			{
				this.tbXOffset.Text = ((double)(bcDatum.m_OffsetX) * 0.254).ToString("0.000");
				this.tbYOffset.Text = ((double)(bcDatum.m_OffsetY) * 0.254).ToString("0.000");
				this.tbWDefault.Text = ((double)(bcDatum.m_DefaultW) * 0.254).ToString("0.000");
				this.tbHDefault.Text = ((double)(bcDatum.m_DefaultH) * 0.254).ToString("0.000");
			}
            this.checkBoxPrinterCanEncode.Visible = true;
            this.checkBoxPrinterCanEncode.Checked = bcDatum.m_bPrinterHasChipEncoder;
			this.checkBoxEncodeChipWhenPrinting.Checked = bcDatum.m_bEncodeChipWhenPrinting;
			this.checkBoxGetChipIDWhenPrinting.Checked = bcDatum.m_bScanChipIDWhenPrinting;

			//Special handling for first 3 (DCSDEV.PrintProperties.PrinterTypeArray.NUMBASECLASSES) indices
			switch(index)
			{
				case 0:	// cards
					this.labelDefaultComment.Text = "Document designs with all sides less then 4 inches";
					this.checkMultiplePerSheet.Visible = false;
					this.checkMultiplePerSheet.Checked = false;
                    this.buttonSheetLayout.Visible = false;
					this.checkMagStripe.Visible = true;
                    this.checkMagStripe.Checked = bcDatum.m_IfMagStripe;
                    this.checkBoxHasKpanel.Visible = true;
                    this.checkBoxHasKpanel.Checked = bcDatum.m_IfKpanel;

					this.checkBoxPrinterCanEncode.Visible = true;
					this.checkBoxPrinterCanEncode.Checked = bcDatum.m_bPrinterHasChipEncoder;
					this.checkBoxEncodeChipWhenPrinting.Checked = bcDatum.m_bEncodeChipWhenPrinting;
					this.checkBoxGetChipIDWhenPrinting.Checked = bcDatum.m_bScanChipIDWhenPrinting;
					break;
				case 1:	// documents - larger than cards
					this.labelDefaultComment.Text = "Document designs with width or height larger then 4 inches";
                    this.checkMultiplePerSheet.Visible = false;
					this.checkMultiplePerSheet.Checked = false;
                    this.buttonSheetLayout.Visible = false;
                    this.checkMagStripe.Visible = false;
					this.checkMagStripe.Checked = false;
                    this.checkBoxHasKpanel.Visible = false;
                    this.checkBoxHasKpanel.Checked = false;

					this.checkBoxPrinterCanEncode.Visible = false;
					this.checkBoxPrinterCanEncode.Checked = false;
					break;
				case 2:	// multi-badge sheets
					this.labelDefaultComment.Text = "Sheet printing uses this printer type";
					this.checkMultiplePerSheet.Visible = true;
					if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.SheetPrinting))
					{
                        this.checkMultiplePerSheet.Enabled = true;
                        this.checkMultiplePerSheet.Checked = true;
                        this.buttonSheetLayout.Visible = true;
					}
					else
					{
                        this.checkMultiplePerSheet.Enabled = false;
                        this.checkMultiplePerSheet.Checked = false;
                        this.buttonSheetLayout.Visible = false;
					}
					this.checkMagStripe.Visible = false;
					this.checkMagStripe.Checked = false;
                    this.checkBoxHasKpanel.Visible = false;
                    this.checkBoxHasKpanel.Checked = false;

					this.checkBoxPrinterCanEncode.Visible = false;
					this.checkBoxPrinterCanEncode.Checked = false;
					break;
				default:	// other
					this.labelDefaultComment.Text = "Custom printer type";
                    this.checkMultiplePerSheet.Visible = false;
                    this.checkMultiplePerSheet.Checked = false;
                    this.buttonSheetLayout.Visible = false;
					this.checkMagStripe.Visible = true;
					this.checkMagStripe.Checked = bcDatum.m_IfMagStripe;
                    this.checkBoxHasKpanel.Visible = true;
                    this.checkBoxHasKpanel.Checked = bcDatum.m_IfKpanel;

					this.checkBoxPrinterCanEncode.Visible = true;
					this.checkBoxPrinterCanEncode.Checked = bcDatum.m_bPrinterHasChipEncoder;
					this.checkBoxEncodeChipWhenPrinting.Checked = bcDatum.m_bEncodeChipWhenPrinting;
					this.checkBoxGetChipIDWhenPrinting.Checked = bcDatum.m_bScanChipIDWhenPrinting;
					break;
			}
            if (!DCSLicensing.IsLicensedOK(LicensedFeatures.EncodeChips))
            {
                this.checkBoxPrinterCanEncode.Enabled = false;
                this.checkBoxPrinterCanEncode.Checked = false;
            }
            this.checkBoxEncodeChipWhenPrinting.Visible = this.checkBoxGetChipIDWhenPrinting.Visible = this.checkBoxPrinterCanEncode.Checked;

            this.groupBoxBadgeOffset.Visible = !bcDatum.m_IfMultiPerSheet;
			this.buttonSheetLayout.Enabled   = bcDatum.m_IfMultiPerSheet;
			this.buttonMagStripe.Visible     = bcDatum.m_IfMagStripe;

		}

		#region Component Designer generated code
		/// <summary> 
		/// Required method for Designer support - do not modify 
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            this.label1 = new System.Windows.Forms.Label();
            this.tbPrinterTypeName = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.cbSelectedPrinter = new System.Windows.Forms.ComboBox();
            this.checkPrintBackground = new System.Windows.Forms.CheckBox();
            this.labelDefaultComment = new System.Windows.Forms.Label();
            this.checkMultiplePerSheet = new System.Windows.Forms.CheckBox();
            this.groupBoxBadgeOffset = new System.Windows.Forms.GroupBox();
            this.label7 = new System.Windows.Forms.Label();
            this.tbHDefault = new System.Windows.Forms.TextBox();
            this.tbWDefault = new System.Windows.Forms.TextBox();
            this.label8 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.comboBoxUnits = new System.Windows.Forms.ComboBox();
            this.labelUnits = new System.Windows.Forms.Label();
            this.tbYOffset = new System.Windows.Forms.TextBox();
            this.tbXOffset = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.buttonSheetLayout = new System.Windows.Forms.Button();
            this.checkLandscape = new System.Windows.Forms.CheckBox();
            this.label3 = new System.Windows.Forms.Label();
            this.checkBoxMirrorImage = new System.Windows.Forms.CheckBox();
            this.checkBoxHasKpanel = new System.Windows.Forms.CheckBox();
            this.buttonMagStripe = new System.Windows.Forms.Button();
            this.checkMagStripe = new System.Windows.Forms.CheckBox();
            this.textBoxBarcodeFeatureH = new System.Windows.Forms.TextBox();
            this.textBoxBarcodeFeatureW = new System.Windows.Forms.TextBox();
            this.labelBarcodePrintResolution = new System.Windows.Forms.Label();
            this.textBoxBarcodePrintResolution = new System.Windows.Forms.TextBox();
            this.checkBoxPrinterCanEncode = new System.Windows.Forms.CheckBox();
            this.checkBoxEncodeChipWhenPrinting = new System.Windows.Forms.CheckBox();
            this.checkBoxGetChipIDWhenPrinting = new System.Windows.Forms.CheckBox();
            this.groupBoxBadgeOffset.SuspendLayout();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.Location = new System.Drawing.Point(16, 8);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(416, 16);
            this.label1.TabIndex = 0;
            this.label1.Text = "Document designs assigned to this type print with the parameters below.";
            // 
            // tbPrinterTypeName
            // 
            this.tbPrinterTypeName.Location = new System.Drawing.Point(120, 32);
            this.tbPrinterTypeName.Name = "tbPrinterTypeName";
            this.tbPrinterTypeName.Size = new System.Drawing.Size(168, 20);
            this.tbPrinterTypeName.TabIndex = 2;
            this.tbPrinterTypeName.Text = "type";
            this.tbPrinterTypeName.WordWrap = false;
            // 
            // label2
            // 
            this.label2.Location = new System.Drawing.Point(16, 80);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(488, 16);
            this.label2.TabIndex = 2;
            this.label2.Text = "Printer";
            // 
            // cbSelectedPrinter
            // 
            this.cbSelectedPrinter.Items.AddRange(new object[] {
            "Windows Default"});
            this.cbSelectedPrinter.Location = new System.Drawing.Point(16, 96);
            this.cbSelectedPrinter.MaxDropDownItems = 12;
            this.cbSelectedPrinter.Name = "cbSelectedPrinter";
            this.cbSelectedPrinter.Size = new System.Drawing.Size(424, 21);
            this.cbSelectedPrinter.TabIndex = 4;
            this.cbSelectedPrinter.Text = "Windows Default";
            // 
            // checkPrintBackground
            // 
            this.checkPrintBackground.Checked = true;
            this.checkPrintBackground.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkPrintBackground.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.checkPrintBackground.Location = new System.Drawing.Point(264, 168);
            this.checkPrintBackground.Name = "checkPrintBackground";
            this.checkPrintBackground.Size = new System.Drawing.Size(176, 24);
            this.checkPrintBackground.TabIndex = 10;
            this.checkPrintBackground.Text = "Print Background";
            // 
            // labelDefaultComment
            // 
            this.labelDefaultComment.Location = new System.Drawing.Point(16, 56);
            this.labelDefaultComment.Name = "labelDefaultComment";
            this.labelDefaultComment.Size = new System.Drawing.Size(416, 16);
            this.labelDefaultComment.TabIndex = 3;
            this.labelDefaultComment.Text = "xxx";
            // 
            // checkMultiplePerSheet
            // 
            this.checkMultiplePerSheet.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.checkMultiplePerSheet.Location = new System.Drawing.Point(15, 139);
            this.checkMultiplePerSheet.Name = "checkMultiplePerSheet";
            this.checkMultiplePerSheet.Size = new System.Drawing.Size(168, 24);
            this.checkMultiplePerSheet.TabIndex = 6;
            this.checkMultiplePerSheet.Text = "Multiple documents per sheet (use sheet layout)";
            this.checkMultiplePerSheet.Click += new System.EventHandler(this.checkMultiplePerSheet_Click);
            // 
            // groupBoxBadgeOffset
            // 
            this.groupBoxBadgeOffset.Controls.Add(this.label7);
            this.groupBoxBadgeOffset.Controls.Add(this.tbHDefault);
            this.groupBoxBadgeOffset.Controls.Add(this.tbWDefault);
            this.groupBoxBadgeOffset.Controls.Add(this.label8);
            this.groupBoxBadgeOffset.Controls.Add(this.label9);
            this.groupBoxBadgeOffset.Controls.Add(this.label6);
            this.groupBoxBadgeOffset.Controls.Add(this.comboBoxUnits);
            this.groupBoxBadgeOffset.Controls.Add(this.labelUnits);
            this.groupBoxBadgeOffset.Controls.Add(this.tbYOffset);
            this.groupBoxBadgeOffset.Controls.Add(this.tbXOffset);
            this.groupBoxBadgeOffset.Controls.Add(this.label5);
            this.groupBoxBadgeOffset.Controls.Add(this.label4);
            this.groupBoxBadgeOffset.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.groupBoxBadgeOffset.Location = new System.Drawing.Point(448, 8);
            this.groupBoxBadgeOffset.Name = "groupBoxBadgeOffset";
            this.groupBoxBadgeOffset.Size = new System.Drawing.Size(152, 144);
            this.groupBoxBadgeOffset.TabIndex = 13;
            this.groupBoxBadgeOffset.TabStop = false;
            // 
            // label7
            // 
            this.label7.Location = new System.Drawing.Point(8, 96);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(120, 16);
            this.label7.TabIndex = 7;
            this.label7.Text = "default width / height";
            // 
            // tbHDefault
            // 
            this.tbHDefault.Location = new System.Drawing.Point(104, 112);
            this.tbHDefault.Name = "tbHDefault";
            this.tbHDefault.Size = new System.Drawing.Size(40, 20);
            this.tbHDefault.TabIndex = 11;
            this.tbHDefault.Text = "0.000";
            // 
            // tbWDefault
            // 
            this.tbWDefault.Location = new System.Drawing.Point(32, 112);
            this.tbWDefault.Name = "tbWDefault";
            this.tbWDefault.Size = new System.Drawing.Size(40, 20);
            this.tbWDefault.TabIndex = 9;
            this.tbWDefault.Text = "0.000";
            // 
            // label8
            // 
            this.label8.Location = new System.Drawing.Point(80, 112);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(16, 20);
            this.label8.TabIndex = 10;
            this.label8.Text = "H";
            this.label8.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            // 
            // label9
            // 
            this.label9.Location = new System.Drawing.Point(8, 112);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(16, 20);
            this.label9.TabIndex = 8;
            this.label9.Text = "W";
            this.label9.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // label6
            // 
            this.label6.Location = new System.Drawing.Point(8, 48);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(136, 16);
            this.label6.TabIndex = 2;
            this.label6.Text = "print offset";
            // 
            // comboBoxUnits
            // 
            this.comboBoxUnits.Items.AddRange(new object[] {
            "Inch",
            "MM"});
            this.comboBoxUnits.Location = new System.Drawing.Point(8, 16);
            this.comboBoxUnits.Name = "comboBoxUnits";
            this.comboBoxUnits.Size = new System.Drawing.Size(56, 21);
            this.comboBoxUnits.TabIndex = 0;
            this.comboBoxUnits.Text = "Inch";
            this.comboBoxUnits.SelectedIndexChanged += new System.EventHandler(this.comboBoxUnits_SelectedIndexChanged);
            // 
            // labelUnits
            // 
            this.labelUnits.Location = new System.Drawing.Point(80, 16);
            this.labelUnits.Name = "labelUnits";
            this.labelUnits.Size = new System.Drawing.Size(56, 21);
            this.labelUnits.TabIndex = 1;
            this.labelUnits.Text = "units:";
            this.labelUnits.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // tbYOffset
            // 
            this.tbYOffset.Location = new System.Drawing.Point(104, 64);
            this.tbYOffset.Name = "tbYOffset";
            this.tbYOffset.Size = new System.Drawing.Size(40, 20);
            this.tbYOffset.TabIndex = 6;
            this.tbYOffset.Text = "0.000";
            // 
            // tbXOffset
            // 
            this.tbXOffset.Location = new System.Drawing.Point(32, 64);
            this.tbXOffset.Name = "tbXOffset";
            this.tbXOffset.Size = new System.Drawing.Size(40, 20);
            this.tbXOffset.TabIndex = 4;
            this.tbXOffset.Text = "0.000";
            // 
            // label5
            // 
            this.label5.Location = new System.Drawing.Point(80, 64);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(16, 20);
            this.label5.TabIndex = 5;
            this.label5.Text = "Y";
            this.label5.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            // 
            // label4
            // 
            this.label4.Location = new System.Drawing.Point(8, 64);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(16, 20);
            this.label4.TabIndex = 3;
            this.label4.Text = "X";
            this.label4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // buttonSheetLayout
            // 
            this.buttonSheetLayout.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.buttonSheetLayout.Location = new System.Drawing.Point(189, 142);
            this.buttonSheetLayout.Name = "buttonSheetLayout";
            this.buttonSheetLayout.Size = new System.Drawing.Size(56, 24);
            this.buttonSheetLayout.TabIndex = 7;
            this.buttonSheetLayout.Text = "Sheet";
            this.buttonSheetLayout.Click += new System.EventHandler(this.buttonSheetLayout_Click);
            // 
            // checkLandscape
            // 
            this.checkLandscape.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.checkLandscape.Location = new System.Drawing.Point(264, 139);
            this.checkLandscape.Name = "checkLandscape";
            this.checkLandscape.Size = new System.Drawing.Size(186, 24);
            this.checkLandscape.TabIndex = 5;
            this.checkLandscape.Text = "Media orientation is landscape";
            // 
            // label3
            // 
            this.label3.Location = new System.Drawing.Point(18, 28);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(96, 24);
            this.label3.TabIndex = 1;
            this.label3.Text = "Print type";
            this.label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // checkBoxMirrorImage
            // 
            this.checkBoxMirrorImage.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.checkBoxMirrorImage.Location = new System.Drawing.Point(264, 201);
            this.checkBoxMirrorImage.Name = "checkBoxMirrorImage";
            this.checkBoxMirrorImage.Size = new System.Drawing.Size(176, 24);
            this.checkBoxMirrorImage.TabIndex = 11;
            this.checkBoxMirrorImage.Text = "Mirror image";
            // 
            // checkBoxHasKpanel
            // 
            this.checkBoxHasKpanel.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.checkBoxHasKpanel.Location = new System.Drawing.Point(264, 234);
            this.checkBoxHasKpanel.Name = "checkBoxHasKpanel";
            this.checkBoxHasKpanel.Size = new System.Drawing.Size(176, 24);
            this.checkBoxHasKpanel.TabIndex = 12;
            this.checkBoxHasKpanel.Text = "K-panel printing";
            // 
            // buttonMagStripe
            // 
            this.buttonMagStripe.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.buttonMagStripe.Location = new System.Drawing.Point(189, 172);
            this.buttonMagStripe.Name = "buttonMagStripe";
            this.buttonMagStripe.Size = new System.Drawing.Size(56, 24);
            this.buttonMagStripe.TabIndex = 9;
            this.buttonMagStripe.Text = "Encoding";
            this.buttonMagStripe.Click += new System.EventHandler(this.buttonMagStripe_Click);
            // 
            // checkMagStripe
            // 
            this.checkMagStripe.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.checkMagStripe.Location = new System.Drawing.Point(15, 172);
            this.checkMagStripe.Name = "checkMagStripe";
            this.checkMagStripe.Size = new System.Drawing.Size(168, 24);
            this.checkMagStripe.TabIndex = 8;
            this.checkMagStripe.Text = "Mag stripe encoding";
            this.checkMagStripe.Click += new System.EventHandler(this.checkMagStripe_Click);
            // 
            // textBoxBarcodeFeatureH
            // 
            this.textBoxBarcodeFeatureH.Location = new System.Drawing.Point(536, 200);
            this.textBoxBarcodeFeatureH.Name = "textBoxBarcodeFeatureH";
            this.textBoxBarcodeFeatureH.Size = new System.Drawing.Size(24, 20);
            this.textBoxBarcodeFeatureH.TabIndex = 17;
            this.textBoxBarcodeFeatureH.Text = "3";
            // 
            // textBoxBarcodeFeatureW
            // 
            this.textBoxBarcodeFeatureW.Location = new System.Drawing.Point(504, 200);
            this.textBoxBarcodeFeatureW.Name = "textBoxBarcodeFeatureW";
            this.textBoxBarcodeFeatureW.Size = new System.Drawing.Size(24, 20);
            this.textBoxBarcodeFeatureW.TabIndex = 16;
            this.textBoxBarcodeFeatureW.Text = "2";
            // 
            // labelBarcodePrintResolution
            // 
            this.labelBarcodePrintResolution.Location = new System.Drawing.Point(456, 168);
            this.labelBarcodePrintResolution.Name = "labelBarcodePrintResolution";
            this.labelBarcodePrintResolution.Size = new System.Drawing.Size(128, 32);
            this.labelBarcodePrintResolution.TabIndex = 14;
            this.labelBarcodePrintResolution.Text = "2D Barcode resolution - Feature W / H";
            // 
            // textBoxBarcodePrintResolution
            // 
            this.textBoxBarcodePrintResolution.Location = new System.Drawing.Point(456, 200);
            this.textBoxBarcodePrintResolution.Name = "textBoxBarcodePrintResolution";
            this.textBoxBarcodePrintResolution.Size = new System.Drawing.Size(40, 20);
            this.textBoxBarcodePrintResolution.TabIndex = 15;
            this.textBoxBarcodePrintResolution.Text = "300";
            // 
            // checkBoxPrinterCanEncode
            // 
            this.checkBoxPrinterCanEncode.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.checkBoxPrinterCanEncode.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkBoxPrinterCanEncode.Location = new System.Drawing.Point(15, 203);
            this.checkBoxPrinterCanEncode.Name = "checkBoxPrinterCanEncode";
            this.checkBoxPrinterCanEncode.Size = new System.Drawing.Size(192, 16);
            this.checkBoxPrinterCanEncode.TabIndex = 84;
            this.checkBoxPrinterCanEncode.Text = "Printer can encode smart chips";
            this.checkBoxPrinterCanEncode.Click += new System.EventHandler(this.checkBoxPrinterCanEncode_Click);
            // 
            // checkBoxEncodeChipWhenPrinting
            // 
            this.checkBoxEncodeChipWhenPrinting.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.checkBoxEncodeChipWhenPrinting.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkBoxEncodeChipWhenPrinting.Location = new System.Drawing.Point(35, 224);
            this.checkBoxEncodeChipWhenPrinting.Name = "checkBoxEncodeChipWhenPrinting";
            this.checkBoxEncodeChipWhenPrinting.Size = new System.Drawing.Size(192, 16);
            this.checkBoxEncodeChipWhenPrinting.TabIndex = 85;
            this.checkBoxEncodeChipWhenPrinting.Text = "Encode chip when printing";
            // 
            // checkBoxGetChipIDWhenPrinting
            // 
            this.checkBoxGetChipIDWhenPrinting.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.checkBoxGetChipIDWhenPrinting.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkBoxGetChipIDWhenPrinting.Location = new System.Drawing.Point(35, 246);
            this.checkBoxGetChipIDWhenPrinting.Name = "checkBoxGetChipIDWhenPrinting";
            this.checkBoxGetChipIDWhenPrinting.Size = new System.Drawing.Size(192, 16);
            this.checkBoxGetChipIDWhenPrinting.TabIndex = 86;
            this.checkBoxGetChipIDWhenPrinting.Text = "Return chip IDs when printing";
            // 
            // PrinterTypeInstanceControl
            // 
            this.BackColor = System.Drawing.SystemColors.Control;
            this.Controls.Add(this.checkBoxGetChipIDWhenPrinting);
            this.Controls.Add(this.checkBoxEncodeChipWhenPrinting);
            this.Controls.Add(this.checkBoxPrinterCanEncode);
            this.Controls.Add(this.textBoxBarcodeFeatureH);
            this.Controls.Add(this.textBoxBarcodeFeatureW);
            this.Controls.Add(this.labelBarcodePrintResolution);
            this.Controls.Add(this.textBoxBarcodePrintResolution);
            this.Controls.Add(this.checkMagStripe);
            this.Controls.Add(this.buttonMagStripe);
            this.Controls.Add(this.checkBoxHasKpanel);
            this.Controls.Add(this.checkBoxMirrorImage);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.checkLandscape);
            this.Controls.Add(this.buttonSheetLayout);
            this.Controls.Add(this.groupBoxBadgeOffset);
            this.Controls.Add(this.checkMultiplePerSheet);
            this.Controls.Add(this.labelDefaultComment);
            this.Controls.Add(this.checkPrintBackground);
            this.Controls.Add(this.cbSelectedPrinter);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.tbPrinterTypeName);
            this.Controls.Add(this.label1);
            this.Name = "PrinterTypeInstanceControl";
            this.Size = new System.Drawing.Size(608, 267);
            this.groupBoxBadgeOffset.ResumeLayout(false);
            this.groupBoxBadgeOffset.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

		}
		#endregion

		private void checkBoxPrinterCanEncode_Click(object sender, EventArgs e)
		{
			this.checkBoxGetChipIDWhenPrinting.Visible =
				this.checkBoxEncodeChipWhenPrinting.Visible = (this.checkBoxPrinterCanEncode.Checked);

		}

		private void checkMultiplePerSheet_Click(object sender, System.EventArgs e)
		{
			bool bChecked = this.checkMultiplePerSheet.Checked;
			if (bChecked && !DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.SheetPrinting, true))
			{
				this.checkMultiplePerSheet.Checked = false;
				return;
			}

			if (m_indexDatum == 2 && DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.SheetPrinting))
			{
				// MultiplePerSheet Is Locked On if sheet printing is licensed
				this.checkMultiplePerSheet.Checked = true;
				return;
			}

			this.groupBoxBadgeOffset.Visible = !bChecked;
			this.buttonSheetLayout.Enabled = bChecked;
		}

		private void buttonSheetLayout_Click(object sender, System.EventArgs e)
		{
			Rectangle rect = new Rectangle(0,0,850,1100);
			try
			{
				System.Drawing.Printing.PrintDocument pdoc = new System.Drawing.Printing.PrintDocument();
				this.GetPrinterTypeDatum(ref m_bcDatum);
				if (m_bcDatum.m_SelectedPrinterName == null || m_bcDatum.m_SelectedPrinterName == "" || m_bcDatum.m_SelectedPrinterName == "Always Ask")
				{
					System.Windows.Forms.PrintDialog pdlg = new System.Windows.Forms.PrintDialog();
					pdlg.PrinterSettings = new System.Drawing.Printing.PrinterSettings();
					DialogResult result = pdlg.ShowDialog(this);
					if (result == DialogResult.Cancel)
						return;
					pdoc.PrinterSettings = pdlg.PrinterSettings;
				}
				else if (m_bcDatum.m_SelectedPrinterName == "Windows Default Printer")
				{
					pdoc.PrinterSettings.PrinterName = null;
				}
				else // printer config specifes a printer
				{
					pdoc.PrinterSettings.PrinterName = m_bcDatum.m_SelectedPrinterName;
				}
				pdoc.DefaultPageSettings.Landscape = m_bcDatum.m_IfLandscape;
				pdoc.PrinterSettings.DefaultPageSettings.Landscape = m_bcDatum.m_IfLandscape;
				rect = pdoc.PrinterSettings.DefaultPageSettings.Bounds;
				pdoc.Dispose();
			}
			catch (Exception ex)
			{
				DCSMsg.Show("Cannot access selected printer.", ex);
			}

            using (DCSDEV.PrintProperties.SheetLayout sl = new SheetLayout(rect.Width, rect.Height))
            {
                sl.ShowDialog(this);
            }
		}

		private void comboBoxUnits_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if (this.comboBoxUnits.SelectedIndex == m_iUnits) return;
			double dd;
			m_iUnits = this.comboBoxUnits.SelectedIndex;
			if (m_iUnits == 0)	// Convert MM to Inch
			{
				dd = Convert.ToDouble(this.tbXOffset.Text);
				this.tbXOffset.Text = (dd / 25.4).ToString("0.000");
				dd = Convert.ToDouble(this.tbYOffset.Text);
				this.tbYOffset.Text = (dd / 25.4).ToString("0.000");
				dd = Convert.ToDouble(this.tbWDefault.Text);
				this.tbWDefault.Text = (dd / 25.4).ToString("0.000");
				dd = Convert.ToDouble(this.tbHDefault.Text);
				this.tbHDefault.Text = (dd / 25.4).ToString("0.000");
			}
			else	// Convert Inch to MM
			{
				dd = Convert.ToDouble(this.tbXOffset.Text);
				this.tbXOffset.Text = (dd * 25.4).ToString("0.000");
				dd = Convert.ToDouble(this.tbYOffset.Text);
				this.tbYOffset.Text = (dd * 25.4).ToString("0.000");
				dd = Convert.ToDouble(this.tbWDefault.Text);
				this.tbWDefault.Text = (dd * 25.4).ToString("0.000");
				dd = Convert.ToDouble(this.tbHDefault.Text);
				this.tbHDefault.Text = (dd * 25.4).ToString("0.000");
			}
		}

		private void checkMagStripe_Click(object sender, System.EventArgs e)
		{
			this.buttonMagStripe.Visible = this.checkMagStripe.Checked;
		}

		private void buttonMagStripe_Click(object sender, System.EventArgs e)
		{
            using (DCSDEV.PrintProperties.EncoderProperties dlg = new EncoderProperties())
            {
                dlg.MoveDataToControls(m_bcDatum);
                DialogResult result = dlg.ShowDialog(this);
                if (result == DialogResult.OK)
                    dlg.MoveDataFromControls(ref m_bcDatum);
            }
		}

		internal int Units
		{
			get { return this.comboBoxUnits.SelectedIndex; }
			set { this.comboBoxUnits.SelectedIndex = value; }
		}
	}
}
