namespace DCSDEV.DCSDesigner
{
	partial class DCSFormulaAddFunction
	{
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		/// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
		protected override void Dispose(bool disposing)
		{
			if (disposing && (components != null))
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		#region Windows Form Designer generated code

		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			this.buttonCancel = new System.Windows.Forms.Button();
			this.buttonOKay = new System.Windows.Forms.Button();
			this.label1 = new System.Windows.Forms.Label();
			this.listBoxFunctionToAppend = new System.Windows.Forms.ListBox();
			this.SuspendLayout();
			// 
			// buttonCancel
			// 
			this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.buttonCancel.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonCancel.ImeMode = System.Windows.Forms.ImeMode.NoControl;
			this.buttonCancel.Location = new System.Drawing.Point(305, 230);
			this.buttonCancel.Name = "buttonCancel";
			this.buttonCancel.Size = new System.Drawing.Size(96, 24);
			this.buttonCancel.TabIndex = 2;
			this.buttonCancel.Text = "&Cancel";
			this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
			// 
			// buttonOKay
			// 
			this.buttonOKay.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonOKay.ImeMode = System.Windows.Forms.ImeMode.NoControl;
			this.buttonOKay.Location = new System.Drawing.Point(177, 230);
			this.buttonOKay.Name = "buttonOKay";
			this.buttonOKay.Size = new System.Drawing.Size(96, 24);
			this.buttonOKay.TabIndex = 1;
			this.buttonOKay.Text = "&OK";
			this.buttonOKay.Click += new System.EventHandler(this.buttonOK_Click);
			// 
			// label1
			// 
			this.label1.ImeMode = System.Windows.Forms.ImeMode.NoControl;
			this.label1.Location = new System.Drawing.Point(32, 9);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(369, 24);
			this.label1.TabIndex = 14;
			this.label1.Text = "Function to add:";
			this.label1.TextAlign = System.Drawing.ContentAlignment.TopCenter;
			// 
			// listBoxFunctionToAppend
			// 
			this.listBoxFunctionToAppend.FormattingEnabled = true;
			this.listBoxFunctionToAppend.Location = new System.Drawing.Point(35, 39);
			this.listBoxFunctionToAppend.Name = "listBoxFunctionToAppend";
			this.listBoxFunctionToAppend.Size = new System.Drawing.Size(366, 160);
			this.listBoxFunctionToAppend.TabIndex = 15;
			// 
			// DCSFormulaAddFunction
			// 
			this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
			this.CancelButton = this.buttonCancel;
			this.ClientSize = new System.Drawing.Size(443, 266);
			this.Controls.Add(this.listBoxFunctionToAppend);
			this.Controls.Add(this.label1);
			this.Controls.Add(this.buttonCancel);
			this.Controls.Add(this.buttonOKay);
			this.Name = "DCSFormulaAddFunction";
			this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
			this.Text = "DCSFormulaAddFunction";
			this.ResumeLayout(false);

		}

		#endregion

		private System.Windows.Forms.Button buttonCancel;
		private System.Windows.Forms.Button buttonOKay;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.ListBox listBoxFunctionToAppend;
	}
}