<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DCSSDK_CaptureMgt</name>
    </assembly>
    <members>
        <member name="T:DCSSDK.CaptureMgt.AboutCaptureMgt">
            <summary>
            Summary description for AboutCaptureMgt.
            </summary>
        </member>
        <member name="F:DCSSDK.CaptureMgt.AboutCaptureMgt.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:DCSSDK.CaptureMgt.AboutCaptureMgt.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
        </member>
        <member name="M:DCSSDK.CaptureMgt.AboutCaptureMgt.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:DCSSDK.CaptureMgt.DCSSDK_CaptureMgt">
            <summary>
            Summary description for CaptureMgtForm.
            </summary>
        </member>
        <member name="F:DCSSDK.CaptureMgt.DCSSDK_CaptureMgt.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:DCSSDK.CaptureMgt.DCSSDK_CaptureMgt.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
        </member>
        <member name="M:DCSSDK.CaptureMgt.DCSSDK_CaptureMgt.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:DCSSDK.CaptureMgt.DCSSDK_CaptureMgt.Main">
            <summary>
            The main entry point for the application.
            </summary>
        </member>
        <member name="M:DCSSDK.CaptureMgt.DCSSDK_CaptureMgt.SetPortraitCaptureInterface">
            <summary>
            Initialize Dynamic Load Management of portraits to the currently selected portrait device
            </summary>
            <returns>portrait capture class</returns>
        </member>
        <member name="M:DCSSDK.CaptureMgt.DCSSDK_CaptureMgt.SetSignatureCaptureInterface">
            <summary>
            Initialize Dynamic Load Management of Signatures to the currently selected Signature device
            </summary>
            <returns>Signature capture class</returns>
        </member>
        <member name="M:DCSSDK.CaptureMgt.DCSSDK_CaptureMgt.SetFingerprintCaptureInterface">
            <summary>
            Initialize Dynamic Load Management of Fingerprints to the currently selected Fingerprint device
            </summary>
            <returns>Fingerprint capture class</returns>
        </member>
        <member name="M:DCSSDK.CaptureMgt.DCSSDK_CaptureMgt.ReadCaptureMgtParameters">
            <summary>
            This routine should be called when the control is instantiated - so m_ps is assigned.
            </summary>
        </member>
        <member name="M:DCSSDK.CaptureMgt.DCSSDK_CaptureMgt.GetFullnameOfImage(System.String,System.String,System.Boolean)">
            <summary>
            Calculate full name and path of an image given image ID and class. 
            The public interface allows imgClass to be input as text.
            If bMustExist is true, only existing files will be returned.  If none is found null 
            will be returned. 
            The  bMustExist requirement allows any image type file to qualify starting with the
            image type configured in the FinisherProperties.
            </summary>
            <param name="strImageID"></param>
            <param name="strImgClass"></param>
            <param name="bMustExist"></param>
            <returns>full file name and path</returns>
        </member>
        <member name="T:DCSSDK.DCSImageDisplay">
            <summary>
            Summary description for Form1.
            </summary>
        </member>
        <member name="F:DCSSDK.DCSImageDisplay.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:DCSSDK.DCSImageDisplay.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
        </member>
        <member name="M:DCSSDK.DCSImageDisplay.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
    </members>
</doc>
