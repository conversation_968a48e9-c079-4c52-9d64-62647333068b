using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace DCSDEV.OLEToFiles
{
    public partial class OLEToFiles : Form
    {
        private int m_iCurrentImageDBSize;
        private ParameterStore m_ps;
        public string m_strImageDBType;    // old type until change is accepted

        public OLEToFiles(ParameterStore ps, string strImageDBType, string strDataRootDir)
        {
            InitializeComponent();

            m_strImageDBType = strImageDBType;
            m_ps = ps;
            this.labelExistingIs.Text += " " + m_strImageDBType;
            this.labelDataType.Text = m_strImageDBType;
            this.labelDataRootDirectory.Text = strDataRootDir;
            if (m_strImageDBType == "ACCESS")
            {
                this.labelDBNameText.Text = "SDSImages.Mdb";
                this.labelServerNameText.Text = "Jet.OLEDB.4.0";
                this.labelServerNameText.Visible = false;
                this.labelServerName.Visible = false;
            }
            else
            {
                this.labelDBNameText.Text = m_ps.GetStringParameter("DCSSDK_ImageDB", "ImageDBCatalog", "SDSImages");
                this.labelServerNameText.Text = m_ps.GetStringParameter("DCSSDK_ImageDB", "ImageDBDataSource", null);
            }
            int nImageInUsersDB = Convert.ToInt32(m_ps.GetStringParameter("DCSSDK_UsersDB", "UsersDBHasImages", "0"));
            string strFields = "none"; ;
            if ((nImageInUsersDB & 1) == 1) strFields = "Portrait; ";
            if ((nImageInUsersDB & 2) == 2) strFields += "Signature; ";
            this.labelUsersDBFields.Text = strFields;

            // check on sizes of current and new image DBs
            m_iCurrentImageDBSize = DCSDatabaseIF.GetImageDBSize();
            this.groupBoxExistingFiles.Enabled = (m_iCurrentImageDBSize > 0);
        }

        private void buttonAccept_Click(object sender, EventArgs e)
        {
            bool bRet;
            bool bCopy = false;
            bool bDelete = false;

            if (m_iCurrentImageDBSize < 0)
            {
                // cannot read current image DB - change to FILES without converting
                if (DCSMsg.ShowOKC("Cannot read from current OLE image database. OK to change to FILES anyway?") == DialogResult.Cancel)
                    return;
            }
            else if (m_iCurrentImageDBSize > 0)
            {
                if (this.radioButtonCopyFiles.Checked)
                {
                    bCopy = true;
                    bDelete = false;
                }
                else if (this.radioButtonMoveFiles.Checked)
                {
                    bCopy = true;
                    bDelete = true;
                }
                else if (this.radioButtonDontCopy.Checked)
                {
                    bCopy = false;
                    bDelete = false;
                }
                else
                {
                    DCSMsg.Show("Must indicate how to handle existing images.");
                    return;
                }
                if (bCopy)
                {
                    this.labelInstructions.Visible = false;
                    this.labelWait.Visible = true;
                    bRet = DCSDatabaseIF.ConvertDBType("FILES", bDelete);
                    if (!bRet)
                    {
                        this.labelInstructions.Visible = true;
                        this.labelWait.Visible = false;
                        return;
                    }
                }
            }
            m_ps.WriteStringParameter("DCSSDK_ImageDB", "ImageDBType", "FILES");
            m_strImageDBType = "FILES";
            DCSDatabaseIF.Reinit();

            this.DialogResult = DialogResult.OK;
            this.Close();
            return;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void OLEToFiles_Load(object sender, EventArgs e)
        {

        }
    }
}