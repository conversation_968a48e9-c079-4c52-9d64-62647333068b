{"RootPath": "D:\\repos_D\\SDS Collection\\DCSSDK Badging\\DCSSDK_CaptureMgt", "ProjectFileName": "DCSSDK_CaptureMgt.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "AboutCaptureMgt.cs"}, {"SourceFile": "AgoraIF.cs"}, {"SourceFile": "AssemblyInfo.cs"}, {"SourceFile": "CaptureMgt.cs"}, {"SourceFile": "DCSFingerMapping.cs"}, {"SourceFile": "FilesToOLE.cs"}, {"SourceFile": "FilesToOLE.Designer.cs"}, {"SourceFile": "FIPS_Tools.cs"}, {"SourceFile": "ImageDisplay.cs"}, {"SourceFile": "OLEToFiles.cs"}, {"SourceFile": "OLEToFiles.Designer.cs"}, {"SourceFile": "ScanBarcode.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Badging\\Referenced Files\\AgoraDlls\\AgrFeature.DLL", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Badging\\Referenced Files\\AgoraDlls\\Antheus.Display.FingerView.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSCapture_Canon\\bin\\Debug\\DCSCapture_Canon.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSCapture_Canon\\bin\\Debug\\DCSCapture_Canon.dll"}, {"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSCapture_CrossMatch\\bin\\Debug\\DCSCapture_CrossMatch.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSCapture_CrossMatch\\bin\\Debug\\DCSCapture_CrossMatch.dll"}, {"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSCapture_DCS8000\\bin\\Debug\\DCSCapture_DCS8000.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSCapture_DCS8000\\bin\\Debug\\DCSCapture_DCS8000.dll"}, {"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSCapture_FDU04\\bin\\Debug\\DCSCapture_FDU04.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSCapture_FDU04\\bin\\Debug\\DCSCapture_FDU04.dll"}, {"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSCapture_FromFile\\bin\\Debug\\DCSCapture_FromFile.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSCapture_FromFile\\bin\\Debug\\DCSCapture_FromFile.dll"}, {"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Badging\\DCSCapture_Scanner\\bin\\Debug\\DCSCapture_Scanner.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\repos_D\\SDS Collection\\DCSSDK Badging\\DCSCapture_Scanner\\bin\\Debug\\DCSCapture_Scanner.dll"}, {"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSCapture_Topaz\\bin\\Debug\\DCSCapture_Topaz.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSCapture_Topaz\\bin\\Debug\\DCSCapture_Topaz.dll"}, {"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSCapture_Twain\\bin\\Debug\\DCSCapture_Twain.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSCapture_Twain\\bin\\Debug\\DCSCapture_Twain.dll"}, {"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Badging\\DCSInnovatricsIF\\bin\\Debug\\DCSInnovatricsIF.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\repos_D\\SDS Collection\\DCSSDK Badging\\DCSInnovatricsIF\\bin\\Debug\\DCSInnovatricsIF.dll"}, {"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Badging\\DCSSDK_ChipIF\\bin\\Debug\\DCSSDK_ChipIF.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\repos_D\\SDS Collection\\DCSSDK Badging\\DCSSDK_ChipIF\\bin\\Debug\\DCSSDK_ChipIF.dll"}, {"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSSDK_Finisher\\bin\\Debug\\DCSSDK_Finisher.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSSDK_Finisher\\bin\\Debug\\DCSSDK_Finisher.dll"}, {"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSSDK_FinisherProperties\\bin\\Debug\\DCSSDK_FinisherProperties.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSSDK_FinisherProperties\\bin\\Debug\\DCSSDK_FinisherProperties.dll"}, {"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSSDK_Utilities\\bin\\Debug\\DCSSDK_Utilities.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSSDK_Utilities\\bin\\Debug\\DCSSDK_Utilities.dll"}, {"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Badging\\Referenced Files\\Innovatrics IDKit\\Innovatrics.IEngine.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "D:\\repos_D\\SDS Collection\\DCSSDK Badging\\DCSSDK_CaptureMgt\\bin\\Debug\\DCSSDK_CaptureMgt.dll", "OutputItemRelativePath": "DCSSDK_CaptureMgt.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}