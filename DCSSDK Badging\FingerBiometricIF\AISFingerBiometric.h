// AISFingerBiometricIF.h : main header file for the FINGERBIOMETRICIF DLL
//

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#ifndef __AFXWIN_H__
	#error include 'stdafx.h' before including this file for PCH
#endif

//#include "resource.h"		// main symbols
#include "ais11.h"

struct ibm_minHead_def
{
	short nHeadSize;
	char szQuality[2];
	short lQuality;
	short lNumMinutia;
	short lSize;
};
struct ibm_minData_def
{
	struct ibm_minHead_def ibm_minHead;
	char PackedData[512];
};


bool DoExtractFeatures(char* szFingerImage, char* szFingerFeaturesFile, long instance);
long DoVerifyFeatures(char* szFingerFeaturesFile1, char* szFingerFeaturesFile2);
long DoVerifyFinger(char* szFingerImage, char* szFingerFeaturesFile, long instance);
long DoMinutiaMatch(char* pMinMap1, char* pMinMap2);
bool DoMeasureQuality(char* szFingerImage, long* lDryness, long* lSmudginess, long* lOrientation, long* lRoll, long* lQuality);
HGLOBAL ImageFromDisk(const CString imagefile);
BOOL ReadMinutiaFile(char*& pBuffer, int iMapSize, const CString minutiafile);
BOOL WriteMinutiaFile(char* pMinMap, int iMapSize, const CString minutiafile, long instance);

/////////////////////////////////////////////////////////////////////////////
