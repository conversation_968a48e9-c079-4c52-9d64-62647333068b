﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <LastOpenVersion>7.10.3077</LastOpenVersion>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ReferencePath>C:\DCS.SDK\Badging\Referenced Files\Barcode\;C:\DCS.SDK\Badging\DCSSDK_BadgingMgt\;C:\DCS.SDK\Lead.Net\</ReferencePath>
    <CopyProjectDestinationFolder>
    </CopyProjectDestinationFolder>
    <CopyProjectUncPath>
    </CopyProjectUncPath>
    <CopyProjectOption>0</CopyProjectOption>
    <ProjectView>ProjectFiles</ProjectView>
    <ProjectTrust>0</ProjectTrust>
    <PublishUrlHistory />
    <InstallUrlHistory />
    <SupportUrlHistory />
    <UpdateUrlHistory />
    <BootstrapperUrlHistory />
    <ErrorReportUrlHistory />
    <FallbackCulture>en-US</FallbackCulture>
    <VerifyUploadedFiles>false</VerifyUploadedFiles>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <EnableASPDebugging>false</EnableASPDebugging>
    <EnableASPXDebugging>false</EnableASPXDebugging>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <EnableSQLServerDebugging>false</EnableSQLServerDebugging>
    <RemoteDebugEnabled>false</RemoteDebugEnabled>
    <RemoteDebugMachine>
    </RemoteDebugMachine>
    <StartAction>Project</StartAction>
    <StartArguments>
    </StartArguments>
    <StartPage>
    </StartPage>
    <StartProgram>
    </StartProgram>
    <StartURL>
    </StartURL>
    <StartWorkingDirectory>
    </StartWorkingDirectory>
    <StartWithIE>false</StartWithIE>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <EnableASPDebugging>false</EnableASPDebugging>
    <EnableASPXDebugging>false</EnableASPXDebugging>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <EnableSQLServerDebugging>false</EnableSQLServerDebugging>
    <RemoteDebugEnabled>false</RemoteDebugEnabled>
    <RemoteDebugMachine>
    </RemoteDebugMachine>
    <StartAction>Project</StartAction>
    <StartArguments>
    </StartArguments>
    <StartPage>
    </StartPage>
    <StartProgram>
    </StartProgram>
    <StartURL>
    </StartURL>
    <StartWorkingDirectory>
    </StartWorkingDirectory>
    <StartWithIE>true</StartWithIE>
  </PropertyGroup>
</Project>