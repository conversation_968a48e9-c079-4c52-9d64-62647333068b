/*******************************************************************************
 *
 *  Filename        :   Sample1.cs
 *  Description     :   Sample program that demostrates template extraction,
 *                      registration, and identification.
 *
 *
 *  Company         :   Innovatrics
 *  Project         :   IDKit SDK
 *  Platform        :   .NET
 *  Language        :   C#
 *  Operating System:   Windows 2000 or higher / Pocket PC 2003 or higher
 *
 *
 *  Copyright (C) 2006-2008 Innovatrics.  All rights reserved.
 ******************************************************************************/

using System;
using System.Drawing;
using System.IO;
using Innovatrics.IEngine;

namespace sample1
{
    class Sample1
    {
        // Windows Mobile compatibility: relative paths are replaced by
        // absolute paths and console output is replaced with output
        // to a text file.
#if PocketPC
        static string SampleFolder = Path.GetDirectoryName(
            System.Reflection.Assembly.GetExecutingAssembly().GetName().CodeBase) + @"\";
        static TextWriter Output = new StreamWriter(SampleFolder + "sample_result.txt");
#else
        string SampleFolder = "";
        static TextWriter Output = Console.Out;
#endif

        private Innovatrics.IEngine.IDKit idkit;
        private Innovatrics.IEngine.User user1;
        private Innovatrics.IEngine.User user2;

        public void Run()
        {
            int uid1;	// Id of user1
            SearchResult match;

            // Initialize IDKit SDK
            idkit = IDKit.GetInstance();

            // Connect to database
            idkit.Connect(SampleFolder + "sample.db");

            // Create first user
			Output.WriteLine("Creating first user (user1_fp1.bmp) ... ");
            user1 = new User();
            user1.Add(new Fingerprint(new Bitmap(SampleFolder + "user1_fp1.bmp")));

            // Register user
            Output.Write("Registering the user... ");
            uid1 = idkit.RegisterUser(user1);
            Output.WriteLine("(ID = {0})", uid1);

            Output.WriteLine();

            // Create second user
			Output.WriteLine("Creating second user (user2_fp1.bmp) ... ");
            user2 = new User();
            user2.Add(new Fingerprint(new Bitmap(SampleFolder + "user2_fp1.bmp")));

            // Try to search for the second user in database
            Output.Write("Searching for the user in database... ");
            match = idkit.Find(user2);

            if (!match.Best.Valid)
            {
                Output.WriteLine("not registered.");
            }
            else
            {
                Output.WriteLine("(User ID = {0}, Score = {1})", match.Best.Id, match.Best.Score);
            }

            Output.WriteLine();

            // Create third user
			Output.WriteLine("Loading new fingerprint (user1_fp2.bmp) ... ");
            user2.Add(new Fingerprint(new Bitmap(SampleFolder + "user1_fp2.bmp")));

            // Try to serach for the third user in database
            Output.Write("Searching for the fingerprint in database... ");
            match = idkit.Find(user2.Fingerprints[1]);

            if (!match.Best.Valid)
            {
                Output.WriteLine("not found.");
            }
            else
            {
                Output.WriteLine("(User ID = {0}, Score = {1}, Best Index = {2})",
                    match.Best.Id, match.Best.Score, match.Best.Index);
            }

			// Try to verify 
			Output.WriteLine("Verifying (user1_fp1.bmp) versus (user1_fp2.bmp) in the database... ");
			Fingerprint fp1 = new Fingerprint(new Bitmap("user2_fp1.bmp"));
			Fingerprint fp2 = new Fingerprint(new Bitmap("user1_fp2.bmp"));
			MatchResult mr = idkit.Match(fp1, fp2);
			Output.WriteLine("Match result = " + mr.Score.ToString());

			Output.WriteLine("\nVerifying (360.bmp) versus (007_bin2.bmp) in the database... ");
			fp1 = new Fingerprint(new Bitmap("360.bmp"));
			fp2 = new Fingerprint(new Bitmap("007_bin2.bmp"));
			mr = idkit.Match(fp1, fp2);
			Output.WriteLine("Match result = " + mr.Score.ToString());
		}

#if !PocketPC
        [STAThread]
#endif
        static void Main(string[] args)
        {
            Sample1 sample = new Sample1();

            try
            {
                sample.Run();
            }
            catch (IEngineException e)
            {
                Output.WriteLine("{0}. (Error Code {1})", e.Message, e.Number);
            }

#if PocketPC
            Output.Flush();
#endif
        }
    }
}
