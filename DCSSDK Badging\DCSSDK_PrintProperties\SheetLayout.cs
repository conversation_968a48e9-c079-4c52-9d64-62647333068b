using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;

using DCSDEV;

namespace DCSDEV.PrintProperties
{
	/// <summary>
	/// Summary description for SheetLayout.
	/// </summary>
    internal class SheetLayout : System.Windows.Forms.Form
	{
		private int m_iUnits = 0;
		private double m_dSheetW = 8.50;
		private double m_dSheetH = 11.00;
		private double m_dScale = 1.0;

		private int m_nColumns = 1;
		private int m_nRows = 1;
		private double m_dFirstX = 0.5;
		private double m_dFirstY = 0.5;
		private double m_dBadgeW = 3.00;
		private double m_dBadgeH = 2.25;
		private double m_dDeltaX = 3.50;
		private double m_dDeltaY = 3.50;

		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.TextBox tbColumns;
		private System.Windows.Forms.TextBox tbRows;
		private System.Windows.Forms.TextBox tbFirstY;
		private System.Windows.Forms.TextBox tbFirstX;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.TextBox tbSpaceY;
		private System.Windows.Forms.TextBox tbSpaceX;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.TextBox tbBadgeY;
		private System.Windows.Forms.TextBox tbBadgeX;
		private System.Windows.Forms.Label label4;
		private System.Windows.Forms.Button buttonCancel;
		private System.Windows.Forms.Button buttonAccept;
		private System.Windows.Forms.Button buttonPreview;
		private System.Windows.Forms.PictureBox pictureBox1;
		private System.Windows.Forms.TextBox tbPrinterH;
		private System.Windows.Forms.TextBox tbPrinterW;
		private System.Windows.Forms.Label label5;
		private System.Windows.Forms.ComboBox comboBoxUnits;
		private System.Windows.Forms.Label labelLayoutError;
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		public SheetLayout(int width, int height)
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			m_dSheetW = (double)width / 100.0;
			m_dSheetH = (double)height / 100.0;
			this.LoadData();
			this.LoadControls();
			this.comboBoxUnits.SelectedIndex = m_iUnits;

			double ratioW = (double)this.pictureBox1.Width / (m_dSheetW * 100.0);
			double ratioH = (double)this.pictureBox1.Height / (m_dSheetH * 100.0);
			if (ratioW < ratioH) m_dScale = ratioW;
			else m_dScale = ratioH;
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		private void LoadControls()
		{
			// do not set comboBoxUnits here to avoid triggering .SelectedIndexChanged 
			this.tbColumns.Text = m_nColumns.ToString();
			this.tbRows.Text   = m_nRows.ToString();

			if (m_iUnits == 0)	// inches are displayed
			{
				this.tbFirstX.Text = m_dFirstX.ToString("0.000");
				this.tbFirstY.Text = m_dFirstY.ToString("0.000");
				this.tbSpaceX.Text = m_dDeltaX.ToString("0.000");
				this.tbSpaceY.Text = m_dDeltaY.ToString("0.000");
				this.tbBadgeX.Text = m_dBadgeW.ToString("0.000");
				this.tbBadgeY.Text = m_dBadgeH.ToString("0.000");

				this.tbPrinterW.Text = m_dSheetW.ToString("0.000");
				this.tbPrinterH.Text = m_dSheetH.ToString("0.000");
			}
			else
			{
				this.tbFirstX.Text = (m_dFirstX * 25.4).ToString("0.000");
				this.tbFirstY.Text = (m_dFirstY * 25.4).ToString("0.000");
				this.tbSpaceX.Text = (m_dDeltaX * 25.4).ToString("0.000");
				this.tbSpaceY.Text = (m_dDeltaY * 25.4).ToString("0.000");
				this.tbBadgeX.Text = (m_dBadgeW * 25.4).ToString("0.000");
				this.tbBadgeY.Text = (m_dBadgeH * 25.4).ToString("0.000");

				this.tbPrinterW.Text = (m_dSheetW * 25.4).ToString("0.000");
				this.tbPrinterH.Text = (m_dSheetH * 25.4).ToString("0.000");
			}
		}

		private bool ReadControls()
		{
			try
			{
				// It is expected that m_iUnits = this.comboBoxUnits.SelectedIndex;
				m_nColumns = System.Convert.ToInt32(this.tbColumns.Text);
				m_nRows   = System.Convert.ToInt32(this.tbRows.Text);

				if (m_iUnits == 0)	// inches are displayed
				{
					m_dFirstX = System.Convert.ToDouble(this.tbFirstX.Text);
					m_dFirstY = System.Convert.ToDouble(this.tbFirstY.Text);
					m_dBadgeW = System.Convert.ToDouble(this.tbBadgeX.Text);
					m_dBadgeH = System.Convert.ToDouble(this.tbBadgeY.Text);
					m_dDeltaX = System.Convert.ToDouble(this.tbSpaceX.Text);
					m_dDeltaY = System.Convert.ToDouble(this.tbSpaceY.Text);
				}
				else
				{
					m_dFirstX = (System.Convert.ToDouble(this.tbFirstX.Text) / 25.4 );
					m_dFirstY = (System.Convert.ToDouble(this.tbFirstY.Text) / 25.4 );
					m_dBadgeW = (System.Convert.ToDouble(this.tbBadgeX.Text) / 25.4 );
					m_dBadgeH = (System.Convert.ToDouble(this.tbBadgeY.Text) / 25.4 );
					m_dDeltaX = (System.Convert.ToDouble(this.tbSpaceX.Text) / 25.4 );
					m_dDeltaY = (System.Convert.ToDouble(this.tbSpaceY.Text) / 25.4 );
				}
			}
			catch (Exception ex)
			{
				DCSMsg.Show("Cannot convert string to number.", ex);
				return false;
			}
			return true;
		}

		private void LoadData()
		{
			DCSDEV.ParameterStore ps = new ParameterStore("SheetLayout");
			m_nColumns = ps.GetIntParameter("Columns", m_nColumns);
			m_nRows   = ps.GetIntParameter("Rows", m_nRows);
			m_dFirstX = (double)ps.GetIntParameter("FirstX", (int)(m_dFirstX * 100.0)) / 100.0;
			m_dFirstY = (double)ps.GetIntParameter("FirstY", (int)(m_dFirstY * 100.0)) / 100.0;
			m_dDeltaX = (double)ps.GetIntParameter("SpaceX", (int)(m_dDeltaX * 100.0)) / 100.0;
			m_dDeltaY = (double)ps.GetIntParameter("SpaceY", (int)(m_dDeltaY * 100.0)) / 100.0;
			m_dBadgeW = (double)ps.GetIntParameter("BadgeW", (int)(m_dBadgeW * 100.0)) / 100.0;
			m_dBadgeH = (double)ps.GetIntParameter("BadgeH", (int)(m_dBadgeH * 100.0)) / 100.0;

			m_iUnits = ps.GetIntParameter("DCSSDK_Mgt", "DisplayUnits", 0);
		}

		private void StoreData()
		{
			DCSDEV.ParameterStore ps = new ParameterStore("SheetLayout");
			ps.WriteIntParameter("Columns", m_nColumns);
			ps.WriteIntParameter("Rows", m_nRows);
			ps.WriteIntParameter("FirstX", (int)(m_dFirstX * 100.0));
			ps.WriteIntParameter("FirstY", (int)(m_dFirstY * 100.0));
			ps.WriteIntParameter("SpaceX", (int)(m_dDeltaX * 100.0));
			ps.WriteIntParameter("SpaceY", (int)(m_dDeltaY * 100.0));
			ps.WriteIntParameter("BadgeW", (int)(m_dBadgeW * 100.0));
			ps.WriteIntParameter("BadgeH", (int)(m_dBadgeH * 100.0));

			DCSDEV.DCSDesignDataAccess.SetUnits(m_iUnits);
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(SheetLayout));
            this.label1 = new System.Windows.Forms.Label();
            this.tbColumns = new System.Windows.Forms.TextBox();
            this.tbRows = new System.Windows.Forms.TextBox();
            this.tbFirstY = new System.Windows.Forms.TextBox();
            this.tbFirstX = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.tbSpaceY = new System.Windows.Forms.TextBox();
            this.tbSpaceX = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.tbBadgeY = new System.Windows.Forms.TextBox();
            this.tbBadgeX = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.buttonCancel = new System.Windows.Forms.Button();
            this.buttonAccept = new System.Windows.Forms.Button();
            this.buttonPreview = new System.Windows.Forms.Button();
            this.pictureBox1 = new System.Windows.Forms.PictureBox();
            this.tbPrinterH = new System.Windows.Forms.TextBox();
            this.tbPrinterW = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.comboBoxUnits = new System.Windows.Forms.ComboBox();
            this.labelLayoutError = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
            this.SuspendLayout();
            // 
            // label1
            // 
            resources.ApplyResources(this.label1, "label1");
            this.label1.Name = "label1";
            // 
            // tbColumns
            // 
            resources.ApplyResources(this.tbColumns, "tbColumns");
            this.tbColumns.Name = "tbColumns";
            // 
            // tbRows
            // 
            resources.ApplyResources(this.tbRows, "tbRows");
            this.tbRows.Name = "tbRows";
            // 
            // tbFirstY
            // 
            resources.ApplyResources(this.tbFirstY, "tbFirstY");
            this.tbFirstY.Name = "tbFirstY";
            // 
            // tbFirstX
            // 
            resources.ApplyResources(this.tbFirstX, "tbFirstX");
            this.tbFirstX.Name = "tbFirstX";
            // 
            // label2
            // 
            resources.ApplyResources(this.label2, "label2");
            this.label2.Name = "label2";
            // 
            // tbSpaceY
            // 
            resources.ApplyResources(this.tbSpaceY, "tbSpaceY");
            this.tbSpaceY.Name = "tbSpaceY";
            // 
            // tbSpaceX
            // 
            resources.ApplyResources(this.tbSpaceX, "tbSpaceX");
            this.tbSpaceX.Name = "tbSpaceX";
            // 
            // label3
            // 
            resources.ApplyResources(this.label3, "label3");
            this.label3.Name = "label3";
            // 
            // tbBadgeY
            // 
            resources.ApplyResources(this.tbBadgeY, "tbBadgeY");
            this.tbBadgeY.Name = "tbBadgeY";
            // 
            // tbBadgeX
            // 
            resources.ApplyResources(this.tbBadgeX, "tbBadgeX");
            this.tbBadgeX.Name = "tbBadgeX";
            // 
            // label4
            // 
            resources.ApplyResources(this.label4, "label4");
            this.label4.Name = "label4";
            // 
            // buttonCancel
            // 
            this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            resources.ApplyResources(this.buttonCancel, "buttonCancel");
            this.buttonCancel.Name = "buttonCancel";
            this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
            // 
            // buttonAccept
            // 
            resources.ApplyResources(this.buttonAccept, "buttonAccept");
            this.buttonAccept.Name = "buttonAccept";
            this.buttonAccept.Click += new System.EventHandler(this.buttonAccept_Click);
            // 
            // buttonPreview
            // 
            resources.ApplyResources(this.buttonPreview, "buttonPreview");
            this.buttonPreview.Name = "buttonPreview";
            this.buttonPreview.Click += new System.EventHandler(this.buttonPreview_Click);
            // 
            // pictureBox1
            // 
            resources.ApplyResources(this.pictureBox1, "pictureBox1");
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.TabStop = false;
            // 
            // tbPrinterH
            // 
            resources.ApplyResources(this.tbPrinterH, "tbPrinterH");
            this.tbPrinterH.Name = "tbPrinterH";
            this.tbPrinterH.ReadOnly = true;
            this.tbPrinterH.TabStop = false;
            // 
            // tbPrinterW
            // 
            resources.ApplyResources(this.tbPrinterW, "tbPrinterW");
            this.tbPrinterW.Name = "tbPrinterW";
            this.tbPrinterW.ReadOnly = true;
            this.tbPrinterW.TabStop = false;
            // 
            // label5
            // 
            resources.ApplyResources(this.label5, "label5");
            this.label5.Name = "label5";
            // 
            // comboBoxUnits
            // 
            this.comboBoxUnits.BackColor = System.Drawing.SystemColors.Control;
            this.comboBoxUnits.Items.AddRange(new object[] {
            resources.GetString("comboBoxUnits.Items"),
            resources.GetString("comboBoxUnits.Items1")});
            resources.ApplyResources(this.comboBoxUnits, "comboBoxUnits");
            this.comboBoxUnits.Name = "comboBoxUnits";
            this.comboBoxUnits.SelectedIndexChanged += new System.EventHandler(this.comboBoxUnits_SelectedIndexChanged);
            // 
            // labelLayoutError
            // 
            resources.ApplyResources(this.labelLayoutError, "labelLayoutError");
            this.labelLayoutError.ForeColor = System.Drawing.Color.Red;
            this.labelLayoutError.Name = "labelLayoutError";
            // 
            // SheetLayout
            // 
            this.AcceptButton = this.buttonAccept;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.CancelButton = this.buttonCancel;
            resources.ApplyResources(this, "$this");
            this.Controls.Add(this.labelLayoutError);
            this.Controls.Add(this.comboBoxUnits);
            this.Controls.Add(this.tbPrinterH);
            this.Controls.Add(this.tbPrinterW);
            this.Controls.Add(this.tbBadgeY);
            this.Controls.Add(this.tbBadgeX);
            this.Controls.Add(this.tbSpaceY);
            this.Controls.Add(this.tbSpaceX);
            this.Controls.Add(this.tbFirstY);
            this.Controls.Add(this.tbFirstX);
            this.Controls.Add(this.tbRows);
            this.Controls.Add(this.tbColumns);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.buttonPreview);
            this.Controls.Add(this.pictureBox1);
            this.Controls.Add(this.buttonCancel);
            this.Controls.Add(this.buttonAccept);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.label1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "SheetLayout";
            this.ShowInTaskbar = false;
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}
		#endregion

		private void buttonAccept_Click(object sender, System.EventArgs e)
		{
			if (!this.ReadControls()) return;
			this.StoreData();
			Close();		
		}

		private void buttonCancel_Click(object sender, System.EventArgs e)
		{
			Close();		
		}

		private void buttonPreview_Click(object sender, System.EventArgs e)
		{
			if (!this.ReadControls()) return;
			this.Refresh();
		}

		protected override void OnPaint(PaintEventArgs e)
		{
			base.OnPaint (e);
			double dScale100 = m_dScale * 100.0;
			System.Drawing.Pen pen = new Pen(System.Drawing.Color.Black, 2.0f);
			Rectangle rectBackground = new Rectangle(0,0,(int)(m_dSheetW*dScale100), (int)(m_dSheetH*dScale100));
			rectBackground.Offset(this.pictureBox1.Left, this.pictureBox1.Top);
			
			e.Graphics.FillRectangle(new SolidBrush(System.Drawing.Color.White), rectBackground);
			Rectangle rectBadgeBox = new Rectangle(0, 0, (int)(m_dBadgeW*dScale100), (int)(m_dBadgeH*dScale100));
			rectBadgeBox.Offset(this.pictureBox1.Left, this.pictureBox1.Top);
			Rectangle rect, rectIntersected;
			bool bExceedsPageBounds = false;
            for (int j = 0; j < m_nRows; j++)
            {
                for (int i = 0; i < m_nColumns; i++)
				{
					rect = rectBadgeBox;
					rect.Offset((int)((m_dFirstX + i*m_dDeltaX) * dScale100), (int)((m_dFirstY + j*m_dDeltaY) * dScale100));
					rectIntersected = rect;
					rectIntersected.Intersect(rectBackground);
					e.Graphics.FillRectangle(new System.Drawing.SolidBrush(Color.LightGray), rectIntersected);
                    e.Graphics.DrawRectangle(new Pen(Color.Blue), rectIntersected);
                    e.Graphics.DrawString((1 + i + (j * m_nColumns)).ToString(), new Font("Arial", 12), new System.Drawing.SolidBrush(Color.Blue), rectIntersected.Location);
                    if (rectIntersected != rect) bExceedsPageBounds = true;
				}
			}
            this.labelLayoutError.Visible = bExceedsPageBounds;
		}

		private void comboBoxUnits_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if (m_iUnits != this.comboBoxUnits.SelectedIndex)
			{
				if (!this.ReadControls()) 
				{
					this.comboBoxUnits.SelectedIndex = m_iUnits;
					return;
				}
				// read controls with old units
				m_iUnits = this.comboBoxUnits.SelectedIndex;
				this.LoadControls();	// reset controls with new units
			}
		}
	}
}
