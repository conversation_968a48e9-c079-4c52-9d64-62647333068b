//#define DYNAMIC_LOADMANAGEMENT

using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;
using System.Data;
using System.Runtime.InteropServices;
using System.IO;

using System.Globalization;
using System.Threading;
using System.Reflection;	// for enumeration

using DCSDEV;
using DCSDEV.DCSFinisherProperties;

//using System.Diagnostics;

namespace DCSSDK.CaptureMgt
{
	/// <summary>
	/// Summary description for DCSSDK_CaptureMgt.
	/// THE DCSSDK_CaptureMgt CLASS HAS PUBLIC SUPPORT FOR:
	/// 
	/// void CaptureCerts(string strImageID, string strImageTitle, int iInstance)
	/// void CapturePortrait(string strImageID, string strImageTitle, int iSubClass) - also Signature and Fingerprint
	/// void Capture10Print(string strImageID, string strImageTitle)
	/// void CaptureAllImages(string strImageID, string strImageTitle)  
	/// void CloseDisplays()
	/// void DeletePortrait(string strImageID, int iSubClass) - also DeleteSignature and DeleteFingerprint and DeleteCerts
	/// void DeleteAllImageClasses(string strImageID)
	/// void DisplayPortrait(string strImageID, int iSubClass(-1 and 0-9), string strLabel) - also Signature and Fingerprint
	/// void DisplayAllImageClasses(string strImageID, string strLabel)
	/// void DisplayCerts(string strImageID, int instnce, string strLabel)
	/// bool FIPSImportExport(bool bImport, string strFilename, string strImageID)
	/// int GenerateBiometric(string strImageID, string strImgClassEx, int iSubClass) 
	/// string GetBiometric(string strImageID, string strImgClassEx, int iSubClass) 
	/// string GetDocumentBIN(string strImageID, strDocName) 
	/// string GetFullnameOfImage(string strImageID, string strImgClassEx, bool bMustExist)
	/// string ScanData(string strSource, string strDestination, string strItem);
	/// string GetLastChipID() - returns unique ID of last chip read.
	/// bool ImportExportPortrait(bool bImport, string strFilename, string strImageID, int iSubClass)
	/// bool ImportExportSignature(bool bImport, string strFilename, string strImageID, int iSubClass)
	/// bool ImportExportFingerprint(bool bImport, string strFilename, string strImageID, int iSubClass)
	/// bool ImportExportCerts(bool bImport, string strFilename, string strImageID, int iSubClass)
	/// bool ImportMergeAll(string strImageID, string strSourceID, string strSourceDataRoot)
	/// bool ImportMergePortrait(string strImageID, string strSourceID, string strSourceDataRoot)
	/// bool ImportMergeSignature(string strImageID, string strSourceID, string strSourceDataRoot)
	/// bool ImportMergeFingerprint(string strImageID, string strSourceID, string strSourceDataRoot)
	/// bool ImportMergeCerts(string strImageID, string strSourceID, string strSourceDataRoot)
	/// bool ValidateConfiguration() - false means you had better ShowDialog to set up paths properly
	/// string VerifyBiometric(string strTargetImageID, string strImageClass, int iSubClass) 
	/// - one or two fingers must exist; return null if error
	/// string SearchBiometric(string strImageID, string imageClass, int iSubClass, arrayOfImageIDs) 
	/// Rectangle PortraitDisplayRectangle - also Signature and Fingerprint
	/// string DataRootDir - root path to BadgeDataPath, PortraitPath, SignaturePath, FingerprintPath
	/// bool CaptureStatus
	/// </summary>
	public class DCSSDK_CaptureMgt : System.Windows.Forms.Form
	{
#if LISKA_BIN
		[DllImport("FPMatch.dll", EntryPoint = "Bin2Enroll", ExactSpelling = true)]
		private static extern string Bin2Enroll(int size1, byte[] finger1, int size2, byte[] finger2);

		[DllImport("FPMatch.dll", EntryPoint = "Bin2Match", ExactSpelling = true)]
		private static extern int Bin2Match(int sizefinger, byte[] finger, int sizebin, string bin);
#endif

		[DllImport("FingerBiometricIF.dll", EntryPoint = "DCSExtractFeatures", ExactSpelling = true)]
		private static extern bool DCSExtractFeatures(string finger, string biometric, long instance);

		[DllImport("FingerBiometricIF.dll", EntryPoint = "DCSVerifyFeatures", ExactSpelling = true)]
		private static extern int DCSVerifyFeatures(string biometric1, string biometric2);

		[DllImport("FingerBiometricIF.dll", EntryPoint = "DCSMinutiaMatch", ExactSpelling = true)]
		private static extern int DCSMinutiaMatch(string biometric1, string biometric2);

		[DllImport("FingerBiometricIF.dll", EntryPoint = "DCSVerifyFinger", ExactSpelling = true)]
		private static extern int DCSVerifyFinger(string finger, string biometric, long instance);

        private DCSDEV.CanonCamera.CanonCameraMain m_dlgCanonPhoto = null;
        private DCSDEV.DCS8000.DCS8000Main m_dlgDCS8000Photo = null;
        private DCSDEV.FromFile.FromFileMain m_dlgFromFilePhoto = null;
        private DCSDEV.DCSTwain.TwainMain m_dlgTwainPhoto = null;
        private DCSDEV.CanonCamera.CanonCameraProperties m_propdlgCanonPhoto = null;
        private DCSDEV.DCS8000.DCS8000Properties m_propdlgDCS8000Photo = null;
        private DCSDEV.FromFile.FromFileProperties m_propdlgFromFilePhoto = null;
        private DCSDEV.DCSTwain.TwainProperties m_propdlgTwainPhoto = null;
        //
        private DCSDEV.CrossMatch.CrossMatchMain m_dlgCrossMatch = null;
        private DCSDEV.FDU04.FDU04Main m_dlgFDU04 = null;
        private DCSDEV.FromFile.FromFileMain m_dlgFromFileFinger = null;
        private DCSDEV.DCSTwain.TwainMain m_dlgTwainFinger = null;
        private DCSDEV.CrossMatch.CrossMatchProperties m_propdlgCrossMatch = null;
        private DCSDEV.FDU04.FDU04Properties m_propdlgFDU04 = null;
        private DCSDEV.FromFile.FromFileProperties m_propdlgFromFileFinger = null;
        private DCSDEV.DCSTwain.TwainProperties m_propdlgTwainFinger = null;
        //
        private DCSDEV.DCSTopaz.TopazMain m_dlgTopaz = null;
        private DCSDEV.FromFile.FromFileMain m_dlgFromFileSig = null;
        private DCSDEV.DCSTwain.TwainMain m_dlgTwainSig = null;
        private DCSDEV.DCSTopaz.TopazProperties m_propdlgTopaz = null;
        private DCSDEV.FromFile.FromFileProperties m_propdlgFromFileSig = null;
        private DCSDEV.DCSTwain.TwainProperties m_propdlgTwainSig = null;
        //
        private static string[] m_listTypeNames_Portrait = { "CanonCamera", "DCS8000", "FromFile", "Twain" };
        private static string[] m_listTypeNames_Fingerprint = { "CrossMatch", "Fdu04", "FromFile", "Twain" };
        private static string[] m_listTypeNames_TenPrint = { };
        private static string[] m_listTypeNames_Signature = { "Topaz", "FromFile", "Twain" };

		private bool m_bPortrait = false;
		private bool m_bSignature = false;
		private bool m_bFingerprint = false;
		private bool m_b10Print = false;
		private bool m_bCerts = false;

		private bool m_bPortraitCapture = false;
		private bool m_bSignatureCapture = false;
		private bool m_bFingerprintCapture = false;
		private bool m_bCertsCapture = false;

		private bool m_bPortraitScannerCapture = false;
		private bool m_bSignatureScannerCapture = false;
		private bool m_bFingerprintScannerCapture = false;

		private bool m_bPortraitDisplay = true;
		private bool m_bSignatureDisplay = false;
		private bool m_bFingerprintDisplay = false;
		private bool m_bCertsDisplay = false;

		private int m_nPortraitInstances = 1;
		private int m_nSignatureInstances = 1;
		private int m_nFingerprintInstances = 1;
		private int m_n10PrintInstances = 10;

		private string m_strImageDBType = "FILES";
		private string m_strDataRootDir;
		private string m_strPortraitPath;
		private string m_strSignaturePath;
		private string m_strFingerprintPath;
		private string m_strCertsPath;

		private string m_strPortraitDevice;
		private string m_strSignatureDevice;
		private string m_strFingerprintDevice;
		private string m_str10PrintDevice;
		//private string m_strCertsDevice;

		private bool m_bFingerMapping = true;
		private string m_strFingerMap = "0549";
		private ArrayList m_arrayFingerMapNames;
		private bool m_bDontSaveFingerImage = false;
		private bool m_bLogBinErrors = false;
		private bool m_bQCVerify = false;
		private bool m_bGenerateFingerBiometric = false;
		//xx private bool m_bMultiFingerDevice = false;

		private DCSDatabaseIF.DCSBioTypes m_iSelectedFingerBioType = DCSDatabaseIF.DCSBioTypes.BIOTYPE_AIS_MINUTIA;
		private int m_iThresholdBIN2 = 55;
		private int m_iThresholdMinutia = 22;

		private string m_strPhotoName;
		private string m_strSigName;
		private string m_strFingerName;
		private string m_strPhotoTitle;
		private string m_strSigTitle;
		private string m_strFingerTitle;

		private int m_nCaptureOrder = 2;  // choices are 0=psf, 1=sfp, 2=fsp
		private bool m_bEnableImageHistory = false;
		private int m_iVerifyMode = 0;
		private ParameterStore m_ps = null;
		private bool m_bCaptureStatus;

		private string m_strLastChipID = null;

		public DCSDEV.ScannerIF m_scannerIF = null;

		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.Windows.Forms.Label labelDCSSDKPath;
		private System.Windows.Forms.TextBox tbMachineIndexFile;
		private System.Windows.Forms.TextBox tbInstallDirectory;
		private System.Windows.Forms.Label labelDCSDataPath;
		private System.Windows.Forms.TabControl tabControl1;
		private System.Windows.Forms.TabPage tabPortrait;
		private System.Windows.Forms.TabPage tabSignature;
		private System.Windows.Forms.Label labelPortraitDevice;
		private System.Windows.Forms.Label labelPortraitPath;
		private System.Windows.Forms.Label labelSignatureDevice;
		private System.Windows.Forms.Label labelSignaturePath;
		private System.Windows.Forms.Label labelFingerprintDevice;
		private System.Windows.Forms.Label labelFingerprintPath;
		private System.Windows.Forms.ComboBox cbFingerprintDevice;
		private System.Windows.Forms.TabPage tabCustomBiometric;
		private System.ComponentModel.IContainer components;
		private System.Windows.Forms.TextBox tbMachineConfigFile;
		private System.Windows.Forms.Label labelConfigFile;
		private System.Windows.Forms.ComboBox cbSignatureDevice;
		private System.Windows.Forms.Button buttonPortraitFinisherProperties;
		private System.Windows.Forms.Button buttonAbout;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.Button buttonCancel;
		private System.Windows.Forms.ComboBox cbPortraitDevice;

		private System.Windows.Forms.TextBox tbTestPhotoID;
		private System.Windows.Forms.Button buttonPortraitDeviceProperties;
		private System.Windows.Forms.Button buttonSigDeviceProperties;
		private System.Windows.Forms.Button buttonSigFinisherProperties;
		private System.Windows.Forms.TextBox tbTestSigID;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.Button buttonTestSigCapture;
		private System.Windows.Forms.TextBox tbFingerID;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.Button buttonTestFingerCapture;
		private System.Windows.Forms.Button buttonFingerDeviceProperties;
		private System.Windows.Forms.CheckBox checkPortraitCapture;
		private System.Windows.Forms.CheckBox checkSignatureCapture;
		private System.Windows.Forms.CheckBox checkFingerprintCapture;
		private System.Windows.Forms.CheckBox checkPortraitDisplay;
		private System.Windows.Forms.CheckBox checkSignatureDisplay;
		private System.Windows.Forms.CheckBox checkFingerprintDisplay;
		private System.Windows.Forms.Button buttonTestSigDisplay;
		private System.Windows.Forms.Button buttonTestFingerDisplay;
		private System.Windows.Forms.Button buttonTestPortraitDisplay;
		private System.Windows.Forms.Button buttonTestPortraitCapture;
		private System.Windows.Forms.TabPage tabFingerprint;
		private System.Windows.Forms.Button buttonAccept;
		private System.Windows.Forms.NumericUpDown numericUpDownPortraitInstances;
		private System.Windows.Forms.Label label4;
		private System.Windows.Forms.Label label7;
		private System.Windows.Forms.NumericUpDown numericUpDownSignatureInstances;
		private System.Windows.Forms.Label label9;
		private System.Windows.Forms.NumericUpDown numericUpDownFingerInstances;
		private System.Windows.Forms.Button buttonValidate;
		private System.Windows.Forms.CheckBox checkPortraitScanner;
		private System.Windows.Forms.TabPage tabScanner;
		private System.Windows.Forms.Button buttonScannerDisplay;
		private System.Windows.Forms.TextBox textBoxScannerImageID;
		private System.Windows.Forms.Label label6;
		private System.Windows.Forms.Button buttonScannerCapture;
		private System.Windows.Forms.Button buttonScannerProperties;
		private System.Windows.Forms.CheckBox checkSignatureScanner;
		private System.Windows.Forms.CheckBox checkFingerprintScanner;
		private System.Windows.Forms.CheckBox checkPortrait;
		private System.Windows.Forms.CheckBox checkSignature;
		private System.Windows.Forms.CheckBox checkFingerprint;
		private System.Windows.Forms.GroupBox groupBox1;
		private System.Windows.Forms.Label label8;
		private System.Windows.Forms.Label labelNumPortraits;
		private System.Windows.Forms.Label labelNumSignatures;
		private System.Windows.Forms.Label label14;
		private System.Windows.Forms.Label labelNumFingerprints;
		private System.Windows.Forms.Label label13;
		private System.Windows.Forms.TextBox textBoxScannerName;
		private System.Windows.Forms.Label label11;
		private System.Windows.Forms.CheckBox checkDontSaveFingerImage;
		private System.Windows.Forms.CheckBox checkGenFingerBIN;
		private System.Windows.Forms.GroupBox groupBox2;
		private System.Windows.Forms.ListBox listBoxFingerBioTypes;
		private System.Windows.Forms.Button buttonVerifyBiometric;
		private System.Windows.Forms.ComboBox cbVerifyHow;
		private System.Windows.Forms.TextBox tbBiometricID;
		private System.Windows.Forms.Label label10;
		private System.Windows.Forms.Button buttonShowBiometrics;
		private System.Windows.Forms.Button buttonGenBiometric;
		private System.Windows.Forms.NumericUpDown numericUpDownBIN2Threshold;
		private System.Windows.Forms.Label label12;
		private System.Windows.Forms.ComboBox comboBoxCaptureOrder;
		private System.Windows.Forms.Label labelCaptureOrder;
		private System.Windows.Forms.Button buttonFingerMapping;
		private System.Windows.Forms.CheckBox checkBoxFingerMapping;
		private System.Windows.Forms.Label label15;
		private System.Windows.Forms.NumericUpDown numericUpDownMinutiaThreshold;
		private System.Windows.Forms.Label label16;
		private System.Windows.Forms.CheckBox checkLogBINErrors;
        private System.Windows.Forms.Button buttonGenDocBiometrics;
		private System.Windows.Forms.TextBox tbDataRootDir;
		private System.Windows.Forms.Label labelDataRootDirectory;
		private System.Windows.Forms.Button buttonSelectDataRootDir;
		private System.Windows.Forms.TabPage tabReaders;
		private System.Windows.Forms.ComboBox comboBoxBarcodeScannerPort;
		private System.Windows.Forms.Label label17;
		private System.Windows.Forms.ToolTip toolTip1;
		private System.Windows.Forms.Button buttonGenFips;
        private System.Windows.Forms.Button buttonReadFips;
		private CheckBox checkBoxFingerMapping10Print;
		private CheckBox check10Print;
		private Label label10PrintInstances;
		private NumericUpDown numericUpDown10PrintInstances;
		private Label label21;
		private TextBox textBox10PrintID;
		private Label label10PrintTestID;
		private Button buttonTest10PrintCapture;
		private Button button10PrintDeviceProperties;
		private Button button10PrintFinisherProperties;
		private Label label10PrintDevice;
		private ComboBox cb10PrintDevice;
		private Button buttonTest10PrintDisplay;
		private CheckBox checkFingerprintBase;
		private TabPage tabCerts;
		private Button buttonTestCertsDisplay;
		private TextBox textBoxTestCertsID;
		private Label labelCertsID;
		private Button buttonTestCertsCapture;
		private Button buttonCertsFinisherProperties;
		private Label label20;
		private Label label22;
		private Label labelCertsInstance;
		private NumericUpDown numericUpDownCertsInstance;
		private CheckBox checkCerts;
		private TextBox tbScannerDeviceName;
		private Button buttonSelectDevice;
		private CheckBox checkCertsDisplay;
		private CheckBox checkCertsCapture;
		private Label label18;
		private Label label19;
		private ComboBox comboBoxChipEncoderPort;
		private Label label23;
		private Button buttonOLEImageDB;
		private Label label24;
		private TextBox textBoxImageDBType;
		private CheckBox checkBoxEnableImageHistory;
		private Button buttonSearchBiometric;
        private CheckBox checkBoxQCVerify;
		private System.Windows.Forms.Button buttonFingerFinisherProperties;

		public DCSSDK_CaptureMgt()
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//

            m_scannerIF = new ScannerIF();

			m_ps = new DCSDEV.ParameterStore("DCSSDK_Mgt");

			this.ReadAndInstallCaptureMgtParameters();

			this.PopulateDeviceCombos();

			m_strPhotoName = "temp_img.bmp";
			m_strSigName = "temp_img.bmp";
			m_strFingerName = "temp_img.bmp";

			this.AdjustVisibilities();
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose(bool disposing)
		{
			if (disposing)
			{
				if (components != null)
				{
					components.Dispose();
				}
				this.CloseDisplays();
			}
			base.Dispose(disposing);
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.TabPage tab10Print;
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(DCSSDK_CaptureMgt));
            this.checkFingerprintBase = new System.Windows.Forms.CheckBox();
            this.buttonTest10PrintDisplay = new System.Windows.Forms.Button();
            this.checkBoxFingerMapping10Print = new System.Windows.Forms.CheckBox();
            this.check10Print = new System.Windows.Forms.CheckBox();
            this.label10PrintInstances = new System.Windows.Forms.Label();
            this.numericUpDown10PrintInstances = new System.Windows.Forms.NumericUpDown();
            this.label21 = new System.Windows.Forms.Label();
            this.textBox10PrintID = new System.Windows.Forms.TextBox();
            this.label10PrintTestID = new System.Windows.Forms.Label();
            this.buttonTest10PrintCapture = new System.Windows.Forms.Button();
            this.button10PrintDeviceProperties = new System.Windows.Forms.Button();
            this.button10PrintFinisherProperties = new System.Windows.Forms.Button();
            this.label10PrintDevice = new System.Windows.Forms.Label();
            this.cb10PrintDevice = new System.Windows.Forms.ComboBox();
            this.labelDCSSDKPath = new System.Windows.Forms.Label();
            this.tbMachineIndexFile = new System.Windows.Forms.TextBox();
            this.tbInstallDirectory = new System.Windows.Forms.TextBox();
            this.labelDCSDataPath = new System.Windows.Forms.Label();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPortrait = new System.Windows.Forms.TabPage();
            this.checkPortrait = new System.Windows.Forms.CheckBox();
            this.checkPortraitScanner = new System.Windows.Forms.CheckBox();
            this.label4 = new System.Windows.Forms.Label();
            this.numericUpDownPortraitInstances = new System.Windows.Forms.NumericUpDown();
            this.buttonTestPortraitDisplay = new System.Windows.Forms.Button();
            this.checkPortraitDisplay = new System.Windows.Forms.CheckBox();
            this.tbTestPhotoID = new System.Windows.Forms.TextBox();
            this.checkPortraitCapture = new System.Windows.Forms.CheckBox();
            this.cbPortraitDevice = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.buttonTestPortraitCapture = new System.Windows.Forms.Button();
            this.buttonPortraitDeviceProperties = new System.Windows.Forms.Button();
            this.buttonPortraitFinisherProperties = new System.Windows.Forms.Button();
            this.labelPortraitDevice = new System.Windows.Forms.Label();
            this.labelPortraitPath = new System.Windows.Forms.Label();
            this.tabSignature = new System.Windows.Forms.TabPage();
            this.checkSignature = new System.Windows.Forms.CheckBox();
            this.checkSignatureScanner = new System.Windows.Forms.CheckBox();
            this.label7 = new System.Windows.Forms.Label();
            this.numericUpDownSignatureInstances = new System.Windows.Forms.NumericUpDown();
            this.buttonTestSigDisplay = new System.Windows.Forms.Button();
            this.checkSignatureDisplay = new System.Windows.Forms.CheckBox();
            this.tbTestSigID = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.buttonTestSigCapture = new System.Windows.Forms.Button();
            this.buttonSigDeviceProperties = new System.Windows.Forms.Button();
            this.buttonSigFinisherProperties = new System.Windows.Forms.Button();
            this.checkSignatureCapture = new System.Windows.Forms.CheckBox();
            this.labelSignatureDevice = new System.Windows.Forms.Label();
            this.labelSignaturePath = new System.Windows.Forms.Label();
            this.cbSignatureDevice = new System.Windows.Forms.ComboBox();
            this.tabFingerprint = new System.Windows.Forms.TabPage();
            this.checkBoxQCVerify = new System.Windows.Forms.CheckBox();
            this.buttonFingerMapping = new System.Windows.Forms.Button();
            this.checkBoxFingerMapping = new System.Windows.Forms.CheckBox();
            this.checkGenFingerBIN = new System.Windows.Forms.CheckBox();
            this.checkDontSaveFingerImage = new System.Windows.Forms.CheckBox();
            this.checkFingerprint = new System.Windows.Forms.CheckBox();
            this.checkFingerprintScanner = new System.Windows.Forms.CheckBox();
            this.label9 = new System.Windows.Forms.Label();
            this.numericUpDownFingerInstances = new System.Windows.Forms.NumericUpDown();
            this.buttonTestFingerDisplay = new System.Windows.Forms.Button();
            this.checkFingerprintDisplay = new System.Windows.Forms.CheckBox();
            this.tbFingerID = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.buttonTestFingerCapture = new System.Windows.Forms.Button();
            this.buttonFingerDeviceProperties = new System.Windows.Forms.Button();
            this.buttonFingerFinisherProperties = new System.Windows.Forms.Button();
            this.checkFingerprintCapture = new System.Windows.Forms.CheckBox();
            this.labelFingerprintDevice = new System.Windows.Forms.Label();
            this.labelFingerprintPath = new System.Windows.Forms.Label();
            this.cbFingerprintDevice = new System.Windows.Forms.ComboBox();
            this.tabCerts = new System.Windows.Forms.TabPage();
            this.checkCertsDisplay = new System.Windows.Forms.CheckBox();
            this.checkCertsCapture = new System.Windows.Forms.CheckBox();
            this.tbScannerDeviceName = new System.Windows.Forms.TextBox();
            this.buttonSelectDevice = new System.Windows.Forms.Button();
            this.checkCerts = new System.Windows.Forms.CheckBox();
            this.labelCertsInstance = new System.Windows.Forms.Label();
            this.numericUpDownCertsInstance = new System.Windows.Forms.NumericUpDown();
            this.buttonTestCertsDisplay = new System.Windows.Forms.Button();
            this.textBoxTestCertsID = new System.Windows.Forms.TextBox();
            this.labelCertsID = new System.Windows.Forms.Label();
            this.buttonTestCertsCapture = new System.Windows.Forms.Button();
            this.buttonCertsFinisherProperties = new System.Windows.Forms.Button();
            this.label20 = new System.Windows.Forms.Label();
            this.label22 = new System.Windows.Forms.Label();
            this.tabCustomBiometric = new System.Windows.Forms.TabPage();
            this.buttonReadFips = new System.Windows.Forms.Button();
            this.buttonGenFips = new System.Windows.Forms.Button();
            this.buttonGenDocBiometrics = new System.Windows.Forms.Button();
            this.checkLogBINErrors = new System.Windows.Forms.CheckBox();
            this.buttonGenBiometric = new System.Windows.Forms.Button();
            this.buttonShowBiometrics = new System.Windows.Forms.Button();
            this.tbBiometricID = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.buttonSearchBiometric = new System.Windows.Forms.Button();
            this.label16 = new System.Windows.Forms.Label();
            this.label15 = new System.Windows.Forms.Label();
            this.numericUpDownMinutiaThreshold = new System.Windows.Forms.NumericUpDown();
            this.label12 = new System.Windows.Forms.Label();
            this.numericUpDownBIN2Threshold = new System.Windows.Forms.NumericUpDown();
            this.cbVerifyHow = new System.Windows.Forms.ComboBox();
            this.buttonVerifyBiometric = new System.Windows.Forms.Button();
            this.listBoxFingerBioTypes = new System.Windows.Forms.ListBox();
            this.tabReaders = new System.Windows.Forms.TabPage();
            this.comboBoxChipEncoderPort = new System.Windows.Forms.ComboBox();
            this.label23 = new System.Windows.Forms.Label();
            this.comboBoxBarcodeScannerPort = new System.Windows.Forms.ComboBox();
            this.label17 = new System.Windows.Forms.Label();
            this.tabScanner = new System.Windows.Forms.TabPage();
            this.label11 = new System.Windows.Forms.Label();
            this.textBoxScannerName = new System.Windows.Forms.TextBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.labelNumFingerprints = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.labelNumSignatures = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.labelNumPortraits = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.buttonScannerDisplay = new System.Windows.Forms.Button();
            this.textBoxScannerImageID = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.buttonScannerCapture = new System.Windows.Forms.Button();
            this.buttonScannerProperties = new System.Windows.Forms.Button();
            this.tbMachineConfigFile = new System.Windows.Forms.TextBox();
            this.labelConfigFile = new System.Windows.Forms.Label();
            this.tbDataRootDir = new System.Windows.Forms.TextBox();
            this.labelDataRootDirectory = new System.Windows.Forms.Label();
            this.buttonAbout = new System.Windows.Forms.Button();
            this.buttonCancel = new System.Windows.Forms.Button();
            this.buttonAccept = new System.Windows.Forms.Button();
            this.buttonValidate = new System.Windows.Forms.Button();
            this.comboBoxCaptureOrder = new System.Windows.Forms.ComboBox();
            this.labelCaptureOrder = new System.Windows.Forms.Label();
            this.buttonSelectDataRootDir = new System.Windows.Forms.Button();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.label18 = new System.Windows.Forms.Label();
            this.label19 = new System.Windows.Forms.Label();
            this.buttonOLEImageDB = new System.Windows.Forms.Button();
            this.label24 = new System.Windows.Forms.Label();
            this.textBoxImageDBType = new System.Windows.Forms.TextBox();
            this.checkBoxEnableImageHistory = new System.Windows.Forms.CheckBox();
            tab10Print = new System.Windows.Forms.TabPage();
            tab10Print.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDown10PrintInstances)).BeginInit();
            this.tabControl1.SuspendLayout();
            this.tabPortrait.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownPortraitInstances)).BeginInit();
            this.tabSignature.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownSignatureInstances)).BeginInit();
            this.tabFingerprint.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownFingerInstances)).BeginInit();
            this.tabCerts.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownCertsInstance)).BeginInit();
            this.tabCustomBiometric.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMinutiaThreshold)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownBIN2Threshold)).BeginInit();
            this.tabReaders.SuspendLayout();
            this.tabScanner.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // tab10Print
            // 
            tab10Print.BackColor = System.Drawing.Color.Transparent;
            tab10Print.Controls.Add(this.checkFingerprintBase);
            tab10Print.Controls.Add(this.buttonTest10PrintDisplay);
            tab10Print.Controls.Add(this.checkBoxFingerMapping10Print);
            tab10Print.Controls.Add(this.check10Print);
            tab10Print.Controls.Add(this.label10PrintInstances);
            tab10Print.Controls.Add(this.numericUpDown10PrintInstances);
            tab10Print.Controls.Add(this.label21);
            tab10Print.Controls.Add(this.textBox10PrintID);
            tab10Print.Controls.Add(this.label10PrintTestID);
            tab10Print.Controls.Add(this.buttonTest10PrintCapture);
            tab10Print.Controls.Add(this.button10PrintDeviceProperties);
            tab10Print.Controls.Add(this.button10PrintFinisherProperties);
            tab10Print.Controls.Add(this.label10PrintDevice);
            tab10Print.Controls.Add(this.cb10PrintDevice);
            resources.ApplyResources(tab10Print, "tab10Print");
            tab10Print.Name = "tab10Print";
            tab10Print.UseVisualStyleBackColor = true;
            // 
            // checkFingerprintBase
            // 
            this.checkFingerprintBase.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.checkFingerprintBase, "checkFingerprintBase");
            this.checkFingerprintBase.Name = "checkFingerprintBase";
            this.checkFingerprintBase.UseVisualStyleBackColor = false;
            // 
            // buttonTest10PrintDisplay
            // 
            this.buttonTest10PrintDisplay.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(255)))));
            resources.ApplyResources(this.buttonTest10PrintDisplay, "buttonTest10PrintDisplay");
            this.buttonTest10PrintDisplay.Name = "buttonTest10PrintDisplay";
            this.buttonTest10PrintDisplay.UseVisualStyleBackColor = false;
            this.buttonTest10PrintDisplay.Click += new System.EventHandler(this.buttonTest10PrintDisplay_Click);
            // 
            // checkBoxFingerMapping10Print
            // 
            this.checkBoxFingerMapping10Print.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.checkBoxFingerMapping10Print, "checkBoxFingerMapping10Print");
            this.checkBoxFingerMapping10Print.Name = "checkBoxFingerMapping10Print";
            this.checkBoxFingerMapping10Print.UseVisualStyleBackColor = false;
            // 
            // check10Print
            // 
            this.check10Print.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.check10Print, "check10Print");
            this.check10Print.Name = "check10Print";
            this.check10Print.UseVisualStyleBackColor = false;
            this.check10Print.Click += new System.EventHandler(this.check10Print_Click);
            // 
            // label10PrintInstances
            // 
            this.label10PrintInstances.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.label10PrintInstances, "label10PrintInstances");
            this.label10PrintInstances.Name = "label10PrintInstances";
            // 
            // numericUpDown10PrintInstances
            // 
            resources.ApplyResources(this.numericUpDown10PrintInstances, "numericUpDown10PrintInstances");
            this.numericUpDown10PrintInstances.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numericUpDown10PrintInstances.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numericUpDown10PrintInstances.Name = "numericUpDown10PrintInstances";
            this.numericUpDown10PrintInstances.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
            // 
            // label21
            // 
            this.label21.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.label21, "label21");
            this.label21.Name = "label21";
            // 
            // textBox10PrintID
            // 
            resources.ApplyResources(this.textBox10PrintID, "textBox10PrintID");
            this.textBox10PrintID.Name = "textBox10PrintID";
            // 
            // label10PrintTestID
            // 
            this.label10PrintTestID.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.label10PrintTestID, "label10PrintTestID");
            this.label10PrintTestID.Name = "label10PrintTestID";
            // 
            // buttonTest10PrintCapture
            // 
            this.buttonTest10PrintCapture.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(255)))));
            resources.ApplyResources(this.buttonTest10PrintCapture, "buttonTest10PrintCapture");
            this.buttonTest10PrintCapture.Name = "buttonTest10PrintCapture";
            this.buttonTest10PrintCapture.UseVisualStyleBackColor = false;
            this.buttonTest10PrintCapture.Click += new System.EventHandler(this.buttonTest10PrintCapture_Click);
            // 
            // button10PrintDeviceProperties
            // 
            this.button10PrintDeviceProperties.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(255)))));
            resources.ApplyResources(this.button10PrintDeviceProperties, "button10PrintDeviceProperties");
            this.button10PrintDeviceProperties.Name = "button10PrintDeviceProperties";
            this.button10PrintDeviceProperties.UseVisualStyleBackColor = false;
            this.button10PrintDeviceProperties.Click += new System.EventHandler(this.button10PrintDeviceProperties_Click);
            // 
            // button10PrintFinisherProperties
            // 
            this.button10PrintFinisherProperties.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(255)))));
            resources.ApplyResources(this.button10PrintFinisherProperties, "button10PrintFinisherProperties");
            this.button10PrintFinisherProperties.Name = "button10PrintFinisherProperties";
            this.button10PrintFinisherProperties.UseVisualStyleBackColor = false;
            this.button10PrintFinisherProperties.Click += new System.EventHandler(this.button10PrintFinisherProperties_Click);
            // 
            // label10PrintDevice
            // 
            this.label10PrintDevice.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.label10PrintDevice, "label10PrintDevice");
            this.label10PrintDevice.Name = "label10PrintDevice";
            // 
            // cb10PrintDevice
            // 
            resources.ApplyResources(this.cb10PrintDevice, "cb10PrintDevice");
            this.cb10PrintDevice.Name = "cb10PrintDevice";
            // 
            // labelDCSSDKPath
            // 
            this.labelDCSSDKPath.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.labelDCSSDKPath, "labelDCSSDKPath");
            this.labelDCSSDKPath.Name = "labelDCSSDKPath";
            // 
            // tbMachineIndexFile
            // 
            this.tbMachineIndexFile.BackColor = System.Drawing.SystemColors.Control;
            this.tbMachineIndexFile.BorderStyle = System.Windows.Forms.BorderStyle.None;
            resources.ApplyResources(this.tbMachineIndexFile, "tbMachineIndexFile");
            this.tbMachineIndexFile.Name = "tbMachineIndexFile";
            this.tbMachineIndexFile.ReadOnly = true;
            this.tbMachineIndexFile.TabStop = false;
            // 
            // tbInstallDirectory
            // 
            this.tbInstallDirectory.BackColor = System.Drawing.SystemColors.Control;
            this.tbInstallDirectory.BorderStyle = System.Windows.Forms.BorderStyle.None;
            resources.ApplyResources(this.tbInstallDirectory, "tbInstallDirectory");
            this.tbInstallDirectory.Name = "tbInstallDirectory";
            this.tbInstallDirectory.ReadOnly = true;
            this.tbInstallDirectory.TabStop = false;
            // 
            // labelDCSDataPath
            // 
            resources.ApplyResources(this.labelDCSDataPath, "labelDCSDataPath");
            this.labelDCSDataPath.Name = "labelDCSDataPath";
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPortrait);
            this.tabControl1.Controls.Add(this.tabSignature);
            this.tabControl1.Controls.Add(this.tabFingerprint);
            this.tabControl1.Controls.Add(this.tabCerts);
            this.tabControl1.Controls.Add(this.tabCustomBiometric);
            this.tabControl1.Controls.Add(tab10Print);
            this.tabControl1.Controls.Add(this.tabReaders);
            this.tabControl1.Controls.Add(this.tabScanner);
            resources.ApplyResources(this.tabControl1, "tabControl1");
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.SelectedIndexChanged += new System.EventHandler(this.tabControl1_SelectedIndexChanged);
            // 
            // tabPortrait
            // 
            this.tabPortrait.BackColor = System.Drawing.Color.Transparent;
            this.tabPortrait.Controls.Add(this.checkPortrait);
            this.tabPortrait.Controls.Add(this.checkPortraitScanner);
            this.tabPortrait.Controls.Add(this.label4);
            this.tabPortrait.Controls.Add(this.numericUpDownPortraitInstances);
            this.tabPortrait.Controls.Add(this.buttonTestPortraitDisplay);
            this.tabPortrait.Controls.Add(this.checkPortraitDisplay);
            this.tabPortrait.Controls.Add(this.tbTestPhotoID);
            this.tabPortrait.Controls.Add(this.checkPortraitCapture);
            this.tabPortrait.Controls.Add(this.cbPortraitDevice);
            this.tabPortrait.Controls.Add(this.label1);
            this.tabPortrait.Controls.Add(this.buttonTestPortraitCapture);
            this.tabPortrait.Controls.Add(this.buttonPortraitDeviceProperties);
            this.tabPortrait.Controls.Add(this.buttonPortraitFinisherProperties);
            this.tabPortrait.Controls.Add(this.labelPortraitDevice);
            this.tabPortrait.Controls.Add(this.labelPortraitPath);
            resources.ApplyResources(this.tabPortrait, "tabPortrait");
            this.tabPortrait.Name = "tabPortrait";
            this.tabPortrait.UseVisualStyleBackColor = true;
            // 
            // checkPortrait
            // 
            this.checkPortrait.BackColor = System.Drawing.SystemColors.Control;
            this.checkPortrait.Checked = true;
            this.checkPortrait.CheckState = System.Windows.Forms.CheckState.Checked;
            resources.ApplyResources(this.checkPortrait, "checkPortrait");
            this.checkPortrait.Name = "checkPortrait";
            this.checkPortrait.UseVisualStyleBackColor = false;
            this.checkPortrait.Click += new System.EventHandler(this.checkPortrait_Click);
            // 
            // checkPortraitScanner
            // 
            this.checkPortraitScanner.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.checkPortraitScanner, "checkPortraitScanner");
            this.checkPortraitScanner.Name = "checkPortraitScanner";
            this.checkPortraitScanner.UseVisualStyleBackColor = false;
            this.checkPortraitScanner.Click += new System.EventHandler(this.checkPortraitScanner_Click);
            // 
            // label4
            // 
            this.label4.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.label4, "label4");
            this.label4.Name = "label4";
            // 
            // numericUpDownPortraitInstances
            // 
            resources.ApplyResources(this.numericUpDownPortraitInstances, "numericUpDownPortraitInstances");
            this.numericUpDownPortraitInstances.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numericUpDownPortraitInstances.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numericUpDownPortraitInstances.Name = "numericUpDownPortraitInstances";
            this.numericUpDownPortraitInstances.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // buttonTestPortraitDisplay
            // 
            resources.ApplyResources(this.buttonTestPortraitDisplay, "buttonTestPortraitDisplay");
            this.buttonTestPortraitDisplay.Name = "buttonTestPortraitDisplay";
            this.buttonTestPortraitDisplay.Click += new System.EventHandler(this.buttonTestPortraitDisplay_Click);
            // 
            // checkPortraitDisplay
            // 
            this.checkPortraitDisplay.BackColor = System.Drawing.SystemColors.Control;
            this.checkPortraitDisplay.Checked = true;
            this.checkPortraitDisplay.CheckState = System.Windows.Forms.CheckState.Checked;
            resources.ApplyResources(this.checkPortraitDisplay, "checkPortraitDisplay");
            this.checkPortraitDisplay.Name = "checkPortraitDisplay";
            this.checkPortraitDisplay.UseVisualStyleBackColor = false;
            // 
            // tbTestPhotoID
            // 
            resources.ApplyResources(this.tbTestPhotoID, "tbTestPhotoID");
            this.tbTestPhotoID.Name = "tbTestPhotoID";
            // 
            // checkPortraitCapture
            // 
            this.checkPortraitCapture.BackColor = System.Drawing.SystemColors.Control;
            this.checkPortraitCapture.Checked = true;
            this.checkPortraitCapture.CheckState = System.Windows.Forms.CheckState.Checked;
            resources.ApplyResources(this.checkPortraitCapture, "checkPortraitCapture");
            this.checkPortraitCapture.Name = "checkPortraitCapture";
            this.checkPortraitCapture.UseVisualStyleBackColor = false;
            this.checkPortraitCapture.Click += new System.EventHandler(this.checkPortraitCapture_Click);
            // 
            // cbPortraitDevice
            // 
            resources.ApplyResources(this.cbPortraitDevice, "cbPortraitDevice");
            this.cbPortraitDevice.Name = "cbPortraitDevice";
            // 
            // label1
            // 
            this.label1.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.label1, "label1");
            this.label1.Name = "label1";
            // 
            // buttonTestPortraitCapture
            // 
            resources.ApplyResources(this.buttonTestPortraitCapture, "buttonTestPortraitCapture");
            this.buttonTestPortraitCapture.Name = "buttonTestPortraitCapture";
            this.buttonTestPortraitCapture.Click += new System.EventHandler(this.buttonTestPortraitCapture_Click);
            // 
            // buttonPortraitDeviceProperties
            // 
            resources.ApplyResources(this.buttonPortraitDeviceProperties, "buttonPortraitDeviceProperties");
            this.buttonPortraitDeviceProperties.Name = "buttonPortraitDeviceProperties";
            this.buttonPortraitDeviceProperties.Click += new System.EventHandler(this.buttonPortraitDeviceProperties_Click);
            // 
            // buttonPortraitFinisherProperties
            // 
            resources.ApplyResources(this.buttonPortraitFinisherProperties, "buttonPortraitFinisherProperties");
            this.buttonPortraitFinisherProperties.Name = "buttonPortraitFinisherProperties";
            this.buttonPortraitFinisherProperties.Click += new System.EventHandler(this.buttonPortraitFinisherProperties_Click);
            // 
            // labelPortraitDevice
            // 
            this.labelPortraitDevice.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.labelPortraitDevice, "labelPortraitDevice");
            this.labelPortraitDevice.Name = "labelPortraitDevice";
            // 
            // labelPortraitPath
            // 
            this.labelPortraitPath.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.labelPortraitPath, "labelPortraitPath");
            this.labelPortraitPath.Name = "labelPortraitPath";
            // 
            // tabSignature
            // 
            this.tabSignature.BackColor = System.Drawing.Color.Transparent;
            this.tabSignature.Controls.Add(this.checkSignature);
            this.tabSignature.Controls.Add(this.checkSignatureScanner);
            this.tabSignature.Controls.Add(this.label7);
            this.tabSignature.Controls.Add(this.numericUpDownSignatureInstances);
            this.tabSignature.Controls.Add(this.buttonTestSigDisplay);
            this.tabSignature.Controls.Add(this.checkSignatureDisplay);
            this.tabSignature.Controls.Add(this.tbTestSigID);
            this.tabSignature.Controls.Add(this.label2);
            this.tabSignature.Controls.Add(this.buttonTestSigCapture);
            this.tabSignature.Controls.Add(this.buttonSigDeviceProperties);
            this.tabSignature.Controls.Add(this.buttonSigFinisherProperties);
            this.tabSignature.Controls.Add(this.checkSignatureCapture);
            this.tabSignature.Controls.Add(this.labelSignatureDevice);
            this.tabSignature.Controls.Add(this.labelSignaturePath);
            this.tabSignature.Controls.Add(this.cbSignatureDevice);
            resources.ApplyResources(this.tabSignature, "tabSignature");
            this.tabSignature.Name = "tabSignature";
            this.tabSignature.UseVisualStyleBackColor = true;
            // 
            // checkSignature
            // 
            this.checkSignature.BackColor = System.Drawing.SystemColors.Control;
            this.checkSignature.Checked = true;
            this.checkSignature.CheckState = System.Windows.Forms.CheckState.Checked;
            resources.ApplyResources(this.checkSignature, "checkSignature");
            this.checkSignature.Name = "checkSignature";
            this.checkSignature.UseVisualStyleBackColor = false;
            this.checkSignature.Click += new System.EventHandler(this.checkSignature_Click);
            // 
            // checkSignatureScanner
            // 
            this.checkSignatureScanner.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.checkSignatureScanner, "checkSignatureScanner");
            this.checkSignatureScanner.Name = "checkSignatureScanner";
            this.checkSignatureScanner.UseVisualStyleBackColor = false;
            this.checkSignatureScanner.Click += new System.EventHandler(this.checkSignatureScanner_Click);
            // 
            // label7
            // 
            this.label7.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.label7, "label7");
            this.label7.Name = "label7";
            // 
            // numericUpDownSignatureInstances
            // 
            resources.ApplyResources(this.numericUpDownSignatureInstances, "numericUpDownSignatureInstances");
            this.numericUpDownSignatureInstances.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numericUpDownSignatureInstances.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numericUpDownSignatureInstances.Name = "numericUpDownSignatureInstances";
            this.numericUpDownSignatureInstances.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // buttonTestSigDisplay
            // 
            this.buttonTestSigDisplay.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(255)))));
            resources.ApplyResources(this.buttonTestSigDisplay, "buttonTestSigDisplay");
            this.buttonTestSigDisplay.Name = "buttonTestSigDisplay";
            this.buttonTestSigDisplay.UseVisualStyleBackColor = false;
            this.buttonTestSigDisplay.Click += new System.EventHandler(this.buttonTestSigDisplay_Click);
            // 
            // checkSignatureDisplay
            // 
            this.checkSignatureDisplay.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.checkSignatureDisplay, "checkSignatureDisplay");
            this.checkSignatureDisplay.Name = "checkSignatureDisplay";
            this.checkSignatureDisplay.UseVisualStyleBackColor = false;
            // 
            // tbTestSigID
            // 
            resources.ApplyResources(this.tbTestSigID, "tbTestSigID");
            this.tbTestSigID.Name = "tbTestSigID";
            // 
            // label2
            // 
            this.label2.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.label2, "label2");
            this.label2.Name = "label2";
            // 
            // buttonTestSigCapture
            // 
            this.buttonTestSigCapture.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(255)))));
            resources.ApplyResources(this.buttonTestSigCapture, "buttonTestSigCapture");
            this.buttonTestSigCapture.Name = "buttonTestSigCapture";
            this.buttonTestSigCapture.UseVisualStyleBackColor = false;
            this.buttonTestSigCapture.Click += new System.EventHandler(this.buttonTestSigCapture_Click);
            // 
            // buttonSigDeviceProperties
            // 
            this.buttonSigDeviceProperties.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(255)))));
            resources.ApplyResources(this.buttonSigDeviceProperties, "buttonSigDeviceProperties");
            this.buttonSigDeviceProperties.Name = "buttonSigDeviceProperties";
            this.buttonSigDeviceProperties.UseVisualStyleBackColor = false;
            this.buttonSigDeviceProperties.Click += new System.EventHandler(this.buttonSigDeviceProperties_Click);
            // 
            // buttonSigFinisherProperties
            // 
            this.buttonSigFinisherProperties.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(255)))));
            resources.ApplyResources(this.buttonSigFinisherProperties, "buttonSigFinisherProperties");
            this.buttonSigFinisherProperties.Name = "buttonSigFinisherProperties";
            this.buttonSigFinisherProperties.UseVisualStyleBackColor = false;
            this.buttonSigFinisherProperties.Click += new System.EventHandler(this.buttonSigFinisherProperties_Click);
            // 
            // checkSignatureCapture
            // 
            this.checkSignatureCapture.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.checkSignatureCapture, "checkSignatureCapture");
            this.checkSignatureCapture.Name = "checkSignatureCapture";
            this.checkSignatureCapture.UseVisualStyleBackColor = false;
            this.checkSignatureCapture.Click += new System.EventHandler(this.checkSignatureCapture_Click);
            // 
            // labelSignatureDevice
            // 
            this.labelSignatureDevice.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.labelSignatureDevice, "labelSignatureDevice");
            this.labelSignatureDevice.Name = "labelSignatureDevice";
            // 
            // labelSignaturePath
            // 
            resources.ApplyResources(this.labelSignaturePath, "labelSignaturePath");
            this.labelSignaturePath.Name = "labelSignaturePath";
            // 
            // cbSignatureDevice
            // 
            resources.ApplyResources(this.cbSignatureDevice, "cbSignatureDevice");
            this.cbSignatureDevice.Name = "cbSignatureDevice";
            // 
            // tabFingerprint
            // 
            this.tabFingerprint.BackColor = System.Drawing.Color.Transparent;
            this.tabFingerprint.Controls.Add(this.checkBoxQCVerify);
            this.tabFingerprint.Controls.Add(this.buttonFingerMapping);
            this.tabFingerprint.Controls.Add(this.checkBoxFingerMapping);
            this.tabFingerprint.Controls.Add(this.checkGenFingerBIN);
            this.tabFingerprint.Controls.Add(this.checkDontSaveFingerImage);
            this.tabFingerprint.Controls.Add(this.checkFingerprint);
            this.tabFingerprint.Controls.Add(this.checkFingerprintScanner);
            this.tabFingerprint.Controls.Add(this.label9);
            this.tabFingerprint.Controls.Add(this.numericUpDownFingerInstances);
            this.tabFingerprint.Controls.Add(this.buttonTestFingerDisplay);
            this.tabFingerprint.Controls.Add(this.checkFingerprintDisplay);
            this.tabFingerprint.Controls.Add(this.tbFingerID);
            this.tabFingerprint.Controls.Add(this.label3);
            this.tabFingerprint.Controls.Add(this.buttonTestFingerCapture);
            this.tabFingerprint.Controls.Add(this.buttonFingerDeviceProperties);
            this.tabFingerprint.Controls.Add(this.buttonFingerFinisherProperties);
            this.tabFingerprint.Controls.Add(this.checkFingerprintCapture);
            this.tabFingerprint.Controls.Add(this.labelFingerprintDevice);
            this.tabFingerprint.Controls.Add(this.labelFingerprintPath);
            this.tabFingerprint.Controls.Add(this.cbFingerprintDevice);
            resources.ApplyResources(this.tabFingerprint, "tabFingerprint");
            this.tabFingerprint.Name = "tabFingerprint";
            this.tabFingerprint.UseVisualStyleBackColor = true;
            // 
            // checkBoxQCVerify
            // 
            resources.ApplyResources(this.checkBoxQCVerify, "checkBoxQCVerify");
            this.checkBoxQCVerify.Name = "checkBoxQCVerify";
            // 
            // buttonFingerMapping
            // 
            resources.ApplyResources(this.buttonFingerMapping, "buttonFingerMapping");
            this.buttonFingerMapping.Name = "buttonFingerMapping";
            this.buttonFingerMapping.Click += new System.EventHandler(this.buttonFingerMapping_Click);
            // 
            // checkBoxFingerMapping
            // 
            this.checkBoxFingerMapping.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.checkBoxFingerMapping, "checkBoxFingerMapping");
            this.checkBoxFingerMapping.Name = "checkBoxFingerMapping";
            this.checkBoxFingerMapping.UseVisualStyleBackColor = false;
            this.checkBoxFingerMapping.Click += new System.EventHandler(this.checkBoxFingerMapping_Click);
            // 
            // checkGenFingerBIN
            // 
            this.checkGenFingerBIN.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.checkGenFingerBIN, "checkGenFingerBIN");
            this.checkGenFingerBIN.Name = "checkGenFingerBIN";
            this.checkGenFingerBIN.UseVisualStyleBackColor = false;
            this.checkGenFingerBIN.Click += new System.EventHandler(this.checkGenFingerBIN_Click);
            // 
            // checkDontSaveFingerImage
            // 
            this.checkDontSaveFingerImage.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.checkDontSaveFingerImage, "checkDontSaveFingerImage");
            this.checkDontSaveFingerImage.Name = "checkDontSaveFingerImage";
            this.checkDontSaveFingerImage.UseVisualStyleBackColor = false;
            this.checkDontSaveFingerImage.Click += new System.EventHandler(this.checkDontSaveFingerImage_Click);
            // 
            // checkFingerprint
            // 
            this.checkFingerprint.BackColor = System.Drawing.SystemColors.Control;
            this.checkFingerprint.Checked = true;
            this.checkFingerprint.CheckState = System.Windows.Forms.CheckState.Checked;
            resources.ApplyResources(this.checkFingerprint, "checkFingerprint");
            this.checkFingerprint.Name = "checkFingerprint";
            this.checkFingerprint.UseVisualStyleBackColor = false;
            this.checkFingerprint.Click += new System.EventHandler(this.checkFingerprint_Click);
            // 
            // checkFingerprintScanner
            // 
            this.checkFingerprintScanner.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.checkFingerprintScanner, "checkFingerprintScanner");
            this.checkFingerprintScanner.Name = "checkFingerprintScanner";
            this.checkFingerprintScanner.UseVisualStyleBackColor = false;
            this.checkFingerprintScanner.Click += new System.EventHandler(this.checkFingerprintScanner_Click);
            // 
            // label9
            // 
            this.label9.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.label9, "label9");
            this.label9.Name = "label9";
            // 
            // numericUpDownFingerInstances
            // 
            resources.ApplyResources(this.numericUpDownFingerInstances, "numericUpDownFingerInstances");
            this.numericUpDownFingerInstances.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.numericUpDownFingerInstances.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numericUpDownFingerInstances.Name = "numericUpDownFingerInstances";
            this.numericUpDownFingerInstances.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // buttonTestFingerDisplay
            // 
            this.buttonTestFingerDisplay.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(255)))));
            resources.ApplyResources(this.buttonTestFingerDisplay, "buttonTestFingerDisplay");
            this.buttonTestFingerDisplay.Name = "buttonTestFingerDisplay";
            this.buttonTestFingerDisplay.UseVisualStyleBackColor = false;
            this.buttonTestFingerDisplay.Click += new System.EventHandler(this.buttonTestFingerDisplay_Click);
            // 
            // checkFingerprintDisplay
            // 
            this.checkFingerprintDisplay.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.checkFingerprintDisplay, "checkFingerprintDisplay");
            this.checkFingerprintDisplay.Name = "checkFingerprintDisplay";
            this.checkFingerprintDisplay.UseVisualStyleBackColor = false;
            // 
            // tbFingerID
            // 
            resources.ApplyResources(this.tbFingerID, "tbFingerID");
            this.tbFingerID.Name = "tbFingerID";
            // 
            // label3
            // 
            this.label3.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.label3, "label3");
            this.label3.Name = "label3";
            // 
            // buttonTestFingerCapture
            // 
            this.buttonTestFingerCapture.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(255)))));
            resources.ApplyResources(this.buttonTestFingerCapture, "buttonTestFingerCapture");
            this.buttonTestFingerCapture.Name = "buttonTestFingerCapture";
            this.buttonTestFingerCapture.UseVisualStyleBackColor = false;
            this.buttonTestFingerCapture.Click += new System.EventHandler(this.buttonTestFingerCapture_Click);
            // 
            // buttonFingerDeviceProperties
            // 
            this.buttonFingerDeviceProperties.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(255)))));
            resources.ApplyResources(this.buttonFingerDeviceProperties, "buttonFingerDeviceProperties");
            this.buttonFingerDeviceProperties.Name = "buttonFingerDeviceProperties";
            this.buttonFingerDeviceProperties.UseVisualStyleBackColor = false;
            this.buttonFingerDeviceProperties.Click += new System.EventHandler(this.buttonFingerDeviceProperties_Click);
            // 
            // buttonFingerFinisherProperties
            // 
            this.buttonFingerFinisherProperties.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(255)))));
            resources.ApplyResources(this.buttonFingerFinisherProperties, "buttonFingerFinisherProperties");
            this.buttonFingerFinisherProperties.Name = "buttonFingerFinisherProperties";
            this.buttonFingerFinisherProperties.UseVisualStyleBackColor = false;
            this.buttonFingerFinisherProperties.Click += new System.EventHandler(this.buttonFingerFinisherProperties_Click);
            // 
            // checkFingerprintCapture
            // 
            this.checkFingerprintCapture.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.checkFingerprintCapture, "checkFingerprintCapture");
            this.checkFingerprintCapture.Name = "checkFingerprintCapture";
            this.checkFingerprintCapture.UseVisualStyleBackColor = false;
            this.checkFingerprintCapture.Click += new System.EventHandler(this.checkFingerprintCapture_Click);
            // 
            // labelFingerprintDevice
            // 
            this.labelFingerprintDevice.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.labelFingerprintDevice, "labelFingerprintDevice");
            this.labelFingerprintDevice.Name = "labelFingerprintDevice";
            // 
            // labelFingerprintPath
            // 
            this.labelFingerprintPath.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.labelFingerprintPath, "labelFingerprintPath");
            this.labelFingerprintPath.Name = "labelFingerprintPath";
            // 
            // cbFingerprintDevice
            // 
            resources.ApplyResources(this.cbFingerprintDevice, "cbFingerprintDevice");
            this.cbFingerprintDevice.Name = "cbFingerprintDevice";
            // 
            // tabCerts
            // 
            this.tabCerts.BackColor = System.Drawing.Color.Transparent;
            this.tabCerts.Controls.Add(this.checkCertsDisplay);
            this.tabCerts.Controls.Add(this.checkCertsCapture);
            this.tabCerts.Controls.Add(this.tbScannerDeviceName);
            this.tabCerts.Controls.Add(this.buttonSelectDevice);
            this.tabCerts.Controls.Add(this.checkCerts);
            this.tabCerts.Controls.Add(this.labelCertsInstance);
            this.tabCerts.Controls.Add(this.numericUpDownCertsInstance);
            this.tabCerts.Controls.Add(this.buttonTestCertsDisplay);
            this.tabCerts.Controls.Add(this.textBoxTestCertsID);
            this.tabCerts.Controls.Add(this.labelCertsID);
            this.tabCerts.Controls.Add(this.buttonTestCertsCapture);
            this.tabCerts.Controls.Add(this.buttonCertsFinisherProperties);
            this.tabCerts.Controls.Add(this.label20);
            this.tabCerts.Controls.Add(this.label22);
            resources.ApplyResources(this.tabCerts, "tabCerts");
            this.tabCerts.Name = "tabCerts";
            this.tabCerts.UseVisualStyleBackColor = true;
            // 
            // checkCertsDisplay
            // 
            this.checkCertsDisplay.BackColor = System.Drawing.SystemColors.Control;
            this.checkCertsDisplay.Checked = true;
            this.checkCertsDisplay.CheckState = System.Windows.Forms.CheckState.Checked;
            resources.ApplyResources(this.checkCertsDisplay, "checkCertsDisplay");
            this.checkCertsDisplay.Name = "checkCertsDisplay";
            this.checkCertsDisplay.UseVisualStyleBackColor = false;
            // 
            // checkCertsCapture
            // 
            this.checkCertsCapture.BackColor = System.Drawing.SystemColors.Control;
            this.checkCertsCapture.Checked = true;
            this.checkCertsCapture.CheckState = System.Windows.Forms.CheckState.Checked;
            resources.ApplyResources(this.checkCertsCapture, "checkCertsCapture");
            this.checkCertsCapture.Name = "checkCertsCapture";
            this.checkCertsCapture.UseVisualStyleBackColor = false;
            this.checkCertsCapture.Click += new System.EventHandler(this.checkCertsScan_Click);
            // 
            // tbScannerDeviceName
            // 
            this.tbScannerDeviceName.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.tbScannerDeviceName, "tbScannerDeviceName");
            this.tbScannerDeviceName.Name = "tbScannerDeviceName";
            this.tbScannerDeviceName.ReadOnly = true;
            // 
            // buttonSelectDevice
            // 
            resources.ApplyResources(this.buttonSelectDevice, "buttonSelectDevice");
            this.buttonSelectDevice.Name = "buttonSelectDevice";
            this.buttonSelectDevice.Click += new System.EventHandler(this.buttonSelectDevice_Click);
            // 
            // checkCerts
            // 
            this.checkCerts.BackColor = System.Drawing.SystemColors.Control;
            this.checkCerts.Checked = true;
            this.checkCerts.CheckState = System.Windows.Forms.CheckState.Checked;
            resources.ApplyResources(this.checkCerts, "checkCerts");
            this.checkCerts.Name = "checkCerts";
            this.checkCerts.UseVisualStyleBackColor = false;
            this.checkCerts.Click += new System.EventHandler(this.checkCerts_Click);
            // 
            // labelCertsInstance
            // 
            this.labelCertsInstance.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.labelCertsInstance, "labelCertsInstance");
            this.labelCertsInstance.Name = "labelCertsInstance";
            // 
            // numericUpDownCertsInstance
            // 
            resources.ApplyResources(this.numericUpDownCertsInstance, "numericUpDownCertsInstance");
            this.numericUpDownCertsInstance.Name = "numericUpDownCertsInstance";
            // 
            // buttonTestCertsDisplay
            // 
            resources.ApplyResources(this.buttonTestCertsDisplay, "buttonTestCertsDisplay");
            this.buttonTestCertsDisplay.Name = "buttonTestCertsDisplay";
            this.buttonTestCertsDisplay.Click += new System.EventHandler(this.buttonTestCertsDisplay_Click);
            // 
            // textBoxTestCertsID
            // 
            resources.ApplyResources(this.textBoxTestCertsID, "textBoxTestCertsID");
            this.textBoxTestCertsID.Name = "textBoxTestCertsID";
            // 
            // labelCertsID
            // 
            this.labelCertsID.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.labelCertsID, "labelCertsID");
            this.labelCertsID.Name = "labelCertsID";
            // 
            // buttonTestCertsCapture
            // 
            resources.ApplyResources(this.buttonTestCertsCapture, "buttonTestCertsCapture");
            this.buttonTestCertsCapture.Name = "buttonTestCertsCapture";
            this.buttonTestCertsCapture.Click += new System.EventHandler(this.buttonTestCertsCapture_Click);
            // 
            // buttonCertsFinisherProperties
            // 
            resources.ApplyResources(this.buttonCertsFinisherProperties, "buttonCertsFinisherProperties");
            this.buttonCertsFinisherProperties.Name = "buttonCertsFinisherProperties";
            this.buttonCertsFinisherProperties.Click += new System.EventHandler(this.buttonCertsFinisherProperties_Click);
            // 
            // label20
            // 
            this.label20.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.label20, "label20");
            this.label20.Name = "label20";
            // 
            // label22
            // 
            this.label22.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.label22, "label22");
            this.label22.Name = "label22";
            // 
            // tabCustomBiometric
            // 
            this.tabCustomBiometric.BackColor = System.Drawing.Color.Transparent;
            this.tabCustomBiometric.Controls.Add(this.buttonReadFips);
            this.tabCustomBiometric.Controls.Add(this.buttonGenFips);
            this.tabCustomBiometric.Controls.Add(this.buttonGenDocBiometrics);
            this.tabCustomBiometric.Controls.Add(this.checkLogBINErrors);
            this.tabCustomBiometric.Controls.Add(this.buttonGenBiometric);
            this.tabCustomBiometric.Controls.Add(this.buttonShowBiometrics);
            this.tabCustomBiometric.Controls.Add(this.tbBiometricID);
            this.tabCustomBiometric.Controls.Add(this.label10);
            this.tabCustomBiometric.Controls.Add(this.groupBox2);
            resources.ApplyResources(this.tabCustomBiometric, "tabCustomBiometric");
            this.tabCustomBiometric.Name = "tabCustomBiometric";
            this.tabCustomBiometric.UseVisualStyleBackColor = true;
            // 
            // buttonReadFips
            // 
            resources.ApplyResources(this.buttonReadFips, "buttonReadFips");
            this.buttonReadFips.Name = "buttonReadFips";
            this.buttonReadFips.Click += new System.EventHandler(this.buttonReadFips_Click);
            // 
            // buttonGenFips
            // 
            resources.ApplyResources(this.buttonGenFips, "buttonGenFips");
            this.buttonGenFips.Name = "buttonGenFips";
            this.buttonGenFips.Click += new System.EventHandler(this.buttonGenFips_Click);
            // 
            // buttonGenDocBiometrics
            // 
            resources.ApplyResources(this.buttonGenDocBiometrics, "buttonGenDocBiometrics");
            this.buttonGenDocBiometrics.Name = "buttonGenDocBiometrics";
            this.buttonGenDocBiometrics.Click += new System.EventHandler(this.buttonGenDocBiometrics_Click);
            // 
            // checkLogBINErrors
            // 
            resources.ApplyResources(this.checkLogBINErrors, "checkLogBINErrors");
            this.checkLogBINErrors.Name = "checkLogBINErrors";
            this.checkLogBINErrors.UseVisualStyleBackColor = false;
            // 
            // buttonGenBiometric
            // 
            resources.ApplyResources(this.buttonGenBiometric, "buttonGenBiometric");
            this.buttonGenBiometric.Name = "buttonGenBiometric";
            this.buttonGenBiometric.Click += new System.EventHandler(this.buttonGenBiometric_Click);
            // 
            // buttonShowBiometrics
            // 
            resources.ApplyResources(this.buttonShowBiometrics, "buttonShowBiometrics");
            this.buttonShowBiometrics.Name = "buttonShowBiometrics";
            this.buttonShowBiometrics.Click += new System.EventHandler(this.buttonShowBiometrics_Click);
            // 
            // tbBiometricID
            // 
            resources.ApplyResources(this.tbBiometricID, "tbBiometricID");
            this.tbBiometricID.Name = "tbBiometricID";
            // 
            // label10
            // 
            this.label10.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.label10, "label10");
            this.label10.Name = "label10";
            // 
            // groupBox2
            // 
            this.groupBox2.BackColor = System.Drawing.SystemColors.Control;
            this.groupBox2.Controls.Add(this.buttonSearchBiometric);
            this.groupBox2.Controls.Add(this.label16);
            this.groupBox2.Controls.Add(this.label15);
            this.groupBox2.Controls.Add(this.numericUpDownMinutiaThreshold);
            this.groupBox2.Controls.Add(this.label12);
            this.groupBox2.Controls.Add(this.numericUpDownBIN2Threshold);
            this.groupBox2.Controls.Add(this.cbVerifyHow);
            this.groupBox2.Controls.Add(this.buttonVerifyBiometric);
            this.groupBox2.Controls.Add(this.listBoxFingerBioTypes);
            resources.ApplyResources(this.groupBox2, "groupBox2");
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.TabStop = false;
            // 
            // buttonSearchBiometric
            // 
            resources.ApplyResources(this.buttonSearchBiometric, "buttonSearchBiometric");
            this.buttonSearchBiometric.Name = "buttonSearchBiometric";
            this.buttonSearchBiometric.Click += new System.EventHandler(this.buttonSearchBiometric_Click);
            // 
            // label16
            // 
            resources.ApplyResources(this.label16, "label16");
            this.label16.Name = "label16";
            // 
            // label15
            // 
            resources.ApplyResources(this.label15, "label15");
            this.label15.Name = "label15";
            // 
            // numericUpDownMinutiaThreshold
            // 
            resources.ApplyResources(this.numericUpDownMinutiaThreshold, "numericUpDownMinutiaThreshold");
            this.numericUpDownMinutiaThreshold.Maximum = new decimal(new int[] {
            99,
            0,
            0,
            0});
            this.numericUpDownMinutiaThreshold.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numericUpDownMinutiaThreshold.Name = "numericUpDownMinutiaThreshold";
            this.numericUpDownMinutiaThreshold.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numericUpDownMinutiaThreshold.ValueChanged += new System.EventHandler(this.numericUpDownMinutiaThreshold_ValueChanged);
            // 
            // label12
            // 
            resources.ApplyResources(this.label12, "label12");
            this.label12.Name = "label12";
            // 
            // numericUpDownBIN2Threshold
            // 
            resources.ApplyResources(this.numericUpDownBIN2Threshold, "numericUpDownBIN2Threshold");
            this.numericUpDownBIN2Threshold.Maximum = new decimal(new int[] {
            99,
            0,
            0,
            0});
            this.numericUpDownBIN2Threshold.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numericUpDownBIN2Threshold.Name = "numericUpDownBIN2Threshold";
            this.numericUpDownBIN2Threshold.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.numericUpDownBIN2Threshold.ValueChanged += new System.EventHandler(this.numericUpDownBIN2Threshold_ValueChanged);
            // 
            // cbVerifyHow
            // 
            this.cbVerifyHow.Items.AddRange(new object[] {
            resources.GetString("cbVerifyHow.Items"),
            resources.GetString("cbVerifyHow.Items1"),
            resources.GetString("cbVerifyHow.Items2"),
            resources.GetString("cbVerifyHow.Items3")});
            resources.ApplyResources(this.cbVerifyHow, "cbVerifyHow");
            this.cbVerifyHow.Name = "cbVerifyHow";
            // 
            // buttonVerifyBiometric
            // 
            resources.ApplyResources(this.buttonVerifyBiometric, "buttonVerifyBiometric");
            this.buttonVerifyBiometric.Name = "buttonVerifyBiometric";
            this.buttonVerifyBiometric.Click += new System.EventHandler(this.buttonVerifyBiometric_Click);
            // 
            // listBoxFingerBioTypes
            // 
            this.listBoxFingerBioTypes.Items.AddRange(new object[] {
            resources.GetString("listBoxFingerBioTypes.Items"),
            resources.GetString("listBoxFingerBioTypes.Items1"),
            resources.GetString("listBoxFingerBioTypes.Items2")});
            resources.ApplyResources(this.listBoxFingerBioTypes, "listBoxFingerBioTypes");
            this.listBoxFingerBioTypes.Name = "listBoxFingerBioTypes";
            this.listBoxFingerBioTypes.SelectedIndexChanged += new System.EventHandler(this.listBoxFingerBioTypes_SelectedIndexChanged);
            // 
            // tabReaders
            // 
            this.tabReaders.BackColor = System.Drawing.Color.Transparent;
            this.tabReaders.Controls.Add(this.comboBoxChipEncoderPort);
            this.tabReaders.Controls.Add(this.label23);
            this.tabReaders.Controls.Add(this.comboBoxBarcodeScannerPort);
            this.tabReaders.Controls.Add(this.label17);
            resources.ApplyResources(this.tabReaders, "tabReaders");
            this.tabReaders.Name = "tabReaders";
            this.tabReaders.UseVisualStyleBackColor = true;
            // 
            // comboBoxChipEncoderPort
            // 
            this.comboBoxChipEncoderPort.Items.AddRange(new object[] {
            resources.GetString("comboBoxChipEncoderPort.Items"),
            resources.GetString("comboBoxChipEncoderPort.Items1"),
            resources.GetString("comboBoxChipEncoderPort.Items2"),
            resources.GetString("comboBoxChipEncoderPort.Items3"),
            resources.GetString("comboBoxChipEncoderPort.Items4"),
            resources.GetString("comboBoxChipEncoderPort.Items5"),
            resources.GetString("comboBoxChipEncoderPort.Items6"),
            resources.GetString("comboBoxChipEncoderPort.Items7"),
            resources.GetString("comboBoxChipEncoderPort.Items8"),
            resources.GetString("comboBoxChipEncoderPort.Items9"),
            resources.GetString("comboBoxChipEncoderPort.Items10"),
            resources.GetString("comboBoxChipEncoderPort.Items11"),
            resources.GetString("comboBoxChipEncoderPort.Items12"),
            resources.GetString("comboBoxChipEncoderPort.Items13"),
            resources.GetString("comboBoxChipEncoderPort.Items14")});
            resources.ApplyResources(this.comboBoxChipEncoderPort, "comboBoxChipEncoderPort");
            this.comboBoxChipEncoderPort.Name = "comboBoxChipEncoderPort";
            // 
            // label23
            // 
            this.label23.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.label23, "label23");
            this.label23.Name = "label23";
            // 
            // comboBoxBarcodeScannerPort
            // 
            this.comboBoxBarcodeScannerPort.Items.AddRange(new object[] {
            resources.GetString("comboBoxBarcodeScannerPort.Items"),
            resources.GetString("comboBoxBarcodeScannerPort.Items1"),
            resources.GetString("comboBoxBarcodeScannerPort.Items2"),
            resources.GetString("comboBoxBarcodeScannerPort.Items3"),
            resources.GetString("comboBoxBarcodeScannerPort.Items4"),
            resources.GetString("comboBoxBarcodeScannerPort.Items5"),
            resources.GetString("comboBoxBarcodeScannerPort.Items6"),
            resources.GetString("comboBoxBarcodeScannerPort.Items7"),
            resources.GetString("comboBoxBarcodeScannerPort.Items8"),
            resources.GetString("comboBoxBarcodeScannerPort.Items9"),
            resources.GetString("comboBoxBarcodeScannerPort.Items10"),
            resources.GetString("comboBoxBarcodeScannerPort.Items11"),
            resources.GetString("comboBoxBarcodeScannerPort.Items12"),
            resources.GetString("comboBoxBarcodeScannerPort.Items13"),
            resources.GetString("comboBoxBarcodeScannerPort.Items14")});
            resources.ApplyResources(this.comboBoxBarcodeScannerPort, "comboBoxBarcodeScannerPort");
            this.comboBoxBarcodeScannerPort.Name = "comboBoxBarcodeScannerPort";
            // 
            // label17
            // 
            this.label17.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.label17, "label17");
            this.label17.Name = "label17";
            // 
            // tabScanner
            // 
            this.tabScanner.BackColor = System.Drawing.Color.Transparent;
            this.tabScanner.Controls.Add(this.label11);
            this.tabScanner.Controls.Add(this.textBoxScannerName);
            this.tabScanner.Controls.Add(this.groupBox1);
            this.tabScanner.Controls.Add(this.buttonScannerDisplay);
            this.tabScanner.Controls.Add(this.textBoxScannerImageID);
            this.tabScanner.Controls.Add(this.label6);
            this.tabScanner.Controls.Add(this.buttonScannerCapture);
            this.tabScanner.Controls.Add(this.buttonScannerProperties);
            resources.ApplyResources(this.tabScanner, "tabScanner");
            this.tabScanner.Name = "tabScanner";
            this.tabScanner.UseVisualStyleBackColor = true;
            // 
            // label11
            // 
            this.label11.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.label11, "label11");
            this.label11.Name = "label11";
            // 
            // textBoxScannerName
            // 
            resources.ApplyResources(this.textBoxScannerName, "textBoxScannerName");
            this.textBoxScannerName.Name = "textBoxScannerName";
            this.textBoxScannerName.ReadOnly = true;
            this.textBoxScannerName.TabStop = false;
            // 
            // groupBox1
            // 
            this.groupBox1.BackColor = System.Drawing.Color.Transparent;
            this.groupBox1.Controls.Add(this.labelNumFingerprints);
            this.groupBox1.Controls.Add(this.label13);
            this.groupBox1.Controls.Add(this.labelNumSignatures);
            this.groupBox1.Controls.Add(this.label14);
            this.groupBox1.Controls.Add(this.labelNumPortraits);
            this.groupBox1.Controls.Add(this.label8);
            resources.ApplyResources(this.groupBox1, "groupBox1");
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.TabStop = false;
            // 
            // labelNumFingerprints
            // 
            resources.ApplyResources(this.labelNumFingerprints, "labelNumFingerprints");
            this.labelNumFingerprints.Name = "labelNumFingerprints";
            // 
            // label13
            // 
            resources.ApplyResources(this.label13, "label13");
            this.label13.Name = "label13";
            // 
            // labelNumSignatures
            // 
            resources.ApplyResources(this.labelNumSignatures, "labelNumSignatures");
            this.labelNumSignatures.Name = "labelNumSignatures";
            // 
            // label14
            // 
            resources.ApplyResources(this.label14, "label14");
            this.label14.Name = "label14";
            // 
            // labelNumPortraits
            // 
            resources.ApplyResources(this.labelNumPortraits, "labelNumPortraits");
            this.labelNumPortraits.Name = "labelNumPortraits";
            // 
            // label8
            // 
            resources.ApplyResources(this.label8, "label8");
            this.label8.Name = "label8";
            // 
            // buttonScannerDisplay
            // 
            resources.ApplyResources(this.buttonScannerDisplay, "buttonScannerDisplay");
            this.buttonScannerDisplay.Name = "buttonScannerDisplay";
            this.buttonScannerDisplay.Click += new System.EventHandler(this.buttonScannerDisplay_Click);
            // 
            // textBoxScannerImageID
            // 
            resources.ApplyResources(this.textBoxScannerImageID, "textBoxScannerImageID");
            this.textBoxScannerImageID.Name = "textBoxScannerImageID";
            // 
            // label6
            // 
            this.label6.BackColor = System.Drawing.Color.Transparent;
            resources.ApplyResources(this.label6, "label6");
            this.label6.Name = "label6";
            // 
            // buttonScannerCapture
            // 
            resources.ApplyResources(this.buttonScannerCapture, "buttonScannerCapture");
            this.buttonScannerCapture.Name = "buttonScannerCapture";
            this.buttonScannerCapture.Click += new System.EventHandler(this.buttonScannerCapture_Click);
            // 
            // buttonScannerProperties
            // 
            resources.ApplyResources(this.buttonScannerProperties, "buttonScannerProperties");
            this.buttonScannerProperties.Name = "buttonScannerProperties";
            this.buttonScannerProperties.Click += new System.EventHandler(this.buttonScannerProperties_Click);
            // 
            // tbMachineConfigFile
            // 
            this.tbMachineConfigFile.BackColor = System.Drawing.SystemColors.Control;
            this.tbMachineConfigFile.BorderStyle = System.Windows.Forms.BorderStyle.None;
            resources.ApplyResources(this.tbMachineConfigFile, "tbMachineConfigFile");
            this.tbMachineConfigFile.Name = "tbMachineConfigFile";
            this.tbMachineConfigFile.ReadOnly = true;
            this.tbMachineConfigFile.TabStop = false;
            // 
            // labelConfigFile
            // 
            resources.ApplyResources(this.labelConfigFile, "labelConfigFile");
            this.labelConfigFile.Name = "labelConfigFile";
            // 
            // tbDataRootDir
            // 
            this.tbDataRootDir.BackColor = System.Drawing.SystemColors.Control;
            resources.ApplyResources(this.tbDataRootDir, "tbDataRootDir");
            this.tbDataRootDir.Name = "tbDataRootDir";
            this.tbDataRootDir.ReadOnly = true;
            this.tbDataRootDir.TabStop = false;
            // 
            // labelDataRootDirectory
            // 
            resources.ApplyResources(this.labelDataRootDirectory, "labelDataRootDirectory");
            this.labelDataRootDirectory.Name = "labelDataRootDirectory";
            // 
            // buttonAbout
            // 
            resources.ApplyResources(this.buttonAbout, "buttonAbout");
            this.buttonAbout.Name = "buttonAbout";
            this.buttonAbout.Click += new System.EventHandler(this.buttonAbout_Click);
            // 
            // buttonCancel
            // 
            this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            resources.ApplyResources(this.buttonCancel, "buttonCancel");
            this.buttonCancel.Name = "buttonCancel";
            this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
            // 
            // buttonAccept
            // 
            resources.ApplyResources(this.buttonAccept, "buttonAccept");
            this.buttonAccept.Name = "buttonAccept";
            this.buttonAccept.Click += new System.EventHandler(this.buttonAccept_Click);
            // 
            // buttonValidate
            // 
            resources.ApplyResources(this.buttonValidate, "buttonValidate");
            this.buttonValidate.Name = "buttonValidate";
            this.buttonValidate.Click += new System.EventHandler(this.buttonValidate_Click);
            // 
            // comboBoxCaptureOrder
            // 
            this.comboBoxCaptureOrder.Items.AddRange(new object[] {
            resources.GetString("comboBoxCaptureOrder.Items"),
            resources.GetString("comboBoxCaptureOrder.Items1"),
            resources.GetString("comboBoxCaptureOrder.Items2")});
            resources.ApplyResources(this.comboBoxCaptureOrder, "comboBoxCaptureOrder");
            this.comboBoxCaptureOrder.Name = "comboBoxCaptureOrder";
            // 
            // labelCaptureOrder
            // 
            resources.ApplyResources(this.labelCaptureOrder, "labelCaptureOrder");
            this.labelCaptureOrder.Name = "labelCaptureOrder";
            // 
            // buttonSelectDataRootDir
            // 
            resources.ApplyResources(this.buttonSelectDataRootDir, "buttonSelectDataRootDir");
            this.buttonSelectDataRootDir.Name = "buttonSelectDataRootDir";
            this.buttonSelectDataRootDir.Click += new System.EventHandler(this.buttonSelectDataRootDir_Click);
            // 
            // label18
            // 
            resources.ApplyResources(this.label18, "label18");
            this.label18.ForeColor = System.Drawing.Color.Maroon;
            this.label18.Name = "label18";
            // 
            // label19
            // 
            resources.ApplyResources(this.label19, "label19");
            this.label19.ForeColor = System.Drawing.Color.Maroon;
            this.label19.Name = "label19";
            // 
            // buttonOLEImageDB
            // 
            resources.ApplyResources(this.buttonOLEImageDB, "buttonOLEImageDB");
            this.buttonOLEImageDB.Name = "buttonOLEImageDB";
            this.buttonOLEImageDB.Click += new System.EventHandler(this.buttonOLEImageDB_Click);
            // 
            // label24
            // 
            resources.ApplyResources(this.label24, "label24");
            this.label24.Name = "label24";
            // 
            // textBoxImageDBType
            // 
            resources.ApplyResources(this.textBoxImageDBType, "textBoxImageDBType");
            this.textBoxImageDBType.Name = "textBoxImageDBType";
            this.textBoxImageDBType.ReadOnly = true;
            // 
            // checkBoxEnableImageHistory
            // 
            resources.ApplyResources(this.checkBoxEnableImageHistory, "checkBoxEnableImageHistory");
            this.checkBoxEnableImageHistory.Name = "checkBoxEnableImageHistory";
            this.checkBoxEnableImageHistory.UseVisualStyleBackColor = true;
            // 
            // DCSSDK_CaptureMgt
            // 
            this.AcceptButton = this.buttonAccept;
            resources.ApplyResources(this, "$this");
            this.CancelButton = this.buttonCancel;
            this.Controls.Add(this.checkBoxEnableImageHistory);
            this.Controls.Add(this.textBoxImageDBType);
            this.Controls.Add(this.label24);
            this.Controls.Add(this.buttonOLEImageDB);
            this.Controls.Add(this.label18);
            this.Controls.Add(this.label19);
            this.Controls.Add(this.buttonSelectDataRootDir);
            this.Controls.Add(this.labelCaptureOrder);
            this.Controls.Add(this.comboBoxCaptureOrder);
            this.Controls.Add(this.buttonValidate);
            this.Controls.Add(this.buttonAccept);
            this.Controls.Add(this.buttonCancel);
            this.Controls.Add(this.buttonAbout);
            this.Controls.Add(this.tbDataRootDir);
            this.Controls.Add(this.tbMachineConfigFile);
            this.Controls.Add(this.tbInstallDirectory);
            this.Controls.Add(this.tbMachineIndexFile);
            this.Controls.Add(this.labelDataRootDirectory);
            this.Controls.Add(this.labelConfigFile);
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.labelDCSDataPath);
            this.Controls.Add(this.labelDCSSDKPath);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "DCSSDK_CaptureMgt";
            this.ShowInTaskbar = false;
            tab10Print.ResumeLayout(false);
            tab10Print.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDown10PrintInstances)).EndInit();
            this.tabControl1.ResumeLayout(false);
            this.tabPortrait.ResumeLayout(false);
            this.tabPortrait.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownPortraitInstances)).EndInit();
            this.tabSignature.ResumeLayout(false);
            this.tabSignature.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownSignatureInstances)).EndInit();
            this.tabFingerprint.ResumeLayout(false);
            this.tabFingerprint.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownFingerInstances)).EndInit();
            this.tabCerts.ResumeLayout(false);
            this.tabCerts.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownCertsInstance)).EndInit();
            this.tabCustomBiometric.ResumeLayout(false);
            this.tabCustomBiometric.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMinutiaThreshold)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownBIN2Threshold)).EndInit();
            this.tabReaders.ResumeLayout(false);
            this.tabScanner.ResumeLayout(false);
            this.tabScanner.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

		}
		#endregion

		/********************************* only if this is to be a main form *****************
		/// <summary>
		/// The main entry point for the application.
		/// </summary>
		[STAThread]
		static void Main() 
		{
			Application.Run(new DCSSDK_CaptureMgt());
		}
		************************************************************************************/

		#region Private Utilities

		private void AdjustVisibilities()
		{
			bool bVisible;

			// CertsHandling tab
			this.checkCertsCapture.Visible = m_bCerts;
			this.checkCertsDisplay.Visible = m_bCerts;
			this.numericUpDownCertsInstance.Visible = m_bCerts;
			this.textBoxTestCertsID.Visible = m_bCerts;
			this.labelCertsInstance.Visible = m_bCerts;
			this.labelCertsID.Visible = m_bCerts;
			this.buttonTestCertsDisplay.Visible = m_bCerts;
			// if capturing
			bVisible = m_bCerts && m_bCertsCapture;
			this.buttonTestCertsCapture.Visible = bVisible;
			this.label20.Visible = bVisible;
			this.tbScannerDeviceName.Visible = bVisible;
			this.buttonSelectDevice.Visible = bVisible;
			this.buttonCertsFinisherProperties.Visible = bVisible;

			// Portrait tab
			this.checkPortraitCapture.Visible = m_bPortrait;
			this.checkPortraitDisplay.Visible = m_bPortrait;
			this.checkPortraitScanner.Visible = m_bPortrait;
			this.buttonTestPortraitDisplay.Visible = m_bPortrait;
			this.numericUpDownPortraitInstances.Visible = m_bPortrait && DCSDEV.DCSLicensing.IsLicensedOK(LicensedFeatures.Instances, false);
			this.label4.Visible = m_bPortrait;
			this.tbTestPhotoID.Visible = m_bPortrait;
			this.label1.Visible = m_bPortrait;

			// if capturing
			bVisible = m_bPortrait && m_bPortraitCapture;
			this.labelPortraitDevice.Visible = bVisible;
			this.cbPortraitDevice.Visible = bVisible;
			this.buttonPortraitDeviceProperties.Visible = bVisible;
			this.buttonTestPortraitCapture.Visible = bVisible;

			// if capturing live or by scanner
			bVisible = m_bPortrait && (m_bPortraitCapture || m_bPortraitScannerCapture);
			this.buttonPortraitFinisherProperties.Visible = bVisible;

			// Signature tab
			this.checkSignatureCapture.Visible = m_bSignature;
			this.checkSignatureDisplay.Visible = m_bSignature;
			this.checkSignatureScanner.Visible = m_bSignature;
			this.buttonTestSigDisplay.Visible = m_bSignature;
			this.numericUpDownSignatureInstances.Visible = m_bSignature && DCSDEV.DCSLicensing.IsLicensedOK(LicensedFeatures.Instances, false);
			this.label7.Visible = m_bSignature;
			this.tbTestSigID.Visible = m_bSignature;
			this.label2.Visible = m_bSignature;

			// if capturing
			bVisible = m_bSignature && m_bSignatureCapture;
			this.labelSignatureDevice.Visible = bVisible;
			this.cbSignatureDevice.Visible = bVisible;
			this.buttonSigDeviceProperties.Visible = bVisible;
			this.buttonTestSigCapture.Visible = bVisible;

			// if capturing live or by scanner
			bVisible = m_bSignature && (m_bSignatureCapture || m_bSignatureScannerCapture);
			this.buttonSigFinisherProperties.Visible = bVisible;

			// Fingerprint tab
			this.checkFingerprintCapture.Visible = m_bFingerprint;
			this.checkFingerprintDisplay.Visible = m_bFingerprint;
			this.buttonFingerMapping.Visible = m_bFingerMapping;
			this.checkFingerprintScanner.Visible = m_bFingerprint;
			this.buttonTestFingerDisplay.Visible = m_bFingerprint;

			this.checkBoxFingerMapping.Visible = m_bFingerprint;
			this.buttonFingerMapping.Visible = m_bFingerprint;
			this.buttonFingerMapping.Enabled = m_bFingerprint && m_bFingerMapping;
			//xx this.checkBoxMultiFingerDevice.Visible = m_bFingerprint;

			this.numericUpDownFingerInstances.Visible = m_bFingerprint && DCSDEV.DCSLicensing.IsLicensedOK(LicensedFeatures.Instances, false);
			this.label9.Visible = m_bFingerprint;
			this.tbFingerID.Visible = m_bFingerprint;
			this.label3.Visible = m_bFingerprint;

			// if capturing live
			bVisible = m_bFingerprint && m_bFingerprintCapture;
			this.labelFingerprintDevice.Visible = bVisible;
			this.cbFingerprintDevice.Visible = bVisible;
			this.buttonFingerDeviceProperties.Visible = bVisible;
			this.buttonTestFingerCapture.Visible = bVisible;
			//xx this.checkBoxMultiFingerDevice.Visible = bVisible;

			// if capturing live or by scanner
			bVisible = m_bFingerprint && (m_bFingerprintCapture || m_bFingerprintScannerCapture);
			this.buttonFingerFinisherProperties.Visible = bVisible;
			this.checkGenFingerBIN.Visible = bVisible;
            this.checkDontSaveFingerImage.Visible = this.checkBoxQCVerify.Enabled = bVisible && m_bGenerateFingerBiometric;

			// if 10 Print Tab
			bVisible = m_bFingerprint;
			this.check10Print.Enabled = bVisible;

			bVisible = m_bFingerprint && m_b10Print;
			this.cb10PrintDevice.Visible = bVisible;
			this.numericUpDown10PrintInstances.Visible = bVisible && DCSDEV.DCSLicensing.IsLicensedOK(LicensedFeatures.Instances, false);
			this.button10PrintFinisherProperties.Visible = bVisible;
			this.button10PrintDeviceProperties.Visible = bVisible;
			this.buttonTest10PrintCapture.Visible = bVisible;
			this.buttonTest10PrintDisplay.Visible = bVisible;
			this.textBox10PrintID.Visible = bVisible;
			this.label10PrintTestID.Visible = bVisible;
			this.label10PrintInstances.Visible = bVisible;
			this.label10PrintDevice.Visible = bVisible;

			// scanner tab
			bool bSomethingToScan = false;
			if ((m_bPortrait && m_bPortraitScannerCapture)
				|| (m_bSignature && m_bSignatureScannerCapture)
				|| (m_bFingerprint && m_bFingerprintScannerCapture))
				bSomethingToScan = true;
			this.buttonScannerProperties.Enabled = bSomethingToScan;
			this.buttonScannerCapture.Enabled = bSomethingToScan;
			this.buttonScannerDisplay.Enabled = bSomethingToScan;

			// capture order choices
			int ctr = 0;
			if (m_bPortrait) ctr++;
			if (m_bSignature) ctr++;
			if (m_bFingerprint) ctr++;
			this.comboBoxCaptureOrder.Visible = this.labelCaptureOrder.Visible = (ctr > 1);

            /*syh*/
			/* commented out because Aladdin protected version produces a run time error
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.GenerateFingerBiometrics))
				((TabPage)this.tabControl1.Controls[3]).Enabled = false;
			 */
		}

		private bool DoScannerCapture(string strImageName, bool bPortrait, bool bSignature, bool bFingerprint)
		{
			// check licensing
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.ScannerMgt, true)) return false;

			DCSDEV.DCSScanner.ScannerMain scanner = new DCSDEV.DCSScanner.ScannerMain(m_scannerIF);
			scanner.PortrtaitPath = bPortrait ? m_strPortraitPath : null;
			scanner.SignaturePath = bSignature ? m_strSignaturePath : null;
			scanner.FingerprintPath = bFingerprint ? m_strFingerprintPath : null;
			scanner.ImageName = strImageName;
			scanner.DoAcquire();

			m_bCaptureStatus = (scanner.CaptureStatus == 2);
			return true;
		}

		private bool DoCertsScan(string ImageID, string title, int iSubClass)
		{
			if (!m_scannerIF.m_bScannerIsInitialized)
			{
				m_scannerIF.InitializeScanner();
			}
			if (!m_scannerIF.m_bScannerIsInitialized) return false;
		RESCAN:
            Image bitmap = m_scannerIF.ScanRect(new Rectangle(0, 0, 850, 1100), 300);
			if (bitmap == null) return false;

			DCSDEV.DCSFinisher.FinisherMain dlgFinish = new DCSDEV.DCSFinisher.FinisherMain(DCSDatabaseIF.ImageClass.Certificate);
			dlgFinish.FinisherImage = bitmap;
			dlgFinish.ProcessFinisherImage();

			dlgFinish.ShowDialog(this);
			// the original image from camera needs to be disposed and the file deleted
			bitmap.Dispose();
			bitmap = null;

			if (dlgFinish.IsCanceled)
			{
				if (DCSMsg.ShowOKC("OK to scan again?") == DialogResult.OK) goto RESCAN;
				return false;
			}
			else
			{
				// if ok the finisher's working image will become the current image
				bool bRet;

				// Finisher process puts image into a temporary file and SetStoredImage puts it into the Image data storage according to FILES or OLE
				string strTempFile = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDatabaseIF.GetNameOfImage("_Temp", DCSDatabaseIF.ImageClass.Certificate));
				if (System.IO.File.Exists(strTempFile)) System.IO.File.Delete(strTempFile);

				bRet = dlgFinish.SaveFinishedImage(strTempFile);
				if (bRet)
				{
					if (strTempFile != null)
					{
						bRet = DCSDatabaseIF.SetStoredImage(strTempFile, ImageID, DCSDatabaseIF.ImageClass.Certificate, iSubClass, false);
						System.IO.File.Delete(strTempFile);	// cleanup
					}
					else bRet = false;
				}
				return bRet;
			}
		}

        private int DoPortraitCaptureUtil(string strTitle, string strTempFile)
        {
            int iStatus = 0;
            switch (m_strPortraitDevice)
            {
                case "CanonCamera":
                    if (m_dlgCanonPhoto == null) m_dlgCanonPhoto = new DCSDEV.CanonCamera.CanonCameraMain();
                    m_dlgCanonPhoto.PhotoTitle = strTitle;
                    m_dlgCanonPhoto.PhotoFileName = strTempFile;
                    try
                    {
                        m_dlgCanonPhoto.TopMost = true;
                        m_dlgCanonPhoto.ShowDialog();
                    }
                    catch (Exception ex)
                    {
                        DCSMsg.Show("ERROR: running portrait capture.", ex);
                        m_dlgCanonPhoto.Dispose();
                        m_dlgCanonPhoto = null;
                        return 0;
                    }
                    iStatus = m_dlgCanonPhoto.CaptureStatus;
                    break;
                case "DCS8000":
                    if (m_dlgDCS8000Photo == null) m_dlgDCS8000Photo = new DCSDEV.DCS8000.DCS8000Main();
                    m_dlgDCS8000Photo.PhotoTitle = strTitle;
                    m_dlgDCS8000Photo.PhotoFileName = strTempFile;
                    try
                    {
                        m_dlgDCS8000Photo.TopMost = true;
                        m_dlgDCS8000Photo.ShowDialog();
                    }
                    catch (Exception ex)
                    {
                        DCSMsg.Show("ERROR: running portrait capture.", ex);
                        m_dlgDCS8000Photo.Dispose();
                        m_dlgDCS8000Photo = null;
                        return 0;
                    }
                    iStatus = m_dlgDCS8000Photo.CaptureStatus;
                    break;
                case "FromFile":
                    if (m_dlgFromFilePhoto == null) m_dlgFromFilePhoto = new DCSDEV.FromFile.FromFileMain("PORTRAIT");
                    m_dlgFromFilePhoto.PhotoTitle = strTitle;
                    m_dlgFromFilePhoto.PhotoFileName = strTempFile;
                    try
                    {
                        //m_dlgFromFilePhoto.TopMost = true;
                        m_dlgFromFilePhoto.ShowDialog();
                    }
                    catch (Exception ex)
                    {
                        DCSMsg.Show("ERROR: running portrait capture.", ex);
                        //m_dlgFromFilePhoto.Dispose();
                        m_dlgFromFilePhoto = null;
                        return 0;
                    }
                    iStatus = m_dlgFromFilePhoto.CaptureStatus;
                    break;
                case "Twain":
                    try
                    {
                        if (m_dlgTwainPhoto == null)
                        {
                            m_dlgTwainPhoto = new DCSDEV.DCSTwain.TwainMain("PORTRAIT");
                        }
                        else
                        {
                            // twain ends its session when it exits - not sure this is necessary but its been there for a while and maybe for a reason.
                            m_dlgTwainPhoto.InitializeTwain();
                        }
                        m_dlgTwainPhoto.PhotoTitle = strTitle;
                        m_dlgTwainPhoto.PhotoFileName = strTempFile;

                        m_dlgTwainPhoto.TopMost = true;
                        m_dlgTwainPhoto.ShowDialog();
                    }
                    catch (Exception ex)
                    {
                        DCSMsg.Show("Twain portrait capture needs resetting.\\nn", ex);
                        m_dlgTwainPhoto.Dispose();
                        m_dlgTwainPhoto = null;
                        return 0;
                    }
                    iStatus = m_dlgTwainPhoto.CaptureStatus;
                    break;
            }
            if (iStatus == 2 && !System.IO.File.Exists(strTempFile)) iStatus = 0;

            return iStatus;
        }

        // scanner capture bypasses this interface
		// capture all instances if instance == -1
		// bPermitArchive is true when taking for the first time and an archive copy is appropriate. Is false if it is a redo case.
		private bool DoPortraitCapture(int iSubClass, bool bPermitArchive)
		{
            // check licensing
            if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.PortraitCapture, true)) return false;
            if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.SystemMgt)) return false;
            // check device setup
            if (!ValidatePortraitDevice(false)) return false;

			bool bRet = false;
			bool bOneGood = false;
			for (int i = 0; i < (int)this.numericUpDownPortraitInstances.Value; i++)
			{
				if (iSubClass != -1 && i != iSubClass) continue;
                
                string strTitle = "Port" + ((i == 0) ? "" : i.ToString()) + ": " + m_strPhotoTitle;
                string strTempFile = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDatabaseIF.GetNameOfImage("_Temp", DCSDatabaseIF.ImageClass.Portrait));
                if (System.IO.File.Exists(strTempFile)) System.IO.File.Delete(strTempFile);

                switch (DoPortraitCaptureUtil(strTitle, strTempFile))	// 0= software did not finish; 1=user canceled; 2=OK
                {
                    case 0:
                    case 1:
                    default:
                        if ((System.Windows.Forms.Control.ModifierKeys & Keys.Shift) == Keys.Shift) break;
                        break;
                    case 2:
                        bRet = DCSDatabaseIF.SetStoredImage(strTempFile, m_strPhotoName, DCSDatabaseIF.ImageClass.Portrait, i, bPermitArchive);
                        System.IO.File.Delete(strTempFile);
                        bOneGood = true;
                        break;
                }
			}
			return bOneGood;
		}

        private int DoSignatureCaptureUtil(string strTitle, string strTempFile)
        {
            int iStatus = 0;
            if (System.IO.File.Exists(strTempFile)) System.IO.File.Delete(strTempFile);
    
            switch (m_strSignatureDevice)
            {
                case "Topaz":
                    if (m_dlgTopaz == null) m_dlgTopaz = new DCSDEV.DCSTopaz.TopazMain("SIGNATURE");
                    m_dlgTopaz.SignatureTitle = strTitle;
                    m_dlgTopaz.SignatureFileName = strTempFile;
                    try
                    {
                        m_dlgTopaz.TopMost = true;
                        m_dlgTopaz.ShowDialog();
                    }
                    catch (Exception ex)
                    {
                        DCSMsg.Show("ERROR: running signature capture.", ex);
                        m_dlgTopaz.Dispose();
                        m_dlgTopaz = null;
                        return 0;
                    }
                    iStatus = m_dlgTopaz.CaptureStatus;
                    m_dlgTopaz.Dispose();   /*syh 2.07.02*/
                    m_dlgTopaz = null;      /*syh 2.07.02*/
                    break;
                case "FromFile":
                    if (m_dlgFromFileSig == null) m_dlgFromFileSig = new DCSDEV.FromFile.FromFileMain("SIGNATURE");
                    m_dlgFromFileSig.PhotoTitle = strTitle;
                    m_dlgFromFileSig.PhotoFileName = strTempFile;
                    try
                    {
                        //m_dlgFromFileSig.TopMost = true;
                        m_dlgFromFileSig.ShowDialog();
                    }
                    catch (Exception ex)
                    {
                        DCSMsg.Show("ERROR: running signature capture.", ex);
                        //m_dlgFromFileSig.Dispose();
                        m_dlgFromFileSig = null;
                        return 0;
                    }
                    iStatus = m_dlgFromFileSig.CaptureStatus;
                    break;
                case "Twain":
                    try
                    {
                        if (m_dlgTwainSig == null)
                        {
                            m_dlgTwainSig = new DCSDEV.DCSTwain.TwainMain("SIGNATURE");
                        }
                        else
                        {
                            // twain ends its session when it exits - not sure this is necessary but its been there for a while and maybe for a reason.
                            m_dlgTwainSig.InitializeTwain();
                        }
                        m_dlgTwainSig.PhotoTitle = strTitle;
                        m_dlgTwainSig.PhotoFileName = strTempFile;

                        m_dlgTwainSig.TopMost = true;
                        m_dlgTwainSig.ShowDialog();
                    }
                    catch (Exception ex)
                    {
                        DCSMsg.Show("Twain signature capture needs resetting.\n\n", ex);
                        m_dlgTwainSig.Dispose();
                        m_dlgTwainSig = null;
                        return 0;
                    }
                    iStatus = m_dlgTwainSig.CaptureStatus;
                    break;
            }
            if (iStatus == 2 && !System.IO.File.Exists(strTempFile)) iStatus = 0;
            return iStatus;
        }

		// scanner capture bypasses this interface
		// bPermitArchive is true when taking for the first time and an archive copy is appropriate. Is false if it is a redo case.
		private bool DoSignatureCapture(int iSubClass, bool bPermitArchive)
		{
			// capture Signature
            // check licensing
            if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.SignatureCapture, true)) return false;
            if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.SystemMgt)) return false;
            // check device setup
            if (!ValidateSignatureDevice(false)) return false;

			bool bRet = false;
			bool bOneGood = false;
			for (int i = 0; i < (int)this.numericUpDownSignatureInstances.Value; i++)
			{
				if (iSubClass != -1 && i != iSubClass) continue;

                // Finisher process puts image into a temporary file and SetStoredImage puts it into the Image data storage according to FILES or OLE
                // set name and path to get a temp file
                string strTempFile = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDatabaseIF.GetNameOfImage("_Temp", DCSDatabaseIF.ImageClass.Signature));
                string strTitle = "Sig" + ((i == 0) ? "" : i.ToString()) + ": " + m_strSigTitle;

                switch (DoSignatureCaptureUtil(strTitle, strTempFile))	// 0= software did not finish; 1=user canceled; 2=OK
                {
                    case 0:
                    case 1:
                    default:
                        if ((System.Windows.Forms.Control.ModifierKeys & Keys.Shift) == Keys.Shift) break;
                        break;
                    case 2:
                        bRet = DCSDatabaseIF.SetStoredImage(strTempFile, m_strSigName, DCSDatabaseIF.ImageClass.Signature, i, bPermitArchive);
                        System.IO.File.Delete(strTempFile);
                        bOneGood = true;
                        break;
                }
			}
			return bOneGood;
		}

        private int DoFingerCaptureUtil(string strTitle, string strTempFile)
        {
            int iStatus = 0;
            if (System.IO.File.Exists(strTempFile)) System.IO.File.Delete(strTempFile);
            switch (m_strFingerprintDevice)
            {
                case "CrossMatch":
                    if (m_dlgCrossMatch == null) m_dlgCrossMatch = new DCSDEV.CrossMatch.CrossMatchMain();
                    m_dlgCrossMatch.FingerTitle = strTitle;
                    m_dlgCrossMatch.FingerFileName = strTempFile;
                    try
                    {
                        m_dlgCrossMatch.TopMost = true;
                        m_dlgCrossMatch.ShowDialog();
                    }
                    catch (Exception ex)
                    {
                        DCSMsg.Show("ERROR: running finger capture.", ex);
                        m_dlgCrossMatch.Dispose();
                        m_dlgCrossMatch = null;
                        return 0;
                    }
                    iStatus = m_dlgCrossMatch.CaptureStatus;
                    break;
                case "Fdu04":
                    if (m_dlgFDU04 == null) m_dlgFDU04 = new DCSDEV.FDU04.FDU04Main();
                    m_dlgFDU04.FingerTitle = strTitle;
                    m_dlgFDU04.FingerFileName = strTempFile;
                    try
                    {
                        m_dlgFDU04.TopMost = true;
                        m_dlgFDU04.ShowDialog();
                    }
                    catch (Exception ex)
                    {
                        DCSMsg.Show("ERROR: running finger capture.", ex);
                        m_dlgFDU04.Dispose();
                        m_dlgFDU04 = null;
                        return 0;
                    }
                    iStatus = m_dlgFDU04.CaptureStatus;
                    break;
                case "FromFile":
                    if (m_dlgFromFileFinger == null) m_dlgFromFileFinger = new DCSDEV.FromFile.FromFileMain("FINGER");
                    m_dlgFromFileFinger.PhotoTitle = strTitle;
                    m_dlgFromFileFinger.PhotoFileName = strTempFile;
                    try
                    {
                        //m_dlgFromFileFinger.TopMost = true;
                        m_dlgFromFileFinger.ShowDialog();
                    }
                    catch (Exception ex)
                    {
                        DCSMsg.Show("ERROR: running finger capture.", ex);
                        //m_dlgFromFileFinger.Dispose();
                        m_dlgFromFileFinger = null;
                        return 0;
                    }
                    iStatus = m_dlgFromFileFinger.CaptureStatus;
                    break;
                case "Twain":
                    try
                    {
                        if (m_dlgTwainFinger == null) 
                        {
                            m_dlgTwainFinger = new DCSDEV.DCSTwain.TwainMain("FINGER");
                        }
                        else
                        {
                            m_dlgTwainFinger.InitializeTwain();
                        }
                        m_dlgTwainFinger.PhotoTitle = strTitle;
                        m_dlgTwainFinger.PhotoFileName = strTempFile;

                        m_dlgTwainFinger.TopMost = true;
                        m_dlgTwainFinger.ShowDialog();
                    }
                    catch (Exception ex)
                    {
                        DCSMsg.Show("Twain finger capture needs resetting.\n\n", ex);
                        m_dlgTwainFinger.Dispose();
                        m_dlgTwainFinger = null;
                        return 0;
                    }
                    iStatus = m_dlgTwainFinger.CaptureStatus;
                    break;
            }
            if (iStatus == 2 && !System.IO.File.Exists(strTempFile)) iStatus = 0;
            return iStatus;
        }

        // scanner capture bypasses this interface
		// bPermitArchive is true when taking for the first time and an archive copy is appropriate. Is false if it is a redo case.
        // syh note creating new fingers images does not remove the old ones under finger mapping so 
        // old unwanted images may be retained if user cancels an image and moves to an alternate.
        private bool DoFingerCapture(int iSubClass, bool bPermitArchive)
		{
			bool bRet;
			string strError;
			int numNeededForBIN = 0;
            string strTempFile = null;
            string strTempFile2 = null; // finger bio type like LISKA require two images be captures (see m_iSelectedFingerBioType)

            // check licensing
            if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.FingerprintCapture, true)) return false;
            if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.SystemMgt)) return false;

            // check m_strFingerprintDevice
            if (!ValidateFingerDevice(false)) return false;

			// turn off biometric generation if not licensed
			if (m_bGenerateFingerBiometric)
			{
				if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.GenerateFingerBiometrics, true))
					m_bGenerateFingerBiometric = false;
				else
				{
					numNeededForBIN = 1;
#if LISKA_BIN
					if (this.m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_LISKA_BIN2 || this.m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_LISKA_AND_AIS)
						numNeededForBIN = 2;
#endif
				}
			}

			int iMapped;
			bool bOneGood = false;
			for (int iMapIndex = 0, i = 0; i < (int)this.numericUpDownFingerInstances.Value; i++)
			{
				// compute the mapped subClass number.
				if (iSubClass != -1 && i != iSubClass) continue;
				if (iSubClass == -1 && this.m_bFingerMapping)
				{
					if (iMapIndex >= this.m_strFingerMap.Length) break;
					iMapped = this.m_strFingerMap[iMapIndex++] - '0';
				}
				else iMapped = i;

                // Finisher process puts image into a temporary file and SetStoredImage puts it into the Image data storage according to FILES or OLE
                // set name and path to get a temp file
                strTempFile = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDatabaseIF.GetNameOfImage("_Temp", DCSDatabaseIF.ImageClass.Fingerprint));
                string strTitlePrefix;
				if (this.m_bFingerMapping)
					strTitlePrefix = (string)m_arrayFingerMapNames[iMapped];
				else
					strTitlePrefix = "Fing" + iMapped.ToString();
                string strTitle = strTitlePrefix + ": " + m_strFingerTitle;

				//////////////////////////////////////////////////////////////////////////////
				// Capture First Finger                                                     //
				//////////////////////////////////////////////////////////////////////////////
                switch (DoFingerCaptureUtil(strTitle, strTempFile))	// 0= software did not finish; 1=user canceled; 2=OK
                {
                    case 0:
                    case 1:
                    default:
                        if ((System.Windows.Forms.Control.ModifierKeys & Keys.Shift) == Keys.Shift) break;
					    if (this.m_bFingerMapping) i--;
					    continue;
                    case 2:
                        break;
                }
				//////////////////////////////////////////////////////////////////////////////
				// Capture Second Image of Same Finger if needed                            //
				// numNeededForBIN is 1 or 2.  Liska BIN needs 2 images of same finger      //
				//////////////////////////////////////////////////////////////////////////////
				if (numNeededForBIN == 2)
				{
                    // Finisher process puts image into a temporary file and SetStoredImage puts it into the Image data storage according to FILES or OLE
                    // FingerPath is already set
                    strTitle = strTitlePrefix + ": Lift and replace for a second image";
                    strTempFile2 = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDatabaseIF.GetNameOfImage("_Temp2", DCSDatabaseIF.ImageClass.Fingerprint));

                    switch (DoFingerCaptureUtil(strTitle, strTempFile2))	// 0= software did not finish; 1=user canceled; 2=OK
                    {
                        case 0:
                        case 1:
                        default:
                            if ((System.Windows.Forms.Control.ModifierKeys & Keys.Shift) == Keys.Shift) break;
                            if (this.m_bFingerMapping) i--;
                            continue;
                        case 2:
                            break;
                    }
				}

				///////////////////////////////////////////
				// Generate finger BINs                  //
				///////////////////////////////////////////
				if (m_bGenerateFingerBiometric)
				{
					// Delete finger biometric BINs for this subClass
					DCSDatabaseIF.DeleteBiometrics(m_strFingerName, DCSDEV.DCSDatabaseIF.ImageClass.Fingerprint, iMapped);

					/////////////////////////////////////////////////////////////////////
					// Generate new finger BINs;                                       //
					// Use temp storage only.                                          //
					// If Innovatrics, try creating the Innovatrics fingerprint object //
					/////////////////////////////////////////////////////////////////////
					string strLiskaBIN;	// null=error; else OK
					string strMinutiaBIN;
					if (m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_INNOVATRICS)
					{
						bRet = DCSDEV.DCSInnovatricsIF.DCSInnovatricsIF.CheckFinger(strTempFile);
						strLiskaBIN = null;
						strMinutiaBIN = null;
						strError = null;
					}
					else
					{
						bRet = this.GenFingerBiometricsFromFiles(m_strFingerName, strTempFile, strTempFile2, iMapped, out strLiskaBIN, out strMinutiaBIN, out strError);
					}
					if (!bRet)
					{
						if (DCSMsg.ShowYN(String.Format("Error enrolling {0} finger.\n{1}\n\nOK to try again?", strTitlePrefix, strError)) == DialogResult.Yes)
						{
							// to try again do not increment instance 
							if (this.m_bFingerMapping) iMapIndex--;
							i--;
						}
						else
						{
							if (this.m_bFingerMapping) i--;
						}
						continue;
					}
					string str = String.Format("Successful enrollment: {0} finger - {1} - {2} \n\n{3}",
						strTitlePrefix,
						strLiskaBIN == null ? "" : "Liska BIN",
						strMinutiaBIN == null ? "" : "Alt BIN",
						m_bQCVerify ? "Continuing with Quality Control verification." : "");
					DCSMsg.Show(str, MessageBoxIcon.Information);

					///////////////////////////////////////////////////////////////
					// Capture an extra finger image and perform QC verification //
					///////////////////////////////////////////////////////////////
					if (m_bQCVerify)
					{
						// capture image without entering it into the image database
						string strQCImagePath = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDatabaseIF.GetNameOfImage("_VERIFY", DCSDatabaseIF.ImageClass.Fingerprint));
						strTitle = strTitlePrefix + ": Replace for verify";
						while (true)
						{
                            int iStatus = DoFingerCaptureUtil(strTitle, strQCImagePath);
                            // 0= software did not finish; 1=user canceled; 2=OK

                            if (iStatus == 2) break;
							if (DCSMsg.ShowYN(String.Format("Verify image canceled.\n\nDo you want to capture {0} finger again?", strTitlePrefix)) == DialogResult.Yes) continue;
							else break;
						}
						if (!bRet)
						{
							DCSMsg.Show(strTitlePrefix + ": Verify canceled.\n\nTry enrolling again.");
							// to try again do not increment instance 
							if (this.m_bFingerMapping) iMapIndex--;
							i--;
							continue;
						}

						int iScoreL = -1;
						int iScoreA = -1;
						bool bOKA = false, bOKL = false, bOKI = false;
						if (strLiskaBIN != null)
						{
							bOKL = DoMatchImageVsBIN(strQCImagePath, strLiskaBIN, iMapped, out iScoreL);
						}
						if (strMinutiaBIN != null)
						{
							bOKA = DoMatchImageVsBIN(strQCImagePath, strMinutiaBIN, iMapped, out iScoreA);
						}
						if (m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_INNOVATRICS)
						{
							string strInnovatricsError = null;
							bRet = DCSDEV.DCSInnovatricsIF.DCSInnovatricsIF.Init(m_strDataRootDir);	// init if not already done
							if (bRet)
							{
                                bRet = DCSDEV.DCSInnovatricsIF.DCSInnovatricsIF.MatchFinger(strTempFile, strQCImagePath);
								if (!bRet) strInnovatricsError = "ERROR: DCS Innovatrics.MatchFinger";
							}
							else strInnovatricsError = "ERROR: DCS Innovatrics.Init";
							if (!bRet) DCSMsg.Show(strInnovatricsError);
							bOKI = bRet;
						}

						// analyze results
						if ((strLiskaBIN != null && !bOKL) && (strMinutiaBIN != null && bOKA))
						{
							// both enrolled, only Alt verified
							DCSMsg.Show(String.Format("{0} finger verified OK\n\nLiska BIN (score={1}; Alt BIN passed (score={2}).", strTitlePrefix, iScoreL, iScoreA));
							if (m_bLogBinErrors) LogMatchErrorData(iMapped, m_strFingerName, strQCImagePath);
						}
						else if ((strLiskaBIN != null && bOKL) && (strMinutiaBIN != null && !bOKA))
						{
							// both enrolled, only liska verified
							DCSMsg.Show(String.Format("{0} finger verified OK\n\n Liska BIN passed (score={1}; Alt BIN (score={2}).", strTitlePrefix, iScoreL, iScoreA));
						}
						else if ((strLiskaBIN != null && bOKL) && (strMinutiaBIN != null && bOKA))
						{
							// both enrolled, both verified
							DCSMsg.Show(String.Format("{0} finger verified OK\n\n Liska BIN passed (score={1}; Alt BIN passed (score={2}).", strTitlePrefix, iScoreL, iScoreA));
						}
						else if (strLiskaBIN != null && bOKL)
						{
							// only liska enrolled and verified
							DCSMsg.Show(String.Format("{0} finger verified OK\n\n Liska BIN passed (score={1}.", strTitlePrefix, iScoreL));
						}
						else if (strMinutiaBIN != null && bOKA)
						{
							// only alt enrolled and verified
							DCSMsg.Show(String.Format("{0} finger verified OK\n\n Alt BIN passed (score={1}).", strTitlePrefix, iScoreA));
						}
						else if (bOKI)
						{
							// finger enrolled and verified by Innovatrics
							DCSMsg.Show(String.Format("{0} finger verified OK.", strTitlePrefix));
						}
						else
						{
							// neither verified
							DCSMsg.Show(String.Format("{0} finger verify failed.\n\nTry enrolling again.", strTitlePrefix), MessageBoxIcon.Information);
							// to try again do not increment instance 
							if (this.m_bFingerMapping) iMapIndex--;
							i--;
						}
						System.IO.File.Delete(strQCImagePath);
						if (!bOKA && !bOKL && !bOKI) continue;
					}
					/////////////////////////////////////////////////////////
					// Put BINS into their database.                       //
					// Innovatrics minutia is stored in Innovatrics db     //
					// Liska and AIS minutia are stored inimage database   //
					/////////////////////////////////////////////////////////
#if LISKA_BIN
					if (strLiskaBIN != null)
						// put Liska BIN into the image database
						DCSDatabaseIF.SetStoredBIN(strLiskaBIN, m_strFingerName, DCSDatabaseIF.ImageClass.BINL, iMapped);
#endif
                    if (strMinutiaBIN != null)
					{
						if (strMinutiaBIN.Substring(0,2) == "AG")
						{
							// put Agora minutia BIN into the image database
							DCSDatabaseIF.SetStoredBIN(strMinutiaBIN, m_strFingerName, DCSDatabaseIF.ImageClass.BING, iMapped);
						}
						else
						{
							// put AIS minutia BIN into the image database
							DCSDatabaseIF.SetStoredBIN(strMinutiaBIN, m_strFingerName, DCSDatabaseIF.ImageClass.BINA, iMapped);
						}
					}
					if (m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_INNOVATRICS)
					{
						// put finger minutia in Innovatrics database
						string strInnovatricsError = null;
						bRet = DCSDEV.DCSInnovatricsIF.DCSInnovatricsIF.Init(m_strDataRootDir);	// init if not already done
						if (bRet)
						{
                            bRet = DCSDEV.DCSInnovatricsIF.DCSInnovatricsIF.AddFinger(m_strFingerName, iMapped, strTempFile);
							if (!bRet) strInnovatricsError = "ERROR: DCS Innovatrics.AddFinger";
						}
						else strInnovatricsError = "ERROR: DCS Innovatrics.Init";
						if (!bRet) DCSMsg.Show(strInnovatricsError);
					}
				}
				//////////////////////////////////////////////
				// put finger images into the image storage //
				// - and remove temp files                  //
				//////////////////////////////////////////////
                if (strTempFile != null)
				{
                    bRet = DCSDatabaseIF.SetStoredImage(strTempFile, m_strFingerName, DCSDatabaseIF.ImageClass.Fingerprint, iMapped, bPermitArchive);
                    System.IO.File.Delete(strTempFile);
				}
				if (strTempFile2 != null)
				{
                    bRet = DCSDatabaseIF.SetStoredImage(strTempFile2, m_strFingerName + "_BIN2", DCSDatabaseIF.ImageClass.Fingerprint, iMapped, bPermitArchive);
                    System.IO.File.Delete(strTempFile2);
				}
				bOneGood = true;
			}
			return bOneGood;
		}

		private bool Do10PrintCapture(bool bPermitArchive)
		{
			DCSDEV.DCSMsg.Show(String.Format("Implement Do10PrintCapture() m_strFingerTitle={0}, m_strFingerName={1}", m_strFingerTitle, m_strFingerName));
			return false;
		}

		private bool DoMatchImageVsBIN(string strTestImagePath, string strBINTest, int iSubClass, out int iScore)
		{
			int iThreshold;
#if LISKA_BIN
			if (strBINTest.StartsWith("LS") || strBINTest.StartsWith("ls"))	// dont know why scanned pdf417 can come back lower case
			{
				iThreshold = m_iThresholdBIN2;
				iScore = BIN2_Match(strTestImagePath, strBINTest.Substring(6)) * 10; // use BIN with stripped off BIN header
				// iScore = Liska.Biometrics.Bin.Match(strTestImagePath, strBINTest.Substring(6)); // use BIN with stripped off BIN header
			}
			else
#endif
            if (strBINTest.StartsWith("AS") || strBINTest.StartsWith("as"))	// dont know why scanned pdf417 can come back lower case
			{
				// generate features for test finger and compare features
				iThreshold = m_iThresholdMinutia;
				string strBiometricFile_Test = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDatabaseIF.GetNameOfImage("_VERIFY1", DCSDatabaseIF.ImageClass.BINA));
				DCSExtractFeatures(strTestImagePath, strBiometricFile_Test, iSubClass);

				// the BIN is written to temp file on disk for the IBM AIS interface
				// syh note - AS DCSVerifyFeatures only works from disk. this should be changed
				string strBiometricFile_DB = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDatabaseIF.GetNameOfImage("_VERIFY2", DCSDatabaseIF.ImageClass.BINA));
				System.IO.StreamWriter stream = new System.IO.StreamWriter(strBiometricFile_DB);
				stream.Write(strBINTest);
				stream.Close();
				iScore = DCSVerifyFeatures(strBiometricFile_DB, strBiometricFile_Test);
				System.IO.File.Delete(strBiometricFile_Test);
				System.IO.File.Delete(strBiometricFile_DB);
			}
			else if (strBINTest.StartsWith("AG") || strBINTest.StartsWith("ag"))	// dont know why scanned pdf417 can come back lower case
			{

                // return -1, 0, or 1: if error, no match, or match
                int iRet = DCSDEV.AgoraIF.AgoraCompareFingerToBIN(strTestImagePath, iSubClass, strBINTest, out iScore);
                return (iRet == 1);
			}
			else
			{
				iScore = 0;
				return false;
			}
			if (iScore >= iThreshold) return true;
			else return false;
		}

		private void DoPortraitDeviceProperties()
		{
			if (m_bPortraitScannerCapture) return;

            if (!ValidatePortraitDevice(false)) return;

            switch (m_strPortraitDevice)
            {
                case "CanonCamera":
                    if (m_propdlgCanonPhoto == null) m_propdlgCanonPhoto = new DCSDEV.CanonCamera.CanonCameraProperties();
                    m_propdlgCanonPhoto.ShowDialog();
                    break;
                case "DCS8000":
                    if (m_propdlgDCS8000Photo == null) m_propdlgDCS8000Photo = new DCSDEV.DCS8000.DCS8000Properties();
                    m_propdlgDCS8000Photo.ShowDialog();
                    break;
                case "FromFile":
                    if (m_propdlgFromFilePhoto == null) m_propdlgFromFilePhoto = new DCSDEV.FromFile.FromFileProperties(DCSDatabaseIF.ImageClass.Portrait);
                    m_propdlgFromFilePhoto.ShowDialog();
                    break;
                case "Twain":
                    if (m_propdlgTwainPhoto == null) m_propdlgTwainPhoto = new DCSDEV.DCSTwain.TwainProperties("PORTRAIT");
                    m_propdlgTwainPhoto.ShowDialog();
                    break;
            }
			return;
		}

		private void DoSignatureDeviceProperties()
		{
			if (m_bSignatureScannerCapture) return;

            if (!ValidateSignatureDevice(false)) return;

            switch (m_strSignatureDevice)
            {
                case "Topaz":
                    if (m_propdlgTopaz == null) m_propdlgTopaz = new DCSDEV.DCSTopaz.TopazProperties("SIGNATURE");
                    m_propdlgTopaz.ShowDialog();
                    break;
                case "FromFile":
                    if (m_propdlgFromFileSig == null) m_propdlgFromFileSig = new DCSDEV.FromFile.FromFileProperties(DCSDatabaseIF.ImageClass.Signature);
                    m_propdlgFromFileSig.ShowDialog();
                    break;
                case "Twain":
                    if (m_propdlgTwainSig == null) m_propdlgTwainSig = new DCSDEV.DCSTwain.TwainProperties("SIGNATURE");
                    m_propdlgTwainSig.ShowDialog();
                    break;
            }
            return;
        }

		private void DoFingerprintDeviceProperties()
		{
			if (m_bFingerprintScannerCapture) return;

            if (!ValidateFingerDevice(false)) return;

            switch (m_strFingerprintDevice)
            {
                case "CrossMatch":
                    if (m_propdlgCrossMatch == null) m_propdlgCrossMatch = new DCSDEV.CrossMatch.CrossMatchProperties();
                    m_propdlgCrossMatch.ShowDialog();
                    break;
                case "Fdu04":
                    if (m_propdlgFDU04 == null) m_propdlgFDU04 = new DCSDEV.FDU04.FDU04Properties();
                    m_propdlgFDU04.ShowDialog();
                    break;
                case "FromFile":
                    if (m_propdlgFromFileFinger == null) m_propdlgFromFileFinger = new DCSDEV.FromFile.FromFileProperties(DCSDatabaseIF.ImageClass.Fingerprint);
                    m_propdlgFromFileFinger.ShowDialog();
                    break;
                case "Twain":
                    if (m_propdlgTwainFinger == null) m_propdlgTwainFinger = new DCSDEV.DCSTwain.TwainProperties("FINGER");
                    m_propdlgTwainFinger.ShowDialog();
                    break;
            }
            return;
		}

		/// <summary>
		/// Delete everything for the given image id, class, & subclass.  Also delete all history. 
		/// BINs are not automatically deleted when fingerprint images are deleted. 
		/// </summary>
		/// <param name="strImageID"></param>
		/// <param name="imageClass"></param>
		/// <param name="iSubClass">if subclass is -1 delete all subclasses</param>
		private void DoDeleteClass(string strImageID, DCSDatabaseIF.ImageClass imageClass, int iSubClass)
		{
			this.CloseDisplays();
			DCSDatabaseIF.DeleteStoredImages(strImageID, imageClass, iSubClass);
		}

		/// <summary>
		/// Display the image according to ID and class
		/// using the indicated optional label.
		/// Display all subclasses that exist if iSubClass is -1
		/// </summary>
		/// <param name="strImageID">ImageID is a name that determines storage location</param>
		/// <param name="imageClass">signifies portrait, signature or fingerprint</param>
		/// <param name="iSubClass">Image instance 0-9 or -1</param>
		/// <param name="strLabel"></param>
		private void DoDisplayClass(string strImageID, DCSDatabaseIF.ImageClass imageClass, int iSubClass, string strLabel)
		{
			Rectangle rectDisplay;
			int iMapped;

			int numInstances;
			string strTitlePrefix;
			bool bFingerMapping;
			// for display (and most cases except capture) treat 10Print as FingerPrint.
			if (imageClass == DCSDatabaseIF.ImageClass.TenPrint) imageClass = DCSDatabaseIF.ImageClass.Fingerprint;
			switch (imageClass)
			{
				default:
				case DCSDatabaseIF.ImageClass.Portrait:
					numInstances = this.m_nPortraitInstances;
					strTitlePrefix = "Port";
					bFingerMapping = false;
					break;
				case DCSDatabaseIF.ImageClass.Signature:
					numInstances = this.m_nSignatureInstances;
					strTitlePrefix = "Sig";
					bFingerMapping = false;
					break;
				case DCSDatabaseIF.ImageClass.Fingerprint:
					numInstances = this.m_nFingerprintInstances;
					if (m_b10Print && m_n10PrintInstances > numInstances) numInstances = m_n10PrintInstances;
					strTitlePrefix = "Fing";
					bFingerMapping = this.m_bFingerMapping && (numInstances != 10);
					break;
				case DCSDatabaseIF.ImageClass.Certificate:
					numInstances = 10;
					strTitlePrefix = "Cert";
					bFingerMapping = false;
					break;
			}
			/////////////////////////////////////////////////////////
			// find a screen display starting position for the class; 
			/////////////////////////////////////////////////////////
			System.Windows.Forms.Screen screen = System.Windows.Forms.Screen.PrimaryScreen;
			Rectangle rectScreen = screen.WorkingArea;
			Rectangle rectDisplayLast = Rectangle.Empty;  // = GetImgClassDisplayRectangle(imageClass);            // get previously stored loc & size of display window

			// find first and last instances already displayed
			DCSImageDisplay idFirst = null;
			DCSImageDisplay idLast = null;
			foreach (DCSDEV.DCSImageDisplay id in DCSDEV.DCSImageDisplay.m_arrayListImageDisplay)
			{
				if (id.ImageClass == imageClass)
				{
					idLast = id;
					if (idFirst == null) idFirst = id;
				}
			}

			if (idFirst == null)
			{
				// get previously stored loc & size of display window
				rectDisplayLast = GetImgClassDisplayRectangle(imageClass);
			}
			else
			{
				// a previous instance is found, place new instance adjacent to previous
				//Rectangle rectNext = new Rectangle(idLast.Location, idLast.ClientSize); //  new Rectangle(idLast.Location, idLast.Size);
				Rectangle rectNext = new Rectangle(idLast.Location, rectDisplayLast.Size); //  new Rectangle(idLast.Location, idLast.Size);
				rectNext.Offset(idLast.Width, 0);
				if (!(Rectangle.Intersect(rectNext, rectScreen)).Equals(rectNext))
				{
					// try placement below first instance when adjacent is off screen
					rectNext = new Rectangle(idFirst.Location, idFirst.ClientSize);
					rectNext.Offset(0, idFirst.Height);
					if (!(Rectangle.Intersect(rectNext, rectScreen)).Equals(rectNext))
					{
						// try cascade until off screen
						rectNext = new Rectangle(idLast.Location + new Size(24, 24), idLast.ClientSize);
						if (!(Rectangle.Intersect(rectNext, rectScreen)).Equals(rectNext))
						{
							// push back onto screen and revert to stacking for later instances
							if (rectNext.Right > rectScreen.Right) rectNext.X = rectScreen.Right - rectNext.Width;
							if (rectNext.Bottom > rectScreen.Bottom) rectNext.Y = rectScreen.Bottom - rectNext.Height;
						}
					}
				}
				rectDisplayLast = rectNext;
			}

			////////////////////////////////////////////////////////////////////////////////////////////////
			// Loop to Display all requested subclasses starting at location rectNewDisplayBase 
			////////////////////////////////////////////////////////////////////////////////////////////////
			for (int iMapIndex = 0, i = 0; i < numInstances; i++)
			{
				if (iSubClass != -1 && iSubClass != i) continue;
				if (iSubClass == -1 && bFingerMapping)
				{
					if (iMapIndex >= this.m_strFingerMap.Length)
					{
						if (i == 0)	// no fingers displayed
						{
							i = -1;
							bFingerMapping = false;
							continue;
						}
						break;
					}
					iMapped = this.m_strFingerMap[iMapIndex++] - '0';
				}
				else iMapped = i;

				System.Drawing.Bitmap image = DCSDatabaseIF.GetStoredImage(strImageID, imageClass, iMapped);

				if (image == null && bFingerMapping)
				{
					i--;
					continue;
				}
				try
				{
					// display all images of the class in a horizontal row 
					// unless there are more than 6 in the class,
					// then display them in two rows.
					// when going off screen revert to cascade then stack.

					rectDisplay = rectDisplayLast;      // if only one subclass is displayed this is the place
					if (iSubClass == -1)
					{
						if (numInstances <= 4)
						{
							if (i > 0) rectDisplay.Offset(rectDisplayLast.Width * i, 0);
						}
						else
						{
							if (i > 0)
							{
								// 5 or more instances
								if (i < (numInstances + 1) / 2)
								{
									// first row
									rectDisplay.Offset(rectDisplayLast.Width * i, 0);
								}
								else
								{
									// second row
									rectDisplay.Offset(rectDisplayLast.Width * (i - (numInstances + 1) / 2), rectDisplayLast.Height + DCSDEV.DCSImageDisplay.HeaderHeight);
								}
							}
						}
					}
					if (!(Rectangle.Intersect(rectDisplay, rectScreen)).Equals(rectDisplay))
					{
						// if off screen push back onto screen and revert to stacking for later instances
						if (rectDisplay.Right > rectScreen.Right) rectDisplay.X = rectScreen.Right - rectDisplay.Width;
						if (rectDisplay.Bottom > rectScreen.Bottom) rectDisplay.Y = rectScreen.Bottom - rectDisplay.Height - DCSDEV.DCSImageDisplay.HeaderHeight;
					}

					if (image != null && i == 0)
					{
						if (imageClass != DCSDatabaseIF.ImageClass.Certificate)
						{
							double dImageRatio = (double)image.Width / (double)image.Height;
							double dWindowRatio = (double)rectDisplay.Width / (double)rectDisplay.Height;
							if (dWindowRatio < dImageRatio * 0.90)	// adjust proportions if more than 5% off
								rectDisplay.Height = DCSMath.IntDivDouble(rectDisplay.Width, dImageRatio);
							else if (dWindowRatio > dImageRatio * 1.10)
								rectDisplay.Width = DCSMath.IntTimesDouble(rectDisplay.Height, dImageRatio);
						}
						rectDisplayLast = new Rectangle(rectDisplayLast.Location, rectDisplay.Size);
					}

					DCSDEV.DCSImageDisplay imageDisplay = new DCSDEV.DCSImageDisplay(this);

					//window is filled with image - preserving original ratio
					imageDisplay.ImageClass = imageClass;
					imageDisplay.ClientSize = rectDisplay.Size;
					imageDisplay.ImageSubClass = iMapped;
					imageDisplay.IsFirstSubclass = (i == 0);
					imageDisplay.pbDisplayImage.Location = new Point(0, 0);
					if ((imageClass == DCSDatabaseIF.ImageClass.Certificate) && (image != null))
						imageDisplay.pbDisplayImage.Size = image.Size;
					else
						imageDisplay.pbDisplayImage.Size = imageDisplay.ClientSize;

					imageDisplay.pbDisplayImage.Image = image;
					imageDisplay.TopMost = true;
					imageDisplay.ArchiveInstance = 0;
					imageDisplay.ImageID = strImageID;

					// compute label
					string strPostfix;
					if (strLabel == null || strLabel == "")
						strPostfix = ": " + strImageID;
					else
						strPostfix = ": " + strLabel;

					if (bFingerMapping)
					{
						imageDisplay.ImageTitle = (string)this.m_arrayFingerMapNames[iMapped] + strPostfix;
					}
					else
					{
						bool bIsFinger = (imageClass == DCSDatabaseIF.ImageClass.Fingerprint);
						if (iMapped == 0 && !bIsFinger)
							imageDisplay.ImageTitle = strTitlePrefix + strPostfix;
						else
							imageDisplay.ImageTitle = strTitlePrefix + (iMapped).ToString() + strPostfix;
					}

					imageDisplay.Location = rectDisplay.Location;
					if (imageClass == DCSDatabaseIF.ImageClass.Certificate)
						imageDisplay.CertsDisplayScale = -1.0;
					// scale of zero is fit width to window
					// scale of -1 is fit image to window preserving aspect ratio

					imageDisplay.Show();
					// add display window to list
					DCSDEV.DCSImageDisplay.m_arrayListImageDisplay.Add(imageDisplay);
				}
				catch (System.Exception ex)
				{
					DCSMsg.Show("ERROR displaying " + DCSDEV.DCSDatabaseIF.ClassToString(imageClass, iMapped) + " " + strImageID, ex);
					return;
				}
			}
		}
		internal void CycleArchiveImages(DCSDEV.DCSImageDisplay imageDisplay)
		{
			if (!m_bEnableImageHistory || !DCSDEV.DCSLicensing.IsLicensedOK(LicensedFeatures.ImageHistory, false)) return;

            // check image in UsersDB - it is imcompatible with history archive images
            if (DCSDatabaseIF.IsStoredInUsersDB(imageDisplay.ImageClass, imageDisplay.ImageSubClass))
                return;

			int nImageFiles = DCSDatabaseIF.GetStoredImageCount(imageDisplay.ImageID, imageDisplay.ImageClass, imageDisplay.ImageSubClass);
			int iCycles = 0;
			System.Drawing.Image image;
			if ((System.Windows.Forms.Control.ModifierKeys & Keys.Shift) == Keys.Shift)
			{
				while (true)
				{
					imageDisplay.ArchiveInstance--;
					if (imageDisplay.ArchiveInstance < 0) imageDisplay.ArchiveInstance = nImageFiles;
					image = DCSDatabaseIF.GetStoredHistoricImage(imageDisplay.ImageID, imageDisplay.ImageClass, imageDisplay.ImageSubClass, imageDisplay.ArchiveInstance);
					if (image != null) break;
					if (iCycles++ > nImageFiles) break;
				}
			}
			else
			{
				while (true)
				{
					imageDisplay.ArchiveInstance++;
					if (imageDisplay.ArchiveInstance > nImageFiles) imageDisplay.ArchiveInstance = 0;
					image = DCSDatabaseIF.GetStoredHistoricImage(imageDisplay.ImageID, imageDisplay.ImageClass, imageDisplay.ImageSubClass, imageDisplay.ArchiveInstance);
					if (image != null) break;
					if (iCycles++ > nImageFiles) break;
				}
			}
			if (imageDisplay.pbDisplayImage.Image != null) imageDisplay.pbDisplayImage.Image.Dispose();
			imageDisplay.pbDisplayImage.Image = image;

			string strWhen;
			DateTime dtNew;
			dtNew = DCSDatabaseIF.GetStoredHistoricImageDateTime(imageDisplay.ImageID, imageDisplay.ImageClass, imageDisplay.ImageSubClass, imageDisplay.ArchiveInstance);
			strWhen = dtNew.ToShortDateString() + " " + dtNew.ToShortTimeString();

			if (imageDisplay.ArchiveInstance == 0)
				imageDisplay.Text = imageDisplay.ImageTitle;
			else
            //    imageDisplay.Text = "(H" + imageDisplay.ArchiveInstance.ToString() + ") " + imageDisplay.ImageTitle + " " + strWhen;
                imageDisplay.Text = "(H" + imageDisplay.ArchiveInstance.ToString() + "/" + (nImageFiles - 1).ToString() + ") " + imageDisplay.ImageTitle + " " + strWhen;
        }

		private void DoDeleteBiometrics(string strImageID, DCSDatabaseIF.ImageClass imageClass, int iSubClass)
		{

			// delete existing old BINs for one subclass (aka instance)
			if (iSubClass != -1)
			{
				DCSDatabaseIF.DeleteBiometrics(strImageID, imageClass, iSubClass);
			}
			else
			{
				// delete all existing old BINs
				for (int i = 0; i < 10; i++)
				{
					DCSDatabaseIF.DeleteBiometrics(strImageID, imageClass, i);
				}
			}
		}

		// 
		private bool DoFIPSImportExport(string strImageID, bool bImport, string strFilename)
		{
			string strMyFile;
			if (bImport)
			{
				if (strFilename == null || strFilename == "")
				{
					System.Windows.Forms.OpenFileDialog openFileDialog1;
					openFileDialog1 = new System.Windows.Forms.OpenFileDialog();
					openFileDialog1.FileName = strImageID + "_FIPS.Dat";
					openFileDialog1.InitialDirectory = "";
					openFileDialog1.CheckFileExists = true;
					openFileDialog1.Filter = "Data Files(*.dat)|*.dat";
					openFileDialog1.Title = "Select FIPS input file";
					if (openFileDialog1.ShowDialog(this) != DialogResult.OK) return false;
					strMyFile = openFileDialog1.FileName;

					DialogResult dr = DCSDEV.DCSMsg.ShowOKC(String.Format("OK to read FIPS image data file {0} into image ID '{1}'", strMyFile, strImageID));
					if (dr == DialogResult.Cancel) return false;
				}
				else strMyFile = strFilename;

				DCSDEV.FIPS.FIPS_FACESTD fips = new DCSDEV.FIPS.FIPS_FACESTD();
				fips.Read(strMyFile, strImageID);
				DCSDEV.DCSMsg.Show(String.Format("FIPS image data file {0} has been read into image ID '{1}'", strMyFile, strImageID));
				return true;
			}
			else
			{
				// export
				if (strFilename == null || strFilename == "")
				{
					System.Windows.Forms.OpenFileDialog openFileDialog1;
					openFileDialog1 = new System.Windows.Forms.OpenFileDialog();
					openFileDialog1.FileName = strImageID + "_FIPS.Dat";
					openFileDialog1.InitialDirectory = "";
					openFileDialog1.CheckFileExists = false;
					openFileDialog1.Filter = "Data Files(*.dat)|*.dat";
					openFileDialog1.Title = "Select FIPS output file";
					if (openFileDialog1.ShowDialog(this) != DialogResult.OK) return false;
					strMyFile = openFileDialog1.FileName;
				}
				else strMyFile = strFilename;


				try { System.IO.File.Delete(strMyFile); }
				catch { ; }

				DCSDEV.FIPS.FIPS_FACESTD fips = new DCSDEV.FIPS.FIPS_FACESTD();
				fips.Populate(strImageID);
				fips.Write(strMyFile);
				DCSDEV.DCSMsg.Show(String.Format("FIPS output record for image ID '{0}' \n is written to file:\n{1}", strImageID, strMyFile));
				return true;
			}
		}

		// import and merge a class with all subclasses from indicated source data root directory
		// source must use file directories for image storage - not oleDB 
		private bool DoImportMergeClass(string strImageID, string strSourceID, string strSourceDataRoot, DCSDatabaseIF.ImageClass imageClass)
		{
			this.CloseDisplays();

			int iMapped;
			string strSourceFilename;

			int numInstances;
			bool bFingerMapping;
			// for display (and most cases except capture) treat 10Print as FingerPrint.
			if (imageClass == DCSDatabaseIF.ImageClass.TenPrint) imageClass = DCSDatabaseIF.ImageClass.Fingerprint;
			switch (imageClass)
			{
				default:
					return false;
				case DCSDatabaseIF.ImageClass.Portrait:
					numInstances = (int)this.numericUpDownPortraitInstances.Value;
					bFingerMapping = false;
					break;
				case DCSDatabaseIF.ImageClass.Signature:
					numInstances = (int)this.numericUpDownSignatureInstances.Value;
					bFingerMapping = false;
					break;
				case DCSDatabaseIF.ImageClass.Fingerprint:
					numInstances = (int)this.numericUpDownFingerInstances.Value;
					if (m_b10Print && m_n10PrintInstances > numInstances) numInstances = m_n10PrintInstances;
					bFingerMapping = this.m_bFingerMapping && (numInstances != 10);
					break;
				case DCSDatabaseIF.ImageClass.Certificate:
					numInstances = 10;
					bFingerMapping = false;
					break;
			}
			// merge all instances which exist
			for (int iMapIndex = 0; iMapIndex < numInstances; iMapIndex++)
			{
				if (bFingerMapping)
				{
					if (iMapIndex >= this.m_strFingerMap.Length) break;
					iMapped = this.m_strFingerMap[iMapIndex++] - '0';
				}
				else iMapped = iMapIndex;
				strSourceFilename = DCSDatabaseIF.GetFullnameOfImage(strSourceID, imageClass, iMapped, true, strSourceDataRoot);
				if (strSourceFilename == null) continue;

				DoImportExportSubclass(strImageID, true /*bImport*/, true /*bPreserveDate*/, strSourceFilename, imageClass, iMapped);
			}
			return true;
		}

		// Import / Export image: id, class, & subclass 0-9
		// bPreserveDate true=preserves original files date when importing; false sets date to Now
		private bool DoImportExportSubclass(string strImageID, bool bImport, bool bPreserveDate, string strFilename, DCSDatabaseIF.ImageClass imageClass, int iSubClass)
		{
			bool bRet;
			this.CloseDisplays();

			if (bImport)	// import
			{
				try
				{
					// get image file to import
					if (strFilename == null || strFilename == "")
					{
						// name is not specified - as operator
						System.Windows.Forms.OpenFileDialog openFileDialog1;
						openFileDialog1 = new System.Windows.Forms.OpenFileDialog();
						openFileDialog1.FileName = "";
						openFileDialog1.InitialDirectory = "";
						openFileDialog1.Filter = "Image Files(*.BMP;*.JPG;*.JPEG;*.GIF;*.PNG;*.TIF)|*.BMP;*.JPG;*.JPEG;*.GIF;*.PNG;*.TIF|All files (*.*)|*.*";
						openFileDialog1.Title = String.Format("Select {0} file to import", imageClass.ToString());
						openFileDialog1.CheckFileExists = true;
						if (openFileDialog1.ShowDialog(this) != DialogResult.OK) return false;
						strFilename = openFileDialog1.FileName;
					}

					// When supporting the merge function, do not go through Finisher.
					// Instead use a direct copy and preserve the original date.
					if (!bPreserveDate)
					{
						// import into database - assign new date
						Bitmap bitmapImage;
						bitmapImage = new Bitmap(strFilename);

						// Finisher process puts image into a temporary file and SetStoredImage puts it into the Image data storage according to FILES or OLE
						string strTempFile = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDatabaseIF.GetNameOfImage("_Temp", imageClass));
						if (System.IO.File.Exists(strTempFile)) System.IO.File.Delete(strTempFile);

						// import to SDS database according to finisher properties
						FinisherProperties dlgFinisherProperties;
						dlgFinisherProperties = new FinisherProperties(imageClass);

						// bitmapImage.Save(strDBFilename, System.Drawing.Imaging.ImageFormat.Jpeg);
						bRet = DCSDEV.DCSImageProcessing.SaveEx(
							bitmapImage,
							strTempFile,
							dlgFinisherProperties.PhotoType,
							dlgFinisherProperties.Compression,
							dlgFinisherProperties.MaxKB,
							dlgFinisherProperties.PixelDepthIndex);

						if (!bRet)
						{
							DCSMsg.Show("Compressed file exceeds maximum size " + dlgFinisherProperties.MaxKB * 1000, MessageBoxIcon.Error);
							return false;
						}
						else
						{
							bRet = DCSDatabaseIF.SetStoredImage(strTempFile, strImageID, imageClass, iSubClass, false);
							if (System.IO.File.Exists(strTempFile)) System.IO.File.Delete(strTempFile);
						}
					}	//end if (!bPreserveDate)
					else
					{
						// merging a database - preserve original date
						DateTime dateStored = System.IO.File.GetLastWriteTime(strFilename);
						// put file name into image DB
						bRet = DCSDatabaseIF.SetStoredImage(strFilename, strImageID, imageClass, iSubClass, dateStored, false);
						return bRet;
					}
				}
				catch (Exception ex)
				{
					DCSMsg.Show("ERROR in Import:", ex);
					return false;
				}
			}	// end if (bImport) 
			else
			{
				// export
				try
				{
					if (strFilename == null || strFilename == "")
					{
						// output file name is not specified, ask user
						System.Windows.Forms.OpenFileDialog openFileDialog1;
						openFileDialog1 = new System.Windows.Forms.OpenFileDialog();
						openFileDialog1.FileName = strImageID;
						openFileDialog1.InitialDirectory = "";
						openFileDialog1.Filter = "Image Files(*.BMP;*.JPG;*.JPEG;*.GIF;*.PNG;*.TIF)|*.BMP;*.JPG;*.JPEG;*.GIF;*.PNG;*.TIF|All files (*.*)|*.*";
						openFileDialog1.Title = String.Format("Select output file name for {0} {1}", imageClass.ToString(), iSubClass.ToString());
						openFileDialog1.CheckFileExists = false;
						if (openFileDialog1.ShowDialog(this) != DialogResult.OK) return false;
						strFilename = openFileDialog1.FileName;
					}
					bRet = DCSDatabaseIF.GetStoredImageFile(strFilename, strImageID, imageClass, iSubClass);
					if (!bRet) return false;
				}
				catch (Exception ex)
				{
					DCSMsg.Show("ERROR in Export:", ex);
					return false;
				}
			}
			return true;
		}

		// see description at public GenerateBiometric
		// RETURN: Number generated. -1=error; 0=none generated
		private int DoGenerateBiometric(string strImageID, DCSDatabaseIF.ImageClass imageClass, int iSubClass, bool bVerbose)
		{
            string strMsg;
			string strRet;
			bool bRet;

			int iInstancesFound = 0;
			int iMapped;

			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.GenerateFingerBiometrics, true)) return -1;

			// protect against bad input
			// syh only finger is supported so far
			bool bIsFinger = (imageClass == DCSDatabaseIF.ImageClass.Fingerprint) || (imageClass == DCSDatabaseIF.ImageClass.TenPrint);
			if (!bIsFinger) return -1;

			if (iSubClass == -1)
				this.DoDeleteBiometrics(strImageID, imageClass, -1);

			for (int iMapIndex = 0, i = 0; i < 10; i++)
			{
				if (iSubClass != -1 && i != iSubClass) continue;
				if (iSubClass == -1 && this.m_bFingerMapping)
				{
					if (iInstancesFound == m_nFingerprintInstances) break;
					if (iMapIndex >= this.m_strFingerMap.Length) break;
					iMapped = this.m_strFingerMap[iMapIndex++] - '0';
				}
				else iMapped = i;

				bRet = this.DoGenBiometric(strImageID, imageClass, iMapped, out strRet);

				string strFingerName;
				if (this.m_bFingerMapping)
					strFingerName = (string)m_arrayFingerMapNames[iMapped];
				else
					strFingerName = "Fing" + iMapped.ToString();

                if (!bRet)
                {
                    strMsg = String.Format("Error generating biometric for {0} finger for image ID {1}. \n\n{2}", strFingerName, strImageID, strRet);
                }
                else
                {
                    iInstancesFound++;
                    if (strRet != null)
                        strMsg = String.Format("Successful enrollment of 1 out of 2 biometrics for {0} finger for image ID {1}. \n\n{2}", strFingerName, strImageID, strRet);
                    else
                        strMsg = String.Format("Successful enrollment of biometrics for {0} finger for image ID {1}.", strFingerName, strImageID);
                }
                if (bVerbose) DCSDEV.DCSMsg.Show(strMsg, (bRet ? MessageBoxIcon.None : MessageBoxIcon.Error));
                else DCSDEV.DCSMsg.Log(strMsg);
				continue;
			}
			return (iInstancesFound);
		}

		// Delete existing biometric and make new if possible
		// Return true if no error - else return false and set strError with error to display
        private bool DoGenBiometric(string strImageID, DCSDatabaseIF.ImageClass imageClass, int iMapped, out string strError)
		{
			bool bRet;
			string strFinger1 = null;
			string strFinger2 = null;

			// protect against bad input
			// syh only finger is supported so far
			bool bIsFinger = (imageClass == DCSDatabaseIF.ImageClass.Fingerprint) || (imageClass == DCSDatabaseIF.ImageClass.TenPrint);
			if (!bIsFinger)
			{
				strError = imageClass.ToString() + " biometrics are not suppoorted.";
				return false;
			}
			DCSDatabaseIF.DeleteBiometrics(strImageID, imageClass, iMapped);

			///////////////////////////////////
			// Get first finger image        //
			///////////////////////////////////
			if (DCSDatabaseIF.IsOleDBType())
			{
				strFinger1 = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDatabaseIF.GetNameOfImage("_Temp", DCSDatabaseIF.ImageClass.Fingerprint));
				bRet = DCSDatabaseIF.GetStoredImageFile(strFinger1, strImageID, imageClass, iMapped);
			}
			else
			{
				// get name of finger - check for existance of file
				strFinger1 = DCSDatabaseIF.GetFullnameOfImage(strImageID, imageClass, iMapped, true);
				bRet = (strFinger1 != null);
			}
			if (!bRet)
			{
				strError = "Finger image not found.";
				return false;
			}

#if LISKA_BIN
			//////////////////////////////////////////////////////
			// Get second finger image for Liska BIN            //
			//////////////////////////////////////////////////////
			if (m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_LISKA_BIN2 || m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_LISKA_AND_AIS)
			{
				string strImageID2 = strImageID + "_BIN2";
				// get name of second image of same finger if required by biometric generator - check for existance of file
				if (DCSDatabaseIF.IsOleDBType())
				{
					strFinger2 = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDatabaseIF.GetNameOfImage("_Temp2", DCSDatabaseIF.ImageClass.Fingerprint));
					bRet = DCSDatabaseIF.GetStoredImageFile(strFinger2, strImageID2, imageClass, iMapped);
				}
				else
				{
					// get name of finger - check for existance of file
					strFinger2 = DCSDatabaseIF.GetFullnameOfImage(strImageID2, imageClass, iMapped, true);
					bRet = (strFinger2 != null);
				}
				if (!bRet)
				{
					strError = "Second finger image not found.";
					return false;
				}
			}
#endif
			// Liska and AIS have BIN strings to return.  Innovatrics returns nothing
			string strLiskaBIN;
			string strMinutiaBIN;
			bRet = this.GenFingerBiometricsFromFiles(strImageID, strFinger1, strFinger2, iMapped, out strLiskaBIN, out strMinutiaBIN, out strError);

#if LISKA_BIN
			// put Liska BIN into the image database
			// SYH TODO: currently bio interfaces assume a string is returned - should be changed to binary data.
			if (bRet && strLiskaBIN != null)
			{
				DCSDatabaseIF.SetStoredBIN(strLiskaBIN, strImageID, DCSDatabaseIF.ImageClass.BINL, iMapped);
			}
#endif
			// put AIS minutia BIN into the image database
			if (bRet && strMinutiaBIN != null)
			{
				if (strMinutiaBIN.StartsWith("AG"))
					DCSDatabaseIF.SetStoredBIN(strMinutiaBIN, strImageID, DCSDatabaseIF.ImageClass.BING, iMapped);
				else
					DCSDatabaseIF.SetStoredBIN(strMinutiaBIN, strImageID, DCSDatabaseIF.ImageClass.BINA, iMapped);
			}
			// If ret is true and Liska and AIS are null the type must be Innovatrics
			if (bRet) strError = null;

			// cleanup - delete temp files
			if (DCSDatabaseIF.IsOleDBType())
			{
				System.IO.File.Delete(strFinger1); //Cleanup temp file
				if (strFinger2 != null) System.IO.File.Delete(strFinger2); //Cleanup temp file
			}
			return bRet;
		}

		// Return true if no error - else return false and set strError with error to display
		/// <summary>
		/// Make new biometrics if possible 
		/// </summary>
		/// <param name="strImageID"></param>
		/// <param name="strFinger1">Finger image</param>
		/// <param name="strFinger2">Second image of same finger if Liska BINs</param>
		/// <param name="iMapped">DCS finger indedx</param>
		/// <param name="strLiskaBIN"></param>
		/// <param name="strMinutiaBIN"></param>
		/// <param name="strError"></param>
		/// <returns></returns>
		private bool GenFingerBiometricsFromFiles(string strImageID, string strFinger1, string strFinger2, int iMapped, out string strLiskaBIN, out string strMinutiaBIN, out string strError)
		{
			bool bRet;
#if LISKA_DEF
			bool bLiskaOK = true;
			string strLiskaError = null;
#endif
            bool bMinutiaOK = true;
			string strMinutiaError = null;
			bool bInnovatricsOK = true;
			string strInnovatricsError = null;
			bool bAgoraOK = true;
			string strAgoraError = null;

			strMinutiaBIN = null;
			strLiskaBIN = null;

#if LISKA_BIN
			if (m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_LISKA_BIN2 || m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_LISKA_AND_AIS)
			{
				try		// try liska type
				{
					bLiskaOK = true;
					string strRawBIN = "ERROR";
					if (strFinger2 == null) bLiskaOK = false;
					if (bLiskaOK)
					{
						try
						{
							strRawBIN = BIN2_FromFingerprints(strFinger1, strFinger2);
							if (strRawBIN.ToUpper().StartsWith("ERROR"))
							{
								strLiskaError = strRawBIN;
								bLiskaOK = false;
							}
						}
						catch (Exception ex)
						{
							strLiskaError = ex.Message;
							bLiskaOK = false;
						}
						if (!bLiskaOK && m_bLogBinErrors) LogLiskaEnrollErrorData(strFinger1, strFinger2);
					}
					if (bLiskaOK)
					{
						strLiskaBIN = "LS01X" + iMapped.ToString() + strRawBIN + ";";
						// prefix is LSVVXF: LS=biometric type LS is Liska BIN; X is reserved; VV=version; F = finger index 0-9 
					}
				}
				catch (Exception ex)
				{
					strLiskaBIN = null;
					strLiskaError = ex.Message;
					bLiskaOK = false;
				}
			}
			if (m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_AIS_MINUTIA || m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_LISKA_AND_AIS)
#endif
            if (m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_AIS_MINUTIA)
			{
				try		// try Minutia type
				{
					// AIS Minutia generation process puts BIN into a file.
					string strAISBINFile = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDatabaseIF.GetNameOfImage("_TempBin", DCSDatabaseIF.ImageClass.BINA));
					if (File.Exists(strAISBINFile)) File.Delete(strAISBINFile);

					// AIS Minutia generation process puts BIN into a file
					bRet = DCSExtractFeatures(strFinger1, strAISBINFile, iMapped);
					if (bRet)
					{
						if (File.Exists(strAISBINFile))
						{
							System.IO.StreamReader stream = new System.IO.StreamReader(strAISBINFile);
							strMinutiaBIN = stream.ReadToEnd();
							stream.Close();
							File.Delete(strAISBINFile);
						}
						else bRet = false;
					}
					bMinutiaOK = bRet;
					if (!bMinutiaOK)
					{
						strMinutiaError = "Minutia extraction error.";
					}
				}
				catch (Exception ex)
				{
					strMinutiaBIN = null;
					bMinutiaOK = false;
					strMinutiaError = "Minutia extraction error.\n\n" + ex.Message;
				}
			}
			if (m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_INNOVATRICS)
			{
				try		// Innovatrics minutia type
				{
					// DCSMsg.Show("Process Innovatrics Biometric");
					bRet = DCSDEV.DCSInnovatricsIF.DCSInnovatricsIF.Init(m_strDataRootDir);	// init if not already done
					if (bRet)
					{
						bRet = DCSDEV.DCSInnovatricsIF.DCSInnovatricsIF.AddFinger(strImageID, iMapped, strFinger1);
						if (!bRet) strInnovatricsError = "DCS Innovatrics CheckFinger error.";
					}
					else strInnovatricsError = "DCS Innovatrics Init error.";
					bInnovatricsOK = bRet;

				}
				catch (Exception ex)
				{
					bInnovatricsOK = false;
					strInnovatricsError = "Innovatrics extraction error.\n\n" + ex.Message;
				}
			}
			if (m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_AGORA)
			{
				try		// try Agora type
				{
					strAgoraError = "Error";
					bAgoraOK = DCSDEV.AgoraIF.AgoraExtractFeatures(strFinger1, iMapped, out strMinutiaBIN);

				}
				catch (Exception ex)
				{
					strMinutiaBIN = null;
					bAgoraOK = false;
					strMinutiaError = "Minutia extraction error.\n\n" + ex.Message;
				}
			}

			////////////////////////////////////////////////////////////////////////////////////////
			// Report errors that may have occured
			////////////////////////////////////////////////////////////////////////////////////////
			strError = null;
			bRet = false;
			if (m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_AIS_MINUTIA)
			{
				if (bMinutiaOK) bRet = true;
				else strError = "\nAIS Error: " + strMinutiaError;
			}
#if LISKA_BIN
			else if (m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_LISKA_BIN2)
			{
				if (bLiskaOK) bRet = true;
				else strError = "\nError: " + strLiskaError;
			}
			else if (m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_LISKA_AND_AIS)
			{
				if (bLiskaOK || bMinutiaOK) bRet = true;
				if (!bLiskaOK || !bMinutiaOK)
				{
					if (!bLiskaOK) strError = "\nError: " + strLiskaError;
					else strError = "";
					if (!bMinutiaOK) strError = strError + "\nAIS Error: " + strMinutiaError;
				}
			}
#endif
			else if (m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_INNOVATRICS)
			{
				if (bInnovatricsOK) bRet = true;
				else strError = "\nInnovatrics Error: " + strInnovatricsError;
			}
			else if (m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_AGORA)
			{
				if (bAgoraOK) bRet = true;
				else strError = "\nAgora Error: " + strAgoraError;
			}
			return bRet;
		}

		// show all the biometrics BINS that have been generated for given imageID
		private void DoShowBiometrics(string strImageID, int iSubClass)
		{
			// display all biometrics generated
			string strBiometric = "ERROR";
			string strBiometrics = "";
			int iInstancesFound = 0;
			int iMapped;
			for (int iMapIndex = 0, i = 0; i < 10; i++)
			{
				if (iSubClass != -1 && i != iSubClass) continue;
				if (iSubClass == -1 && this.m_bFingerMapping)
				{
					if (iInstancesFound == m_nFingerprintInstances) break;
					if (iMapIndex >= this.m_strFingerMap.Length) break;
					iMapped = this.m_strFingerMap[iMapIndex++] - '0';
				}
				else iMapped = i;

				if (m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_INNOVATRICS)
				{
					int idx1 = -1;
					int idx2 = -1;
					bool bRet = DCSDEV.DCSInnovatricsIF.DCSInnovatricsIF.Init(m_strDataRootDir);	// init if not already done
					if (bRet)
					{
						bRet = DCSDEV.DCSInnovatricsIF.DCSInnovatricsIF.GetFingerIndices(strImageID, out idx1, out idx2);
					}
					if (bRet)
					{
						if (iMapped == idx1)
							strBiometric = "IS01X" + idx1.ToString();
						else if
							(iMapped == idx2) strBiometric = "IS01X" + idx2.ToString();
					}
				}
				else
				{
					strBiometric = DCSDatabaseIF.GetBiometric(strImageID, DCSDatabaseIF.ImageClass.Fingerprint, iMapped);
				}
				if (!strBiometric.StartsWith("ERROR"))
				{
					strBiometrics += "Biometric " + iMapped.ToString() + "\r\n" + strBiometric + "\r\n";
					iInstancesFound++;
				}
				continue;
			}
			if (iInstancesFound != 0)
				DCSMsg.Show(strBiometrics);
			else
				DCSMsg.Show("ERROR: No generated biometrics to display");
		}

		/// <summary>
		/// Capture a finger and search for a match in the database
		/// </summary>
		/// <param name="strImageID">ImageID or "_LIVE"</param>
		/// <param name="iSubClass">Subclass indicates which finger</param>
		/// <param name="arrayImageIDs">list of imageIDs which match</param>
		/// <returns>"ERROR", "CANCEL", or "OK".  </returns>
		private string DoSearchFingerInDB(string strImageID, int iSubClass, ArrayList arrayImageIDs)
		{
            if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.SearchFingerBiometrics, true)) 
                return "ERROR";

			ArrayList arrayBINs = new ArrayList();
			int iMapped;
			bool bSearchDone = false;   // becomes true after a search is done for a finger and no match is found.

			if (strImageID == "_LIVE")
			{
				///////////////////////////////////////////////////////////////
				// Capture live finger image to test
				///////////////////////////////////////////////////////////////
				string strFingerFile = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDatabaseIF.GetNameOfImage("_LIVE", DCSDEV.DCSDatabaseIF.ImageClass.Fingerprint));

				string strCurrFingerName = null;
				string strPrevFingerName = null;
				bool bFirstTime = true;
				for (int iMapIndex = 0, i = 0; i < 10; i++)
				{
					if (iSubClass != -1 && i != iSubClass) continue;
					if (iSubClass == -1 && this.m_bFingerMapping)
					{
						if (iMapIndex >= this.m_strFingerMap.Length) break;
						iMapped = this.m_strFingerMap[iMapIndex++] - '0';
					}
					else iMapped = i;
					strCurrFingerName = (this.m_bFingerMapping ? (string)this.m_arrayFingerMapNames[iMapped] : iMapped.ToString());
                    if (!bFirstTime)
                    {
                        if (bSearchDone)
                        {
                            // when previous finger did not match, ask about trying this next finger
                            string msg = (String.Format("No match found for finger {0}. \n\nDo you want to try finger '{1}'?", strPrevFingerName, strCurrFingerName));
                            bSearchDone = false;
                            DialogResult result = DCSDEV.DCSMsg.ShowYNC(msg);
                            if (result == DialogResult.No) continue;
                            else if (result == DialogResult.Cancel) break;
                        }
                        else
                        {
                            string msg = (String.Format("Do you want to try finger '{0}'?", strCurrFingerName));
                            DialogResult result = DCSDEV.DCSMsg.ShowYNC(msg);
                            if (result == DialogResult.No) continue;
                            else if (result == DialogResult.Cancel) break;

                            //                        if (!bFirstTime)
                            //						{
                            //							// when previous finger capture was cancelled, ask about trying next finger
                            //							string msg;
                            //							msg = String.Format("Finger '{0}' was canceled. \r\nDo you want to try another finger?", strPrevFingerName);
                            //							if (DCSDEV.DCSMsg.ShowYN(msg) == DialogResult.No) break;
                            //						}
                        }
                    }
					bSearchDone = false;
					bFirstTime = false;
					strPrevFingerName = strCurrFingerName;

					//////////////////////////////////////////////
					// Capture the Test image(ie LIVE) finger   //
					//////////////////////////////////////////////
                    string strFingerTitle;
					if (this.m_bFingerMapping)
						strFingerTitle = strCurrFingerName + ": Image to search for";
					else
						strFingerTitle = "Fing" + iMapped.ToString() + ": Image to search for";

                    switch (DoFingerCaptureUtil(strFingerTitle, strFingerFile))	// 0= software did not finish; 1=user canceled; 2=OK
                    {
                        case 0:
                        case 1:
                        default:
                            continue;
                        case 2:
                            /////////////////////////////////////////////////////////////
                            // OK, search the BIN database                                 //
                            /////////////////////////////////////////////////////////////
                            int iRet = SearchOneFinger(strFingerFile, iMapped, arrayImageIDs);		// strImageID = "_LIVE"
                            if (iRet < 0) return "ERROR";
                            else if (iRet == 0)
                            {
                                bSearchDone = true;
                                continue;
                            }
                            else
                            {
                                return "OK";
                            }
                    }
				}
				return "CANCEL";
			}
			else
			{
				/////////////////////////////////////////////////////////////////////////////
				// look for matches using every finger that exists for this imageID
				/////////////////////////////////////////////////////////////////////////////
				for (int iMapIndex = 0, i = 0; i < 10; i++)
				{
					if (iSubClass != -1 && i != iSubClass) continue;
					if (iSubClass == -1 && this.m_bFingerMapping)
					{
						if (iMapIndex >= this.m_strFingerMap.Length) break;
						iMapped = this.m_strFingerMap[iMapIndex++] - '0';
					}
					else iMapped = i;

					/////////////////////////////////////////////////////////////
					// search the BIN database                                 //
					/////////////////////////////////////////////////////////////

                    // syh todo - this method assumes finger images are always saved.  want a method that
                    // can use BIN directly if images are not saved.  This would require improved interfaces to Innovatrics, AIS, and Agora
                    // and could not be used with Liska.
                    string strFingerFile;
                    if (DCSDatabaseIF.IsOleDBType())
                    {
                        strFingerFile = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDatabaseIF.GetNameOfImage("_SOURCE", DCSDatabaseIF.ImageClass.Fingerprint));
                        bool bRet = DCSDatabaseIF.GetStoredImageFile(strFingerFile, strImageID, DCSDEV.DCSDatabaseIF.ImageClass.Fingerprint, iMapped);
                        if (!bRet) continue;                    // no file
                    }
                    else
                    {
                        strFingerFile = DCSDatabaseIF.GetFullnameOfImage(strImageID, DCSDatabaseIF.ImageClass.Fingerprint, iMapped, true);
                        if (strFingerFile == null) continue;	// no file
                    }

					int iRet = SearchOneFinger(strFingerFile, iMapped, arrayImageIDs);
					if (iRet > 0) return "OK";
					else if (iRet == 0) continue;
					else return "ERROR";
				}
				return "NO";
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="strFingerFile"></param>
		/// <param name="iMapped"></param>
		/// <param name="arrayImageIDs"></param>
		/// <returns>number of matches(up to 5) or negative if error
        ///         -101 = Innovatrics Init error
        ///         -102 = Innovatrics matching engine error
        ///         -2   = unsupported biometric type
        /// </returns>
		private int SearchOneFinger(string strFingerFile, int iMapped, ArrayList arrayImageIDs)
		{
			/////////////////////////////////////////////////////////////
			// search the BIN database                                 //
			/////////////////////////////////////////////////////////////
			bool bRet;

			if (m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_INNOVATRICS)
			{
                ///////////////////////////////////////////////////////////
                // Innovatrics has its own data base of fingerprints     //
                ///////////////////////////////////////////////////////////
				bRet = DCSDEV.DCSInnovatricsIF.DCSInnovatricsIF.Init(m_strDataRootDir);	// init if not already done
				if (!bRet) return -101;

				bRet = DCSDEV.DCSInnovatricsIF.DCSInnovatricsIF.SearchForMatches(strFingerFile, iMapped, arrayImageIDs);
				if (bRet)
					return arrayImageIDs.Count;
				else return -102;		// error
			}
			else
			{
                ///////////////////////////////////////////////////////////
                // Use IDServices native database of fingerprints        //
                ///////////////////////////////////////////////////////////
                string strImageID;
                string strSourceBINFile = null;
                DCSDEV.DCSDatabaseIF.ImageClass imageClass;
                switch (m_iSelectedFingerBioType)
                {
                    case DCSDatabaseIF.DCSBioTypes.BIOTYPE_AGORA:
                        // Agora matches a source finger file with a target BIN string
                        imageClass = DCSDatabaseIF.ImageClass.BING;
                        break;
                    case DCSDatabaseIF.DCSBioTypes.BIOTYPE_AIS_MINUTIA:
#if LISKA_BIN
                    case DCSDatabaseIF.DCSBioTypes.BIOTYPE_LISKA_AND_AIS:
#endif
                        imageClass = DCSDatabaseIF.ImageClass.BINA;
                        // AIS interface matches source BIN in a file to a target BIN in a file
                        // so generate source BIN into a file.
                        strSourceBINFile = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDatabaseIF.GetNameOfImage("_SOURCE", DCSDatabaseIF.ImageClass.BINA));
                        DCSExtractFeatures(strFingerFile, strSourceBINFile, iMapped);
                        break;
#if LISKA_BIN
                    case DCSDatabaseIF.DCSBioTypes.BIOTYPE_LISKA_BIN2:
                        // Liska matches a source finger file with a target BIN string - ONLY
                        imageClass = DCSDatabaseIF.ImageClass.BINL;
                        break;
#endif
                    default:
    				    return -2;
                }
                arrayImageIDs.Clear();

                string strTargetBIN;
                string strTargetBinFile;
                int iScore;
                int iRet;
                bool bMatches;
                strImageID = DCSDatabaseIF.GetIDofFirstStoredBIN(imageClass, iMapped);
                while (strImageID != null)
                {
                    // get BIN for this image ID and test for a match
                    strTargetBIN = DCSDatabaseIF.GetStoredBIN(strImageID, imageClass, iMapped);
                    bMatches = false;
                    switch (m_iSelectedFingerBioType)
                    {
                        case DCSDatabaseIF.DCSBioTypes.BIOTYPE_AGORA:
                            // Agora matches a source finger file with a target BIN string
                            // SYH todo - add Agora interface to match using a source BIN in a string.
                            //          - also add a mode that uses Agora finger database 
                            iRet = DCSDEV.AgoraIF.AgoraCompareFingerToBIN(strFingerFile, iMapped, strTargetBIN, out iScore);
                            bMatches = (iRet == 1);    // return -1, 0, or 1: if error, no match, or match
                            break;
                        case DCSDatabaseIF.DCSBioTypes.BIOTYPE_AIS_MINUTIA:
#if LISKA_BIN
                        case DCSDatabaseIF.DCSBioTypes.BIOTYPE_LISKA_AND_AIS:
#endif
                            // AIS interface matches source BIN in a file to a target BIN in a file
                            // so put target bin into a file
                            // SYH to add AIS interface to work with BINs in strings
                            if (DCSDatabaseIF.IsOleDBType())
                            {
                                strTargetBinFile = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDatabaseIF.GetNameOfImage("_TEMP", DCSDatabaseIF.ImageClass.BINA));
                                DCSDatabaseIF.GetStoredImageFile(strTargetBinFile, strImageID, DCSDEV.DCSDatabaseIF.ImageClass.BINA, iMapped);
                            }
                            else
                            {
                                strTargetBinFile = DCSDatabaseIF.GetFullnameOfImage(strImageID, DCSDatabaseIF.ImageClass.BINA, iMapped, false);
                            }
                            iScore = DCSVerifyFeatures(strTargetBinFile, strSourceBINFile);
                            bMatches = (iScore >= m_iThresholdMinutia);
                            break;
#if LISKA_BIN
                        case DCSDatabaseIF.DCSBioTypes.BIOTYPE_LISKA_BIN2:
                            // Liska matches a source finger file with a target BIN string - ONLY
                            iScore = BIN2_Match(strFingerFile, strTargetBIN.Substring(6)) * 10; // use BIN with stripped off BIN header
                            bMatches = (iScore >= m_iThresholdBIN2);
                            break;
#endif
                    }

                    // if a match add to the array
                    if (bMatches) arrayImageIDs.Add(strImageID);
                    
                    // go to next image ID
                    strImageID = DCSDatabaseIF.GetIDofNextStoredBIN();
                }
                return arrayImageIDs.Count;
			}
		}

		// See description at public VerifyBiometric
		// Return "YES, NO, CANCEL or ERROR with possible descriptive text or scores following.
		private string DoVerifyFingerVsImageID(string strImageID, int iSubClass)
		{
			string strFinger_Captured;
			bool bRet;
			ArrayList arrayBINs = new ArrayList();

			/////////////////////////////////////////////////////
			// is it a special case: not comparing to database //
			// if so, get all data and bins from card          //
			/////////////////////////////////////////////////////
			bool bVerifyVsDB;
			string strBiometricFromDocument = "";
			if (strImageID == "_BIN_BARCODE")			//verifying against 2D Barcode
			{
				bVerifyVsDB = false;
				// read barcode and put it in strBiometricFromDocument
				// scanned string in file needs to be parsed
				bRet = this.DoScanBarcode(out strBiometricFromDocument);
				if (!bRet) return "CANCEL";
			}
			else if (strImageID == "_BIN_MAGSTRIPE")	//verifying against magnetic stripe
			{
				bVerifyVsDB = false;
				// read mag stripe and put it in strBiometricFromDocument
				bRet = this.DoScanMagstripe(out strBiometricFromDocument);
				if (!bRet) return "CANCEL";
			}
			else if (strImageID == "_BIN_SMARTCHIP")	//verifying against smart chip
			{
				DCSDEV.DCSChipIF.DCSDEV_ChipIF.ChipIFType eChipIFType = (DCSDEV.DCSChipIF.DCSDEV_ChipIF.ChipIFType)m_ps.GetIntParameter("ChipIFType", 0);
				if (eChipIFType == DCSDEV.DCSChipIF.DCSDEV_ChipIF.ChipIFType.MIFARE_RFID_CHIP_FORMULA)
				{
					// scan for simple verify BIN is only supported in MIFARE chip
					bVerifyVsDB = false;
					// read smart chip and put BIN in strBiometricFromDocument
					string strTempFile = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, "_TempChip.Dat");

					int iRet = this.DoScanSmartChip(strTempFile, out strBiometricFromDocument, true);	// arg3=true=for verify biometric
					if (iRet < 0) return "ERROR";
					else if (iRet == 0) return "CANCEL";
				}
				else if (eChipIFType == DCSDEV.DCSChipIF.DCSDEV_ChipIF.ChipIFType.ICAO_CONTACT_CHIP)
				{
					// scan chip, put files into default places
					int iRet = this.DoScanSmartChip(null, out strBiometricFromDocument, true);	// arg3=true=for verify biometric
					if (iRet < 0) return "ERROR";
					else if (iRet == 0) return "CANCEL";

					string strICAOFinger1File = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDEV.DCSChipIF.DCSDEV_ChipIF.CONST_ICAO_FINGER1_FILE_READ);
					string strICAOFinger2File = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDEV.DCSChipIF.DCSDEV_ChipIF.CONST_ICAO_FINGER2_FILE_READ);
					if (!System.IO.File.Exists(strICAOFinger1File)) strICAOFinger1File = null;
					if (!System.IO.File.Exists(strICAOFinger2File)) strICAOFinger2File = null;

					// SYH ToDo
					// convert finger files to BMP if WSQ
					DCSMsg.Show(String.Format("Finger files are: \n   {0}\n   {1}", strICAOFinger1File, strICAOFinger2File));

					// Move finger files to database locations with a temp image ID
					// need a way to handle image subclass = finger number

					// DoGenerateBiometric() - only non-Liska BIN2 -

					// Use verify versus database.
					bVerifyVsDB = true;

                    DCSMsg.Show(String.Format("Chip interface type {0} does not support biometric verification.", eChipIFType), MessageBoxIcon.Error);
					return "CANCEL";
				}
				else
				{
					DCSMsg.Show(String.Format("Chip interface type {0} does not support biometric verification.", eChipIFType));
					return "CANCEL";
				}
			}
			else bVerifyVsDB = true;

			if (!bVerifyVsDB)
			{
				///////////////////////////////////////////////////////
				// handle special case: not comparing to database    //
				// parse strBiometricFromDocument into separate bins //
				///////////////////////////////////////////////////////
				int posOffset = 0;
				int posStart;
				int posEnd;
				while (true)
				{
					posStart = strBiometricFromDocument.ToUpper().IndexOf("BIN=", posOffset);	// dont know why scanned pdf417 can come back lower case
					if (posStart < 0) break;
					posStart += 4;
					posEnd = strBiometricFromDocument.IndexOf(";", posStart + 1);
					if (posEnd < 0) break;
					arrayBINs.Add(strBiometricFromDocument.Substring(posStart, posEnd - posStart));
					posOffset = posEnd + 1;
				}
			}
			else
			{
				//////////////////////////////////////////////////////////////////////////////
				// handle special case: comparing to database                               //
				// read the first two subClass BINs out of the database into strBINs array. //
				//////////////////////////////////////////////////////////////////////////////
				int idx1 = -1;
				int idx2 = -1;
				if (m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_INNOVATRICS)
				{
					bRet = DCSDEV.DCSInnovatricsIF.DCSInnovatricsIF.Init(m_strDataRootDir);	// init if not already done
					if (bRet)
					{
						bRet = DCSDEV.DCSInnovatricsIF.DCSInnovatricsIF.GetFingerIndices(strImageID, out idx1, out idx2);
					}
				}
				int iInstancesFound = 0;
				int iMapped;
				for (int iMapIndex = 0, i = 0; i < 10; i++)
				{
					if (iSubClass != -1 && i != iSubClass) continue;
					if (iSubClass == -1 && this.m_bFingerMapping)
					{
						if (iInstancesFound >= m_nFingerprintInstances) break;
						if (iMapIndex >= this.m_strFingerMap.Length) break;
						iMapped = this.m_strFingerMap[iMapIndex++] - '0';
					}
					else iMapped = i;

					if (m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_INNOVATRICS)
					{
						if (idx1 >= 0) { arrayBINs.Add("IS01X" + idx1.ToString()); iInstancesFound++; }
						if (idx2 >= 0) { arrayBINs.Add("IS01X" + idx2.ToString()); }
					}
					else
					{
						string strError;
						string strLiskaBIN;
						string strMinutiaBIN;
						bRet = DCSDatabaseIF.ReadAllBinsForFinger(strImageID, iMapped, out strLiskaBIN, out strMinutiaBIN, out strError, this.m_iSelectedFingerBioType);
						if (!bRet) return strError;
						if (strLiskaBIN != null) arrayBINs.Add(strLiskaBIN);
						if (strMinutiaBIN != null) arrayBINs.Add(strMinutiaBIN);

						// loop over all 10 subClasses - until m_nFingerprintInstances subClasses with bins are returned
						if (strLiskaBIN != null || strMinutiaBIN != null) iInstancesFound++;
					}
				}
				if (iInstancesFound == 0) return "ERROR: Biometrics have not been generated";
			}
			if (arrayBINs.Count <= 0) return "ERROR";

			///////////////////////////////////////
			// Capture the Test (ie LIVE) finger //
			///////////////////////////////////////
			int iPrevSubClass = -1;
			int iTestSubClass = -1;
			bool bTestFingercaptured = false;

			string strCompareReturn = "ERROR";

			strFinger_Captured = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDatabaseIF.GetNameOfImage("_VERIFY", DCSDEV.DCSDatabaseIF.ImageClass.Fingerprint));

			for (int k = 0; k < 10; k++)
			{
				iTestSubClass = -1;
				// map finger as required
				if (this.m_bFingerMapping)
				{
					if (k >= m_strFingerMap.Length) break;
					iTestSubClass = m_strFingerMap[k] - '0';
				}
				else
				{
					if (k >= m_nFingerprintInstances) break;
					iTestSubClass = k;
				}
				// look for equivalent BIN
				int j;
				string strBIN;
				for (j = 0; j < arrayBINs.Count; j++)
				{
					strBIN = (string)arrayBINs[j];
					if (strBIN.Length < 6) continue;	// protect against erroneous entries
					// get sub class # (instance) from BIN Header = LS01X# or AS01X#
					if (strBIN[5] - '0' == iTestSubClass) break;
				}
				if (j >= arrayBINs.Count) continue;      // no corresponding BIN found

				if (iPrevSubClass != -1 && !bTestFingercaptured)
				{
					string strFingerName = (this.m_bFingerMapping ? (string)this.m_arrayFingerMapNames[iTestSubClass] : iTestSubClass.ToString());
					string strPrevFingerName = (this.m_bFingerMapping ? (string)this.m_arrayFingerMapNames[iPrevSubClass] : iPrevSubClass.ToString());
					if (bTestFingercaptured)
					{
						// when previous finger did not match, ask about trying this next finger
						if (DCSDEV.DCSMsg.ShowYN(String.Format("Do you want to try finger '{0}'?", strFingerName)) == DialogResult.No)
							return strCompareReturn;
						// yes = try alt finger
					}
					else
					{
						// when previous finger capture was cancelled, ask about trying next finger
						string msg;
						msg = String.Format("Finger '{0}' canceled. \r\nDo you want to try finger '{1}'?", strPrevFingerName, strFingerName);
						if (DCSDEV.DCSMsg.ShowYN(msg) == DialogResult.No) continue;
					}
				}
				iPrevSubClass = iTestSubClass;

                string strFingerTitle;
				if (this.m_bFingerMapping)
					strFingerTitle = (string)m_arrayFingerMapNames[iTestSubClass] + ": Image for verify";
				else
					strFingerTitle = "Fing" + iTestSubClass.ToString() + ": Image for verify";

			Capture_Finger_Loop:
                switch (DoFingerCaptureUtil(strFingerTitle, strFinger_Captured))	// 0= software did not finish; 1=user canceled; 2=OK
                {
                    case 0:
                    case 1:
                    default:
                        bTestFingercaptured = false;
                        continue;
                    case 2:
                        bTestFingercaptured = true;
                        ///////////////////////////////////////////////////////////////////////////////////
                        // Compare test finger (strFinger_Captured) to the database or scanned biometric //
                        ///////////////////////////////////////////////////////////////////////////////////
                        strCompareReturn = CompareTestFinger(strImageID, bVerifyVsDB, arrayBINs, strFinger_Captured, iTestSubClass);

                        System.IO.File.Delete(strFinger_Captured);	// cleanup

                        if (strCompareReturn.StartsWith("YES"))
                        {
                            DoDumpResultAndBIN("YES;" + strBiometricFromDocument);
                            return strCompareReturn;
                        }
                        else if (strCompareReturn.StartsWith("NO"))
                        {
                            string msg;
                            if (this.m_bFingerMapping)
                                msg = String.Format("ANSWER: {0}\n\nFinger {1} does not match. \r\nDo you want to try the same finger again?", strCompareReturn, (string)this.m_arrayFingerMapNames[iPrevSubClass]);
                            else
                                msg = String.Format("ANSWER: {0}\n\nFinger {1} does not match. \r\nDo you want to try the same finger again?", strCompareReturn, iTestSubClass.ToString());
                            if (DCSDEV.DCSMsg.ShowYN(msg) == DialogResult.No) continue;
                            else goto Capture_Finger_Loop;	// try same finger again
                        }
                        else if (strCompareReturn.StartsWith("ERROR"))
                        {
                            DoDumpResultAndBIN("ERROR;" + strBiometricFromDocument);
                            return strCompareReturn;
                        }
                        else
                        {
                            DoDumpResultAndBIN("CANCEL;" + strBiometricFromDocument);
                            return strCompareReturn;
                        }
                }
			}
			return "CANCEL";
		}

		// private utility used by DoVerifyFinger Vs ImageID
		private string CompareTestFinger(string strImageID, bool bVerifyVsDB, ArrayList arrayBINs, string strFinger_Captured, int iSubClass_Captured)
		{
			///////////////////////////////////////////////////////////////////////////////////
			// Compare test finger (strFinger_Captured) to the database or scanned biometric //
			///////////////////////////////////////////////////////////////////////////////////
			try
			{
				bool bUsingAlt;		// comparing finger against a BIN marked for a different finger.
				int iScore;
				string strScore;

				// compare captured finger to the available bins
				int iThreshold;
				int countYes = 0;
				int countYesAlt = 0;
				string strYesScores = "";
				string strYesScoresAlt = "";
				int countNo = 0;
				int countNoAlt = 0;
				string strNoScores = "";
				string strNoScoresAlt = "";
				int iArrayItemSubClass;
				foreach (string strBINTest in arrayBINs)
				{
					iArrayItemSubClass = (strBINTest[5] - '0');
					bUsingAlt = (iArrayItemSubClass != iSubClass_Captured);
#if LISKA_BIN
                    if (strBINTest.StartsWith("LS") || strBINTest.StartsWith("ls"))	// dont know why scanned pdf417 can come back lower case
					{
						iThreshold = m_iThresholdBIN2;
						iScore = BIN2_Match(strFinger_Captured, strBINTest.Substring(6)) * 10;
						// iScore = Liska.Biometrics.Bin.Match(strFinger_Captured, strBINTest.Substring(6)); // use BIN with stripped off BIN header
						strScore = iScore.ToString() + "L";
					}
					else
#endif
                    if (strBINTest.StartsWith("AS") || strBINTest.StartsWith("as"))	// dont know why scanned pdf417 can come back lower case
					{
						// generate BINs with the features of the test finger and compare the BINs
						// get the original BIN file name or create a temp file name even thou the BINs are in arrayBINs
						// because DCSVerifyFeatures wants to work fronm files.
						iThreshold = m_iThresholdMinutia;

						string strBiometricFile_DB;
						string strBiometricFile_Test = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDatabaseIF.GetNameOfImage("_VERIFY", DCSDatabaseIF.ImageClass.BINA));
						DCSExtractFeatures(strFinger_Captured, strBiometricFile_Test, iSubClass_Captured);

						if (bVerifyVsDB)
						{
							if (DCSDatabaseIF.IsOleDBType())
							{
								strBiometricFile_DB = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDatabaseIF.GetNameOfImage("_TEMP", DCSDatabaseIF.ImageClass.BINA));
								DCSDatabaseIF.GetStoredImageFile(strBiometricFile_DB, strImageID, DCSDEV.DCSDatabaseIF.ImageClass.BINA, iArrayItemSubClass);
							}
							else
							{
								strBiometricFile_DB = DCSDatabaseIF.GetFullnameOfImage(strImageID, DCSDatabaseIF.ImageClass.BINA, iArrayItemSubClass, false);
							}
						}
						else
						{
							// the BIN from the barcode/magstripe/chip is written to disk for the IBM AIS interface
							// syh note - AS verify only works from disk. this should be changed
							strBiometricFile_DB = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, DCSDatabaseIF.GetNameOfImage("_TEMP", DCSDatabaseIF.ImageClass.BINA));
							System.IO.StreamWriter stream = new System.IO.StreamWriter(strBiometricFile_DB);
							stream.Write(strBINTest);
							stream.Close();
						}

						iScore = DCSVerifyFeatures(strBiometricFile_DB, strBiometricFile_Test);
                        strScore = iScore.ToString() + "A";
                        System.IO.File.Delete(strBiometricFile_Test);       // cleanup temp file 
						if (!bVerifyVsDB || DCSDatabaseIF.IsOleDBType()) System.IO.File.Delete(strBiometricFile_DB);	// cleanup
					}
					else if (strBINTest.StartsWith("AG") || strBINTest.StartsWith("ag"))
					{
						// generate Agora features for test finger and compare features
						iThreshold = m_iThresholdMinutia;

                        // return -1, 0, or 1: if error, no match, or match
                        int iRet = DCSDEV.AgoraIF.AgoraCompareFingerToBIN(strFinger_Captured, iSubClass_Captured, strBINTest, out iScore);

						if (iRet == -1)
						{
							iThreshold = 0;
						}
                        else if (iRet == 0)		// different
						{
							iThreshold = iScore + 1;
						}
						else    // same
						{
							iThreshold = iScore - 1;
						}
						strScore = iScore.ToString() + "G";
					}
					else if (strBINTest.StartsWith("IS") || strBINTest.StartsWith("is"))	// dont know why scanned pdf417 can come back lower case
					{
						// (m_iSelectedFingerBioType == DCSDatabaseIF.DCSBioTypes.BIOTYPE_INNOVATRICS)
						// compare test finger with the 
						iThreshold = 1000;
						strScore = "0";
						iScore = -1;
						if (bVerifyVsDB)
						{
							// Innovatrivatics is already init'ed by caller.
							iScore = DCSDEV.DCSInnovatricsIF.DCSInnovatricsIF.VerifyFinger(strImageID, iArrayItemSubClass, strFinger_Captured);
							if (iScore >= 0)
							{
								strScore = iScore.ToString();
							}
						}
						else
						{
							iScore = -1;
						}
					}
					else
					{
						return "ERROR: DoVerifyFinger Vs ImageID->CompareTestFinger E001";
					}
					if (iScore < 0)
					{
						return "ERROR: DoVerifyFinger Vs ImageID->CompareTestFinger E002";
					}
					else if (iScore > iThreshold)
					{
						if (bUsingAlt)
						{
							countYesAlt++;
							strYesScoresAlt += " " + strScore;
						}
						else
						{
							countYes++;
							strYesScores += " " + strScore;
						}
					}
					else
					{
						if (bUsingAlt)
						{
							countNoAlt++;
							strNoScoresAlt += " " + strScore;
						}
						else
						{
							countNo++;
							strNoScores += " " + strScore;
						}
					}
				}

				// after testing all BINS check for various errors
				if (countYes + countNo + countYesAlt + countNoAlt != arrayBINs.Count)
				{
					return "ERROR: DoVerifyFinger Vs ImageID->CompareTestFinger E003";
				}
				if (countYes + countNo == 0)
				{
					return "ERROR: DoVerifyFinger Vs ImageID->CompareTestFinger E004";
				}

				// No Error. Determine overall score to report
				if (countYes > 0 && countNo == 0 && countYesAlt == 0)
				{
					return "YES" + strYesScores;
				}
				else if (countYes > 0 && countNo == 0 && countYesAlt != 0)
				{
					return "YES" + strYesScores + " + Alt " + strYesScoresAlt;
				}
				else if (countYes > 0 && countNo > 0 && countYesAlt == 0)
				{
					if (m_bLogBinErrors) this.LogMatchErrorData(iSubClass_Captured, strImageID, strFinger_Captured);
					return "YES" + strYesScores + " XX";
				}
				else if (countYes > 0 && countNo > 0 && countYesAlt != 0)
				{
					if (m_bLogBinErrors) this.LogMatchErrorData(iSubClass_Captured, strImageID, strFinger_Captured);
					return "YES" + strYesScores + " XX Alt " + strYesScoresAlt;
				}

				else if (countYes == 0 && countYesAlt == 0)
				{
					return "NO" + strNoScores;
				}
				else if (countYes == 0 && countYesAlt >= 0 && countNoAlt == 0)
				{
					return "YES" + strYesScoresAlt + " Alternate Finger Matches";
				}
				else if (countYes == 0 && countYesAlt >= 0 && countNoAlt != 0)
				{
					if (m_bLogBinErrors) this.LogMatchErrorData(iSubClass_Captured, strImageID, strFinger_Captured);
					return "YES" + strYesScoresAlt + " Alternate Finger Matches XX";
				}
				else
				{
					return "ERROR: DoVerifyFinger Vs ImageID->CompareTestFinger E005";
				}
			}
			catch (Exception ex)
			{
				DCSMsg.Show("ERROR: DoVerifyFinger Vs ImageID->CompareTestFinger", ex);
				return "ERROR: DoVerifyFinger Vs ImageID->CompareTestFinger E006";
			}
		}

		// save result to a file so it can be accessed by other demo projects like the trucking demo
		private void DoDumpResultAndBIN(string strAnsPlusScannedData)
		{
			string strFileName = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, "_MatchResult.Txt");
			System.IO.StreamWriter stream = new System.IO.StreamWriter(strFileName);
			stream.Write(strAnsPlusScannedData);
			stream.Close();
		}

		private bool DoScanMagstripe(out string strBiometricFromDocument)
		{
			strBiometricFromDocument = "";
			DCSDEV.DCSMsg.Show("Magnetic Stripe Scan and Decode is not avaliable");	// SYH TODO
			return false;
		}

		// read smart chip and put decoded text in strFromDocument
		// then write decoded text to file strDestination
		private int DoScanSmartChip(string strDestination, out string strFromDocument, bool bIfForVerify)
		{
			m_strLastChipID = null;
			strFromDocument = "";
			DCSDEV.SmartChipIO dlg = new DCSDEV.SmartChipIO(
				bIfForVerify ? SmartChipIO.ChipIOMode.READ_VERIFY : SmartChipIO.ChipIOMode.READ_DATA,
				strDestination,
				true);			// true=stand-alone device; false = in printer
			dlg.ShowDialog(this);

			int iRet;
			if (dlg.GetStatus == SmartChipIO.ChipStatus.OK)
			{
				iRet = 1;
				m_strLastChipID = dlg.GetChipID;
				strFromDocument = dlg.TextReadFromChip;
			}
			else if (dlg.GetStatus == SmartChipIO.ChipStatus.CANCEL) iRet = 0;
			else iRet = -1;

			dlg.Dispose();
			return iRet;
		}

		private bool DoScanBarcode(out string strBiometricFromDocument)
		{
			strBiometricFromDocument = "";
			DCSDEV.ScanBarcode dlg = new ScanBarcode(this.comboBoxBarcodeScannerPort.Text);
			dlg.ShowDialog(this.Parent);

			if (dlg.DialogResult == System.Windows.Forms.DialogResult.Cancel) return false;

			if (dlg.ScannedText == null || dlg.ScannedText == "") return false;

			strBiometricFromDocument = dlg.ScannedText;
			return true;
		}


		/// <summary>
		/// Initialize Dynamic Load Management of Fingerprints to the currently selected Fingerprint device
		/// </summary>
		/// <returns>Fingerprint capture class</returns>
		/// 


		private void ExtractCaptureMgtParameters()
		{
			m_bPortrait = this.checkPortrait.Checked;
			m_bSignature = this.checkSignature.Checked;
			m_bFingerprint = this.checkFingerprint.Checked;
			m_b10Print = this.check10Print.Checked;
			m_bCerts = this.checkCerts.Checked;

			m_bPortraitCapture = this.checkPortraitCapture.Checked;
			m_bSignatureCapture = this.checkSignatureCapture.Checked;
			m_bFingerprintCapture = this.checkFingerprintCapture.Checked;
			m_bCertsCapture = this.checkCertsCapture.Checked;

			m_bPortraitScannerCapture = this.checkPortraitScanner.Checked;
			m_bSignatureScannerCapture = this.checkSignatureScanner.Checked;
			m_bFingerprintScannerCapture = this.checkFingerprintScanner.Checked;

			m_bPortraitDisplay = this.checkPortraitDisplay.Checked;
			m_bSignatureDisplay = this.checkSignatureDisplay.Checked;
			m_bFingerprintDisplay = this.checkFingerprintDisplay.Checked;
			m_bCertsDisplay = this.checkCertsDisplay.Checked;

			m_nPortraitInstances = (int)this.numericUpDownPortraitInstances.Value;
			m_nSignatureInstances = (int)this.numericUpDownSignatureInstances.Value;
			m_nFingerprintInstances = (int)this.numericUpDownFingerInstances.Value;
			m_n10PrintInstances = (int)this.numericUpDown10PrintInstances.Value;

			m_strImageDBType = this.textBoxImageDBType.Text;
			m_strDataRootDir = this.tbDataRootDir.Text;

			m_strPortraitDevice = this.cbPortraitDevice.Text;
			m_strSignatureDevice = this.cbSignatureDevice.Text;
			m_strFingerprintDevice = this.cbFingerprintDevice.Text;
			m_str10PrintDevice = this.cb10PrintDevice.Text;

			m_bFingerMapping = this.checkBoxFingerMapping.Checked;
			m_bDontSaveFingerImage = this.checkDontSaveFingerImage.Checked;
			//xx m_bMultiFingerDevice = this.checkBoxMultiFingerDevice.Checked;

			m_bLogBinErrors = this.checkLogBINErrors.Checked;
			m_bQCVerify = this.checkBoxQCVerify.Checked;
			m_bGenerateFingerBiometric = this.checkGenFingerBIN.Checked;
			m_iSelectedFingerBioType = (DCSDatabaseIF.DCSBioTypes)this.listBoxFingerBioTypes.SelectedIndex;
			m_iThresholdBIN2 = (int)this.numericUpDownBIN2Threshold.Value;
			m_iThresholdMinutia = (int)this.numericUpDownMinutiaThreshold.Value;

			m_nCaptureOrder = this.comboBoxCaptureOrder.SelectedIndex;
			m_bEnableImageHistory = this.checkBoxEnableImageHistory.Checked;
			m_iVerifyMode = this.cbVerifyHow.SelectedIndex;
		}

		private bool ValidateQueryAndFix(bool bQuiet, bool bCheckOnly)
		{
			bool bRet = false;

			//////////////////////////////////////////////////////////////////////////////////
			// Checking device settings
			//////////////////////////////////////////////////////////////////////////////////
			if (m_bCerts)
			{
				// if (m_strCertsDevice == null || m_strCertsDevice == "")
				// {
				// 	if (!bQuiet) DCSMsg.Show("A device for certificate scanning needs to be selected.", System.Windows.Forms.MessageBoxIcon.Error);
				// 	this.cbCertsScanner.Focus();
				// 	return false;
				// }
			}
            if (m_bFingerprint && m_bFingerprintCapture && !ValidateFingerDevice(bQuiet))
            {
                this.cbFingerprintDevice.Focus();
                return false;
            }
            if (m_bFingerprint && m_b10Print && !Validate10PrintDevice(bQuiet))
            {
                this.cb10PrintDevice.Focus();
                return false;
            }
            if (m_bPortrait && m_bPortraitCapture && !ValidatePortraitDevice(bQuiet))
            {
                this.cbPortraitDevice.Focus();
                return false;
            }
			if (m_bSignature && m_bSignatureCapture && !ValidateSignatureDevice(bQuiet))
            {
                this.cbSignatureDevice.Focus();
                return false;
            }
			//////////////////////////////////////////////////////////////////////////////////
			// Fixing Paths for all image classes 
			//////////////////////////////////////////////////////////////////////////////////
			bRet = ValidateQueryAndFixPaths(bQuiet, bCheckOnly);
			return bRet;
		}
        private bool ValidateFingerDevice(bool bQuiet)
        {
            if (m_strFingerprintDevice == null || m_strFingerprintDevice == "")
            {
                if (!bQuiet) DCSMsg.Show("A device for fingerprint capture needs to be selected.", System.Windows.Forms.MessageBoxIcon.Error);
                return false;
            }
            if (!this.cbFingerprintDevice.Items.Contains(m_strFingerprintDevice))
            {
                if (!bQuiet) DCSMsg.Show(String.Format("The selected device for fingerprint capture ('{0}') is not available.", m_strFingerprintDevice), System.Windows.Forms.MessageBoxIcon.Error);
                return false;
            }
            return true;
        }
        private bool Validate10PrintDevice(bool bQuiet)
        {
            if (m_str10PrintDevice == null || m_str10PrintDevice == "")
            {
                if (!bQuiet) DCSMsg.Show("A device for 10-print capture needs to be selected.", System.Windows.Forms.MessageBoxIcon.Error);
                return false;
            }
            return true;
        }
        private bool ValidatePortraitDevice(bool bQuiet)
        {
            if (m_strPortraitDevice == null || m_strPortraitDevice == "")
            {
                if (!bQuiet) DCSMsg.Show("A device for portrait capture needs to be selected.", System.Windows.Forms.MessageBoxIcon.Error);
                return false;
            }
            if (!this.cbPortraitDevice.Items.Contains(m_strPortraitDevice))
            {
                if (!bQuiet) DCSMsg.Show(String.Format("The selected device for portrait capture ('{0}') is not available.", m_strPortraitDevice), System.Windows.Forms.MessageBoxIcon.Error);
                return false;
            }
            return true;
        }
        private bool ValidateSignatureDevice(bool bQuiet)
        {
            if (m_strSignatureDevice == null || m_strSignatureDevice == "")
            {
                if (!bQuiet) DCSMsg.Show("A device for signature capture needs to be selected.", System.Windows.Forms.MessageBoxIcon.Error);
                return false;
            }
            if (!this.cbSignatureDevice.Items.Contains(m_strSignatureDevice))
            {
                if (!bQuiet) DCSMsg.Show(String.Format("The selected device for signature capture ('{0}') is not available.", m_strSignatureDevice), System.Windows.Forms.MessageBoxIcon.Error);
                return false;
            }
            return true;
        }
        private bool ValidateQueryAndFixPaths(bool bQuiet, bool bCheckOnly)
		{
			bool bRet;
			string strPath = null;
			if (m_bCerts)
			{
				strPath = m_strCertsPath;
				bRet = DCSDEV.DCSServerStuff.ValidateDirectoryPath("Certificate", strPath, bQuiet, bCheckOnly);
				if (!bRet) return false;
			}
			if (m_bPortrait)
			{
				strPath = m_strPortraitPath;
				int instances = m_nPortraitInstances;	// instances goes from 1 to 10 - but path name uses blank and 1 to 9
				if (instances > 10 || instances <= 0) instances = 1;
				bRet = DCSDEV.DCSServerStuff.ValidateDirectoryPath("Portrait", strPath, bQuiet, bCheckOnly);
				if (!bRet) return false;
				for (int i = 1; i < instances; i++)
				{
					strPath = m_strPortraitPath + (i).ToString();
					bRet = DCSDEV.DCSServerStuff.ValidateDirectoryPath("Portrait instance " + (i + 1).ToString(), strPath, bQuiet, bCheckOnly);
					if (!bRet) return false;
				}
			}
			if (m_bSignature)
			{
				strPath = m_strSignaturePath;
				int instances = m_nSignatureInstances;	// goes from 1 to 10 - but path name use blank and 1 to 9
				if (instances > 10 || instances <= 0) instances = 1;
				bRet = DCSDEV.DCSServerStuff.ValidateDirectoryPath("Signature", strPath, bQuiet, bCheckOnly);
				if (!bRet) return false;
				for (int i = 1; i < instances; i++)
				{
					strPath = m_strSignaturePath + (i).ToString();
					bRet = DCSDEV.DCSServerStuff.ValidateDirectoryPath("Signature instance " + (i + 1).ToString(), strPath, bQuiet, bCheckOnly);
					if (!bRet) return false;
				}
			}
			if (m_bFingerprint)
			{
				bool bMappingMode = m_bFingerMapping;
				int instances = m_nFingerprintInstances;	// goes from 1 to 10 - but path name use blank and 1 to 9
				if (m_b10Print)
				{
					bMappingMode = false;
					if (instances < m_n10PrintInstances) instances = m_n10PrintInstances;
				}

				if (!bMappingMode)
				{
					strPath = m_strFingerprintPath;
					if (instances > 10 || instances <= 0) instances = 1;
					bRet = DCSDEV.DCSServerStuff.ValidateDirectoryPath("Fingerprint", strPath, bQuiet, bCheckOnly);
					if (!bRet) return false;
					for (int i = 1; i < instances; i++)
					{
						strPath = m_strFingerprintPath + (i).ToString();
						bRet = DCSDEV.DCSServerStuff.ValidateDirectoryPath("Fingerprint instance " + (i).ToString(), strPath, bQuiet, bCheckOnly);
						if (!bRet) return false;
					}
				}
				else
				{
					int iMapped;
					for (int i = 0; i < this.m_strFingerMap.Length; i++)
					{
						iMapped = this.m_strFingerMap[i] - '0';
						if (iMapped == 0) strPath = m_strFingerprintPath;
						else strPath = m_strFingerprintPath + (iMapped).ToString();
						bRet = DCSDEV.DCSServerStuff.ValidateDirectoryPath("Fingerprint instance " + (iMapped).ToString(), strPath, bQuiet, bCheckOnly);
						if (!bRet) return false;
					}
				}
			}
			return true;
		}

		/// <summary>
		/// This routine should be called when the control is instantiated - so m_ps is assigned.
		/// </summary>
		public void ReadAndInstallCaptureMgtParameters()
		{
			m_ps = new DCSDEV.ParameterStore("DCSSDK_Mgt");
			this.tbMachineIndexFile.Text = m_ps.m_strMachineIndexFile;
			this.tbInstallDirectory.Text = m_ps.m_strDCSInstallDirectory;
			this.tbMachineConfigFile.Text = m_ps.m_strMachineConfigFile;

			if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.CertsHandling))
			{
				m_bCerts = this.checkCerts.Checked = m_ps.GetBoolParameter("CertsEnabled", false);
				m_bCertsCapture = this.checkCertsCapture.Checked = m_ps.GetBoolParameter("CertsCaptureEnabled", true);
				m_bCertsDisplay = this.checkCertsDisplay.Checked = m_ps.GetBoolParameter("CertsDisplayEnabled", true);
			}
			else
			{
				m_bCertsDisplay = m_bCertsCapture = m_bCerts = this.checkCerts.Enabled = this.checkCerts.Checked = false;
			}

			m_bPortrait = this.checkPortrait.Checked = m_ps.GetBoolParameter("PortraitEnabled", true);
			m_bSignature = this.checkSignature.Checked = m_ps.GetBoolParameter("SignatureEnabled", false);
			m_bFingerprint = this.checkFingerprint.Checked = this.checkFingerprintBase.Checked = m_ps.GetBoolParameter("FingerprintEnabled", false);
			m_b10Print = this.check10Print.Checked = m_ps.GetBoolParameter("10PrintEnabled", false);

			if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.PortraitCapture))
				m_bPortraitCapture = this.checkPortraitCapture.Checked = m_ps.GetBoolParameter("PortraitCaptureEnabled", true);
			if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.SignatureCapture))
				m_bSignatureCapture = this.checkSignatureCapture.Checked = m_ps.GetBoolParameter("SignatureCaptureEnabled", false);
			if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.FingerprintCapture))
				m_bFingerprintCapture = this.checkFingerprintCapture.Checked = m_ps.GetBoolParameter("FingerprintCaptureEnabled", false);
			else
				m_bFingerprintCapture = this.checkFingerprintCapture.Checked = false;

			if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.ScannerMgt))
			{
				m_bPortraitScannerCapture = this.checkPortraitScanner.Checked = m_ps.GetBoolParameter("PortraitScannerEnabled", false);
				m_bSignatureScannerCapture = this.checkSignatureScanner.Checked = m_ps.GetBoolParameter("SignatureScannerEnabled", false);
				m_bFingerprintScannerCapture = this.checkFingerprintScanner.Checked = m_ps.GetBoolParameter("FingerprintScannerEnabled", false);
			}
			if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.Display))
			{
				m_bPortraitDisplay = this.checkPortraitDisplay.Checked = m_ps.GetBoolParameter("PortraitDisplayEnabled", true);
				m_bSignatureDisplay = this.checkSignatureDisplay.Checked = m_ps.GetBoolParameter("SignatureDisplayEnabled", false);
				if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.FingerprintCapture))
					m_bFingerprintDisplay = this.checkFingerprintDisplay.Checked = m_ps.GetBoolParameter("FingerprintDisplayEnabled", false);
			}

			if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.Instances))
			{
				this.numericUpDownPortraitInstances.Value = m_nPortraitInstances = m_ps.GetIntParameter("PortraitInstances", 1);
				this.numericUpDownSignatureInstances.Value = m_nSignatureInstances = m_ps.GetIntParameter("SignatureInstances", 1);
				this.numericUpDownFingerInstances.Value = m_nFingerprintInstances = m_ps.GetIntParameter("FingerprintInstances", 1);
				this.numericUpDown10PrintInstances.Value = m_n10PrintInstances = m_ps.GetIntParameter("10PrintInstances", 10);
			}
            m_strImageDBType = m_ps.GetStringParameter("DCSSDK_ImageDB", "ImageDBType", "FILES");
            if ((m_strImageDBType != "FILES") && (m_strImageDBType != "ACCESS") && (m_strImageDBType != "SQL") && (m_strImageDBType != "ORACLE")) m_strImageDBType = "FILES";
			m_strDataRootDir = m_ps.GetStringParameter("DataRootDir", m_ps.m_strDCSInstallDirectory);
			// with no network permissions, data root dir must be equal to or under install directory
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.ImagesInDatabase, false))
			{
				if (!m_strDataRootDir.ToUpper().StartsWith(m_ps.m_strDCSInstallDirectory.ToUpper())) m_strDataRootDir = m_ps.m_strDCSInstallDirectory;
                if (m_strImageDBType != "FILES") DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.ImagesInDatabase, true);
				m_strImageDBType = "FILES";
			}
			this.textBoxImageDBType.Text = m_strImageDBType;
			this.DataRootDir = m_strDataRootDir;	// this operation sets all image class paths

			m_strPortraitDevice = this.cbPortraitDevice.Text = m_ps.GetStringParameter("PortraitDevice", "");
			m_strSignatureDevice = this.cbSignatureDevice.Text = m_ps.GetStringParameter("SignatureDevice", "");
			m_strFingerprintDevice = this.cbFingerprintDevice.Text = m_ps.GetStringParameter("FingerprintDevice", "");
			m_str10PrintDevice = this.cb10PrintDevice.Text = m_ps.GetStringParameter("10PrintDevice", "");

			DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("DCSScanner");
			string strSelectedDevice = ps.GetStringParameter("DCSScannerDeviceID", m_scannerIF.m_strSelectedID);
			string strSelectedName = ps.GetStringParameter("DCSScannerDeviceName", m_scannerIF.m_strSelectedName);
			m_scannerIF.m_strSelectedID = strSelectedDevice;
			m_scannerIF.m_strSelectedName = strSelectedName;
			this.tbScannerDeviceName.Text = strSelectedName;

			m_bFingerMapping = this.checkBoxFingerMapping.Checked = this.checkBoxFingerMapping10Print.Checked = m_ps.GetBoolParameter("FingerMapping", true);
			m_strFingerMap = m_ps.GetStringParameter("FingerMapString", m_strFingerMap);
			if (m_strFingerMap.Length < 1) m_bFingerMapping = false;
			m_arrayFingerMapNames = new ArrayList();
			string[] strDefaultNames = {
										   "Right Index",
										   "Right Middle",
										   "Right Ring",
										   "Right Pinky",
										   "Right Thumb",
										   "Left Index",
										   "Left Middle",
										   "Left Ring",
										   "Left Pinky",
										   "Left Thumb"   };
			string strIn;
			for (int i = 0; i < 10; i++)
			{
				strIn = m_ps.GetStringParameter("FingerName" + i.ToString(), strDefaultNames[i]);
				m_arrayFingerMapNames.Add(strIn);
			}

			m_bDontSaveFingerImage = this.checkDontSaveFingerImage.Checked = m_ps.GetBoolParameter("DontSaveFingerImage", false);
			m_bLogBinErrors = this.checkLogBINErrors.Checked = m_ps.GetBoolParameter("LogBINErrors", false);
			m_bQCVerify = this.checkBoxQCVerify.Checked = m_ps.GetBoolParameter("QCVerify", true);
			m_bGenerateFingerBiometric = this.checkGenFingerBIN.Checked = m_ps.GetBoolParameter("GenerateFingerBiometric", false);
			//xx m_bMultiFingerDevice = this.checkBoxMultiFingerDevice.Checked = m_ps.GetBoolParameter("MultiFingerDevice", false);

			this.listBoxFingerBioTypes.SelectedIndex = m_ps.GetIntParameter("SelectedFingerBioType", (int)DCSDatabaseIF.DCSBioTypes.BIOTYPE_AIS_MINUTIA);
			m_iSelectedFingerBioType = (DCSDatabaseIF.DCSBioTypes)this.listBoxFingerBioTypes.SelectedIndex;
			m_iThresholdBIN2 = m_ps.GetIntParameter("BIN2FingerMatchThreshold", m_iThresholdBIN2);
			m_iThresholdMinutia = m_ps.GetIntParameter("MinutiaFingerMatchThreshold", m_iThresholdMinutia);
			this.numericUpDownBIN2Threshold.Value = m_iThresholdBIN2;
			this.numericUpDownMinutiaThreshold.Value = m_iThresholdMinutia;
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.GenerateFingerBiometrics))
				m_bGenerateFingerBiometric = this.checkGenFingerBIN.Checked = false;

			m_nCaptureOrder = m_ps.GetIntParameter("CaptureOrder", m_nCaptureOrder);
			this.comboBoxCaptureOrder.SelectedIndex = m_nCaptureOrder;

			m_bEnableImageHistory = false;
			if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.ImageHistory, false))
				m_bEnableImageHistory = m_ps.GetBoolParameter("EnableImageHistory", m_bEnableImageHistory);
			this.checkBoxEnableImageHistory.Checked = m_bEnableImageHistory;

			this.comboBoxBarcodeScannerPort.Text = m_ps.GetStringParameter("BarcodeScannerPort", "COM3");
			this.comboBoxChipEncoderPort.Text = m_ps.GetStringParameter("ChipEncoderPort", "COM4");		// "DCSSDK_Mgt"

			// database read / write interface
			DCSDatabaseIF.Reinit();
		}

		private void WriteCaptureMgtParameters()
		{
			m_ps.WriteBoolParameter("CertsEnabled", m_bCerts);
			m_ps.WriteBoolParameter("PortraitEnabled", m_bPortrait);
			m_ps.WriteBoolParameter("SignatureEnabled", m_bSignature);
			m_ps.WriteBoolParameter("FingerprintEnabled", m_bFingerprint);
			m_ps.WriteBoolParameter("10PrintEnabled", m_b10Print);

			m_ps.WriteBoolParameter("PortraitCaptureEnabled", m_bPortraitCapture);
			m_ps.WriteBoolParameter("SignatureCaptureEnabled", m_bSignatureCapture);
			m_ps.WriteBoolParameter("FingerprintCaptureEnabled", m_bFingerprintCapture);
			m_ps.WriteBoolParameter("CertsCaptureEnabled", m_bCertsCapture);

			m_ps.WriteBoolParameter("PortraitScannerEnabled", m_bPortraitScannerCapture);
			m_ps.WriteBoolParameter("SignatureScannerEnabled", m_bSignatureScannerCapture);
			m_ps.WriteBoolParameter("FingerprintScannerEnabled", m_bFingerprintScannerCapture);

			m_ps.WriteBoolParameter("PortraitDisplayEnabled", m_bPortraitDisplay);
			m_ps.WriteBoolParameter("SignatureDisplayEnabled", m_bSignatureDisplay);
			m_ps.WriteBoolParameter("FingerprintDisplayEnabled", m_bFingerprintDisplay);
			m_ps.WriteBoolParameter("CertsDisplayEnabled", m_bCertsDisplay);

			m_ps.WriteIntParameter("PortraitInstances", m_nPortraitInstances);
			m_ps.WriteIntParameter("SignatureInstances", m_nSignatureInstances);
			m_ps.WriteIntParameter("FingerprintInstances", m_nFingerprintInstances);
			m_ps.WriteIntParameter("10PrintInstances", m_n10PrintInstances);

            m_ps.WriteStringParameter("DCSSDK_ImageDB", "ImageDBType", m_strImageDBType);
			m_ps.WriteStringParameter("DataRootDir", m_strDataRootDir);

			m_ps.WriteStringParameter("PortraitDevice", m_strPortraitDevice);
			m_ps.WriteStringParameter("SignatureDevice", m_strSignatureDevice);
			m_ps.WriteStringParameter("FingerprintDevice", m_strFingerprintDevice);
			m_ps.WriteStringParameter("10PrintDevice", m_str10PrintDevice);

			DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("DCSScanner");
			ps.WriteStringParameter("DCSScannerDeviceID", m_scannerIF.m_strSelectedID);
			ps.WriteStringParameter("DCSScannerDeviceName", m_scannerIF.m_strSelectedName);

			m_ps.WriteBoolParameter("FingerMapping", m_bFingerMapping);
			m_ps.WriteStringParameter("FingerMapString", m_strFingerMap);
			for (int i = 0; i < 10; i++)
				m_ps.WriteStringParameter("FingerName" + i.ToString(), (string)m_arrayFingerMapNames[i]);
			m_ps.WriteBoolParameter("DontSaveFingerImage", m_bDontSaveFingerImage);

			m_ps.WriteBoolParameter("LogBINErrors", m_bLogBinErrors);
			m_ps.WriteBoolParameter("QCVerify", m_bQCVerify);
			m_ps.WriteBoolParameter("GenerateFingerBiometric", m_bGenerateFingerBiometric);
			m_ps.WriteIntParameter("SelectedFingerBioType", (int)m_iSelectedFingerBioType);
			m_ps.WriteIntParameter("BIN2FingerMatchThreshold", m_iThresholdBIN2);
			m_ps.WriteIntParameter("MinutiaFingerMatchThreshold", m_iThresholdMinutia);

			m_ps.WriteIntParameter("CaptureOrder", m_nCaptureOrder);
			m_ps.WriteBoolParameter("EnableImageHistory", m_bEnableImageHistory);

			m_ps.WriteStringParameter("BarcodeScannerPort", this.comboBoxBarcodeScannerPort.Text);
			m_ps.WriteStringParameter("ChipEncoderPort", this.comboBoxChipEncoderPort.Text);
		}

		private Rectangle GetImgClassDisplayRectangle(DCSDatabaseIF.ImageClass imageClass)
		{
			DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore(imageClass.ToString() + "_Display");
			Rectangle rectScreen = System.Windows.Forms.Screen.PrimaryScreen.Bounds;
			// compute default display rectangle
			Rectangle rectDefault;
			if (imageClass == DCSDatabaseIF.ImageClass.Certificate)
				rectDefault = new Rectangle(Point.Empty, DCSMath.Half(rectScreen.Size));
			else
				rectDefault = new Rectangle(0, 0, 300, 400);
			Rectangle rectDisplay = ps.GetRectParameter("DisplayRect", rectDefault);

			if (rectDisplay.X < 0 || rectDisplay.Right > rectScreen.Width) rectDisplay.X = 0;
			if (rectDisplay.Y < 0 || rectDisplay.Bottom > rectScreen.Height) rectDisplay.Y = 0;
			return rectDisplay;
		}
		private void SetImgClassDisplayRectangle(Rectangle rect, DCSDEV.DCSDatabaseIF.ImageClass imageClass)
		{
			DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore(imageClass.ToString() + "_Display");
			ps.WriteRectParameter("DisplayRect", rect);
		}

		private void PopulateDeviceCombos()
		{
			this.cbPortraitDevice.Items.Clear();
			foreach (string devName in m_listTypeNames_Portrait)
				this.cbPortraitDevice.Items.Add(devName);

			this.cbSignatureDevice.Items.Clear();
			foreach (string devName in m_listTypeNames_Signature)
				this.cbSignatureDevice.Items.Add(devName);

			this.cbFingerprintDevice.Items.Clear();
			foreach (string devName in m_listTypeNames_Fingerprint)
				this.cbFingerprintDevice.Items.Add(devName);
		}

		private void LogLiskaEnrollErrorData(string strFinger1, string strFinger2)
		{
			try
			{
				int i = m_ps.GetIntParameter("Log", "LastEnrollError", 0);
				string strErrorSubdir;
				string strErrorDir = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, "Enroll_Errors");
				while (true)
				{
					strErrorSubdir = System.IO.Path.Combine(strErrorDir, "Error" + i.ToString());
					if (System.IO.Directory.Exists(strErrorSubdir)) i++;
					else break;
				}
				System.IO.Directory.CreateDirectory(strErrorSubdir);
				System.IO.File.Copy(strFinger1, System.IO.Path.Combine(strErrorSubdir, "Finger1.bmp"));
				System.IO.File.Copy(strFinger2, System.IO.Path.Combine(strErrorSubdir, "Finger2.bmp"));
				i++;
				m_ps.WriteIntParameter("Log", "LastEnrollError", i);
			}
			catch
			{
			}
		}
		private void LogMatchErrorData(int iSubClass, string strImageID, string strFingerPath)
		{
			try
			{
				string strError;
				string strLiskaBIN;
				string strMinutiaBIN;
				bool bRet = DCSDatabaseIF.ReadAllBinsForFinger(strImageID, iSubClass, out strLiskaBIN, out strMinutiaBIN, out strError, this.m_iSelectedFingerBioType);
				if (!bRet) return;

				int i = m_ps.GetIntParameter("Log", "LastMatchError", 0);
				string strErrorSubdir;
				string strErrorDir = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, "Match_Errors");
				while (true)
				{
					strErrorSubdir = System.IO.Path.Combine(strErrorDir, "Error" + i.ToString());
					if (System.IO.Directory.Exists(strErrorSubdir)) i++;
					else break;
				}
				System.IO.Directory.CreateDirectory(strErrorSubdir);
				System.IO.File.Copy(strFingerPath, System.IO.Path.Combine(strErrorSubdir, "Finger.bmp"));

				System.IO.StreamWriter stream;
#if LISKA_BIN
                stream = new System.IO.StreamWriter(System.IO.Path.Combine(strErrorSubdir, "Match.BIN2"));
				stream.Write(strLiskaBIN);
				stream.Close();
#endif
				if (strMinutiaBIN.StartsWith("AG"))
					stream = new System.IO.StreamWriter(System.IO.Path.Combine(strErrorSubdir, "Match.BING"));
				else
					stream = new System.IO.StreamWriter(System.IO.Path.Combine(strErrorSubdir, "Match.BINA"));
				stream.Write(strMinutiaBIN);
				stream.Close();
				i++;
				m_ps.WriteIntParameter("Log", "LastMatchError", i);
			}
			catch
			{
			}
		}
#if LISKA_BIN
		private string BIN2_FromFingerprints(string finger1, string finger2)
		{
			FileStream fs = new FileStream(finger1, FileMode.Open, FileAccess.Read);
			BinaryReader r = new BinaryReader(fs);
			int len = (int)fs.Length;
			byte[] buff = new byte[200000];
			r.Read(buff, 0, len);

			FileStream fs2 = new FileStream(finger1, FileMode.Open, FileAccess.Read);
			BinaryReader r2 = new BinaryReader(fs2);
			int len2 = (int)fs2.Length;
			byte[] buff2 = new byte[200000];
			r2.Read(buff2, 0, len2);

			string str;
			try
			{
				str = Bin2Enroll(len, buff, len2, buff2);
			}
			catch (Exception ex)
			{
				str = "ERROR: " + ex.Message;
			}

			r.Close();
			fs.Close();
			r2.Close();
			fs2.Close();

			return str;
		}
#endif
#if LISKA_BIN
        private int BIN2_Match(string finger, string bin)
		{
			FileStream fs = new FileStream(finger, FileMode.Open, FileAccess.Read);
			BinaryReader r = new BinaryReader(fs);
			int len = (int)fs.Length;
			byte[] buff = new byte[200000];
			r.Read(buff, 0, len);

			int iscore = Bin2Match(len, buff, bin.Length, bin);

			r.Close();
			fs.Close();
			return iscore;
		}
#endif
		#endregion // Private Utilities

		#region message handlers

		private void buttonAbout_Click(object sender, System.EventArgs e)
		{
			DCSSDK.CaptureMgt.AboutCaptureMgt dlg = new DCSSDK.CaptureMgt.AboutCaptureMgt();
			dlg.ShowDialog(this);
		}

		private void buttonCancel_Click(object sender, System.EventArgs e)
		{
			this.CloseDisplays();
			ReadAndInstallCaptureMgtParameters();			// undo changes
			bool bRet = ValidateQueryAndFix(true, true);	// quiet is true, check-only is false
			if (!bRet)
			{
				DCSMsg.Show(
					"Some of the original configuration parameters are invalid." + Environment.NewLine +
					"You will need to return to this dialog to fix these problems.", System.Windows.Forms.MessageBoxIcon.Error);
			}
			this.Hide();
		}

		// CAPTURE
		private void buttonTestCertsCapture_Click(object sender, EventArgs e)
		{
			this.CloseDisplays();
			this.ExtractCaptureMgtParameters();
			this.WriteCaptureMgtParameters();

			string id = this.textBoxTestCertsID.Text;
			int instance = (int)this.numericUpDownCertsInstance.Value;
			m_bCaptureStatus = this.DoCertsScan(id, id, instance);
		}

		private void buttonTestPortraitCapture_Click(object sender, System.EventArgs e)
		{
			this.CloseDisplays();
			this.ExtractCaptureMgtParameters();
			this.WriteCaptureMgtParameters();
			this.m_strPhotoName = this.tbTestPhotoID.Text;
			int iSubClass = -1;
			m_bCaptureStatus = DoPortraitCapture(iSubClass, true);
		}
		private void buttonTestSigCapture_Click(object sender, System.EventArgs e)
		{
			this.CloseDisplays();
			this.ExtractCaptureMgtParameters();
			this.WriteCaptureMgtParameters();
			this.m_strSigName = this.tbTestSigID.Text;
			int iSubClass = -1;
			m_bCaptureStatus = DoSignatureCapture(iSubClass, true);
		}
		private void buttonTestFingerCapture_Click(object sender, System.EventArgs e)
		{
			this.CloseDisplays();
			this.ExtractCaptureMgtParameters();
			this.WriteCaptureMgtParameters();

			this.m_strFingerName = this.tbFingerID.Text;
			int iSubClass = -1;
			m_bCaptureStatus = this.DoFingerCapture(iSubClass, true);
		}
		// DISPLAY
		private void buttonTestPortraitDisplay_Click(object sender, System.EventArgs e)
		{
			this.CloseDisplays();
			this.ExtractCaptureMgtParameters();
			this.WriteCaptureMgtParameters();
			DisplayPortrait(this.tbTestPhotoID.Text, -1, null);
		}
		private void buttonTestSigDisplay_Click(object sender, System.EventArgs e)
		{
			this.CloseDisplays();
			this.ExtractCaptureMgtParameters();
			this.WriteCaptureMgtParameters();
			DisplaySignature(this.tbTestSigID.Text, -1, null);
		}
		private void buttonTestFingerDisplay_Click(object sender, System.EventArgs e)
		{
			this.CloseDisplays();
			this.ExtractCaptureMgtParameters();
			this.WriteCaptureMgtParameters();
			DisplayFingerprint(this.tbFingerID.Text, -1, null);
		}
		// DEVICE PROPERTIES
		// call the portrait capture device properties dialog
		// the dialog is built into the capture device assembly
		private void buttonPortraitDeviceProperties_Click(object sender, System.EventArgs e)
		{
			this.CloseDisplays();
			this.ExtractCaptureMgtParameters();
			this.WriteCaptureMgtParameters();
			DoPortraitDeviceProperties();
		}
		private void buttonSigDeviceProperties_Click(object sender, System.EventArgs e)
		{
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.Display, true)) return;
			this.CloseDisplays();
			this.ExtractCaptureMgtParameters();
			this.WriteCaptureMgtParameters();
			DoSignatureDeviceProperties();
		}
		private void buttonFingerDeviceProperties_Click(object sender, System.EventArgs e)
		{
			this.CloseDisplays();
			this.ExtractCaptureMgtParameters();
			this.WriteCaptureMgtParameters();
			DoFingerprintDeviceProperties();
		}

		// FINISHER PROPERTIES
		private void buttonPortraitFinisherProperties_Click(object sender, System.EventArgs e)
		{
			this.CloseDisplays();
			FinisherProperties dlgFinisherProperties = new FinisherProperties(DCSDatabaseIF.ImageClass.Portrait);
			dlgFinisherProperties.ShowDialog(this);
			return;
		}
		private void buttonSigFinisherProperties_Click(object sender, System.EventArgs e)
		{
			this.CloseDisplays();
			FinisherProperties dlgFinisherProperties = new FinisherProperties(DCSDatabaseIF.ImageClass.Signature);
			dlgFinisherProperties.ShowDialog(this);
			return;
		}
		private void buttonFingerFinisherProperties_Click(object sender, System.EventArgs e)
		{
			this.CloseDisplays();
			FinisherProperties dlgFinisherProperties = new FinisherProperties(DCSDatabaseIF.ImageClass.Fingerprint);
			dlgFinisherProperties.ShowDialog(this);
			return;
		}
		private void tabControl1_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			this.ExtractCaptureMgtParameters();
			this.labelNumPortraits.Text = ((m_bPortrait && m_bPortraitScannerCapture) ? m_nPortraitInstances.ToString() : "none");
			this.labelNumSignatures.Text = ((m_bSignature && m_bSignatureScannerCapture) ? m_nSignatureInstances.ToString() : "none");
			this.labelNumFingerprints.Text = ((m_bFingerprint && m_bFingerprintScannerCapture) ? m_nFingerprintInstances.ToString() : "none");
		}

		private void checkSignature_Click(object sender, System.EventArgs e)
		{
			m_bSignature = this.checkSignature.Checked;
			if (m_bSignature)
			{
				this.checkSignatureDisplay.Checked = true;
				this.m_bSignatureCapture = this.checkSignatureCapture.Checked = true;
				this.m_bSignatureScannerCapture = this.checkSignatureScanner.Checked = false;
			}
			this.AdjustVisibilities();
		}

		private void checkFingerprint_Click(object sender, System.EventArgs e)
		{
			m_bFingerprint = this.checkFingerprintBase.Checked = this.checkFingerprint.Checked;
			if (m_bFingerprint)
			{
				this.checkFingerprintDisplay.Checked = true;
				this.m_bFingerprintCapture = this.checkFingerprintCapture.Checked = DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.FingerprintCapture);
				this.m_bFingerprintScannerCapture = this.checkFingerprintScanner.Checked = false;
			}
			else
			{
				m_b10Print = this.check10Print.Checked = false;
			}
			this.AdjustVisibilities();
		}

		private void checkPortrait_Click(object sender, System.EventArgs e)
		{
			m_bPortrait = this.checkPortrait.Checked;
			if (m_bPortrait)
			{
				this.checkPortraitDisplay.Checked = true;
				this.m_bPortraitCapture = this.checkPortraitCapture.Checked = true;
				this.m_bPortraitScannerCapture = this.checkPortraitScanner.Checked = false;
			}
			this.AdjustVisibilities();
		}

		private void buttonScannerCapture_Click(object sender, System.EventArgs e)
		{
			this.CloseDisplays();
			this.ExtractCaptureMgtParameters();

			DoScannerCapture(this.textBoxScannerImageID.Text
				, (m_bPortrait && m_bPortraitScannerCapture)
				, (m_bSignature && m_bSignatureScannerCapture)
				, (m_bFingerprint && m_bFingerprintScannerCapture));
		}

		private void buttonScannerProperties_Click(object sender, System.EventArgs e)
		{
			this.CloseDisplays();
			this.ExtractCaptureMgtParameters();
			bool bRet = ValidateQueryAndFix(false, false);	// quiet is false, checkonly is false
			if (!bRet) return;
			this.WriteCaptureMgtParameters();

			DCSDEV.DCSScanner.ScannerProperties scannerProp = new DCSDEV.DCSScanner.ScannerProperties(m_scannerIF);
			scannerProp.ShowDialog(this);

			DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("DCSScanner");
			this.textBoxScannerName.Text = ps.GetStringParameter("DCSScannerDeviceName", m_scannerIF.m_strSelectedName);
		}

		private void buttonScannerDisplay_Click(object sender, System.EventArgs e)
		{
			this.CloseDisplays();
			this.ExtractCaptureMgtParameters();
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.Display, true)) return;

			string strLabel = null;
			string strImageID = this.textBoxScannerImageID.Text;
			if (m_bPortrait && m_bPortraitScannerCapture)
			{
				DoDisplayClass(strImageID, DCSDatabaseIF.ImageClass.Portrait, -1, strLabel);
			}
			if (m_bSignature && m_bSignatureScannerCapture)
			{
				DoDisplayClass(strImageID, DCSDatabaseIF.ImageClass.Signature, -1, strLabel);
			}
			if (m_bFingerprint && m_bFingerprintScannerCapture)
			{
				DoDisplayClass(strImageID, DCSDatabaseIF.ImageClass.Fingerprint, -1, strLabel);
			}
		}

		private void checkFingerprintCapture_Click(object sender, System.EventArgs e)
		{
			if (this.checkFingerprintCapture.Checked && !DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.FingerprintCapture, true)) this.checkFingerprintCapture.Checked = false;

			this.m_bFingerprintCapture = this.checkFingerprintCapture.Checked;
			if (this.m_bFingerprintCapture)
				this.m_bFingerprintScannerCapture = this.checkFingerprintScanner.Checked = false;
			else
				this.checkGenFingerBIN.Checked = false;
			this.AdjustVisibilities();
		}

		private void checkSignatureCapture_Click(object sender, System.EventArgs e)
		{
			if (this.checkSignatureCapture.Checked && !DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.SignatureCapture, true)) this.checkSignatureCapture.Checked = false;

			this.m_bSignatureCapture = this.checkSignatureCapture.Checked;
			if (this.m_bSignatureCapture)
				this.m_bSignatureScannerCapture = this.checkSignatureScanner.Checked = false;
			this.AdjustVisibilities();
		}

		private void checkPortraitCapture_Click(object sender, System.EventArgs e)
		{
			if (this.checkPortraitCapture.Checked && !DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.PortraitCapture, true)) this.checkPortraitCapture.Checked = false;

			this.m_bPortraitCapture = this.checkPortraitCapture.Checked;
			if (this.m_bPortraitCapture)
				this.m_bPortraitScannerCapture = this.checkPortraitScanner.Checked = false;
			this.AdjustVisibilities();
		}

		private void checkFingerprintScanner_Click(object sender, System.EventArgs e)
		{
			if (this.checkFingerprintScanner.Checked && !DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.FingerprintCapture, true)) this.checkFingerprintScanner.Checked = false;
			if (this.checkFingerprintScanner.Checked && !DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.ScannerMgt, true)) this.checkFingerprintScanner.Checked = false;

			this.m_bFingerprintScannerCapture = this.checkFingerprintScanner.Checked;
			if (this.m_bFingerprintScannerCapture)
				this.m_bFingerprintCapture = this.checkFingerprintCapture.Checked = false;
			this.AdjustVisibilities();
		}

		private void checkSignatureScanner_Click(object sender, System.EventArgs e)
		{
			if (this.checkSignatureScanner.Checked && !DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.ScannerMgt, true)) this.checkSignatureScanner.Checked = false;

			this.m_bSignatureScannerCapture = this.checkSignatureScanner.Checked;
			if (this.m_bSignatureScannerCapture)
				this.m_bSignatureCapture = this.checkSignatureCapture.Checked = false;
			this.AdjustVisibilities();
		}

		private void checkPortraitScanner_Click(object sender, System.EventArgs e)
		{
			// check licensing
			if (this.checkPortraitScanner.Checked && !DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.ScannerMgt, true)) this.checkPortraitScanner.Checked = false;

			this.m_bPortraitScannerCapture = this.checkPortraitScanner.Checked;
			if (this.m_bPortraitScannerCapture)
				this.m_bPortraitCapture = this.checkPortraitCapture.Checked = false;
			this.AdjustVisibilities();
		}

		private void buttonValidate_Click(object sender, System.EventArgs e)
		{
			this.CloseDisplays();
			this.ExtractCaptureMgtParameters();
			bool bRet = ValidateQueryAndFix(false, false);	// quiet is false, checkonly is false
			if (bRet) DCSMsg.Show("Checks OK", MessageBoxIcon.Information);
		}
		private void buttonAccept_Click(object sender, System.EventArgs e)
		{
			this.CloseDisplays();
			this.ExtractCaptureMgtParameters();
			bool bRet = ValidateQueryAndFix(false, false);	// quiet is false, checkonly is false
			if (!bRet) return;
			AcceptProcess();
			this.Hide();
		}
		private void AcceptProcess()
		{
			this.CloseDisplays();
			this.ExtractCaptureMgtParameters();
			this.WriteCaptureMgtParameters();
			DCSDEV.DCSDesignDataAccess.Reinit();
			DCSDatabaseIF.Reinit();
		}

		private void check10Print_Click(object sender, EventArgs e)
		{
			m_b10Print = this.check10Print.Checked;
			this.AdjustVisibilities();
		}

		protected override void OnVisibleChanged(EventArgs e)
		{
			if (this.Visible) this.AdjustVisibilities();
			base.OnVisibleChanged(e);
		}

		private void checkDontSaveFingerImage_Click(object sender, System.EventArgs e)
		{
			m_bDontSaveFingerImage = this.checkDontSaveFingerImage.Checked;
			if (m_bDontSaveFingerImage) m_bFingerprintDisplay = this.checkFingerprintDisplay.Checked = false;
			this.AdjustVisibilities();
		}

		private void checkGenFingerBIN_Click(object sender, System.EventArgs e)
		{
            if (this.checkGenFingerBIN.Checked && !DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.GenerateFingerBiometrics, true))
            {
                this.checkGenFingerBIN.Checked = false;
            }
			m_bGenerateFingerBiometric = this.checkGenFingerBIN.Checked;
            if (!m_bGenerateFingerBiometric) this.checkBoxQCVerify.Checked = false;
			this.AdjustVisibilities();
		}

		private void listBoxFingerBioTypes_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			m_iSelectedFingerBioType = (DCSDatabaseIF.DCSBioTypes)this.listBoxFingerBioTypes.SelectedIndex;
			this.AdjustVisibilities();
		}
		private void buttonShowBiometrics_Click(object sender, System.EventArgs e)
		{
			AcceptProcess();
			this.DoShowBiometrics(this.tbBiometricID.Text, -1);
		}

		private void buttonVerifyBiometric_Click(object sender, System.EventArgs e)
		{
			string strAnswer;
			int iSubClass = -1;
			AcceptProcess();

			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.VerifyFingerBiometrics, true)) return;

			switch (m_iVerifyMode)
			{
				default:
				case 0:
					strAnswer = this.DoVerifyFingerVsImageID(this.tbBiometricID.Text, iSubClass);
					break;
				case 1:
					strAnswer = this.DoVerifyFingerVsImageID("_BIN_BARCODE", iSubClass);
					break;
				case 2:
					strAnswer = this.DoVerifyFingerVsImageID("_BIN_MAGSTRIPE", iSubClass);
					break;
				case 3:
					strAnswer = this.DoVerifyFingerVsImageID("_BIN_SMARTCHIP", iSubClass);
					break;
			}
			DCSMsg.Show("Answer = " + strAnswer, MessageBoxIcon.Information);
		}
		private void numericUpDownMinutiaThreshold_ValueChanged(object sender, System.EventArgs e)
		{
			m_iThresholdMinutia = (int)this.numericUpDownMinutiaThreshold.Value;
		}

		private void checkBoxFingerMapping_Click(object sender, System.EventArgs e)
		{
			m_bFingerMapping = this.checkBoxFingerMapping10Print.Checked = this.checkBoxFingerMapping.Checked;
			this.AdjustVisibilities();
		}

		private void button10PrintFinisherProperties_Click(object sender, EventArgs e)
		{
			this.CloseDisplays();
			FinisherProperties dlgFinisherProperties = new FinisherProperties(DCSDatabaseIF.ImageClass.Fingerprint);
			dlgFinisherProperties.ShowDialog(this);
			return;
		}

		private void button10PrintDeviceProperties_Click(object sender, EventArgs e)
		{
			this.CloseDisplays();
			this.ExtractCaptureMgtParameters();
			this.WriteCaptureMgtParameters();
			DCSDEV.DCSMsg.Show("Implement Do10PrintDeviceProperties");
		}

		private void buttonTest10PrintCapture_Click(object sender, EventArgs e)
		{
			this.CloseDisplays();
			this.ExtractCaptureMgtParameters();
			this.WriteCaptureMgtParameters();

			this.m_strFingerName = this.textBox10PrintID.Text;
			m_bCaptureStatus = Do10PrintCapture(true);
		}

		private void buttonTest10PrintDisplay_Click(object sender, EventArgs e)
		{
			this.CloseDisplays();
			this.ExtractCaptureMgtParameters();
			this.WriteCaptureMgtParameters();
			DisplayFingerprint(this.textBox10PrintID.Text, -1, null);
		}

		private void buttonFingerMapping_Click(object sender, System.EventArgs e)
		{
			DCSDEV.DCSFingerMapping.DCSFingerMapping dlg = new DCSDEV.DCSFingerMapping.DCSFingerMapping();
			dlg.FingerMap = this.m_strFingerMap;
			dlg.FingerMapNames = this.m_arrayFingerMapNames;
			dlg.ShowDialog(this);
			this.m_strFingerMap = dlg.FingerMap;
		}

		private void numericUpDownBIN2Threshold_ValueChanged(object sender, System.EventArgs e)
		{
			m_iThresholdBIN2 = (int)this.numericUpDownBIN2Threshold.Value;
		}

		private void buttonGenBiometric_Click(object sender, System.EventArgs e)
		{
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.GenerateFingerBiometrics, true)) return;

			Cursor cursorSave = this.Cursor;
			this.Cursor = Cursors.WaitCursor;

			AcceptProcess();

			// delete all existing old BINs
			DoDeleteBiometrics(this.tbBiometricID.Text, DCSDEV.DCSDatabaseIF.ImageClass.Fingerprint, -1);

			int iRet = this.DoGenerateBiometric(this.tbBiometricID.Text, DCSDEV.DCSDatabaseIF.ImageClass.Fingerprint, -1, true);
			this.Cursor = cursorSave;
			if (iRet > 0)
			{
				this.DoShowBiometrics(this.tbBiometricID.Text, -1);
			}
			else
				DCSMsg.Show("ERROR: No biometrics generated");
		}

		private void buttonGenDocBiometrics_Click(object sender, System.EventArgs e)
		{
			AcceptProcess();
			string strBiometrics = DCSDatabaseIF.GetDocumentBIN(this.tbBiometricID.Text, "Document Biometrics");
			DCSMsg.Show(strBiometrics, MessageBoxIcon.Information);
		}

		private void buttonSelectDataRootDir_Click(object sender, System.EventArgs e)
		{
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.NetworkPaths, true)) return;

			System.Windows.Forms.FolderBrowserDialog folderDialog;
			folderDialog = new System.Windows.Forms.FolderBrowserDialog();
			folderDialog.Description = "Select Data Root Directory";

			//folderDialog.RootFolder = Environment.SpecialFolder.Personal; = My Documents
			folderDialog.SelectedPath = this.tbDataRootDir.Text;  // initial dir

			DialogResult result = folderDialog.ShowDialog(this);
			if (result != DialogResult.Cancel)
			{
				this.DataRootDir = folderDialog.SelectedPath;
				// the above operation also sets the control and all image class paths
			}
		}

		private void buttonGenFips_Click(object sender, System.EventArgs e)
		{
			AcceptProcess();
			DoFIPSImportExport(this.tbBiometricID.Text, false, null);
		}

		private void buttonReadFips_Click(object sender, System.EventArgs e)
		{
			AcceptProcess();
			DoFIPSImportExport(this.tbBiometricID.Text, true, null);
		}

		private void buttonTestCertsDisplay_Click(object sender, EventArgs e)
		{
			string strImageID = this.textBoxTestCertsID.Text;
			int instance = (int)this.numericUpDownCertsInstance.Value;
			string strLabel = null;

			this.CloseDisplays();
			this.ExtractCaptureMgtParameters();
			this.WriteCaptureMgtParameters();
			DisplayCerts(strImageID, instance, strLabel);
		}

		private void checkCerts_Click(object sender, EventArgs e)
		{
			m_bCerts = this.checkCerts.Checked;
			if (m_bCerts)
			{
				this.checkCertsDisplay.Checked = true;
				this.m_bCertsCapture = this.checkCertsCapture.Checked = true;
				this.m_bCertsDisplay = this.checkCertsDisplay.Checked = true;
			}
			this.AdjustVisibilities();

			m_bCerts = this.checkCerts.Checked;
			if (m_bCerts)
			{
				this.checkCertsDisplay.Checked = true;
				this.m_bCertsCapture = this.checkCertsCapture.Checked = true;
			}
			this.AdjustVisibilities();

		}

		private void buttonCertsFinisherProperties_Click(object sender, EventArgs e)
		{
			this.CloseDisplays();
			FinisherProperties dlgFinisherProperties = new FinisherProperties(DCSDatabaseIF.ImageClass.Certificate);
			dlgFinisherProperties.ShowDialog(this);
			return;
		}

		private void buttonSelectDevice_Click(object sender, EventArgs e)
		{
			bool bRet = m_scannerIF.SelectDevice();
			if (bRet) this.tbScannerDeviceName.Text = m_scannerIF.m_strSelectedName;
		}

		private void checkCertsScan_Click(object sender, EventArgs e)
		{
			if (this.checkCertsCapture.Checked && !DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.CertsHandling, true)) this.checkCertsCapture.Checked = false;

			this.m_bCertsCapture = this.checkCertsCapture.Checked;
			this.AdjustVisibilities();

		}

		private void buttonOLEImageDB_Click(object sender, EventArgs e)
		{
            this.CloseDisplays();
            if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.ImagesInDatabase, true))
                return;

//            SQLTest.SQL1 dd = new SQLTest.SQL1();
//            return;

            // Need a dialog to specify:  - server and database name currently hardwired in OLEImageDB.Init()

            switch (m_strImageDBType)
			{
				default:
				case "FILES":        // currently FILES switching to OLE (ACCESS or SQL)
                    DCSDEV.FilesToOle.FilesToOLE dlg = new DCSDEV.FilesToOle.FilesToOLE(m_ps, m_strDataRootDir);
                    dlg.ShowDialog();
                    if (dlg.DialogResult == DialogResult.Cancel) return;
                    m_strImageDBType = dlg.m_strImageDBType;
                    this.textBoxImageDBType.Text = m_strImageDBType;
                    break;
				case "ACCESS":
				case "SQL":		// current ACCESS or SQL(not supported yet) switch to FILES
                    DCSDEV.OLEToFiles.OLEToFiles dlg2 = new DCSDEV.OLEToFiles.OLEToFiles(m_ps, m_strImageDBType, m_strDataRootDir);
                    dlg2.ShowDialog();
                    if (dlg2.DialogResult == DialogResult.Cancel) return;
                    m_strImageDBType = "FILES";
                    this.textBoxImageDBType.Text = m_strImageDBType;
                    break;
            }
		}

		private void buttonSearchBiometric_Click(object sender, EventArgs e)
		{
			string strAnswer;
			int iSubClass = -1;

			AcceptProcess();

			ArrayList arrayOfImageIDs = new ArrayList();
			strAnswer = this.DoSearchFingerInDB("_LIVE", iSubClass, arrayOfImageIDs);
			if (strAnswer == "OK")
			{
				foreach (string str in arrayOfImageIDs)
					strAnswer = strAnswer + str + ",";
			}

			DCSMsg.Show("Answer = " + strAnswer, MessageBoxIcon.Information);
		}
		#endregion		// end message handlers

		#region Public Interface

		public void CaptureCerts(string strImageID, string strImageTitle, int iInstance)
		{
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.CertsHandling, true))
				return;
			if (!m_bCerts)
			{
				DCSDEV.DCSMsg.Show("Certificates scanning has not been enabled.", MessageBoxIcon.Error);
				return;
			}
			m_bCaptureStatus = this.DoCertsScan(strImageID, strImageTitle, iInstance);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="strImageID"></param>
		/// <param name="strImageTitle"></param>
		/// <param name="iSubClass">0-9 0r -1 for ALL instances</param>
		public void CapturePortrait(string strImageID, string strImageTitle, int iSubClass, bool bPermitArchive)
		{
			if (!m_bPortrait)
			{
				DCSDEV.DCSMsg.Show("Portrait images have not been enabled.", MessageBoxIcon.Error);
				return;
			}
			if (m_bPortraitScannerCapture)
			{
				DoScannerCapture(strImageID, true, false, false);
			}
			else
			{
				m_strPhotoName = strImageID;
				m_strPhotoTitle = strImageTitle;
				m_bCaptureStatus = DoPortraitCapture(iSubClass, bPermitArchive);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="strImageID"></param>
		/// <param name="strImageTitle"></param>
		/// <param name="iSubClass">0-9 0r -1 for ALL instances</param>
		public void CaptureSignature(string strImageID, string strImageTitle, int iSubClass, bool bPermitArchive)
		{
			if (!m_bSignature)
			{
				DCSDEV.DCSMsg.Show("Signature images have not been enabled.", MessageBoxIcon.Error);
				return;
			}
			if (m_bSignatureScannerCapture)
			{
				DoScannerCapture(strImageID, false, true, false);
			}
			else
			{
				m_strSigName = strImageID;
				m_strSigTitle = strImageTitle;
				m_bCaptureStatus = DoSignatureCapture(iSubClass, bPermitArchive);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="strImageID"></param>
		/// <param name="strImageTitle"></param>
		/// <param name="iSubClass">0-9 0r -1 for ALL instances</param>
		public void CaptureFingerprint(string strImageID, string strImageTitle, int iSubClass, bool bPermitArchive)
		{
			if (!m_bFingerprint)
			{
				DCSDEV.DCSMsg.Show("Fingerprint images have not been enabled.", MessageBoxIcon.Error);
				return;
			}
			m_strFingerName = strImageID;
			if (m_bFingerprintScannerCapture)
			{
				DoScannerCapture(strImageID, false, false, true);
			}
			else
			{
				m_strFingerName = strImageID;
				m_strFingerTitle = strImageTitle;
				m_bCaptureStatus = this.DoFingerCapture(iSubClass, bPermitArchive);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="strImageID"></param>
		/// <param name="strImageTitle"></param>
		public void Capture10Print(string strImageID, string strImageTitle, bool bPermitArchive)
		{
			if (!m_bFingerprint || !m_b10Print)
			{
				DCSDEV.DCSMsg.Show("Ten print images have not been enabled.", MessageBoxIcon.Error);
				return;
			}
			m_strFingerName = strImageID;
			m_strFingerTitle = strImageTitle;
			m_bCaptureStatus = this.Do10PrintCapture(bPermitArchive);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="strImageID"></param>
		/// <param name="strImageTitle"></param>
		public void CaptureAllImages(string strImageID, string strImageTitle, bool bPermitArchive)
		{
			m_bCaptureStatus = false;
			// the portrait pass of scanner capture will capture all classes
			bool bDoScanner;
			bool bRet;
			if ((m_bPortrait && m_bPortraitScannerCapture)
				|| (m_bSignature && m_bSignatureScannerCapture)
				|| (m_bFingerprint && m_bFingerprintScannerCapture))
				bDoScanner = true;
			else
				bDoScanner = false;

			if (bDoScanner)
			{
				bool bRetScan = DoScannerCapture(strImageID, true, true, true);
			}
			if (m_nCaptureOrder == 0)
			{
				if (m_bPortrait && m_bPortraitCapture)
				{
					m_strPhotoName = strImageID;
					m_strPhotoTitle = strImageTitle;
					bRet = DoPortraitCapture(-1, bPermitArchive);	// -1 captures all instances
					if (bRet) m_bCaptureStatus = true;
				}
				if (m_bSignature && m_bSignatureCapture)
				{
					m_strSigName = strImageID;
					m_strSigTitle = strImageTitle;
					bRet = DoSignatureCapture(-1, bPermitArchive);	// -1 captures all instances
					if (bRet) m_bCaptureStatus = true;
				}
				if (m_bFingerprint && m_bFingerprintCapture)
				{
					m_strFingerName = strImageID;
					m_strFingerTitle = strImageTitle;
					bRet = this.DoFingerCapture(-1, bPermitArchive);	// -1 captures all instances
					if (bRet) m_bCaptureStatus = true;
				}
				if (m_bFingerprint && m_b10Print)
				{
					m_strFingerName = strImageID;
					m_strFingerTitle = strImageTitle;
					bRet = this.Do10PrintCapture(bPermitArchive);
					if (bRet) m_bCaptureStatus = true;
				}
			}
			else if (m_nCaptureOrder == 1)
			{
				if (m_bSignature && m_bSignatureCapture)
				{
					m_strSigName = strImageID;
					m_strSigTitle = strImageTitle;
					bRet = DoSignatureCapture(-1, bPermitArchive);	// -1 captures all instances
					if (bRet) m_bCaptureStatus = true;
				}
				if (m_bFingerprint && m_bFingerprintCapture)
				{
					m_strFingerName = strImageID;
					m_strFingerTitle = strImageTitle;
					bRet = this.DoFingerCapture(-1, bPermitArchive);	// -1 captures all instances
					if (bRet) m_bCaptureStatus = true;
				}
				if (m_bFingerprint && m_b10Print)
				{
					m_strFingerName = strImageID;
					m_strFingerTitle = strImageTitle;
					bRet = this.Do10PrintCapture(bPermitArchive);
					if (bRet) m_bCaptureStatus = true;
				}
				if (m_bPortrait && m_bPortraitCapture)
				{
					m_strPhotoName = strImageID;
					m_strPhotoTitle = strImageTitle;
					bRet = DoPortraitCapture(-1, bPermitArchive);	// -1 captures all instances
					if (bRet) m_bCaptureStatus = true;
				}
			}
			else // if (m_nCaptureOrder ==2)
			{
				if (m_bFingerprint && m_bFingerprintCapture)
				{
					m_strFingerName = strImageID;
					m_strFingerTitle = strImageTitle;
					bRet = this.DoFingerCapture(-1, bPermitArchive);	// -1 captures all instances
					if (bRet) m_bCaptureStatus = true;
				}
				if (m_bFingerprint && m_b10Print)
				{
					m_strFingerName = strImageID;
					m_strFingerTitle = strImageTitle;
					bRet = this.Do10PrintCapture(bPermitArchive);
					if (bRet) m_bCaptureStatus = true;
				}
				if (m_bSignature && m_bSignatureCapture)
				{
					m_strSigName = strImageID;
					m_strSigTitle = strImageTitle;
					bRet = DoSignatureCapture(-1, bPermitArchive);	// -1 captures all instances
					if (bRet) m_bCaptureStatus = true;
				}
				if (m_bPortrait && m_bPortraitCapture)
				{
					m_strPhotoName = strImageID;
					m_strPhotoTitle = strImageTitle;
					bRet = DoPortraitCapture(-1, bPermitArchive);	// -1 captures all instances
					if (bRet) m_bCaptureStatus = true;
				}
			}
		}

		/// <summary>
		/// 
		/// </summary>
		public void CloseDisplays()
		{
			DCSDEV.DCSImageDisplay imageDisplay;
			int count = DCSDEV.DCSImageDisplay.m_arrayListImageDisplay.Count;
			while (--count >= 0)
			{
				imageDisplay = (DCSDEV.DCSImageDisplay)DCSDEV.DCSImageDisplay.m_arrayListImageDisplay[count];
				imageDisplay.Close();
				if (imageDisplay.pbDisplayImage.Image != null)
				{
					imageDisplay.pbDisplayImage.Image.Dispose();
					imageDisplay.pbDisplayImage.Image = null;
				}
				imageDisplay.Dispose();
			}
			DCSDEV.DCSImageDisplay.m_arrayListImageDisplay.Clear();
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="strImageID"></param>
		/// <param name="iSubClass">0-9 0r -1 for ALL instances</param>
		public void DeletePortrait(string strImageID, int iSubClass)
		{
			this.DoDeleteClass(strImageID, DCSDatabaseIF.ImageClass.Portrait, iSubClass);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="strImageID"></param>
		/// <param name="iSubClass">0-9 0r -1 for ALL instances</param>
		public void DeleteSignature(string strImageID, int iSubClass)
		{
			this.DoDeleteClass(strImageID, DCSDatabaseIF.ImageClass.Signature, iSubClass);
		}

		/// <summary>
		/// Delete fingerprint images and associated BINs
		/// </summary>
		/// <param name="strImageID"></param>
		/// <param name="iSubClass">The value is 0-9 or -1 for ALL subclasses</param>
		public void DeleteFingerprint(string strImageID, int iSubClass)
		{
			this.DoDeleteClass(strImageID, DCSDatabaseIF.ImageClass.Fingerprint, iSubClass);
			this.DoDeleteClass(strImageID + "_BIN2", DCSDatabaseIF.ImageClass.Fingerprint, iSubClass);
#if LISKA_BIN
			this.DoDeleteClass(strImageID, DCSDatabaseIF.ImageClass.BINL, iSubClass);
#endif
            this.DoDeleteClass(strImageID, DCSDatabaseIF.ImageClass.BINA, iSubClass);
			this.DoDeleteClass(strImageID, DCSDatabaseIF.ImageClass.BING, iSubClass);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="strImageID"></param>
		/// <param name="iSubClass">0-9 0r -1 for ALL subclasses</param>
		public void DeleteCerts(string strImageID, int iSubClass)
		{
			this.DoDeleteClass(strImageID, DCSDatabaseIF.ImageClass.Certificate, iSubClass);
		}

		/// <summary>
		/// Delete all images and BINs for the indicated image ID.
		/// </summary>
		/// <param name="strImageID"></param>
		public void DeleteAllImageClasses(string strImageID)
		{
			// SYH Note: this function ends up calling DeleteStoredHistoricFiles for each image class and subclass
			// which is necessary for images in files but repeated calls could be avoided for oleDB storage.
			this.DoDeleteClass(strImageID, DCSDatabaseIF.ImageClass.Portrait, -1);
			this.DoDeleteClass(strImageID, DCSDatabaseIF.ImageClass.Signature, -1);
			this.DoDeleteClass(strImageID, DCSDatabaseIF.ImageClass.Fingerprint, -1);
#if LISKA_BIN
			this.DoDeleteClass(strImageID, DCSDatabaseIF.ImageClass.BINL, -1);
#endif
            this.DoDeleteClass(strImageID, DCSDatabaseIF.ImageClass.BINA, -1);
			this.DoDeleteClass(strImageID, DCSDatabaseIF.ImageClass.BING, -1);
			this.DoDeleteClass(strImageID, DCSDatabaseIF.ImageClass.Certificate, -1);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="strImageID"></param>
		public bool FIPSImportExport(bool bImport, string strFilename, string strImageID)
		{
			return this.DoFIPSImportExport(strImageID, bImport, strFilename);
		}

		// this routine exists only to give DCSSDK.CaptureMgt() an easy programatic interface to get the file name.
		public string GetFullnameOfImage(string strImageID, string strImgClassEx, bool bMustExist)
		{
			DCSDEV.DCSDatabaseIF.ImageClass imageClass = DCSDEV.DCSDatabaseIF.StringToClass(strImgClassEx);
			int iSubClass = DCSDEV.DCSDatabaseIF.StringToSubClass(strImgClassEx);
			if (iSubClass < 0) iSubClass = 0;
			return DCSDatabaseIF.GetFullnameOfImage(strImageID, imageClass, iSubClass, bMustExist);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="strImageID"></param>
		/// <param name="iSubClass">0-9 instance</param>
		public bool ImportExportPortrait(bool bImport, string strFilename, string strImageID, int iSubClass)
		{
			return this.DoImportExportSubclass(strImageID, bImport, false, strFilename, DCSDatabaseIF.ImageClass.Portrait, iSubClass);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="strImageID"></param>
		/// <param name="iSubClass">0-9 instance</param>
		public bool ImportExportSignature(bool bImport, string strFilename, string strImageID, int iSubClass)
		{
			return this.DoImportExportSubclass(strImageID, bImport, false, strFilename, DCSDatabaseIF.ImageClass.Signature, iSubClass);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="strImageID"></param>
		/// <param name="iSubClass">0-9 instance</param>
		public bool ImportExportFingerprint(bool bImport, string strFilename, string strImageID, int iSubClass)
		{
			return this.DoImportExportSubclass(strImageID, bImport, false, strFilename, DCSDatabaseIF.ImageClass.Fingerprint, iSubClass);
		}

		public bool ImportExportCerts(bool bImport, string strFilename, string strImageID, int instance)
		{
			return this.DoImportExportSubclass(strImageID, bImport, false, strFilename, DCSDatabaseIF.ImageClass.Certificate, instance);
		}

		public bool ImportMergeAll(string strImageID, string strSourceID, string strSourceDataRoot)
		{
			bool bRet = false;
			if (this.m_bCerts)
				bRet = this.DoImportMergeClass(strImageID, strSourceID, strSourceDataRoot, DCSDatabaseIF.ImageClass.Certificate);
			if (this.m_bPortrait)
				bRet = this.DoImportMergeClass(strImageID, strSourceID, strSourceDataRoot, DCSDatabaseIF.ImageClass.Portrait);
			if (this.m_bSignature)
				bRet = this.DoImportMergeClass(strImageID, strSourceID, strSourceDataRoot, DCSDatabaseIF.ImageClass.Signature);
			if (this.m_bFingerprint)
				bRet = this.DoImportMergeClass(strImageID, strSourceID, strSourceDataRoot, DCSDatabaseIF.ImageClass.Fingerprint);
			return bRet;
		}

		public bool ImportMergeCerts(string strImageID, string strSourceID, string strSourceDataRoot)
		{
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.CertsHandling, true))
				return false;
			return this.DoImportMergeClass(strImageID, strSourceID, strSourceDataRoot, DCSDatabaseIF.ImageClass.Certificate);
		}

		public bool ImportMergePortrait(string strImageID, string strSourceID, string strSourceDataRoot)
		{
			return this.DoImportMergeClass(strImageID, strSourceID, strSourceDataRoot, DCSDatabaseIF.ImageClass.Portrait);
		}

		public bool ImportMergeSignature(string strImageID, string strSourceID, string strSourceDataRoot)
		{
			return this.DoImportMergeClass(strImageID, strSourceID, strSourceDataRoot, DCSDatabaseIF.ImageClass.Signature);
		}

		public bool ImportMergeFingerprint(string strImageID, string strSourceID, string strSourceDataRoot)
		{
			return this.DoImportMergeClass(strImageID, strSourceID, strSourceDataRoot, DCSDatabaseIF.ImageClass.Fingerprint);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="strImageID"></param>
		public void DisplayAllImageClasses(string strImageID)
		{
			this.DisplayAllImageClasses(strImageID, null);
		}

		public void DisplayAllImageClasses(string strImageID, string strLabel)
		{
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.Display, true)) return;

			bool bNothingToDo = true;
			// display portrait last so it will layer on top of fingerprint if they overlap
			if (m_bFingerprint && this.checkFingerprintDisplay.Checked)
			{
				bNothingToDo = false;
				DoDisplayClass(strImageID, DCSDatabaseIF.ImageClass.Fingerprint, -1, strLabel);
			}
			if (this.checkSignature.Checked && this.checkSignatureDisplay.Checked)
			{
				bNothingToDo = false;
				DoDisplayClass(strImageID, DCSDatabaseIF.ImageClass.Signature, -1, strLabel);
			}
			if (m_bPortrait && this.checkPortraitDisplay.Checked)
			{
				bNothingToDo = false;
				DoDisplayClass(strImageID, DCSDatabaseIF.ImageClass.Portrait, -1, strLabel);
			}
			if (bNothingToDo)
			{
				DCSDEV.DCSMsg.Show("No image class is selected for display", MessageBoxIcon.Error);
			}
		}

		public void DisplayCerts(string strImageID, int instance, string strLabel)
		{
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.CertsHandling, true))
				return;
			DoDisplayClass(strImageID, DCSDatabaseIF.ImageClass.Certificate, instance, strLabel);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="strImageID"></param>
		/// <param name="iSubClass">0-9 0r -1 for ALL instances</param>
		/// <param name="strLabel"></param>
		public void DisplayPortrait(string strImageID, int iSubClass, string strLabel)
		{
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.Display)) return;
			DoDisplayClass(strImageID, DCSDatabaseIF.ImageClass.Portrait, iSubClass, strLabel);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="strImageID"></param>
		/// <param name="iSubClass">0-9 0r -1 for ALL instances</param>
		/// <param name="strLabel"></param>
		public void DisplaySignature(string strImageID, int iSubClass, string strLabel)
		{
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.Display)) return;
			m_strSigName = strImageID;
			DoDisplayClass(strImageID, DCSDatabaseIF.ImageClass.Signature, iSubClass, strLabel);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="strImageID"></param>
		/// <param name="iSubClass">0-9 0r -1 for ALL instances</param>
		/// <param name="strLabel"></param>
		public void DisplayFingerprint(string strImageID, int iSubClass, string strLabel)
		{
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.Display)) return;
			m_strFingerName = strImageID;
			DoDisplayClass(strImageID, DCSDatabaseIF.ImageClass.Fingerprint, iSubClass, strLabel);
		}

		/// <summary>
		/// Generate biometric from finger image named strImageID and strImageID_BIN2 in finger directory for iSubClass instance
		/// If subclass = -1 do all subclasses.
		/// Write biometric into strImageID.BIN?  (? = '2' for Liska and 'A' for IBM AIS)
		/// 
		/// </summary>
		/// <param name="strImageID">string identifier of image from which name of image file and BIN file is generated</param>
		/// <param name="strImgClassEx"></param>
		/// <param name="iSubClass">Finger instance 0-9 or -1 for ALL. Determines finger directory(s) used.</param>
        /// <param name="bVerbose"></param>
        /// <returns>int Number generated. -1=error; 0=none generated;</returns>
		public int GenerateBiometric(string strImageID, string strImgClass, int iSubClass, bool bVerbose)
		{
			DCSDatabaseIF.ImageClass imageClass = DCSDatabaseIF.StringToClass(strImgClass);
			// delete all existing old BINs
			if (iSubClass == -1)
				DoDeleteBiometrics(strImageID, imageClass, -1);

			return this.DoGenerateBiometric(strImageID, imageClass, iSubClass, bVerbose);
		}

		/// <summary>
		/// Return the biometric as a hex encoded text string.  The biometric will correspond to the
		/// image ID of the indicated class.
		/// Each biometric has a 6 char prefix: LSVVXF: LS=biometric type (LS = Liska BIN, AS = IBM AIS Biometric); VV=version; X is reserved; F = finger index 0-9 

		/// </summary>
		/// <param name="strImageID">Image ID</param>
		/// <param name="strImageClass">strImage Class - only Fingerprint is supported</param>
		/// <param name="iSubClass">iSubClass is instance of fingerprint 0-9</param>
		/// <returns></returns>
		/// 
		public string GetBiometric(string strImageID, string strImageClass, int iSubClass)
		{
			DCSDEV.DCSDatabaseIF.ImageClass imageClass = DCSDEV.DCSDatabaseIF.StringToClass(strImageClass);
			return DCSDatabaseIF.GetBiometric(strImageID, imageClass, iSubClass);
		}

		/// <summary>
		/// Return the document biometrics as a hex encoded text string.  The biometric will correspond to the
		/// image ID.
		/// A DocumentBIN has structure: "DOC=" + DocumentName + ";BIN=" + strBIN + ";" - where the BIN= part is repeated N times.
		/// where strBIN is an individual biomatric with a 6 char header as defined under GetBiometric.
		/// Bins inserted into Document Barcodes have structure: "DOC=" + DocumentName + ";" + DocumentBIN 
		/// </summary>
		/// <param name="strImageID">Image ID</param>
		/// <param name="strDocName">strDocName = reference name to the document being produced (or blank)</param>
		/// <returns>Empty string if there is a problem.</returns>
		public string GetDocumentBIN(string strImageID, string strDocName)
		{
			return DCSDatabaseIF.GetDocumentBIN(strImageID, strDocName);
		}

		public string GetLastChipID()
		{
			return m_strLastChipID;
		}

		// strSource = "2DBAR" or "CHIP"
		// strDestination file name.  
		// RETURN <1 = error; 0 = cancel; >0 = ok
		public int ScanData(string strSource, string strDestination)
		{
			bool bRet;
			string strFromDocument = "";    // text string read from document
			if (strSource.ToUpper() == "2DBAR")
			{
				// read barcode and put decoded text in strFromDocument
				// then write decoded text to file strDestination
				bRet = this.DoScanBarcode(out strFromDocument);
				if (bRet && strDestination != null)
				{
					try
					{
						System.IO.StreamWriter stream = new System.IO.StreamWriter(strDestination);
						stream.Write(strFromDocument);
						stream.Close();
					}
					catch (Exception ex)
					{
						DCSMsg.Show(String.Format("Cannot write scanned data to destination {0}.", strDestination), ex);
						return -1;
					}
					return 1;
				}
				else if (bRet) return 1;
				else return -1;
			}
			else if (strSource.ToUpper() == "CHIP")
			{
				// read smart chip and put decoded text in strFromDocument
				// then write decoded text to file strDestination
				int iRet = this.DoScanSmartChip(strDestination, out strFromDocument, false);	// arg3=false=not for verify biometric
				if (iRet > 0 && strDestination != null)
				{
					try
					{
						System.IO.StreamWriter stream = new System.IO.StreamWriter(strDestination);
						stream.Write(strFromDocument);
						stream.Close();
					}
					catch (Exception ex)
					{
						DCSMsg.Show(String.Format("Cannot write scanned data to destination {0}.", strDestination), ex);
						return -1;
					}
				}
				return iRet;
			}
			else
			{
				DCSDEV.DCSMsg.Show(String.Format("Data source '{0}' is not recognized. Only '2DBar' and 'Chip' are known", strSource), MessageBoxIcon.Error);
				return -1;
			}
		}

		public bool ValidateConfiguration()
		{
			this.CloseDisplays();
			this.ExtractCaptureMgtParameters();
			return ValidateQueryAndFix(true, true);	// quiet is true, checkonly is true
		}

		/// <summary>
		/// Capture a live biometric and compare to the biometric stored for strImageID.
		/// If strImageID is _BIN_BARCODE or _BIN_MAGSTRIPE or _BIN_SMARTCHIP, then instead of live finger, use biometric on the card.
		/// The biometric for strImageID is in strImageID.BIN#  (# = '2' for Liska BIN and 'A' for IBM AIS BIN)
		/// </summary>
		/// <param name="strImageID">strImageID = string identifier of image from name of image file is generated
		///            = _BIN_BARCODE if verifying against 2D Barcode
		///            = _BIN_MAGSTRIPE if verifying against magnetic stripe
		///            = _BIN_SMARTCHIP</param>
		/// <param name="strImageClass">Only Fingerprint is supported currently</param>
		/// <param name="iSubClass">instance 0-9 or -1 for ALL. Determines finger directory(s) used.</param>
		/// <returns>Return YES or NO followed by a space and a matching score, or a string beginning with "CANCEL" or "ERROR"</returns>
		public string VerifyBiometric(string strImageID, string strImageClass, int iSubClass)
		{
			// syh: the only class implemented is finger print - so far - but class face will be added here
			switch (DCSDEV.DCSDatabaseIF.StringToClass(strImageClass))
			{
				default:
				case DCSDEV.DCSDatabaseIF.ImageClass.Portrait:
				case DCSDEV.DCSDatabaseIF.ImageClass.Signature:
					return "ERROR: Unsupported biometric class " + strImageClass;
				case DCSDEV.DCSDatabaseIF.ImageClass.Fingerprint:
				case DCSDEV.DCSDatabaseIF.ImageClass.TenPrint:
					if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.VerifyFingerBiometrics, true))
						return "ERROR";
					return this.DoVerifyFingerVsImageID(strImageID, iSubClass);
			}
		}

		public string SearchBiometric(string strImageID, string strImageClass, int iSubClass, ArrayList arrayOfImageIDs)
		{
            // syh: the only class implemented is finger print - so far - but class face will be added here
            switch (DCSDEV.DCSDatabaseIF.StringToClass(strImageClass))
            {
                default:
                case DCSDEV.DCSDatabaseIF.ImageClass.Portrait:
                case DCSDEV.DCSDatabaseIF.ImageClass.Signature:
                    return "ERROR: Unsupported biometric class " + strImageClass;
                case DCSDEV.DCSDatabaseIF.ImageClass.Fingerprint:
                case DCSDEV.DCSDatabaseIF.ImageClass.TenPrint:
                    if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.SearchFingerBiometrics, true))
                        return "ERROR";
                    return DoSearchFingerInDB(strImageID, iSubClass, arrayOfImageIDs);
            }
		}

		public Rectangle PortraitDisplayRectangle
		{
			get
			{
				return GetImgClassDisplayRectangle(DCSDEV.DCSDatabaseIF.ImageClass.Portrait);
			}
			set
			{
				SetImgClassDisplayRectangle(value, DCSDEV.DCSDatabaseIF.ImageClass.Portrait);
			}
		}
		public Rectangle SignatureDisplayRectangle
		{
			get
			{
				return GetImgClassDisplayRectangle(DCSDEV.DCSDatabaseIF.ImageClass.Signature);
			}
			set
			{
				SetImgClassDisplayRectangle(value, DCSDEV.DCSDatabaseIF.ImageClass.Signature);
			}
		}
		public Rectangle FingerprintDisplayRectangle
		{
			get
			{
				return GetImgClassDisplayRectangle(DCSDEV.DCSDatabaseIF.ImageClass.Fingerprint);
			}
			set
			{
				SetImgClassDisplayRectangle(value, DCSDEV.DCSDatabaseIF.ImageClass.Fingerprint);
			}
		}
		public Rectangle CertsDisplayRectangle
		{
			get
			{
				return GetImgClassDisplayRectangle(DCSDEV.DCSDatabaseIF.ImageClass.Certificate);
			}
			set
			{
				SetImgClassDisplayRectangle(value, DCSDEV.DCSDatabaseIF.ImageClass.Certificate);
			}
		}
		public string DataRootDir
		{
			get
			{
				return m_strDataRootDir;
			}
			set
			{
				m_strDataRootDir = value;
				this.tbDataRootDir.Text = m_strDataRootDir;
				m_strPortraitPath = System.IO.Path.Combine(m_strDataRootDir, "Portrait");
				m_strSignaturePath = System.IO.Path.Combine(m_strDataRootDir, "Signatur");
				m_strFingerprintPath = System.IO.Path.Combine(m_strDataRootDir, "Fingerpr");
				m_strCertsPath = System.IO.Path.Combine(m_strDataRootDir, "Certs");
			}
		}
		public bool CaptureStatus
		{
			get
			{
				return m_bCaptureStatus;
			}
			set
			{
				m_bCaptureStatus = value;
			}
		}
		#endregion
	}
}
