using System;
using System.Collections;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Windows.Forms;

namespace DCSDEV.DCSDesigner
{
	/// <summary>
	/// Summary description for DCSPositionSizeProperties.
	/// </summary>
    internal class DCSPositionSizeProperties : System.Windows.Forms.UserControl
	{
		private double m_BoundsX = 0;	// internal units are inch x 1000
		private double m_BoundsY = 0;
		private double m_BoundsW = 0;
		private double m_BoundsH = 0;
		private int m_iUnits = 0;

		public delegate void UnitsChangedEventHandler (object sender, System.EventArgs ev);
		public event UnitsChangedEventHandler UnitsChanged;

		private System.Windows.Forms.Label label4;
		private System.Windows.Forms.TextBox tbH;
		private System.Windows.Forms.TextBox tbW;
		private System.Windows.Forms.Label label5;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.ComboBox comboBoxUnits;
		private System.Windows.Forms.TextBox tbY;
		private System.Windows.Forms.TextBox tbX;
		/// <summary> 
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		public DCSPositionSizeProperties()
		{
			// This call is required by the Windows.Forms Form Designer.
			InitializeComponent();

			// TODO: Add any initialization after the InitializeComponent call
			this.PopulateControls();
		}

		/// <summary> 
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Component Designer generated code
		/// <summary> 
		/// Required method for Designer support - do not modify 
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			this.label4 = new System.Windows.Forms.Label();
			this.tbY = new System.Windows.Forms.TextBox();
			this.tbX = new System.Windows.Forms.TextBox();
			this.tbH = new System.Windows.Forms.TextBox();
			this.tbW = new System.Windows.Forms.TextBox();
			this.label5 = new System.Windows.Forms.Label();
			this.label3 = new System.Windows.Forms.Label();
			this.label2 = new System.Windows.Forms.Label();
			this.comboBoxUnits = new System.Windows.Forms.ComboBox();
			this.SuspendLayout();
			// 
			// label4
			// 
			this.label4.Location = new System.Drawing.Point(0, 56);
			this.label4.Name = "label4";
			this.label4.Size = new System.Drawing.Size(56, 16);
			this.label4.TabIndex = 61;
			this.label4.Text = "Y offset";
			// 
			// tbY
			// 
			this.tbY.Location = new System.Drawing.Point(72, 56);
			this.tbY.Name = "tbY";
			this.tbY.Size = new System.Drawing.Size(72, 20);
			this.tbY.TabIndex = 2;
			this.tbY.Text = "0";
			this.tbY.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
			// 
			// tbX
			// 
			this.tbX.Location = new System.Drawing.Point(72, 32);
			this.tbX.Name = "tbX";
			this.tbX.Size = new System.Drawing.Size(72, 20);
			this.tbX.TabIndex = 1;
			this.tbX.Text = "0";
			this.tbX.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
			// 
			// tbH
			// 
			this.tbH.Location = new System.Drawing.Point(72, 104);
			this.tbH.Name = "tbH";
			this.tbH.Size = new System.Drawing.Size(72, 20);
			this.tbH.TabIndex = 4;
			this.tbH.Text = "0";
			this.tbH.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
			// 
			// tbW
			// 
			this.tbW.Location = new System.Drawing.Point(72, 80);
			this.tbW.Name = "tbW";
			this.tbW.Size = new System.Drawing.Size(72, 20);
			this.tbW.TabIndex = 3;
			this.tbW.Text = "0";
			this.tbW.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
			// 
			// label5
			// 
			this.label5.Location = new System.Drawing.Point(0, 32);
			this.label5.Name = "label5";
			this.label5.Size = new System.Drawing.Size(56, 16);
			this.label5.TabIndex = 59;
			this.label5.Text = "X offset";
			// 
			// label3
			// 
			this.label3.Location = new System.Drawing.Point(0, 104);
			this.label3.Name = "label3";
			this.label3.Size = new System.Drawing.Size(56, 16);
			this.label3.TabIndex = 57;
			this.label3.Text = "height";
			// 
			// label2
			// 
			this.label2.Location = new System.Drawing.Point(0, 80);
			this.label2.Name = "label2";
			this.label2.Size = new System.Drawing.Size(56, 16);
			this.label2.TabIndex = 55;
			this.label2.Text = "width";
			// 
			// comboBoxUnits
			// 
			this.comboBoxUnits.BackColor = System.Drawing.SystemColors.Control;
			this.comboBoxUnits.Items.AddRange(new object[] {
															   "Inch",
															   "MM"});
			this.comboBoxUnits.Location = new System.Drawing.Point(88, 8);
			this.comboBoxUnits.Name = "comboBoxUnits";
			this.comboBoxUnits.Size = new System.Drawing.Size(56, 21);
			this.comboBoxUnits.TabIndex = 0;
			this.comboBoxUnits.Text = "Inch";
			this.comboBoxUnits.SelectedIndexChanged += new System.EventHandler(this.comboBoxUnits_SelectedIndexChanged);
			// 
			// DCSPositionSizeProperties
			// 
			this.Controls.Add(this.comboBoxUnits);
			this.Controls.Add(this.label4);
			this.Controls.Add(this.tbY);
			this.Controls.Add(this.tbX);
			this.Controls.Add(this.tbH);
			this.Controls.Add(this.tbW);
			this.Controls.Add(this.label5);
			this.Controls.Add(this.label3);
			this.Controls.Add(this.label2);
			this.Name = "DCSPositionSizeProperties";
			this.Size = new System.Drawing.Size(152, 128);
			this.ResumeLayout(false);

		}
		#endregion

		private void ExtractFromControls()
		{
			if (m_iUnits == 0)	// inches are displayed
			{
				m_BoundsX =  (Convert.ToDouble(this.tbX.Text) );
				m_BoundsY =  (Convert.ToDouble(this.tbY.Text) );
				m_BoundsW =  (Convert.ToDouble(this.tbW.Text) );
				m_BoundsH =  (Convert.ToDouble(this.tbH.Text) );
			}
			else
			{
				m_BoundsX =  (Convert.ToDouble(this.tbX.Text) / 25.4 );
				m_BoundsY =  (Convert.ToDouble(this.tbY.Text) / 25.4 );
				m_BoundsW =  (Convert.ToDouble(this.tbW.Text) / 25.4 );
				m_BoundsH =  (Convert.ToDouble(this.tbH.Text) / 25.4 );
			}
		}
		private void PopulateControls()
		{
			this.comboBoxUnits.SelectedIndex = m_iUnits;
			if (m_iUnits == 0)	// inches are displayed
			{
				this.tbX.Text = m_BoundsX.ToString("0.000");
				this.tbY.Text = m_BoundsY.ToString("0.000");
				this.tbW.Text = m_BoundsW.ToString("0.000");
				this.tbH.Text = m_BoundsH.ToString("0.000");
			}
			else
			{
				this.tbX.Text = (m_BoundsX * 25.4).ToString("0.000");
				this.tbY.Text = (m_BoundsY * 25.4).ToString("0.000");
				this.tbW.Text = (m_BoundsW * 25.4).ToString("0.000");
				this.tbH.Text = (m_BoundsH * 25.4).ToString("0.000");
			}
		}

		private void comboBoxUnits_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if (m_iUnits != (int)comboBoxUnits.SelectedIndex)
			{
				this.ExtractFromControls();	// extract and repopulate will be required to convert units
				m_iUnits = (int)comboBoxUnits.SelectedIndex;
				this.PopulateControls();

				//public event UnitsChangedEventHandler UnitsChanged;
                try
                {
                    UnitsChanged(sender, e);
                }
                catch { ; }
			}
		}

		// all units are inchx100 - except for display
		public Rectangle DisplayBounds
		{
			get 
			{
				this.ExtractFromControls();
				Rectangle rect = new Rectangle(
					(int)(m_BoundsX * 100.0),
					(int)(m_BoundsY * 100.0),
					(int)(m_BoundsW * 100.0),
					(int)(m_BoundsH * 100.0));
				return rect;
			}
			set 
			{
				m_BoundsX = (double)(value.X) / 100.0;
				m_BoundsY = (double)(value.Y) / 100.0;
				m_BoundsW = (double)(value.Width) / 100.0;
				m_BoundsH = (double)(value.Height) / 100.0;
				this.PopulateControls();
			}
		}
		public int Units
		{
			// 0 = inches; 1 = MM
			get 
			{ 
				return m_iUnits; 
			}
			set 
			{
				if (value < 0) value = 0;
				if (value > 1) value = 1;
				if (m_iUnits != value)
				{
					this.ExtractFromControls();	// extract and repopulate will be required to convert units
					m_iUnits = value;
					this.PopulateControls();
				}
			}
		}
	}
}
