using System;
using System.IO;
using System.Windows.Forms;

namespace DCSDEV
{
	public class DCSDatatypes
	{
		// Note MagStripe is not used, but it cannot be deleted
		public enum DCSDesignObjectTypes {TextObj, ImageObj, Portrait, Signature, Barcode, Fingerprint, MagStripe, ICAOMRZ, GraphicBlock, Barcode2D};	// do not change order

		public enum DCSLabeledTextOrientations { LEFT, RIGHT, TOP, BOTTOM };

		public enum BackFillTypes {FILL_COLOR, FILL_CLEAR, FILL_IMAGE, FILL_GRADIENT, CONDITIONAL_COLOR};
		
		public enum Justifications {LEFT, CENTER, RIGHT, WRAP, NONE};
		
		public enum Alignments {TOP, MIDDLE, BOTTOM, NONE};
		
		public enum SourceTypes {StaticValue, Database, Formula};
		
		public enum ScaleMode {Original, ScaleToFit, KeepAspect};
		
		public enum FramingMode {NONE, ELLIPSE, FADED_ELLIPSE1, FADED_ELLIPSE2, ROUNDED_ALLCORNERS, ROUNDED_TOPCORNERS};

		public static string MultiLineDelimiter = ";";			//Environment.NewLine;
						
	}
}
