<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonCancel.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonCancel.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="buttonCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>470, 400</value>
  </data>
  <data name="buttonCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>138, 24</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonCancel.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="buttonCancel.Text" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Name" xml:space="preserve">
    <value>buttonCancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonCancel.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="buttonAccept.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonAccept.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonAccept.Location" type="System.Drawing.Point, System.Drawing">
    <value>470, 360</value>
  </data>
  <data name="buttonAccept.Size" type="System.Drawing.Size, System.Drawing">
    <value>138, 24</value>
  </data>
  <data name="buttonAccept.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="buttonAccept.Text" xml:space="preserve">
    <value>&amp;OK - apply and close</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Name" xml:space="preserve">
    <value>buttonAccept</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAccept.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="buttonApply.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonApply.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonApply.Location" type="System.Drawing.Point, System.Drawing">
    <value>470, 318</value>
  </data>
  <data name="buttonApply.Size" type="System.Drawing.Size, System.Drawing">
    <value>138, 24</value>
  </data>
  <data name="buttonApply.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="buttonApply.Text" xml:space="preserve">
    <value>&amp;Apply</value>
  </data>
  <data name="&gt;&gt;buttonApply.Name" xml:space="preserve">
    <value>buttonApply</value>
  </data>
  <data name="&gt;&gt;buttonApply.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonApply.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonApply.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="comboBoxFont.Items" xml:space="preserve">
    <value>normal font</value>
  </data>
  <data name="comboBoxFont.Items1" xml:space="preserve">
    <value>Label font</value>
  </data>
  <data name="comboBoxFont.Items2" xml:space="preserve">
    <value>Doc font 1</value>
  </data>
  <data name="comboBoxFont.Items3" xml:space="preserve">
    <value>Doc font 2</value>
  </data>
  <data name="comboBoxFont.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 5</value>
  </data>
  <data name="comboBoxFont.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 21</value>
  </data>
  <data name="comboBoxFont.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="comboBoxFont.Text" xml:space="preserve">
    <value>normal font</value>
  </data>
  <data name="&gt;&gt;comboBoxFont.Name" xml:space="preserve">
    <value>comboBoxFont</value>
  </data>
  <data name="&gt;&gt;comboBoxFont.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxFont.Parent" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;comboBoxFont.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="comboBoxDateFormat.Items" xml:space="preserve">
    <value>app format</value>
  </data>
  <data name="comboBoxDateFormat.Items1" xml:space="preserve">
    <value>system default</value>
  </data>
  <data name="comboBoxDateFormat.Items2" xml:space="preserve">
    <value>MM/dd/yy</value>
  </data>
  <data name="comboBoxDateFormat.Items3" xml:space="preserve">
    <value>MM/dd/yyyy</value>
  </data>
  <data name="comboBoxDateFormat.Items4" xml:space="preserve">
    <value>MMM dd, yyyy</value>
  </data>
  <data name="comboBoxDateFormat.Items5" xml:space="preserve">
    <value>dd MMM yyyy</value>
  </data>
  <data name="comboBoxDateFormat.Items6" xml:space="preserve">
    <value>dd/MM/yy</value>
  </data>
  <data name="comboBoxDateFormat.Items7" xml:space="preserve">
    <value>dd/MM/yyyy</value>
  </data>
  <data name="comboBoxDateFormat.Items8" xml:space="preserve">
    <value>dd-MM-yy</value>
  </data>
  <data name="comboBoxDateFormat.Items9" xml:space="preserve">
    <value>dd-MM-yyyy</value>
  </data>
  <data name="comboBoxDateFormat.Items10" xml:space="preserve">
    <value>dd.MM.yy</value>
  </data>
  <data name="comboBoxDateFormat.Items11" xml:space="preserve">
    <value>dd.MM.yyyy</value>
  </data>
  <data name="comboBoxDateFormat.Items12" xml:space="preserve">
    <value>yy-MM-dd</value>
  </data>
  <data name="comboBoxDateFormat.Items13" xml:space="preserve">
    <value>yyyy-MM-dd</value>
  </data>
  <data name="comboBoxDateFormat.Items14" xml:space="preserve">
    <value>ICAO ENG</value>
  </data>
  <data name="comboBoxDateFormat.Items15" xml:space="preserve">
    <value>ICAO FRA</value>
  </data>
  <data name="comboBoxDateFormat.Items16" xml:space="preserve">
    <value>ICAO SPA</value>
  </data>
  <data name="comboBoxDateFormat.Location" type="System.Drawing.Point, System.Drawing">
    <value>209, 120</value>
  </data>
  <data name="comboBoxDateFormat.Size" type="System.Drawing.Size, System.Drawing">
    <value>136, 21</value>
  </data>
  <data name="comboBoxDateFormat.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;comboBoxDateFormat.Name" xml:space="preserve">
    <value>comboBoxDateFormat</value>
  </data>
  <data name="&gt;&gt;comboBoxDateFormat.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxDateFormat.Parent" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;comboBoxDateFormat.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>209, 105</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 16</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>71</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>Date format:</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="comboBoxCase.Items" xml:space="preserve">
    <value>Mixed Case</value>
  </data>
  <data name="comboBoxCase.Items1" xml:space="preserve">
    <value>All Upper Case</value>
  </data>
  <data name="comboBoxCase.Items2" xml:space="preserve">
    <value>All Lower Case</value>
  </data>
  <data name="comboBoxCase.Location" type="System.Drawing.Point, System.Drawing">
    <value>209, 77</value>
  </data>
  <data name="comboBoxCase.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 21</value>
  </data>
  <data name="comboBoxCase.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="comboBoxCase.Text" xml:space="preserve">
    <value>Mixed Case</value>
  </data>
  <data name="&gt;&gt;comboBoxCase.Name" xml:space="preserve">
    <value>comboBoxCase</value>
  </data>
  <data name="&gt;&gt;comboBoxCase.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxCase.Parent" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;comboBoxCase.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="dcsRotationProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>317, 167</value>
  </data>
  <data name="dcsRotationProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 80</value>
  </data>
  <data name="dcsRotationProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;dcsRotationProperties1.Name" xml:space="preserve">
    <value>dcsRotationProperties1</value>
  </data>
  <data name="&gt;&gt;dcsRotationProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSRotationProperties, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsRotationProperties1.Parent" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;dcsRotationProperties1.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="checkShowBarcodeText.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkShowBarcodeText.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkShowBarcodeText.Location" type="System.Drawing.Point, System.Drawing">
    <value>209, 48</value>
  </data>
  <data name="checkShowBarcodeText.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 24</value>
  </data>
  <data name="checkShowBarcodeText.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="checkShowBarcodeText.Text" xml:space="preserve">
    <value>Show barcode text</value>
  </data>
  <data name="&gt;&gt;checkShowBarcodeText.Name" xml:space="preserve">
    <value>checkShowBarcodeText</value>
  </data>
  <data name="&gt;&gt;checkShowBarcodeText.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkShowBarcodeText.Parent" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;checkShowBarcodeText.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="labelBarcodeType.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelBarcodeType.Location" type="System.Drawing.Point, System.Drawing">
    <value>209, 8</value>
  </data>
  <data name="labelBarcodeType.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 16</value>
  </data>
  <data name="labelBarcodeType.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="labelBarcodeType.Text" xml:space="preserve">
    <value>Barcode Type:</value>
  </data>
  <data name="&gt;&gt;labelBarcodeType.Name" xml:space="preserve">
    <value>labelBarcodeType</value>
  </data>
  <data name="&gt;&gt;labelBarcodeType.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelBarcodeType.Parent" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;labelBarcodeType.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="comboBoxBarcodeTypes.Location" type="System.Drawing.Point, System.Drawing">
    <value>209, 24</value>
  </data>
  <data name="comboBoxBarcodeTypes.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 21</value>
  </data>
  <data name="comboBoxBarcodeTypes.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="comboBoxBarcodeTypes.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;comboBoxBarcodeTypes.Name" xml:space="preserve">
    <value>comboBoxBarcodeTypes</value>
  </data>
  <data name="&gt;&gt;comboBoxBarcodeTypes.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxBarcodeTypes.Parent" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;comboBoxBarcodeTypes.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="dcsAlignmentProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>201, 167</value>
  </data>
  <data name="dcsAlignmentProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 89</value>
  </data>
  <data name="dcsAlignmentProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;dcsAlignmentProperties1.Name" xml:space="preserve">
    <value>dcsAlignmentProperties1</value>
  </data>
  <data name="&gt;&gt;dcsAlignmentProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSAlignmentProperties, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsAlignmentProperties1.Parent" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;dcsAlignmentProperties1.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="dcsFontProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>18, 34</value>
  </data>
  <data name="dcsFontProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>159, 231</value>
  </data>
  <data name="dcsFontProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;dcsFontProperties1.Name" xml:space="preserve">
    <value>dcsFontProperties1</value>
  </data>
  <data name="&gt;&gt;dcsFontProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSFontProperties, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsFontProperties1.Parent" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;dcsFontProperties1.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="tabPage1.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tabPage1.Size" type="System.Drawing.Size, System.Drawing">
    <value>400, 269</value>
  </data>
  <data name="tabPage1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="tabPage1.Text" xml:space="preserve">
    <value>Foreground</value>
  </data>
  <data name="&gt;&gt;tabPage1.Name" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;tabPage1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabPage1.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabPage1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="dcsBackGroundProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 16</value>
  </data>
  <data name="dcsBackGroundProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>217, 165</value>
  </data>
  <data name="dcsBackGroundProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>65</value>
  </data>
  <data name="&gt;&gt;dcsBackGroundProperties1.Name" xml:space="preserve">
    <value>dcsBackGroundProperties1</value>
  </data>
  <data name="&gt;&gt;dcsBackGroundProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSBackGroundProperties, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsBackGroundProperties1.Parent" xml:space="preserve">
    <value>tabPage2</value>
  </data>
  <data name="&gt;&gt;dcsBackGroundProperties1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="tabPage2.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tabPage2.Size" type="System.Drawing.Size, System.Drawing">
    <value>400, 269</value>
  </data>
  <data name="tabPage2.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="tabPage2.Text" xml:space="preserve">
    <value>Background</value>
  </data>
  <data name="&gt;&gt;tabPage2.Name" xml:space="preserve">
    <value>tabPage2</value>
  </data>
  <data name="&gt;&gt;tabPage2.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabPage2.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabPage2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="dcsFrameProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 32</value>
  </data>
  <data name="dcsFrameProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>176, 112</value>
  </data>
  <data name="dcsFrameProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>45</value>
  </data>
  <data name="&gt;&gt;dcsFrameProperties1.Name" xml:space="preserve">
    <value>dcsFrameProperties1</value>
  </data>
  <data name="&gt;&gt;dcsFrameProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSFrameProperties1, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsFrameProperties1.Parent" xml:space="preserve">
    <value>tabPage3</value>
  </data>
  <data name="&gt;&gt;dcsFrameProperties1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="tabPage3.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tabPage3.Size" type="System.Drawing.Size, System.Drawing">
    <value>400, 269</value>
  </data>
  <data name="tabPage3.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="tabPage3.Text" xml:space="preserve">
    <value>Frame outline</value>
  </data>
  <data name="&gt;&gt;tabPage3.Name" xml:space="preserve">
    <value>tabPage3</value>
  </data>
  <data name="&gt;&gt;tabPage3.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabPage3.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabPage3.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="checkBoxLabeledText.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBoxLabeledText.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkBoxLabeledText.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBoxLabeledText.Location" type="System.Drawing.Point, System.Drawing">
    <value>38, 19</value>
  </data>
  <data name="checkBoxLabeledText.Size" type="System.Drawing.Size, System.Drawing">
    <value>194, 18</value>
  </data>
  <data name="checkBoxLabeledText.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="checkBoxLabeledText.Text" xml:space="preserve">
    <value>Include a label with this text object</value>
  </data>
  <data name="&gt;&gt;checkBoxLabeledText.Name" xml:space="preserve">
    <value>checkBoxLabeledText</value>
  </data>
  <data name="&gt;&gt;checkBoxLabeledText.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBoxLabeledText.Parent" xml:space="preserve">
    <value>tabPage4</value>
  </data>
  <data name="&gt;&gt;checkBoxLabeledText.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="comboBoxLabelOrientation.Items" xml:space="preserve">
    <value>Left</value>
  </data>
  <data name="comboBoxLabelOrientation.Items1" xml:space="preserve">
    <value>Right</value>
  </data>
  <data name="comboBoxLabelOrientation.Items2" xml:space="preserve">
    <value>Top</value>
  </data>
  <data name="comboBoxLabelOrientation.Items3" xml:space="preserve">
    <value>Bottom</value>
  </data>
  <data name="comboBoxLabelOrientation.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 94</value>
  </data>
  <data name="comboBoxLabelOrientation.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 21</value>
  </data>
  <data name="comboBoxLabelOrientation.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="comboBoxLabelOrientation.Text" xml:space="preserve">
    <value>Top</value>
  </data>
  <data name="&gt;&gt;comboBoxLabelOrientation.Name" xml:space="preserve">
    <value>comboBoxLabelOrientation</value>
  </data>
  <data name="&gt;&gt;comboBoxLabelOrientation.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxLabelOrientation.Parent" xml:space="preserve">
    <value>groupBoxLabeledText</value>
  </data>
  <data name="&gt;&gt;comboBoxLabelOrientation.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="comboBoxLabelFont.Items" xml:space="preserve">
    <value>Label font</value>
  </data>
  <data name="comboBoxLabelFont.Items1" xml:space="preserve">
    <value>Doc font 1</value>
  </data>
  <data name="comboBoxLabelFont.Items2" xml:space="preserve">
    <value>Doc font 2</value>
  </data>
  <data name="comboBoxLabelFont.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 64</value>
  </data>
  <data name="comboBoxLabelFont.Size" type="System.Drawing.Size, System.Drawing">
    <value>89, 21</value>
  </data>
  <data name="comboBoxLabelFont.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="comboBoxLabelFont.Text" xml:space="preserve">
    <value>Label font</value>
  </data>
  <data name="&gt;&gt;comboBoxLabelFont.Name" xml:space="preserve">
    <value>comboBoxLabelFont</value>
  </data>
  <data name="&gt;&gt;comboBoxLabelFont.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxLabelFont.Parent" xml:space="preserve">
    <value>groupBoxLabeledText</value>
  </data>
  <data name="&gt;&gt;comboBoxLabelFont.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelLabelFont.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelLabelFont.Location" type="System.Drawing.Point, System.Drawing">
    <value>27, 64</value>
  </data>
  <data name="labelLabelFont.Size" type="System.Drawing.Size, System.Drawing">
    <value>95, 16</value>
  </data>
  <data name="labelLabelFont.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="labelLabelFont.Text" xml:space="preserve">
    <value>Label font</value>
  </data>
  <data name="&gt;&gt;labelLabelFont.Name" xml:space="preserve">
    <value>labelLabelFont</value>
  </data>
  <data name="&gt;&gt;labelLabelFont.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelLabelFont.Parent" xml:space="preserve">
    <value>groupBoxLabeledText</value>
  </data>
  <data name="&gt;&gt;labelLabelFont.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="labelLabelOffset.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelLabelOffset.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 125</value>
  </data>
  <data name="labelLabelOffset.Size" type="System.Drawing.Size, System.Drawing">
    <value>95, 16</value>
  </data>
  <data name="labelLabelOffset.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="labelLabelOffset.Text" xml:space="preserve">
    <value>Label height</value>
  </data>
  <data name="&gt;&gt;labelLabelOffset.Name" xml:space="preserve">
    <value>labelLabelOffset</value>
  </data>
  <data name="&gt;&gt;labelLabelOffset.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelLabelOffset.Parent" xml:space="preserve">
    <value>groupBoxLabeledText</value>
  </data>
  <data name="&gt;&gt;labelLabelOffset.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="unitizedNumberBoxLabelOffset.Location" type="System.Drawing.Point, System.Drawing">
    <value>129, 125</value>
  </data>
  <data name="unitizedNumberBoxLabelOffset.Size" type="System.Drawing.Size, System.Drawing">
    <value>71, 22</value>
  </data>
  <data name="unitizedNumberBoxLabelOffset.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;unitizedNumberBoxLabelOffset.Name" xml:space="preserve">
    <value>unitizedNumberBoxLabelOffset</value>
  </data>
  <data name="&gt;&gt;unitizedNumberBoxLabelOffset.Type" xml:space="preserve">
    <value>DCSSDK_Utilities.UnitizedNumberBox, DCSSDK_Utilities, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;unitizedNumberBoxLabelOffset.Parent" xml:space="preserve">
    <value>groupBoxLabeledText</value>
  </data>
  <data name="&gt;&gt;unitizedNumberBoxLabelOffset.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="labelLabelOrientation.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelLabelOrientation.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 94</value>
  </data>
  <data name="labelLabelOrientation.Size" type="System.Drawing.Size, System.Drawing">
    <value>95, 16</value>
  </data>
  <data name="labelLabelOrientation.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="labelLabelOrientation.Text" xml:space="preserve">
    <value>Label alignment</value>
  </data>
  <data name="&gt;&gt;labelLabelOrientation.Name" xml:space="preserve">
    <value>labelLabelOrientation</value>
  </data>
  <data name="&gt;&gt;labelLabelOrientation.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelLabelOrientation.Parent" xml:space="preserve">
    <value>groupBoxLabeledText</value>
  </data>
  <data name="&gt;&gt;labelLabelOrientation.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="labelLabelText.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelLabelText.Location" type="System.Drawing.Point, System.Drawing">
    <value>27, 30</value>
  </data>
  <data name="labelLabelText.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 16</value>
  </data>
  <data name="labelLabelText.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelLabelText.Text" xml:space="preserve">
    <value>Label text</value>
  </data>
  <data name="&gt;&gt;labelLabelText.Name" xml:space="preserve">
    <value>labelLabelText</value>
  </data>
  <data name="&gt;&gt;labelLabelText.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelLabelText.Parent" xml:space="preserve">
    <value>groupBoxLabeledText</value>
  </data>
  <data name="&gt;&gt;labelLabelText.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="textBoxLabelText.Location" type="System.Drawing.Point, System.Drawing">
    <value>106, 30</value>
  </data>
  <data name="textBoxLabelText.Size" type="System.Drawing.Size, System.Drawing">
    <value>111, 20</value>
  </data>
  <data name="textBoxLabelText.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="textBoxLabelText.Text" xml:space="preserve">
    <value>Label text</value>
  </data>
  <data name="&gt;&gt;textBoxLabelText.Name" xml:space="preserve">
    <value>textBoxLabelText</value>
  </data>
  <data name="&gt;&gt;textBoxLabelText.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxLabelText.Parent" xml:space="preserve">
    <value>groupBoxLabeledText</value>
  </data>
  <data name="&gt;&gt;textBoxLabelText.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="groupBoxLabeledText.Location" type="System.Drawing.Point, System.Drawing">
    <value>38, 42</value>
  </data>
  <data name="groupBoxLabeledText.Size" type="System.Drawing.Size, System.Drawing">
    <value>234, 186</value>
  </data>
  <data name="groupBoxLabeledText.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="groupBoxLabeledText.Text" xml:space="preserve">
    <value>Labeled Text Parameters</value>
  </data>
  <data name="&gt;&gt;groupBoxLabeledText.Name" xml:space="preserve">
    <value>groupBoxLabeledText</value>
  </data>
  <data name="&gt;&gt;groupBoxLabeledText.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBoxLabeledText.Parent" xml:space="preserve">
    <value>tabPage4</value>
  </data>
  <data name="&gt;&gt;groupBoxLabeledText.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="tabPage4.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tabPage4.Size" type="System.Drawing.Size, System.Drawing">
    <value>400, 269</value>
  </data>
  <data name="tabPage4.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="tabPage4.Text" xml:space="preserve">
    <value>Label</value>
  </data>
  <data name="&gt;&gt;tabPage4.Name" xml:space="preserve">
    <value>tabPage4</value>
  </data>
  <data name="&gt;&gt;tabPage4.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabPage4.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabPage4.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="tabControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 149</value>
  </data>
  <data name="tabControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>408, 295</value>
  </data>
  <data name="tabControl1.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;tabControl1.Name" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabControl1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabControl, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tabControl1.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="buttonLockAspectRatio.ImageIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <metadata name="imageList1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="imageList1.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj0yLjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAADw
        BwAAAk1TRnQBSQFMAgEBAgEAAQwBAAEMAQABEAEAARABAAT/AQkBAAj/AUIBTQE2AQQGAAE2AQQCAAEo
        AwABQAMAARADAAEBAQABCAYAAQQYAAGAAgABgAMAAoABAAGAAwABgAEAAYABAAKAAgADwAEAAcAB3AHA
        AQAB8AHKAaYBAAEzBQABMwEAATMBAAEzAQACMwIAAxYBAAMcAQADIgEAAykBAANVAQADTQEAA0IBAAM5
        AQABgAF8Af8BAAJQAf8BAAGTAQAB1gEAAf8B7AHMAQABxgHWAe8BAAHWAucBAAGQAakBrQIAAf8BMwMA
        AWYDAAGZAwABzAIAATMDAAIzAgABMwFmAgABMwGZAgABMwHMAgABMwH/AgABZgMAAWYBMwIAAmYCAAFm
        AZkCAAFmAcwCAAFmAf8CAAGZAwABmQEzAgABmQFmAgACmQIAAZkBzAIAAZkB/wIAAcwDAAHMATMCAAHM
        AWYCAAHMAZkCAALMAgABzAH/AgAB/wFmAgAB/wGZAgAB/wHMAQABMwH/AgAB/wEAATMBAAEzAQABZgEA
        ATMBAAGZAQABMwEAAcwBAAEzAQAB/wEAAf8BMwIAAzMBAAIzAWYBAAIzAZkBAAIzAcwBAAIzAf8BAAEz
        AWYCAAEzAWYBMwEAATMCZgEAATMBZgGZAQABMwFmAcwBAAEzAWYB/wEAATMBmQIAATMBmQEzAQABMwGZ
        AWYBAAEzApkBAAEzAZkBzAEAATMBmQH/AQABMwHMAgABMwHMATMBAAEzAcwBZgEAATMBzAGZAQABMwLM
        AQABMwHMAf8BAAEzAf8BMwEAATMB/wFmAQABMwH/AZkBAAEzAf8BzAEAATMC/wEAAWYDAAFmAQABMwEA
        AWYBAAFmAQABZgEAAZkBAAFmAQABzAEAAWYBAAH/AQABZgEzAgABZgIzAQABZgEzAWYBAAFmATMBmQEA
        AWYBMwHMAQABZgEzAf8BAAJmAgACZgEzAQADZgEAAmYBmQEAAmYBzAEAAWYBmQIAAWYBmQEzAQABZgGZ
        AWYBAAFmApkBAAFmAZkBzAEAAWYBmQH/AQABZgHMAgABZgHMATMBAAFmAcwBmQEAAWYCzAEAAWYBzAH/
        AQABZgH/AgABZgH/ATMBAAFmAf8BmQEAAWYB/wHMAQABzAEAAf8BAAH/AQABzAEAApkCAAGZATMBmQEA
        AZkBAAGZAQABmQEAAcwBAAGZAwABmQIzAQABmQEAAWYBAAGZATMBzAEAAZkBAAH/AQABmQFmAgABmQFm
        ATMBAAGZATMBZgEAAZkBZgGZAQABmQFmAcwBAAGZATMB/wEAApkBMwEAApkBZgEAA5kBAAKZAcwBAAKZ
        Af8BAAGZAcwCAAGZAcwBMwEAAWYBzAFmAQABmQHMAZkBAAGZAswBAAGZAcwB/wEAAZkB/wIAAZkB/wEz
        AQABmQHMAWYBAAGZAf8BmQEAAZkB/wHMAQABmQL/AQABzAMAAZkBAAEzAQABzAEAAWYBAAHMAQABmQEA
        AcwBAAHMAQABmQEzAgABzAIzAQABzAEzAWYBAAHMATMBmQEAAcwBMwHMAQABzAEzAf8BAAHMAWYCAAHM
        AWYBMwEAAZkCZgEAAcwBZgGZAQABzAFmAcwBAAGZAWYB/wEAAcwBmQIAAcwBmQEzAQABzAGZAWYBAAHM
        ApkBAAHMAZkBzAEAAcwBmQH/AQACzAIAAswBMwEAAswBZgEAAswBmQEAA8wBAALMAf8BAAHMAf8CAAHM
        Af8BMwEAAZkB/wFmAQABzAH/AZkBAAHMAf8BzAEAAcwC/wEAAcwBAAEzAQAB/wEAAWYBAAH/AQABmQEA
        AcwBMwIAAf8CMwEAAf8BMwFmAQAB/wEzAZkBAAH/ATMBzAEAAf8BMwH/AQAB/wFmAgAB/wFmATMBAAHM
        AmYBAAH/AWYBmQEAAf8BZgHMAQABzAFmAf8BAAH/AZkCAAH/AZkBMwEAAf8BmQFmAQAB/wKZAQAB/wGZ
        AcwBAAH/AZkB/wEAAf8BzAIAAf8BzAEzAQAB/wHMAWYBAAH/AcwBmQEAAf8CzAEAAf8BzAH/AQAC/wEz
        AQABzAH/AWYBAAL/AZkBAAL/AcwBAAJmAf8BAAFmAf8BZgEAAWYC/wEAAf8CZgEAAf8BZgH/AQAC/wFm
        AQABIQEAAaUBAANfAQADdwEAA4YBAAOWAQADywEAA7IBAAPXAQAD3QEAA+MBAAPqAQAD8QEAA/gBAAHw
        AfsB/wEAAaQCoAEAA4ADAAH/AgAB/wMAAv8BAAH/AwAB/wEAAf8BAAL/AgAD/wEAIAcgAAUHCIYBrgcH
        CIYBrgIHIAAFBwEEBe0CBAGuBwcBBAXtAgQBrgIHIAAFBwIEBfcBBAGuBwcCBAX3AQQBrgIHIAAFBwEE
        BYYCBAGuBwcBBAWGAgQBrgIHIAAFBwEEBoYBBAGuBwcBBAaGAQQBrgIHIAAFBwgEAa4HBwgEAa4CByAA
        BgcBBAGuDgcBBAGuAwcBBAQHIAAGBwEEAa4OBwEEAa4DBwEEBAcgAAYHAQQBrg4HAQQBrgMHAQQEByAA
        BgcBBAGuDgcBBAGuAwcBBAQHIAAHBwEEAvcBBAGuCwcBBAL3AQQBrgQHIAAHBwGGAwQB9wsHAYYDBAH3
        BAcgACAHIAAgByAAIAcgAAFCAU0BPgcAAT4DAAEoAwABQAMAARADAAEBAQABAQUAAYAXAAP/gQAL
</value>
  </data>
  <data name="buttonLockAspectRatio.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonLockAspectRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>588, 144</value>
  </data>
  <data name="buttonLockAspectRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>26, 25</value>
  </data>
  <data name="buttonLockAspectRatio.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;buttonLockAspectRatio.Name" xml:space="preserve">
    <value>buttonLockAspectRatio</value>
  </data>
  <data name="&gt;&gt;buttonLockAspectRatio.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonLockAspectRatio.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonLockAspectRatio.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="tbRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>508, 144</value>
  </data>
  <data name="tbRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 20</value>
  </data>
  <data name="tbRatio.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="tbRatio.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="tbRatio.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="&gt;&gt;tbRatio.Name" xml:space="preserve">
    <value>tbRatio</value>
  </data>
  <data name="&gt;&gt;tbRatio.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbRatio.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbRatio.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="label2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>436, 144</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 16</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>w/h ratio</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="buttonRotateCCW.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonRotateCCW.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonRotateCCW.Location" type="System.Drawing.Point, System.Drawing">
    <value>470, 276</value>
  </data>
  <data name="buttonRotateCCW.Size" type="System.Drawing.Size, System.Drawing">
    <value>65, 24</value>
  </data>
  <data name="buttonRotateCCW.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="buttonRotateCCW.Text" xml:space="preserve">
    <value>-90</value>
  </data>
  <data name="&gt;&gt;buttonRotateCCW.Name" xml:space="preserve">
    <value>buttonRotateCCW</value>
  </data>
  <data name="&gt;&gt;buttonRotateCCW.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonRotateCCW.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonRotateCCW.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="buttonRotateCW.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonRotateCW.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonRotateCW.Location" type="System.Drawing.Point, System.Drawing">
    <value>543, 276</value>
  </data>
  <data name="buttonRotateCW.Size" type="System.Drawing.Size, System.Drawing">
    <value>65, 24</value>
  </data>
  <data name="buttonRotateCW.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="buttonRotateCW.Text" xml:space="preserve">
    <value>+90</value>
  </data>
  <data name="&gt;&gt;buttonRotateCW.Name" xml:space="preserve">
    <value>buttonRotateCW</value>
  </data>
  <data name="&gt;&gt;buttonRotateCW.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonRotateCW.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonRotateCW.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <metadata name="toolTip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>122, 17</value>
  </metadata>
  <data name="labelPanel.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelPanel.Location" type="System.Drawing.Point, System.Drawing">
    <value>436, 173</value>
  </data>
  <data name="labelPanel.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 16</value>
  </data>
  <data name="labelPanel.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="labelPanel.Text" xml:space="preserve">
    <value>panel</value>
  </data>
  <data name="&gt;&gt;labelPanel.Name" xml:space="preserve">
    <value>labelPanel</value>
  </data>
  <data name="&gt;&gt;labelPanel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelPanel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelPanel.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="comboBoxPanel.Items" xml:space="preserve">
    <value>default</value>
  </data>
  <data name="comboBoxPanel.Items1" xml:space="preserve">
    <value>K Panel</value>
  </data>
  <data name="comboBoxPanel.Location" type="System.Drawing.Point, System.Drawing">
    <value>508, 170</value>
  </data>
  <data name="comboBoxPanel.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 21</value>
  </data>
  <data name="comboBoxPanel.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="comboBoxPanel.Text" xml:space="preserve">
    <value>default</value>
  </data>
  <data name="&gt;&gt;comboBoxPanel.Name" xml:space="preserve">
    <value>comboBoxPanel</value>
  </data>
  <data name="&gt;&gt;comboBoxPanel.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxPanel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;comboBoxPanel.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="dcsSourceProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 8</value>
  </data>
  <data name="dcsSourceProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>328, 146</value>
  </data>
  <data name="dcsSourceProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;dcsSourceProperties1.Name" xml:space="preserve">
    <value>dcsSourceProperties1</value>
  </data>
  <data name="&gt;&gt;dcsSourceProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSSourceProperties, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsSourceProperties1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;dcsSourceProperties1.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="dcsPositionSizeProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>436, 8</value>
  </data>
  <data name="dcsPositionSizeProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 128</value>
  </data>
  <data name="dcsPositionSizeProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;dcsPositionSizeProperties1.Name" xml:space="preserve">
    <value>dcsPositionSizeProperties1</value>
  </data>
  <data name="&gt;&gt;dcsPositionSizeProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSPositionSizeProperties, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsPositionSizeProperties1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;dcsPositionSizeProperties1.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>40</value>
  </metadata>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>634, 448</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterParent</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Text Object Properties ---</value>
  </data>
  <data name="&gt;&gt;imageList1.Name" xml:space="preserve">
    <value>imageList1</value>
  </data>
  <data name="&gt;&gt;imageList1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ImageList, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolTip1.Name" xml:space="preserve">
    <value>toolTip1</value>
  </data>
  <data name="&gt;&gt;toolTip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolTip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>TextObjProperties</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>