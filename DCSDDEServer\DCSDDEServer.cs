using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;
using System.Data;
using System.Text;
using System.Runtime.InteropServices;
using System.Diagnostics;
using DCSDEV;
using DCSSDK;
using DCSSDK.CaptureMgt;

namespace DCSDEV.DDEServer
{
	/// <summary>
	/// Summary description for DCSDDEServer.
	/// </summary>
	public class DDEServer : System.Windows.Forms.Form
	{
		[DllImport("kernel32.dll", EntryPoint = "GlobalLock", ExactSpelling = true)]
		internal static extern string GlobalLockString(IntPtr handle);

		[DllImport("kernel32.dll", EntryPoint = "GlobalLock", ExactSpelling = true)]
		internal static extern StringBuilder GlobalLockStringBuilder(IntPtr handle);

		[DllImport("kernel32.dll", ExactSpelling = true)]
		internal static extern IntPtr GlobalAlloc(int flags, int size);
		[DllImport("kernel32.dll", ExactSpelling = true)]
		internal static extern IntPtr GlobalLock(IntPtr handle);
		[DllImport("kernel32.dll", ExactSpelling = true)]
		internal static extern IntPtr GlobalFree(IntPtr handle);
		[DllImport("kernel32.dll", ExactSpelling = true)]
		internal static extern bool GlobalUnlock(IntPtr handle);

		[DllImport("User32.dll")]
		static extern int SendMessage(IntPtr to, int msg, IntPtr from, IntPtr lParam);
		[DllImport("User32.dll")]
		static extern int PostMessage(IntPtr to, int msg, IntPtr from, IntPtr lParam);

		[DllImport("Kernel32.dll")]
		static extern uint GlobalGetAtomName(IntPtr hAtom, StringBuilder buffer, int len);
		[DllImport("Kernel32.dll")]
		static extern IntPtr GlobalAddAtom(string txt);
		[DllImport("User32.dll")]
		static extern IntPtr PackDDElParam(int msg, IntPtr Lo, IntPtr Hi);
		[DllImport("User32.dll")]
		static extern bool UnpackDDElParam(int msg, IntPtr lParam, out IntPtr Lo, out IntPtr Hi);

		[DllImport("User32.dll")]
		static extern IntPtr SetFocus(IntPtr hWnd);

		[DllImport("User32.dll")]
		static extern IntPtr GetForegroundWindow();

		[DllImport("User32.dll")]
		static extern IntPtr SetForegroundWindow(IntPtr hWnd);

		[DllImport("User32.dll")]
		static extern IntPtr FindWindow(string ClassName, string WindowName);

		[DllImport("User32.dll")]
		static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

		[DllImport("user32")]
		public static extern int SetParent(IntPtr hWndChild, IntPtr hWndNewParent);

		private struct DDEExeCmd
		{
			public string strCommand;
			public int numRequiredTokens;
			public string strArgs;
			public DDEExeCmd(string str, int i, string args)
			{
				strCommand = str;
				numRequiredTokens = i;
				strArgs = args;
			}
		}

		private const int NEGATIVE_ACK = 0x0000;
		private const int POSITIVE_ACK = 0x8001;

		private bool m_bCurrentPrintIsDirect = false;
		private bool m_bCurrentIsScanIDType = false;
		private string m_strLastChipID = null;
		private int m_iLastPrinterRefNo = 0;

		private bool m_bExecPending = false;    // If an EXEC execute LAYOUT or SETUP command is still pending.
		private bool m_bKillPending = false;
		private int m_DDEExecutePendingCount = 0;
		private Message m_PendingMessage;
		private enum PendingRequest
		{
			PENDING_NONE, PENDING_PRINT, PENDING_TAKE, PENDING_ENCODECHIP, PENDING_SCANDATA, PENDING_GENBIO, PENDING_SEARCHBIO, PENDING_VERIFYBIO
		};
		private PendingRequest m_ePendingRequest;

		private enum CaptureStates
		{
			CAPTURE_NONE, CAPTURE_PENDING, CAPTURE_OK, CAPTURE_CANCEL
		};
		private CaptureStates m_eCaptureState = CaptureStates.CAPTURE_NONE;

		private enum PrintStates
		{
			PRINT_NONE, PRINT_PENDING, PRINT_OK, PRINT_CANCEL
		};
		private PrintStates m_ePrintState = PrintStates.PRINT_NONE;

		private enum GenBiometricStates
		{
			GENBIO_NONE, GENBIO_PENDING, GENBIO_OK, GENBIO_CANCEL, GENBIO_ERROR
			// CANCEL is finished but no biometric images existed for generation
		};
		private GenBiometricStates m_eGenBiometricState = GenBiometricStates.GENBIO_NONE;

		private enum SearchBiometricStates
		{
			SEARCHBIO_NONE, SEARCHBIO_PENDING, SEARCHBIO_OK, SEARCHBIO_CANCEL, SEARCHBIO_ERROR
		};
		private SearchBiometricStates m_eSearchBiometricState = SearchBiometricStates.SEARCHBIO_NONE;
		private string m_strSearchResult;

		private enum VerifyBiometricStates
		{
			VERIFYBIO_NONE, VERIFYBIO_PENDING, VERIFYBIO_OK, VERIFYBIO_CANCEL, VERIFYBIO_ERROR
		};
		private VerifyBiometricStates m_eVerifyBiometricState = VerifyBiometricStates.VERIFYBIO_NONE;
		private string m_strVerifyResult;

		private enum EncodeChipStates
		{
			ENCODECHIP_NONE, ENCODECHIP_PENDING, ENCODECHIP_OK, ENCODECHIP_CANCEL, ENCODECHIP_ERROR
		};
		private EncodeChipStates m_eEncodeChipState = EncodeChipStates.ENCODECHIP_NONE;

		private enum ScanDataStates
		{
			SCANDATA_NONE, SCANDATA_PENDING, SCANDATA_OK, SCANDATA_CANCEL, SCANDATA_ERROR
		};
		private ScanDataStates m_eScanDataState = ScanDataStates.SCANDATA_NONE;

		private DCSSDK.CaptureMgt.DCSSDK_CaptureMgt m_dlgCaptureMgt;
		private DCSSDK.BadgingMgt.DCSSDK_BadgingMgt m_dlgBadgingMgt;
		private DCSDEV.DDEServer.DCSSetup m_dlgSetup;

		private bool m_bShowTestControls = true;

		private bool m_bStillStartingUp = true;
		private int m_iDDEMethod = 1;
		//private int m_iTicks = 0;

		private System.Windows.Forms.Button buttonExit;
		private System.ComponentModel.IContainer components;
		private System.Windows.Forms.GroupBox groupBoxTestControls;
		private System.Windows.Forms.ComboBox cbBadgeDat;
		private System.Windows.Forms.Label label6;
		private System.Windows.Forms.Button buttonClose;
		private System.Windows.Forms.TextBox tbImageIDDisplay;
		private System.Windows.Forms.TextBox tbImageID;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.ComboBox cbImageClassDisplay;
		private System.Windows.Forms.Button buttonDisplay;
		private System.Windows.Forms.Label label5;
		private System.Windows.Forms.Label label4;
		private System.Windows.Forms.Button buttonPreview;
		private System.Windows.Forms.Button buttonPrint;
		private System.Windows.Forms.Button buttonLayout;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.ComboBox cbImageClass;
		private System.Windows.Forms.Button buttonAllCapture;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.Button buttonSetup;
		private System.Windows.Forms.CheckBox checkBoxShowTest;
		private System.Windows.Forms.Button buttonAbout;
		private System.Windows.Forms.Button buttonMinimize;
		private System.Windows.Forms.TextBox tbImageTitle;
		private System.Windows.Forms.Label label7;
		private Button buttonStartPrinter;
		private Button buttonReadFips;
		private Button buttonGenFips;
		private Label label8;
		private CheckBox checkBoxRetake;
		private System.Windows.Forms.Timer timer1;

		public DDEServer()
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			m_bStillStartingUp = true;
			DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("DCSSDK_Mgt");
			Rectangle rectPtr = ps.GetRectParameter("DDEServerRect", Rectangle.Empty);
			if (rectPtr != Rectangle.Empty)
			{
				Rectangle rectScreen = System.Windows.Forms.Screen.GetBounds(this);
				rectScreen.Intersect(rectPtr);
				if (rectScreen.Equals(rectPtr)) this.Location = rectPtr.Location;
			}
			this.cbImageClass.Text = "All";
			this.cbImageClassDisplay.Text = "All";

			try
			{
				m_dlgCaptureMgt = new DCSSDK.CaptureMgt.DCSSDK_CaptureMgt();
			}
			catch (Exception ex)
			{
				MessageBox.Show("ID Services ERROR DCSSDK_CaptureMgt: " + ex.Message, "ID Services", MessageBoxButtons.OK, MessageBoxIcon.Stop);
				throw ex;
			}
			try
			{
				m_dlgBadgingMgt = new DCSSDK.BadgingMgt.DCSSDK_BadgingMgt();
			}
			catch (Exception ex)
			{
				MessageBox.Show("ID Services ERROR DCSSDK_BadgingMgt: " + ex.Message, "ID Services", MessageBoxButtons.OK, MessageBoxIcon.Stop);
				throw ex;
			}
			try
			{
				m_dlgSetup = new DCSSetup(this.m_dlgCaptureMgt, this.m_dlgBadgingMgt);
			}
			catch (Exception ex)
			{
				MessageBox.Show("ID Services ERROR DCSSetup: " + ex.Message, "ID Services", MessageBoxButtons.OK, MessageBoxIcon.Stop);
				throw ex;
			}

			m_bShowTestControls = ps.GetBoolParameter("ShowTestControls", true);
			if (!m_bShowTestControls)
			{
				this.checkBoxShowTest.Visible = false;
				this.checkBoxShowTest.Checked = false;
				this.groupBoxTestControls.Visible = false;
				this.Width = 320;
				this.Height = 120;
			}
			else
			{
				this.groupBoxTestControls.Visible = this.checkBoxShowTest.Checked;
				if (this.checkBoxShowTest.Checked)
				{
					this.Width = 640;
					this.Height = 480;
				}
				else
				{
					this.Width = 320;
					this.Height = 120;
				}
			}
			DCSDEV.DCSMsg.Log("DDEServer startup");

			m_bStillStartingUp = false;
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose(bool disposing)
		{
			if (disposing)
			{
				if (components != null)
				{
					components.Dispose();
				}
				if (m_dlgSetup != null) m_dlgSetup.Dispose();
				if (m_dlgCaptureMgt != null) m_dlgCaptureMgt.Dispose();
				if (m_dlgBadgingMgt != null) m_dlgBadgingMgt.Dispose();
			}
			base.Dispose(disposing);
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(DDEServer));
            this.buttonExit = new System.Windows.Forms.Button();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.groupBoxTestControls = new System.Windows.Forms.GroupBox();
            this.checkBoxRetake = new System.Windows.Forms.CheckBox();
            this.label8 = new System.Windows.Forms.Label();
            this.buttonReadFips = new System.Windows.Forms.Button();
            this.buttonGenFips = new System.Windows.Forms.Button();
            this.tbImageTitle = new System.Windows.Forms.TextBox();
            this.label7 = new System.Windows.Forms.Label();
            this.cbBadgeDat = new System.Windows.Forms.ComboBox();
            this.label6 = new System.Windows.Forms.Label();
            this.buttonClose = new System.Windows.Forms.Button();
            this.tbImageIDDisplay = new System.Windows.Forms.TextBox();
            this.tbImageID = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.cbImageClassDisplay = new System.Windows.Forms.ComboBox();
            this.buttonDisplay = new System.Windows.Forms.Button();
            this.label5 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.buttonPreview = new System.Windows.Forms.Button();
            this.buttonPrint = new System.Windows.Forms.Button();
            this.buttonLayout = new System.Windows.Forms.Button();
            this.label3 = new System.Windows.Forms.Label();
            this.cbImageClass = new System.Windows.Forms.ComboBox();
            this.buttonAllCapture = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            this.buttonSetup = new System.Windows.Forms.Button();
            this.buttonStartPrinter = new System.Windows.Forms.Button();
            this.checkBoxShowTest = new System.Windows.Forms.CheckBox();
            this.buttonAbout = new System.Windows.Forms.Button();
            this.buttonMinimize = new System.Windows.Forms.Button();
            this.groupBoxTestControls.SuspendLayout();
            this.SuspendLayout();
            // 
            // buttonExit
            // 
            resources.ApplyResources(this.buttonExit, "buttonExit");
            this.buttonExit.Name = "buttonExit";
            this.buttonExit.Click += new System.EventHandler(this.buttonExit_Click);
            // 
            // timer1
            // 
            this.timer1.Interval = 5000;
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // groupBoxTestControls
            // 
            this.groupBoxTestControls.Controls.Add(this.checkBoxRetake);
            this.groupBoxTestControls.Controls.Add(this.label8);
            this.groupBoxTestControls.Controls.Add(this.buttonReadFips);
            this.groupBoxTestControls.Controls.Add(this.buttonGenFips);
            this.groupBoxTestControls.Controls.Add(this.tbImageTitle);
            this.groupBoxTestControls.Controls.Add(this.label7);
            this.groupBoxTestControls.Controls.Add(this.cbBadgeDat);
            this.groupBoxTestControls.Controls.Add(this.label6);
            this.groupBoxTestControls.Controls.Add(this.buttonClose);
            this.groupBoxTestControls.Controls.Add(this.tbImageIDDisplay);
            this.groupBoxTestControls.Controls.Add(this.tbImageID);
            this.groupBoxTestControls.Controls.Add(this.label2);
            this.groupBoxTestControls.Controls.Add(this.cbImageClassDisplay);
            this.groupBoxTestControls.Controls.Add(this.buttonDisplay);
            this.groupBoxTestControls.Controls.Add(this.label5);
            this.groupBoxTestControls.Controls.Add(this.label4);
            this.groupBoxTestControls.Controls.Add(this.buttonPreview);
            this.groupBoxTestControls.Controls.Add(this.buttonPrint);
            this.groupBoxTestControls.Controls.Add(this.buttonLayout);
            this.groupBoxTestControls.Controls.Add(this.label3);
            this.groupBoxTestControls.Controls.Add(this.cbImageClass);
            this.groupBoxTestControls.Controls.Add(this.buttonAllCapture);
            this.groupBoxTestControls.Controls.Add(this.label1);
            this.groupBoxTestControls.Controls.Add(this.buttonSetup);
            this.groupBoxTestControls.FlatStyle = System.Windows.Forms.FlatStyle.System;
            resources.ApplyResources(this.groupBoxTestControls, "groupBoxTestControls");
            this.groupBoxTestControls.Name = "groupBoxTestControls";
            this.groupBoxTestControls.TabStop = false;
            // 
            // checkBoxRetake
            // 
            resources.ApplyResources(this.checkBoxRetake, "checkBoxRetake");
            this.checkBoxRetake.Name = "checkBoxRetake";
            this.checkBoxRetake.UseVisualStyleBackColor = true;
            // 
            // label8
            // 
            resources.ApplyResources(this.label8, "label8");
            this.label8.Name = "label8";
            // 
            // buttonReadFips
            // 
            resources.ApplyResources(this.buttonReadFips, "buttonReadFips");
            this.buttonReadFips.Name = "buttonReadFips";
            this.buttonReadFips.Click += new System.EventHandler(this.buttonReadFips_Click);
            // 
            // buttonGenFips
            // 
            resources.ApplyResources(this.buttonGenFips, "buttonGenFips");
            this.buttonGenFips.Name = "buttonGenFips";
            this.buttonGenFips.Click += new System.EventHandler(this.buttonGenFips_Click);
            // 
            // tbImageTitle
            // 
            resources.ApplyResources(this.tbImageTitle, "tbImageTitle");
            this.tbImageTitle.Name = "tbImageTitle";
            // 
            // label7
            // 
            resources.ApplyResources(this.label7, "label7");
            this.label7.Name = "label7";
            // 
            // cbBadgeDat
            // 
            this.cbBadgeDat.Items.AddRange(new object[] {
            resources.GetString("cbBadgeDat.Items"),
            resources.GetString("cbBadgeDat.Items1"),
            resources.GetString("cbBadgeDat.Items2"),
            resources.GetString("cbBadgeDat.Items3"),
            resources.GetString("cbBadgeDat.Items4")});
            resources.ApplyResources(this.cbBadgeDat, "cbBadgeDat");
            this.cbBadgeDat.Name = "cbBadgeDat";
            // 
            // label6
            // 
            resources.ApplyResources(this.label6, "label6");
            this.label6.Name = "label6";
            // 
            // buttonClose
            // 
            resources.ApplyResources(this.buttonClose, "buttonClose");
            this.buttonClose.Name = "buttonClose";
            this.buttonClose.Click += new System.EventHandler(this.buttonClose_Click);
            // 
            // tbImageIDDisplay
            // 
            resources.ApplyResources(this.tbImageIDDisplay, "tbImageIDDisplay");
            this.tbImageIDDisplay.Name = "tbImageIDDisplay";
            // 
            // tbImageID
            // 
            resources.ApplyResources(this.tbImageID, "tbImageID");
            this.tbImageID.Name = "tbImageID";
            // 
            // label2
            // 
            resources.ApplyResources(this.label2, "label2");
            this.label2.Name = "label2";
            // 
            // cbImageClassDisplay
            // 
            this.cbImageClassDisplay.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbImageClassDisplay.Items.AddRange(new object[] {
            resources.GetString("cbImageClassDisplay.Items"),
            resources.GetString("cbImageClassDisplay.Items1"),
            resources.GetString("cbImageClassDisplay.Items2"),
            resources.GetString("cbImageClassDisplay.Items3")});
            resources.ApplyResources(this.cbImageClassDisplay, "cbImageClassDisplay");
            this.cbImageClassDisplay.Name = "cbImageClassDisplay";
            // 
            // buttonDisplay
            // 
            resources.ApplyResources(this.buttonDisplay, "buttonDisplay");
            this.buttonDisplay.Name = "buttonDisplay";
            this.buttonDisplay.Click += new System.EventHandler(this.buttonDisplay_Click);
            // 
            // label5
            // 
            resources.ApplyResources(this.label5, "label5");
            this.label5.Name = "label5";
            // 
            // label4
            // 
            resources.ApplyResources(this.label4, "label4");
            this.label4.Name = "label4";
            // 
            // buttonPreview
            // 
            resources.ApplyResources(this.buttonPreview, "buttonPreview");
            this.buttonPreview.Name = "buttonPreview";
            this.buttonPreview.Click += new System.EventHandler(this.buttonPreview_Click);
            // 
            // buttonPrint
            // 
            resources.ApplyResources(this.buttonPrint, "buttonPrint");
            this.buttonPrint.Name = "buttonPrint";
            this.buttonPrint.Click += new System.EventHandler(this.buttonPrint_Click);
            // 
            // buttonLayout
            // 
            resources.ApplyResources(this.buttonLayout, "buttonLayout");
            this.buttonLayout.Name = "buttonLayout";
            this.buttonLayout.Click += new System.EventHandler(this.buttonLayout_Click);
            // 
            // label3
            // 
            resources.ApplyResources(this.label3, "label3");
            this.label3.Name = "label3";
            // 
            // cbImageClass
            // 
            this.cbImageClass.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cbImageClass.Items.AddRange(new object[] {
            resources.GetString("cbImageClass.Items"),
            resources.GetString("cbImageClass.Items1"),
            resources.GetString("cbImageClass.Items2"),
            resources.GetString("cbImageClass.Items3"),
            resources.GetString("cbImageClass.Items4")});
            resources.ApplyResources(this.cbImageClass, "cbImageClass");
            this.cbImageClass.Name = "cbImageClass";
            // 
            // buttonAllCapture
            // 
            resources.ApplyResources(this.buttonAllCapture, "buttonAllCapture");
            this.buttonAllCapture.Name = "buttonAllCapture";
            this.buttonAllCapture.Click += new System.EventHandler(this.buttonAllCapture_Click);
            // 
            // label1
            // 
            resources.ApplyResources(this.label1, "label1");
            this.label1.Name = "label1";
            // 
            // buttonSetup
            // 
            resources.ApplyResources(this.buttonSetup, "buttonSetup");
            this.buttonSetup.Name = "buttonSetup";
            this.buttonSetup.Click += new System.EventHandler(this.buttonSetup_Click);
            // 
            // buttonStartPrinter
            // 
            resources.ApplyResources(this.buttonStartPrinter, "buttonStartPrinter");
            this.buttonStartPrinter.Name = "buttonStartPrinter";
            this.buttonStartPrinter.Click += new System.EventHandler(this.buttonStartPrinter_Click);
            // 
            // checkBoxShowTest
            // 
            resources.ApplyResources(this.checkBoxShowTest, "checkBoxShowTest");
            this.checkBoxShowTest.Name = "checkBoxShowTest";
            this.checkBoxShowTest.CheckedChanged += new System.EventHandler(this.checkBoxShowTest_CheckedChanged);
            // 
            // buttonAbout
            // 
            resources.ApplyResources(this.buttonAbout, "buttonAbout");
            this.buttonAbout.Name = "buttonAbout";
            this.buttonAbout.Click += new System.EventHandler(this.buttonAbout_Click);
            // 
            // buttonMinimize
            // 
            resources.ApplyResources(this.buttonMinimize, "buttonMinimize");
            this.buttonMinimize.Name = "buttonMinimize";
            this.buttonMinimize.Click += new System.EventHandler(this.buttonMinimize_Click);
            // 
            // DDEServer
            // 
            resources.ApplyResources(this, "$this");
            this.Controls.Add(this.buttonStartPrinter);
            this.Controls.Add(this.buttonMinimize);
            this.Controls.Add(this.buttonAbout);
            this.Controls.Add(this.buttonExit);
            this.Controls.Add(this.checkBoxShowTest);
            this.Controls.Add(this.groupBoxTestControls);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.Fixed3D;
            this.MaximizeBox = false;
            this.Name = "DDEServer";
            this.WindowState = System.Windows.Forms.FormWindowState.Minimized;
            this.groupBoxTestControls.ResumeLayout(false);
            this.groupBoxTestControls.PerformLayout();
            this.ResumeLayout(false);

		}
		#endregion

		/// <summary>
		/// The main entry point for the application.
		/// </summary>
		[STAThread]
		static void Main()
		{
			Application.EnableVisualStyles();
			Application.DoEvents();

			// Allow only one instance of this program

			// FindWindow only works if the title is not changed dynamically -
			// and multi doc windows have doc name added.  
			// ID Server used this method, and it would fail sometimes because of this restriction.
			//    if ((IntPtr hWnd = FindWindow(null, "ID Services")) != 0) 
			//    {     SetForegroundWindow(hWnd); return; }
			/*
				#define SW_HIDE             0
				#define SW_SHOWNORMAL       1
				#define SW_NORMAL           1
				#define SW_SHOWMINIMIZED    2
				#define SW_SHOWMAXIMIZED    3
				#define SW_MAXIMIZE         3
				#define SW_SHOWNOACTIVATE   4
				#define SW_SHOW             5
				#define SW_MINIMIZE         6
				#define SW_SHOWMINNOACTIVE  7
				#define SW_SHOWNA           8
				#define SW_RESTORE          9
				#define SW_SHOWDEFAULT      10
				#define SW_FORCEMINIMIZE    11
				#define SW_MAX              11
			*/

			Process procThis = Process.GetCurrentProcess();	 	// get this process's name. "ID Services" is normal but it might be changed.
			Process[] procs = Process.GetProcessesByName(procThis.ProcessName);	//Application.ProductName);
			if (procs.Length > 1) // This code should prevent the number from ever getting above 2.
			{
				// the previously running instance will be at either index 0 or 1
				int index;
				if ((int)procs[0].MainWindowHandle != 0) index = 0;
				else index = 1;
				SetForegroundWindow(procs[index].MainWindowHandle);
				//ShowWindow is necessary to restore a minimized window - I think I want to leave it the way the opr left it.
				//ShowWindow(procs[index].MainWindowHandle, 9);
			ShowWindow(procs[index].MainWindowHandle, 1);
				return;
			}
			try
			{
				Application.Run(new DDEServer());
			}
			catch (Exception ex)
			{
				MessageBox.Show("ID Services ERROR: " + ex.Message, "ID Services", MessageBoxButtons.OK, MessageBoxIcon.Stop);
				throw ex;
			}
		}

		// used only for HELP command
		private static DDEExeCmd[] ALLRequestCommands = 
		{
			//            command,  num required tokens, arguments
			new DDEExeCmd("COMPUTERNAME", 1, ""),
			new DDEExeCmd("ENCODECHIP_STATUS", 1, ""),
			new DDEExeCmd("FINDIMAGE", 2, "ImageID {classEx {Date}}"),
			new DDEExeCmd("GENBIO_STATUS", 1, ""),
			new DDEExeCmd("GETBADGEDATAPATH", 1, ""),
			new DDEExeCmd("GETBIOMETRIC", 2, "ImageID {classEx}"),
			new DDEExeCmd("GETCHIPID", 1, ""),
			new DDEExeCmd("GETDOCBIN", 2, "ImageID {DocName {datafile}}"),
			new DDEExeCmd("GETIMAGENAME", 2, "ImageID {classEx}"),
			new DDEExeCmd("HASPPERMISSIONS", 1, ""),
			new DDEExeCmd("ISCHIPIDSCANNED", 1, ""),
			new DDEExeCmd("ISPRINTDIRECT", 1, ""),
			new DDEExeCmd("PRINT", 1, ""),
			new DDEExeCmd("QUEUE_REFNO", 1, ""),
			new DDEExeCmd("SCANDATA_STATUS", 1, ""),
			new DDEExeCmd("SEARCHBIO_STATUS", 1, ""),
			new DDEExeCmd("SERIALNUMBER", 1, ""),
			new DDEExeCmd("TAKE", 1, ""),
			new DDEExeCmd("VERIFYBIO_STATUS", 1, "")
		};

		// used to validate commands so backward compatiblity commands must be in the list
		private static DDEExeCmd[] ALLExecuteCommands = 
		{
			//            command,  num required tokens, arguments
			new DDEExeCmd("BADGE",		2,	"datafile"),	// same as PRINT
			new DDEExeCmd("CLOSE",		1,	""),
			new DDEExeCmd("DELETE",		2,	"ImageID {classEx}"), 
			new DDEExeCmd("DISPLAY",	2,	"ImageID {title {classEx}}"), 
			new DDEExeCmd("ENCODECHIP", 2,	"datafile"),
			new DDEExeCmd("EXEC",		2,	"LAYOUT|SETUP"),
			new DDEExeCmd("EXPORT",		3,	"ImageID imageFile {classEx}"), 
			new DDEExeCmd("EXPORTHISTORY",	5,	"ImageID imageFile classEx date"), 
			new DDEExeCmd("EXPORTFIPS",	3,	"ImageID datafile"), 
			new DDEExeCmd("GENBIO",		2,	"ImageID {classEx}"),
			new DDEExeCmd("HELP",		1,	""),
			new DDEExeCmd("IMPORT",		3,	"ImageID imageFile {classEx}"), 
			new DDEExeCmd("IMPORTMERGE",4,	"ImageID SourceID importDir {class}"), 
			new DDEExeCmd("IMPORTFIPS",	3,	"ImageID datafile"), 
			new DDEExeCmd("KILL",		1,	""),
			new DDEExeCmd("PREVIEW",	2,	"datafile {title}"),
			new DDEExeCmd("PRINTNOW",	1,	"ACTIVE|PAUSE|IMMEDIATE"),
			new DDEExeCmd("PRINT",		2,	"datafile"),
			new DDEExeCmd("PRINTDIRECT",2,	"datafile"),
			new DDEExeCmd("QUEUE",		2,	"datafile"),
			new DDEExeCmd("RETAKE",		2,	"ImageID {title {classEx}}"),
			new DDEExeCmd("SCANDATA",	2,	"2DBAR|CHIP datafile"),	// to scan 2d barcode or chip
			new DDEExeCmd("SEARCHBIO",	2,	"ImageID {classEx}"),
			new DDEExeCmd("SETIMGDISPLAY", 5, "X Y Width Height {class}"),
			new DDEExeCmd("SETUPCHANGED", 1,	""),
			new DDEExeCmd("TAKE",		2,	"ImageID {title {classEx}}"),
			new DDEExeCmd("VERIFYBIO",	2,	"ImageID {classEx}")
		};
		//
		/// <summary>
		/// Process DDE Execute Message.
		/// All return paths should post ACK in response to DDE Execute. 
		/// "BADGE" and "TAKE" post an immediate ACK and then use DDEREQUEST to get results. 
		/// Ditto for SCANDATA, ENCODECHIP, GENBIO, SEARCHBIO and VERIFYBIO.
		/// EXEC posts immediate ACK.
		/// DDEACK  struct = 
		///				unsigned short bAppReturnCode:8, 
		///				reserved:6, 
		///				fBusy:1, 
		///				fAck:1; 
		/// in C# use a ushort representing the 16bit DDEACK structure - 1 ack(HI), 1 busy, 6 reserved, 8 bAppReturnCode(LO)
		/// Success = 0x8001 (POSITIVE_ACK); failure = 0 (NEGATIVE_ACK);
		///
		/// not yet supported: COPY, MOVE, image/type in CLOSE
		/// </summary>
		/// <param name="m"></param>
		/// <returns></returns>
		private void DoDDEExecute(Message m)
		{
			string strErrorMsg = null;
            string strCommand = null;

			IntPtr hCallerWindow = GetForegroundWindow();
			//			SetForegroundWindow(this.Handle);

			// SetParent will make ID Services visible over the application.
			// if i do this i want to make server iconic, and redo all the topmost stuff coded into the server.
			// SetParent(this.Handle, hCallerWindow);  // added 11/25/07 to keep server over app

			m_DDEExecutePendingCount++;
			// startup is not complete
			if (m_bStillStartingUp)
			{
				strErrorMsg = "ID Services still starting up.";
				DCSDEV.DCSMsg.Log("DDEExecute ERROR: " + strErrorMsg);
				System.Threading.Thread.Sleep(3000);
				goto DoDDEExecute_NOTOK_ACK;
			}

			// get the command
			IntPtr hCommand = (IntPtr)m.LParam.ToInt32();
			string command;

			//NOTE SYH: I can never tell whether Marshal or GlobalLockString is going to work
			//          From IDTest, only GlobalLockString works; from MSAccess only Marshal.PtrToStringAuto works
            // use either:
            //string command = GlobalLockString(hCommand);
            //string command = Marshal.PtrToStringAuto(GlobalLock(hCommand));
            if (m_iDDEMethod == 0)
			{
				command = GlobalLockString(hCommand);
				if (command.Length == 1)
				{
					m_iDDEMethod = 1;
					GlobalUnlock(hCommand);
					command = Marshal.PtrToStringAuto(GlobalLock(hCommand));
				}
			}
			else
			{
				command = Marshal.PtrToStringAuto(GlobalLock(hCommand));
				string str1 = command.Substring(0, 1).ToUpper();
				if ("?ABCDEFGHIJKLMNOPQRSTUVWXYZ".IndexOf(str1) < 0)
				{
					m_iDDEMethod = 0;
					command = GlobalLockString(hCommand);
				}
			}

			if (command.Length == 0)
				strCommand = "blank";
			else
				strCommand = (string)command.Clone();	// clone so I can free the original
			GlobalFree(hCommand);

			// validate the command
			DCSDEV.DCSMsg.Log("WM_DDE_EXECUTE: " + strCommand);
			ArrayList strArray = new ArrayList();
			bool bRet = DCSServerStuff.ParseString(strCommand, ref strArray);
			if (!bRet)
			{
				strErrorMsg = "ERROR parsing command syntax:" + Environment.NewLine + strCommand;
				goto DoDDEExecute_NOTOK_ACK;
			}
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.DDEServer)
			|| DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.SystemMgt))
			{
				goto DoDDEExecute_NOTOK_ACK;
			}

			// check the command and make it upper case.
			strArray[0] = ((string)strArray[0]).ToUpper();
			bool bFound = false;
			foreach (DDEExeCmd ddecmd in ALLExecuteCommands)
			{
				if (ddecmd.strCommand == (string)strArray[0])
				{
					bFound = true;
					if (ddecmd.numRequiredTokens > strArray.Count)
					{
						strErrorMsg = "ERROR in command syntax:\n\n" + strCommand;
						goto DoDDEExecute_NOTOK_ACK;
					}
					break;
				}
			}
			if (!bFound)
			{
				strErrorMsg = "Unsupported DDE Execute command:\n\n" + strCommand;
				goto DoDDEExecute_NOTOK_ACK;
			}

			// check for pending commands
			if (m_DDEExecutePendingCount > 1)
			{
				// another command is still being executed
				if (m_bExecPending)
				{
					// if EXEC is pending and command is EXEC do it
					if ((string)strArray[0] == "EXEC")
					{
						SetForegroundWindow(this.Handle);
						PostMessage(m.WParam, WM_DDE_ACK, this.Handle, PackDDElParam(WM_DDE_ACK, (IntPtr)POSITIVE_ACK, m.LParam));
						m_DDEExecutePendingCount--;
						return;
					}
				}
                else if ((string)strArray[0] == "KILL")
                {
                    m_bKillPending = true;
                    // acknowlege command
                    goto DoDDEExecute_OK_ACK;
                }
                else if ((string)strArray[0] == "TAKE")
                {
                    DCSMsg.Show("TAKE command with another execute command pending");
                    m_dlgCaptureMgt.Dispose();
                    m_dlgCaptureMgt = new DCSSDK.CaptureMgt.DCSSDK_CaptureMgt();
                    m_DDEExecutePendingCount = 1;
                }
                else
                {
                    System.Threading.Thread.Sleep(2000);
                    goto DoDDEExecute_NOTOK_ACK;
                }
			}

			SetForegroundWindow(this.Handle);
			m_bKillPending = false;

            // see if command is "HELP" a request for information
            if ((string)strArray[0] == "HELP")
            {
                // acknowlege command before completion
                PostMessage(m.WParam, WM_DDE_ACK, this.Handle, PackDDElParam(WM_DDE_ACK, (IntPtr)POSITIVE_ACK, m.LParam));
                StringBuilder sb = new StringBuilder();
                sb.Append("DDE Execute Commands\n");
                foreach (DDEExeCmd ddecmd in ALLExecuteCommands)
                {
                    sb.Append(ddecmd.strCommand + "\t");
                    if (ddecmd.strCommand.Length < 8) sb.Append("\t");
                    sb.Append(ddecmd.strArgs + "\n");
                }
                sb.Append("\nDDE Request Commands\n");
                foreach (DDEExeCmd ddecmd in ALLRequestCommands)
                {
                    sb.Append(ddecmd.strCommand + "\t");
                    if (ddecmd.strCommand.Length < 8) sb.Append("\t");
                    sb.Append(ddecmd.strArgs + "\n");
                }
                DCSMsg.Show(sb.ToString(), MessageBoxIcon.None);		// only error or warning messages are logged
                goto DoDDEExecute_OK_NOACK;
            }
            // syntax: PRINT BadgeDotDatPath
			//         BADGE BadgeDotDatPath
			else if ((string)strArray[0] == "BADGE" || (string)strArray[0] == "PRINT")
			{
				// set print pending
				m_ePrintState = PrintStates.PRINT_PENDING;
				PostMessage(m.WParam, WM_DDE_ACK, this.Handle, PackDDElParam(WM_DDE_ACK, (IntPtr)POSITIVE_ACK, m.LParam));

				this.DoPrint((string)strArray[1]);

				timer1_Tick(null, null);
				goto DoDDEExecute_OK_NOACK;
			}
			else if ((string)strArray[0] == "QUEUE")
			{
				// set print pending
				m_ePrintState = PrintStates.PRINT_PENDING;
				PostMessage(m.WParam, WM_DDE_ACK, this.Handle, PackDDElParam(WM_DDE_ACK, (IntPtr)POSITIVE_ACK, m.LParam));

				m_bCurrentIsScanIDType = m_bCurrentPrintIsDirect = false;
				m_strLastChipID = null;
				this.DoPrintToQueue((string)strArray[1]);

				timer1_Tick(null, null);
				goto DoDDEExecute_OK_NOACK;
			}
			// syntax: PRINTDIRECT BadgeDotDatPath
			else if ((string)strArray[0] == "PRINTDIRECT")
			{
				// set print pending
				m_ePrintState = PrintStates.PRINT_PENDING;
				PostMessage(m.WParam, WM_DDE_ACK, this.Handle, PackDDElParam(WM_DDE_ACK, (IntPtr)POSITIVE_ACK, m.LParam));

				m_bCurrentIsScanIDType = m_dlgBadgingMgt.IsReturnDataType((string)strArray[1]);
				m_bCurrentPrintIsDirect = true;
				m_strLastChipID = null;
				this.DoPrintDirect((string)strArray[1]);

				timer1_Tick(null, null);
				goto DoDDEExecute_OK_NOACK;
			}
			// syntax: PREVIEW BadgeDotDatPath {label}
			else if ((string)strArray[0] == "PREVIEW")
			{
				// get display label
				string strDisplayLabel = null;
				if (strArray.Count > 2) strDisplayLabel = (string)strArray[2];

				DoPreview((string)strArray[1], strDisplayLabel);
				// acknowlege command after completion
				goto DoDDEExecute_OK_ACK;
			}
			// syntax: PRINTNOW {ACTIVE|PAUSE|IMMEDIATE}
			else if ((string)strArray[0] == "PRINTNOW")
			{
				m_dlgCaptureMgt.CloseDisplays();
				m_dlgBadgingMgt.ClosePreview();

				string strPrintNowOption = "";
				if (strArray.Count > 1) strPrintNowOption = ((string)strArray[1]).ToUpper();
				m_dlgBadgingMgt.StartDCSPrinter();
				if (strPrintNowOption == "ACTIVE")
				{
					m_dlgBadgingMgt.PrintQueueStart();
				}
				else if (strPrintNowOption == "PAUSE")
				{
					m_dlgBadgingMgt.PrintQueuePause();
				}
				else if (strPrintNowOption == "IMMEDIATE")
				{
					m_dlgBadgingMgt.PrintQueuePrintNow();  // prints whatever partial sheet is accumulated.
				}
				else
				{
					m_dlgBadgingMgt.PrintQueueStart();
					m_dlgBadgingMgt.PrintQueuePrintNow();  // prints whatever partial sheet is accumulated.
				}
				goto DoDDEExecute_OK_ACK;
			}
			// syntax: CLOSE
			else if ((string)strArray[0] == "CLOSE")
			{
				m_dlgCaptureMgt.CloseDisplays();
				m_dlgBadgingMgt.ClosePreview();

				// acknowlege command
				goto DoDDEExecute_OK_ACK;
			}
			// syntax: DELETE imageID {imageclassEX}
			else if ((string)strArray[0] == "DELETE")
			{
				// get image ID
				string strImageID = (string)strArray[1];
				int iSubClass = -1;
				// get image class
				string strImgClass = "All";
				if (strArray.Count > 2)
				{
					DCSDatabaseIF.ImageClass imageClass = DCSDatabaseIF.StringToClass((string)strArray[2]);
					iSubClass = DCSDatabaseIF.StringToSubClass((string)strArray[2]);
					strImgClass = imageClass.ToString();
				}
				bRet = DoDelete(strImageID, strImgClass, iSubClass);
				// acknowlege command
				if (bRet) goto DoDDEExecute_OK_ACK;
				else goto DoDDEExecute_NOTOK_ACK;
			}
			// syntax: DISPLAY imageID {label {imageclass}}
			else if ((string)strArray[0] == "DISPLAY")
			{
				// get image ID
				string strImageID = (string)strArray[1];
				// get display label
				string strDisplayLabel = null;
				if (strArray.Count > 2) strDisplayLabel = (string)strArray[2];
				// get image class
				string strImageClass;
				if (strArray.Count > 3)
				{
					DCSDatabaseIF.ImageClass imageClass = DCSDatabaseIF.StringToClass((string)strArray[3]);
					int iSubClass = DCSDatabaseIF.StringToSubClass((string)strArray[3]);
					strImageClass = imageClass.ToString();
					if (iSubClass != -1) strImageClass = strImageClass + iSubClass.ToString();
				}
				else
					strImageClass = "All";

				DoDisplay(strImageID, strDisplayLabel, strImageClass);	// image class must be a sring to accomodate all
				// acknowlege command
				goto DoDDEExecute_OK_ACK;
			}
			// syntax: EXEC SETUP
			//         EXEC LAYOUT
			else if ((string)strArray[0] == "EXEC")
			{
				string exe = ((string)strArray[1]).ToUpper();
				if (exe == "SETUP" || exe == "LXSETUP")
				{
					m_bExecPending = true;
					// acknowledge command
					PostMessage(m.WParam, WM_DDE_ACK, this.Handle, PackDDElParam(WM_DDE_ACK, (IntPtr)POSITIVE_ACK, m.LParam));

					m_dlgCaptureMgt.CloseDisplays();
					m_dlgBadgingMgt.ClosePreview();

					if (m_dlgSetup.Visible == true)
						m_dlgSetup.Focus();
					else
					{
						m_dlgSetup.ShowDialog(this);
						m_bExecPending = false;

						DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("DCSSDK_Mgt");
						m_bShowTestControls = ps.GetBoolParameter("ShowTestControls", true);
						if (!m_bShowTestControls) this.checkBoxShowTest.Checked = false;
						this.checkBoxShowTest.Visible = m_bShowTestControls;
					}
					m_DDEExecutePendingCount--;
					return;
				}
				else if (exe == "LAYOUT")
				{
					m_dlgCaptureMgt.CloseDisplays();
					m_dlgBadgingMgt.ClosePreview();

					m_bExecPending = true;
					// acknowledge command
					PostMessage(m.WParam, WM_DDE_ACK, this.Handle, PackDDElParam(WM_DDE_ACK, (IntPtr)POSITIVE_ACK, m.LParam));

					m_dlgBadgingMgt.DesignBadge(null);
					m_bExecPending = false;
					m_DDEExecutePendingCount--;
					return;
				}
				else
				{
					strErrorMsg = "Unsupported EXEC command: " + strCommand;
					goto DoDDEExecute_NOTOK_ACK;
				}
			}
			// syntax: ENCODECHIP strSourceDataFileName
			// In ICAO case source file is BadgeDataFile.
			else if ((string)strArray[0] == "ENCODECHIP")
			{
				// set encoce chip pending
				m_eEncodeChipState = DCSDEV.DDEServer.DDEServer.EncodeChipStates.ENCODECHIP_PENDING;
				// acknowlege command before completion
				PostMessage(m.WParam, WM_DDE_ACK, this.Handle, PackDDElParam(WM_DDE_ACK, (IntPtr)POSITIVE_ACK, m.LParam));

				this.DoEncodeChip((string)strArray[1]);

				// set up busy loop
				timer1_Tick(null, null);
				goto DoDDEExecute_OK_NOACK;
			}
			// syntax: GENBIO imageid {imageclassEX}
			// unspecified subclass defaults to ALL 
			else if ((string)strArray[0] == "GENBIO")
			{
				// GENBIO imageID imageClass[ with instance 0-9]
				// instance is zero based. Defaults to 0;
				// Call GENBIO_STATUS request to determine completion 
				string strImageID = (string)strArray[1];
				DCSDatabaseIF.ImageClass imageClass;
				int iSubClass = 0;
				if (strArray.Count >= 3)
				{
					imageClass = DCSDatabaseIF.StringToClass((string)strArray[2]);
					iSubClass = DCSDatabaseIF.StringToSubClass((string)strArray[2]);
				}
				else
				{
					imageClass = DCSDatabaseIF.ImageClass.Fingerprint;
					iSubClass = -1;
				}

				// set GenBiometric pending
				m_eGenBiometricState = GenBiometricStates.GENBIO_PENDING;
				// acknowlege command before completion
				PostMessage(m.WParam, WM_DDE_ACK, this.Handle, PackDDElParam(WM_DDE_ACK, (IntPtr)POSITIVE_ACK, m.LParam));
				this.DoGenBiometric(strImageID, imageClass, iSubClass);
				timer1_Tick(null, null);
				goto DoDDEExecute_OK_NOACK;
			}
			// syntax: KILL
			// Prepares server to terminate when the DDE Terminate command is recieved
			else if ((string)strArray[0] == "KILL")
			{
				m_bKillPending = true;
				// acknowlege command
				goto DoDDEExecute_OK_ACK;
			}
			// Finds the file that was current at the given date.
			// syntax: EXPORTHISTORY imageID filename imageclassEX date
			else if ((string)strArray[0] == "EXPORTHISTORY")
			{
				DCSDatabaseIF.ImageClass imageclass;
				int iSubClass;

				imageclass = DCSDatabaseIF.StringToClass((string)strArray[3]);
				iSubClass = DCSDatabaseIF.StringToSubClass((string)strArray[3]);
				if (iSubClass < 0) iSubClass = 0;

				// date is specified - go back in time looking for the first image that is <= the specified date
				DCSDEV.ParameterStore ps = new ParameterStore("DCSSDK_Mgt");
				string strAppDateFormat = ps.GetStringParameter("AppDateFormat", "default");
				if (!DCSDEV.DCSServerStuff.IsStringDateTime((string)strArray[4], strAppDateFormat))
					goto DoDDEExecute_NOTOK_ACK;

				DateTime dtGiven;
				dtGiven = DCSDEV.DCSServerStuff.String2DateTime((string)strArray[4], strAppDateFormat);

				string strFilename;
				int i = 0;
				while (true)
				{
					DCSMsg.Show("There is no code to handle images in the image database table.");

					// look thru files from newest to oldest.
					// the first file the is older than the given date is the one that was current at that date.
					strFilename = DCSDatabaseIF.GetFullNameOfHistoricImage((string)strArray[1], imageclass, iSubClass, i, true);
					if (strFilename == null) break;
					if (System.IO.File.GetLastWriteTime(strFilename) <= dtGiven)
					{
						// SYH TODO:
						// Export the file to the path provided
						DCSMsg.Show(String.Format("Export image that was active {0} to {1}", dtGiven, (string)strArray[2]));
						bRet = false;

						// acknowlege command
						if (bRet) goto DoDDEExecute_OK_ACK;
						else goto DoDDEExecute_NOTOK_ACK;
					}
					i++;
				}
				goto DoDDEExecute_NOTOK_ACK;

			}
			// syntax: IMPORT/EXPORT imageID filename {imageclassEX})
			else if ((string)strArray[0] == "IMPORT" || (string)strArray[0] == "EXPORT")
			{
				bool bImport = false;
				if ((string)strArray[0] == "IMPORT") bImport = true;
				// get image ID
				string strImageID = (string)strArray[1];

				// get file name
				string strFilename = (string)strArray[2];

				// get image class
				int iSubClass = 0;
				string strImgClass = DCSDatabaseIF.ImageClass.Portrait.ToString();
				if (strArray.Count > 3)
				{
					DCSDatabaseIF.ImageClass imageClass = DCSDatabaseIF.StringToClass((string)strArray[3]);
					iSubClass = DCSDatabaseIF.StringToSubClass((string)strArray[3]);
					if (iSubClass == -1) iSubClass = 0;
					strImgClass = imageClass.ToString();
				}
				// the export cammand supports size adjustments for interface to DHS databases
				string strSize = null;
				if (strArray.Count > 4 && !bImport)
				{
					strSize = (string)strArray[4];
				}

				if (strFilename == "")
				{
					// acknowlege command before completion
					PostMessage(m.WParam, WM_DDE_ACK, this.Handle, PackDDElParam(WM_DDE_ACK, (IntPtr)POSITIVE_ACK, m.LParam));
					bRet = DoImportExport(bImport, strFilename, strImageID, strImgClass, iSubClass);

					if (strSize != null && !bImport)
						bRet = DoAdjustFileSize(strFilename, strSize);
					goto DoDDEExecute_OK_NOACK;
				}
				else
				{
					bRet = DoImportExport(bImport, strFilename, strImageID, strImgClass, iSubClass);
					if (strSize != null && !bImport)
						bRet = DoAdjustFileSize(strFilename, strSize);

					// acknowlege command
					if (bRet) goto DoDDEExecute_OK_ACK;
					else goto DoDDEExecute_NOTOK_ACK;
				}
			}
			// syntax: IMPORTMERGE ImageID SourceImageID SourceDataRootPath imageclass|ALL}
			// source must use file directories for image storage - not oleDB 
			else if ((string)strArray[0] == "IMPORTMERGE")
			{
				// get image ID
				string strImageID = (string)strArray[1];
				string strSourceID = (string)strArray[2];
				string strSourceDataRoot = (string)strArray[3];
				string strImgClass = "All";
				if (strArray.Count > 4)
					strImgClass = (string)strArray[4];

				bRet = DoImportMerge(strImageID, strSourceID, strSourceDataRoot, strImgClass);
				// acknowlege command
				if (bRet) goto DoDDEExecute_OK_ACK;
				else goto DoDDEExecute_NOTOK_ACK;
			}
			// syntax: IMPORTFIPS/EXPORTFIPS imageID filename {imageclassEX}
			else if ((string)strArray[0] == "IMPORTFIPS" || (string)strArray[0] == "EXPORTFIPS")
			{
				bool bImport = false;
				if ((string)strArray[0] == "IMPORTFIPS") bImport = true;
				// get image ID
				string strImageID = (string)strArray[1];

				// get file name
				string strFilename = (string)strArray[2];

				bRet = DoFIPSImportExport(bImport, strFilename, strImageID);
				// acknowlege command
				if (bRet)
				{
					goto DoDDEExecute_OK_ACK;
				}
				else
				{
					goto DoDDEExecute_NOTOK_ACK;
				}
			}

			// systax SEARCHBIO ImageID {classEx} 
			// ImageID = 
			//         = "_LIVE" to capture a live image for search; 
			// Call SEARCHBIO_STATUS request to determine when done and answer
			else if ((string)strArray[0] == "SEARCHBIO")
			{
				string strImageID = (string)strArray[1];

				DCSDatabaseIF.ImageClass imageClass;
				int iSubClass = -1;
				if (strArray.Count >= 3)
				{
					imageClass = DCSDatabaseIF.StringToClass((string)strArray[2]);
					iSubClass = DCSDatabaseIF.StringToSubClass((string)strArray[2]);
				}
				else
				{
					imageClass = DCSDatabaseIF.ImageClass.Fingerprint;
					iSubClass = -1;
				}

				// set SearchBiometric pending
				m_eSearchBiometricState = SearchBiometricStates.SEARCHBIO_PENDING;
				m_strSearchResult = "ERROR";
				// acknowlege command before completion
				PostMessage(m.WParam, WM_DDE_ACK, this.Handle, PackDDElParam(WM_DDE_ACK, (IntPtr)POSITIVE_ACK, m.LParam));
				this.DoSearchBiometric(strImageID, imageClass, iSubClass);
				timer1_Tick(null, null);
				goto DoDDEExecute_OK_NOACK;
			}
			// syntax: SCANDATA ScanSource(2DBar or Chip) strDestination
			else if ((string)strArray[0] == "SCANDATA")
			{
				// set scan data pending
				m_eScanDataState = ScanDataStates.SCANDATA_PENDING;
				// acknowlege command before completion
				PostMessage(m.WParam, WM_DDE_ACK, this.Handle, PackDDElParam(WM_DDE_ACK, (IntPtr)POSITIVE_ACK, m.LParam));

				if (strArray.Count >= 3)
					this.DoScanData((string)strArray[1], (string)strArray[2]);
				else
					this.DoScanData((string)strArray[1], null);

				// set up busy loop
				timer1_Tick(null, null);
				goto DoDDEExecute_OK_NOACK;
			}
			// syntax: SETIMGDISPLAY x y sx sy {imageClass}
			else if ((string)strArray[0] == "SETIMGDISPLAY")
			{
				DCSDatabaseIF.ImageClass imgclass = DCSDatabaseIF.ImageClass.Portrait;

				m_dlgCaptureMgt.CloseDisplays();
				m_dlgBadgingMgt.ClosePreview();

				if (strArray.Count >= 6) imgclass = DCSDatabaseIF.StringToClass((string)strArray[5]);

				Rectangle rect = new Rectangle();
				rect.Width = Convert.ToInt32(((string)strArray[1]));
				rect.Height = Convert.ToInt32(((string)strArray[2]));
				rect.X = Convert.ToInt32(((string)strArray[3]));
				rect.Y = Convert.ToInt32(((string)strArray[4]));
				switch (imgclass)
				{
					default:
					case DCSDatabaseIF.ImageClass.Certificate:
						m_dlgCaptureMgt.CertsDisplayRectangle = rect;
						break;
					case DCSDatabaseIF.ImageClass.Portrait:
						m_dlgCaptureMgt.PortraitDisplayRectangle = rect;
						break;
					case DCSDatabaseIF.ImageClass.Signature:
						m_dlgCaptureMgt.SignatureDisplayRectangle = rect;
						break;
					case DCSDatabaseIF.ImageClass.Fingerprint:
					case DCSDatabaseIF.ImageClass.TenPrint:
						m_dlgCaptureMgt.FingerprintDisplayRectangle = rect;
						break;
				}
				// acknowlege command after completion
				goto DoDDEExecute_OK_ACK;
			}
			// syntax: SETUPCHANGED
			else if ((string)strArray[0] == "SETUPCHANGED")
			{
				ParameterStore.RereadMachineIndex();
				m_dlgCaptureMgt.ReadAndInstallCaptureMgtParameters();
				DCSDEV.DCSDesignDataAccess.Reinit();

				goto DoDDEExecute_OK_ACK;
			}
			// syntax: TAKE {imageid {label} {imageclass}}
			else if ((string)strArray[0] == "TAKE" || (string)strArray[0] == "RETAKE")
			{
				string strImageID = (string)strArray[1];
				string strImageClass = null;
				int iSubClass = -1;
				string strImageTitle = null;
				if (strArray.Count > 2)
					strImageTitle = (string)strArray[2];
				else
					strImageTitle = "";
				if (strArray.Count > 3)
				{
					if (((string)strArray[3]).Length >= 3 && ((string)strArray[3]).ToUpper().Substring(0, 3) == "TEN")
					{
						strImageClass = "TenPrint";
						iSubClass = -1;
					}
					else
					{
						DCSDatabaseIF.ImageClass imageClass = DCSDatabaseIF.StringToClass((string)strArray[3]);
						iSubClass = DCSDatabaseIF.StringToSubClass((string)strArray[3]);
						strImageClass = imageClass.ToString();
					}
				}
				else
					strImageClass = "All";

				// set capture pending so dde request can report busy until done
				m_eCaptureState = CaptureStates.CAPTURE_PENDING;
				// acknowlege command before completion
				PostMessage(m.WParam, WM_DDE_ACK, this.Handle, PackDDElParam(WM_DDE_ACK, (IntPtr)POSITIVE_ACK, m.LParam));

				// set bPermitArchive to true for standard version of the command 
				// a separate recapture command with suppress archive
				bool bAllowArchive = true;
				if ((string)strArray[0] == "RETAKE") bAllowArchive = false;
				DoCapture(strImageID, strImageClass, iSubClass, strImageTitle, bAllowArchive);
				timer1_Tick(null, null);
				goto DoDDEExecute_OK_NOACK;
			}
			// syntax: VERIFYBIO imageid {imageclass}
			// imageID = _BIN_BARCODE if verifying against 2D Barcode
			// imageID = _BIN_MAGSTRIPE if verifying against mag stripe
			// imageID = _BIN_SMARTCHIP if verifying against chip
			//
			// if subclass is not specified, defaults to subclass -1
			// Call VERIFYBIO_STATUS request to determine when done and answer
			else if ((string)strArray[0] == "VERIFYBIO")
			{
				string strImageID = (string)strArray[1];
				DCSDatabaseIF.ImageClass imageClass;
				int iSubClass = -1;
				if (strArray.Count >= 3)
				{
					imageClass = DCSDatabaseIF.StringToClass((string)strArray[2]);
					iSubClass = DCSDatabaseIF.StringToSubClass((string)strArray[2]);
				}
				else
				{
					imageClass = DCSDatabaseIF.ImageClass.Fingerprint;
					iSubClass = -1;
				}

				// set VerifyBiometric pending
				m_eVerifyBiometricState = VerifyBiometricStates.VERIFYBIO_PENDING;
				m_strVerifyResult = "ERROR";
				// acknowlege command before completion
				PostMessage(m.WParam, WM_DDE_ACK, this.Handle, PackDDElParam(WM_DDE_ACK, (IntPtr)POSITIVE_ACK, m.LParam));
				this.DoVerifyBiometric(strImageID, imageClass, iSubClass);
				timer1_Tick(null, null);
				goto DoDDEExecute_OK_NOACK;
			}
			else
			{
				// this case should have been caught by initial validation
				goto DoDDEExecute_NOTOK_ACK;
			}

		DoDDEExecute_OK_ACK:
			// acknowlege command before completion
			PostMessage(m.WParam, WM_DDE_ACK, this.Handle, PackDDElParam(WM_DDE_ACK, (IntPtr)POSITIVE_ACK, m.LParam));
		DoDDEExecute_OK_NOACK:
			SetForegroundWindow(hCallerWindow);
			m_DDEExecutePendingCount--;
			return;

		DoDDEExecute_NOTOK_ACK:
			PostMessage(m.WParam, WM_DDE_ACK, this.Handle, PackDDElParam(WM_DDE_ACK, (IntPtr)NEGATIVE_ACK, m.LParam));
			if (strErrorMsg != null)
			{
                DCSDEV.DCSMsg.Log(" Execute ERROR - NACK sent: " + strCommand + " - " + strErrorMsg);
                SetForegroundWindow(this.Handle);
				DCSMsg.Show(strErrorMsg, MessageBoxIcon.Error);
				strErrorMsg = null;
			}
            else
                DCSDEV.DCSMsg.Log(" Execute ERROR - NACK sent: " + strCommand);

			SetForegroundWindow(hCallerWindow);
			m_DDEExecutePendingCount--;
			return;
		}

		// Get status of DDEExec:
		//    BADGE(or PRINT), TAKE, ENCODECHIP_STATUS, SCANDATA_STATUS, GENBIO_STATUS, SEARCHBIO_STATUS, VERIFYBIO_STATUS
		// Get info:
		//    APPBLOCKNUMBER, SERIALNUMBER, HASPPERMISSIONS, GETBIOMETRIC, GETDOCBIN, GETIMAGENAME, FINDIMAGE, COMPUTERNAME
		//    GETBADGEDATAPATH, GETCHIPID, ISCHIPIDSCANNED, ISOLEDB, ISPRINTDIRECT, QUEUE_REFNO
		// Does not get and set foreground window because all calls that change focus
		// are via Showdialog which does its own fous reset,
		// NOT SUPPORTED: , BLOCKNUMBER, INIFILE
		private void DoDDERequest(Message m)
		{
			// startup is not complete
			if (m_bStillStartingUp)
			{
				DCSDEV.DCSMsg.Log("DDERequest ERROR: still starting up");
				System.Threading.Thread.Sleep(3000);
				SendDDEData("ERROR", m);
				return;
			}

			IntPtr atomType;
			IntPtr atomRequest;

			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			UnpackDDElParam(WM_DDE_REQUEST, m.LParam, out atomType, out atomRequest);
			StringBuilder sbreq = new StringBuilder(200);
			GlobalGetAtomName(atomRequest, sbreq, sbreq.Capacity);
			string strRequest = sbreq.ToString();

			DCSDEV.DCSMsg.Log("WM_DDE_REQUEST: " + strRequest);
			ArrayList strArray = new ArrayList();
			bool bRet = DCSServerStuff.ParseString(strRequest, ref strArray);

			strArray[0] = ((string)strArray[0]).ToUpper();
			if ((string)strArray[0] == "APPBLOCKNUMBER")	// unused by DCS Apps - there for compatibility with ITC apps
			{
				SendDDEData("7", m);
				return;
			}
			else if ((string)strArray[0] == "COMPUTERNAME")
			{
				SendDDEData(SystemInformation.ComputerName, m);
				return;
			}
			// syntax: FINDIMAGE imageID {imageclassEX {DATE}}
			// RETURN: full filename or "" if none found or ERROR if program error
			// if class is not specified, defaults to Portrait
			// if subclass is not specified, defaults to subclass 0
			// If date is specified, look for image with latest ImageDate <= Date; this is image that was current as of Date.
			else if ((string)strArray[0] == "FINDIMAGE")
			{
				if (strArray.Count <= 1)
				{
					SendDDEData("ERROR", m);
					return;
				}
				DCSDatabaseIF.ImageClass imgclass;
				int iSubClass = 0;
				if (strArray.Count > 2)
				{
					imgclass = DCSDatabaseIF.StringToClass((string)strArray[2]);
					iSubClass = DCSDatabaseIF.StringToSubClass((string)strArray[2]);
					if (iSubClass < 0) iSubClass = 0;
				}
				else
				{
					imgclass = DCSDatabaseIF.ImageClass.Portrait;
					iSubClass = 0;
				}
				if (strArray.Count > 3)     // if a date is specified
				{
					// date is specified - go back in time looking for the first image that is <= the specified date
					DCSDEV.ParameterStore ps = new ParameterStore("DCSSDK_Mgt");
					string strAppDateFormat = ps.GetStringParameter("AppDateFormat", "default");
					if (!DCSDEV.DCSServerStuff.IsStringDateTime((string)strArray[3], strAppDateFormat)
                    || DCSDatabaseIF.IsOleDBType())
					{
						SendDDEData("ERROR", m);
						return;
					}
					DateTime dtGiven;
					dtGiven = DCSDEV.DCSServerStuff.String2DateTime((string)strArray[3], strAppDateFormat);

					string strFilename;
					int i = 0;
					while (true)
					{
						strFilename = DCSDatabaseIF.GetFullNameOfHistoricImage((string)strArray[1], imgclass, iSubClass, i, true);
						if (strFilename == null) break;
						if (System.IO.File.GetLastWriteTime(strFilename) <= dtGiven)
						{
							SendDDEData(strFilename, m);
							return;
						}
						i++;
					}
					SendDDEData("", m);
					return;
				}
				else    // no date is specified
				{
                    string strName = "";
                    if (DCSDatabaseIF.IsStoredInUsersDB(imgclass, iSubClass))
                    {
                        if (DCSDatabaseIF.GetUsersDBImageCount((string)strArray[1], imgclass, iSubClass) > 0)
                        {
                            SendDDEData("UsersDB", m);
                            return;
                        }
                    }
                    if (DCSDatabaseIF.IsOleDBType())
                    {
                        if (DCSDatabaseIF.GetStoredImageCount((string)strArray[1], imgclass, iSubClass) > 0)
                        {
                            SendDDEData("OleDB", m);
                            return;
                        }
                    }
                    else
                    {
                        strName = DCSDatabaseIF.GetFullnameOfImage((string)strArray[1], imgclass, iSubClass, true);
                        if (strName == null) strName = "";
                    }
					SendDDEData(strName, m);
				}
				return;
			}
			else if ((string)strArray[0] == "GETBADGEDATAPATH")
			{
				SendDDEData(m_dlgBadgingMgt.GetBadgeDataPath(), m);
				return;
			}
			else if ((string)strArray[0] == "GETCHIPID")
			{
				if (m_strLastChipID == null)
					SendDDEData("", m);
				else
					SendDDEData(m_strLastChipID, m);
				return;
			}
			// syntax: GETBIOMETRIC imageID {imageclassEX}
			// if subclass is not specified, defaults to subclass 0
			else if ((string)strArray[0] == "GETBIOMETRIC")
			{
				if (strArray.Count <= 1)
				{
					SendDDEData("", m);
					return;
				}

				DCSDatabaseIF.ImageClass imgclass;
				int iSubClass = 0;
				string strBio;

				if (strArray.Count > 2)
				{
					imgclass = DCSDatabaseIF.StringToClass((string)strArray[2]);
					iSubClass = DCSDatabaseIF.StringToSubClass((string)strArray[2]);
					if (iSubClass == -1) iSubClass = 0;
				}
				else
				{
					imgclass = DCSDatabaseIF.ImageClass.Fingerprint;
					iSubClass = 0;
				}

				strBio = m_dlgCaptureMgt.GetBiometric((string)strArray[1], imgclass.ToString(), iSubClass);
				SendDDEData(strBio, m);
			}
			// syntax: GETDOCBIN imageID [docName] [FileName]
			else if ((string)strArray[0] == "GETDOCBIN")
			{
				string strBio;
				string strDocName;
				if (strArray.Count <= 1)
				{
					SendDDEData("", m);
					return;
				}
				if (strArray.Count > 2)
					strDocName = (string)strArray[2];
				else
					strDocName = (string)strArray[1];

				//strBio = m_dlgCaptureMgt.GetDocumentBIN((string)strArray[1], strDocName);
				strBio = DCSDatabaseIF.GetDocumentBIN((string)strArray[1], strDocName);

				if (strArray.Count > 3)
				{
					System.IO.StreamWriter stream = new System.IO.StreamWriter((string)strArray[3]);
					stream.Write(strBio);
					stream.Close();
				}

				// syh todo
				// Access cannot accept a response if (strBio.Length > 1023)
				if (strBio.Length > 1023) strBio = strBio.Substring(0, 1023);
				SendDDEData(strBio, m);
			}
			// syntax: GETIMAGENAME imageID {imageclassEX}
			// RETURN: full filename of image whether it exists or not; return ERROR if program error
			// if class is not specified, defaults to Portrait
			// if subclass is not specified, defaults to subclass 0
			else if ((string)strArray[0] == "GETIMAGENAME")
			{
				if (strArray.Count <= 1)
				{
					SendDDEData("", m);
					return;
				}
				DCSDatabaseIF.ImageClass imgclass;
				int iSubClass = 0;
				try
				{
					if (strArray.Count > 2)
					{
						imgclass = DCSDatabaseIF.StringToClass((string)strArray[2]);
						iSubClass = DCSDatabaseIF.StringToSubClass((string)strArray[2]);
						if (iSubClass < 0) iSubClass = 0;
					}
					else
					{
						imgclass = DCSDatabaseIF.ImageClass.Portrait;
						iSubClass = 0;
					}
				}
				catch
				{
					SendDDEData("ERROR", m);
					return;
				}
				SendDDEData(DCSDatabaseIF.GetFullnameOfImage((string)strArray[1], imgclass, iSubClass, false), m);
				return;
			}
			else if ((string)strArray[0] == "HASPPERMISSIONS")
			{
				string strPermissions;
				strPermissions = DCSDEV.DCSLicensing.GetHaspFeaturePermissions();
				SendDDEData(strPermissions, m);
				return;
			}
			else if ((string)strArray[0] == "INIFILE")
			{
				SendDDEData("ERROR", m);
				DCSDEV.DCSMsg.Show("Old INIFILE method of interface is not supported.", System.Windows.Forms.MessageBoxIcon.Error);
				return;
			}
			else if ((string)strArray[0] == "ISCHIPIDSCANNED")
			{
				SendDDEData(m_bCurrentIsScanIDType ? "YES" : "NO", m);
				return;
			}
            else if ((string)strArray[0] == "ISOLEDB")
            {
                SendDDEData(DCSDatabaseIF.IsOleDBType() ? "YES" : "NO", m);
                return;
            }
            else if ((string)strArray[0] == "ISPRINTDIRECT")
			{
				SendDDEData(m_bCurrentPrintIsDirect ? "YES" : "NO", m);
				return;
			}
			else if ((string)strArray[0] == "QUEUE_REFNO")
			{
				/* Badge status indicates the badge was enqueued OK.  It will be printed some time later.
				 * QUEUE_REFNO is the number the badge was queued by.
				 * */
				if (m_iLastPrinterRefNo <= 0)
					SendDDEData(m_iLastPrinterRefNo.ToString(), m);
				else
					SendDDEData(m_iLastPrinterRefNo.ToString("00000000"), m);
				return;
			}
			else if ((string)strArray[0] == "SERIALNUMBER")	// used by DCS Apps - there for compatibility with ITC apps
			{
				SendDDEData(DCSDEV.DCSLicensing.GetHaspID(), m);
				return;
			}
			else if ((string)strArray[0] == "BADGE" || (string)strArray[0] == "PRINT")
			{
				switch (this.m_ePrintState)
				{
					default:
					case PrintStates.PRINT_NONE:
					case PrintStates.PRINT_CANCEL:
						SendDDEData("NOTOK", m);
						return;
					case PrintStates.PRINT_OK:
						SendDDEData("OK", m);
						return;
					case PrintStates.PRINT_PENDING:
						// Pause a little before returning busy.
						m_PendingMessage = m;
						m_ePendingRequest = PendingRequest.PENDING_PRINT;
						this.timer1.Start();
						return;
				}
			}
			else if ((string)strArray[0] == "TAKE")
			{
				switch (this.m_eCaptureState)
				{
					default:
					case CaptureStates.CAPTURE_NONE:
					case CaptureStates.CAPTURE_CANCEL:
						SendDDEData("NOTOK", m);
						return;
					case CaptureStates.CAPTURE_OK:
						SendDDEData("OK", m);
						return;
					case CaptureStates.CAPTURE_PENDING:
						// Pause a little before returning busy.
						m_PendingMessage = m;
						m_ePendingRequest = PendingRequest.PENDING_TAKE;
						this.timer1.Start();
						return;
				}
			}
			else if ((string)strArray[0] == "ENCODECHIP_STATUS")
			{
				switch (this.m_eEncodeChipState)
				{
					default:
					case EncodeChipStates.ENCODECHIP_NONE:
					case EncodeChipStates.ENCODECHIP_ERROR:
						SendDDEData("ERROR", m);
						return;
					case EncodeChipStates.ENCODECHIP_CANCEL:
						SendDDEData("CANCEL", m);
						return;
					case EncodeChipStates.ENCODECHIP_OK:
						SendDDEData("OK", m);
						return;
					case EncodeChipStates.ENCODECHIP_PENDING:
						// Pause a little before returning busy.
						m_PendingMessage = m;
						m_ePendingRequest = PendingRequest.PENDING_ENCODECHIP;
						this.timer1.Start();
						return;
				}
			}
			else if ((string)strArray[0] == "SCANDATA_STATUS")
			{
				switch (this.m_eScanDataState)
				{
					default:
					case ScanDataStates.SCANDATA_NONE:
					case ScanDataStates.SCANDATA_ERROR:
						SendDDEData("ERROR", m);
						return;
					case ScanDataStates.SCANDATA_CANCEL:
						SendDDEData("CANCEL", m);
						return;
					case ScanDataStates.SCANDATA_OK:
						SendDDEData("OK", m);
						return;
					case ScanDataStates.SCANDATA_PENDING:
						// Pause a little before returning busy.
						m_PendingMessage = m;
						m_ePendingRequest = PendingRequest.PENDING_SCANDATA;
						this.timer1.Start();
						return;
				}
			}
			else if ((string)strArray[0] == "GENBIO_STATUS")
			{
				switch (this.m_eGenBiometricState)
				{
					default:
					case GenBiometricStates.GENBIO_NONE:
					case GenBiometricStates.GENBIO_ERROR:
						SendDDEData("ERROR", m);
						return;
					case GenBiometricStates.GENBIO_CANCEL:
						SendDDEData("CANCEL", m);
						return;
					case GenBiometricStates.GENBIO_OK:
						SendDDEData("OK", m);
						return;
					case GenBiometricStates.GENBIO_PENDING:
						// Pause a little before returning busy.
						m_PendingMessage = m;
						m_ePendingRequest = PendingRequest.PENDING_GENBIO;
						this.timer1.Start();
						return;
				}
			}
			else if ((string)strArray[0] == "SEARCHBIO_STATUS")
			{
				switch (this.m_eSearchBiometricState)
				{
					default:
					case SearchBiometricStates.SEARCHBIO_NONE:
					case SearchBiometricStates.SEARCHBIO_ERROR:
					case SearchBiometricStates.SEARCHBIO_CANCEL:
					case SearchBiometricStates.SEARCHBIO_OK:
						SendDDEData(m_strSearchResult, m);
						return;
					case SearchBiometricStates.SEARCHBIO_PENDING:
						// Pause a little before returning busy.
						m_PendingMessage = m;
						m_ePendingRequest = PendingRequest.PENDING_SEARCHBIO;
						this.timer1.Start();
						return;
				}
			}
			else if ((string)strArray[0] == "VERIFYBIO_STATUS")
			{
				switch (this.m_eVerifyBiometricState)
				{
					default:
					case VerifyBiometricStates.VERIFYBIO_NONE:
						SendDDEData("NOTOK", m);
						return;
					case VerifyBiometricStates.VERIFYBIO_CANCEL:
					case VerifyBiometricStates.VERIFYBIO_ERROR:
					case VerifyBiometricStates.VERIFYBIO_OK:
						SendDDEData(m_strVerifyResult, m);
						return;
					case VerifyBiometricStates.VERIFYBIO_PENDING:
						// Pause a little before returning busy.
						m_PendingMessage = m;
						m_ePendingRequest = PendingRequest.PENDING_VERIFYBIO;
						this.timer1.Start();
						return;
				}
			}
			else
			{
				SendDDEData("ERROR", m);
				DCSDEV.DCSMsg.Show("Unsupported Request: " + strRequest, MessageBoxIcon.Error);
				return;
			}
		}

		// used by DoDDERequest to send response
		private void SendDDEData(string strResponse, Message m)
		{
			int i;
			const int MAX_RESPONSE_LENGTH = 1024;
			try
			{
				if (strResponse == null || strResponse.Length > MAX_RESPONSE_LENGTH - 1)
				{
					// negative ack
                    PostMessage(m.WParam, WM_DDE_ACK, this.Handle, PackDDElParam(WM_DDE_ACK, (IntPtr)NEGATIVE_ACK, m.LParam));
                    DCSDEV.DCSMsg.Log(" Request NOT OK: ");
                }
				else
				{
					/* DDEDATA struct 
								unsigned short unused:12, 
								fResponse:1, 
								fRelease:1, 
								reserved:1, 
								fAckReq:1; 
								short cfFormat; 
								BYTE  Value[MAX_RESPONSE_LENGTH]; 
							*/
					// structure def starts at low and ends with fAckReq in 2**16 bit
					IntPtr hResponse = GlobalAlloc(0x42, MAX_RESPONSE_LENGTH + 4);
					IntPtr pResponse = GlobalLock(hResponse);
					Marshal.WriteByte(pResponse, 0, 0);		// bits 0-7
					Marshal.WriteByte(pResponse, 1, 0x30);	// bits 8-15; bits 13,12 (=fRelease,fResponse) are set to true
					Marshal.WriteByte(pResponse, 2, 1);		// cFormat low byte
					Marshal.WriteByte(pResponse, 3, 0);		// cFormat high byte
					for (i = 0; i < strResponse.Length; i++)
						Marshal.WriteByte(pResponse, i + 4, (byte)strResponse[i]); // Value string
					Marshal.WriteByte(pResponse, i + 4, 0);
					GlobalUnlock(hResponse);

					IntPtr atomType;
					IntPtr atomRequest;
					UnpackDDElParam(WM_DDE_REQUEST, m.LParam, out atomType, out atomRequest);
					PostMessage(m.WParam, WM_DDE_DATA, this.Handle, PackDDElParam(WM_DDE_DATA, hResponse, atomRequest));
                    DCSDEV.DCSMsg.Log(" Request response: " + strResponse);
                }
			}
			catch (Exception ex)
			{
                DCSMsg.Log("ERROR sending response=" + strResponse, ex);
                //DCSMsg.Show("ERROR sending response=" + strResponse, ex);
                throw;
			}
		}

		private bool DoCapture(string strImageID, string strImageClass, int iSubClass, string strImageTitle, bool bPermitArchive)
		{
			// close all image and badge preview displays
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			if (strImageClass == "All")
			{
				m_dlgCaptureMgt.CaptureAllImages(strImageID, strImageTitle, bPermitArchive);
			}
			else
			{
				DCSDatabaseIF.ImageClass imgcls = DCSDatabaseIF.StringToClass(strImageClass);
				if (imgcls == DCSDatabaseIF.ImageClass.Portrait)
				{
					m_dlgCaptureMgt.CapturePortrait(strImageID, strImageTitle, iSubClass, bPermitArchive);
				}
				else if (imgcls == DCSDatabaseIF.ImageClass.Signature)
				{
					m_dlgCaptureMgt.CaptureSignature(strImageID, strImageTitle, iSubClass, bPermitArchive);
				}
				else if (imgcls == DCSDatabaseIF.ImageClass.Fingerprint)
				{
					m_dlgCaptureMgt.CaptureFingerprint(strImageID, strImageTitle, iSubClass, bPermitArchive);
				}
				else if (imgcls == DCSDatabaseIF.ImageClass.TenPrint)
				{
					m_dlgCaptureMgt.Capture10Print(strImageID, strImageTitle, bPermitArchive);
				}
				else if (imgcls == DCSDatabaseIF.ImageClass.Certificate)
				{
                    if (iSubClass < 0) iSubClass = 0;
					m_dlgCaptureMgt.CaptureCerts(strImageID, strImageTitle, iSubClass);
				}
			}
			// record capture status
			m_eCaptureState = (m_dlgCaptureMgt.CaptureStatus ? CaptureStates.CAPTURE_OK : CaptureStates.CAPTURE_CANCEL);

			//DCSDEV.DCSMsg.Show(m_dlgCaptureMgt.CaptureStatus ? "Capture OK" : "Capture CANCEL");
			return true;
		}

		// strBadgeDatName = name of data source file
		private bool DoEncodeChip(string strBadgeDatName)
		{
			// close all image and badge preview displays
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			// set EncodeChip pending so dde request can report busy until done
			this.m_eEncodeChipState = EncodeChipStates.ENCODECHIP_PENDING;

			int iRet = -1;
			try
			{
				m_strLastChipID = null;
				iRet = m_dlgBadgingMgt.EncodeChip(strBadgeDatName, out m_strLastChipID);
			}
			catch (Exception ex)
			{
				DCSMsg.Show("ERROR: in BadgingMgt.EncodeChip", ex);
			}

			// record EncodeChip status
			if (iRet < 0) m_eEncodeChipState = EncodeChipStates.ENCODECHIP_ERROR;
			else if (iRet == 0) m_eEncodeChipState = EncodeChipStates.ENCODECHIP_CANCEL;
			else m_eEncodeChipState = EncodeChipStates.ENCODECHIP_OK;
			return true;
		}
        // strSource = "2DBar" or "Chip"
		private bool DoScanData(string strSource, string strDestination)
		{
			// close all image and badge preview displays
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			// set ScanData pending so dde request can report busy until done
			this.m_eScanDataState = ScanDataStates.SCANDATA_PENDING;

			int iRet = -1;
			try
			{
				m_strLastChipID = null;
				iRet = m_dlgCaptureMgt.ScanData(strSource, strDestination);
				if (iRet == 1 && strSource.ToUpper() == "CHIP")
				{
					m_strLastChipID = m_dlgCaptureMgt.GetLastChipID();
				}
			}
			catch (Exception ex)
			{
				DCSMsg.Show("ERROR: in CaptureMgt.ScanData", ex);
			}

			// record ScanData status
			if (iRet < 0) m_eScanDataState = ScanDataStates.SCANDATA_ERROR;
			else if (iRet == 0) m_eScanDataState = ScanDataStates.SCANDATA_CANCEL;
			else m_eScanDataState = ScanDataStates.SCANDATA_OK;
			return true;
		}
		private bool DoGenBiometric(string strImageID, DCSDatabaseIF.ImageClass imageClass, int iSubClass)
		{
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			int iRet = m_dlgCaptureMgt.GenerateBiometric(strImageID, imageClass.ToString(), iSubClass, false);	// image id, finger instance 0-9

			// record GenBiometric status
			if (iRet < 0) m_eGenBiometricState = GenBiometricStates.GENBIO_ERROR;
			else if (iRet == 0) m_eGenBiometricState = GenBiometricStates.GENBIO_CANCEL;
			else m_eGenBiometricState = GenBiometricStates.GENBIO_OK;
			return true;
		}
		// capture a test finger image and search to existing biometric for strImageID. 
		private void DoSearchBiometric(string strImageID, DCSDatabaseIF.ImageClass imageClass, int iSubClass)
		{
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			// set Search bio pending so dde request can report busy until done
			m_eSearchBiometricState = SearchBiometricStates.SEARCHBIO_PENDING;

			ArrayList arrayOfImageIDs = new ArrayList();
			string strResult = "ERROR";
			try
			{
				strResult = m_dlgCaptureMgt.SearchBiometric(strImageID, imageClass.ToString(), iSubClass, arrayOfImageIDs);	// image id, fnger instance 0-9
			}
			catch (Exception ex)
			{
				DCSMsg.Show("ERROR: in CaptureMgt.SearchBiometric", ex);
			}

			// record SearchBiometric status
			if (strResult == "OK")
			{
				foreach (string str in arrayOfImageIDs)
					strResult = strResult + "," + str;
			}
			this.m_strSearchResult = strResult;

			if (strResult.StartsWith("CANCEL"))
				m_eSearchBiometricState = SearchBiometricStates.SEARCHBIO_CANCEL;
			else if (strResult.StartsWith("ERROR"))
				m_eSearchBiometricState = SearchBiometricStates.SEARCHBIO_ERROR;
			else
				m_eSearchBiometricState = SearchBiometricStates.SEARCHBIO_OK;	// OK = completed with a OK or NO answer
		}
		// capture a test finger image and compare to existing biometric for strImageID. 
		// Special cases for "_BIN_BARCODE", "_BIN_SMARTCHIP" and "_BIN_MAGSTRIPE"
		private void DoVerifyBiometric(string strImageID, DCSDatabaseIF.ImageClass imageClass, int iSubClass)
		{
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			string strResult = "ERROR";
			try
			{
				strResult = m_dlgCaptureMgt.VerifyBiometric(strImageID, imageClass.ToString(), iSubClass);	// image id, fnger instance 0-9
			}
			catch (Exception ex)
			{
				DCSMsg.Show("ERROR: in CaptureMgt.VerifyBiometric", ex);
			}

			// record VerifyBiometric status
			this.m_strVerifyResult = strResult;

			if (strResult.StartsWith("CANCEL"))
				m_eVerifyBiometricState = VerifyBiometricStates.VERIFYBIO_CANCEL;
			else if (strResult.StartsWith("ERROR"))
				m_eVerifyBiometricState = VerifyBiometricStates.VERIFYBIO_ERROR;
			else
				m_eVerifyBiometricState = VerifyBiometricStates.VERIFYBIO_OK;	// OK = completed with a YES or NO answer
		}

		private bool DoDelete(string strImageID, string strImgClass, int iSubClass)
		{
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			try
			{
				if (strImgClass == "All")
				{
					m_dlgCaptureMgt.DeleteAllImageClasses(strImageID);
				}
				else
				{
					DCSDatabaseIF.ImageClass imgcls = DCSDatabaseIF.StringToClass(strImgClass);
					if (imgcls == DCSDatabaseIF.ImageClass.Portrait)
					{
						m_dlgCaptureMgt.DeletePortrait(strImageID, iSubClass);
					}
					else if (imgcls == DCSDatabaseIF.ImageClass.Signature)
					{
						m_dlgCaptureMgt.DeleteSignature(strImageID, iSubClass);
					}
					else if ((imgcls == DCSDatabaseIF.ImageClass.Fingerprint) || (imgcls == DCSDatabaseIF.ImageClass.TenPrint))
					{
						// delete fingerprints and BINs
						m_dlgCaptureMgt.DeleteFingerprint(strImageID, iSubClass);
					}
					else if (imgcls == DCSDatabaseIF.ImageClass.Certificate)
					{
						m_dlgCaptureMgt.DeleteCerts(strImageID, iSubClass);
					}
				}
			}
			catch (Exception ex)
			{
				DCSMsg.Show("ERROR: in CaptureMgt.Delete...", ex);
			}
			return true;
		}

		private void DoDisplay(string strImageID, string strDisplayLabel, string strImageClass)
		{
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			// get image to display
			if (strImageClass == "All")
			{
				m_dlgCaptureMgt.DisplayAllImageClasses(strImageID, strDisplayLabel);
			}
			else
			{
				DCSDatabaseIF.ImageClass imgcls = DCSDatabaseIF.StringToClass(strImageClass);
				int iSubClass = DCSDatabaseIF.StringToSubClass(strImageClass);
				if (imgcls == DCSDatabaseIF.ImageClass.Portrait)
				{
					m_dlgCaptureMgt.DisplayPortrait(strImageID, iSubClass, strDisplayLabel);
				}
				else if (imgcls == DCSDatabaseIF.ImageClass.Signature)
				{
					m_dlgCaptureMgt.DisplaySignature(strImageID, iSubClass, strDisplayLabel);
				}
				else if ((imgcls == DCSDatabaseIF.ImageClass.Fingerprint) || (imgcls == DCSDatabaseIF.ImageClass.TenPrint))
				{
					m_dlgCaptureMgt.DisplayFingerprint(strImageID, iSubClass, strDisplayLabel);
				}
				else if (imgcls == DCSDatabaseIF.ImageClass.Certificate)
				{
                    if (iSubClass < 0) iSubClass = 0;
					m_dlgCaptureMgt.DisplayCerts(strImageID, iSubClass, strDisplayLabel);
				}
			}
		}

		private bool DoFIPSImportExport(bool bImport, string strFileName, string strImageID)
		{
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			return m_dlgCaptureMgt.FIPSImportExport(bImport, strFileName, strImageID);
		}

		// Reduce overall size so height is no more the the specified height.
		private bool DoAdjustFileSize(string strFileName, String strSize)
		{
			int idx = strSize.ToUpper().IndexOf('X');
			int width = Convert.ToInt32(strSize.Substring(0,idx));
			int height = Convert.ToInt32(strSize.Substring(idx + 1));
			// DCSMsg.Show("Adjust resolution to " + strSize + " " + width.ToString() + " " + height.ToString());
			try
			{
				Bitmap bitmap1 = new Bitmap(strFileName);
				if (bitmap1.Height > height)
				{
					Bitmap bitmap2 = new Bitmap(bitmap1,(bitmap1.Width * height) / bitmap1.Height, height);
					string strTemp = System.IO.Path.Combine(System.IO.Path.GetDirectoryName(strFileName), System.IO.Path.GetFileNameWithoutExtension(strFileName) + "_Temp" + System.IO.Path.GetExtension(strFileName));
					bitmap2.Save(strTemp, System.Drawing.Imaging.ImageFormat.Jpeg);
					bitmap2.Dispose();
					bitmap1.Dispose();
					System.IO.File.Delete(strFileName);
					System.IO.File.Move(strTemp, strFileName);
				}
			}
			catch (Exception ex)
			{
				DCSMsg.Show(ex);
				return false;
			}
			return true;
		}

		private bool DoImportExport(bool bImport, string strFileName, string strImageID, string strImgClass, int iSubClass)
		{
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			if (strImgClass == "All") return false;

			DCSDatabaseIF.ImageClass imgcls = DCSDatabaseIF.StringToClass(strImgClass);
			if (imgcls == DCSDatabaseIF.ImageClass.Portrait)
			{
				// derived from m_dlgCaptureMgt.DeletePortrait(strImageID, iSubClass);
				return m_dlgCaptureMgt.ImportExportPortrait(bImport, strFileName, strImageID, iSubClass);
			}
			else if (imgcls == DCSDatabaseIF.ImageClass.Signature)
			{
				return m_dlgCaptureMgt.ImportExportSignature(bImport, strFileName, strImageID, iSubClass);
			}
			else if ((imgcls == DCSDatabaseIF.ImageClass.Fingerprint) || (imgcls == DCSDatabaseIF.ImageClass.TenPrint))
			{
				return m_dlgCaptureMgt.ImportExportFingerprint(bImport, strFileName, strImageID, iSubClass);
			}
            else if (imgcls == DCSDatabaseIF.ImageClass.Certificate)
            {
                return m_dlgCaptureMgt.ImportExportCerts(bImport, strFileName, strImageID, iSubClass);
            }
			return false;
		}

		// Merge images from source directory into current database of images
		// source must use file directories for image storage - not oleDB 
		private bool DoImportMerge(string strImageID, string strSourceID, string strSourceDataRoot, string strImgClass)
		{
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			if (strImgClass == "All")
			{
				return m_dlgCaptureMgt.ImportMergeAll(strImageID, strSourceID, strSourceDataRoot);
			}
			else
			{
				DCSDatabaseIF.ImageClass imgcls = DCSDatabaseIF.StringToClass(strImgClass);
				if (imgcls == DCSDatabaseIF.ImageClass.Portrait)
				{
					return m_dlgCaptureMgt.ImportMergePortrait(strImageID, strSourceID, strSourceDataRoot);
				}
				else if (imgcls == DCSDatabaseIF.ImageClass.Signature)
				{
					return m_dlgCaptureMgt.ImportMergeSignature(strImageID, strSourceID, strSourceDataRoot);
				}
				else if ((imgcls == DCSDatabaseIF.ImageClass.Fingerprint) || (imgcls == DCSDatabaseIF.ImageClass.TenPrint))
				{
					return m_dlgCaptureMgt.ImportMergeFingerprint(strImageID, strSourceID, strSourceDataRoot);
				}
				else if (imgcls == DCSDatabaseIF.ImageClass.Certificate)
				{
					return m_dlgCaptureMgt.ImportMergeCerts(strImageID, strSourceID, strSourceDataRoot);
				}
				return false;
			}
		}

		private void DoPreview(string strBadgeDatFileName, string strLabel)
		{
			// close all image and badge preview displays
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			m_dlgBadgingMgt.PreviewBadge(strBadgeDatFileName, strLabel);
		}

		private void DoPrint(string strBadgeDatFileName)
		{
			// Decide whether to use queue or print direct
			m_bCurrentIsScanIDType = m_bCurrentPrintIsDirect = m_dlgBadgingMgt.IsReturnDataType(strBadgeDatFileName);
			m_strLastChipID = null;

			if (m_bCurrentPrintIsDirect)
				this.DoPrintDirect(strBadgeDatFileName);
			else
				this.DoPrintToQueue(strBadgeDatFileName);
			return;
		}

		private void DoPrintDirect(string strBadgeDatFileName)
		{
			// close all image and badge preview displays
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			if (strBadgeDatFileName != null)
			{
				// set print pending so dde request can report busy until done
				this.m_ePrintState = PrintStates.PRINT_PENDING;

				string strChipID = null;
				int iRet = m_dlgBadgingMgt.PrintBadgeDirect(strBadgeDatFileName, out strChipID);
				if (iRet > 0)
					this.m_ePrintState = PrintStates.PRINT_OK;
				else if (iRet == 0)
					this.m_ePrintState = PrintStates.PRINT_CANCEL;
				else
					this.m_ePrintState = PrintStates.PRINT_CANCEL;
				this.m_strLastChipID = strChipID;
				return;
			}
			else this.m_ePrintState = PrintStates.PRINT_CANCEL;
		}

		// set m_iLastPrinterRefNo - queue reference number GT 0 if successful, LT 0 if error
		private void DoPrintToQueue(string strBadgeDatFileName)
		{
			m_iLastPrinterRefNo = 0;

			// close all image and badge preview displays
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			if (strBadgeDatFileName != null)
			{
				// set print pending so dde request can report busy until done
				this.m_ePrintState = PrintStates.PRINT_PENDING;

				// start printer process - or do nothing if aleady running
				m_dlgBadgingMgt.StartDCSPrinter();
				//m_dlgBadgingMgt.PrintQueueStart();

				m_iLastPrinterRefNo = m_dlgBadgingMgt.EnqueueBadge(strBadgeDatFileName);
				if (m_iLastPrinterRefNo > 0)
					this.m_ePrintState = PrintStates.PRINT_OK;
				else
					this.m_ePrintState = PrintStates.PRINT_CANCEL;
				return;
			}
			else this.m_ePrintState = PrintStates.PRINT_CANCEL;
		}

		private void buttonSetup_Click(object sender, System.EventArgs e)
		{
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			if (m_dlgSetup.Visible == true)
				m_dlgSetup.Focus();
			else
			{
				m_dlgSetup.ShowDialog(this);
				DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("DCSSDK_Mgt");
				m_bShowTestControls = ps.GetBoolParameter("ShowTestControls", true);
				if (!m_bShowTestControls) this.checkBoxShowTest.Checked = false;
				this.checkBoxShowTest.Visible = m_bShowTestControls;
			}
		}

		private void buttonAllCapture_Click(object sender, System.EventArgs e)
		{
			bool bPermitArchive = true;
			if (this.checkBoxRetake.Checked) bPermitArchive = false;
			DoCapture(this.tbImageID.Text, this.cbImageClass.Text, -1, this.tbImageTitle.Text, bPermitArchive);
		}

		private void buttonExit_Click(object sender, System.EventArgs e)
		{
			DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("DCSSDK_Mgt");
			ps.WriteRectParameter("DDEServerRect", new Rectangle(this.Location, this.ClientRectangle.Size));

			Application.Exit();
		}

		private void buttonLayout_Click(object sender, System.EventArgs e)
		{
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			m_dlgBadgingMgt.DesignBadge(null);
		}

		private void buttonPrint_Click(object sender, System.EventArgs e)
		{
			this.DoPrint(this.cbBadgeDat.Text);
		}

		private void buttonPreview_Click(object sender, System.EventArgs e)
		{
			Cursor cursor = this.Cursor;
			this.Cursor = Cursors.WaitCursor;
			DoPreview(this.cbBadgeDat.Text, this.cbBadgeDat.Text);	// set display label same as badge design name
			this.Cursor = cursor;
		}

		private const int WM_DDE_FIRST = 0x03E0;
		private const int WM_DDE_INITIATE = (WM_DDE_FIRST);
		private const int WM_DDE_TERMINATE = (WM_DDE_FIRST + 1);
		private const int WM_DDE_ACK = (WM_DDE_FIRST + 4);
		private const int WM_DDE_DATA = (WM_DDE_FIRST + 5);
		private const int WM_DDE_REQUEST = (WM_DDE_FIRST + 6);
		private const int WM_DDE_EXECUTE = (WM_DDE_FIRST + 8);

		//		[System.Security.Permissions.PermissionSet(System.Security.Permissions.SecurityAction.Demand, Name="FullTrust")]
		protected override void WndProc(ref Message m)
		{
			// Listen for operating system messages.
			switch (m.Msg)
			{
				case WM_DDE_INITIATE:
                    DCSDEV.DCSMsg.Log("WM_DDE_INITIATE: m_bStillStartingUp = " + (m_bStillStartingUp? "true" : "false") + " ; KILL Pending = " + (m_bKillPending? "true" : "false"));

                    //MessageBox.Show("WM_DDE_INITIATE");
                    string app;
					string topic;
					IntPtr hwndClient;
					IntPtr atomNewApp;
					IntPtr atomNewTopic;
					IntPtr atomApp;	// = (IntPtr)((m.LParam.ToInt32())&0xFFFF);
					IntPtr atomTopic; // = (IntPtr)(((m.LParam.ToInt32())>>16)&0xFFFF);
					UnpackDDElParam(WM_DDE_INITIATE, m.LParam, out atomApp, out atomTopic);

					StringBuilder sb = new StringBuilder(20);
					GlobalGetAtomName(atomApp, sb, sb.Capacity);
					app = sb.ToString();
					GlobalGetAtomName(atomTopic, sb, sb.Capacity);
					topic = sb.ToString();
					if ((app.ToString().ToUpper().CompareTo("SYNBADGE") == 0 && topic.ToString().ToUpper().CompareTo("IMAGE") == 0)
						|| (app.ToString().ToUpper().CompareTo("DCS") == 0 && topic.ToString().ToUpper().CompareTo("DDESERVER") == 0))
					{
                        //MessageBox.Show("WM_DDE_INITIATE succeeded");
						hwndClient = (IntPtr)(m.WParam.ToInt32());

						// create new atoms for ACK
						atomNewApp = GlobalAddAtom(app);
						atomNewTopic = GlobalAddAtom(topic);

						SendMessage(m.WParam, WM_DDE_ACK, this.Handle, PackDDElParam(WM_DDE_ACK, atomNewApp, atomNewTopic));
					}
					break;
				case WM_DDE_TERMINATE:
					// MessageBox.Show("WM_DDE_TERMINATE");
					PostMessage(m.WParam, WM_DDE_TERMINATE, this.Handle, IntPtr.Zero);
                    DCSDEV.DCSMsg.Log("WM_DDE_TERMINATE: KILL Pending = " + (m_bKillPending? "true" : "false"));
                    if (m_bKillPending)
					{
						m_bKillPending = false;
						this.Close();
					}
					break;
				case WM_DDE_EXECUTE:
					// perform the operation and send ack
					DoDDEExecute(m);
					break;
				case WM_DDE_REQUEST:
					DoDDERequest(m);
					break;
			}
			base.WndProc(ref m);
		}

		private void timer1_Tick(object sender, System.EventArgs e)
		{
			this.timer1.Stop();

			/***************************************************************** 
			 * This attempts to bring covered up server window to the foreground
			 * so the user does not lose it. It works nice for a lot of cases
			 * but messes up for image capture.
			m_iTicks++;
			//if (DCSDEV.DCSMsg.IsMessageShowing)
			if (m_iTicks > 3)
			{
				m_iTicks = 0;
				////Process procThis = Process.GetCurrentProcess();
				////SetForegroundWindow(procThis.MainWindowHandle);
				//this.Activate();
				////DCSMsg.Beep();
			}
			****************************************************/


			if (m_ePendingRequest == PendingRequest.PENDING_PRINT)
			{
				switch (this.m_ePrintState)
				{
					default:
					case PrintStates.PRINT_NONE:
					case PrintStates.PRINT_CANCEL:
						SendDDEData("NOTOK", m_PendingMessage);
						break;
					case PrintStates.PRINT_OK:
						SendDDEData("OK", m_PendingMessage);
						break;
					case PrintStates.PRINT_PENDING:
						System.Threading.Thread.Sleep(2000);    //2.22.2008
						SendDDEData("BUSY", m_PendingMessage);
						break;
				}
			}
			else if (m_ePendingRequest == PendingRequest.PENDING_TAKE)
			{
				switch (this.m_eCaptureState)
				{
					default:
					case CaptureStates.CAPTURE_NONE:
					case CaptureStates.CAPTURE_CANCEL:
						SendDDEData("NOTOK", m_PendingMessage);
						break;
					case CaptureStates.CAPTURE_OK:
						SendDDEData("OK", m_PendingMessage);
						break;
					case CaptureStates.CAPTURE_PENDING:
						System.Threading.Thread.Sleep(2000);    //2.22.2008
						SendDDEData("BUSY", m_PendingMessage);
						break;
				}
			}
			else if (m_ePendingRequest == PendingRequest.PENDING_ENCODECHIP)
			{
				switch (this.m_eEncodeChipState)
				{
					default:
					case EncodeChipStates.ENCODECHIP_NONE:
					case EncodeChipStates.ENCODECHIP_ERROR:
						SendDDEData("ERROR", m_PendingMessage);
						break;
					case EncodeChipStates.ENCODECHIP_CANCEL:
						SendDDEData("CANCEL", m_PendingMessage);
						break;
					case EncodeChipStates.ENCODECHIP_OK:
						SendDDEData("OK", m_PendingMessage);
						break;
					case EncodeChipStates.ENCODECHIP_PENDING:
						System.Threading.Thread.Sleep(2000);    //2.22.2008
						SendDDEData("BUSY", m_PendingMessage);
						break;
				}
			}
			else if (m_ePendingRequest == PendingRequest.PENDING_SCANDATA)
			{
				switch (this.m_eScanDataState)
				{
					default:
					case ScanDataStates.SCANDATA_NONE:
					case ScanDataStates.SCANDATA_ERROR:
						SendDDEData("ERROR", m_PendingMessage);
						break;
					case ScanDataStates.SCANDATA_CANCEL:
						SendDDEData("CANCEL", m_PendingMessage);
						break;
					case ScanDataStates.SCANDATA_OK:
						SendDDEData("OK", m_PendingMessage);
						break;
					case ScanDataStates.SCANDATA_PENDING:
						System.Threading.Thread.Sleep(2000);    //2.22.2008
						SendDDEData("BUSY", m_PendingMessage);
						break;
				}
			}
			else if (m_ePendingRequest == PendingRequest.PENDING_GENBIO)
			{
				switch (this.m_eGenBiometricState)
				{
					default:
					case GenBiometricStates.GENBIO_NONE:
					case GenBiometricStates.GENBIO_ERROR:
						SendDDEData("ERROR", m_PendingMessage);
						break;
					case GenBiometricStates.GENBIO_CANCEL:
						SendDDEData("CANCEL", m_PendingMessage);
						break;
					case GenBiometricStates.GENBIO_OK:
						SendDDEData("OK", m_PendingMessage);
						break;
					case GenBiometricStates.GENBIO_PENDING:
						System.Threading.Thread.Sleep(2000);    //2.22.2008
						SendDDEData("BUSY", m_PendingMessage);
						break;
				}
			}
			else if (m_ePendingRequest == PendingRequest.PENDING_SEARCHBIO)
			{
				switch (this.m_eSearchBiometricState)
				{
					default:
					case SearchBiometricStates.SEARCHBIO_NONE:
						SendDDEData("NOTOK", m_PendingMessage);
						break;
					case SearchBiometricStates.SEARCHBIO_CANCEL:
					case SearchBiometricStates.SEARCHBIO_ERROR:
					case SearchBiometricStates.SEARCHBIO_OK:
						SendDDEData(m_strSearchResult, m_PendingMessage);
						break;
					case SearchBiometricStates.SEARCHBIO_PENDING:
						System.Threading.Thread.Sleep(2000);    //2.22.2008
						SendDDEData("BUSY", m_PendingMessage);
						break;
				}
			}
			else if (m_ePendingRequest == PendingRequest.PENDING_VERIFYBIO)
			{
				switch (this.m_eVerifyBiometricState)
				{
					default:
					case VerifyBiometricStates.VERIFYBIO_NONE:
						SendDDEData("NOTOK", m_PendingMessage);
						break;
					case VerifyBiometricStates.VERIFYBIO_CANCEL:
					case VerifyBiometricStates.VERIFYBIO_ERROR:
					case VerifyBiometricStates.VERIFYBIO_OK:
						SendDDEData(m_strVerifyResult, m_PendingMessage);
						break;
					case VerifyBiometricStates.VERIFYBIO_PENDING:
						System.Threading.Thread.Sleep(2000);    //2.22.2008
						SendDDEData("BUSY", m_PendingMessage);
						break;
				}
			}
			m_ePendingRequest = PendingRequest.PENDING_NONE;
			return;
		}
		protected override void OnClosing(CancelEventArgs e)
		{
			DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("DCSSDK_Mgt");
			ps.WriteRectParameter("DDEServerRect", new Rectangle(this.Location, this.ClientRectangle.Size));

			base.OnClosing(e);
		}

		private void buttonDisplay_Click(object sender, System.EventArgs e)
		{
			DoDisplay(this.tbImageIDDisplay.Text, null, this.cbImageClassDisplay.Text);
		}

		private void buttonClose_Click(object sender, System.EventArgs e)
		{
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();
		}

		private void checkBoxShowTest_CheckedChanged(object sender, System.EventArgs e)
		{
			this.groupBoxTestControls.Visible = this.checkBoxShowTest.Checked;
			if (this.checkBoxShowTest.Checked)
			{
				this.Width = 640;
				this.Height = 480;
			}
			else
			{
				this.Width = 320;
				this.Height = 120;
			}
		}

		private void buttonAbout_Click(object sender, System.EventArgs e)
		{
			DCSDEV.DDEServer.AboutDDEServer dlgAbout = new AboutDDEServer();
			dlgAbout.ShowDialog(this);
		}

		private void buttonMinimize_Click(object sender, System.EventArgs e)
		{
			DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("DCSSDK_Mgt");
			m_bShowTestControls = ps.GetBoolParameter("ShowTestControls", true);
			this.checkBoxShowTest.Visible = m_bShowTestControls;

			this.checkBoxShowTest.Checked = false;
			this.Width = 320;
			this.Height = 120;

			this.WindowState = FormWindowState.Minimized;
		}

		private void buttonStartPrinter_Click(object sender, System.EventArgs e)
		{
			// close all image and badge preview displays
			m_dlgCaptureMgt.CloseDisplays();
			m_dlgBadgingMgt.ClosePreview();

			// start printer process - or do nothing if aleady running
			m_dlgBadgingMgt.StartDCSPrinter();
			//SetForegroundWindow(m_dlgBadgingMgt.m_dlgPrinter.Activate());   //.MainWindowHandle);
			m_dlgBadgingMgt.PrintShowNormal();
		}

		private void buttonGenFips_Click(object sender, EventArgs e)
		{
			DoFIPSImportExport(false, null, this.tbImageID.Text);
		}

		private void buttonReadFips_Click(object sender, EventArgs e)
		{
			DoFIPSImportExport(true, null, this.tbImageID.Text);
		}
	}
}
