using System;
using System.Collections;
using System.Windows.Forms;
//using System.Drawing;
//using System.ComponentModel;
//using System.Data;

//using System.Reflection;	// for enumeration

namespace DCSSDK
{
	/// <summary>
	/// Summary description for StaticLoadMgt.
	/// This class supports dynamic loading of a class that supports the indicated interface.  
	///	Methods
	///	  object GetSelectedClass()
	/// Properties
	///   ArrayList CaptureClassNames
	///   string SelectedCaptureClass
	/// </summary>
	/// 
	public class StaticLoadMgt
	{
		private string m_strSelectedType = null;

		private DCSSDK.DCSDatabaseIF.ImageClass m_imageClass;
		private string[] m_listTypeNames;
		private object[] m_listTypes;


		// the following arrays must align with eachother
		private string[] m_listTypeNames_Portrait = {"Canon Camera", "DCS8000 Camera", "Get from File", "Twain Device"};
		private string[] m_listTypeNames_Fingerprint = {"Crossmatch Scanner", "FDU04 Scanner", "Get from File", "Twain Device"};
		private string[] m_listTypeNames_Signature = {"Topaz Tablet", "Get from File", "Twain Device"};

		private DCSSDK_ICapture[] m_listTypes_Portrait = 
		{
			new DCSSDK.CanonCamera.DCSSDK_CanonCamera(), 
			new DCSSDK.DCS8000.DCSSDK_DCS8000(), 
			new DCSSDK.FromFile.DCSSDK_FromFile(), 
			new DCSSDK.DCSTwain.DCSSDK_Twain()
		};
		private DCSSDK_IFingerCapture[] m_listTypes_Fingerprint = 
		{
			new DCSSDK.CrossMatch.DCSSDK_CrossMatch(),
			new DCSSDK.FDU04.DCSSDK_FDU04(),
			new DCSSDK.FromFile.DCSSDK_FingerFromFile(), 
			new DCSSDK.DCSTwain.DCSSDK_FingerTwain()
		};
		private DCSSDK_ISigCapture[] m_listTypes_Signature = 
		{
			new DCSSDK.DCSTopaz.DCSSDK_Topaz(), 
			new DCSSDK.FromFile.DCSSDK_SigFromFile(),
			new DCSSDK.DCSTwain.DCSSDK_SigTwain()
		};

		public StaticLoadMgt(DCSSDK.DCSDatabaseIF.ImageClass imageClass, Type tInterface)
		{
			//
			// TODO: Add constructor logic here
			//
			m_imageClass = imageClass;
			switch (m_imageClass)
			{
				default:
				case DCSSDK.DCSDatabaseIF.ImageClass.Portrait:
					m_listTypeNames = m_listTypeNames_Portrait;
					m_listTypes = m_listTypes_Portrait;
					break;
				case DCSSDK.DCSDatabaseIF.ImageClass.Signature:
					m_listTypeNames = m_listTypeNames_Signature;
					m_listTypes = m_listTypes_Signature;
					break;
				case DCSSDK.DCSDatabaseIF.ImageClass.Fingerprint:
					m_listTypeNames = m_listTypeNames_Fingerprint;
					m_listTypes = m_listTypes_Fingerprint;
					break;
			}
		}

		/// <summary>
		/// Get list of class type names generated upon creation.
		/// </summary>
		public string[] CaptureClassNames
		{
			get
			{
				return this.m_listTypeNames;
			}
		}

		/// <summary>
		/// Get class object that matches the selected type.
		/// </summary>
		/// <returns>
		/// object that supports the required interface type.  
		/// The returned object can be cast to the required interface type.
		/// </returns>
		public object GetSelectedClass()
		{
			if (m_strSelectedType == null || m_strSelectedType == "") 
			{
				DCSMsg.Show("No capture device has been selected.");
				return null;
			}
			try
			{
				// find the selected capture type in the array lists
				int index=0;
				for (index=0; index<this.m_listTypeNames.Length; index++)
					if (m_listTypeNames[index] == m_strSelectedType)
					{
						return m_listTypes[index];
					}
				DCSMsg.Show(String.Format("The selected capture device '{0}' is not available.", m_strSelectedType));
				return null;
			}
			catch(Exception ex)
			{
				DCSMsg.Show("ERROR in DLMCreateInstance: ", ex);
				return null;
			}
		}

		/// <summary>
		/// Gets or Sets a class name from the list of class names that conform to the required interface.
		/// </summary>
		public string SelectedCaptureClass
		{
			get { return m_strSelectedType; }
			set 
			{ 
				m_strSelectedType = value;
				foreach(string classname in m_listTypeNames)
					if (classname == m_strSelectedType) return;
				DCSMsg.Show(String.Format("The specified capture device '{0}' is not available.", m_strSelectedType));
				m_strSelectedType = null;
			}
		}
	}
}
