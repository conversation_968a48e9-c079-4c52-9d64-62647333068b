using System;
using System.ComponentModel;
using System.Collections;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;

using DCSDEV;

namespace DCSDEV.DCSDesign
{
	/// <summary>
	/// Summary description for DCSDesignObjectGraphics.
	/// </summary>
	internal class DCSDesignObjectGraphics : System.ComponentModel.Component
	{
		private Graphics m_gr;
		private Bitmap m_bitmapWork = null; 
		private DCSDesignObject m_designObject;
		private Rectangle m_rectObjectAdjustedScaled;
		private double m_dScale;

		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		public DCSDesignObjectGraphics(System.ComponentModel.IContainer container)
		{
			///
			/// Required for Windows.Forms Class Composition Designer support
			///
			container.Add(this);
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
		}

		internal DCSDesignObjectGraphics(Graphics gr, DCSDesignObject designObject, double dScale)
		{
			///
			/// Required for Windows.Forms Class Composition Designer support
			///
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			m_gr = gr;
			m_designObject = designObject;
			m_dScale = dScale;

			if (m_designObject.LineWidth > 0)
			{
				// reduce rectangle to make room for frame.  NOTE this changes the aspect ratio of the space available for the image.
				Rectangle rectDesignObjectAdjusted;
				rectDesignObjectAdjusted = Rectangle.Inflate(m_designObject.Bounds, -m_designObject.LineWidth, -m_designObject.LineWidth);
				// adjust for scale 
				m_rectObjectAdjustedScaled = DCSMath.RectTimesDouble(rectDesignObjectAdjusted, m_dScale);
				// When there is a frame draw the interior one pixel larger to prevent a gap from round-offs
				m_rectObjectAdjustedScaled = Rectangle.Inflate(m_rectObjectAdjustedScaled, 1, 1);
			}
			else
			{
				// adjust for scale 
				m_rectObjectAdjustedScaled = DCSMath.RectTimesDouble(designObject.Bounds, m_dScale);
			}
			
			if (designObject.DesignObjectImage == null
				&& designObject.DesignObjectImageName != null
				&& designObject.DesignObjectImageName != String.Empty)
			{
				designObject.DesignObjectImage = (Bitmap)DCSDEV.DCSDesignDataAccess.GetImage(designObject.DesignObjectImageName, true);
			}
			// the work image begins as the original image but may be replaced if transform is needed
			m_bitmapWork = designObject.DesignObjectImage;

			//detect if transform for gray, back detect, rotation etc is necessary 
			//if so substitute the original image with a new temp work image clone
			if (this.IsCloneNecessary(m_bitmapWork))
			{
				// create a new ARGB bmTemp clone to work on - Alpha supports semi-transparent pixels
				Bitmap bm = m_bitmapWork.Clone(new Rectangle(new Point(0, 0), m_bitmapWork.Size), System.Drawing.Imaging.PixelFormat.Format32bppArgb);
				m_bitmapWork = bm;
			}
		}

		/// <summary> 
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
				if (m_bitmapWork != m_designObject.DesignObjectImage && m_bitmapWork != null) m_bitmapWork.Dispose();
			}
			base.Dispose( disposing );
		}

		#region Component Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			components = new System.ComponentModel.Container();
		}
		#endregion
		
		// do everything necessary to draw m_bitmapWork
		internal void ProcessImageObject(DCSDesign.RipMode eRipMode)
		{
			if (m_bitmapWork != null)
			{
				// object has no background - ie transparent
				// rotation of foreground (with background if there is one)
				if (m_designObject.RotateFlip != System.Drawing.RotateFlipType.RotateNoneFlipNone)
					m_bitmapWork.RotateFlip(m_designObject.RotateFlip);

				Rectangle rectSrcClip;
				Rectangle rectVisibleAreaScaled;
				this.AlignImageScaled(out rectVisibleAreaScaled, out rectSrcClip, m_bitmapWork, m_rectObjectAdjustedScaled, m_dScale);
				// doesnt work m_bitmapWork = AdjustForResolution(ref m_bitmapWork, rectVisibleAreaScaled, ref rectSrcClip);

				if (m_designObject.BackDetectEnabled)
				{
					//detect if transform for gray, back detect, rotation etc is necessary 
					//if so substitute the original image with a new temp work image clone
					if (!Image.IsAlphaPixelFormat(m_bitmapWork.PixelFormat))
					{
						// create a new ARGB bmTemp clone to work on - Alpha supports semi-transparent pixels
						Bitmap bm = m_bitmapWork.Clone(new Rectangle(new Point(0, 0), m_bitmapWork.Size), System.Drawing.Imaging.PixelFormat.Format32bppArgb);
						m_bitmapWork = bm;
					}
					this.PrepBackgroundReplacementAndApplyGray(true  /*TransparencyToo*/);
				}
				else
					// modify m_bitmapWork for Transform to Gray if necessary
					this.ApplyGrayTransform(m_bitmapWork, true  /*TransparencyToo*/);

				// modify bmTemp - set transparent alpha for ellipse and other Framing - if any
				this.ApplyFraming(ref m_bitmapWork);

				m_gr.DrawImage(m_bitmapWork, rectVisibleAreaScaled, rectSrcClip, GraphicsUnit.Pixel);
			}
			this.DrawOutline();
		}

		private void ApplyFraming(ref Bitmap bitmap)
		{
			// check image
			if (bitmap == null) return;

			if (m_designObject.Framing == DCSDEV.DCSDatatypes.FramingMode.ELLIPSE)
			{
				ApplyEllipse(ref bitmap);
				return;
			}

			if (m_designObject.Framing == DCSDEV.DCSDatatypes.FramingMode.ROUNDED_ALLCORNERS
			||  m_designObject.Framing == DCSDEV.DCSDatatypes.FramingMode.ROUNDED_TOPCORNERS)
			{
				ApplyCornerRound(ref bitmap);
				return;
			}

			Color color;
			int alpha = 0;
			int w = bitmap.Width;
			int h = bitmap.Height;
			double ww = w*w/4;	// horizontal radius squared
			double hh = h*h/4;	// vertical radius squared
			double yy, xx, rr;	// x,y and distance from center squared normalized to 1.0
			double RR;			// fading starts at normalized distance rr=RR from center
			double RRMAX;		// fading ends at normalized distance rr=RRMAX from center

			if (m_designObject.Framing == DCSDEV.DCSDatatypes.FramingMode.FADED_ELLIPSE1)
			{
				RR = 0.65;
				RRMAX = 1.0; 
			}
			else if (m_designObject.Framing == DCSDEV.DCSDatatypes.FramingMode.FADED_ELLIPSE2)
			{
				RR = 0.35;
				RRMAX = 1.1; 
			}
			else return;

			for (int y=0; y<h; y++)
			{
                System.Windows.Forms.Application.DoEvents();
                yy = (double)(y - h / 2) * (double)(y - h / 2);
				for (int x=0; x<w; x++)
				{
					xx = (double)(x-w/2) * (double)(x-w/2);
					rr = xx / ww + yy / hh;
					if (rr >= RRMAX)
					{
						color = bitmap.GetPixel(x,y);
						bitmap.SetPixel(x, y, Color.FromArgb(0, color));
					}
					else if (rr >= RR)
					{
						color = bitmap.GetPixel(x,y);
						//alpha = (int)(255.0 * (RRMAX-rr) / (RRMAX - RR) );
						alpha = (int)(color.A * (RRMAX-rr) / (RRMAX - RR) );
						bitmap.SetPixel(x, y, Color.FromArgb(alpha, color));
					}
				}
			}
		}

		private void ApplyCornerRound(ref Bitmap bitmap)
		{
			Color color;
			int w = bitmap.Width;
			int h = bitmap.Height;

			double yy, xx, rr;	// x,y and distance from center squared normalized to 1.0

			int D = DCSMath.TimesDouble(Math.Min(w,h), 0.35);
			double DD = (double)D * (double)D;
			int X,Y;

			for (int y=0; y<D; y++)
			{
                System.Windows.Forms.Application.DoEvents();
                yy = (double)(y - D) * (double)(y - D);
				for (int x=0; x<D; x++)
				{
					xx = (double)(x-D) * (double)(x-D);
					rr = xx / DD + yy / DD;
					if (rr >= 1.0)
					{
						X = w - x -1;

						// top left and right corners
						color = bitmap.GetPixel(x,y);
						bitmap.SetPixel(x, y, Color.FromArgb(0, color));

						color = bitmap.GetPixel(X,y);
						bitmap.SetPixel(X, y, Color.FromArgb(0, color));

						if (m_designObject.Framing == DCSDEV.DCSDatatypes.FramingMode.ROUNDED_ALLCORNERS)
						{
							// bottom lect and right corners
							Y = h - y - 1;
							color = bitmap.GetPixel(x,Y);
							bitmap.SetPixel(x, Y, Color.FromArgb(0, color));

							color = bitmap.GetPixel(X,Y);
							bitmap.SetPixel(X, Y, Color.FromArgb(0, color));
						}
					}
				}
			}
		}
		
		private void ApplyEllipse(ref Bitmap bitmap)
		{
			// check image
			if (bitmap == null) return;

			Bitmap imageEllipse = new Bitmap(bitmap.Width, bitmap.Height);
			Graphics gr = Graphics.FromImage(imageEllipse);
			gr.Clear(Color.White);
			gr.FillEllipse(new SolidBrush(Color.Black),0,0,bitmap.Width, bitmap.Height);
			// transparent is 255

			Color color;
			Color colorE;

			for (int y=0; y<bitmap.Height; y++)
			{
                System.Windows.Forms.Application.DoEvents();
                for (int x = 0; x < bitmap.Width; x++)
				{
					colorE = imageEllipse.GetPixel(x,y);
					if (colorE.R == 255)
					{
						color = bitmap.GetPixel(x,y);
						bitmap.SetPixel(x, y, Color.FromArgb(0, color));
					}
				}
			}
			gr.Dispose();
			imageEllipse.Dispose();
		}

        // this is to reduce the resolution of the bmTemp before lengthy processing if its input resolution 
        // is much more than needed.
        private Bitmap AdjustForResolution(ref Bitmap bitmap, Rectangle rectAdjusted, ref Rectangle rectSrcClip)
        {
            if (bitmap.Width > DCSMath.TimesDouble(rectAdjusted.Width, 1.25))
            {
                Graphics grTemp;
                Bitmap bmTemp;
                int iWorkHeight = rectAdjusted.Height;
                int iWorkWidth = rectAdjusted.Width;
                bmTemp = new Bitmap(iWorkWidth, iWorkHeight, bitmap.PixelFormat);
                grTemp = Graphics.FromImage(bmTemp);
                grTemp.DrawImage(bitmap, new Rectangle(0, 0, iWorkWidth, iWorkHeight));
                grTemp.Dispose();
                rectSrcClip = new Rectangle(0, 0, iWorkWidth, iWorkHeight);
                return bmTemp;
            }
            else return bitmap;
        }

		private void AlignImageScaled(out Rectangle rectVisibleArea, out Rectangle rectSrcClip, Bitmap bitmap, Rectangle rectObjectAdjustedScaled, double dScale)
		{
			int widthObjectImage;
			int heightObjectImage;

			if (bitmap == null)
			{
				rectVisibleArea = rectObjectAdjustedScaled;
				rectSrcClip = new Rectangle(0,0,0,0);
				return;
			}

			widthObjectImage = bitmap.Width;
			heightObjectImage = bitmap.Height;

			int newWidth = widthObjectImage;
			int newHeight = heightObjectImage;
			int dx = widthObjectImage; // width of the section in the image
			int dy = heightObjectImage; // height of the section in the image
			int xoff = 0; // x offset of the section in the image
			int yoff = 0; //  y offset of the section in the image
			int pleft = 0; // left pos for starting painting the image
			int ptop = 0;// top pos for starting painting the image
					
			if (m_designObject.Scaling == DCSDEV.DCSDatatypes.ScaleMode.Original)		// Original mode is no extra scaling 
			{
				// syh this mode needs to output image unscaled to the target printer and 
				// needs to apply a scaling to any smaller resolution display.
				if (widthObjectImage > rectObjectAdjustedScaled.Width) 
				{
					dx = rectObjectAdjustedScaled.Width;
					newWidth = dx;
					if (m_designObject.Justification == DCSDEV.DCSDatatypes.Justifications.RIGHT) 
					{
						xoff = widthObjectImage - rectObjectAdjustedScaled.Width;						
					}
					else if (m_designObject.Justification == DCSDEV.DCSDatatypes.Justifications.CENTER)
					{
						xoff = (widthObjectImage - rectObjectAdjustedScaled.Width) / 2;
					}
				}
				else 
				{
					dx = widthObjectImage;
					if (m_designObject.Justification == DCSDEV.DCSDatatypes.Justifications.RIGHT) 
					{
						pleft = rectObjectAdjustedScaled.Width - widthObjectImage;						
					}
					else if (m_designObject.Justification == DCSDEV.DCSDatatypes.Justifications.CENTER)
					{
						pleft = (rectObjectAdjustedScaled.Width - widthObjectImage) / 2;
					}
				}
				if (heightObjectImage > rectObjectAdjustedScaled.Height) 
				{
					dy = rectObjectAdjustedScaled.Height;
					newHeight = dy;
					if (m_designObject.Alignment == DCSDEV.DCSDatatypes.Alignments.BOTTOM) 
					{
						yoff = heightObjectImage - rectObjectAdjustedScaled.Height;						
					}
					else if (m_designObject.Alignment == DCSDEV.DCSDatatypes.Alignments.MIDDLE)
					{
						yoff = (heightObjectImage - rectObjectAdjustedScaled.Height) / 2;
					}
				}
				else 
				{
					dy = heightObjectImage;
					if (m_designObject.Alignment == DCSDEV.DCSDatatypes.Alignments.BOTTOM) 
					{
						ptop = rectObjectAdjustedScaled.Height - heightObjectImage;						
					}
					else if (m_designObject.Alignment == DCSDEV.DCSDatatypes.Alignments.MIDDLE)
					{
						ptop = (rectObjectAdjustedScaled.Height - heightObjectImage) / 2;
					}
				}
			}
			else if (m_designObject.Scaling == DCSDEV.DCSDatatypes.ScaleMode.ScaleToFit) 
			{
				newWidth = rectObjectAdjustedScaled.Width;
				newHeight = rectObjectAdjustedScaled.Height;
			}
			else if (m_designObject.Scaling == DCSDEV.DCSDatatypes.ScaleMode.KeepAspect) 
			{
				double f = 1.0;
				double clientaspect = ((double)rectObjectAdjustedScaled.Width) / (double)rectObjectAdjustedScaled.Height;
				double pictureaspect = (double)widthObjectImage / (double)heightObjectImage;
				if (clientaspect > pictureaspect) 
				{
					f =  ((double)rectObjectAdjustedScaled.Height) / (double)heightObjectImage;
					newWidth = DCSDEV.DCSMath.TimesDouble(widthObjectImage, f);
					newHeight = DCSDEV.DCSMath.TimesDouble(heightObjectImage, f);
					dx = widthObjectImage;
					if (m_designObject.Justification == DCSDEV.DCSDatatypes.Justifications.RIGHT) 
					{
						pleft = rectObjectAdjustedScaled.Width - newWidth;						
					}
					else if (m_designObject.Justification == DCSDEV.DCSDatatypes.Justifications.CENTER)
					{
						pleft = (rectObjectAdjustedScaled.Width - newWidth) / 2;
					}
				}
				else 
				{
					f = (double)rectObjectAdjustedScaled.Width / (double)widthObjectImage;
					newWidth = DCSDEV.DCSMath.TimesDouble(widthObjectImage, f);
					newHeight = DCSDEV.DCSMath.TimesDouble(heightObjectImage, f);
					dy = heightObjectImage;
					if (m_designObject.Alignment == DCSDEV.DCSDatatypes.Alignments.BOTTOM) 
					{
						ptop = rectObjectAdjustedScaled.Height - newHeight;						
					}
					else if (m_designObject.Alignment == DCSDEV.DCSDatatypes.Alignments.MIDDLE)
					{
						ptop = (rectObjectAdjustedScaled.Height - newHeight) / 2;
					}
				}
			}
			//System.Drawing.Imaging.ImageAttributes ia = new System.Drawing.Imaging.ImageAttributes();
			rectVisibleArea = new Rectangle(rectObjectAdjustedScaled.X+pleft, rectObjectAdjustedScaled.Y+ptop, newWidth, newHeight);
			rectSrcClip = new Rectangle(xoff, yoff, dx, dy);
			return;
		}

		// detect background and set alpha transparency for background replacement during merge
		private void PrepBackgroundReplacementAndApplyGray(bool bTransparencyToo)
		{
			if (m_bitmapWork == null) return;

			bool bGray = m_designObject.GrayScale;

			// set transparency - convert from opaque-clear = 0-100 to 255-0
			int transparency100 = m_designObject.Transparency;
			if (transparency100 < 0) transparency100 = 0;
			if (transparency100 > 100) transparency100 = 100;
			int transparency255 = (100-transparency100) * 255 / 100;
			bool bDoTrans = (bTransparencyToo && m_designObject.Transparency != 0);

			// set transparency
			Color colorKey = m_designObject.ColorToDetect;
			if (m_designObject.AutoKey || m_designObject.PortraitAutoKey)
			{
				colorKey = this.CalcAutoColor();
			}
			Color color;
#if OLDWAY
			int rangeSquared = m_designObject.ColorDetectThreshold * m_designObject.ColorDetectThreshold;
			for (int y=0; y<m_bitmapWork.Height; y++)
			{
                System.Windows.Forms.Application.DoEvents();
				for (int x=0; x<m_bitmapWork.Width; x++)
				{
                    color = m_bitmapWork.GetPixel(x,y);
					// detect background pixels
                    if ((color.R - colorKey.R) * (color.R - colorKey.R) + (color.G - colorKey.G) * (color.G - colorKey.G) + (color.B - colorKey.B) * (color.B - colorKey.B) < rangeSquared)
                    //if (Math.Abs(color.R - colorKey.R) + Math.Abs(color.G - colorKey.G) + Math.Abs(color.B - colorKey.B) * (color.B - colorKey.B) < m_designObject.ColorDetectThreshold)
					{
						m_bitmapWork.SetPixel(x, y, Color.FromArgb(0, color));	// change alpha to 0=opaque
					}
					else
					{
						if (bGray && bDoTrans)
							m_bitmapWork.SetPixel(x, y, Color.FromArgb(transparency255, color.G, color.G, color.G));
						else if (bGray)
							m_bitmapWork.SetPixel(x, y, Color.FromArgb(color.A, color.G, color.G, color.G));
						else if (bDoTrans)
							m_bitmapWork.SetPixel(x, y, Color.FromArgb(transparency255, color));
					}
				}
			}
#else
			int range = m_designObject.ColorDetectThreshold;
			int rangeMask = range;
			// int rangeHue = range / 2;
			Color colorMask;
			double m = (100.0 - range) / (235.0 - 45.0);	
			// double mH = (range) / (235 - 45.0);

			// get face alpha mask bmTemp if it exists
			Bitmap bitmapMask = null;
			//float hue = 0;
			if (m_designObject.PortraitAutoKey)
			{
				DCSDEV.ParameterStore ps = new ParameterStore("");
				string strMask = System.IO.Path.Combine(ps.m_strDCSInstallDirectory, "FaceAlphaMask.jpg");
				if (System.IO.File.Exists(strMask))
				{
					bitmapMask = new Bitmap(strMask);
					// rotation requires same rotation of mask
					if (m_designObject.RotateFlip != System.Drawing.RotateFlipType.RotateNoneFlipNone)
						bitmapMask.RotateFlip(m_designObject.RotateFlip);
				}
				// hue = colorKey.GetHue();
			}
			for (int y = 0; y < m_bitmapWork.Height; y++)
			{
				int yMask = 0;
				if (bitmapMask != null) yMask = y * bitmapMask.Height / m_bitmapWork.Height;

				System.Windows.Forms.Application.DoEvents();
				for (int x = 0; x < m_bitmapWork.Width; x++)
				{
					color = m_bitmapWork.GetPixel(x, y);
					if (bitmapMask != null)
					{
						colorMask = bitmapMask.GetPixel(x * bitmapMask.Width / m_bitmapWork.Width, yMask);
						// colorMask.G) = 0 if most likely face, = 255 if most likely background 
						if (colorMask.G <= 45)
						{
							rangeMask = 0;
							// rangeHue = 0;
						}
						else
						{
							// rangeMask = m*G + B
							// B = range - m * 45.0;
							rangeMask = (int)(m * colorMask.G + range - m * 45.0);
							// rangeHue = (int)(mH * colorMask.G + range - mH * 45.0);
						}
					}
					// detect background pixels
					//if ((color.R - colorKey.R) * (color.R - colorKey.R) + (color.G - colorKey.G) * (color.G - colorKey.G) + (color.B - colorKey.B) * (color.B - colorKey.B) < rangeMask*rangeMask)

					if ( 
					//	(Math.Abs(hue - color.GetHue()) < rangeHue)
					//	&& 
						(Math.Abs(color.R - colorKey.R) + Math.Abs(color.G - colorKey.G) + Math.Abs(color.B - colorKey.B) < rangeMask*16/10) 
						)
					{
						m_bitmapWork.SetPixel(x, y, Color.FromArgb(0, color));	// change alpha to 0=transparent
					}
					else
					{
						if (bGray && bDoTrans)
							m_bitmapWork.SetPixel(x, y, Color.FromArgb(transparency255, color.G, color.G, color.G));
						else if (bGray)
							m_bitmapWork.SetPixel(x, y, Color.FromArgb(color.A, color.G, color.G, color.G));
						else if (bDoTrans)
							m_bitmapWork.SetPixel(x, y, Color.FromArgb(transparency255, color));
					}
				}
			}
			if (bitmapMask != null) bitmapMask.Dispose();
#endif
		}

		// calculate keycolor from the upper 1/8 corners of the original image
		private Color CalcAutoColor()
		{
			int tr, tg, tb;
			int w, h, x, y;
			int cnt;
			tr=tg=tb=0;

			Color colorAuto = m_designObject.DesignObjectImage.GetPixel(0,0);
			if (m_designObject.DesignObjectImage == null) return colorAuto;

			h = m_designObject.DesignObjectImage.Height / 8;
			w = m_designObject.DesignObjectImage.Width / 8;

			if (h > 0 && w > 0) 
			{
				for(y=0; y < h; y++) 
				{
                    System.Windows.Forms.Application.DoEvents();
                    for (x = 0; x < w; x++) 
					{
						colorAuto = m_designObject.DesignObjectImage.GetPixel(x,y);
						tr+= colorAuto.R;
						tg+= colorAuto.G;
						tb+= colorAuto.B;
					}
				}
				for (y = 0; y < h; y++)
				{
					System.Windows.Forms.Application.DoEvents();
					for (x = m_designObject.DesignObjectImage.Width - w; x < m_designObject.DesignObjectImage.Width; x++)
					{
						colorAuto = m_designObject.DesignObjectImage.GetPixel(x, y);
						tr += colorAuto.R;
						tg += colorAuto.G;
						tb += colorAuto.B;
					}
				}
				cnt = w * h * 2 ;
				tr = (tr / cnt);
				tg = (tg / cnt);
				tb = (tb / cnt);
				colorAuto = Color.FromArgb(tr,tg,tb);
			}
			return colorAuto;
		}
		
		internal void DrawOutline()
		{
			// Dont bother if frame width is zero
			if (m_designObject.LineWidth > 0) 
			{
				int iPenWidthScaled = DCSMath.IntTimesDouble(m_designObject.LineWidth, m_dScale);
				Pen pen = new Pen(new SolidBrush(m_designObject.LineColor), iPenWidthScaled);
							
				Rectangle rectFrame = DCSMath.RectTimesDouble(m_designObject.Bounds, m_dScale);
				rectFrame = Rectangle.Inflate(rectFrame, -iPenWidthScaled/2, -iPenWidthScaled/2);
				if (m_designObject.Radius > 0)
				{
					int minDim = Math.Min(rectFrame.Width, rectFrame.Height);
					int rad = minDim * m_designObject.Radius / 100;
					if (minDim > 0)
					{
						Rectangle rect = new Rectangle(0, 0, rad, rad);
						rect.Offset(rectFrame.X, rectFrame.Y);
						m_gr.DrawArc(pen, rect, 179F, 92F);
						rect.Offset(rectFrame.Width-rad, 0);
						m_gr.DrawArc(pen, rect, 268F, 92F);
						rect.Offset(0, rectFrame.Height-rad);
						m_gr.DrawArc(pen, rect, -1F, 92F);
						rect.Offset(-rectFrame.Width+rad, 0);
						m_gr.DrawArc(pen, rect, 89F, 92F);

						Brush brush = new SolidBrush(m_designObject.LineColor);
						m_gr.FillRectangle(brush, rectFrame.X+rad/2, rectFrame.Y-iPenWidthScaled/2,		rectFrame.Width-rad+1, iPenWidthScaled);
						m_gr.FillRectangle(brush, rectFrame.X+rad/2, rectFrame.Bottom-iPenWidthScaled/2, rectFrame.Width-rad+1, iPenWidthScaled);
						m_gr.FillRectangle(brush, rectFrame.X-iPenWidthScaled/2, rectFrame.Y+rad/2,		iPenWidthScaled, rectFrame.Height-rad+1);
						m_gr.FillRectangle(brush, rectFrame.Right-iPenWidthScaled/2, rectFrame.Y+rad/2, iPenWidthScaled, rectFrame.Height-rad+1);
					}
					else
					{
						m_gr.DrawRectangle(pen, rectFrame);
					}
				}
				else
				{
					m_gr.DrawRectangle(pen, rectFrame);
				}
			}
		}

		internal void DrawObjectBackground()
		{
			if (m_designObject.BackFillType == DCSDEV.DCSDatatypes.BackFillTypes.FILL_IMAGE && m_designObject.BackImage != null) 
			{
				// SYH: if text is rotated then this background needs same rotation as for image
				if (m_designObject.RotateFlip != System.Drawing.RotateFlipType.RotateNoneFlipNone)
				{
					Bitmap bitmap = (Bitmap)m_designObject.BackImage.Clone();
					bitmap.RotateFlip(m_designObject.RotateFlip);
					m_gr.DrawImage(bitmap, m_rectObjectAdjustedScaled, 0, 0, bitmap.Width, bitmap.Height, GraphicsUnit.Pixel);
				}
				else
				{
					m_gr.DrawImage(m_designObject.BackImage, m_rectObjectAdjustedScaled, 0, 0, m_designObject.BackImage.Width, m_designObject.BackImage.Height, GraphicsUnit.Pixel);
				}
			}
			else if (m_designObject.BackFillType == DCSDEV.DCSDatatypes.BackFillTypes.FILL_COLOR) 
			{
				m_gr.FillRectangle(new SolidBrush(m_designObject.BackColor), m_rectObjectAdjustedScaled);
			}
			else if (m_designObject.BackFillType == DCSDEV.DCSDatatypes.BackFillTypes.FILL_GRADIENT)
			{
				System.Drawing.Drawing2D.LinearGradientMode lgm = m_designObject.BackGradientType;
				System.Drawing.Drawing2D.LinearGradientBrush lgbrush = new System.Drawing.Drawing2D.LinearGradientBrush(
					m_rectObjectAdjustedScaled, m_designObject.BackColor, m_designObject.BackColor2, lgm);
				m_gr.FillRectangle(lgbrush, m_rectObjectAdjustedScaled);
			}
			else if (m_designObject.BackFillType == DCSDEV.DCSDatatypes.BackFillTypes.CONDITIONAL_COLOR)
			{
				m_gr.FillRectangle(new SolidBrush(m_designObject.ColorEval), m_rectObjectAdjustedScaled);
			}
		}

		private bool IsCloneNecessary(Bitmap bitmap)
		{
			if (m_bitmapWork == null) return false;
			bool bDoRotateFlip = (m_designObject.RotateFlip != 0);
			bool bDoGray = m_designObject.GrayScale;
			bool bDoBackDetect = (m_designObject.BackDetectEnabled);
			bool bTransparencyToo = (m_designObject.Transparency != 0);
			bool bDoFraming = (m_designObject.Framing != DCSDEV.DCSDatatypes.FramingMode.NONE);

			// if nothing to do return the original false
			if (!bDoRotateFlip && !bDoGray && !bTransparencyToo && !bDoBackDetect && !bDoFraming) return false;

			return true;
		}

		// modify m_bitmapWork for Transparency
		// return unchanged if there is nothing to do.
		private void ApplyAlphaTransform(ref Bitmap bitmap, int designTransparency)
		{
			// check image
			if (bitmap == null) return;
			// if nothing to do return the original bmTemp
			if (designTransparency == 0) return;

			// set transparency - convert from 0-100 to 255-0
			int transparency100 = designTransparency;
			if (transparency100 < 0) transparency100 = 0;
			if (transparency100 > 100) transparency100 = 100;
			int transparency255 = (100-transparency100) * 255 / 100;

			Color color;
			int alpha;

			for (int y=0; y<bitmap.Height; y++)
			{
                System.Windows.Forms.Application.DoEvents();
                for (int x = 0; x < bitmap.Width; x++)
				{
					color = bitmap.GetPixel(x,y);
					alpha = color.A;
					if (alpha > transparency255) alpha = transparency255;
					bitmap.SetPixel(x, y, Color.FromArgb(alpha, color));
				}
			}
		}

		// modify bmTemp for Transform to Gray
		// return unchanged if there is nothing to do.
		private void ApplyGrayTransform(Bitmap bitmap, bool bTransparencyToo)
		{
			// check image
			if (bitmap == null) return;
			Color color;

			bool bDoTrans = (bTransparencyToo && m_designObject.Transparency != 0);
			bool bDoGray = m_designObject.GrayScale;

			// if nothing to do return without changing the original bmTemp
			if (!bDoTrans && !bDoGray) return;

			// set transparency - convert from 0-100 to 255-0
			int transparency100 = m_designObject.Transparency;
			if (transparency100 < 0) transparency100 = 0;
			if (transparency100 > 100) transparency100 = 100;
			int transparency255 = (100-transparency100) * 255 / 100;

			for (int y=0; y<bitmap.Height; y++)
			{
                System.Windows.Forms.Application.DoEvents();
                for (int x = 0; x < bitmap.Width; x++)
				{
					// taking the green value is quick and dirty convert to gray
					color = bitmap.GetPixel(x,y);
					if (bDoGray && bDoTrans)
						bitmap.SetPixel(x, y, Color.FromArgb(transparency255, color.G, color.G, color.G));
					else if (bDoGray)
						bitmap.SetPixel(x, y, Color.FromArgb(color.A, color.G, color.G, color.G));
					else if (bDoTrans)
						bitmap.SetPixel(x, y, Color.FromArgb(transparency255, color));
				}
			}
		}
	}
}
