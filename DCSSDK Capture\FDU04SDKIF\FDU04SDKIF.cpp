// FDU04SDKIF.cpp : Defines the initialization routines for the DLL.
//

#include "stdafx.h"
#include "FDU04SDKIF.h"
#include "drv_dll2.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#endif

static ISensor*    m_pSensor = NULL;
static HWND		m_hWnd = NULL;
static bool		g_bStopFlag = true;
static bool		g_bRunning = false;

UINT		m_Brightness;
UINT		m_Contrast;
UINT		m_DevID;
CString		m_DevSN;
UINT		m_Gain;
UINT		m_ImageDPI;
UINT		m_ImgHeight;
UINT		m_ImgWidth;
CString		m_FWVersion;

CString		m_ErrorDisplay;
BYTE*       m_pImgBuf = NULL;        // image buffer
HINSTANCE   m_hLib = NULL;
CDibClass*  m_pDibCtl = NULL;  // for drawing fp image on first window

static CWinThread*		m_pCVFThread;				/* The class of a picture display thread */

//
//	Note!
//
//		If this DLL is dynamically linked against the MFC
//		DLLs, any functions exported from this DLL which
//		call into MFC must have the AFX_MANAGE_STATE macro
//		added at the very beginning of the function.
//
//		For example:
//
//		extern "C" BOOL PASCAL EXPORT ExportedFunction()
//		{
//			AFX_MANAGE_STATE(AfxGetStaticModuleState());
//			// normal function body here
//		}
//
//		It is very important that this macro appear in each
//		function, prior to any calls into MFC.  This means that
//		it must appear as the first statement within the 
//		function, even before any object variable declarations
//		as their constructors may generate calls into the MFC
//		DLL.
//
//		Please see MFC Technical Notes 33 and 58 for additional
//		details.
//

// start the SDK
extern "C" FDU04SDKIF_API bool FDU04SDK_Init(HWND hWnd)
{
	bool bRet;
	m_hWnd = hWnd;

	if (m_pSensor && m_hLib) 	
	{
		FreeSensorDll(&m_pSensor, &m_hLib);
		m_pSensor = NULL;
	}

	if (LoadSensorDll(FDU04_DLLNAME, &m_pSensor, &m_hLib) != 0 || m_pSensor == NULL)
	{
		m_ErrorDisplay.Format(TEXT("Cannot load dll %s or Create Sensor object"), FDU04_DLLNAME);
		AfxMessageBox(m_ErrorDisplay);
		return false;
	}

	bRet = InitDevice();
	return bRet;
}

extern "C" FDU04SDKIF_API bool FDU04SDK_Term()
{
	//AfxMessageBox("FDU04SDK_Term");
	if (g_bRunning)
	{
		g_bStopFlag = true;
		Sleep(500);
	}

	if (m_pImgBuf)
		delete m_pImgBuf;
	m_pImgBuf = 0;

	if (m_pDibCtl)
		delete m_pDibCtl;
	m_pDibCtl = 0;

	FreeSensorDll(&m_pSensor, &m_hLib);
	return true;
}

extern "C" FDU04SDKIF_API bool FDU04SDK_StartVideo()
{
	//AfxMessageBox("FDU04SDK_StartVideo");
	DWORD error;
	error = m_pSensor->SetCallBackFunction(ISensor::CALLBACK_LIVE_CAPTURE, MyCallbackFunction, NULL);
	if (error != ISensor::ERROR_NONE)
	{
		m_ErrorDisplay = GetErrorMsg(error);
		AfxMessageBox(m_ErrorDisplay);
		return false;
	}

	//syh added 9/19/2007
	if (m_pImgBuf) memset(m_pImgBuf, 255, m_ImgWidth*m_ImgHeight);

	g_bStopFlag = false;
	g_bRunning = true;
	m_pCVFThread = AfxBeginThread(MyCaptureThread, NULL); 
	if (m_pCVFThread == NULL)
	{
		m_ErrorDisplay = "ERROR AfxBeginThread";
		AfxMessageBox(m_ErrorDisplay);
		return false;
	}
	return true;
}

extern "C" FDU04SDKIF_API bool FDU04SDK_StopVideo()
{
	//AfxMessageBox("FDU04SDK_StopVideo");
	DWORD error;
	g_bStopFlag = true;
	Sleep(500);

	if (!g_bRunning)
	{
		error = m_pSensor->SetCallBackFunction(ISensor::CALLBACK_LIVE_CAPTURE, NULL, NULL);
		if (error != ISensor::ERROR_NONE)
		{
			m_ErrorDisplay = GetErrorMsg(error);
			AfxMessageBox(m_ErrorDisplay + " in stopvideo");
			return false;
		}
	}
	return true;
}

extern "C" FDU04SDKIF_API bool FDU04SDK_Capture(char* szFileName)
{
	//AfxMessageBox("FDU04SDK_Capture");

	if (!m_pSensor) return false;
	DWORD error;
	error = m_pSensor->GetImage(m_pImgBuf);
	if (error != ISensor::ERROR_NONE)
	{
		m_ErrorDisplay = GetErrorMsg(error);
		AfxMessageBox(m_ErrorDisplay);
		return false;
	}
	/******************************************
	* Save Fingerprint image to BMP format
	/******************************************/
	SaveBmp(szFileName);
	//DisplayImage();
	return true;
}

/******************************************
* Save Fingerprint image to BMP format
/******************************************/
void SaveBmp(char* szFileName) 
{
   BYTE *bImage = m_pImgBuf;
   LONG dwWidth = m_ImgWidth;
   LONG dwHeight = m_ImgHeight;   

   FILE *fp = fopen(szFileName, "wb");
   if (fp)
   {
      BITMAPFILEHEADER bmpFileHdr = {0};
      BITMAPINFOHEADER bmpInfoHdr;
      BITMAPINFO *pBmpInfo = (BITMAPINFO*)malloc(sizeof(BITMAPINFOHEADER) + 256 * sizeof(RGBQUAD));

      // initialize bitmap info header
      bmpInfoHdr.biSize = sizeof(BITMAPINFOHEADER);
      bmpInfoHdr.biWidth = (dwWidth/4)*4;
      bmpInfoHdr.biHeight = dwHeight;
      bmpInfoHdr.biPlanes = 1;
      bmpInfoHdr.biBitCount = 8;
      bmpInfoHdr.biCompression = BI_RGB;
      bmpInfoHdr.biSizeImage = ((dwWidth/4)*4) * dwHeight;
      bmpInfoHdr.biClrUsed = 256;
      bmpInfoHdr.biClrImportant = 256;

      memcpy(pBmpInfo, &bmpInfoHdr, sizeof(BITMAPINFOHEADER));
      int i;
      for (i = 0; i < 256; i++)
      {
         pBmpInfo->bmiColors[i].rgbRed = i;
         pBmpInfo->bmiColors[i].rgbGreen = i;
         pBmpInfo->bmiColors[i].rgbBlue = i;
         pBmpInfo->bmiColors[i].rgbReserved = 0;
      }
      // initialize bitmap file header
      bmpFileHdr.bfType = 0x4d42;  // "BM"
      bmpFileHdr.bfSize = (DWORD)sizeof(BITMAPFILEHEADER) + bmpInfoHdr.biSize +
                     bmpInfoHdr.biClrUsed * sizeof(RGBQUAD) + bmpInfoHdr.biSizeImage;
      bmpFileHdr.bfOffBits = (DWORD)sizeof(BITMAPFILEHEADER) + bmpInfoHdr.biSize +
                     bmpInfoHdr.biClrUsed * sizeof(RGBQUAD);

      // write to file
      fwrite(&bmpFileHdr, 1, sizeof(BITMAPFILEHEADER), fp);
      fwrite(pBmpInfo, 1, sizeof(BITMAPINFOHEADER) + 256 * sizeof(RGBQUAD), fp);

      // reverse the bImage
      BYTE *bmp = (BYTE*)malloc(dwWidth * dwHeight);
      for (i = 0; i < dwHeight; i++)
	  {
//       memcpy(&bmp[i*dwWidth], &bImage[(dwHeight - 1 - i) * ((dwWidth/4)*4)], ((dwWidth/4)*4));
         memcpy(&bmp[i*((dwWidth/4)*4)], &bImage[(dwHeight - 1 - i) * ((dwWidth))], ((dwWidth/4)*4));
		 if (i==0)
			 memset(&bmp[i*dwWidth],0,1);
	  }
      fwrite(bmp, 1, bmpInfoHdr.biSizeImage, fp);
      free (bmp);

      free(pBmpInfo);
      fclose(fp);
   }
}

////////////////////////////////////////////////
// InitDevice
// - 
////////////////////////////////////////////////
static bool InitDevice()
{
	DWORD error = m_pSensor->OpenDevice(0);
	if (error != ISensor::ERROR_NONE)
	{
		m_ErrorDisplay.Format(TEXT("Initialization failed. Error#: %d \n %s"), error, GetErrorMsg(error));
		AfxMessageBox(m_ErrorDisplay);
		return false;
	}

	// GetDeviceInfo2()
	ISensor::DeviceInfoParam2 device_info2;
	memset(&device_info2, 0x00, sizeof(ISensor::DeviceInfoParam2));
	error = m_pSensor->GetDeviceInfoEx(SGST_DEV_INFO2, &device_info2);
	if (error == ISensor::ERROR_NONE)
	{
		m_Brightness = device_info2.Brightness;
		m_Contrast = device_info2.Contrast;
		m_DevID = device_info2.DeviceID; 
		m_DevSN = device_info2.DeviceSN; 
		m_Gain = device_info2.Gain;
		m_ImageDPI = device_info2.ImageDPI;
		m_ImgHeight = device_info2.ImageHeight;
		m_ImgWidth = device_info2.ImageWidth;

		char buffer[20];
		_ultoa(device_info2.FWVersion, buffer, 16);
		m_FWVersion = CString(buffer);
	}
	else
	{
		m_ErrorDisplay = GetErrorMsg(error);
		AfxMessageBox(m_ErrorDisplay);
		return false;
	}

	if (m_pImgBuf) delete m_pImgBuf;
	m_pImgBuf = new BYTE[m_ImgWidth*m_ImgHeight];

	if (m_pDibCtl) delete m_pDibCtl;
	m_pDibCtl = new CDibClass(m_hWnd);
	m_pDibCtl->DibInit(m_ImgWidth, m_ImgHeight);

	m_ErrorDisplay = TEXT("Initialization Success");
	return true;
}


//////////////////////////////////////////////////////////////
UINT MyCaptureThread(LPVOID pParam)
{
	while (g_bStopFlag == false)
	{
		DWORD err = ContinuousCapture();
		if (err != ISensor::ERROR_NONE)
			break;
	}
	return 1;
}

//////////////////////////////////////////////////////////////
DWORD ContinuousCapture()
{
	DWORD error = 0;
	error = m_pSensor->GetLiveImageEx(m_pImgBuf, 0xFFFF, m_hWnd, 100);
	if (error != ISensor::ERROR_NONE)
	{
		// syh m_ErrorDisplay = GetErrorMsg(error);
		// syh AfxMessageBox(m_ErrorDisplay + " in GetLiveImageEx");
		// syh this message is appearing when doing cancel
	}
	return error;
}

//////////////////////////////////////////////////////////////
// Callback Function Related
// Used in Thread
DWORD WINAPI MyCallbackFunction(LPVOID pParam1, LPVOID pParam2)
{
	//AfxMessageBox("MyCallbackFunction");
	ISensor::CBLiveCaptureParam* pCaptureInfo = (ISensor::CBLiveCaptureParam*)pParam2; // Data from driver
	if (g_bStopFlag)
	{
		g_bRunning = false;
		return ISensor::ERROR_FUNCTION_FAILED;
	}
	else
	{
		DisplayImage();
		return ISensor::ERROR_NONE;
	}
}

////////////////////////////////////////////////
// Display fingerprint Image
////////////////////////////////////////////////
void DisplayImage()
{
	//AfxMessageBox("DisplayImage");
	if (m_pSensor && m_pImgBuf && m_pDibCtl)
	{
		m_pDibCtl->SetBits((BYTE*)m_pImgBuf);
		m_pDibCtl->DrawDib();
	}
}

////////////////////////////////////////////////
// Get Error Message
////////////////////////////////////////////////
CString GetErrorMsg(DWORD err_number) 
{
	CString msg;
	switch(err_number)
	{
	case ISensor::ERROR_NONE:
		msg = "Function Success";      
		break;
	case ISensor::ERROR_CREATION_FAILED:
		msg = "Failed to ISensor Object"; 
		break;
	case ISensor::ERROR_FUNCTION_FAILED:
		msg = "Function Failed";   
		break;
	case ISensor::ERROR_INVALID_PARAM:
		msg = "Invalid Parameter"; 
		break;
	case ISensor::ERROR_NOT_USED:
		msg = "Not used function"; 
		break;
	case ISensor::ERROR_VXDLOAD_FAILED:
		msg = "Failed to load venusdrv.sys";
		break;
	case ISensor::ERROR_INITIALIZE_FAILED:
		msg = "Failed to initialize the device";
		break;
	case ISensor::ERROR_LINE_DROPPED:
		msg = "Line dropped";
		break;
	case ISensor::ERROR_TIME_OUT:      
		msg = "Time out";
		break;
	case ISensor::ERROR_DEVICE_NOT_FOUND:
		msg = "Device not found";
		break;
	case ISensor::ERROR_DLLLOAD_FAILED:
		msg = "Dll load failed";  // Actually it is not used...
		break;
	case ISensor::ERROR_WRONG_IMAGE:         
		msg = "Wrong Image";
		break;
	case ISensor::ERROR_LACK_OF_BANDWIDTH:
		msg = "USB Lack of Bandwidth";
		break;
	case ISensor::ERROR_ALREADY_OPEN:
		msg = "Device is already opened";
		break;
	case ISensor::ERROR_GETSN_FAILED:
		msg = "Failed to get serial number";
		break;
	}
	TCHAR buf[100];
	wsprintf(buf, "ERROR = %d, %s", err_number, msg);
	return TEXT(buf);
}
