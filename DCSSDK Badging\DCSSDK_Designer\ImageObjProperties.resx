<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonCancel.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonCancel.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="buttonCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>480, 400</value>
  </data>
  <data name="buttonCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 24</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonCancel.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="buttonCancel.Text" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Name" xml:space="preserve">
    <value>buttonCancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonCancel.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="buttonAccept.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonAccept.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonAccept.Location" type="System.Drawing.Point, System.Drawing">
    <value>480, 360</value>
  </data>
  <data name="buttonAccept.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 24</value>
  </data>
  <data name="buttonAccept.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="buttonAccept.Text" xml:space="preserve">
    <value>&amp;OK - apply and close</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Name" xml:space="preserve">
    <value>buttonAccept</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAccept.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="buttonApply.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonApply.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonApply.Location" type="System.Drawing.Point, System.Drawing">
    <value>480, 320</value>
  </data>
  <data name="buttonApply.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 24</value>
  </data>
  <data name="buttonApply.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="buttonApply.Text" xml:space="preserve">
    <value>&amp;Apply</value>
  </data>
  <data name="&gt;&gt;buttonApply.Name" xml:space="preserve">
    <value>buttonApply</value>
  </data>
  <data name="&gt;&gt;buttonApply.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonApply.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonApply.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="dcsRotationProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>312, 40</value>
  </data>
  <data name="dcsRotationProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 80</value>
  </data>
  <data name="dcsRotationProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>78</value>
  </data>
  <data name="&gt;&gt;dcsRotationProperties1.Name" xml:space="preserve">
    <value>dcsRotationProperties1</value>
  </data>
  <data name="&gt;&gt;dcsRotationProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSRotationProperties, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsRotationProperties1.Parent" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;dcsRotationProperties1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="dcsAlignmentProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>184, 16</value>
  </data>
  <data name="dcsAlignmentProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 112</value>
  </data>
  <data name="dcsAlignmentProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>77</value>
  </data>
  <data name="&gt;&gt;dcsAlignmentProperties1.Name" xml:space="preserve">
    <value>dcsAlignmentProperties1</value>
  </data>
  <data name="&gt;&gt;dcsAlignmentProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSAlignmentProperties, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsAlignmentProperties1.Parent" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;dcsAlignmentProperties1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="checkGray.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkGray.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkGray.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 104</value>
  </data>
  <data name="checkGray.Size" type="System.Drawing.Size, System.Drawing">
    <value>176, 24</value>
  </data>
  <data name="checkGray.TabIndex" type="System.Int32, mscorlib">
    <value>65</value>
  </data>
  <data name="checkGray.Text" xml:space="preserve">
    <value>Convert to gray</value>
  </data>
  <data name="&gt;&gt;checkGray.Name" xml:space="preserve">
    <value>checkGray</value>
  </data>
  <data name="&gt;&gt;checkGray.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkGray.Parent" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;checkGray.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="comboBoxScaling.Items" xml:space="preserve">
    <value>No scaling</value>
  </data>
  <data name="comboBoxScaling.Items1" xml:space="preserve">
    <value>Stretch to fill</value>
  </data>
  <data name="comboBoxScaling.Items2" xml:space="preserve">
    <value>Preserve aspect ratio</value>
  </data>
  <data name="comboBoxScaling.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 40</value>
  </data>
  <data name="comboBoxScaling.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 21</value>
  </data>
  <data name="comboBoxScaling.TabIndex" type="System.Int32, mscorlib">
    <value>64</value>
  </data>
  <data name="comboBoxScaling.Text" xml:space="preserve">
    <value>Preserve aspect ratio</value>
  </data>
  <data name="&gt;&gt;comboBoxScaling.Name" xml:space="preserve">
    <value>comboBoxScaling</value>
  </data>
  <data name="&gt;&gt;comboBoxScaling.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxScaling.Parent" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;comboBoxScaling.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 16</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 24</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>63</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>Scaling mode</value>
  </data>
  <data name="label1.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="tabPage1.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tabPage1.Size" type="System.Drawing.Size, System.Drawing">
    <value>400, 214</value>
  </data>
  <data name="tabPage1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="tabPage1.Text" xml:space="preserve">
    <value>Foreground</value>
  </data>
  <data name="&gt;&gt;tabPage1.Name" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;tabPage1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabPage1.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabPage1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="numericUpDownTransparency.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 16</value>
  </data>
  <data name="numericUpDownTransparency.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="numericUpDownTransparency.TabIndex" type="System.Int32, mscorlib">
    <value>79</value>
  </data>
  <data name="&gt;&gt;numericUpDownTransparency.Name" xml:space="preserve">
    <value>numericUpDownTransparency</value>
  </data>
  <data name="&gt;&gt;numericUpDownTransparency.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numericUpDownTransparency.Parent" xml:space="preserve">
    <value>tabPage4</value>
  </data>
  <data name="&gt;&gt;numericUpDownTransparency.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label4.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>80, 16</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 24</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>78</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>Transparency</value>
  </data>
  <data name="label4.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>tabPage4</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pictureBoxDetectColor.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="pictureBoxDetectColor.Location" type="System.Drawing.Point, System.Drawing">
    <value>160, 40</value>
  </data>
  <data name="pictureBoxDetectColor.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 72</value>
  </data>
  <data name="pictureBoxDetectColor.TabIndex" type="System.Int32, mscorlib">
    <value>78</value>
  </data>
  <data name="&gt;&gt;pictureBoxDetectColor.Name" xml:space="preserve">
    <value>pictureBoxDetectColor</value>
  </data>
  <data name="&gt;&gt;pictureBoxDetectColor.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pictureBoxDetectColor.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;pictureBoxDetectColor.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="numericUpDownRange.Location" type="System.Drawing.Point, System.Drawing">
    <value>96, 96</value>
  </data>
  <data name="numericUpDownRange.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="numericUpDownRange.TabIndex" type="System.Int32, mscorlib">
    <value>77</value>
  </data>
  <data name="&gt;&gt;numericUpDownRange.Name" xml:space="preserve">
    <value>numericUpDownRange</value>
  </data>
  <data name="&gt;&gt;numericUpDownRange.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numericUpDownRange.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;numericUpDownRange.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelRange.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelRange.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 96</value>
  </data>
  <data name="labelRange.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 16</value>
  </data>
  <data name="labelRange.TabIndex" type="System.Int32, mscorlib">
    <value>76</value>
  </data>
  <data name="labelRange.Text" xml:space="preserve">
    <value>Range</value>
  </data>
  <data name="&gt;&gt;labelRange.Name" xml:space="preserve">
    <value>labelRange</value>
  </data>
  <data name="&gt;&gt;labelRange.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelRange.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;labelRange.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="buttonChooseColor.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonChooseColor.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 8.25pt, style=Bold</value>
  </data>
  <data name="buttonChooseColor.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonChooseColor.Location" type="System.Drawing.Point, System.Drawing">
    <value>120, 40</value>
  </data>
  <data name="buttonChooseColor.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 20</value>
  </data>
  <data name="buttonChooseColor.TabIndex" type="System.Int32, mscorlib">
    <value>75</value>
  </data>
  <data name="buttonChooseColor.Text" xml:space="preserve">
    <value>&gt;</value>
  </data>
  <data name="&gt;&gt;buttonChooseColor.Name" xml:space="preserve">
    <value>buttonChooseColor</value>
  </data>
  <data name="&gt;&gt;buttonChooseColor.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonChooseColor.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;buttonChooseColor.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="comboBoxDetection.Items" xml:space="preserve">
    <value>none</value>
  </data>
  <data name="comboBoxDetection.Items1" xml:space="preserve">
    <value>Color</value>
  </data>
  <data name="comboBoxDetection.Items2" xml:space="preserve">
    <value>Automatic</value>
  </data>
  <data name="comboBoxDetection.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 40</value>
  </data>
  <data name="comboBoxDetection.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 21</value>
  </data>
  <data name="comboBoxDetection.TabIndex" type="System.Int32, mscorlib">
    <value>74</value>
  </data>
  <data name="comboBoxDetection.Text" xml:space="preserve">
    <value>none</value>
  </data>
  <data name="&gt;&gt;comboBoxDetection.Name" xml:space="preserve">
    <value>comboBoxDetection</value>
  </data>
  <data name="&gt;&gt;comboBoxDetection.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxDetection.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;comboBoxDetection.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="groupBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 59</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>232, 133</value>
  </data>
  <data name="groupBox1.TabIndex" type="System.Int32, mscorlib">
    <value>77</value>
  </data>
  <data name="groupBox1.Text" xml:space="preserve">
    <value>Image background detection</value>
  </data>
  <data name="&gt;&gt;groupBox1.Name" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;groupBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox1.Parent" xml:space="preserve">
    <value>tabPage4</value>
  </data>
  <data name="&gt;&gt;groupBox1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="tabPage4.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tabPage4.Size" type="System.Drawing.Size, System.Drawing">
    <value>400, 214</value>
  </data>
  <data name="tabPage4.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="tabPage4.Text" xml:space="preserve">
    <value>See through</value>
  </data>
  <data name="&gt;&gt;tabPage4.Name" xml:space="preserve">
    <value>tabPage4</value>
  </data>
  <data name="&gt;&gt;tabPage4.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabPage4.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabPage4.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="dcsBackGroundProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 16</value>
  </data>
  <data name="dcsBackGroundProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>216, 164</value>
  </data>
  <data name="dcsBackGroundProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;dcsBackGroundProperties1.Name" xml:space="preserve">
    <value>dcsBackGroundProperties1</value>
  </data>
  <data name="&gt;&gt;dcsBackGroundProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSBackGroundProperties, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsBackGroundProperties1.Parent" xml:space="preserve">
    <value>tabPage2</value>
  </data>
  <data name="&gt;&gt;dcsBackGroundProperties1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="tabPage2.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tabPage2.Size" type="System.Drawing.Size, System.Drawing">
    <value>400, 214</value>
  </data>
  <data name="tabPage2.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="tabPage2.Text" xml:space="preserve">
    <value>Background</value>
  </data>
  <data name="&gt;&gt;tabPage2.Name" xml:space="preserve">
    <value>tabPage2</value>
  </data>
  <data name="&gt;&gt;tabPage2.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabPage2.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabPage2.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="label5.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label5.Location" type="System.Drawing.Point, System.Drawing">
    <value>240, 40</value>
  </data>
  <data name="label5.Size" type="System.Drawing.Size, System.Drawing">
    <value>136, 16</value>
  </data>
  <data name="label5.TabIndex" type="System.Int32, mscorlib">
    <value>76</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>Framing special effect</value>
  </data>
  <data name="&gt;&gt;label5.Name" xml:space="preserve">
    <value>label5</value>
  </data>
  <data name="&gt;&gt;label5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label5.Parent" xml:space="preserve">
    <value>tabPage3</value>
  </data>
  <data name="&gt;&gt;label5.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="comboBoxFraming.Items" xml:space="preserve">
    <value>none</value>
  </data>
  <data name="comboBoxFraming.Items1" xml:space="preserve">
    <value>Ellipse</value>
  </data>
  <data name="comboBoxFraming.Items2" xml:space="preserve">
    <value>Faded ellipse</value>
  </data>
  <data name="comboBoxFraming.Items3" xml:space="preserve">
    <value>Faded ellipse2</value>
  </data>
  <data name="comboBoxFraming.Items4" xml:space="preserve">
    <value>All corners rounded</value>
  </data>
  <data name="comboBoxFraming.Items5" xml:space="preserve">
    <value>Top corners rounded</value>
  </data>
  <data name="comboBoxFraming.Location" type="System.Drawing.Point, System.Drawing">
    <value>240, 64</value>
  </data>
  <data name="comboBoxFraming.Size" type="System.Drawing.Size, System.Drawing">
    <value>136, 21</value>
  </data>
  <data name="comboBoxFraming.TabIndex" type="System.Int32, mscorlib">
    <value>75</value>
  </data>
  <data name="comboBoxFraming.Text" xml:space="preserve">
    <value>none</value>
  </data>
  <data name="&gt;&gt;comboBoxFraming.Name" xml:space="preserve">
    <value>comboBoxFraming</value>
  </data>
  <data name="&gt;&gt;comboBoxFraming.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxFraming.Parent" xml:space="preserve">
    <value>tabPage3</value>
  </data>
  <data name="&gt;&gt;comboBoxFraming.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="dcsFrameProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 40</value>
  </data>
  <data name="dcsFrameProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>176, 112</value>
  </data>
  <data name="dcsFrameProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>45</value>
  </data>
  <data name="&gt;&gt;dcsFrameProperties1.Name" xml:space="preserve">
    <value>dcsFrameProperties1</value>
  </data>
  <data name="&gt;&gt;dcsFrameProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSFrameProperties1, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsFrameProperties1.Parent" xml:space="preserve">
    <value>tabPage3</value>
  </data>
  <data name="&gt;&gt;dcsFrameProperties1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="tabPage3.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tabPage3.Size" type="System.Drawing.Size, System.Drawing">
    <value>400, 214</value>
  </data>
  <data name="tabPage3.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="tabPage3.Text" xml:space="preserve">
    <value>Frame outline</value>
  </data>
  <data name="&gt;&gt;tabPage3.Name" xml:space="preserve">
    <value>tabPage3</value>
  </data>
  <data name="&gt;&gt;tabPage3.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabPage3.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabPage3.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="tabControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 192</value>
  </data>
  <data name="tabControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>408, 240</value>
  </data>
  <data name="tabControl1.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;tabControl1.Name" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabControl1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabControl, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tabControl1.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <metadata name="colorDialog1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="tbRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>498, 148</value>
  </data>
  <data name="tbRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 20</value>
  </data>
  <data name="tbRatio.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="tbRatio.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="tbRatio.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="&gt;&gt;tbRatio.Name" xml:space="preserve">
    <value>tbRatio</value>
  </data>
  <data name="&gt;&gt;tbRatio.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbRatio.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbRatio.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="label2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>426, 148</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 16</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>w / h ratio</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="buttonLockAspectRatio.ImageIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <metadata name="imageList1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>130, 17</value>
  </metadata>
  <data name="imageList1.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj0yLjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAC8
        CQAAAk1TRnQBSQFMAgEBAgEAAQwBAAEMAQABGAEAARcBAAT/AQkBAAj/AUIBTQE2AQQGAAE2AQQCAAEo
        AwABYAMAARcDAAEBAQABCAUAAaABCBgAAYACAAGAAwACgAEAAYADAAGAAQABgAEAAoACAAPAAQABwAHc
        AcABAAHwAcoBpgEAATMFAAEzAQABMwEAATMBAAIzAgADFgEAAxwBAAMiAQADKQEAA1UBAANNAQADQgEA
        AzkBAAGAAXwB/wEAAlAB/wEAAZMBAAHWAQAB/wHsAcwBAAHGAdYB7wEAAdYC5wEAAZABqQGtAgAB/wEz
        AwABZgMAAZkDAAHMAgABMwMAAjMCAAEzAWYCAAEzAZkCAAEzAcwCAAEzAf8CAAFmAwABZgEzAgACZgIA
        AWYBmQIAAWYBzAIAAWYB/wIAAZkDAAGZATMCAAGZAWYCAAKZAgABmQHMAgABmQH/AgABzAMAAcwBMwIA
        AcwBZgIAAcwBmQIAAswCAAHMAf8CAAH/AWYCAAH/AZkCAAH/AcwBAAEzAf8CAAH/AQABMwEAATMBAAFm
        AQABMwEAAZkBAAEzAQABzAEAATMBAAH/AQAB/wEzAgADMwEAAjMBZgEAAjMBmQEAAjMBzAEAAjMB/wEA
        ATMBZgIAATMBZgEzAQABMwJmAQABMwFmAZkBAAEzAWYBzAEAATMBZgH/AQABMwGZAgABMwGZATMBAAEz
        AZkBZgEAATMCmQEAATMBmQHMAQABMwGZAf8BAAEzAcwCAAEzAcwBMwEAATMBzAFmAQABMwHMAZkBAAEz
        AswBAAEzAcwB/wEAATMB/wEzAQABMwH/AWYBAAEzAf8BmQEAATMB/wHMAQABMwL/AQABZgMAAWYBAAEz
        AQABZgEAAWYBAAFmAQABmQEAAWYBAAHMAQABZgEAAf8BAAFmATMCAAFmAjMBAAFmATMBZgEAAWYBMwGZ
        AQABZgEzAcwBAAFmATMB/wEAAmYCAAJmATMBAANmAQACZgGZAQACZgHMAQABZgGZAgABZgGZATMBAAFm
        AZkBZgEAAWYCmQEAAWYBmQHMAQABZgGZAf8BAAFmAcwCAAFmAcwBMwEAAWYBzAGZAQABZgLMAQABZgHM
        Af8BAAFmAf8CAAFmAf8BMwEAAWYB/wGZAQABZgH/AcwBAAHMAQAB/wEAAf8BAAHMAQACmQIAAZkBMwGZ
        AQABmQEAAZkBAAGZAQABzAEAAZkDAAGZAjMBAAGZAQABZgEAAZkBMwHMAQABmQEAAf8BAAGZAWYCAAGZ
        AWYBMwEAAZkBMwFmAQABmQFmAZkBAAGZAWYBzAEAAZkBMwH/AQACmQEzAQACmQFmAQADmQEAApkBzAEA
        ApkB/wEAAZkBzAIAAZkBzAEzAQABZgHMAWYBAAGZAcwBmQEAAZkCzAEAAZkBzAH/AQABmQH/AgABmQH/
        ATMBAAGZAcwBZgEAAZkB/wGZAQABmQH/AcwBAAGZAv8BAAHMAwABmQEAATMBAAHMAQABZgEAAcwBAAGZ
        AQABzAEAAcwBAAGZATMCAAHMAjMBAAHMATMBZgEAAcwBMwGZAQABzAEzAcwBAAHMATMB/wEAAcwBZgIA
        AcwBZgEzAQABmQJmAQABzAFmAZkBAAHMAWYBzAEAAZkBZgH/AQABzAGZAgABzAGZATMBAAHMAZkBZgEA
        AcwCmQEAAcwBmQHMAQABzAGZAf8BAALMAgACzAEzAQACzAFmAQACzAGZAQADzAEAAswB/wEAAcwB/wIA
        AcwB/wEzAQABmQH/AWYBAAHMAf8BmQEAAcwB/wHMAQABzAL/AQABzAEAATMBAAH/AQABZgEAAf8BAAGZ
        AQABzAEzAgAB/wIzAQAB/wEzAWYBAAH/ATMBmQEAAf8BMwHMAQAB/wEzAf8BAAH/AWYCAAH/AWYBMwEA
        AcwCZgEAAf8BZgGZAQAB/wFmAcwBAAHMAWYB/wEAAf8BmQIAAf8BmQEzAQAB/wGZAWYBAAH/ApkBAAH/
        AZkBzAEAAf8BmQH/AQAB/wHMAgAB/wHMATMBAAH/AcwBZgEAAf8BzAGZAQAB/wLMAQAB/wHMAf8BAAL/
        ATMBAAHMAf8BZgEAAv8BmQEAAv8BzAEAAmYB/wEAAWYB/wFmAQABZgL/AQAB/wJmAQAB/wFmAf8BAAL/
        AWYBAAEhAQABpQEAA18BAAN3AQADhgEAA5YBAAPLAQADsgEAA9cBAAPdAQAD4wEAA+oBAAPxAQAD+AEA
        AfAB+wH/AQABpAKgAQADgAMAAf8CAAH/AwAC/wEAAf8DAAH/AQAB/wEAAv8CAAP/AQAXtQHPF7UBzzAA
        BwcB9wu1AfcB7wIHAbUHBwH3C7UB9wHvAgcBtTAABwcBrguGAa4B7QIHAbUHBwGuC4YBrgHtAgcBtTAA
        BwcChgfsAYYCBAGGAe0CBwG1BwcChgfsAYYCBAGGAe0CBwG1MAAHBwOGAewFkgGuAYYBBAGGAe0CBwG1
        BwcDhgHsBZIBrgGGAQQBhgHtAgcBtTAABwcBhgIEAa4F7QHsAa4BBAGGAe0CBwG1BwcBhgIEAa4F7QHs
        Aa4BBAGGAe0CBwG1MAAHBwGGAQQIhgIEAYYB7QIHAbUHBwGGAQQIhgIEAYYB7QIHAbUwAAcHAYYBBAiG
        AgQBhgHtAgcBtQcHAYYBBAiGAgQBhgHtAgcBtTAABwcBhgEECYYBBAGGAe0CBwG1BwcBhgEECYYBBAGG
        Ae0CBwG1MAAHBwGGCwQBhgHtAgcBtQcHAYYLBAGGAe0CBwG1MAAHBwH3Aa4BBAGGCK4B9wHvAgcBtQcH
        AfcBrgEEAYYFrgKGAa4B9wHvAgcBtTAACAcB7QEEAYYB7QsHAbUIBwHtAQQBhgHtBAcChgUHAbUwAAgH
        Ae0BBAGGAe0LBwG1CAcB7QEEAYYB7QQHAoYFBwG1MAAIBwHtAQQBhgHtCwcBtQgHAe0BBAGGAe0EBwKG
        BQcBtTAACAcB7QEEAYYB7QsHAbUIBwHtAQQBhgHtBAcChgUHAbUwAAgHAe0ChgGuAgcD7wYHAbUIBwHt
        AoYBrgIHAu8ChgUHAbUwAAgHAe8B9wKGAvcB7AGGAa4B7QUHAbUIBwHvAfcChgL3AewChgGuBQcBtTAA
        CgcBrgSGAQQBrgH3BQcBtQoHAa4EhgEEAa4B9wUHAbUwAAoHAa4FhgGuAe8FBwG1CgcBrgWGAa4B7wUH
        AbUwAAsHBe8HBwG1CwcF7wcHAbUwABcHAbUXBwG1MAAXBwG1FwcBtTAAFwcBtRcHAbUwAAFCAU0BPgcA
        AT4DAAEoAwABYAMAARcDAAEBAQABAQUAARQBARYAA///ABYACw==
</value>
  </data>
  <data name="buttonLockAspectRatio.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonLockAspectRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>578, 148</value>
  </data>
  <data name="buttonLockAspectRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>26, 25</value>
  </data>
  <data name="buttonLockAspectRatio.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;buttonLockAspectRatio.Name" xml:space="preserve">
    <value>buttonLockAspectRatio</value>
  </data>
  <data name="&gt;&gt;buttonLockAspectRatio.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonLockAspectRatio.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonLockAspectRatio.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="buttonRotateCW.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonRotateCW.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonRotateCW.Location" type="System.Drawing.Point, System.Drawing">
    <value>552, 280</value>
  </data>
  <data name="buttonRotateCW.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 24</value>
  </data>
  <data name="buttonRotateCW.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="buttonRotateCW.Text" xml:space="preserve">
    <value>+90</value>
  </data>
  <data name="&gt;&gt;buttonRotateCW.Name" xml:space="preserve">
    <value>buttonRotateCW</value>
  </data>
  <data name="&gt;&gt;buttonRotateCW.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonRotateCW.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonRotateCW.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="buttonRotateCCW.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonRotateCCW.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonRotateCCW.Location" type="System.Drawing.Point, System.Drawing">
    <value>480, 280</value>
  </data>
  <data name="buttonRotateCCW.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 24</value>
  </data>
  <data name="buttonRotateCCW.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="buttonRotateCCW.Text" xml:space="preserve">
    <value>-90</value>
  </data>
  <data name="&gt;&gt;buttonRotateCCW.Name" xml:space="preserve">
    <value>buttonRotateCCW</value>
  </data>
  <data name="&gt;&gt;buttonRotateCCW.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonRotateCCW.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonRotateCCW.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <metadata name="toolTip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>235, 17</value>
  </metadata>
  <data name="comboBoxPanel.Items" xml:space="preserve">
    <value>default</value>
  </data>
  <data name="comboBoxPanel.Items1" xml:space="preserve">
    <value>K Panel</value>
  </data>
  <data name="comboBoxPanel.Location" type="System.Drawing.Point, System.Drawing">
    <value>498, 176</value>
  </data>
  <data name="comboBoxPanel.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 21</value>
  </data>
  <data name="comboBoxPanel.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="comboBoxPanel.Text" xml:space="preserve">
    <value>default</value>
  </data>
  <data name="&gt;&gt;comboBoxPanel.Name" xml:space="preserve">
    <value>comboBoxPanel</value>
  </data>
  <data name="&gt;&gt;comboBoxPanel.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxPanel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;comboBoxPanel.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelPanel.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelPanel.Location" type="System.Drawing.Point, System.Drawing">
    <value>426, 179</value>
  </data>
  <data name="labelPanel.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 16</value>
  </data>
  <data name="labelPanel.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="labelPanel.Text" xml:space="preserve">
    <value>panel</value>
  </data>
  <data name="&gt;&gt;labelPanel.Name" xml:space="preserve">
    <value>labelPanel</value>
  </data>
  <data name="&gt;&gt;labelPanel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelPanel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelPanel.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="dcsSourceProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 24</value>
  </data>
  <data name="dcsSourceProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>360, 149</value>
  </data>
  <data name="dcsSourceProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;dcsSourceProperties1.Name" xml:space="preserve">
    <value>dcsSourceProperties1</value>
  </data>
  <data name="&gt;&gt;dcsSourceProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSSourceProperties, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsSourceProperties1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;dcsSourceProperties1.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="dcsPositionSizeProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>426, 12</value>
  </data>
  <data name="dcsPositionSizeProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 128</value>
  </data>
  <data name="dcsPositionSizeProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;dcsPositionSizeProperties1.Name" xml:space="preserve">
    <value>dcsPositionSizeProperties1</value>
  </data>
  <data name="&gt;&gt;dcsPositionSizeProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSPositionSizeProperties, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsPositionSizeProperties1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;dcsPositionSizeProperties1.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>42</value>
  </metadata>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>633, 447</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterParent</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Image Object Properties ---</value>
  </data>
  <data name="&gt;&gt;colorDialog1.Name" xml:space="preserve">
    <value>colorDialog1</value>
  </data>
  <data name="&gt;&gt;colorDialog1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColorDialog, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;imageList1.Name" xml:space="preserve">
    <value>imageList1</value>
  </data>
  <data name="&gt;&gt;imageList1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ImageList, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolTip1.Name" xml:space="preserve">
    <value>toolTip1</value>
  </data>
  <data name="&gt;&gt;toolTip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolTip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>ImageObjProperties</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>