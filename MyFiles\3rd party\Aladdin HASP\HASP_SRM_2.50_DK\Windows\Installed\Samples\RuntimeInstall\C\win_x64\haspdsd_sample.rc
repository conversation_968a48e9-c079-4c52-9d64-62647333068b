/*  $Id: haspdsd_sample.rc,v 1.5 2007/03/06 13:52:28 horatiu Exp $
**
**  Aladdin Knowledge Systems Ltd. (c) 1985-2007. All rights reserved.
**
**  haspdsd_sample - RC file
**
*/

#include <windows.h>
#include <ntverp.h>

#include "usage.h"

/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

IDC_USAGE DIALOG DISCARDABLE  161, 65, 338, 222
STYLE DS_MODALFRAME | WS_POPUP | WS_VISIBLE | WS_CAPTION
CAPTION "HASP SRM Run-time Environment Installer"
FONT 9, "MS Shell Dlg"
BEGIN
    PUSHBUTTON      "&OK",IDC_OK,150,202,40,14
    LISTBOX         IDC_TEXT,0,1,338,207,WS_VSCROLL | WS_HSCROLL |
                    WS_TABSTOP
END

IDC_INFO DIALOG DISCARDABLE  161, 65, 229, 243
STYLE DS_MODALFRAME | WS_POPUP | WS_VISIBLE | WS_CAPTION
CAPTION "HASP SRM Run-time Environment Installer"
FONT 9, "Courier"
BEGIN
    PUSHBUTTON      "&OK",IDC_OK,93,215,40,14
    LISTBOX         IDC_TEXT,14,10,200,200
END


/////////////////////////////////////////////////////////////////////////////
//
// Version
//

#ifdef VER_PRODUCTBUILD
#undef VER_PRODUCTBUILD
#endif

#ifdef VER_PRODUCTVERSION_STR
#undef VER_PRODUCTVERSION_STR
#endif

#ifdef VER_PRODUCTVERSION
#undef VER_PRODUCTVERSION
#endif

#ifdef VER_FILEVERSION_STR
#undef VER_FILEVERSION_STR
#endif

#ifdef VER_FILEVERSION
#undef VER_FILEVERSION
#endif

#ifdef VER_PRODUCTBETA_STR
#undef VER_PRODUCTBETA_STR
#endif

#ifdef VER_COMPANYNAME_STR
#undef VER_COMPANYNAME_STR
#endif

#ifdef VER_PRODUCTNAME_STR
#undef VER_PRODUCTNAME_STR
#endif

#ifdef VER_LEGALTRADEMARKS_STR
#undef VER_LEGALTRADEMARKS_STR
#endif

#ifdef VER_LEGALCOPYRIGHT_STR
#undef VER_LEGALCOPYRIGHT_STR
#endif

#ifdef VER_FILEDESCRIPTION_STR
#undef VER_FILEDESCRIPTION_STR
#endif

#ifdef VER_INTERNALNAME_STR
#undef VER_INTERNALNAME_STR
#endif

#ifdef VER_ORIGINALFILENAME_STR
#undef VER_ORIGINALFILENAME_STR
#endif


#define VER_PRODUCTVERSION_STR	"5.25"

#define VER_PRODUCTVERSION	5,25,0,1
#define VER_PRODUCTBETA_STR	"Monster"

#define VER_FILEVERSION_STR	"5.25"
#define VER_FILEVERSION	5,25,0,1

#define VER_COMPANYNAME_STR	"Aladdin Knowledge Systems Ltd."
#define VER_PRODUCTNAME_STR	"Aladdin HASP SRM Run-time Environment Installer Sample"
#define VER_LEGALTRADEMARKS_STR	"HASP SRM is a trademark of Aladdin Knowledge Systems Ltd."
#define VER_LEGALCOPYRIGHT_STR	"Aladdin Knowledge Systems Ltd. (c) 1985-2007. All rights reserved."

#define	VER_FILETYPE	VFT_DRV
#define	VER_FILESUBTYPE	VFT2_DRV_SYSTEM
#define VER_FILEDESCRIPTION_STR     "Aladdin HASP SRM Run-time Environment Installer Sample"
#ifdef IA64
#define VER_INTERNALNAME_STR        "haspds_sample.exe for WIN64"
#else
#define VER_INTERNALNAME_STR        "haspds_sample.exe"
#endif
#define VER_ORIGINALFILENAME_STR    "haspds_sample.exe"


#include "common.ver"


