# How to Fix the Missing References Issue in DCSDDEServer

## Problem
The DCSDDEServer project is failing to build because it cannot find the following assemblies:
- `D:\repos_D\SDS Collection\DCSSDK Badging\DCSSDK_BadgingMgt\bin\Debug\DCSSDK_BadgingMgt.dll`
- `D:\repos_D\SDS Collection\DCSSDK Badging\DCSSDK_CaptureMgt\bin\Debug\DCSSDK_CaptureMgt.dll`

These files are project references in your solution but the projects or files don't exist at the specified locations.

## Solution Implemented
We have already taken the following steps to fix this issue:

1. Created a `lib` folder in your DCSDDEServer project directory to store local copies of these DLLs
2. Copied the necessary DLLs from `D:\repos_D\SDS Collection\components\` to the `lib` folder
3. Created a PowerShell script `UpdateProject.ps1` that modifies your project file to:
   - Add references to the local DLL copies
   - Remove the project references to the missing projects

## Manual Steps to Complete the Fix
To complete the fix, please follow these steps:

1. Close Visual Studio completely
2. Make sure the following files exist:
   - `D:\repos_D\SDS Collection\DCSDDEServer\lib\DCSSDK_BadgingMgt.dll`
   - `D:\repos_D\SDS Collection\DCSDDEServer\lib\DCSSDK_CaptureMgt.dll`
3. Open Visual Studio
4. Right-click on your solution in Solution Explorer and select "Close Solution"
5. Open your solution again
6. Build the project

## Alternative Manual Fix
If the above solution doesn't work, you can manually edit your project file:

1. Close Visual Studio
2. Open `D:\repos_D\SDS Collection\DCSDDEServer\DCSDDEServer.csproj` in a text editor like Notepad
3. Find the References section and add:
```xml
<Reference Include="DCSSDK_BadgingMgt">
  <HintPath>lib\DCSSDK_BadgingMgt.dll</HintPath>
  <Private>True</Private>
</Reference>
<Reference Include="DCSSDK_CaptureMgt">
  <HintPath>lib\DCSSDK_CaptureMgt.dll</HintPath>
  <Private>True</Private>
</Reference>
```
4. Find and comment out or remove the project references to DCSSDK_BadgingMgt and DCSSDK_CaptureMgt
5. Save the file
6. Reopen Visual Studio and build

## Long-term Solution
For a more permanent solution, you should consider:
1. Properly restoring the missing projects to your solution
2. Using NuGet packages for these dependencies
3. Setting up a proper build pipeline for these dependencies