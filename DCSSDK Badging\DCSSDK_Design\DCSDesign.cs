using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Collections;
using System.IO;
using System.Windows.Forms;
using System.Drawing.Text;
using System.Runtime.InteropServices;

namespace DCSDEV.DCSDesign
{
	/// <summary>
	/// Summary description for DCSDesign.
	/// </summary>
	[Serializable()] 
	public class DCSDesign : IDisposable
	{
        public enum RipMode { RIPMODE_LAYOUT, RIPMODE_LAYOUTPRINT, RIPMODE_PREVIEW, RIPMODE_PRINT, RIPMODE_PRINT_NONK, RIPMODE_PRINT_KIMAGE, RIPMODE_PRINT_KTEXT };

		public DCSDEV.DCSDesign.DCSBadgeDataset m_BadgeData = null;
		private DCSDEV.DCSDesign.DCSBadgeDataset m_LayoutBadgeData = null;
		public bool m_isDirty;	// used by derived DCSDesignerDoc class
		public bool m_isViewDirty;	// used by derived DCSDesignerDoc class
		private string m_AppDateFormat;		// date format provided in badge.dat file.

		public ArrayList m_designSides = null;
		public string m_strDocumentName = null;	// name of design with personal data included - may appear in preview header and print mgr list
		public string m_strDesignName = null;	// name of design template alone
		
		//common
		public string strVersion = "1.1";
		public int PrinterTypeIndex = 0;
		public Rectangle Bounds;
		public int NumBadgeSides = 1;
		public string BadgeSizeCode;   // recorded but not yet uesd

		public bool HasChip;
		public string FormulaChip = "";
		public bool HasMagStripe;
		public string SourceTrack1 = "";
		public string SourceTrack2 = "";
		public string SourceTrack3 = "";
		public string FormulaTrack1 = "";
		public string FormulaTrack2 = "";
		public string FormulaTrack3 = "";

		private string Track1Data = "";
		private string Track2Data = "";
		private string Track3Data = "";
        private const int TRACK1_LENGTH = 76;
        private const int TRACK2_LENGTH = 37;
        private const int TRACK3_LENGTH = 104;
        private const int TRACK3_AAMVA_LENGTH = 76;

		public int m_GridSpacingX = 10;		// the snap to grid size - which the designer may or may not use
		public int m_GridSpacingY = 10;
		public int m_TabRange = 10;
		public ArrayList m_arrayTabStopsH = null;
		public ArrayList m_arrayTabStopsV = null;

		// Predefined Document fonts
		public ArrayList m_arrayDocFontExs = new ArrayList();

		// default fonts
		public DCSDatatypes.DCSLabeledTextOrientations PriorLabelOrientation = DCSDatatypes.DCSLabeledTextOrientations.TOP;
		public int PriorLabelOffset = 15;
		public int PriorLabelFontIndex = 0;

		public int PriorBoundsHeight = 20;
		public int PriorFontIndex = -1;
		public DCSFontEx PriorFontEx = new DCSFontEx();
		public DCSDEV.DCSDatatypes.Alignments PriorAlignment;
		public DCSDEV.DCSDatatypes.Justifications PriorJustification;

		public DCSDesign()
		{
			//
			// TODO: Add constructor logic here
			//
			this.Reset();
			this.m_arrayDocFontExs.Add(new DCSFontEx());
			this.m_arrayDocFontExs.Add(new DCSFontEx());
			this.m_arrayDocFontExs.Add(new DCSFontEx());

			((DCSFontEx)m_arrayDocFontExs[0]).Font = new Font("Arial", 6.0F, System.Drawing.FontStyle.Regular);
			((DCSFontEx)m_arrayDocFontExs[1]).Font = new Font("Arial", 12.0F, System.Drawing.FontStyle.Regular);
			((DCSFontEx)m_arrayDocFontExs[2]).Font = new Font("Arial", 12.0F, System.Drawing.FontStyle.Regular);
			((DCSFontEx)m_arrayDocFontExs[2]).ForeColor = Color.Gray;

            ParameterStore ps = new ParameterStore("DCSSDK_Mgt");
            m_AppDateFormat = ps.GetStringParameter("AppDateFormat", "default");
		}

		// Implement IDisposable.
		// Do not make this method virtual.
		// A derived class should not be able to override this method.
		public void Dispose()
		{
			if (m_designSides != null)
			{
				foreach(DCSDEV.DCSDesign.DCSDesignSide designSide in m_designSides)
					designSide.Dispose();
			}
		}

        // read variable data into DCSBadgeDataSet from strBadgeDoDatName; load the template into DCSDesign and merge the two
        public bool LoadBadgeDesignAndData(string strBadgeDotDatName)
        {
            bool bRet = true;

            /////////////////////////////////////////////////////////
            // initialize m_lFieldNames from AllField.txt
            /////////////////////////////////////////////////////////
            string allfield;
            DCSDEV.ParameterStore ps = new ParameterStore("DCSSDK_Mgt");
            allfield = System.IO.Path.Combine(ps.m_strDCSInstallDirectory, "AllField.txt");
            if (!File.Exists(allfield))
            {
                DCSMsg.Show("The table of field names " + allfield + " is missing.");
                return false;
            }

            m_BadgeData = new DCSBadgeDataset();
			bRet = m_BadgeData.LoadAll(allfield, strBadgeDotDatName);
            if (!bRet) return false;

            ///////////////////////
            // load badge design //
            ///////////////////////
            string strDesignFile;
			strDesignFile = DCSDEV.DCSDesignDataAccess.ExpandDesignName(m_BadgeData.m_strDesignName, true);
            if (strDesignFile == null)
            {
				DCSDEV.DCSMsg.Show(String.Format("The document design '{0}' is missing.", m_BadgeData.m_strDesignName));
                return false;
            }

			bRet = this.ReadBadgeDesign(m_BadgeData.m_strDesignName);
            if (!bRet) return false;

            /////////////////////////////////////////
            // merge Badge data into this badge design  //
            /////////////////////////////////////////
			this.MergeWithDB(m_BadgeData);

            return true;
        }

		private void MergeWithDB(DCSBadgeDataset badgeData)
		{
			m_strDocumentName = badgeData.m_strDocumentName;

			// loop over all sides
			foreach(DCSDEV.DCSDesign.DCSDesignSide designSide in m_designSides)
				designSide.MergeSideWithDB(badgeData);

            bool bDoMagStripe = false;
            DCSDEV.PrintProperties.PrinterTypeDatum bcDatum = null;
            if (this.HasMagStripe)
            {
                bcDatum = new DCSDEV.PrintProperties.PrinterTypeDatum("");
                bcDatum.LoadPrinterTypeData(this.PrinterTypeIndex);
                if (bcDatum.m_IfMagStripe) bDoMagStripe = true;
            }
            if (bDoMagStripe && bcDatum != null)
			{
				this.Track1Data = "";
				this.Track2Data = "";
				this.Track3Data = "";

                // merge data into mag track
                // datum says if printer can do the track; source says if layout wants track done.
                if (bcDatum.m_IfMagTrack1 && this.SourceTrack1 != "")
                {
                    if (this.SourceTrack1 == "Formula")
                        this.Track1Data = DCSDEV.DCSDesign.DCSFormula.FormulaEval(this.FormulaTrack1, badgeData);
                    else
                    {
                        foreach (DCSDEV.DCSDesign.FieldInfo fi in badgeData.m_lFieldNamesAndValues)
                        {
                            if (this.SourceTrack1 == fi.fieldName)
                            {
                                this.Track1Data = fi.FieldValue.ToString();
                                break;
                            }
                        }
                    }
                    if (this.Track1Data != "")
                    {
                        if (Track1Data.Length > TRACK1_LENGTH)
                        {
                            DCSMsg.Show(String.Format("Data to encode in magnetic track 1 exceeds {0} characters. Track 1 will be truncated.", TRACK1_LENGTH));
                            this.Track1Data = this.Track1Data.Substring(0, TRACK1_LENGTH);
                        }
                        this.Track1Data = this.Track1Data.ToUpper();
                        if (!DCSDEV.DCSServerStuff.IsAlphaNumericOrCaret(this.Track1Data))
                        {
                            DCSMsg.Show("Improper character in magnetic stripe track 1.  Track 1 will be blank.");
                            this.Track1Data = "";
                        }
                    }
                    if (this.Track1Data != "")
                        this.Track1Data = bcDatum.m_strPrefixTrack1 + this.Track1Data + bcDatum.m_strSuffixTrack1;
                }
                if (bcDatum.m_IfMagTrack2 && this.SourceTrack2 != "")
                {
                    if (this.SourceTrack2 == "Formula")
                        this.Track2Data = DCSDEV.DCSDesign.DCSFormula.FormulaEval(this.FormulaTrack2, badgeData);
                    else
                    {
                        foreach (DCSDEV.DCSDesign.FieldInfo fi in badgeData.m_lFieldNamesAndValues)
                        {
                            if (this.SourceTrack2 == fi.fieldName)
                            {
                                this.Track2Data = fi.FieldValue.ToString();
                                break;
                            }
                        }
                    }
                    if (Track2Data.Length > TRACK2_LENGTH)
                    {
                        DCSMsg.Show(String.Format("Data to encode in magnetic track 2 exceeds {0} characters. Track 2 will be truncated.", TRACK2_LENGTH));
                        this.Track2Data = this.Track2Data.Substring(0, TRACK2_LENGTH);
                    }
                    if (this.Track2Data != "")
                    {
                        if (!DCSDEV.DCSServerStuff.IsNumericOrEqual(this.Track2Data))
                        {
                            DCSMsg.Show("Improper non numeric character in magnetic stripe track 2.  Track 2 will be blank.");
                            this.Track2Data = "";
                        }
                    }
                    if (this.Track2Data != "")
                        this.Track2Data = bcDatum.m_strPrefixTrack2 + this.Track2Data + bcDatum.m_strSuffixTrack2;
                }
                if (bcDatum.m_IfMagTrack3 && this.SourceTrack3 != "")
                {
                    if (this.SourceTrack3 == "Formula")
                        this.Track3Data = DCSDEV.DCSDesign.DCSFormula.FormulaEval(this.FormulaTrack3, badgeData);
                    else
                    {
                        foreach (DCSDEV.DCSDesign.FieldInfo fi in badgeData.m_lFieldNamesAndValues)
                        {
                            if (this.SourceTrack3 == fi.fieldName)
                            {
                                this.Track3Data = fi.FieldValue.ToString();
                                break;
                            }
                        }
                    }
                    if (this.Track3Data != "")
                    {
                        if (bcDatum.m_IfAAMVA)
                        {
                            if (Track3Data.Length > TRACK3_AAMVA_LENGTH)
                            {
                                DCSMsg.Show(String.Format("Data to encode in AAMVA magnetic track 3 exceeds {0} characters. Track 3 will be truncated.", TRACK3_AAMVA_LENGTH));
                                this.Track3Data = this.Track3Data.Substring(0, TRACK3_AAMVA_LENGTH);
                            }
                            // AAMVA encoding allow alpha numeric chars so allow them here
                            this.Track3Data = this.Track3Data.ToUpper();
                            if (!DCSDEV.DCSServerStuff.IsAlphaNumericOrEqual(this.Track3Data))
                            {
                                DCSMsg.Show("Improper non alphanumeric character in AAMVA magnetic stripe track 3.  Track 3 will be blank.");
                                this.Track3Data = "";
                            }
                        }
                        else
                        {
                            if (Track3Data.Length > TRACK3_LENGTH)
                            {
                                DCSMsg.Show(String.Format("Data to encode in magnetic track 3 exceeds {0} characters. Track 3 will be truncated.", TRACK3_LENGTH));
                                this.Track3Data = this.Track3Data.Substring(0, TRACK3_LENGTH);
                            }
                            if (this.Track3Data != "")
                            {
                                if (!DCSDEV.DCSServerStuff.IsNumericOrEqual(this.Track3Data))
                                {
                                    DCSMsg.Show("Improper non numeric character in magnetic stripe track 3.  Track 3 will be blank.");
                                    this.Track3Data = "";
                                }
                            }
                        }
                    }
                    if (this.Track3Data != "")
                        this.Track3Data = bcDatum.m_strPrefixTrack3 + this.Track3Data + bcDatum.m_strSuffixTrack3;
                }
            }
		}

		public void MergeWithSampleImages()
		{
			// loop over all sides
			foreach(DCSDEV.DCSDesign.DCSDesignSide designSide in m_designSides)
				designSide.MergeSideWithSampleImages();
		}

		// convert all measurements to INCH100
		// As read from DigiCardLayout, tokens coords are in pixels and Badge/sheet coords are in MM100
		private void ConvertDesignToInch100()
		{
			this.Bounds = DCSMath.MM100toINCH100(this.Bounds);

			foreach(DCSDEV.DCSDesign.DCSDesignSide designSide in m_designSides)
			{
				foreach (DCSDesignObject designObject in designSide.m_DCSDesignObjects)
				{
					// convert pixels to inch100
					designObject.Bounds = DCSMath.PIXELtoINCH100(designObject.Bounds);
				}
			}
		}

		// make sure interdependent parameters are set right
		private void NormalizeParameters()
		{
			foreach(DCSDEV.DCSDesign.DCSDesignSide designSide in m_designSides)
			{
				if (designSide.SideFillType == DCSDEV.DCSDatatypes.BackFillTypes.FILL_CLEAR)
					designSide.SideBackColor = Color.White;
				foreach (DCSDesignObject designObject in designSide.m_DCSDesignObjects)
				{
					if (designObject.BackFillType == DCSDEV.DCSDatatypes.BackFillTypes.FILL_CLEAR)
						designObject.BackColor = Color.Transparent;	// syh want to eliminate
				}
			}
		}

        public int ReadBadgeDesign_GetPrinterType(string strDesignFileIn)
        {
            string strDesignFile = DCSDesignDataAccess.ExpandDesignName(strDesignFileIn, true);
            if (strDesignFile == null)
            {
                DCSDEV.DCSMsg.Show(String.Format("Unable to find design file named '{0}'.", strDesignFileIn));
                return -1;
            }
            using (StreamReader sr = new StreamReader(strDesignFile))	// design files created by this program use UTF8 character set
            {
                string line = "xxx";
                int linecount = 0;
                int ireturn = -1;

                try
                {
                    int i;
                    string strVersion = "";
                    string strDataValue;
                    string[] tokens;
                    char[] Separator = { ' ' };

                    while ((line = sr.ReadLine()) != null)
                    {
                        if (linecount == 0)
                        {
                            if (line.Substring(0, 7) != "Version") break;
                        }
                        ++linecount;
                        // split line apart on spaces and then recombine all segments but the first -syh this could be simplified!
                        tokens = line.Split(Separator);
                        strDataValue = String.Empty;
                        for (i = 1; i < tokens.Length; ++i)
                            if (i == 1)
                                strDataValue = tokens[1];
                            else
                                strDataValue = strDataValue + " " + tokens[i];

                        switch (tokens[0])
                        {
                            // version should come first
                            case "Version":
                                strVersion = tokens[1];
                                break;
                            case "PrinterTypeIndex":
                                ireturn = Convert.ToInt32(tokens[1]);
                                break;
                        }
                        if (ireturn != -1) break;
                    }	// end of while read loop
                    if (strVersion.Length <= 0)
                    {
                        DCSDEV.DCSMsg.Show(String.Format("ERROR: Design file '{0}' is not a valid document design format", strDesignFile));
                        return -1;
                    }
                }
                catch
                {
                    if (sr != null) sr.Close();
                    ireturn = -1;
                }
                finally
                {
                    if (sr != null) sr.Close();
                }
                return ireturn;
            }
        }

		public bool ReadBadgeDesign(string strDesignName)
		{
			bool bRet;
			bRet = this.ReadDesignFromTextFile(strDesignName);
			if (!bRet) return false;

			this.m_strDesignName = strDesignName;

			if (this.strVersion == "1.0")
			{
				// convert pixels to inch100 - only tokens are in pixels
				this.ConvertDesignToInch100();
				this.strVersion = "1.1";
			}
			this.NormalizeParameters();

			return true;
		}
		public bool WriteBadgeDesign(string strDesignName)
		{
			return this.WriteDesignToTextFile(strDesignName);
		}

		public void Reset()
		{
			Bounds = Rectangle.Empty;
			NumBadgeSides = 1;
			BadgeSizeCode = string.Empty;
			
			HasChip = false;
			FormulaChip = "";
			HasMagStripe = false;

			SourceTrack1 = "";
			SourceTrack2 = "";
			SourceTrack3 = "";
			FormulaTrack1 = "";
			FormulaTrack2 = "";
			FormulaTrack3 = "";
			this.Track1Data = "";
			this.Track2Data = "";
			this.Track3Data = "";

			if (m_designSides != null)
			{
				foreach(DCSDEV.DCSDesign.DCSDesignSide designSide in m_designSides)
					designSide.Dispose();
				m_designSides.Clear();
			}
			else
				m_designSides = new ArrayList();

			// start with one side
			m_designSides.Add(new DCSDesignSide());
		}

		private Font MakeFont(System.Drawing.FontStyle iFontStyle, string strFontFamily, string strFontSize)
		{
			string strBDGDecimal = null;
			if (strFontSize.IndexOf(".") >= 0) strBDGDecimal = ".";
			else if (strFontSize.IndexOf(",") >= 0) strBDGDecimal = ",";
			if (strBDGDecimal != null && strBDGDecimal != System.Globalization.NumberFormatInfo.CurrentInfo.NumberDecimalSeparator)
			{
				string strToken = strFontSize.Replace(strBDGDecimal, System.Globalization.NumberFormatInfo.CurrentInfo.NumberDecimalSeparator);
				return new Font(strFontFamily, Convert.ToSingle(strToken), iFontStyle);
			}
			else
			{
				return new Font(strFontFamily, Convert.ToSingle(strFontSize), iFontStyle);
			}
		}

		static System.Drawing.FontStyle iFontStyle = 0;
		static string strFontFamily = null;
		static string strFontSize = null;
		private void ReadFontFromTokens(string[] tokens, string strDataValue, string suffix, ref DCSFontEx fontexOut)
		{
			string strToken;
			if (suffix != null)
			{
				if (tokens[0].EndsWith(suffix)) strToken = tokens[0].Substring(0, tokens[0].Length - suffix.Length);
				else return;
			}
			else strToken = tokens[0];

			switch (strToken)
			{
				case "FontFamily":
					strFontFamily = strDataValue;
					break;
				case "FontStyle":
					iFontStyle = (System.Drawing.FontStyle)Convert.ToInt32(tokens[1]);
					break;
				case "FontSize":
					strFontSize = strDataValue;
					fontexOut.Font = this.MakeFont(iFontStyle, strFontFamily, strFontSize);
					break;
				case "FontLineSpacing":
					fontexOut.FontLineSpacing = Convert.ToInt32(tokens[1]);
					break;
				case "ForeColor":
					fontexOut.ForeColor = this.ReadColor(tokens);
					break;
				case "SizeToFit":
					fontexOut.SizeToFit = Convert.ToBoolean(tokens[1]);
					break;
				case "Wrap":
					fontexOut.Wrap = Convert.ToBoolean(tokens[1]);
					break;
				case "Shadow":
					fontexOut.Shadow = Convert.ToBoolean(tokens[1]);
					break;
				case "ShadowColor":
					fontexOut.ShadowColor = this.ReadColor(tokens);
					break;
			}
		}

		// read the DigiCard Badge file (.Bdg) and populate this DCSDesign
		private bool ReadDesignFromTextFile(string strDesignFileIn)
		{
			bool bIsDCSFormat;
			string strVersion = "";

			string strDesignFile = DCSDesignDataAccess.ExpandDesignName(strDesignFileIn, true);
            if (strDesignFile == null)
            {
                DCSDEV.DCSMsg.Show(String.Format("Unable to find design file named '{0}'.", strDesignFileIn));
                return false;
            }

            string line = null;
            int linecount = 0;
            using (StreamReader sr = new StreamReader(strDesignFile))	// design files created by this program use UTF8 character set
            {
                try
                {
					// read first line - check version
					line = sr.ReadLine();
					bIsDCSFormat = (line.Substring(0, 7) == "Version");
					if (bIsDCSFormat)
					{

						DCSDesign thisDesign = this;
						DCSDesignSide designSide0 = (DCSDesignSide)this.m_designSides[0];
						DCSDesignSide designSide1 = null;

						DCSFontEx fontex0 = (DCSFontEx)thisDesign.m_arrayDocFontExs[0];
						DCSFontEx fontex1 = (DCSFontEx)thisDesign.m_arrayDocFontExs[1];
						DCSFontEx fontex2 = (DCSFontEx)thisDesign.m_arrayDocFontExs[2];

						bool designObjectStarted = false;
						DCSDesignObject myDesignObject = null;
						string[] tokens;
						char[] Separator = { ' ' };
						int i;

						int X = 0, Y = 0, height = 0, width = 0;
						string strDataValue;

						while (line != null)
						{
							++linecount;
							// split line apart on spaces and then recombine all segments but the first -syh this could be simplified!
							tokens = line.Split(Separator);
							strDataValue = String.Empty;
							for (i = 1; i < tokens.Length; ++i)
								if (i == 1)
									strDataValue = tokens[1];
								else
									strDataValue = strDataValue + " " + tokens[i];

							this.ReadFontFromTokens(tokens, strDataValue, "_Ref0", ref fontex0);
							this.ReadFontFromTokens(tokens, strDataValue, "_Ref1", ref fontex1);
							this.ReadFontFromTokens(tokens, strDataValue, "_Ref2", ref fontex2);
							this.ReadFontFromTokens(tokens, strDataValue, "_Prior", ref this.PriorFontEx);
							if (myDesignObject != null)
								this.ReadFontFromTokens(tokens, strDataValue, null, ref (DCSFontEx)myDesignObject.FontEx);

							switch (tokens[0])
							{
								// version should come first
								case "Version":
									strVersion = tokens[1];
									thisDesign.strVersion = tokens[1];
									break;

								case "PrinterTypeIndex":
									thisDesign.PrinterTypeIndex = Convert.ToInt32(tokens[1]);
									break;

								case "BoundsX":
									X = Convert.ToInt32(tokens[1]);
									break;

								case "BoundsY":
									Y = Convert.ToInt32(tokens[1]);
									break;

								case "BoundsHeight":
									height = Convert.ToInt32(tokens[1]);
									break;

								case "BoundsWidth":
									width = Convert.ToInt32(tokens[1]);
									if (width > 2000) width = 500;
									if (width < 50) width = 100;
									if (height > 2000) height = 500;
									if (height < 50) height = 100;
									thisDesign.Bounds = new Rectangle(X, Y, width, height);
									break;

								case "NumBadgeSides":
									thisDesign.NumBadgeSides = Convert.ToInt32(tokens[1]);
									if (thisDesign.NumBadgeSides >= 2)
									{
										// prepare second side array et al if double sided
										while (this.m_designSides.Count < thisDesign.NumBadgeSides)
										{
											this.m_designSides.Add(new DCSDEV.DCSDesign.DCSDesignSide());
										}
										if (this.m_designSides.Count >= 2) designSide1 = (DCSDesignSide)this.m_designSides[1];
									}
									break;

								case "BadgeSizeCode":
									thisDesign.BadgeSizeCode = tokens[1];
									break;
								case "HasChip":
									thisDesign.HasChip = Convert.ToBoolean(tokens[1]);
									break;
								case "FormulaChip":
									thisDesign.FormulaChip = strDataValue;
									break;
								case "HasMagStripe":
									thisDesign.HasMagStripe = Convert.ToBoolean(tokens[1]);
									break;
								case "SourceTrack1":
									thisDesign.SourceTrack1 = tokens[1];
									break;
								case "SourceTrack2":
									thisDesign.SourceTrack2 = tokens[1];
									break;
								case "SourceTrack3":
									thisDesign.SourceTrack3 = tokens[1];
									break;
								case "FormulaTrack1":
									thisDesign.FormulaTrack1 = strDataValue;
									break;
								case "FormulaTrack2":
									thisDesign.FormulaTrack2 = strDataValue;
									break;
								case "FormulaTrack3":
									thisDesign.FormulaTrack3 = strDataValue;
									break;
								case "GridSpacing":
									thisDesign.m_GridSpacingX = Convert.ToInt32(tokens[1]);
									thisDesign.m_GridSpacingY = Convert.ToInt32(tokens[2]);
									break;

								case "TabRange":
									thisDesign.m_TabRange = Convert.ToInt32(tokens[1]);
									break;
								case "TabStopsH":
									thisDesign.m_arrayTabStopsH = new ArrayList();
									for (int iH=1; iH<tokens.Length; iH++)
										m_arrayTabStopsH.Add(Convert.ToInt32(tokens[iH]));
									break;
								case "TabStopsV":
									thisDesign.m_arrayTabStopsV = new ArrayList();
									for (int iV=1; iV<tokens.Length; iV++)
										m_arrayTabStopsV.Add(Convert.ToInt32(tokens[iV]));
									break;


								// default settings
								case "PriorLabelFontIndex":
									thisDesign.PriorLabelFontIndex = Convert.ToInt32(tokens[1]);
									break;
								case "PriorLabelOffset":
									thisDesign.PriorLabelOffset = Convert.ToInt32(tokens[1]);
									break;
								case "PriorLabelOrientation":
									thisDesign.PriorLabelOrientation = (DCSDatatypes.DCSLabeledTextOrientations)Convert.ToInt32(tokens[1]);
									break;

								case "PriorBoundsHeight":
									thisDesign.PriorBoundsHeight = Convert.ToInt32(tokens[1]);
									break;
								case "PriorFontIndex":
									thisDesign.PriorFontIndex = Convert.ToInt32(tokens[1]);
									break;
								case "PriorAlignment":
									thisDesign.PriorAlignment = (DCSDEV.DCSDatatypes.Alignments)Convert.ToInt32(tokens[1]);
									break;
								case "PriorJustification":
									thisDesign.PriorJustification = (DCSDEV.DCSDatatypes.Justifications)Convert.ToInt32(tokens[1]);
									break;

								///////////////
								// back side //
								///////////////
								case "BackSideFillType":
									designSide1.SideFillType = (DCSDEV.DCSDatatypes.BackFillTypes)Convert.ToInt32(tokens[1]);
									break;

								case "BackSideBackColor":
									designSide1.SideBackColor = this.ReadColor(tokens);
									break;

								case "BackSideBackImageName":
									designSide1.SideBackImageName = strDataValue;
									if (designSide1.SideBackImage != null) designSide1.SideBackImage.Dispose();
									if (designSide1.SideFillType == DCSDEV.DCSDatatypes.BackFillTypes.FILL_IMAGE)
										designSide1.SideBackImage = DCSDesignDataAccess.GetImage(designSide1.SideBackImageName, false);	// quiet = false, report errors in GetImage
									break;

								case "BackSideBackColor2":
									designSide1.SideBackColor2 = this.ReadColor(tokens);
									break;

								case "BackSideBackGradientType":
									designSide1.SideBackGradientType = (System.Drawing.Drawing2D.LinearGradientMode)Convert.ToInt32(tokens[1]);
									break;

								case "BackSideColorChoice1":
									designSide1.SideColorChoice1 = this.ReadColor(tokens);
									break;
								case "BackSideColorChoice2":
									designSide1.SideColorChoice2 = this.ReadColor(tokens);
									break;
								case "BackSideColorChoice3":
									designSide1.SideColorChoice3 = this.ReadColor(tokens);
									break;
								case "BackSideColorCondition1":
									designSide1.SideColorCondition1 = strDataValue;
									break;
								case "BackSideColorCondition2":
									designSide1.SideColorCondition2 = strDataValue;
									break;

								case "BackIsLandscape":
									designSide1.SideIsLandscape = Convert.ToBoolean(tokens[1]);
									break;

								case "FrontSideFillType":
									designSide0.SideFillType = (DCSDEV.DCSDatatypes.BackFillTypes)Convert.ToInt32(tokens[1]);
									break;

								case "FrontSideBackColor":
									designSide0.SideBackColor = this.ReadColor(tokens);
									break;

								case "FrontSideBackImageName":
									designSide0.SideBackImageName = strDataValue;
									if (designSide0.SideBackImage != null) designSide0.SideBackImage.Dispose();
									if (designSide0.SideFillType == DCSDEV.DCSDatatypes.BackFillTypes.FILL_IMAGE)
										designSide0.SideBackImage = DCSDesignDataAccess.GetImage(designSide0.SideBackImageName, false);
									break;

								case "FrontSideBackColor2":
									designSide0.SideBackColor2 = this.ReadColor(tokens);
									break;

								case "FrontSideBackGradientType":
									designSide0.SideBackGradientType = (System.Drawing.Drawing2D.LinearGradientMode)Convert.ToInt32(tokens[1]);
									break;

								case "FrontSideColorChoice1":
									designSide0.SideColorChoice1 = this.ReadColor(tokens);
									break;
								case "FrontSideColorChoice2":
									designSide0.SideColorChoice2 = this.ReadColor(tokens);
									break;
								case "FrontSideColorChoice3":
									designSide0.SideColorChoice3 = this.ReadColor(tokens);
									break;
								case "FrontSideColorCondition1":
									designSide0.SideColorCondition1 = strDataValue;
									break;
								case "FrontSideColorCondition2":
									designSide0.SideColorCondition2 = strDataValue;
									break;

								case "FrontIsLandscape":
									designSide0.SideIsLandscape = Convert.ToBoolean(tokens[1]);
									break;

								case "//Object":
								case "//Field":		// obsolete
									// add any started designObject to the appropriate side array
									if (designObjectStarted)
									{
										((DCSDEV.DCSDesign.DCSDesignSide)m_designSides[myDesignObject.Side]).m_DCSDesignObjects.Add(myDesignObject);
										designObjectStarted = false;
									}

									// start a new designObject
									designObjectStarted = true;
									myDesignObject = new DCSDesignObject();
									break;

								/////////////////////////////////////
								// Object stuff                     //
								/////////////////////////////////////
								case "Side":
									myDesignObject.Side = Convert.ToInt32(tokens[1]);
									break;

								case "FieldType":		// obsolete
								case "ObjectType":
									myDesignObject.DCSDesignObjectType = (DCSDEV.DCSDatatypes.DCSDesignObjectTypes)Convert.ToInt32(tokens[1]);
									break;

								case "FieldTypeInstance":	// obsolete
								case "ObjectTypeInstance":
									myDesignObject.ObjectTypeInstance = Convert.ToInt32(tokens[1]);
									break;

								case "FBoundsX":
									X = Convert.ToInt32(tokens[1]);
									break;

								case "FBoundsY":
									Y = Convert.ToInt32(tokens[1]);
									break;

								case "FBoundsHeight":
									height = Convert.ToInt32(tokens[1]);
									break;

								case "FBoundsWidth":
									width = Convert.ToInt32(tokens[1]);
									myDesignObject.Bounds = new Rectangle(X, Y, width, height);
									break;

								case "RotateFlip":
									myDesignObject.RotateFlip = (RotateFlipType)Convert.ToInt32(tokens[1]);
									break;


								/////////////////////////////////////
								// Foreground stuff                //
								/////////////////////////////////////
								case "SourceType":
									myDesignObject.SourceType = (DCSDEV.DCSDatatypes.SourceTypes)Convert.ToInt32(tokens[1]);
									break;

								case "SourceName":
									myDesignObject.SourceName = strDataValue;
									break;

								case "Formula":
									myDesignObject.Formula = strDataValue;
									break;

								case "VisibleIf":
									myDesignObject.VisibleIf = Convert.ToBoolean(tokens[1]);
									break;

								case "VisibleIfCondition":
									myDesignObject.VisibleIfCondition = strDataValue;
									break;

								case "FieldImageName":		// obsolete
								case "ObjectImageName":
									// only enter image name if image object - otherwise old images persist
									if (myDesignObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ImageObj
									|| myDesignObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Portrait
									|| myDesignObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Signature
									|| myDesignObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Fingerprint)
									{
										myDesignObject.DesignObjectImageName = strDataValue;
										if (myDesignObject.DesignObjectImage != null) myDesignObject.DesignObjectImage.Dispose();
										//if (myDesignObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ImageObj)
										//	myDesignObject.DesignObjectImage = (Bitmap)DCSDesignDataAccess.GetImage(myDesignObject.DesignObjectImageName, false);
										if (myDesignObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ImageObj
											&& myDesignObject.SourceType == DCSDEV.DCSDatatypes.SourceTypes.StaticValue)
											myDesignObject.DesignObjectImage = (Bitmap)DCSDesignDataAccess.GetImage(myDesignObject.DesignObjectImageName, false);
									}
									break;

								case "Text":
									myDesignObject.Text = strDataValue;
									break;

								// LabeledText parameters
								case "LabelOn":
									myDesignObject.LabelOn = Convert.ToBoolean(tokens[1]);
									break;
								case "LabelText":
									myDesignObject.LabelText = strDataValue;
									break;
								case "LabelFontIndex":
									myDesignObject.LabelFontIndex = Convert.ToInt32(tokens[1]);
									break;
								case "LabelOrientation":
									myDesignObject.LabelOrientation = (DCSDatatypes.DCSLabeledTextOrientations)Convert.ToInt32(tokens[1]);
									break;
								case "LabelOffset":
									myDesignObject.LabelOffset = Convert.ToInt32(tokens[1]);
									break;

								case "FontIndex":
									myDesignObject.FontIndex = Convert.ToInt32(tokens[1]);
									break;

								case "CaseIndex":
									myDesignObject.CaseIndex = Convert.ToInt32(tokens[1]);
									break;

								case "TxtFormat":
									if (strDataValue == "default") myDesignObject.TxtFormat = "system default";
									else if (strDataValue == "") myDesignObject.TxtFormat = "app format";
									else myDesignObject.TxtFormat = strDataValue;
									break;

								case "Alignment":
									myDesignObject.Alignment = (DCSDEV.DCSDatatypes.Alignments)Convert.ToInt32(tokens[1]);
									break;

								case "Justification":
									myDesignObject.Justification = (DCSDEV.DCSDatatypes.Justifications)Convert.ToInt32(tokens[1]);
									break;

								/////////////////////////////////////
								// Background stuff                //
								/////////////////////////////////////
								case "BackFillType":
									myDesignObject.BackFillType = (DCSDEV.DCSDatatypes.BackFillTypes)Convert.ToInt32(tokens[1]);
									break;

								case "BackColor":
									myDesignObject.BackColor = this.ReadColor(tokens);
									break;

								case "BackColor2":
									myDesignObject.BackColor2 = this.ReadColor(tokens);
									break;

								case "BackGradientType":
									myDesignObject.BackGradientType = (System.Drawing.Drawing2D.LinearGradientMode)Convert.ToInt32(tokens[1]);
									break;

								case "ColorChoice1":
									myDesignObject.ColorChoice1 = this.ReadColor(tokens);
									break;
								case "ColorChoice2":
									myDesignObject.ColorChoice2 = this.ReadColor(tokens);
									break;
								case "ColorChoice3":
									myDesignObject.ColorChoice3 = this.ReadColor(tokens);
									break;
								case "ColorCondition1":
									myDesignObject.ColorCondition1 = strDataValue;
									break;
								case "ColorCondition2":
									myDesignObject.ColorCondition2 = strDataValue;
									break;

								case "Transparency":
									myDesignObject.Transparency = Convert.ToByte(tokens[1]);
									break;

								case "BackImageName":
									myDesignObject.BackImageName = strDataValue;
									if (myDesignObject.BackImage != null) myDesignObject.BackImage.Dispose();
									if (myDesignObject.BackFillType == DCSDEV.DCSDatatypes.BackFillTypes.FILL_IMAGE)
										myDesignObject.BackImage = (Bitmap)DCSDesignDataAccess.GetImage(myDesignObject.BackImageName, false);
									break;

								case "Scaling":
									myDesignObject.Scaling = (DCSDEV.DCSDatatypes.ScaleMode)Convert.ToInt16(tokens[1]);
									break;
								case "Framing":
									myDesignObject.Framing = (DCSDEV.DCSDatatypes.FramingMode)Convert.ToInt16(tokens[1]);
									break;

								/////////////////////////////////////
								// Process stuff                   //
								/////////////////////////////////////
								case "IfLockAspect":
									myDesignObject.IfLockAspect = Convert.ToBoolean(tokens[1]);
									break;

								case "GroupID":
									myDesignObject.GroupID = Convert.ToInt32(tokens[1]);
									break;

								case "Locked":
									myDesignObject.Locked = Convert.ToBoolean(tokens[1]);
									break;

								case "ZOrder":
									myDesignObject.ZOrder = Convert.ToInt32(tokens[1]);
									break;

								case "SpecialKPanel":
									myDesignObject.SpecialKPanel = Convert.ToInt32(tokens[1]);
									break;
								/////////////////////////////////////
								// Border stuff                    //
								/////////////////////////////////////
								case "LineColor":
									myDesignObject.LineColor = this.ReadColor(tokens);
									break;

								case "LineWidth":
									myDesignObject.LineWidth = Convert.ToInt32(tokens[1]);
									break;

								case "Radius":
									myDesignObject.Radius = Convert.ToInt32(tokens[1]);
									break;


								case "GrayScale":
									myDesignObject.GrayScale = Convert.ToBoolean(tokens[1]);
									break;

								case "BackDetectEnabled":
									myDesignObject.BackDetectEnabled = Convert.ToBoolean(tokens[1]);
									break;

								case "DetectThreshold":
									myDesignObject.ColorDetectThreshold = Convert.ToInt32(tokens[1]);
									break;

								case "FieldColorToDetect":		// obsolete
								case "ColorToDetect":
									myDesignObject.ColorToDetect = this.ReadColor(tokens);
									break;

								case "AutoKey":
									myDesignObject.AutoKey = Convert.ToBoolean(tokens[1]);
									break;
								case "PortraitAutoKey":
									myDesignObject.PortraitAutoKey = Convert.ToBoolean(tokens[1]);
									if (myDesignObject.PortraitAutoKey && (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.PortraitAutoKey, true)))									
									{
										myDesignObject.PortraitAutoKey = false;
										myDesignObject.AutoKey = true;
									}
									break;

								case "BarcodeCode":
									myDesignObject.BarcodeCode = Convert.ToInt16(tokens[1]);
									break;

								case "BarcodeShowText":
									myDesignObject.BarcodeShowText = Convert.ToBoolean(tokens[1]);
									break;

								case "IcaoType":
									myDesignObject.IcaoType = Convert.ToInt16(tokens[1]);
									break;
							}
							line = sr.ReadLine();
						}	// end of while read loop
						if (designObjectStarted)
						{
							if (myDesignObject.Side >= NumBadgeSides) myDesignObject.Side = 0;
							((DCSDEV.DCSDesign.DCSDesignSide)m_designSides[myDesignObject.Side]).m_DCSDesignObjects.Add(myDesignObject);
							designObjectStarted = false;
						}
					}
                }
                catch (Exception ex)
                {
                    if (sr != null) sr.Close();
                    DCSDEV.DCSMsg.Show(String.Format("ERROR encountered reading design '{0}' at line number: {1}:\r\n Text: {2}", strDesignFile, linecount, line), ex);
                    return false;
                }
                finally
                {
                    if (sr != null) sr.Close();
                }
            }
			if (bIsDCSFormat) return true;

			if (line.Substring(0, 8) == "[LAYOUT]")
			{
				if (DCSMsg.ShowYN(String.Format(" {0} \n\n appears to be an IFW format badge design.\n\nOK to try converting it?", strDesignFile)) == DialogResult.Yes)
				{
					// try reading file as an IFW format badge design
					bool ireturn = ReadIFWDesignFromFile(strDesignFile);
					if (ireturn)
					{
						this.m_isDirty = true;
						return true;
					}
				}
			}
			DCSDEV.DCSMsg.Show(String.Format("File '{0}' is not a valid ID Services badge design.", strDesignFile));
			return false;
		}

		private bool ReadIFWDesignFromFile(string strDesignFile)
		{
			int height = 0, width = 0;
			DCSDEV.ParameterStore ps;

			ps = new ParameterStore("LAYOUT", strDesignFile);
			height = ps.GetIntParameter("BadgeHeight", 0);
			width = ps.GetIntParameter("BadgeWidth", 0);
			if (height == 0 || width == 0) return false;
			this.Bounds = new Rectangle(0, 0, width, height);
			this.PrinterTypeIndex = 0;
			if (height >= 400 || width >= 400) this.PrinterTypeIndex = 1;
			this.NumBadgeSides = ps.GetIntParameter("NumSides", 0);
			if (this.NumBadgeSides >= 2)
			{
				// prepare second side array et al if double sided
				while (this.m_designSides.Count < this.NumBadgeSides)
				{
					this.m_designSides.Add(new DCSDEV.DCSDesign.DCSDesignSide());
				}
			}

			string strColor;
			int iNoImage = ps.GetIntParameter("IfNoImage", 1);
			if (iNoImage == 1)
			{
				((DCSDesignSide)this.m_designSides[0]).SideFillType = DCSDatatypes.BackFillTypes.FILL_COLOR;
				strColor = ps.GetStringParameter("BackgroundColor", "255,255,255");
				((DCSDesignSide)this.m_designSides[0]).SideBackColor = ParseIFWColor(strColor);
			}
			else
			{
				((DCSDesignSide)this.m_designSides[0]).SideFillType = DCSDatatypes.BackFillTypes.FILL_IMAGE;
				((DCSDesignSide)this.m_designSides[0]).SideBackImageName = ps.GetStringParameter("BackgroundImage", "");
			}
			bool bIfMag = false;
			if (ps.GetIntParameter("MagTrack1", 0) == 1)
			{
				bIfMag = true;
				this.SourceTrack1 = ps.GetStringParameter("Tk1FieldName", "");
			}
			if (ps.GetIntParameter("MagTrack2", 0) == 1)
			{
				bIfMag = true;
				this.SourceTrack2 = ps.GetStringParameter("Tk2FieldName", "");
			}
			if (ps.GetIntParameter("MagTrack3", 0) == 1)
			{
				bIfMag = true;
				this.SourceTrack3 = ps.GetStringParameter("Tk3FieldName", "");
			}
			if (bIfMag) this.HasMagStripe = true;

			string strType;
			DCSDEV.DCSDesign.DCSDesignObject myObj;
			int iJust;
			int iObject = 0;
			while (true)
			{
				ps = new ParameterStore("OBJECT" + iObject.ToString("000"), strDesignFile);
				strType = ps.GetStringParameter("Type", "");
				if (strType == "") break;

				iObject++;
				if (strType == "Portrait")
				{
					myObj = new DCSDesignObject(DCSDatatypes.DCSDesignObjectTypes.Portrait);
					myObj.DesignObjectImageName = ps.GetStringParameter("SampleValue", "");
				}
				else if (strType == "Signature")
				{
					myObj = new DCSDesignObject(DCSDatatypes.DCSDesignObjectTypes.Signature);
					myObj.DesignObjectImageName = ps.GetStringParameter("SampleValue", "");
				}
				else if (strType == "Fingerprint")
				{
					myObj = new DCSDesignObject(DCSDatatypes.DCSDesignObjectTypes.Fingerprint);
					myObj.DesignObjectImageName = ps.GetStringParameter("SampleValue", "");
				}
				else if (strType == "Static Text")
				{
					myObj = new DCSDesignObject(DCSDatatypes.DCSDesignObjectTypes.TextObj);
					myObj.SourceType = DCSDatatypes.SourceTypes.StaticValue;
					myObj.Text = ps.GetStringParameter("DataValue", "Static Text");
					this.ReadIFWFontParams(myObj, ps);
				}
				else if (strType == "Text")
				{
					myObj = new DCSDesignObject(DCSDatatypes.DCSDesignObjectTypes.TextObj);
					this.ReadIFWVariableDataSource(myObj, ps);
					this.ReadIFWFontParams(myObj, ps);
				}
				else if (strType == "Static Image")
				{
					myObj = new DCSDesignObject(DCSDatatypes.DCSDesignObjectTypes.ImageObj);
					myObj.DesignObjectImageName = ps.GetStringParameter("FileName", "");
				}
				else if (strType == "Image")
				{
					myObj = new DCSDesignObject(DCSDatatypes.DCSDesignObjectTypes.ImageObj);
					this.ReadIFWVariableDataSource(myObj, ps);
				}
				else if (strType == "Bar Code")
				{
					myObj = new DCSDesignObject(DCSDatatypes.DCSDesignObjectTypes.Barcode);
					this.ReadIFWVariableDataSource(myObj, ps);
				}
				else if (strType == "2D Bar Code")
				{
					myObj = new DCSDesignObject(DCSDatatypes.DCSDesignObjectTypes.Barcode2D);
					this.ReadIFWVariableDataSource(myObj, ps);
				}
				else if (strType == "Box")
				{
					myObj = new DCSDesignObject(DCSDatatypes.DCSDesignObjectTypes.GraphicBlock);
					this.ReadIFWVariableDataSource(myObj, ps);
				}

				else continue;

				myObj.Side = ps.GetIntParameter("Side", 0);
				myObj.Bounds.Height = ps.GetIntParameter("Height", 100);
				myObj.Bounds.Width = ps.GetIntParameter("Width", 100);
				myObj.Bounds.X = ps.GetIntParameter("LocX", 0);
				myObj.Bounds.Y = ps.GetIntParameter("LocY", 0);
				strColor = ps.GetStringParameter("BackgroundColor", "255,255,255");
				myObj.BackColor = ParseIFWColor(strColor);
				strColor = ps.GetStringParameter("ForeGroundColor", "0,0,0");
				myObj.FontEx.ForeColor = ParseIFWColor(strColor);
				if (ps.GetIntParameter("BackgroundDetect", 0) == 1)
				{
					myObj.BackDetectEnabled = true;
					string strKey;
					strKey = ps.GetStringParameter("KeyColor", "");
					if (strKey != "")
					{
						if (strKey == "auto") myObj.AutoKey = true;
						else myObj.ColorToDetect = this.ParseIFWColor(strKey);
						myObj.ColorDetectThreshold = ps.GetIntParameter("KeyRange", 23);
					}
				}
				if (ps.GetIntParameter("IsTransparent", 0) == 1) 
					myObj.BackFillType = DCSDatatypes.BackFillTypes.FILL_CLEAR;
				else
					myObj.BackFillType = DCSDatatypes.BackFillTypes.FILL_COLOR;
				iJust = ps.GetIntParameter("HorizAlign", 0);
				switch (iJust)
				{
					case 0:
						myObj.Justification = DCSDatatypes.Justifications.LEFT;
						break;
					case 1:
						myObj.Justification = DCSDatatypes.Justifications.CENTER;
						break;
					case 2:
						myObj.Justification = DCSDatatypes.Justifications.RIGHT;
						break;
				}
				iJust = ps.GetIntParameter("VertAlign", 0);
				switch (iJust)
				{
					case 0:
						myObj.Alignment = DCSDatatypes.Alignments.TOP;
						break;
					case 1:
						myObj.Alignment = DCSDatatypes.Alignments.MIDDLE;
						break;
					case 2:
						myObj.Alignment = DCSDatatypes.Alignments.BOTTOM;
						break;
				}
				int iRot;
				iRot = ps.GetIntParameter("Rotation", 0);
				if (iRot == 90)
					myObj.RotateFlip = RotateFlipType.Rotate90FlipNone;
				else if (iRot == 180)
					myObj.RotateFlip = RotateFlipType.Rotate180FlipNone;
				else if (iRot == 270)
					myObj.RotateFlip = RotateFlipType.Rotate90FlipNone;
				myObj.Transparency = ps.GetIntParameter("GhostTransparency", 0);

				((DCSDEV.DCSDesign.DCSDesignSide)this.m_designSides[myObj.Side]).m_DCSDesignObjects.Add(myObj);
			}
			return true;
		}
		private void ReadIFWVariableDataSource(DCSDEV.DCSDesign.DCSDesignObject myObj, DCSDEV.ParameterStore ps)
		{
			string strSource = ps.GetStringParameter("DBField", "");
			if (strSource != "")
			{
				if (strSource.Substring(0, 1) == "=")
				{
					myObj.SourceType = DCSDatatypes.SourceTypes.Formula;
					myObj.Formula = strSource.Substring(1);
				}
				else
				{
					myObj.SourceType = DCSDatatypes.SourceTypes.Database;
					myObj.SourceName = strSource;
				}
			}

			if (myObj.DCSDesignObjectType == DCSDatatypes.DCSDesignObjectTypes.ImageObj)
				myObj.DesignObjectImageName = ps.GetStringParameter("SampleValue", "");
			else
				myObj.Text = ps.GetStringParameter("SampleValue", "");
		}

		private void ReadIFWFontParams(DCSDEV.DCSDesign.DCSDesignObject myObj, DCSDEV.ParameterStore ps)
		{
			int iSize = Math.Abs(ps.GetIntParameter("TextSize", 10));
			int iStyle = ps.GetIntParameter("TextStyle", 0);
			string strName = ps.GetStringParameter("TextFont", "Arial");
			int iWeight = ps.GetIntParameter("TextWeight", 0);
			FontStyle fontStyle = FontStyle.Regular;
			if (iWeight > 400) fontStyle = fontStyle | FontStyle.Bold;
			if (iStyle > 0) fontStyle = fontStyle | FontStyle.Italic;
			myObj.FontEx.Font = new Font(strName, iSize, fontStyle);
			iSize =  (int)(((double)iSize * myObj.FontEx.Font.Size / myObj.FontEx.Font.Height) + 0.90);
			myObj.FontEx.Font = new Font(strName, iSize, fontStyle);
			
			myObj.FontEx.Wrap = (ps.GetIntParameter("WordWrap", 0) == 0) ? false : true;
			myObj.FontEx.SizeToFit = (ps.GetIntParameter("SizeToFit", 1) == 0) ? false : true;
		}

		private Color ParseIFWColor(string strIFWColor)
		{
            string[] tokens;
            char[] Separator = { ',' };
            tokens = strIFWColor.Split(Separator);

			return Color.FromArgb(
				255,
				Convert.ToByte(tokens[0]),
				Convert.ToByte(tokens[1]),
				Convert.ToByte(tokens[2]));
		}

		private void WriteFontExToStream(DCSFontEx fontex, string suffix, StreamWriter sw)
		{
			if (suffix == null) suffix = "";
			sw.WriteLine("FontFamily" + suffix + " " + fontex.Font.FontFamily.Name);
			sw.WriteLine("FontStyle" + suffix + " " + (int)fontex.Font.Style);
			sw.WriteLine("FontSize" + suffix + " " + fontex.Font.Size.ToString());
			sw.WriteLine("FontLineSpacing" + suffix + " " + fontex.FontLineSpacing);
			this.WriteColor(sw, "ForeColor" + suffix, fontex.ForeColor);
			sw.WriteLine("Wrap" + suffix + " " + fontex.Wrap.ToString());
			sw.WriteLine("SizeToFit" + suffix + " " + fontex.SizeToFit.ToString());
			sw.WriteLine("Shadow" + suffix + " " + fontex.Shadow.ToString());
			this.WriteColor(sw, "ShadowColor" + suffix, fontex.ShadowColor);
		}

		private bool WriteDesignToTextFile(string strDesignName)
		{
			string str;
			int i;
			string fileName = DCSDEV.DCSDesignDataAccess.ExpandDesignName(strDesignName, false);
			DCSDesign thisDesign = this;
			bool ireturn = true;
            StreamWriter sw = null;

            try
            {
                sw = new StreamWriter(fileName, false);
                // version should come first
                sw.WriteLine("Version " + thisDesign.strVersion);
                sw.WriteLine("PrinterTypeIndex " + thisDesign.PrinterTypeIndex.ToString());
                sw.WriteLine("BoundsX " + thisDesign.Bounds.X.ToString());
                sw.WriteLine("BoundsY " + thisDesign.Bounds.Y.ToString());
                sw.WriteLine("BoundsHeight " + thisDesign.Bounds.Height.ToString());
                sw.WriteLine("BoundsWidth " + thisDesign.Bounds.Width.ToString());
                sw.WriteLine("NumBadgeSides " + thisDesign.NumBadgeSides.ToString());
                sw.WriteLine("BadgeSizeCode " + thisDesign.BadgeSizeCode);
                sw.WriteLine("HasChip " + thisDesign.HasChip);
                sw.WriteLine("FormulaChip " + thisDesign.FormulaChip);
                sw.WriteLine("HasMagStripe " + thisDesign.HasMagStripe);
                sw.WriteLine("SourceTrack1 " + thisDesign.SourceTrack1);
                sw.WriteLine("SourceTrack2 " + thisDesign.SourceTrack2);
                sw.WriteLine("SourceTrack3 " + thisDesign.SourceTrack3);
                sw.WriteLine("FormulaTrack1 " + thisDesign.FormulaTrack1);
                sw.WriteLine("FormulaTrack2 " + thisDesign.FormulaTrack2);
                sw.WriteLine("FormulaTrack3 " + thisDesign.FormulaTrack3);

				sw.WriteLine("GridSpacing " + thisDesign.m_GridSpacingX.ToString() + " " + thisDesign.m_GridSpacingY.ToString());

				sw.WriteLine("TabRange " + thisDesign.m_TabRange.ToString());
				if (m_arrayTabStopsH != null && m_arrayTabStopsH.Count > 0)
				{
					str = "TabStopsH";
					foreach (int coordH in m_arrayTabStopsH)
						str = str + " " + coordH.ToString();
					sw.WriteLine(str);
				}
				if (m_arrayTabStopsV != null && m_arrayTabStopsV.Count > 0)
				{
					str = "TabStopsV";
					foreach (int coordV in m_arrayTabStopsV)
						str = str + " " + coordV.ToString();
					sw.WriteLine(str);
				}
				// Labeled Text object properties
				i=0;
				foreach (DCSFontEx fontex in this.m_arrayDocFontExs)
				{
					this.WriteFontExToStream(fontex, "_Ref" + i.ToString(), sw);
					i++;
				}
				// default property values
				sw.WriteLine("PriorLabelFontIndex " + thisDesign.PriorLabelFontIndex);
				sw.WriteLine("PriorLabelOrientation " + (int)(thisDesign.PriorLabelOrientation));
				sw.WriteLine("PriorLabelOffset " + thisDesign.PriorLabelOffset);

				sw.WriteLine("PriorBoundsHeight " + thisDesign.PriorBoundsHeight);
				sw.WriteLine("PriorFontIndex " + thisDesign.PriorFontIndex);
				this.WriteFontExToStream(this.PriorFontEx, "_Prior", sw);
				sw.WriteLine("PriorAlignment " + (int)thisDesign.PriorAlignment);
				sw.WriteLine("PriorJustification " + (int)thisDesign.PriorJustification);
				
                // Front side stuff
                DCSDesignSide designSide1 = (DCSDesignSide)this.m_designSides[0];
                sw.WriteLine("FrontSideFillType " + (int)designSide1.SideFillType);
                if (designSide1.SideBackImageName != null)
					sw.WriteLine("FrontSideBackImageName " + designSide1.SideBackImageName);

                this.WriteColor(sw, "FrontSideBackColor", designSide1.SideBackColor);
                this.WriteColor(sw, "FrontSideBackColor2", designSide1.SideBackColor2);
                sw.WriteLine("FrontSideBackGradientType " + (int)designSide1.SideBackGradientType);

				this.WriteColor(sw, "FrontSideColorChoice1", designSide1.SideColorChoice1);
				this.WriteColor(sw, "FrontSideColorChoice2", designSide1.SideColorChoice2);
				this.WriteColor(sw, "FrontSideColorChoice3", designSide1.SideColorChoice3);
				sw.WriteLine("FrontSideColorCondition1 " + designSide1.SideColorCondition1);
				sw.WriteLine("FrontSideColorCondition2 " + designSide1.SideColorCondition2);
				sw.WriteLine("FrontIsLandscape " + designSide1.SideIsLandscape);

                // Backside 
                if (thisDesign.NumBadgeSides >= 2)
                {
                    DCSDesignSide designSide2 = (DCSDesignSide)this.m_designSides[1];
                    sw.WriteLine("BackSideFillType " + (int)designSide2.SideFillType);
                    if (designSide2.SideBackImageName != null)
                        sw.WriteLine("BackSideBackImageName " + designSide2.SideBackImageName);

                    this.WriteColor(sw, "BackSideBackColor", designSide2.SideBackColor);
                    this.WriteColor(sw, "BackSideBackColor2", designSide2.SideBackColor2);
                    sw.WriteLine("BackSideBackGradientType " + (int)designSide2.SideBackGradientType);

					this.WriteColor(sw, "BackSideColorChoice1", designSide2.SideColorChoice1);
					this.WriteColor(sw, "BackSideColorChoice2", designSide2.SideColorChoice2);
					this.WriteColor(sw, "BackSideColorChoice3", designSide2.SideColorChoice3);
					sw.WriteLine("BackSideColorCondition1 " + designSide2.SideColorCondition1);
					sw.WriteLine("BackSideColorCondition2 " + designSide2.SideColorCondition2);
					sw.WriteLine("BackIsLandscape " + designSide2.SideIsLandscape);
                }
                int iSide = 0;
                i = 1;
                foreach (DCSDesignSide designSide in m_designSides)
                {
                    foreach (DCSDesignObject fld in designSide.m_DCSDesignObjects)
                    {
                        sw.WriteLine("//Object " + i.ToString());	// was Field
                        ++i;
                        sw.WriteLine("Side " + iSide);
                        sw.WriteLine("ObjectType " + (int)fld.DCSDesignObjectType);	// was FieldType
                        sw.WriteLine("ObjectTypeInstance " + (int)fld.ObjectTypeInstance);	// was FieldTypeInstance
                        sw.WriteLine("FBoundsX " + fld.Bounds.X);
                        sw.WriteLine("FBoundsY " + fld.Bounds.Y);
                        sw.WriteLine("FBoundsHeight " + fld.Bounds.Height);
                        sw.WriteLine("FBoundsWidth " + fld.Bounds.Width);
                        sw.WriteLine("RotateFlip " + (int)fld.RotateFlip);

                        // foreground
                        sw.WriteLine("SourceType " + (int)fld.SourceType);
                        sw.WriteLine("SourceName " + fld.SourceName);
                        sw.WriteLine("Formula " + fld.Formula);
						sw.WriteLine("VisibleIf " + fld.VisibleIf);
						sw.WriteLine("VisibleIfCondition " + fld.VisibleIfCondition);
						sw.WriteLine("ObjectImageName " + fld.DesignObjectImageName);	// was FieldImageName
                        sw.WriteLine("Text " + fld.Text);

						// LabeledText parameters
						sw.WriteLine("LabelOn " + fld.LabelOn);
						sw.WriteLine("LabelText " + fld.LabelText);
						sw.WriteLine("LabelFontIndex " + fld.LabelFontIndex);
						sw.WriteLine("LabelOrientation " + (int)(fld.LabelOrientation));
						sw.WriteLine("LabelOffset " + fld.LabelOffset);

						sw.WriteLine("FontIndex " + fld.FontIndex.ToString());
						this.WriteFontExToStream(fld.FontEx, null, sw);

						sw.WriteLine("CaseIndex " + (int)fld.CaseIndex);
                        sw.WriteLine("TxtFormat " + fld.TxtFormat);
						sw.WriteLine("Alignment " + (int)fld.Alignment);
                        sw.WriteLine("Justification " + (int)fld.Justification);

                        // background
                        sw.WriteLine("BackFillType " + (int)fld.BackFillType);
                        this.WriteColor(sw, "BackColor", fld.BackColor);
                        this.WriteColor(sw, "BackColor2", fld.BackColor2);
                        sw.WriteLine("BackGradientType " + (int)fld.BackGradientType);
						this.WriteColor(sw, "ColorChoice1", fld.ColorChoice1);
						this.WriteColor(sw, "ColorChoice2", fld.ColorChoice2);
						this.WriteColor(sw, "ColorChoice3", fld.ColorChoice3);
						sw.WriteLine("ColorCondition1 " + fld.ColorCondition1);
						sw.WriteLine("ColorCondition2 " + fld.ColorCondition2);
						sw.WriteLine("Transparency " + fld.Transparency);
                        sw.WriteLine("BackImageName " + fld.BackImageName);
                        sw.WriteLine("Scaling " + (int)fld.Scaling);
                        sw.WriteLine("Framing " + (int)fld.Framing);

                        // process
                        sw.WriteLine("IfLockAspect " + fld.IfLockAspect);
                        sw.WriteLine("GroupID " + fld.GroupID);
                        sw.WriteLine("Locked " + fld.Locked);
                        sw.WriteLine("ZOrder " + fld.ZOrder);
                        sw.WriteLine("SpecialKPanel " + fld.SpecialKPanel);

                        // border
                        this.WriteColor(sw, "LineColor", fld.LineColor);
                        sw.WriteLine("LineWidth " + fld.LineWidth);
                        sw.WriteLine("Radius " + fld.Radius);

                        sw.WriteLine("GrayScale " + fld.GrayScale);
                        sw.WriteLine("BackDetectEnabled " + fld.BackDetectEnabled);
                        sw.WriteLine("DetectThreshold " + fld.ColorDetectThreshold);
                        this.WriteColor(sw, "ColorToDetect", fld.ColorToDetect);

                        sw.WriteLine("AutoKey " + fld.AutoKey);
						sw.WriteLine("PortraitAutoKey " + fld.PortraitAutoKey);
						sw.WriteLine("BarcodeCode " + fld.BarcodeCode);
                        sw.WriteLine("BarcodeShowText " + fld.BarcodeShowText);
                        sw.WriteLine("IcaoType " + fld.IcaoType);
                    }
                    iSide++;
                }
                sw.Close();
            }
            catch (Exception ex)
            {
                DCSDEV.DCSMsg.Show("ERROR: writing document design file.", ex);
                ireturn = false;
            }
            finally
            {
                sw.Dispose();
            }
			return ireturn;
		}
		private Color ReadColor(string [] tokens)
		{
			return Color.FromArgb(
                255,
				Convert.ToByte(tokens[2]),
				Convert.ToByte(tokens[3]),
				Convert.ToByte(tokens[4]));
		}
		private void WriteColor(StreamWriter sw, string name, Color clr)
		{
			sw.Write(name);
			sw.Write(" " + clr.A);
			sw.Write(" " + clr.R);
			sw.Write(" " + clr.G);
			sw.Write(" " + clr.B);
			sw.WriteLine("");
		}

		public bool RipSideToGDI(int iSide, Graphics gr, double dScale, DCSDesign.RipMode eRipMode, bool bDoBack, bool bDoFore, bool bDoText)
		{
			// get badge design for the current side or page
			DCSDEV.DCSDesign.DCSDesignSide designSide;
			designSide = (DCSDEV.DCSDesign.DCSDesignSide)this.m_designSides[iSide];

			Rectangle rectDesign = Rectangle.Empty;
			rectDesign.Size = this.Bounds.Size; 
			rectDesign = DCSMath.RectTimesDouble(rectDesign, dScale);

			// if back side orientation is not same as front swap dimensions
			if (iSide != 0)
			{
				DCSDesignSide designSide0 = (DCSDesignSide)this.m_designSides[0];
				DCSDesignSide designSide1 = (DCSDesignSide)this.m_designSides[1];
				if (designSide0.SideIsLandscape != designSide1.SideIsLandscape) DCSMath.SwapWandH(ref rectDesign);  
			}

			/*******************************************************************
			// if (bAntialiasing)
				gr.CompositingQuality = CompositingQuality.HighQuality;
				gr.SmoothingMode = SmoothingMode.HighQuality;
				gr.InterpolationMode = InterpolationMode.HighQualityBicubic;
				gr.PixelOffsetMode = PixelOffsetMode.HighQuality;
				gr.TextRenderingHint = TextRenderingHint.ClearTypeGridFit;
			******************************************************************/
		
			gr.CompositingQuality = CompositingQuality.AssumeLinear;
			gr.SmoothingMode = SmoothingMode.None;
			gr.InterpolationMode = InterpolationMode.Low;
			gr.PixelOffsetMode = PixelOffsetMode.HighSpeed;
			gr.TextRenderingHint = TextRenderingHint.SystemDefault;

			gr.CompositingMode = CompositingMode.SourceOver;

			if (eRipMode == RipMode.RIPMODE_LAYOUT)
			{
				if (m_LayoutBadgeData == null)
				{
                    m_LayoutBadgeData = new DCSDEV.DCSDesign.DCSBadgeDataset();
					m_LayoutBadgeData.m_strPortraitName = "test";
					m_LayoutBadgeData.m_strSignatureName = "test";
					m_LayoutBadgeData.m_strFingerprintName = "test";
					m_LayoutBadgeData.m_strDocumentName = "document";
				}
			}

            if (eRipMode == RipMode.RIPMODE_PRINT_KIMAGE || eRipMode == RipMode.RIPMODE_PRINT_KTEXT) bDoBack = false;
			if (bDoBack)
			{
				try
				{
					switch (designSide.SideFillType)
					{
						default:
						case DCSDEV.DCSDatatypes.BackFillTypes.FILL_CLEAR:
							gr.FillRectangle(new SolidBrush(Color.White), rectDesign);
							break;
						case DCSDEV.DCSDatatypes.BackFillTypes.FILL_COLOR:
							gr.FillRectangle(new SolidBrush(designSide.SideBackColor), rectDesign);
							break;
						case DCSDEV.DCSDatatypes.BackFillTypes.FILL_IMAGE:
							if (designSide.SideBackImage == null
								&& designSide.SideBackImageName != null 
								&& designSide.SideBackImageName.Length != 0)
							{
								designSide.SideBackImage = DCSDEV.DCSDesignDataAccess.GetImage(designSide.SideBackImageName, true);
							}
							if (designSide.SideBackImage != null)
								gr.DrawImage(designSide.SideBackImage, rectDesign, 0, 0, designSide.SideBackImage.Width, designSide.SideBackImage.Height, GraphicsUnit.Pixel);
							else
							{
								gr.FillRectangle(new SolidBrush(Color.White), rectDesign);
							}
							break;
						case DCSDEV.DCSDatatypes.BackFillTypes.FILL_GRADIENT:
							System.Drawing.Drawing2D.LinearGradientMode lgm = designSide.SideBackGradientType;
							System.Drawing.Drawing2D.LinearGradientBrush lgbrush = new System.Drawing.Drawing2D.LinearGradientBrush(
								rectDesign, designSide.SideBackColor, designSide.SideBackColor2, lgm);
							gr.FillRectangle(lgbrush, rectDesign);
							break;
						case DCSDEV.DCSDatatypes.BackFillTypes.CONDITIONAL_COLOR:
							gr.FillRectangle(new SolidBrush(designSide.SideColorEval), rectDesign);
							break;
					}
				}
				catch (Exception ex)
				{
					DCSDEV.DCSMsg.Show("ERROR drawing badge background." + Environment.NewLine + Environment.NewLine + ex.Message);
				}
			}
	
			///////////////////////////////////////////////
			// Pass One - Images                         //
			///////////////////////////////////////////////
			if (bDoFore)
			{
				foreach (DCSDesignObject designObject in designSide.m_DCSDesignObjects)
				{
                    // skip those wrong for this print mode setting
                    if (eRipMode == RipMode.RIPMODE_PRINT_KIMAGE && designObject.SpecialKPanel != 1) continue;
                    if (eRipMode == RipMode.RIPMODE_PRINT_NONK && designObject.SpecialKPanel != 0) continue;
                
                    // determine if it is an object type for this pass
                    bool bIsImage = false;
                    bool bIsGraphic = false;
                    switch (designObject.DCSDesignObjectType)
                    {
                        case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ImageObj:
                        case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Portrait:
                        case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Signature:
                        case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Fingerprint:
                            bIsImage = true;
                            break;
                        case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.GraphicBlock:
                            bIsGraphic = true;
                            break;
                    }
                    if ((!bIsImage) && (!bIsGraphic)) continue;
					
					// test for a visible if.... condition
					if (eRipMode != RipMode.RIPMODE_LAYOUT && designObject.VisibleIf && !designObject.VisibleIfEval) continue;

					Application.DoEvents();
                    DCSDEV.DCSDesign.DCSDesignObjectGraphics imageRip = null;
                    try
                    {
                        imageRip = new DCSDesignObjectGraphics(gr, designObject, dScale);
						if (bIsImage)
						{
							imageRip.DrawObjectBackground();
							imageRip.ProcessImageObject(eRipMode);
						}
						else if (bIsGraphic)
							imageRip.DrawObjectBackground();

                        if (eRipMode == RipMode.RIPMODE_LAYOUT)
                        {
                            // draw box around object
                            System.Drawing.Pen pen = new Pen(System.Drawing.Color.DarkGray, 1);
                            gr.DrawRectangle(pen, DCSMath.RectTimesDouble(designObject.Bounds, dScale));
                        }
                    }
                    catch (Exception ex)
                    {
                        DCSDEV.DCSMsg.Show("ERROR drawing image object.", ex);
                    }
                    finally
                    {
                        imageRip.Dispose();
                        imageRip = null;
                    }
				}
			}
			///////////////////////////////////////////
			// Pass 2: Rasterize text tokens
			///////////////////////////////////////////
			if (bDoText)
			{
                foreach (DCSDesignObject designObject in designSide.m_DCSDesignObjects)
                {
                    if (eRipMode == RipMode.RIPMODE_PRINT_KTEXT && designObject.SpecialKPanel != 1) continue;

                    bool bIsText = false;
                    switch (designObject.DCSDesignObjectType)
                    {
                        case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ICAOMRZ:
                        case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.TextObj:
						case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode2D:
						case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode:
							bIsText = true;
                            break;
                    }
                    if (!bIsText) continue;

					// test for a visible if.... condition
					if (eRipMode != RipMode.RIPMODE_LAYOUT && designObject.VisibleIf && !designObject.VisibleIfEval) continue;

					Application.DoEvents();
					int AdjLineWidth = designObject.LineWidth;
					Rectangle rectDesignObjectAdjusted = Rectangle.Inflate(designObject.Bounds, -AdjLineWidth, -AdjLineWidth);
					Rectangle rectObjectAdjustedScaled = DCSMath.RectTimesDouble(rectDesignObjectAdjusted, dScale);
					string strToRip = designObject.Text;
					DCSDEV.DCSDesign.DCSDesignObjectGraphics imageRip = null;		// text type objects use imageRip for drawing background and/or frame outline

                    try
                    {
                        imageRip = new DCSDesignObjectGraphics(gr, designObject, dScale);
                        switch (designObject.DCSDesignObjectType)
                        {
                            case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ICAOMRZ:
                                DCSIcaoBuilder icaoBuilder = new DCSIcaoBuilder(designObject);
                                icaoBuilder.BuildMRZ();
								strToRip = "=;" + icaoBuilder.ICAO_Line1 + ";" + icaoBuilder.ICAO_Line2;
                                if (icaoBuilder.ICAO_Line3.Length > 0) strToRip = strToRip + ";" + icaoBuilder.ICAO_Line3;
                                if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.IcaoMRZ, false))
                                    strToRip = "Unlicensed ICAOMRZ";

								this.RipTextObject(designObject, designObject.FontEx, gr, dScale, eRipMode, strToRip, imageRip, rectObjectAdjustedScaled);
                                break;
                            case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.TextObj:
								// see if a doc default font is used
								DCSDEV.DCSDesign.DCSFontEx objectFontEx = designObject.FontEx;
								DCSDEV.DCSDesign.DCSFontEx labelFontEx = null;
								if (designObject.FontIndex != -1) objectFontEx = (DCSDEV.DCSDesign.DCSFontEx)this.m_arrayDocFontExs[designObject.FontIndex];

							    switch (designObject.CaseIndex)
                                {
                                    default:
                                    case 0:			// normal mixed
                                        break;
                                    case 1:
                                        strToRip = strToRip.ToUpper();
                                        break;
                                    case 2:
                                        strToRip = strToRip.ToLower();
                                        break;
                                }
								if (designObject.TxtFormat != null && designObject.TxtFormat != "" && designObject.TxtFormat != "app format")
                                {
									// change string to rip if it is a date format and TxtFormat != null
                                    if (DCSDEV.DCSServerStuff.IsStringDateTime(strToRip, m_AppDateFormat))
                                    {
                                        DateTime dt;
                                        try
                                        {
                                            dt = DCSDEV.DCSServerStuff.String2DateTime(strToRip, m_AppDateFormat);
                                            if (designObject.TxtFormat == "system default")
                                                strToRip = dt.ToString();
                                            else if (designObject.TxtFormat.Substring(0, 4) != "ICAO")
                                                strToRip = dt.ToString(designObject.TxtFormat);
                                            else
                                            {
                                                int iLang = 2;
                                                string[,] strMonths =	
											{	// SPA			FRA			ENG
												{"ENE/JAN",		"JAN/JAN",	"JAN/JAN"},
												{"FEB/FEB",		"FEV/FEB",	"FEB/FEV"},
												{"MAR.MAR",		"MARS/MAR",	"MAR/MARS"},
												{"ABR/APR",		"AVR/APR",	"APR/AVR"},
												{"MAYO/MAY",	"MAI/MAY",	"MAY/MAI"},
												{"JUN/JUN",		"JUIN/JUN",	"JUN/JUIN"},
												{"JUL/JUL",		"JUIL/JUL",	"JUL/JUIL"},
												{"AGO/AUG",		"AOUT/AUG",	"AUG/AOUT"},
												{"SEPT/SEP",	"SEPT/SEP",	"SEP/SEPT"},
												{"OCT/OCT",		"OCT/OCT",	"OCT/OCT"},
												{"NOV/NOV",		"NOV/NOV",	"NOV/NOV"},
												{"DIC/DEC",		"DEC/DEC",	"DEC/DEC"}
											};
                                                if (designObject.TxtFormat.Substring(5) == "FRA") iLang = 1;
                                                else if (designObject.TxtFormat.Substring(5) == "SPA") iLang = 0;
                                                else iLang = 2;	// ENG english

                                                int day = dt.Day;
                                                int mon = dt.Month - 1;
                                                int year = dt.Year;
                                                strToRip = day.ToString() + " " + strMonths[mon, iLang] + " " + year.ToString();
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            DCSMsg.Show(ex);
                                        }
                                    }
                                }
								if (!designObject.LabelOn)
								{
									// non LabeledText case
									this.RipTextObject(designObject, objectFontEx, gr, dScale, eRipMode, strToRip, imageRip, rectObjectAdjustedScaled);
								}
								else
								{
									// LabeledText 

									// adjust text rectangle by label offset 
									int OffsetScaled = DCSDEV.DCSMath.TimesDouble(designObject.LabelOffset, dScale);
									Rectangle TextRectScaled = rectObjectAdjustedScaled;
									switch (designObject.LabelOrientation)
									{
										case DCSDatatypes.DCSLabeledTextOrientations.LEFT:
											TextRectScaled.X = TextRectScaled.X + OffsetScaled;
											TextRectScaled.Width = TextRectScaled.Width - OffsetScaled;
											break;
										case DCSDatatypes.DCSLabeledTextOrientations.RIGHT:
											TextRectScaled.Width = TextRectScaled.Width - OffsetScaled;
											break;
										case DCSDatatypes.DCSLabeledTextOrientations.TOP:
											TextRectScaled.Y = TextRectScaled.Y + OffsetScaled;
											TextRectScaled.Height = TextRectScaled.Height - OffsetScaled;
											break;
										case DCSDatatypes.DCSLabeledTextOrientations.BOTTOM:
											TextRectScaled.Height = TextRectScaled.Height - OffsetScaled;
											break;
									}
									this.RipTextObject(designObject, objectFontEx, gr, dScale, eRipMode, strToRip, imageRip, TextRectScaled);

									DCSDesignObject objectLabel = new DCSDesignObject(DCSDatatypes.DCSDesignObjectTypes.TextObj);
									objectLabel.Text = designObject.LabelText;
									Rectangle rectLabelScaled = rectObjectAdjustedScaled;
									strToRip = objectLabel.Text;

									objectLabel.BackFillType = DCSDatatypes.BackFillTypes.FILL_CLEAR;
									imageRip = new DCSDesignObjectGraphics(gr, objectLabel, dScale);

									switch (designObject.LabelOrientation)
									{
										case DCSDatatypes.DCSLabeledTextOrientations.LEFT:
											objectLabel.Justification = DCSDatatypes.Justifications.RIGHT;
											objectLabel.Alignment = designObject.Alignment;
											rectLabelScaled.Width = OffsetScaled;
											break;
										case DCSDatatypes.DCSLabeledTextOrientations.RIGHT:
											objectLabel.Alignment = designObject.Alignment;
											objectLabel.Justification = DCSDatatypes.Justifications.LEFT;
											rectLabelScaled.X = rectLabelScaled.Right - OffsetScaled;
											rectLabelScaled.Width = OffsetScaled;
											break;
										case DCSDatatypes.DCSLabeledTextOrientations.TOP:
											objectLabel.Alignment = DCSDatatypes.Alignments.TOP;
											objectLabel.Justification = designObject.Justification;
											rectLabelScaled.Height = OffsetScaled;
											break;
										case DCSDatatypes.DCSLabeledTextOrientations.BOTTOM:
											objectLabel.Alignment = DCSDatatypes.Alignments.BOTTOM;
											objectLabel.Justification = designObject.Justification;
											rectLabelScaled.Y = rectLabelScaled.Y + OffsetScaled;
											rectLabelScaled.Height = rectLabelScaled.Height - OffsetScaled;
											break;
									}
									// label text always uses one of the document default fonts
									labelFontEx = (DCSDEV.DCSDesign.DCSFontEx)this.m_arrayDocFontExs[designObject.LabelFontIndex];
									this.RipTextObject(objectLabel, labelFontEx, gr, dScale, eRipMode, strToRip, imageRip, rectLabelScaled);
								}
                                break;
							case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode2D:
                                if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.TwoDBarcodes, false))
                                    strToRip = "ERROR: Unlicensed TwoDBarcode";
                                this.Rip2DBarcode(designObject, gr, dScale, eRipMode, strToRip, imageRip, rectObjectAdjustedScaled);
                                break;
                            case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode:
                                this.RipBarcodeObject(designObject, gr, dScale, eRipMode, strToRip, imageRip, rectObjectAdjustedScaled);
                                break;
							default:
                                break;
                        }
                        if (eRipMode == RipMode.RIPMODE_LAYOUT)
                        {
                            // draw box around object
                            System.Drawing.Pen pen = new Pen(System.Drawing.Color.DarkGray, 1);
                            gr.DrawRectangle(pen, DCSMath.RectTimesDouble(designObject.Bounds, dScale));
                        }
                    }
                    catch (Exception ex)
                    {
                        DCSDEV.DCSMsg.Show("ERROR drawing text object.", ex);
                    }
                    finally
                    {
                        imageRip.Dispose();
                        imageRip = null;
                    }
				}
			}
			if (eRipMode == RipMode.RIPMODE_LAYOUT)
			{
                // draw box around badge design
				System.Drawing.Pen pen = new Pen(System.Drawing.Color.DarkGray, 1);
				Rectangle rect = rectDesign;
				rect.Width--;
				rect.Height--;
				gr.DrawRectangle(pen, rect);
			}
			return true;
		}

		private void Rip2DBarcode(DCSDesignObject designObject, Graphics gr, double dScale, DCSDesign.RipMode eRipMode, string strToRip, DCSDEV.DCSDesign.DCSDesignObjectGraphics imageRip, Rectangle rectObjectAdjustedScaled)
		{
			// syh the only implemented 2d Barcode type is PDF417
			if (eRipMode == RipMode.RIPMODE_LAYOUT)
			{
				if (strToRip == null || strToRip == "")	// if no sample data show source
				{
					if (designObject.SourceType == DCSDEV.DCSDatatypes.SourceTypes.Database) 
						strToRip = "*" + designObject.SourceName;
					else if (designObject.SourceType == DCSDEV.DCSDatatypes.SourceTypes.Formula)
					{
						strToRip = DCSDEV.DCSDesign.DCSFormula.FormulaEval(designObject.Formula, m_LayoutBadgeData);
						if (strToRip == "" || strToRip == designObject.Formula)
							strToRip = "=" + designObject.Formula;
					}
				}
			}
            // always draw background and outline except when doing Kpanel text
            if (eRipMode != RipMode.RIPMODE_PRINT_KTEXT)
            {
                imageRip.DrawObjectBackground();
                imageRip.DrawOutline();
            }
            // then quit if doing the non k background of a k panel object
            if (designObject.SpecialKPanel != 0 && eRipMode == RipMode.RIPMODE_PRINT_NONK) return;

			if (strToRip != null && strToRip != "" && !strToRip.StartsWith("ERROR"))
			{
				try
				{
					// old way // Point pointTranslateTransform = Point.Empty;
					// old way // float floatRotateTransform = 0.0f;
					Size sizeBounds = rectObjectAdjustedScaled.Size;
					switch(designObject.RotateFlip)
					{
						default:
						case System.Drawing.RotateFlipType.RotateNoneFlipNone:
						case System.Drawing.RotateFlipType.Rotate180FlipNone:
							break;
						case System.Drawing.RotateFlipType.Rotate90FlipNone:
						case System.Drawing.RotateFlipType.Rotate270FlipNone:
							// old way // floatRotateTransform = 90.0f;
							sizeBounds.Width = rectObjectAdjustedScaled.Height;
							sizeBounds.Height = rectObjectAdjustedScaled.Width;
							// old way // pointTranslateTransform.X = rectObjectAdjustedScaled.X + rectObjectAdjustedScaled.Width;
							// old way // pointTranslateTransform.Y = rectObjectAdjustedScaled.Y;
							break;
					}

                    DCSDEV.PrintProperties.PrinterTypeDatum bcDatum = new DCSDEV.PrintProperties.PrinterTypeDatum("");
					bcDatum.LoadPrinterTypeData(this.PrinterTypeIndex);
					double dPrinterScale = (double)(bcDatum.m_2DBarPrintResolution) / 100.0;

					Size sizePrintTarget = DCSMath.TimesDouble(sizeBounds, dPrinterScale/dScale);

					// test ==================================================
					// 1100 bytes or 1800 ascii chars
					// row times columns < 925
					int nMW = Math.Max(1, (int)((double)bcDatum.m_2DBarFeatureW * dPrinterScale / 3.0));
					int nMH = Math.Max(1, (int)((double)bcDatum.m_2DBarFeatureH * dPrinterScale / 3.0));

					DCSSDK.DCSBarcodeIF.SaxIF sax;
					Bitmap bm;
                    sax = new DCSSDK.DCSBarcodeIF.SaxIF();

                    /*******************************  binary mode **********************************
                    if ((System.Windows.Forms.Control.ModifierKeys & Keys.Shift) == Keys.Shift)
                        bitmap = sax.RipPDF417Ex(strToRip, sizePrintTarget, dPrinterScale, nMW, nMH);   // experimental binary version
                    else
                    *********************************************************************************/
                    bm = sax.RipPDF417(strToRip, sizePrintTarget, dPrinterScale, nMW, nMH);
                    
                    if (bm == null) return;

                    DCSDesignObject designObject2 = designObject.CloneObject(false);
					designObject2.DesignObjectImage = bm;
					if (designObject.BackFillType == DCSDatatypes.BackFillTypes.FILL_COLOR && designObject.BackColor == Color.White)
						designObject2.BackDetectEnabled = false;
					else
						designObject2.BackDetectEnabled = true;
					//designObject2.BackDetectEnabled = true;
					designObject2.BackFillType = DCSDEV.DCSDatatypes.BackFillTypes.FILL_CLEAR;
					designObject2.ColorToDetect = System.Drawing.Color.White;
					DCSDEV.DCSDesign.DCSDesignObjectGraphics imageRip2 = new DCSDesignObjectGraphics(gr, designObject2, dScale);
					imageRip2.ProcessImageObject(eRipMode);
					imageRip2.Dispose();
					designObject2.Dispose();
				}
				catch (Exception ex)
				{
					DCSMsg.Show(ex);
				}
			}
			/** this code will print ERROR message in place of barcode if there is an error with the code
			else
			{
				if (strToRip == null || strToRip == "") strToRip = "ERROR";
				this.RipTextObject(designObject, designObject.FontEx, gr, dScale, eRipMode, strToRip, imageRip, rectObjectAdjustedScaled);
			}
			***********************************************************************************************/
		}

		private void RipTextObject(DCSDesignObject designObject, DCSFontEx fontex, Graphics gr, double dScale, DCSDesign.RipMode eRipMode, string strToRip, DCSDEV.DCSDesign.DCSDesignObjectGraphics imageRip, Rectangle rectObjectAdjustedScaled)
		{
			// In layout mode show the data source or formula if there is no sample text
			if (eRipMode == RipMode.RIPMODE_LAYOUT)
			{
				if (strToRip == null || strToRip == "")	// if no sample data show source
				{
					if (designObject.SourceType == DCSDEV.DCSDatatypes.SourceTypes.Database) 
						strToRip = "[" + designObject.SourceName + "]";
					else if (designObject.SourceType == DCSDEV.DCSDatatypes.SourceTypes.Formula) 
						strToRip = "~" + designObject.Formula;
				}
			}
			// Try drawing the text
			try
			{
                if (eRipMode != RipMode.RIPMODE_PRINT_KTEXT)
                {
                    imageRip.DrawObjectBackground();
                    imageRip.DrawOutline();
                }
                if (designObject.SpecialKPanel != 0 && eRipMode == RipMode.RIPMODE_PRINT_NONK) return;

				// look for embedded backslash n strings - then use multi line logic
				ArrayList lines = new ArrayList();
				bool bMulti = false;
				int iMultiSpacing = 0;

				// Before version 3.18 the multi-line case was trigered by both checking wordwrap and using the internal delimiter.
				// Internal delimiter is semicolon or any selected character if it is indicated by an equal-char pair preceding the string.
				// (Barbados relies on the older behavior).
				// New behavior is also turned on without checking wordwrap when delimiter is indicated by equal-char.
				// New method allows blank lines.

				if (fontex.Wrap)
				{
					string strDelim = DCSDEV.DCSDatatypes.MultiLineDelimiter;
					int pos0 = 0;
					int pos1;
					// look for delimiter character.
					// If string begins with equal sign, second char is the delimiter and output string begins with third char
					// Otherwise string delimiter is semi colon
					if (strToRip.Length > 2 && strToRip.Substring(0, 1) == "=")
					{
						strDelim = strToRip.Substring(1, 1);
						pos0 = 2;
					}
					while (true)
					{
						pos1 = strToRip.IndexOf(strDelim, pos0);
						if (pos1 < 0)
						{
							lines.Add(strToRip.Substring(pos0));
							break;
						}
						else
						{
							if (pos1 > pos0) lines.Add(strToRip.Substring(pos0, pos1 - pos0));
							pos0 = pos1 + 1;
							continue;
						}
					}
					if (lines.Count > 1) bMulti = true;
				}
				else
				{
					string strDelim;
					int pos0;
					int pos1;
					// look for delimiter character.
					if (strToRip.Length > 2 && strToRip.Substring(0, 1) == "=")
					{
						strDelim = strToRip.Substring(1, 1);
						pos0 = 2;
						while (true)
						{
							pos1 = strToRip.IndexOf(strDelim, pos0);
							if (pos1 < 0)
							{
								lines.Add(strToRip.Substring(pos0));
								break;
							}
							else
							{
								if (pos1 >= pos0) lines.Add(strToRip.Substring(pos0, pos1 - pos0));
								pos0 = pos1 + 1;
								continue;
							}
						}
						if (lines.Count > 1) bMulti = true;
					}
				}

				// Set up text drawing parameters
                SolidBrush brush = null;
				SolidBrush brushShadow = null;
				int iShadowDist = 0;
				Point ptShadowOffset = Point.Empty;
				if (eRipMode == RipMode.RIPMODE_PRINT_KTEXT)
					brush = new SolidBrush(Color.Black);
				else
				{
					brush = new SolidBrush(fontex.ForeColor);
					if (fontex.Shadow)
					{
						brushShadow = new SolidBrush(fontex.ShadowColor);
						iShadowDist = DCSMath.TimesDouble(1, dScale);
						ptShadowOffset.X = iShadowDist;
						ptShadowOffset.Y = iShadowDist;
					}
				}
                StringFormat stringFormat = new StringFormat();
				if (!fontex.Wrap) stringFormat.FormatFlags |= StringFormatFlags.NoWrap;
				//syh stringFormat.FormatFlags |= StringFormatFlags.LineLimit | StringFormatFlags.NoClip;
				stringFormat.FormatFlags |= StringFormatFlags.NoClip;
				
				switch(designObject.Alignment)
				{
					default:
					case DCSDEV.DCSDatatypes.Alignments.TOP:
						stringFormat.LineAlignment = StringAlignment.Near;
						break;
					case DCSDEV.DCSDatatypes.Alignments.MIDDLE:
						stringFormat.LineAlignment = StringAlignment.Center;
						break;
					case DCSDEV.DCSDatatypes.Alignments.BOTTOM:
						stringFormat.LineAlignment = StringAlignment.Far;
						break;
				}
				switch(designObject.Justification)
				{
					default:
					case DCSDEV.DCSDatatypes.Justifications.LEFT:
						stringFormat.Alignment = StringAlignment.Near;
						break;
					case DCSDEV.DCSDatatypes.Justifications.CENTER:
						stringFormat.Alignment = StringAlignment.Center;
						break;
					case DCSDEV.DCSDatatypes.Justifications.RIGHT:
						stringFormat.Alignment = StringAlignment.Far;
						break;
				}

				// set up rotation transform
				// always use the transform method even if there is no rotation
				// syh note: it might be faster to make a special case of the non rotated case
				Point pointTranslateTransform = Point.Empty;
				float floatRotateTransform = 0.0f;
				Size sizeBounds = Size.Empty;

				switch(designObject.RotateFlip)
				{
					default:
					case System.Drawing.RotateFlipType.RotateNoneFlipNone:
						floatRotateTransform = 0.0f;
						pointTranslateTransform.X = rectObjectAdjustedScaled.X;
						pointTranslateTransform.Y = rectObjectAdjustedScaled.Y;
						sizeBounds.Width = rectObjectAdjustedScaled.Width;
						sizeBounds.Height = rectObjectAdjustedScaled.Height;
						break;
					case System.Drawing.RotateFlipType.Rotate90FlipNone:
						floatRotateTransform = 90.0f;
						pointTranslateTransform.X = rectObjectAdjustedScaled.X + rectObjectAdjustedScaled.Width;
						pointTranslateTransform.Y = rectObjectAdjustedScaled.Y;
						sizeBounds.Width = rectObjectAdjustedScaled.Height;
						sizeBounds.Height = rectObjectAdjustedScaled.Width;
						break;
					case System.Drawing.RotateFlipType.Rotate180FlipNone:
						floatRotateTransform = 180.0f;
						pointTranslateTransform.X = rectObjectAdjustedScaled.X + rectObjectAdjustedScaled.Width;
						pointTranslateTransform.Y = rectObjectAdjustedScaled.Y + rectObjectAdjustedScaled.Height;
						sizeBounds.Width = rectObjectAdjustedScaled.Width;
						sizeBounds.Height = rectObjectAdjustedScaled.Height;
						break;
					case System.Drawing.RotateFlipType.Rotate270FlipNone:
						floatRotateTransform = 270.0f;
						pointTranslateTransform.X = rectObjectAdjustedScaled.X;
						pointTranslateTransform.Y = rectObjectAdjustedScaled.Y + rectObjectAdjustedScaled.Height;
						sizeBounds.Width = rectObjectAdjustedScaled.Height;
						sizeBounds.Height = rectObjectAdjustedScaled.Width;
						break;
				}

				// set up font - reduced if optioned and needed to make the text fit.
				System.Drawing.Font fontLastOK;

				SizeF sz = new SizeF(0.0F, 0.0F);
				if (fontex.SizeToFit)
				{
					// this method makes the text as big as it can be and still fit - size-to-fit
					/***************************************
									int len = strToRip.Length;
									int charactersFitted, linesFilled;
									float fFontSize = designObject.Font.Size;
									float min = 1.0f;
									float max = rectObjectAdjustedScaled.Height;
									float med;
									System.Drawing.Font font = designObject.Font;
									for(;;) 
									{
										med = (float)Math.Round((max+min)/2.0f,2);
										if (max - min <= 2.0f) break;
										font = new System.Drawing.Font(designObject.Font.FontFamily, med, designObject.Font.Style);
										SizeF sz = gr.MeasureString(strToRip, font, rectObjectAdjustedScaled.Size, stringFormat, out charactersFitted, out linesFilled);
										if (charactersFitted < len || sz.Width > rectObjectAdjustedScaled.Width || sz.Height > rectObjectAdjustedScaled.Height) 
										{
											max = med;
										}
										else 
										{
											fontLastOK = font;
											min = med;
										}
									}
									designObject.Font = fontLastOK;
									*********************************************/

					// the more useful option is to only decrease font size - i.e. shrink-to-fit
					fontLastOK = new System.Drawing.Font(fontex.Font.FontFamily, fontex.Font.Size * (float)dScale, fontex.Font.Style);
					if (bMulti)
					{
						// multi lines width is the max of all line widths
						foreach(string str in lines)
						{
							SizeF s = gr.MeasureString(str, fontLastOK);
							if (s.Width > sz.Width) sz = s;
						}
					}
					else
					{
						sz = gr.MeasureString(strToRip, fontLastOK);
					}

					/* this way reduces size to fit in both horizontal and vertical directions.******
					double factor, factorW=1.0, factorH=1.0;
					if (sz.Width > sizeBounds.Width)
						factorW = (double)sizeBounds.Width / (double)sz.Width;
					if (sz.Height > sizeBounds.Height)
						factorH = (double)sizeBounds.Height / (double)sz.Height;
					factor = Math.Min(factorH, factorW);
					*********************************************************************************/
					if (sz.Width > sizeBounds.Width)
                    {
                        float sizeNewF = (float)(fontLastOK.Size * (double)sizeBounds.Width / (double)sz.Width);
                        fontLastOK = new System.Drawing.Font(fontLastOK.FontFamily, sizeNewF, fontLastOK.Style);
                    }
				}
				else 
				{
					fontLastOK = new System.Drawing.Font(fontex.Font.FontFamily, fontex.Font.Size * (float)dScale, fontex.Font.Style);
				}

				//	if (designObject.RotateFlip != RotateFlipType.RotateNoneFlipNone)
				// in previous version, Rotation and multi line were mutually exclusive
				// now rotated and non-rotated are handles with  a rotation transform. The transform 
				// also applies the xy translation

                System.Drawing.Drawing2D.Matrix transformSaved = gr.Transform.Clone();
                
				gr.TranslateTransform(pointTranslateTransform.X, pointTranslateTransform.Y);
				gr.RotateTransform(floatRotateTransform);
				Rectangle rectDraw = new Rectangle(new Point(0,0), sizeBounds);
				// then draw the text and reset the transform - as is done below

				if (bMulti)
				{
					// determine the line spacing and initial vertical offset of the multi lines
					// handle vertical alignment "manually" rather then by StringAlignment as is done for single line.
					stringFormat.LineAlignment = StringAlignment.Near;
					// if the design specifies spacing=0 then use the spacing derived by measuring the string.
					iMultiSpacing = fontex.FontLineSpacing;
					if (iMultiSpacing <= 0)
					{
						// if SizeToFit, the string was already measured.
						if (!fontex.SizeToFit) sz = gr.MeasureString((string)lines[0], fontLastOK);
						iMultiSpacing = DCSDEV.DCSMath.DivDouble((int)(sz.Height), dScale);
					}

					int i = 0;
					int hgt = DCSMath.TimesDouble(iMultiSpacing, dScale);
					int topAdj = 0;
					int diff = sizeBounds.Height - (hgt * lines.Count);
					if (diff > 0)
					{
						switch(designObject.Alignment)
						{
							case DCSDEV.DCSDatatypes.Alignments.MIDDLE:
								topAdj = diff / 2;
								break;
							case DCSDEV.DCSDatatypes.Alignments.BOTTOM:
								topAdj = diff;
								break;
						}
					}

					Rectangle rectMulti = new Rectangle(Point.Empty, sizeBounds);
					foreach(string str in lines)
					{

						rectMulti = rectObjectAdjustedScaled;
						rectMulti.Y = rectObjectAdjustedScaled.Top + topAdj + hgt * i;
						rectMulti.Height = rectObjectAdjustedScaled.Height - topAdj - hgt * i;

						rectMulti = new Rectangle(Point.Empty, sizeBounds);
						rectMulti.Y = 0 + topAdj + hgt * i;
						rectMulti.Height = sizeBounds.Height - topAdj - hgt * i;

						if (rectMulti.Height <= 0) break;
						if (str.Length > 0)
						{
							if (fontex.Shadow && brushShadow != null)
							{
								gr.DrawString(str, fontLastOK, brushShadow, new Rectangle(rectMulti.Location + (Size)ptShadowOffset, rectMulti.Size), stringFormat);
							}
							gr.DrawString(str, fontLastOK, brush, rectMulti, stringFormat);
						}
						i++;
					}
				}
				else
				{
					// draw using a rectangle based on sizeBounds with zero offset, because this is the rectangle the works with the rotation transform
					if (fontex.Shadow && brushShadow != null)
					{
						gr.DrawString(strToRip, fontLastOK, brushShadow, new Rectangle(ptShadowOffset, sizeBounds), stringFormat);
					}
					gr.DrawString(strToRip, fontLastOK, brush, new Rectangle(Point.Empty, sizeBounds), stringFormat);
				}

				// reset the transform 
				gr.ResetTransform();
                gr.Transform = transformSaved;
			}
			catch (Exception ex)
			{
				DCSDEV.DCSMsg.Show("ERROR drawing text object.", ex);
			}
		}

		private void RipBarcodeObject(DCSDesignObject designObject, Graphics gr, double dScale, DCSDesign.RipMode eRipMode, string strToRip, DCSDEV.DCSDesign.DCSDesignObjectGraphics imageRip, Rectangle rectObjectAdjustedScaled)
		{
			if (eRipMode == RipMode.RIPMODE_LAYOUT)
			{
				if (strToRip == null || strToRip == "") strToRip = "012345";
			}
            // always draw background and outline except when doing Kpanel text
            if (eRipMode != RipMode.RIPMODE_PRINT_KTEXT)
            {
                imageRip.DrawObjectBackground();
                imageRip.DrawOutline();
            }
            // then quit if doing the non k background of a k panel object
            if (designObject.SpecialKPanel != 0 && eRipMode == RipMode.RIPMODE_PRINT_NONK) return;

            DCSSDK.DCSBarcodeIF.SaxIF dcsSax = new DCSSDK.DCSBarcodeIF.SaxIF();
			Color colorBack;
			if (designObject.BackFillType == DCSDEV.DCSDatatypes.BackFillTypes.FILL_CLEAR)
				colorBack = System.Drawing.Color.Transparent;
			else if (designObject.BackFillType == DCSDEV.DCSDatatypes.BackFillTypes.FILL_COLOR)
				colorBack = designObject.BackColor;
			else
				colorBack = System.Drawing.Color.White;

            string strError = null;
			bool bRet = dcsSax.Rip1DBarcode(strToRip, gr, rectObjectAdjustedScaled, designObject.RotateFlip, (int)designObject.Justification, designObject.BarcodeCode, designObject.BarcodeShowText, colorBack, designObject.FontEx.ForeColor, designObject.FontEx.Font, dScale, ref strError);
            if (!bRet) DCSDEV.DCSMsg.Show(strError, MessageBoxIcon.Error);
        }

		// output mag tracks if configured to do so
		public void SendMagTracksToDevice(Graphics gr)
		{
			if (this.HasMagStripe)
			{
                /********** this method is tested and released with P640 printers 
				Font font = new Font("Microsoft Sans Serif", 8.0F, System.Drawing.FontStyle.Regular);
				Brush brush = new SolidBrush(Color.Black);
				if (this.Track1Data != "") gr.DrawString(this.Track1Data, font, brush, 0, 0);
                if (this.Track2Data != "") gr.DrawString(this.Track2Data, font, brush, 0, 0);
                if (this.Track3Data != "") gr.DrawString(this.Track3Data, font, brush, 0, 0);
                *****/

                // new method introduced to make encoding work on XID printers - 11/14/2007
                if (this.Track1Data != "") SendMagStripeToMedia(gr, this.Track1Data);      //Data for track1
                if (this.Track2Data != "") SendMagStripeToMedia(gr, this.Track2Data);      //Data for track2
                if (this.Track3Data != "") SendMagStripeToMedia(gr, this.Track3Data);      //Data for track3
            }
		}

        // definitions necessary for SendMagStripeToMedia
        public struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }
        [DllImport("gdi32", CharSet = CharSet.Auto)]
        private static extern int ExtTextOut(IntPtr hdc, int x, int y, int nOptions, ref RECT lpRect, string s, int nStrLength, int[] lpDx);

        // new method introduced to make encoding work on XID printers
        public static void SendMagStripeToMedia(System.Drawing.Graphics grfx, string trackData)
        {
            IntPtr hdc = grfx.GetHdc();
            RECT rect;
            rect.Left = 0;
            rect.Top = 0;
            rect.Right = 0;
            rect.Bottom = 0;
            int iRet = ExtTextOut(hdc, 0, 0, 0, ref rect, trackData, trackData.Length, null);
            grfx.ReleaseHdc(hdc);
        }

		public bool AreAllObjectsInside(int iSide, Rectangle rectSide)
		{
			DCSDEV.DCSDesign.DCSDesignSide designSide = (DCSDEV.DCSDesign.DCSDesignSide)this.m_designSides[iSide];
			foreach (DCSDesignObject designObject in designSide.m_DCSDesignObjects)
			{
				if (!rectSide.Contains(designObject.Bounds)) return false;
			}
			return true;
		}

		public void MoveAllObjectsInside(int iSide, Rectangle rectSide)
		{
			DCSDEV.DCSDesign.DCSDesignSide designSide = (DCSDEV.DCSDesign.DCSDesignSide)this.m_designSides[iSide];
			foreach (DCSDesignObject designObject in designSide.m_DCSDesignObjects)
			{
				if (!rectSide.Contains(designObject.Bounds))
				{
					DCSDEV.DCSMath.ApplyBounds(ref designObject.Bounds, rectSide);
				}
			}
		}
	}

	public class DCSFontEx
	{
		public System.Drawing.Font Font = new Font("Microsoft Sans Serif", 8.0F, System.Drawing.FontStyle.Regular);
		public int FontLineSpacing = 0;
		public Color ForeColor = Color.Black;
		public bool Wrap;
		public bool SizeToFit;
		public bool Shadow;
		public Color ShadowColor = Color.Gray;

		public DCSFontEx()
		{
		}

		public DCSFontEx Clone()
		{
			DCSFontEx myclone = new DCSFontEx();
			myclone.Font = (Font)this.Font.Clone();
			myclone.FontLineSpacing = this.FontLineSpacing;
			myclone.ForeColor = this.ForeColor;
			myclone.Wrap = this.Wrap;
			myclone.SizeToFit = this.SizeToFit;
			myclone.Shadow = this.Shadow;
			myclone.ShadowColor = this.ShadowColor;
			return myclone;
		}
	}
}

