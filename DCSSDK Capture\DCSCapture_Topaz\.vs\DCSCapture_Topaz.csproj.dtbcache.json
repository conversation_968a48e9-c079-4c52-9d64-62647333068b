{"RootPath": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSCapture_Topaz", "ProjectFileName": "DCSCapture_Topaz.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "AboutTopaz.cs"}, {"SourceFile": "AssemblyInfo.cs"}, {"SourceFile": "TopazMain.cs"}, {"SourceFile": "TopazProperties.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSSDK_Finisher\\bin\\Debug\\DCSSDK_Finisher.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSSDK_Finisher\\bin\\Debug\\DCSSDK_Finisher.dll"}, {"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSSDK_FinisherProperties\\bin\\Debug\\DCSSDK_FinisherProperties.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSSDK_FinisherProperties\\bin\\Debug\\DCSSDK_FinisherProperties.dll"}, {"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSSDK_Utilities\\bin\\Debug\\DCSSDK_Utilities.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSSDK_Utilities\\bin\\Debug\\DCSSDK_Utilities.dll"}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\Referenced Files\\Topaz\\SigPlusNET.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSCapture_Topaz\\bin\\Debug\\DCSCapture_Topaz.dll", "OutputItemRelativePath": "DCSCapture_Topaz.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}