:Base Ltdotnetn.hlp>Main
1 LEADTOOLS for .NET Help
2 Welcome to LEADTOOLS for .NET Help,Version 13=Welcome_to_LEADTOOLS_NET_Help
2 Introduction to LEADTOOLS for .NET Help
3 Introduction=Introduction
3 Compression Using LEAD and JPEG Formats=17Compression
2 Version History
3 Version History=Highlights
3 Version 13=Version13
2 Copyright Notice
3 Copyright notice=7Copyright
2 LEADTOOLS Licensing, Products, & Imaging
3 Topic Selection=Products
2 Introduction and Overview
3 Introduction
4 Introduction To Image Processing with LEADTOOLS=16Image
4 Introduction: Bitmaps in Memory and in Files=9Introduction
4 Introduction: Bits Per Pixel and Related Ideas=4Introduction
4 Introduction: Color Resolution and Dithering=5Introduction
4 Introduction: DIBs, DDBs, and the Clipboard=10Introduction
4 Introduction: Image Display=8Introduction
4 Introduction: Image Manipulation and Analysis=7Introduction
4 Introduction: Palette Handling=6Introduction
3 Example Programs
4 Example Programs=5Example
2 Programming with LEADTOOLS .NET
3 Programming with the LEADTOOLS for .NET=Programming
3 Getting Started
4 Adding and Removing LEADTOOLS for .NET Components to or from the GAC=AddingandRemovingLEADTOOLSforNETComponentstoorfromtheGAC
4 Using LEADTOOLS For .NET Components in Your Projects=UsingLEADTOOLSForNETComponentsinYourProjects
4 Unlocking LEADTOOLS Capabilities=UnlockingLEADTOOLSCapabilities
4 Unlocking Vector File Format Support=Unlocking_Vector_File_Format_Support
4 Concepts and Definitions=1Concepts
4 Error Codes and Exceptions=ErrorCodesandExceptions
4 Bitmaps - Single and Multi-page=BitmapsSingleandMultipage
4 Loading Filters=ProgLoadFilters
3 Working with Raster Images
4 Input and Output
5 Input and Output=ProgIO
5 Loading a Bitmap from a File=2Loading
5 Loading a Bitmap from Your Own Buffer=3Loading
5 Loading and Saving Large TIFF Files=Loading_and_Saving_Large_TIFF_Files
5 Loading Vector Files=Loading_Vector_Files
5 Saving a Bitmap to a File=5Saving
5 Maintaining File Comments=ProgComments
5 Implementing PhotoCD and FlashPix Features=RasterKodak
5 Implementing GIF Features=RasterGif
5 Implementing JBIG Features=RasterJBIG
5 Implementing JPEG 2000 Plug in Features=Implementing_JPEG_2000_Plug_in_Features
5 Implementing PDF Plug in Features=Implementing_PDF_Plug_in_Features
5 Implementing Internet Features=ProgInternet
5 Using Memory-Resident Image Files=MemFiles
5 Capturing an Image from the Screen=20Capturing
5 Working with the RAW File Filter=Working_with_the_RAW_File_Filter
4 Displaying Images
5 Displaying an Image=4Manipulating
5 Scrolling an Image=Scrolling_an_Image
5 Managing Palettes=39Managing
5 Marking a Selection with a Rubberband=6Marking
4 Image Processing
5 Image Processing=ProgImageProc
5 Examining and Altering Bitmaps=3Manipulating
5 Changing Brightness and Contrast=ProgBrightness
5 Removing Noise=ProgNoise
5 Detecting and Enhancing Edges and Lines=ProgEdge
5 Applying Artistic Effects=ProgArtistic
5 Correcting Colors=ProgColors
5 Managing Color Separations=24Managing
5 Combining Images=36Combining
5 Using the .NET Graphics Class=UsingtheNETGraphicsClass
5 Using Histograms=ProgHistogram
5 Cleaning Up 1-Bit Images=ProgCleaning
4 Imaging Common Dialogs
5 Using Imaging Common Dialogs=ProgCommonDlg
5 Working with the AngleDialog Class=WorkingwiththeAngleDialogClass
5 Working with the ChangeDialog Class=WorkingwiththeChangeDialogClass
5 Working with the ColorResDialog Class=WorkingwiththeColorResDialogClass
5 Working with the ContourDialog Class=WorkingwiththeContourDialogClass
5 Working with the EmbossDialog Class=WorkingwiththeEmbossDialogClass
5 Working with the FileDialog Class=WorkingwiththeFileDialogClass
5 Working with the FilterDialog Class=WorkingwiththeFilterDialogClass
5 Working with the GammaDialog Class=WorkingwiththeGammaDialogClass
5 Working with the HalfToneDialog Class=WorkingwiththeHalfToneDialogClass
5 Working with the HelpEventArgs Class=WorkingwiththeHelpEventArgsClass
5 Working with the NoiseDialog Class=WorkingwiththeNoiseDialogClass
5 Working with the OpenFileDialog Class=WorkingwiththeOpenFileDialogClass
5 Working with the RangeDialog Class=WorkingwiththeRangeDialogClass
5 Working with the SaveFileDialog Class=WorkingwiththeSaveFileDialogClass
5 Working with the SizeDialog Class=WorkingwiththeSizeDialogClass
5 Working with the WindowLevelDialog Class=WorkingwiththeWindowLevelDialogClass
4 Scanning Images with TWAIN
5 TWAIN Programming with LEADTOOLS for .NET=LEADTOOLSProgramming
5 Initializing a TWAIN Session=InitializingaTWAINSession
5 Managing the TWAIN Source=ManagingTWAINSource
5 Getting and Setting Capabilities=GettingandSettingCapabilities
5 How to Work with the Containers=WorkingwithContainers
5 How to Acquire from the TWAIN Source=AcquiringImages
5 Handling Template Files=HandlingTemplateFiles
5 Freeing the TWAIN session=FreeingtheTWAINsession
4 Handling Events
5 Controlling and Detecting Events=ProgEvents
5 Controlling Events=9Controlling
5 Detecting Events=9Detecting
4 Working with Bitmaps, Images, Regions and Files
5 Copying and Creating Bitmaps=7Copying
5 Accounting for View Perspective=TutViewPer
5 Linking and Unlinking Images=LinkingandUnlinkingImages
5 Super Compressed Bitmaps=SuperCompressedBitmaps
5 Translating to and from Bitmap Coordinates=BitmapCoord
5 Converting Image Formats=ConvertingImageFormats
5 Creating and Using a Bitmap Region=3Creating
5 Implementing TIFF Tags=ProgTIFFTags
5 Exif File Extensions=ExifFileExtensions
3 Annotations
4 Implementing Annotations=3Implementing
2 Components
3 Installation Directory=4Installation
3 Technical Support=TechSupport
3 LEADTOOLS Documentation
4 LEADTOOLS Reference Manuals=LtRefDocs
2 Redistributables / Files to be Included with Your Application
3 Files To Be Included With Your Application=14Files
2 File Formats
3 Summary of All Supported Image File Formats=18Summary
3 Formats of Input Files=InFormats
3 Formats of Output Files=OutFormats
3 Multipage File Formats=TutMultipageFmt
3 Color and Grayscale
4 File Formats: AFP (AFP)=AFPFmt
4 File Formats: BMP Formats=BmpFmt
4 File Formats: Computer Graphics Metafile (CGM)=CGMFmt
4 File Formats: Corel Presentation Exchange (CMX)=CMXFmt
4 File Formats: DGN (DGN)=DGNFmt
4 File Formats: DICOM Format (DIC)=DicomFmt
4 File Formats: Dr. Halo (CUT)=CUTFmt
4 File Formats: DRaWing (DRW)=DRWFmt
4 File Formats: Drawing Interchange Format (DXF)=DXFFmt
4 File Formats: DWF (DWF)=DWFFmt
4 File Formats: DWG Format (DWG)=DWG_Format_DWG_
4 File Formats: Encapsulated PostSript (EPS)=PostScriptFmt
4 File Formats: ESRI Shape (SHP)=SHPFmt
4 File Formats: Exif Formats (TIFF and JPG)=ExifFmt
4 File Formats: Flic Animation (FLC)=FLCFmt
4 File Formats: Gerber Format (GBR)=GBRFmt
4 File Formats: GIF and TIFF with LZW Compression=LzwFmt
4 File Formats: Icons and Cursors=IconFmt
4 File Formats: Interchange File Format (IFF)=IFFFmt
4 File Formats: JBIG Format (JBG)=JBIGFmt
4 File Formats: JPEG and LEAD Compressed (JPG, J2K, JP2, CMP, CMW)=JpegLeadFmt
4 File Formats: Kodak Formats (PCD and FPX)=KodakFmt
4 File Formats: Macintosh Pict Format (PCT)=PctFmt
4 File Formats: Microsoft Windows Clipboard (CLP)=CLPFmt
4 File Formats: PCX Formats (PCX and DCX)=PcxFmt
4 File Formats: PhotoShop 3.0 Format (PSD)=PsdFmt
4 File Formats: PLT (PLT)=PLTFmt
4 File Formats: Portable Bitmap Utilities (PBM)=PBMFmt
4 File Formats: Portable Document Format (PDF)=PDFFmt
4 File Formats: Portable Network Graphics Format (PNG)=PngFmt
4 File Formats: Printer Command Language (PCL)=PCLFmt
4 File Formats: PTOCA (PTOCA)=PTOCAFmt
4 File Formats: Scalable Vector Graphics (SVG)=SVGFmt
4 File Formats: Scitex Continuous Tone Format (SCT)=SCTFmt
4 File Formats: Silicon Graphics Image Format (SGI)=SGIFmt
4 File Formats: SUN Raster Format (RAS)=SunFmt
4 File Formats: TIFF Without LZW Compression=TiffFmt
4 File Formats: Truevision TARGA Format (TGA)=TgaFmt
4 File Formats: Windows Animated Cursor (ANI)=ANIFmt
4 File Formats: Windows AVI Format (AVI)=AVIFmt
4 File Formats: Windows Metafile Format (WMF)=WmfFmt
4 File Formats: Wireless Bitmap Format (WBMP)=WBMPFmt
4 File Formats: WordPerfect Graphics Format (WPG and VWPG)=WpgFmt
4 File Formats: X Window Dump (XWD)=XWDFmt
4 File Formats: XPicMap Format (XPM)=XPMFmt
3 Bitonal (1-Bit)
4 File Formats: Image Object Content Architecture (IOCA/MODCA)=IOCAFmt
4 File Formats: Intergraph (ITG)=ITGFmt
4 File Formats: LEAD 1-Bit Format (CMP)=Lead1Fmt
4 File Formats: Miscellaneous 1-Bit Formats (MAC, IMG, and MSP)=MiscFmt
4 File Formats: SMP (SMP)=SMPFmt
4 File Formats: TIFF CCITT and Other FAX Formats=FaxFmt
4 File Formats: XBitMap (XBM)=XBMFmt
2 Classes
3 Class Hierarchy Chart=LEADHierarchy
3 AcquirePageEventArgs
4 AcquirePageEventArgs Class=AcquirePageEventArgsClass
4 AcquirePageEventArgs Class Members=AcquirePageEventArgsClassMembers
3 AngleDialog
4 AngleDialog Class=AngleDialogClass
4 AngleDialog Class Members=AngleDialogClassMembers
3 ArrayCapability
4 ArrayCapability Class=ArrayCapabilityClass
4 ArrayCapability Class Members=ArrayCapabilityClassMembers
3 BinaryFilter
4 BinaryFilter Class=BinaryFilterClass
4 BinaryFilter Class Members=BinaryFilterClassMembers
3 Bitmap
4 Bitmap Class=BitmapClass
4 Bitmap Class Members=BitmapClassMembers
3 BitmapData
4 BitmapData Class=BitmapDataClass
4 BitmapData Class Members=BitmapDataClassMembers
3 BorderRemoveEventArgs
4 BorderRemoveEventArgs Class=BorderRemoveEventArgsClass
4 BorderRemoveEventArgs Class Members=BorderRemoveEventArgsClassMembers
3 BorderRemoveFilter
4 BorderRemoveFilter Class=BorderRemoveFilterClass
4 BorderRemoveFilter Class Members=BorderRemoveFilterClassMembers
3 Capability
4 Capability Class=CapabilityClass
4 Capability Class Members=CapabilityClassMembers
3 CapabilityValues
4 CapabilityValues Class=CapabilityValuesClass
4 CapabilityValues Class Members=CapabilityValuesClassMembers
3 ChangeDialog
4 ChangeDialog Class=ChangeDialogClass
4 ChangeDialog Class Members=ChangeDialogClassMembers
3 Codecs
4 Codecs Class=CodecsClass
4 Codecs Class Members=CodecsClassMembers
3 ColorFactor
4 ColorFactor Structure=ColorFactorStructure
4 ColorFactor Structure Members=ColorFactorStructureMembers
3 ColorPalette
4 ColorPalette Class=ColorPaletteClass
4 ColorPalette Class Members=ColorPaletteClassMembers
3 ColorResDialog
4 ColorResDialog Class=ColorResDialogClass
4 ColorResDialog Class Members=ColorResDialogClassMembers
3 Comment
4 Comment Class=CommentClass
4 Comment Class Members=CommentClassMembers
3 CommonDialog
4 CommonDialog Class=CommonDialogClass
4 CommonDialog Class Members=CommonDialogClassMembers
3 ContourDialog
4 ContourDialog Class=ContourDialogClass
4 ContourDialog Class Members=ContourDialogClassMembers
3 Curve
4 Curve Class=CurveClass
4 Curve Class Members=CurveClassMembers
3 DocCleanFilter
4 DocCleanFilter Class=DocCleanFilterClass
4 DocCleanFilter Class Members=DocCleanFilterClassMembers
3 DotRemoveEventArgs
4 DotRemoveEventArgs Class=DotRemoveEventArgsClass
4 DotRemoveEventArgs Class Members=DotRemoveEventArgsClassMembers
3 DotRemoveFilter
4 DotRemoveFilter Class=DotRemoveFilterClass
4 DotRemoveFilter Class Members=DotRemoveFilterClassMembers
3 EmbossDialog
4 EmbossDialog Class=EmbossDialogClass
4 EmbossDialog Class Members=EmbossDialogClassMembers
3 EnumerationCapability
4 EnumerationCapability Class=EnumerationCapabilityClass
4 EnumerationCapability Class Members=EnumerationCapabilityClassMembers
3 Exception
4 Exception Class=ExceptionClass
4 Exception Class Members=ExceptionClassMembers
3 ExtImageInfo
4 ExtImageInfo Class=ExtImageInfoClass
4 ExtImageInfo Class Members=ExtImageInfoClassMembers
3 Extensions
4 Extensions Class=ExtensionsClass
4 Extensions Class Members=ExtensionsClassMembers
3 FileDialog
4 FileDialog Class=FileDialogClass
4 FileDialog Class Members=FileDialogClassMembers
3 FilterDialog
4 FilterDialog Class=FilterDialogClass
4 FilterDialog Class Members=FilterDialogClassMembers
3 Frame
4 Frame Class=FrameClass
4 Frame Class Members=FrameClassMembers
3 GammaDialog
4 GammaDialog Class=GammaDialogClass
4 GammaDialog Class Members=GammaDialogClassMembers
3 HalfToneDialog
4 HalfToneDialog Class=HalfToneDialogClass
4 HalfToneDialog Class Members=HalfToneDialogClassMembers
3 HelpEventArgs
4 HelpEventArgs Class=HelpEventArgsClass
4 HelpEventArgs Class Members=HelpEventArgsClassMembers
3 HolePunchRemoveEventArgs
4 HolePunchRemoveEventArgs Class=HolePunchRemoveEventArgsClass
4 HolePunchRemoveEventArgs Class Members=HolePunchRemoveEventArgsClassMembers
3 HolePunchRemoveFilter
4 HolePunchRemoveFilter Class=HolePunchRemoveFilterClass
4 HolePunchRemoveFilter Class Members=HolePunchRemoveFilterClassMembers
3 Image
4 Image Class=ImageClass
4 Image Class Members=ImageClassMembers
3 ImageChangeEventArgs
4 ImageChangeEventArgs Class=ImageChangeEventArgsClass
4 ImageChangeEventArgs Class Members=ImageChangeEventArgsClassMembers
3 ImageFormatConverter
4 ImageFormatConverter Class=ImageFormatConverterClass
4 ImageFormatConverter Class Members=ImageFormatConverterClassMembers
3 ImageProcessing
4 ImageProcessing Class=ImageProcessingClass
4 ImageProcessing Class Members=ImageProcessingClassMembers
3 Info
4 Info Class=InfoClass
4 Info Class Members=InfoClassMembers
3 InvertedTextEventArgs
4 InvertedTextEventArgs Class=InvertedTextEventArgsClass
4 InvertedTextEventArgs Class Members=InvertedTextEventArgsClassMembers
3 InvertedTextFilter
4 InvertedTextFilter Class=InvertedTextFilterClass
4 InvertedTextFilter Class Members=InvertedTextFilterClassMembers
3 J2KOptions
4 J2KOptions Class=J2KOptionsclass
4 J2KOptions Class Members=J2KOptionsClassMembers
3 LineRemoveEventArgs
4 LineRemoveEventArgs Class=LineRemoveEventArgsClass
4 LineRemoveEventArgs Class Members=LineRemoveEventArgsClassMembers
3 LineRemoveFilter
4 LineRemoveFilter Class=LineRemoveFilterClass
4 LineRemoveFilter Class Members=LineRemoveFilterClassMembers
3 NoiseDialog
4 NoiseDialog Class=NoiseDialogClass
4 NoiseDialog Class Members=NoiseDialogClassMembers
3 OneValueCapability
4 OneValueCapability Class=OneValueCapabilityClass
4 OneValueCapability Class Members=OneValueCapabilityClassMembers
3 OpenFileDialog
4 OpenFileDialog Class=OpenFileDialogClass
4 OpenFileDialog Class Members=OpenFileDialogClassMembers
3 PCDOptions
4 PCDOptions Class=PCDOptionsClass
4 PCDOptions Class Members=PCDOptionsClassMembers
3 PDFOptions
4 PDFOptions Class=PDFOptionsClass
4 PDFOptions Class Members=PDFOptionsClassMembers
3 PictureBox
4 PictureBox Class=PictureBoxClass
4 PictureBox Class Members=PictureBoxClassMembers
3 PicturizeEventArgs
4 PicturizeEventArgs Class=PicturizeEventArgsClass
4 PicturizeEventArgs Class Members=PicturizeEventArgsClassMembers
3 PixelFormatConverter
4 PixelFormatConverter Class=PixelFormatConverterClass
4 PixelFormatConverter Class Members=PixelFormatConverterClassMembers
3 PreviewCommonDialog
4 PreviewCommonDialog Class=PreviewCommonDialogClass
4 PreviewCommonDialog Class Members=PreviewCommonDialogClassMembers
3 ProgressEventArgs
4 ProgressEventArgs Class=ProgressEventArgsClass
4 ProgressEventArgs Class Members=ProgressEventArgsClassMembers
3 RangeCapability
4 RangeCapability Class=RangeCapabilityClass
4 RangeCapability Class Members=RangeCapabilityClassMembers
3 RangeDialog
4 RangeDialog Class=RangeDialogClass
4 RangeDialog Class Members=RangeDialogClassMembers
3 RasterizeOptions
4 RasterizeOptions Class=RasterizeOptionsclass
4 RasterizeOptions Class Members=RasterizeOptionsClassMembers
3 ReadyStateChangeEventArgs
4 ReadyStateChangeEventArgs Class=ReadyStateChangeEventArgsClass
4 ReadyStateChangeEventArgs Class Members=ReadyStateChangeEventArgsClassMembers
3 RegionXForm
4 RegionXForm Class=RegionXFormClass
4 RegionXForm Class Members=RegionXFormClassMembers
3 RubberBand
4 RubberBand Class=RubberBandClass
4 RubberBand Class Members=RubberBandClassMembers
3 RubberBandButtonOptions
4 RubberBandButtonOptions Structure=RubberBandButtonOptionsStructure
4 RubberBandButtonOptions Structure Members=RubberBandButtonOptionsStructureMembers
3 RubberBandEventArgs
4 RubberBandEventArgs Class=RubberBandEventArgsClass
4 RubberBandEventArgs Class Members=RubberBandEventArgsClassMembers
3 SaveFileDialog
4 SaveFileDialog Class=SaveFileDialogClass
4 SaveFileDialog Class Members=SaveFileDialogClassMembers
3 SaveFileFormat
4 SaveFileFormat Class=SaveFileFormatClass
4 SaveFileFormat Class Members=SaveFileFormatClassMembers
3 SizeDialog
4 SizeDialog Class=SizeDialogClass
4 SizeDialog Class Members=SizeDialogClassMembers
3 SmoothEventArgs
4 SmoothEventArgs Class=SmoothEventArgsClass
4 SmoothEventArgs Class Members=SmoothEventArgsClassMembers
3 SmoothFilter
4 SmoothFilter Class=SmoothFilterClass
4 SmoothFilter Class Members=SmoothFilterClassMembers
3 SpatialFilter
4 SpatialFilter Class=SpatialFilterClass
4 SpatialFilter Class Members=SpatialFilterClassMembers
3 Support
4 Support Class=SupportClass
4 Support Class Members=SupportClassMembers
3 Tag
4 Tag Class=TagClass
4 Tag Class Members=TagClassMembers
3 TemplateEventArgs
4 TemplateEventArgs Class=TemplateEventArgsClass
4 TemplateEventArgs Class Members=TemplateEventArgsClassMembers
3 Twain
4 Twain Class=TwainClass
4 Twain Class Members=TwainClassMembers
3 WindowLevelDialog
4 WindowLevelDialog Class=WindowLevelDialogClass
4 WindowLevelDialog Class Members=WindowLevelDialogClassMembers
3 WMFOptions
4 WMFOptions Class=WMFOptionsClass
4 WMFOptions Class Members=WMFOptionsClassMembers
2 Quick Reference
3 The .Net methods, events, and properties=TheNetmethodseventsandproperties
3 Working with .NET
4 Working with .NET: Client Area Usage=WorkingwithNETClient
4 Working with .NET: Events=WorkingwithNETEvents
3 Raster Images
4 Input and Output
5 Raster Images: Input and Output=RasterInput
5 Raster Images: Loading Files=RasterImagesLoadingFiles
5 Raster Images: Saving Files=RasterImagesSavingFiles
5 Raster Images: Getting and Setting File Information=RasterImagesGettingandSettingFileInformation
5 Raster Images: Loading and Saving Large TIFF Files=RasterImagesLoadingandSavingLargeTIFFFiles
5 Raster Images: Loading and Saving Stamp Images=RasterImagesLoadingandSavingStampImages
5 Raster Images: Capturing a Screen Image=RasterImagesCapturingaScreenImage
5 Raster Images: Exchanging Images with Other Objects=RasterImagesExchangingImageswithOtherObjects
4 Creation, Deletion, and Copying
5 Raster Images: Creation, Deletion, and Copying=RasterCreation
5 Raster Images: Creating and Deleting Images=RasterImagesCreatingandDeletingImages
5 Raster Images: Creating and Maintaining Lists of Images=RasterImagesCreatingandMaintainingListsofImages
5 Raster Images: Using the Clipboard=RasterImagesUsingtheClipboard
5 Raster Images: Copying Images=RasterImagesCopyingImages
4 Displaying and Printing
5 Raster Images: Displaying and Printing=RasterDisplaying
5 Raster Images: Displaying Images=RasterImagesDisplayingImages
5 Raster Images: Implementing Special Effects=RasterImagesImplementingSpecialEffects
5 Raster Images: Interpreting Bitmap Coordinates=RasterImagesInterpretingBitmapCoordinates
4 Image Processing
5 Raster Images: Processing Images=RasterImagesProcessingImages
5 Raster Images: Image Processing=RasterImageProc
5 Raster Images: Getting Current Information=RasterImagesGettingCurrentInformation
5 Raster Images: Doing Color Space Conversions=RasterImagesDoingColorSpaceConversions
5 Raster Images: Doing Color Expansion or Reduction=RasterImagesDoingColorExpansionorReduction
5 Raster Images: Doing Geometric Transformations=RasterImagesDoingGeometricTransformations
5 Raster Images: Modifying Individual Pixels=RasterImagesModifyingIndividualPixels
5 Raster Images: Getting and Setting Pixel Values=RasterImagesGettingandSettingPixelValues
5 Raster Images: Modifying Intensity Levels=RasterImagesModifyingIntensityLevels
5 Raster Images: Modifying Intensity Values=RasterImagesModifyingIntensityValues
5 Raster Images: Filtering Images=RasterImagesFilteringImages
5 Raster Images: Using Histograms=RasterImagesUsingHistograms
5 Raster Images: Combining Images=RasterImagesCombiningImages
5 Raster Images: Converting Image Formats=RasterImagesConvertingImageFormats
5 Raster Images: Getting Image Handles=RasterImagesGettingImageHandles
5 Raster Images: Locking/Unlocking a Bitmap=RasterImagesLockingUnlockingaBitmap
5 Raster Images: Cleaning Up 1-Bit Images=RasterImagesCleaningUp1BitImages
4 Palettes
5 Raster Images: Palettes=RasterPalettes
4 Region Processing
5 Raster Images: Region Processing=RasterRegion
5 Raster Images: Creating and Using a Region=RasterImagesCreatingandUsingaRegion
5 Raster Images: Where the Region Preempts the Bitmap=RasterImagesWheretheRegionPreemptstheBitmap
5 Raster Images: Methods That Transform the Region and the Bitmap=RasterImagesMethodsThatTransformtheRegionandtheBitmap
3 Imaging Common Dialogs
4 Imaging Common Dialogs: Properties Associated with All Dialogs=ImagingCommonDialogsPropertiesAssociatedwithAllDialogs
4 Imaging Common Dialogs: AngleDialog=ImagingCommonDialogsAngleDialog
4 Imaging Common Dialogs: ChangeDialog=ImagingCommonDialogsChangeDialog
4 Imaging Common Dialogs: ColorResDialog=ImagingCommonDialogsColorResDialog
4 Imaging Common Dialogs: ContourDialog=ImagingCommonDialogsContourDialog
4 Imaging Common Dialogs: EmbossDialog=ImagingCommonDialogsEmbossDialog
4 Imaging Common Dialogs: FileDialog=ImagingCommonDialogsFileDialog
4 Imaging Common Dialogs: FilterDialog=ImagingCommonDialogsFilterDialog
4 Imaging Common Dialogs: GammaDialog=ImagingCommonDialogsGammaDialog
4 Imaging Common Dialogs: HalfToneDialog=ImagingCommonDialogsHalfToneDialog
4 Imaging Common Dialogs: NoiseDialog=ImagingCommonDialogsNoiseDialog
4 Imaging Common Dialogs: OpenFileDialog=ImagingCommonDialogsOpenFileDialog
4 Imaging Common Dialogs: RangeDialog=ImagingCommonDialogsRangeDialog
4 Imaging Common Dialogs: SaveFile Dialog=ImagingCommonDialogsSaveFileDialog
4 Imaging Common Dialogs: SizeDialog=ImagingCommonDialogsSizeDialog
4 Imaging Common Dialogs: WindowLevelDialog=ImagingCommonDialogsWindowLevelDialog
3 TWAIN Functionality
4 TWAIN Functionality: Application Functions=TWAINFunctionalityApplicationFunctions
4 TWAIN Functionality: Capability Functions=TWAINFunctionalityCapabilityFunctions
4 TWAIN Functionality: Image Functions=TWAINFunctionalityImageFunctions
4 TWAIN Functionality: Margin Functions=TWAINFunctionalityMarginFunctions
4 TWAIN Functionality: Session Functions=TWAINFunctionalitySessionFunctions
4 TWAIN Functionality: Template Functions=TWAINFunctionalityTemplateFunctions
4 TWAIN Functionality: TWAIN Source Functions=TWAINFunctionalityTWAINSourceFunctions
2 Namespaces
3 LEAD Namespace=LEADNamespace
3 LEAD.Drawing Namespace=LEADDrawingNamespace
3 LEAD.Drawing.Drawing2D Namespace=LEADDrawingDrawing2DNamespace
3 LEAD.Drawing.Imaging Namespace=LEADDrawingImagingNamespace
3 LEAD.Drawing.Imaging.Codecs Namespace=LEADDrawingImagingCodecsNamespace
3 LEAD.Drawing.Imaging.ImageProcessing Namespace=LEADDrawingImagingImageProcessingNamespace
3 LEAD.Drawing.Imaging.Twain Namespace=LEADDrawingImagingTwainNamespace
3 LEAD.Windows.Forms Namespace=LEADWindowsFormsNamespace
3 LEAD.Windows.Forms.CommonDialogs Namespace=LEADWindowsFormsCommonDialogsNamespace
2 Tutorials
3 Visual Basic
4 Tutorial for Visual Basic Programmers=vTutorial
4 Visual Basic Miscellaneous Examples
5 Miscellaneous Examples (Visual Basic)=MiscExV
5 AcquirePageEventArgs Example for Visual Basic=AcquirePageEventArgsExVb
5 AngleDialog Example for Visual Basic=AngleDialogExVb
5 ArrayCapability [index] Indexer Example for Visual Basic=ArrayCapability[index]IndexerExVb
5 ArrayCapability Example for Visual Basic=ArrayCapabilityExVb
5 ArrayCapability.GetItemType Example for Visual Basic=ArrayCapabilityGetItemTypeExVb
5 BinaryFilter.Dimension Example for Visual Basic=BinaryFilterDimensionExVb
5 BinaryFilter.Max Example for Visual Basic=BinaryFilterMaxExVb
5 Bitmap () Example for Visual Basic=Bitmap1ExVb
5 Bitmap (filename) Example for Visual Basic=Bitmap4ExVb
5 Bitmap (original) Example for Visual Basic=Bitmap2ExVb
5 Bitmap (original,newSize) Example for Visual Basic=Bitmap5ExVb
5 Bitmap (original,width,height) Example for Visual Basic=Bitmap8ExVb
5 Bitmap (stream) Example for Visual Basic=Bitmap3ExVb
5 Bitmap (type,resource) Example for Visual Basic=Bitmap7ExVb
5 Bitmap (width,height) Example for Visual Basic=Bitmap6ExVb
5 Bitmap (width,height,format) Example for Visual Basic=Bitmap10ExVb
5 Bitmap (width,height,format,Palette) Example for Visual Basic=Bitmap13ExVb
5 Bitmap (width,height,format,scan0) Example for Visual Basic=Bitmap11ExVb
5 Bitmap (width,height,format,scan0,Palette) Example for Visual Basic=Bitmap12ExVb
5 Bitmap (width,height,g) Example for Visual Basic=Bitmap9ExVb
5 Bitmap.AddColorHSVRangeToRegion Example for Visual Basic=BitmapAddColorHSVRangeToRegionExVb
5 Bitmap.AddColorRGBRangeToRegion Example for Visual Basic=BitmapAddColorRGBRangeToRegionExVb
5 Bitmap.AddColorToRegion Example for Visual Basic=BitmapAddColorToRegionExVb
5 Bitmap.AddCurveToRegion Example for Visual Basic=BitmapAddCurveToRegionExVb
5 Bitmap.AddDataToRegion Example for Visual Basic=BitmapAddDataToRegionExVb
5 Bitmap.AddEllipseToRegion Example for Visual Basic=BitmapAddEllipseToRegionExVb
5 Bitmap.AddMagicWandToRegion Example for Visual Basic=BitmapAddMagicWandToRegionExVb
5 Bitmap.AddMaskToRegion Example for Visual Basic=BitmapAddMaskToRegionExVb
5 Bitmap.AddPolygonToRegion Example for Visual Basic=BitmapAddPolygonToRegionExVb
5 Bitmap.AddRectangleToRegion Example for Visual Basic=BitmapAddRectangleToRegionExVb
5 Bitmap.AddRoundRectangleToRegion Example for Visual Basic=BitmapAddRoundRectangleToRegionExVb
5 Bitmap.Advise Example for Visual Basic=BitmapAdviseExVb
5 Bitmap.BitmapSize Example for Visual Basic=BitmapBitmapSizeExVb
5 Bitmap.Capture Example for Visual Basic=BitmapCaptureExVb
5 Bitmap.ChangeViewPerspective Example for Visual Basic=BitmapChangeViewPerspectiveExVb
5 Bitmap.Clear Example for Visual Basic=BitmapClearExVb
5 Bitmap.Clone (RectangleF,PixelFormat) Example for Visual Basic=BitmapClone2ExVb
5 Bitmap.Clone(Rectangle,PixelFormat) Example for Visual Basic=BitmapClone1ExVb
5 Bitmap.Compression Example for Visual Basic=BitmapCompressionExVb
5 Bitmap.Copy Example for Visual Basic=BitmapCopyExVb
5 Bitmap.DrawAttributes Example for Visual Basic=BitmapDrawAttributesExVb
5 Bitmap.DrawProperties Example for Visual Basic=BitmapDrawPropertiesExVb
5 Bitmap.FromHicon Example for Visual Basic=BitmapFromHiconExVb
5 Bitmap.FromResource Example for Visual Basic=BitmapFromResourceExVb
5 Bitmap.GetClipSegments Example for Visual Basic=BitmapGetClipSegmentsExVb
5 Bitmap.GetHbitmap () Example for Visual Basic=BitmapGetHbitmap1ExVb
5 Bitmap.GetHbitmap (Color) Example for Visual Basic=BitmapGetHbitmap2ExVb
5 Bitmap.GetHicon Example for Visual Basic=BitmapGetHiconExVb
5 Bitmap.GetPixel Example for Visual Basic=BitmapGetPixelExVb
5 Bitmap.GetRegion () Example for Visual Basic=BitmapGetRegion1ExVb
5 Bitmap.GetRegion (RegionXForm) Example for Visual Basic=BitmapGetRegion2ExVb
5 Bitmap.GetRegionArea Example for Visual Basic=BitmapGetRegionAreaExVb
5 Bitmap.HasRegion Example for Visual Basic=BitmapHasRegionExVb
5 Bitmap.LockBits Example for Visual Basic=BitmapLockBitsExVb
5 Bitmap.MakeTransparent () Example for Visual Basic=BitmapMakeTransparent1ExVb
5 Bitmap.MakeTransparent (Color) Example for Visual Basic=BitmapMakeTransparent2ExVb
5 Bitmap.PaintContrast Example for Visual Basic=BitmapPaintContrastExVb
5 Bitmap.PaintGamma Example for Visual Basic=BitmapPaintGammaExVb
5 Bitmap.PaintIntensity Example for Visual Basic=BitmapPaintIntensityExVb
5 Bitmap.Paste Example for Visual Basic=BitmapPasteExVb
5 Bitmap.PointFromBitmap Example for Visual Basic=BitmapPointFromBitmapExVb
5 Bitmap.PointToBitmap Example for Visual Basic=BitmapPointToBitmapExVb
5 Bitmap.RectFromBitmap Example for Visual Basic=BitmapRectFromBitmapExVb
5 Bitmap.RectToBitmap Example for Visual Basic=BitmapRectToBitmapExVb
5 Bitmap.SetResolution Example for Visual Basic=BitmapSetResolutionExVb
5 Bitmap.Signed Example for Visual Basic=BitmapSignedExVb
5 Bitmap.Transparent Example for Visual Basic=BitmapTransparentExVb
5 Bitmap.ViewPerspective Example for Visual Basic=BitmapViewPerspectiveExVb
5 Bitmap.WindowLevelFillLUT Example for Visual Basic=BitmapWindowLevelFillLUTExVb
5 BorderRemoveEventArgs.Region Example for Visual Basic=BorderRemoveEventArgsRegionExVb
5 Capability.CapabilityType Example for Visual Basic=CapabilityCapabilityTypeExVb
5 ChangeDialog Example for Visual Basic=ChangeDialogExVb
5 Codecs.Dispose Example for Visual Basic=CodecsDisposeExVb
5 Codecs Example for Visual Basic=CodecsExVb
5 Codecs.DeletePage Example for Visual Basic=CodecsDeletePageExVb
5 Codecs.GetInfo (mem, page) Example for Visual Basic=CodecsGetInfoExVb
5 Codecs.IgnoreCodecs Example for Visual Basic=CodecsIgnoreCodecsExVb
5 Codecs.InfoHasAlpha Example for Visual Basic=CodecsInfoHasAlphaExVb
5 Codecs.InfoIFD Example for Visual Basic=CodecsInfoIFDExVb
5 Codecs.InfoViewPerspective Example for Visual Basic=CodecsInfoViewPerspectiveExVb
5 Codecs.J2KOptions Example for Visual Basic=CodecsJ2KOptionsExVb
5 Codecs.Load (image, buffer, format, page, pages) Example for Visual Basic=CodecsLoad1ExVb
5 Codecs.Load (image, stream, format, page, pages) Example for Visual Basic=CodecsLoad2ExVb
5 Codecs.LoadCompressed Example for Visual Basic=CodecsLoadCompressedExVb
5 Codecs.LoadInfoImageFormat Example for Visual Basic=CodecsLoadInfoImageFormatExVb
5 Codecs.LoadResolutionSize Example for Visual Basic=CodecsLoadResolutionSizeExVb
5 Codecs.LoadSigned Example for Visual Basic=CodecsLoadSignedExVb
5 Codecs.LoadStamp Example for Visual Basic=CodecsLoadStampEXVb
5 Codecs.LoadUseViewPerspective Example for Visual Basic=CodecsLoadUseViewPerspectiveExVb
5 Codecs.OnFilePage Example for Visual Basic=CodecsOnFilePageExVb
5 Codecs.OnProgressStatus Example for Visual Basic=CodecsOnProgressStatusExVb
5 Codecs.OnReadyStateChange Example for Visual Basic=CodecsOnReadyStateChangeExVb
5 Codecs.PCDOptions Example for Visual Basic=CodecsPCDOptionsExVb
5 Codecs.PDFOptions Example for Visual Basic=CodecsPDFOptionsExVb
5 Codecs.PreferredLoadImageFormat Example for Visual Basic=CodecsPreferredLoadImageFormatExVb
5 Codecs.ProgressivePasses Example for Visual Basic=CodecsProgressivePassesExVb
5 Codecs.RasterizeOptions Example for Visual Basic=CodecsRasterizeOptionsExVb
5 Codecs.ReadComment Example for Visual Basic=CodecsReadCommentExVb
5 Codecs.ReadExtensions Example for Visual Basic=CodecsReadExtensionsExVb
5 Codecs.ReadyState Example for Visual Basic=CodecsReadyStateExVb
5 Codecs.Save (image, fileName, imageFormat, pixelFormat, modify) Example for Visual Basic=CodecsSave1ExVb
5 Codecs.Save (image, stream, imageFormat, pixelFormat, modify) Example for Visual Basic=CodecsSave2ExVb
5 Codecs.SaveIFD Example for Visual Basic=CodecsSaveIFDExVb
5 Codecs.SaveInterlaced Example for Visual Basic=CodecsSaveInterlacedExVb
5 Codecs.SaveLSB Example for Visual Basic=CodecsSaveLSBExVb
5 Codecs.SavePad4 Example for Visual Basic=CodecsSavePad4ExVb
5 Codecs.SavePage Example for Visual Basic=CodecsSavePageExVb
5 Codecs.SaveResolutionSize Example for Visual Basic=CodecsSaveResolutionSizeExVb
5 Codecs.SaveTileSize Example for Visual Basic=CodecsSaveTileSizeExVb
5 Codecs.SetSaveQFactor Example for Visual Basic=CodecsSetSaveQFactorExVb
5 Codecs.SetTag Example for Visual Basic=CodecsSetTagExVb
5 Codecs.StartFeedLoad Example for Visual Basic=CodecsStartFeedLoadExVb
5 Codecs.WMFOptions Example for Visual Basic=CodecsWMFOptionsExVb
5 ColorPalette ()Example for Visual Basic=ColorPaletteExVb
5 ColorPalette.Clone Example for Visual Basic=ColorPaletteCloneExVb
5 ColorPalette.FromHpalette Example for Visual Basic=ColorPaletteFromHpaletteExVb
5 ColorPalette.GetHpalette Example for Visual Basic=ColorPaletteGetHpaletteExVb
5 ColorResDialog Example for Visual Basic=ColorResDialogExVb
5 Comment.Clone Example for Visual Basic=CommentCloneExVb
5 CommonDialog.Font Example for Visual Basic=CommonDialogFontExVb
5 CommonDialog.OnHelp Example for Visual Basic=CommonDialogOnHelpExVb
5 CommonDialog.ShowDialog Example for Visual Basic=CommonDialogShowDialogExVb
5 ContourDialog Example for Visual Basic=ContourDialogExVb
5 Curve Constructor Example for Visual Basic=CurveConstructorExVb
5 Curve.Clone Example for Visual Basic=CurveCloneExVb
5 DocCleanFilter.LEADRegion Example for Visual Basic=DocCleanFilterLEADRegionExVb
5 DocCleanFilter.Region Example for Visual Basic=DocCleanFilterRegionExVb
5 DotRemoveEventArgs.Region Example for Visual Basic=DotRemoveEventArgsRegionExVb
5 Draw(Graphics g, int x, int y, int width, int height)  Example for Visual Basic=ImageDraw11ExVb
5 EmbossDialog Example for Visual Basic=EmbossDialogExVb
5 EnumerationCapability Example for Visual Basic=EnumerationCapabilityExVb
5 EnumerationCapability.SetCurrentIndex Example for Visual Basic=EnumerationCapabilitySetCurrentIndexExVb
5 Exception () Example for Visual Basic=Exception1ExVb
5 Exception (code) Example for Visual Basic=Exception5ExVb
5 Exception (message) Example for Visual Basic=Exception2ExVb
5 Exception (message, code) Example for Visual Basic=Exception4ExVb
5 Exception (message, innerException) example for Visual Basic=Exception3ExVb
5 Exception.Code example for Visual Basic=ExceptionCodeExVb
5 ExtensionsGetAudio Example for Visual Basic=ExtensionsGetAudioExVb
5 ExtensionsLoadStamp Example for Visual Basic=ExtensionsLoadStampExVb
5 ExtImageInfo Example for Visual Basic=ExtImageInfoExVb
5 FilterDialog Example for Visual Basic=FilterDialogExVb
5 Frame Example for Visual Basic=FrameExVb
5 Frame.Clone Example for Visual Basic=FrameCloneExVb
5 GammaDialog Example for Visual Basic=GammaDialogExVb
5 HalfToneDialog Example for Visual Basic=HalfToneDialogExVb
5 HolePunchRemoveEventArgs.Region Example for Visual Basic=HolePunchRemoveEventArgsRegionExVb
5 Image.ActiveFrameIndex Example for Visual Basic=ImageActiveFrameIndexExVb
5 Image.Clone Example for Visual Basic=ImageCloneExVb
5 Image.DeleteFrame Example for Visual Basic=ImageDeleteFrameExVb
5 Image.Dispose Example for Visual Basic=ImageDisposeExVb
5 Image.Draw (Graphics  g, float x, float y, float width, float height) Example for Visual Ba=ImageDraw12ExVb
5 Image.Draw (Graphics g, float x, float y) Example for Visual Basic=ImageDraw6ExVb
5 Image.Draw (Graphics g, float x, float y, RectangleF srcRect, GraphicsUnit srcUnit)  Exampl=ImageDraw10ExVb
5 Image.Draw (Graphics g, int x, int y) Example for Visual Basic=ImageDraw5ExVb
5 Image.Draw (Graphics g, int x, int y, Rectangle srcRect, GraphicsUnit srcUnit) Example for=ImageDraw9ExVb
5 Image.Draw (Graphics g, Point point) Example for Visual Basic=ImageDraw1ExVb
5 Image.Draw (Graphics g, PointF point) Example for Visual Basic Example for Visual Basic=ImageDraw2ExVb
5 Image.Draw (Graphics g, Rectangle destRect, float srcX, float srcY, float srcWidth, float s=ImageDraw14ExVb
5 Image.Draw (Graphics g, Rectangle destRect, float srcX, float srcY, float srcWidth, float s=ImageDraw16ExVb
5 Image.Draw (Graphics g, Rectangle destRect, float srcX, float srcY, float srcWidth, float s=ImageDraw18ExVb
5 Image.Draw (Graphics g, Rectangle destRect, float srcX, float srcY, float srcWidth, float s=ImageDraw20ExVb
5 Image.Draw (Graphics g, Rectangle destRect, int srcX, int srcY, int srcWidth, int srcHeight=ImageDraw13ExVb
5 Image.Draw (Graphics g, Rectangle destRect, int srcX, int srcY, int srcWidth, int srcHeight=ImageDraw15ExVb
5 Image.Draw (Graphics g, Rectangle destRect, int srcX, int srcY, int srcWidth, int srcHeight=ImageDraw17ExVb
5 Image.Draw (Graphics g, Rectangle destRect, int srcX, int srcY, int srcWidth, int srcHeight=ImageDraw19ExVb
5 Image.Draw (Graphics g, Rectangle destRect, Rectangle srcRect, GraphicsUnit srcUnit) Exampl=ImageDraw7ExVb
5 Image.Draw (Graphics g, Rectangle rect) Example for Visual Basic=ImageDraw3ExVb
5 Image.Draw (Graphics g, RectangleF destRect, RectangleF srcRect, GraphicsUnit rcUnit) Examp=ImageDraw8ExVb
5 Image.Draw (Graphics g, RectangleF rect) Example for Visual Basic=ImageDraw4ExVb
5 Image.DrawMethod Example for Visual Basic=ImageDrawMethodExVb
5 Image.EnableEvents Example for Visual Basic=ImageEnableEventsExVb
5 Image.FireEvent Example for Visual Basic=ImageFireEventExVb
5 Image.Flags Example for Visual Basic=ImageFlagsExVb
5 Image.FrameDimensionsList Example for Visual Basic=ImageFrameDimensionsListExVb
5 Image.FromFile Example for Visual Basic=ImageFromFileExVb
5 Image.FromHbitmap (IntPtr) Example for Visual Basic=ImageFromHbitmap1ExVb
5 Image.FromHbitmap (IntPtr, IntPtr) Example for Visual Basic=ImageFromHbitmap2ExVb
5 Image.FromImage Example for Visual Basic=ImageFromImageExVb
5 Image.FromLEADBitmapHandle Example for Visual Basic=ImageFromLEADBitmapHandleExVb
5 Image.FromStream Example for Visual Basic=ImageFromStreamExVb
5 Image.GetBounds Example for Visual Basic=ImageGetBoundsExVb
5 Image.GetPixelFormatSize Example for Visual Basic=ImageGetPixelFormatSizeExVb
5 Image.GetThumbnailImage Example for Visual Basic=ImageGetThumbnailImageExVb
5 Image.GetThumbnailImageAbort Delegate Example for Visual Basic=ImageGetThumbnailImageAbortDelegateExVb
5 Image.GetType Example for Visual Basic=ImageGetTypeExVb
5 Image.HorizontalResolution Example for Visual Basic=ImageHorizontalResolutionExVb
5 Image.InsertFrame Example for Visual Basic=ImageInsertFrameExVb
5 Image.IsAlphaPixelFormat Example for Visual Basic=ImageIsAlphaPixelFormatExVb
5 Image.IsCanonicalPixelFormat Example for Visual Basic=ImageIsCanonicalPixelFormatExVb
5 Image.IsExtendedPixelFormat Example for Visual Basic=ImageIsExtendedPixelFormatExVb
5 Image.IsIndexedPixelFormat Example for Visual Basic=ImageIsIndexedPixelFormatExVb
5 Image.Link Example for Visual Basic=ImageLinkExVb
5 Image.Linked Example for Visual Basic=ImageLinkedExVb
5 Image.OnChange Example for Visual Basic=ImageOnChangeExVb
5 Image.Palette Example for Visual Basic=ImagePaletteExVb
5 Image.PhysicalDimension Example for Visual Basic=ImagePhysicalDimensionExVb
5 Image.PixelFormat Example for Visual Basic=ImagePixelFormatExVb
5 Image.RawFormat Example for Visual Basic=ImageRawFormatExVb
5 Image.RotateFlip Example for Visual Basic=ImageRotateFlipExVb
5 Image.Save (Stream,ImageFormat) Example for Visual Basic=ImageSave2ExVb
5 Image.Save (String) Example for Visual Basic=ImageSave1ExVb
5 Image.Save (String,ImageFormat) Example for Visual Basic=ImageSave3ExVb
5 Image.SelectActiveFrame Example for Visual Basic=ImageSelectActiveFrameExVb
5 Image.Size Example for Visual Basic=ImageSizeExVb
5 Image.ToImage Example for Visual Basic=ImageToImageExVb
5 Image.Width Example for Visual Basic=ImageWidthExVb
5 ImageProcessing Example for Visual Basic=ImageProcessingExVb
5 ImageProcessing.Add Example for Visual Basic=ImageProcessingAddExVb
5 ImageProcessing.AddNoise Example for Visual Basic=ImageProcessingAddNoiseExVb
5 ImageProcessing.AlphaBlend Example for Visual Basic=ImageProcessingAlphaBlendExVb
5 ImageProcessing.AntiAlias Example for Visual Basic=ImageProcessingAntiAliasExVb
5 ImageProcessing.AutoTrim Example for Visual Basic=ImageProcessingAutoTrimExVb
5 ImageProcessing.Average Example for Visual Basic=ImageProcessingAverageExVb
5 ImageProcessing.BalanceColors Example for Visual Basic=ImageProcessingBalanceColorsExVb
5 ImageProcessing.Binary Example for Visual Basic=ImageProcessingBinaryExVb
5 ImageProcessing.BorderRemove Example for Visual Basic=ImageProcessingBorderRemoveExVb
5 ImageProcessing.ColorMerge Example for Visual Basic=ImageProcessingColorMergeExVb
5 ImageProcessing.ColorRes Example for Visual Basic=ImageProcessingColorResExVb
5 ImageProcessing.ColorResList Example for Visual Basic=ImageProcessingColorResListExVb
5 ImageProcessing.Combine Example for Visual Basic=ImageProcessingCombineExVb
5 ImageProcessing.Compress Example for Visual Basic=ImageProcessingCompressExVb
5 ImageProcessing.Contour Example for Visual Basic=ImageProcessingContourExVb
5 ImageProcessing.Contrast Example for Visual Basic=ImageProcessingContrastExVb
5 ImageProcessing.ConvertSignedToUnsigned Example for Visual Basic=ImageProcessingConvertSignedToUnsignedExVb
5 ImageProcessing.ConvertToColoredGray Example for Visual Basic=ImageProcessingConvertToColoredGrayExVb
5 ImageProcessing.CreateFadedMask Example for Visual Basic=ImageProcessingCreateFadedMaskExVb
5 ImageProcessing.Deskew Example for Visual Basic=ImageProcessingDeskewExVb
5 ImageProcessing.Despeckle Example for Visual Basic=ImageProcessingDespeckleExVb
5 ImageProcessing.DotRemove Example for Visual Basic=ImageProcessingDotRemoveExVb
5 ImageProcessing.EdgeDetector Example for Visual Basic=ImageProcessingEdgeDetectorExVb
5 ImageProcessing.Emboss Example for Visual Basic=ImageProcessingEmbossExVb
5 ImageProcessing.FastRotate Example for Visual Basic=ImageProcessingFastRotateExVb
5 ImageProcessing.Fill Example for Visual Basic=ImageProcessingFillExVb
5 ImageProcessing.Flip Example for Visual Basic=ImageProcessingFlipExVb
5 ImageProcessing.GammaCorrect Example for Visual Basic=ImageProcessingGammaCorrectExVb
5 ImageProcessing.Gaussian Example for Visual Basic=ImageProcessingGaussianExVb
5 ImageProcessing.GetAutoTrimRectangle Example for Visual Basic=ImageProcessingGetAutoTrimRectangleExVb
5 ImageProcessing.GetColorCount Example for Visual Basic=ImageProcessingGetColorCountExVb
5 ImageProcessing.GetHistogram Example for Visual Basic=ImageProcessingGetHistogramExVb
5 ImageProcessing.GetRow (Image image, int row) Example for Visual Basic=ImageProcessingGetRow2ExVb
5 ImageProcessing.GetRow (Image image, int row, byte buffer) Example for Visual Basic=ImageProcessingGetRow1ExVb
5 ImageProcessing.GetUserLookupTable Example for Visual Basic=ImageProcessingGetUserLookupTableExVb
5 ImageProcessing.GrayScale (Image image, int redFact, int greenFact,int blueFact) Example fo=ImageProcessingGrayScale2ExVb
5 ImageProcessing.GrayScale (Image image, PixelFormat pixelFormat) Example for Visual Basic=ImageProcessingGrayScale1ExVb
5 ImageProcessing.HalfTone Example for Visual Basic=ImageProcessingHalfToneExVb
5 ImageProcessing.HistoContrast Example for Visual Basic=ImageProcessingHistoContrastExVb
5 ImageProcessing.HistoEqualize Example for Visual Basic=ImageProcessingHistoEqualizeExVb
5 ImageProcessing.HolePunchRemove Example for Visual Basic=ImageProcessingHolePunchRemoveExVb
5 ImageProcessing.Hue Example for Visual Basic=ImageProcessingHueExVb
5 ImageProcessing.Intensity Example for Visual Basic=ImageProcessingIntensityExVb
5 ImageProcessing.IntensityDetect Example for Visual Basic=ImageProcessingIntensityDetectExVb
5 ImageProcessing.Invert Example for Visual Basic=ImageProcessingInvertExVb
5 ImageProcessing.InvertedText Example for Visual Basic=ImageProcessingInvertedTextExVb
5 ImageProcessing.LineProfile Example for Visual Basic=ImageProcessingLineProfileExVb
5 ImageProcessing.LineRemove Example for Visual Basic=ImageProcessingLineRemoveExVb
5 ImageProcessing.Max Example for Visual Basic=ImageProcessingMaxExVb
5 ImageProcessing.Median Example for Visual Basic=ImageProcessingMedianExVb
5 ImageProcessing.Min Example for Visual Basic=ImageProcessingMinExVb
5 ImageProcessing.Mosaic Example for Visual Basic=ImageProcessingMosaicExVb
5 ImageProcessing.MotionBlur Example for Visual Basic=ImageProcessingMotionBlurExVb
5 ImageProcessing.Oilify Example for Visual Basic=ImageProcessingOilifyExVb
5 ImageProcessing.OnProgress Example for Visual Basic=ImageProcessingOnProgressExVb
5 ImageProcessing.Picturize (Image image, Size cellSize, int lightness, Image[] images())Exam=ImageProcessingPicturize1ExVB
5 ImageProcessing.Picturize(Image image, String directory, PicturizeConstants flags,Size cell=ImageProcessingPicturize2ExVb
5 ImageProcessing.Posterize Example for Visual Basic=ImageProcessingPosterizeExVb
5 ImageProcessing.RemapHue Example for Visual Basic=ImageProcessingRemapHueExVb
5 ImageProcessing.RemapIntensity Example for Visual Basic=ImageProcessingRemapIntensityExVb
5 ImageProcessing.RemoveRedEye Example for Visual Basic=ImageProcessingRemoveRedEyeExVb
5 ImageProcessing.ResizeRegion Example for Visual Basic=ImageProcessingResizeRegionExVb
5 ImageProcessing.Reverse Example for Visual Basic=ImageProcessingReverseExVb
5 ImageProcessing.Rotate Example for Visual Basic=ImageProcessingRotateExVb
5 ImageProcessing.Saturation Example for Visual Basic=ImageProcessingSaturationExVb
5 ImageProcessing.Sharpen Example for Visual Basic=ImageProcessingSharpenExVb
5 ImageProcessing.Shear Example for Visual Basic=ImageProcessingShearExVb
5 ImageProcessing.Size Example for Visual Basic=ImageProcessingSizeExVb
5 ImageProcessing.Smooth Example for Visual Basic=ImageProcessingSmoothExVb
5 ImageProcessing.Solarize Example for Visual Basic=ImageProcessingSolarizeExVb
5 ImageProcessing.Spatial Example for Visual Basic=ImageProcessingSpatialExVb
5 ImageProcessing.StretchIntensity Example for Visual Basic=ImageProcessingStretchIntensityExVb
5 ImageProcessing.SwapColors Example for Visual Basic=ImageProcessingSwapColorsExVb
5 ImageProcessing.Underlay Example for Visual Basic=ImageProcessingUnderlayExVb
5 ImageProcessing.UnsharpMask Example for Visual Basic=ImageProcessingUnsharpMaskExVb
5 ImageProcessing.WindowLevel Example for Visual Basic=ImageProcessingWindowLevelExVb
5 Info Example for Visual Basic=InfoExVb
5 InvertedTextEventArgs.Region Example for Visual Basic=InvertedTextEventArgsRegionExVb
5 LineRemoveEventArgs.Region Example for Visual Basic=LineRemoveEventArgsRegionExVb
5 NoiseDialog Constructor Example for Visual Basic=NoiseDialogConstructorExVb
5 OneValueCapability Example for Visual Basic=OneValueCapabilityExVb
5 OpenFileDialog Example for Visual Basic=OpenFileDialogExVb
5 OpenFileDialog.ShowResolutionDialog Example for Visual Basic=OpenFileDialogShowResolutionDialogExVb
5 OpenFileDialog.Thumbnail Example for Visual Basic=OpenFileDialogThumbnailExVb
5 PDFOptions.SaveUseDPI Example for Visual Basic=PDFOptionsSaveUseDPIExVb
5 PictureBox () Example for Visual Basic=PictureBoxExVb
5 PictureBox.BorderStyle Example for Visual Basic=PictureBoxBorderStyleExVb
5 PictureBox.ClientToPicture Example for Visual Basic=PictureBoxClientToPictureExVb
5 PictureBox.LEADImage Example for Visual Basic=PictureBoxLEADImageExVb
5 PictureBox.LeftButtonInteractiveMode Example for Visual Basic=PictureBoxLeftButtonInteractiveModeExVb
5 PictureBox.PictureToClient Example for Visual Basic=PictureBoxPictureToClientExVb
5 PictureBox.RightButtonInteractiveMode Example for Visual Basic=PictureBoxRightButtonInteractiveModeExVb
5 PictureBox.SourceRectangle Example for Visual Basic=PictureBoxSourceRectangleExVb
5 PictureBox.SystemImage Example for Visual Basic=PictureBoxSystemImageExVb
5 PictureBox.Zoom Example for Visual Basic=PictureBoxZoomExVb
5 PictureBox.ZoomToRect Example for Visual Basic=PictureBoxZoomToRectExVb
5 PreviewCommonDialog.Image Example for Visual Basic=PreviewCommonDialogImageExVb
5 ProgressEventArgs ()Example for Visual Basic=ProgressEventArgs1ExVb
5 ProgressEventArgs (int percentage) Example for Visual Basic=ProgressEventArgs2ExVb
5 ProgressEventArgs.Canceled Example for Visual Basic=ProgressEventArgsCanceledExVb
5 RangeCapability Example for Visual Basic=RangeCapabilityExVb
5 RangeCapability.GetItemType Example for Visual Basic=RangeCapabilityGetItemTypeExVb
5 RangeDialog Constructor Example for Visual Basic=RangeDialogConstructorExVb
5 RegionXForm Constructor (ViewPerspective) Example for Visual Basic=RegionXFormConstructor(ViewPerspective)ExVb
5 RegionXForm.Default Example for Visual Basic=RegionXFormDefaultExVb
5 RegionXForm.ViewPerspective Example for Visual Basic=RegionXFormViewPerspectiveExVb
5 RubberBand Constructor Example for Visual Basic=RubberBandConstructorExVb
5 RubberBand.ClipRectangle Example for Visual Basic=RubberBandClipRectangleExVb
5 RubberBand.Enabled Example for Visual Basic=RubberBandEnabledExVb
5 RubberBand.OnTracking Example for Visual Basic=RubberBandOnTrackingExVb
5 RubberBand.Owner Example for Visual Basic=RubberBandOwnerExVb
5 RubberBand[MouseButtons] Indexer Example for Visual Basic=RubberBandMouseButtonsIndexerExVb
5 RubberBandEventArgs () Example for Visual Basic=RubberBandEventArgs1ExVb
5 RubberBandEventArgs (status, button, rect) Example for Visual Basic=RubberBandEventArgs2ExVb
5 SaveFileDialog Example for Visual Basic=SaveFileDialogExVb
5 SaveFileDialog.BitsPerPixel Example for Visual Basic=SaveFileDialogBitsPerPixelExVb
5 SaveFileDialog.PixelFormat Example for Visual Basic=SaveFileDialogPixelFormatExVb
5 SizeDialog Example for Visual Basic=SizeDialogExVb
5 SmoothEventArgs.BumpOrNick Example for Visual Basic=SmoothEventArgsBumpOrNickExVb
5 SpatialFilter.Dimension Example for Visual Basic=SpatialFilterDimensionExVb
5 StringConstants Enumeration Example for Visual Basic=StringConstantsEnumerationExVb
5 Support.Unlock example for Visual Basic=SupportUnlockExVb
5 Tag.Clone Example for Visual Basic=TagCloneExVb
5 Tag.Id Example for Visual Basic=TagIdExVb
5 TemplateEventArgs Example for Visual Basic=TemplateEventArgsExVb
5 Twain.Acquire Example for Visual Basic=TwainAcquireExVb
5 Twain.EndSession Example for Visual Basic=TwainEndSessionExVb
5 Twain.GetCapabilitiesCount Example for Visual Basic=TwainGetCapabilitiesCountExVb
5 Twain.GetCapability (CapabilityConstants cap, GetCapabilityConstants flags) Example for Vis=TwainGetCapabilityExVb
5 Twain.GetSourceCount Example for Visual Basic=TwainGetSourceCountExVb
5 Twain.InitSession Example for Visual Basic=TwainInitSessionExVb
5 Twain.LoadTemplate Example for Visual Basic=TwainLoadTemplateExVb
5 Twain.ManufacturerName Example for Visual Basic=TwainManufacturerNameExVb
5 Twain.QueryCapability Example for Visual Basic=TwainQueryCapabilityExVb
5 Twain.SaveTemplate Example for Visual Basic=TwainSaveTemplateExVb
5 Twain.SelectSource Example for Visual Basic=TwainSelectSourceExVb
5 Twain.SetAcquireSourceName Example for Visual Basic=TwainSetAcquireSourceNameExVb
5 Twain.SetCapability Example for Visual Basic=TwainSetCapabilityExVb
5 Twain.ShowTemplateDlg Example for Visual Basic=TwainShowTemplateDlgExVb
5 WindowLevelDialog Example for Visual Basic=WindowLevelDialogExVb
3 C Sharp
4 Tutorial for C# Programmers=c4Tutorial
4 C Sharp Miscellaneous Examples
5 Miscellaneous Examples (C#)=MiscExC4
5 AcquirePageEventArgs Example for C#=AcquirePageEventArgsExC
5 AngleDialog Example for C#=AngleDialogExC
5 ArrayCapability [index] Indexer Example for C#=ArrayCapability[index]IndexerExC
5 ArrayCapability Example for C#=ArrayCapabilityExC
5 ArrayCapability.GetItemType Example for C#=ArrayCapabilityGetItemTypeExC
5 BinaryFilter.Dimension Example for C#=BinaryFilterDimensionExC
5 BinaryFilter.Max Example for C#=BinaryFilterMaxExC
5 Bitmap ()Example for C#=Bitmap1ExC
5 Bitmap (filename) Example for C#=Bitmap4ExC
5 Bitmap (original) Example for C#=Bitmap2ExC
5 Bitmap (original,newSize) Example for C#=Bitmap5ExC
5 Bitmap (original,width,height) Example for C#=Bitmap8ExC
5 Bitmap (stream) Example for C#=Bitmap3ExC
5 Bitmap (type,resource) Example for C#=Bitmap7ExC
5 Bitmap (width,height) Example for C#=Bitmap6ExC
5 Bitmap (width,height,format) Example for C#=Bitmap10ExC
5 Bitmap (width,height,format,Palette) Example for C#=Bitmap13ExC
5 Bitmap (width,height,format,scan0) Example for C#=Bitmap11ExC
5 Bitmap (width,height,format,scan0,Palette) Example for C#=Bitmap12ExC
5 Bitmap (width,height,g) Example for C#=Bitmap9ExC
5 Bitmap.AddColorHSVRangeToRegion Example for C#=BitmapAddColorHSVRangeToRegionExC
5 Bitmap.AddColorRGBRangeToRegion Example for C#=BitmapAddColorRGBRangeToRegionExC
5 Bitmap.AddColorToRegion Example for C#=BitmapAddColorToRegionExC
5 Bitmap.AddCurveToRegion Example for C#=BitmapAddCurveToRegionExC
5 Bitmap.AddDataToRegion Example for C#=BitmapAddDataToRegionExC
5 Bitmap.AddEllipseToRegion Example for C#=BitmapAddEllipseToRegionExC
5 Bitmap.AddMagicWandToRegion Example for C#=BitmapAddMagicWandToRegionExC
5 Bitmap.AddMaskToRegion Example for C#=BitmapAddMaskToRegionExC
5 Bitmap.AddPolygonToRegion Example for C#=BitmapAddPolygonToRegionExC
5 Bitmap.AddRectangleToRegion Example for C#=BitmapAddRectangleToRegionExC
5 Bitmap.AddRoundRectangleToRegion Example for C#=BitmapAddRoundRectangleToRegionExC
5 Bitmap.Advise Example for C#=BitmapAdviseExC
5 Bitmap.BitmapSize Example for C#=BitmapBitmapSizeExC
5 Bitmap.Capture Example for C#=BitmapCaptureExC
5 Bitmap.ChangeViewPerspective Example for C#=BitmapChangeViewPerspectiveExC
5 Bitmap.Clear Example for C#=BitmapClearExC
5 Bitmap.Clone (RectangleF,PixelFormat) Example for C#=BitmapClone2ExC
5 Bitmap.Clone(Rectangle,PixelFormat) Example for C#=BitmapClone1ExC
5 Bitmap.Compression Example for C#=BitmapCompressionExC
5 Bitmap.Copy Example for C#=BitmapCopyExC
5 Bitmap.DrawAttributes Example for C#=BitmapDrawAttributesExC
5 Bitmap.DrawProperties Example for C#=BitmapDrawPropertiesExC
5 Bitmap.FromHicon Example for C#=BitmapFromHiconExC
5 Bitmap.FromResource Example for C#=BitmapFromResourceExC
5 Bitmap.GetClipSegments Example for C#=BitmapGetClipSegmentsExC
5 Bitmap.GetHbitmap ()Example for C#=BitmapGetHbitmap1ExC
5 Bitmap.GetHbitmap (Color) Example for C#=BitmapGetHbitmap2ExC
5 Bitmap.GetHicon Example for C#=BitmapGetHiconExC
5 Bitmap.GetPixel Example for C#=BitmapGetPixelExC
5 Bitmap.GetRegion ()Example for C#=BitmapGetRegion1ExC
5 Bitmap.GetRegion (RegionXForm) Example for C#=BitmapGetRegion2ExC
5 Bitmap.GetRegionArea Example for C#=BitmapGetRegionAreaExC
5 Bitmap.HasRegion Example for C#=BitmapHasRegionExC
5 Bitmap.LockBits Example for C#=BitmapLockBitsExC
5 Bitmap.MakeTransparent () Example for C#=BitmapMakeTransparent1ExC
5 Bitmap.MakeTransparent (Color) Example for C#=BitmapMakeTransparent2ExC
5 Bitmap.PaintContrast Example for C#=BitmapPaintContrastExC
5 Bitmap.PaintGamma Example for C#=BitmapPaintGammaExC
5 Bitmap.PaintIntensity Example for C#=BitmapPaintIntensityExC
5 Bitmap.Paste Example for C#=BitmapPasteExC
5 Bitmap.PointFromBitmap Example for C#=BitmapPointFromBitmapExC
5 Bitmap.PointToBitmap Example for C#=BitmapPointToBitmapExC
5 Bitmap.RectFromBitmap Example for C#=BitmapRectFromBitmapExC
5 Bitmap.RectToBitmap Example for C#=BitmapRectToBitmapExC
5 Bitmap.SetResolution Example for C#=BitmapSetResolutionExC
5 Bitmap.Signed Example for C#=BitmapSignedExC
5 Bitmap.Transparent Example for C#=BitmapTransparentExC
5 Bitmap.ViewPerspective Example for C#=BitmapViewPerspectiveExC
5 Bitmap.WindowLevelFillLUT Example for C#=BitmapWindowLevelFillLUTExC
5 BorderRemoveEventArgs.Region Example for C#=BorderRemoveEventArgsRegionExC
5 Capability.CapabilityType Example for C#=CapabilityCapabilityTypeExC
5 ChangeDialog Example for C#=ChangeDialogExC
5 Codecs Example for C#=CodecsExC
5 Codecs.DeletePage Example for C#=CodecsDeletePageExC
5 Codecs.Dispose Example for C#=CodecsDisposeExC
5 Codecs.GetInfo (mem, page) Example for C#=CodecsGetInfoExC
5 Codecs.IgnoreCodecs Example for C#=CodecsIgnoreCodecsExC
5 Codecs.InfoHasAlpha Example for C#=CodecsInfoHasAlphaExC
5 Codecs.InfoIFD Example for C#=CodecsInfoIFDExC
5 Codecs.InfoViewPerspective Example for C#=CodecsInfoViewPerspectiveExC
5 Codecs.J2KOptions Example for C#=CodecsJ2KOptionsExC
5 Codecs.Load (image, buffer, format, page, pages) Example for C#=CodecsLoad1ExC
5 Codecs.Load (image, stream, format, page, pages) Example for C#=CodecsLoad2ExC
5 Codecs.LoadCompressed Example for C#=CodecsLoadCompressedExC
5 Codecs.LoadInfoImageFormat Example for C#=CodecsLoadInfoImageFormatExC
5 Codecs.LoadResolutionSize Example for C#=CodecsLoadResolutionSizeExC
5 Codecs.LoadSigned Example for C#=CodecsLoadSignedExC
5 Codecs.LoadStamp Example for C#=CodecsLoadStampEXC
5 Codecs.LoadUseViewPerspective Example for C#=CodecsLoadUseViewPerspectiveExC
5 Codecs.OnFilePage Example for C#=CodecsOnFilePageExC
5 Codecs.OnProgressStatus Example for C#=CodecsOnProgressStatusExC
5 Codecs.OnReadyStateChange Example for C#=CodecsOnReadyStateChangeExC
5 Codecs.PCDOptions Example for C#=CodecsPCDOptionsExC
5 Codecs.PDFOptions Example for C#=CodecsPDFOptionsExC
5 Codecs.PreferredLoadImageFormat Example for C#=CodecsPreferredLoadImageFormatExC
5 Codecs.ProgressivePasses Example for C#=CodecsProgressivePassesExC
5 Codecs.RasterizeOptions Example for C#=CodecsRasterizeOptionsExC
5 Codecs.ReadComment Example for C#=CodecsReadCommentExC
5 Codecs.ReadExtensions Example for C#=CodecsReadExtensionsExC
5 Codecs.ReadyState Example for C#=CodecsReadyStateExC
5 Codecs.Save (image, fileName, imageFormat, pixelFormat, modify) Example for C#=CodecsSave1ExC
5 Codecs.Save (image, stream, imageFormat, pixelFormat, modify) Example for C#=CodecsSave2ExC
5 Codecs.SaveIFD Example for C#=CodecsSaveIFDExC
5 Codecs.SaveInterlaced Example for C#=CodecsSaveInterlacedExC
5 Codecs.SaveLSB Example for C#=CodecsSaveLSBExC
5 Codecs.SavePad4 Example for C#=CodecsSavePad4ExC
5 Codecs.SavePage Example for C#=CodecsSavePageExC
5 Codecs.SaveResolutionSize Example for C#=CodecsSaveResolutionSizeExC
5 Codecs.SaveTileSize Example for C#=CodecsSaveTileSizeExC
5 Codecs.SetSaveQFactor Example for C#=CodecsSetSaveQFactorExC
5 Codecs.SetTag Example for C#=CodecsSetTagExC
5 Codecs.StartFeedLoad Example for C#=CodecsStartFeedLoadExC
5 Codecs.WMFOptions Example for C#=CodecsWMFOptionsExC
5 ColorPalette () Example for C#=ColorPaletteExC
5 ColorPalette.Clone Example for C#=ColorPaletteCloneExC
5 ColorPalette.FromHpalette Example for C#=ColorPaletteFromHpaletteExC
5 ColorPalette.GetHpalette Example for C#=ColorPaletteGetHpaletteExC
5 ColorResDialog Example for C#=ColorResDialogExC
5 Comment.Clone Example for C#=CommentCloneExC
5 CommonDialog.Font Example for C#=CommonDialogFontExC
5 CommonDialog.OnHelp Example for C#=CommonDialogOnHelpExC
5 CommonDialog.ShowDialog Example for C#=CommonDialogShowDialogExC
5 ContourDialog Example for C#=ContourDialogExC
5 Curve Constructor Example for C#=CurveConstructorExC
5 Curve.Clone Example for C#=CurveCloneExC
5 DocCleanFilter.LEADRegion Example for C#=DocCleanFilterLEADRegionExC
5 DocCleanFilter.Region Example for C#=DocCleanFilterRegionExC
5 DotRemoveEventArgs.Region Example for C#=DotRemoveEventArgsRegionExC
5 Draw(Graphics g, int x, int y, int width, int height)  Example for C#=ImageDraw11ExC
5 EmbossDialog Example for C#=EmbossDialogExC
5 EnumerationCapability Example for C#=EnumerationCapabilityExC
5 EnumerationCapability.SetCurrentIndex Example for C#=EnumerationCapabilitySetCurrentIndexExC
5 Exception () Example for C#=Exception1ExC
5 Exception (code) Example for C#=Exception5ExC
5 Exception (message) Example for C#=Exception2ExC
5 Exception (message, code) Example for C#=Exception4ExC
5 Exception (message, innerException) example for C#=Exception3ExC
5 Exception.Code example for C#=ExceptionCodeExC
5 Extensions.GetAudio example for C#=ExtensionsGetAudioExC
5 Extensions.LoadStamp example for C#=ExtensionsLoadStampExC
5 FilterDialog Example for C#=FilterDialogExC
5 Frame Example for C#=FrameExC
5 Frame.Clone Example for C#=FrameCloneExC
5 GammaDialog Example for C#=GammaDialogExC
5 HalfToneDialog Example for C#=HalfToneDialogExC
5 HolePunchRemoveEventArgs.Region Example for C#=HolePunchRemoveEventArgsRegionExC
5 Image.ActiveFrameIndex Example for C#=ImageActiveFrameIndexExC
5 Image.Clone Example for C#=ImageCloneExC
5 Image.DeleteFrame Example for C#=ImageDeleteFrameExC
5 Image.Dispose Example for C#=ImageDisposeExC
5 Image.Draw (Graphics  g, float x, float y, float width, float height) Example for C#=ImageDraw12ExC
5 Image.Draw (Graphics g, float x, float y) Example for C#=ImageDraw6ExC
5 Image.Draw (Graphics g, float x, float y, RectangleF srcRect, GraphicsUnit srcUnit)  Exampl=ImageDraw10ExC
5 Image.Draw (Graphics g, int x, int y) Example for C#=ImageDraw5ExC
5 Image.Draw (Graphics g, int x, int y, Rectangle srcRect, GraphicsUnit srcUnit) Example for=ImageDraw9ExC
5 Image.Draw (Graphics g, Point point) Example for C#=ImageDraw1ExC
5 Image.Draw (Graphics g, PointF point) Example for C#=ImageDraw2ExC
5 Image.Draw (Graphics g, Rectangle destRect, float srcX, float srcY, float srcWidth, float s=ImageDraw14ExC
5 Image.Draw (Graphics g, Rectangle destRect, float srcX, float srcY, float srcWidth, float s=ImageDraw16ExC
5 Image.Draw (Graphics g, Rectangle destRect, float srcX, float srcY, float srcWidth, float s=ImageDraw18ExC
5 Image.Draw (Graphics g, Rectangle destRect, float srcX, float srcY, float srcWidth, float s=ImageDraw20ExC
5 Image.Draw (Graphics g, Rectangle destRect, int srcX, int srcY, int srcWidth, int srcHeight=ImageDraw13ExC
5 Image.Draw (Graphics g, Rectangle destRect, int srcX, int srcY, int srcWidth, int srcHeight=ImageDraw15ExC
5 Image.Draw (Graphics g, Rectangle destRect, int srcX, int srcY, int srcWidth, int srcHeight=ImageDraw17ExC
5 Image.Draw (Graphics g, Rectangle destRect, int srcX, int srcY, int srcWidth, int srcHeight=ImageDraw19ExC
5 Image.Draw (Graphics g, Rectangle destRect, Rectangle srcRect, GraphicsUnit srcUnit) Exampl=ImageDraw7ExC
5 Image.Draw (Graphics g, Rectangle rect) Example for C#=ImageDraw3ExC
5 Image.Draw (Graphics g, RectangleF destRect, RectangleF srcRect, GraphicsUnit rcUnit) Examp=ImageDraw8ExC
5 Image.Draw (Graphics g, RectangleF rect) Example for C#=ImageDraw4ExC
5 Image.DrawMethod Example for C#=ImageDrawMethodExC
5 Image.EnableEvents Example for C#=ImageEnableEventsExC
5 Image.FireEvent Example for C#=ImageFireEventExC
5 Image.Flags Example for C#=ImageFlagsExC
5 Image.FrameDimensionsList Example for C#=ImageFrameDimensionsListExC
5 Image.FromFile Example for C#=ImageFromFileExC
5 Image.FromHbitmap (IntPtr) Example for C#=ImageFromHbitmap1ExC
5 Image.FromHbitmap (IntPtr, IntPtr) Example for C#=ImageFromHbitmap2ExC
5 Image.FromImage Example for C#=ImageFromImageExC
5 Image.FromLEADBitmapHandle Example for C#=ImageFromLEADBitmapHandleExC
5 Image.FromStream Example for C#=ImageFromStreamExC
5 Image.GetBounds Example for C#=ImageGetBoundsExC
5 Image.GetPixelFormatSize Example for C#=ImageGetPixelFormatSizeExC
5 Image.GetThumbnailImage Example for C#=ImageGetThumbnailImageExC
5 Image.GetThumbnailImageAbort Delegate Example for C#=ImageGetThumbnailImageAbortDelegateExC
5 Image.GetType Example for C#=ImageGetTypeExC
5 Image.HorizontalResolution Example for C#=ImageHorizontalResolutionExC
5 Image.InsertFrame Example for C#=ImageInsertFrameExC
5 Image.IsAlphaPixelFormat Example for C#=ImageIsAlphaPixelFormatExC
5 Image.IsCanonicalPixelFormat Example for C#=ImageIsCanonicalPixelFormatExC
5 Image.IsExtendedPixelFormat Example for C#=ImageIsExtendedPixelFormatExC
5 Image.IsIndexedPixelFormat Example for C#=ImageIsIndexedPixelFormatExC
5 Image.Link Example for C#=ImageLinkExC
5 Image.Linked Example for C#=ImageLinkedExC
5 Image.OnChange Example for C#=ImageOnChangeExC
5 Image.Palette Example for C#=ImagePaletteExC
5 Image.PhysicalDimension Example for C#=ImagePhysicalDimensionExC
5 Image.PixelFormat Example for C#=ImagePixelFormatExC
5 Image.RawFormat Example for C#=ImageRawFormatExC
5 Image.RotateFlip Example for C#=ImageRotateFlipExC
5 Image.Save (Stream,ImageFormat) Example for C#=ImageSave2ExC
5 Image.Save (String) Example for C#=ImageSave1ExC
5 Image.Save (String,ImageFormat) Example for C#=ImageSave3ExC
5 Image.SelectActiveFrame Example for C#=ImageSelectActiveFrameExC
5 Image.Size Example for C#=ImageSizeExC
5 Image.ToImage Example for C#=ImageToImageExC
5 Image.Width Example for C#=ImageWidthExC
5 ImageProcessing Example for C#=ImageProcessingExC
5 ImageProcessing.Add Example for C#=ImageProcessingAddExC
5 ImageProcessing.AddNoise Example for C#=ImageProcessingAddNoiseExC
5 ImageProcessing.AlphaBlend Example for C#=ImageProcessingAlphaBlendExC
5 ImageProcessing.AntiAlias Example for C#=ImageProcessingAntiAliasExC
5 ImageProcessing.AutoTrim Example for C#=ImageProcessingAutoTrimExC
5 ImageProcessing.Average Example for C#=ImageProcessingAverageExC
5 ImageProcessing.BalanceColors Example for C#=ImageProcessingBalanceColorsExC
5 ImageProcessing.Binary Example for C#=ImageProcessingBinaryExC
5 ImageProcessing.BorderRemove Example for C#=ImageProcessingBorderRemoveExC
5 ImageProcessing.ColorMerge Example for C#=ImageProcessingColorMergeExC
5 ImageProcessing.ColorRes Example for C#=ImageProcessingColorResExC
5 ImageProcessing.ColorResList Example for C#=ImageProcessingColorResListExC
5 ImageProcessing.Combine Example for C#=ImageProcessingCombineExC
5 ImageProcessing.Compress Example for C#=ImageProcessingCompressExC
5 ImageProcessing.Contour Example for C#=ImageProcessingContourExC
5 ImageProcessing.Contrast Example for C#=ImageProcessingContrastExC
5 ImageProcessing.ConvertSignedToUnsigned Example for C#=ImageProcessingConvertSignedToUnsignedExC
5 ImageProcessing.ConvertToColoredGray Example for C#=ImageProcessingConvertToColoredGrayExC
5 ImageProcessing.CreateFadedMask Example for C#=ImageProcessingCreateFadedMaskExC
5 ImageProcessing.Deskew Example for C#=ImageProcessingDeskewExC
5 ImageProcessing.Despeckle Example for C#=ImageProcessingDespeckleExC
5 ImageProcessing.DotRemove Example for C#=ImageProcessingDotRemoveExC
5 ImageProcessing.EdgeDetector Example for C#=ImageProcessingEdgeDetectorExC
5 ImageProcessing.Emboss Example for C#=ImageProcessingEmbossExC
5 ImageProcessing.FastRotate Example for C#=ImageProcessingFastRotateExC
5 ImageProcessing.Fill Example for C#=ImageProcessingFillExC
5 ImageProcessing.Flip Example for C#=ImageProcessingFlipExC
5 ImageProcessing.GammaCorrect Example for C#=ImageProcessingGammaCorrectExC
5 ImageProcessing.Gaussian Example for C#=ImageProcessingGaussianExC
5 ImageProcessing.GetAutoTrimRectangle Example for C#=ImageProcessingGetAutoTrimRectangleExC
5 ImageProcessing.GetColorCount Example for C#=ImageProcessingGetColorCountExC
5 ImageProcessing.GetHistogram Example for C#=ImageProcessingGetHistogramExC
5 ImageProcessing.GetRow (Image image, int row)Example for C#=ImageProcessingGetRow2ExC
5 ImageProcessing.GetRow (Image image, int row, byte buffer) Example for C#=ImageProcessingGetRow1ExC
5 ImageProcessing.GetUserLookupTable Example for C#=ImageProcessingGetUserLookupTableExC
5 ImageProcessing.GrayScale (Image image, int redFact, int greenFact,int blueFact) Example fo=ImageProcessingGrayScale2ExC
5 ImageProcessing.GrayScale (Image image, PixelFormat pixelFormat) Example for C#=ImageProcessingGrayScale1ExC
5 ImageProcessing.HalfTone Example for C#=ImageProcessingHalfToneExC
5 ImageProcessing.HistoContrast Example for C#=ImageProcessingHistoContrastExC
5 ImageProcessing.HistoEqualize Example for C#=ImageProcessingHistoEqualizeExC
5 ImageProcessing.HolePunchRemove Example for C#=ImageProcessingHolePunchRemoveExC
5 ImageProcessing.Hue Example for C#=ImageProcessingHueExC
5 ImageProcessing.Intensity Example for C#=ImageProcessingIntensityExC
5 ImageProcessing.IntensityDetect Example for C#=ImageProcessingIntensityDetectExC
5 ImageProcessing.Invert Example for C#=ImageProcessingInvertExC
5 ImageProcessing.InvertedText Example for C#=ImageProcessingInvertedTextExC
5 ImageProcessing.LineProfile Example for C#=ImageProcessingLineProfileExC
5 ImageProcessing.LineRemove Example for C#=ImageProcessingLineRemoveExC
5 ImageProcessing.Max Example for C#=ImageProcessingMaxExC
5 ImageProcessing.Median Example for C#=ImageProcessingMedianExC
5 ImageProcessing.Min Example for C#=ImageProcessingMinExC
5 ImageProcessing.Mosaic Example for C#=ImageProcessingMosaicExC
5 ImageProcessing.MotionBlur Example for C#=ImageProcessingMotionBlurExC
5 ImageProcessing.Oilify Example for C#=ImageProcessingOilifyExC
5 ImageProcessing.OnProgress Example for C#=ImageProcessingOnProgressExC
5 ImageProcessing.Picturize (Image image, Size cellSize, int lightness, Image[] images()) Exa=ImageProcessingPicturize1ExC
5 ImageProcessing.Picturize(Image image, String directory, PicturizeConstants flags,Size cell=ImageProcessingPicturize2ExC
5 ImageProcessing.Posterize Example for C#=ImageProcessingPosterizeExC
5 ImageProcessing.RemapHue Example for C#=ImageProcessingRemapHueExC
5 ImageProcessing.RemapIntensity Example for C#=ImageProcessingRemapIntensityExC
5 ImageProcessing.RemoveRedEye Example for C#=ImageProcessingRemoveRedEyeExC
5 ImageProcessing.ResizeRegion Example for C#=ImageProcessingResizeRegionExC
5 ImageProcessing.Reverse Example for C#=ImageProcessingReverseExC
5 ImageProcessing.Rotate Example for C#=ImageProcessingRotateExC
5 ImageProcessing.Saturation Example for C#=ImageProcessingSaturationExc
5 ImageProcessing.Sharpen Example for C#=ImageProcessingSharpenExC
5 ImageProcessing.Shear Example for C#=ImageProcessingShearExC
5 ImageProcessing.Size Example for C#=ImageProcessingSizeExC
5 ImageProcessing.Smooth Example for C#=ImageProcessingSmoothExC
5 ImageProcessing.Solarize Example for C#=ImageProcessingSolarizeExc
5 ImageProcessing.Spatial Example for C#=ImageProcessingSpatialExc
5 ImageProcessing.StretchIntensity Example for C#=ImageProcessingStretchIntensityExC
5 ImageProcessing.SwapColors Example for C#=ImageProcessingSwapColorsExC
5 ImageProcessing.Underlay Example for C#=ImageProcessingUnderlayExC
5 ImageProcessing.UnsharpMask Example for C#=ImageProcessingUnsharpMaskExC
5 ImageProcessing.WindowLevel Example for C#=ImageProcessingWindowLevelExC
5 Info Example for C#=InfoExC
5 InvertedTextEventArgs.Region Example for C#=InvertedTextEventArgsRegionExC
5 LineRemoveEventArgs.Region Example for C#=LineRemoveEventArgsRegionExC
5 NoiseDialog Constructor Example for C#=NoiseDialogConstructorExC
5 OneValueCapability Example for C#=OneValueCapabilityExC
5 OpenFileDialog Example for C#=OpenFileDialogExC
5 OpenFileDialog.ShowResolutionDialog Example for C#=OpenFileDialogShowResolutionDialogExC
5 OpenFileDialog.Thumbnail Example for C#=OpenFileDialogThumbnailExC
5 PDFOptions.SaveUseDPI Example for C#=PDFOptionsSaveUseDPIExC
5 PictureBox () Example for C#=PictureBoxExC
5 PictureBox.BorderStyle Example for C#=PictureBoxBorderStyleExC
5 PictureBox.ClientToPicture Example for C#=PictureBoxClientToPictureExC
5 PictureBox.LEADImage Example for C#=PictureBoxLEADImageExC
5 PictureBox.LeftButtonInteractiveMode Example for C#=PictureBoxLeftButtonInteractiveModeExC
5 PictureBox.PictureToClient Example for C#=PictureBoxPictureToClientExC
5 PictureBox.RightButtonInteractiveMode Example for C#=PictureBoxRightButtonInteractiveModeExC
5 PictureBox.SourceRectangle Example for C#=PictureBoxSourceRectangleExC
5 PictureBox.SystemImage Example for C#=PictureBoxSystemImageExC
5 PictureBox.Zoom Example for C#=PictureBoxZoomExC
5 PictureBox.ZoomToRect Example for C#=PictureBoxZoomToRectExC
5 PreviewCommonDialog.Image Example for C#=PreviewCommonDialogImageExC
5 ProgressEventArgs () Example for C#=ProgressEventArgs1ExC
5 ProgressEventArgs (int percentage) Example for C#=ProgressEventArgs2ExC
5 ProgressEventArgs.Canceled Example for C#=ProgressEventArgsCanceledExC
5 RangeCapability Example for C#=RangeCapabilityExC
5 RangeDialog Constructor Example for C#=RangeDialogConstructorExC
5 RegionXForm Constructor (ViewPerspective) Example for C#=RegionXFormConstructor(ViewPerspective)ExC
5 RegionXForm.Default Example for C#=RegionXFormDefaultExC
5 RegionXForm.ViewPerspective Example for C#=RegionXFormViewPerspectiveExC
5 RubberBand Constructor Example for C#=RubberBandConstructorExC
5 RubberBand.ClipRectangle Example for C#=RubberBandClipRectangleExC
5 RubberBand.Enabled Example for C#=RubberBandEnabledExC
5 RubberBand.OnTracking Example for C#=RubberBandOnTrackingExC
5 RubberBand.Owner Example for C#=RubberBandOwnerExC
5 RubberBand[MouseButtons] Indexer Example for C#=RubberBandMouseButtonsIndexerExC
5 RubberBandEventArgs () Example for C#=RubberBandEventArgs1ExC
5 RubberBandEventArgs (status, button, rect) Example for C#=RubberBandEventArgs2ExC
5 SaveFileDialog Example for C#=SaveFileDialogExC
5 SaveFileDialog.BitsPerPixel Example for C#=SaveFileDialogBitsPerPixelExC
5 SaveFileDialog.PixelFormat Example for C#=SaveFileDialogPixelFormatExC
5 SizeDialog Example for C#=SizeDialogExC
5 SmoothEventArgs.BumpOrNick Example for C#=SmoothEventArgsBumpOrNickExC
5 SpatialFilter.Dimension Example for C#=SpatialFilterDimensionExC
5 StringConstants Enumeration Example for C#=StringConstantsEnumerationExC
5 Support.Unlock example for C#=SupportUnlockExC
5 Tag.Clone Example for C#=TagCloneExC
5 Tag.Id Example for C#=TagIdExC
5 TemplateEventArgs Example for C#=TemplateEventArgsExC
5 Twain.Acquire Example for C#=TwainAcquireExC
5 Twain.EndSession Example for C#=TwainEndSessionExC
5 Twain.ExtImageInfo Example for C#=TwainExtImageInfoExC
5 Twain.GetCapabilitiesCount Example for C#=TwainGetCapabilitiesCountExC
5 Twain.GetCapability (CapabilityConstants cap, GetCapabilityConstants flags) Example for C#=TwainGetCapabilityExC
5 Twain.GetSourceCount Example for C#=TwainGetSourceCountExC
5 Twain.InitSession Example for C#=TwainInitSessionExC
5 Twain.LoadTemplate Example for C#=TwainLoadTemplateExC
5 Twain.ManufacturerName Example for C#=TwainManufacturerNameExC
5 Twain.QueryCapability Example for C#=TwainQueryCapabilityExC
5 Twain.SaveTemplate Example for C#=TwainSaveTemplateExC
5 Twain.SelectSource Example for C#=TwainSelectSourceExC
5 Twain.SetAcquireSourceName Example for C#=TwainSetAcquireSourceNameExC
5 Twain.SetCapability Example for C#=TwainSetCapabilityExC
5 Twain.ShowTemplateDlg Example for C#=TwainShowTemplateDlgExC
5 WindowLevelDialog Example for C#=WindowLevelDialogExC
2 Properties, Methods, Events, etc.
3 AcquirePageEventArgs
4 AcquirePageEventArgs Constructor=AcquirePageEventArgsconstructor
4 AcquirePageEventArgs.Image Property=AcquirePageEventArgsImageproperty
3 AngleDialog
4 AngleDialog Constructor=AngleDialogConstructor
4 AngleDialog.Angle Property=AngleDialogAngleproperty
4 AngleDialog.AngleChecked Property=AngleDialogAngleCheckedproperty
4 AngleDialog.BackColor Property=AngleDialogBackColorproperty
4 AngleDialog.HideBackColor Property=AngleDialogHideBackColorproperty
4 AngleDialog.Rotate Property=AngleDialogRotateproperty
4 AngleDialog.ShowRotateOptions Property=AngleDialogShowRotateOptionsproperty
4 AngleDialog.Type Property=AngleDialogTypeproperty
4 AngleDialog.UseSysColorDlg Property=AngleDialogUseSysColorDlgproperty
4 DialogTypeConstants Enumeration=DialogTypeConstants
3 ArrayCapability
4 ArrayCapability [index] Indexer=ArrayCapabilityindexIndexer
4 ArrayCapability Constructor=ArrayCapabilityConstructor
4 ArrayCapability.GetItemType Method=ArrayCapabilityGetItemTypemethod
4 ArrayCapability.GetNumberOfItems Method=ArrayCapabilityGetNumberOfItemsmethod
4 ArrayCapability.GetValue Method=ArrayCapabilityGetValuemethod
4 ArrayCapability.Length Property=ArrayCapabilityLengthproperty
4 ArrayCapability.SetItemType Method=ArrayCapabilitySetItemTypemethod
4 ArrayCapability.SetValue Method=ArrayCapabilitySetValuemethod
3 BinaryFilter
4 BinaryFilter Constructor=BinaryFilterConstructor
4 BinaryFilter.Dimension Property=BinaryFilterDimensionproperty
4 BinaryFilter.DontCare Property=BinaryFilterDontCareproperty
4 BinaryFilter.Item Indexer=BinaryFilterItemIndexer
4 BinaryFilter.Max Property=BinaryFilterMaxproperty
4 BinaryFilter.SetType Method=BinaryFilterSetTypemethod
4 BinaryFilter.TypeConstants Enumeration=BinaryFilterTypeConstants
3 Bitmap
4 Bitmap Constructor=BitmapConstructor
4 Bitmap.AddColorHSVRangeToRegion Method=BitmapAddColorHSVRangeToRegionmethod
4 Bitmap.AddColorRGBRangeToRegion Method=BitmapAddColorRGBRangeToRegionmethod
4 Bitmap.AddColorToRegion Method=BitmapAddColorToRegionmethod
4 Bitmap.AddCurveToRegion Method=BitmapAddCurveToRegionmethod
4 Bitmap.AddDataToRegion Method=BitmapAddDataToRegionmethod
4 Bitmap.AddEllipseToRegion Method=BitmapAddEllipseToRegionmethod
4 Bitmap.AddMagicWandToRegion Method=BitmapAddMagicWandToRegionmethod
4 Bitmap.AddMaskToRegion Method=BitmapAddMaskToRegionmethod
4 Bitmap.AddPolygonToRegion Method=BitmapAddPolygonToRegionmethod
4 Bitmap.AddRectangleToRegion Method=BitmapAddRectangleToRegionmethod
4 Bitmap.AddRegion Method=BitmapAddRegionmethod
4 Bitmap.AddRoundRectangleToRegion Method=BitmapAddRoundRectangleToRegionmethod
4 Bitmap.Advise Method=BitmapAdvisemethod
4 Bitmap.BitmapSize Property=BitmapBitmapSizeproperty
4 Bitmap.CanPaste Method=BitmapCanPastemethod
4 Bitmap.Capture Method=BitmapCapturemethod
4 Bitmap.ChangeViewPerspective Method=BitmapChangeViewPerspectivemethod
4 Bitmap.Clear Method=BitmapClearmethod
4 Bitmap.Clone Method=BitmapClonemethod
4 Bitmap.Compression Enumeration=BitmapCompressionEnumeration
4 Bitmap.Compression Property=BitmapCompressionproperty
4 Bitmap.Copy Method=BitmapCopymethod
4 Bitmap.CopyConstants Enumeration=BitmapCopyConstants
4 Bitmap.DrawAttributes Property=BitmapDrawAttributesproperty
4 Bitmap.DrawAttributesConstants Enumeration=BitmapDrawAttributesConstants
4 Bitmap.DrawProperties Property=BitmapDrawPropertiesproperty
4 Bitmap.DrawPropertiesConstants Enumeration=BitmapDrawPropertiesConstants
4 Bitmap.FromHicon Method=BitmapFromHiconmethod
4 Bitmap.FromResource Method=BitmapFromResourcemethod
4 Bitmap.GetClipSegments Method=BitmapGetClipSegmentsmethod
4 Bitmap.GetHbitmap Method=BitmapGetHbitmapmethod
4 Bitmap.GetHicon Method=BitmapGetHiconmethod
4 Bitmap.GetMinMaxBits Method=BitmapGetMinMaxBitsmethod
4 Bitmap.GetMinMaxVal Method=BitmapGetMinMaxValmethod
4 Bitmap.GetPixel Method=BitmapGetPixelmethod
4 Bitmap.GetRegion Method=BitmapGetRegionmethod
4 Bitmap.GetRegionArea Method=BitmapGetRegionAreamethod
4 Bitmap.GetRegionData Method=BitmapGetRegionDatamethod
4 Bitmap.HasRegion Property=BitmapHasRegionproperty
4 Bitmap.LevelHighBit Property=BitmapLevelHighBitproperty
4 Bitmap.LevelLowBit Property=BitmapLevelLowBitproperty
4 Bitmap.LevelLUT Property=BitmapLevelLUTproperty
4 Bitmap.LockBits Method=BitmapLockBitsmethod
4 Bitmap.MakeTransparent Method=BitmapMakeTransparentmethod
4 Bitmap.MaxBit Property=BitmapMaxBitproperty
4 Bitmap.MaxRegionFrameIndex Property=BitmapMaxRegionFrameIndexproperty
4 Bitmap.MaxVal Property=BitmapMaxValproperty
4 Bitmap.MinBit Property=BitmapMinBitproperty
4 Bitmap.MinVal Property=BitmapMinValproperty
4 Bitmap.PaintContrast Property=BitmapPaintContrastproperty
4 Bitmap.PaintGamma Property=BitmapPaintGammaproperty
4 Bitmap.PaintIntensity Property=BitmapPaintIntensityproperty
4 Bitmap.Paste Method=BitmapPastemethod
4 Bitmap.PointFromBitmap Method=BitmapPointFromBitmapmethod
4 Bitmap.PointToBitmap Method=BitmapPointToBitmapmethod
4 Bitmap.RectFromBitmap Method=BitmapRectFromBitmapmethod
4 Bitmap.RectToBitmap Method=BitmapRectToBitmapmethod
4 Bitmap.RegionColor Property=BitmapRegionColorproperty
4 Bitmap.RegionFrameIndex Property=BitmapRegionFrameIndexproperty
4 Bitmap.RegionRectangle Property=BitmapRegionRectangleproperty
4 Bitmap.SetPixel Method=BitmapSetPixelmethod
4 Bitmap.SetResolution Method=BitmapSetResolutionmethod
4 Bitmap.Signed Property=BitmapSignedproperty
4 Bitmap.Transparent Property=BitmapTransparentproperty
4 Bitmap.TransparentColor Property=BitmapTransparentColorproperty
4 Bitmap.UnAdvise Method=BitmapUnAdvisemethod
4 Bitmap.UnlockBits Method=BitmapUnlockBitsmethod
4 Bitmap.ViewPerspective Property=BitmapViewPerspectiveproperty
4 Bitmap.WindowLevelFillLUT Method=BitmapWindowLevelFillLUTmethod
3 BitmapData
4 BitmapData Constructor=BitmapDataConstructor
4 BitmapData.Height Property=BitmapDataHeightproperty
4 BitmapData.PixelFormat Property=BitmapDataPixelFormatproperty
4 BitmapData.Reserved Property=BitmapDataReservedproperty
4 BitmapData.Scan0 Property=BitmapDataScan0property
4 BitmapData.Stride Property=BitmapDataStrideproperty
4 BitmapData.Width Property=BitmapDataWidthproperty
3 BorderRemoveEventArgs
4 BorderRemoveEventArgs Constructor=BorderRemoveEventArgsConstructor
4 BorderRemoveEventArgs.Border Property=BorderRemoveEventArgsBorderproperty
4 BorderRemoveEventArgs.BoundingRectangle Property=BorderRemoveEventArgsBoundingRectangleproperty
4 BorderRemoveEventArgs.Region Property=BorderRemoveEventArgsRegionproperty
4 BorderRemoveEventArgs.Status Property=BorderRemoveEventArgsStatusproperty
3 BorderRemoveFilter
4 BorderRemoveFilter Constructor=BorderRemoveFilterconstructor
4 BorderRemoveFilter.Border Property=BorderRemoveFilterBorderproperty
4 BorderRemoveFilter.Flags Property=BorderRemoveFilterFlagsproperty
4 BorderRemoveFilter.FlagsConstants Enumeration=BorderRemoveFilterFlagsConstants
4 BorderRemoveFilter.Percent Property=BorderRemoveFilterPercentproperty
4 BorderRemoveFilter.Variance Property=BorderRemoveFilterVarianceproperty
4 BorderRemoveFilter.WhiteNoiseLength Property=BorderRemoveFilterWhiteNoiseLengthproperty
3 Capability
4 Capability Constructor=CapabilityConstructor
4 Capability.CapabilityType Property=CapabilityCapabilityTypeproperty
4 Capability.ContainerType Property=CapabilityContainerTypeproperty
4 Capability.GetItemType Method=CapabilityGetItemTypemethod
4 Capability.SetItemType Method=CapabilitySetItemTypemethod
3 CapabilityValues
4 CapabilityValues.AlarmConstants Enumeration=CapabilityValuesAlarmConstants
4 CapabilityValues.AudioFileFormatConstants Enumeration=CapabilityValuesAudioFileFormatConstants
4 CapabilityValues.BarCodeRotationConstants Enumeration=CapabilityValuesBarCodeRotationConstants
4 CapabilityValues.BarCodeSearchModeConstants Enumeration=CapabilityValuesBarCodeSearchModeConstants
4 CapabilityValues.BarCodeTypeConstants Enumeration=CapabilityValuesBarCodeTypeConstants
4 CapabilityValues.BitDepthReductionConstants Enumeration=CapabilityValuesBitDepthReductionConstants
4 CapabilityValues.BitOrderConstants Enumeration=CapabilityValuesBitOrderConstants
4 CapabilityValues.ClearBufferConstants Enumeration=CapabilityValuesClearBufferConstants
4 CapabilityValues.CompressionConstants Enumeration=CapabilityValuesCompressionConstants
4 CapabilityValues.CountryConstants Enumeration=CapabilityValuesCountryConstants
4 CapabilityValues.DeskewConstants Enumeration=CapabilityValuesDeskewConstants
4 CapabilityValues.DeviceEventConstants Enumeration=CapabilityValuesDeviceEventConstants
4 CapabilityValues.DuplexConstants Enumeration=CapabilityValuesDuplexConstants
4 CapabilityValues.FeederAlignmentConstants Enumeration=CapabilityValuesFeederAlignmentConstants
4 CapabilityValues.FeederOrderConstants Enumeration=CapabilityValuesFeederOrderConstants
4 CapabilityValues.FileFormatConstants Enumeration=CapabilityValuesFileFormatConstants
4 CapabilityValues.FileSysConstants Enumeration=CapabilityValuesFileSysConstants
4 CapabilityValues.FileSystemConstants Enumeration=CapabilityValuesFileSystemConstants
4 CapabilityValues.FilterConstants Enumeration=CapabilityValuesFilterConstants
4 CapabilityValues.FlashUsedConstants Enumeration=CapabilityValuesFlashUsedConstants
4 CapabilityValues.FlipRotationConstants Enumeration=CapabilityValuesFlipRotationConstants
4 CapabilityValues.ImageFilterConstants Enumeration=CapabilityValuesImageFilterConstants
4 CapabilityValues.JobControlConstants Enumeration=CapabilityValuesJobControlConstants
4 CapabilityValues.JpegQualityConstants Enumeration=CapabilityValuesJpegQualityConstants
4 CapabilityValues.LanguageConstants Enumeration=CapabilityValuesLanguageConstants
4 CapabilityValues.LightPathConstants Enumeration=CapabilityValuesLightPathConstants
4 CapabilityValues.LightSourceConstants Enumeration=CapabilityValuesLightSourceConstants
4 CapabilityValues.NoiseFilterConstants Enumeration=CapabilityValuesNoiseFilterConstants
4 CapabilityValues.OrientationConstants Enumeration=CapabilityValuesOrientationConstants
4 CapabilityValues.OverScanConstants Enumeration=CapabilityValuesOverScanConstants
4 CapabilityValues.PatchCodeConstants Enumeration=CapabilityValuesPatchCodeConstants
4 CapabilityValues.PixelFlavorConstants Enumeration=CapabilityValuesPixelFlavorConstants
4 CapabilityValues.PixelTypeConstants Enumeration=CapabilityValuesPixelTypeConstants
4 CapabilityValues.PlanarChunkyConstants Enumeration=CapabilityValuesPlanarChunkyConstants
4 CapabilityValues.PowerSupplyConstants Enumeration=CapabilityValuesPowerSupplyConstants
4 CapabilityValues.PrinterConstants Enumeration=CapabilityValuesPrinterConstants
4 CapabilityValues.PrinterModeConstants Enumeration=CapabilityValuesPrinterModeConstants
4 CapabilityValues.SupportedSizesConstants Enumeration=CapabilityValuesSupportedSizesConstants
4 CapabilityValues.UnitsConstants Enumeration=CapabilityValuesUnitsConstants
4 CapabilityValues.XferMechConstants Enumeration=CapabilityValuesXferMechConstants
3 ChangeDialog
4 ChangeDialog Constructor=ChangeDialogConstructor
4 ChangeDialog.Change Property=ChangeDialogChangeproperty
4 ChangeDialog.Process Property=ChangeDialogProcessproperty
4 ChangeDialog.ProcessConstants Enumeration=ChangeDialogProcessConstants
3 ColorPalette
4 ColorPalette Constructor=ColorPaletteConstructor
4 ColorPalette.Clone Method=ColorPaletteClonemethod
4 ColorPalette.Dispose Method=ColorPaletteDisposemethod
4 ColorPalette.Entries Property=ColorPaletteEntriesproperty
4 ColorPalette.Flags Property=ColorPaletteFlagsproperty
4 ColorPalette.FromHpalette Method=ColorPaletteFromHpalettemethod
4 ColorPalette.GetHpalette Method=ColorPaletteGetHpalettemethod
3 Codecs
4 Codecs Constructor=CodecsConstructor
4 Codecs.ClearComment Method=CodecsClearCommentmethod
4 Codecs.ClearTag Method=CodecsClearTagmethod
4 Codecs.DeletePage Method=CodecsDeletePagemethod
4 Codecs.Dispose Method=CodecsDisposemethod
4 Codecs.FeedLoad Method=CodecsFeedLoadmethod
4 Codecs.FilePage Delegate=CodecsFilePagedelegate
4 Codecs.FileSizeWritten Property=CodecsFileSizeWrittenproperty
4 Codecs.GetComment Method=CodecsGetCommentmethod
4 Codecs.GetInfo Method=CodecsGetInfomethod
4 Codecs.GetInfoFlagsConstants Enumeration=CodecsGetInfoFlagsConstants
4 Codecs.GetLoadResolution Method=CodecsGetLoadResolutionmethod
4 Codecs.GetSaveQFactor Method=CodecsGetSaveQFactormethod
4 Codecs.GetTag Method=CodecsGetTagmethod
4 Codecs.IgnoreCodecs Method=CodecsIgnoreCodecsmethod
4 Codecs.ImageDataPath Method=CodecsImageDataPathmethod
4 Codecs.InfoAnimation Property=CodecsInfoAnimationproperty
4 Codecs.InfoAnimationSize Property=CodecsInfoAnimationSizeproperty
4 Codecs.InfoBitsPerPixel Property=CodecsInfoBitsPerPixelproperty
4 Codecs.InfoCompress Property=CodecsInfoCompressproperty
4 Codecs.InfoHasAlpha Property=CodecsInfoHasAlphaproperty
4 Codecs.InfoIFD Property=CodecsInfoIFDproperty
4 Codecs.InfoImageFormat Property=CodecsInfoImageFormatproperty
4 Codecs.InfoPage Property=CodecsInfoPageproperty
4 Codecs.InfoPixelFormat Property=CodecsInfoPixelFormatproperty
4 Codecs.InfoResolutionSize Property=CodecsInfoResolutionSizeproperty
4 Codecs.InfoSize Property=CodecsInfoSizeproperty
4 Codecs.InfoSizeDisk Property=CodecsInfoSizeDiskproperty
4 Codecs.InfoSizeMemory Property=CodecsInfoSizeMemoryproperty
4 Codecs.InfoTotalPages Property=CodecsInfoTotalPagesproperty
4 Codecs.InfoViewPerspective Property=CodecsInfoViewPerspectiveproperty
4 Codecs.IOBufferSize Property=CodecsIOBufferSizeproperty
4 Codecs.IOPalette Property=CodecsIOPaletteproperty
4 Codecs.J2KOptions Property=CodecsJ2KOptionsproperty
4 Codecs.Load Method=CodecsLoadmethod
4 Codecs.LoadCompressed Property=CodecsLoadCompressedproperty
4 Codecs.LoadIFD Property=CodecsLoadIFDproperty
4 Codecs.LoadInfo Delegate=CodecsLoadInfodelegate
4 Codecs.LoadInfoBitsPerPixel Property=CodecsLoadInfoBitsPerPixelproperty
4 Codecs.LoadInfoFlagConstants Enumeration=CodecsLoadInfoFlagConstants
4 Codecs.LoadInfoFlags Property=CodecsLoadInfoFlagsproperty
4 Codecs.LoadInfoImageFormat Property=CodecsLoadInfoImageFormatProperty
4 Codecs.LoadInfoOffset Property=CodecsLoadInfoOffsetproperty
4 Codecs.LoadInfoPixelFormat Property=CodecsLoadInfoPixelFormatproperty
4 Codecs.LoadInfoResolutionSize Property=CodecsLoadInfoResolutionSizeproperty
4 Codecs.LoadInfoSize Property=CodecsLoadInfoSizeproperty
4 Codecs.LoadResolutionSize Property=CodecsLoadResolutionSizeproperty
4 Codecs.LoadSigned Property=CodecsLoadSignedproperty
4 Codecs.LoadStamp Method=CodecsLoadStampMethod
4 Codecs.LoadUseViewPerspective Property=CodecsLoadUseViewPerspectiveproperty
4 Codecs.OnFilePage Event=CodecsOnFilePageevent
4 Codecs.OnLoadInfo Event=CodecsOnLoadInfoevent
4 Codecs.OnProgressStatus Event=CodecsOnProgressStatusevent
4 Codecs.OnReadyStateChange Event=CodecsOnReadyStateChangeevent
4 Codecs.PCDOptions Property=CodecsPCDOptionsproperty
4 Codecs.PDFOptions Property=CodecsPDFOptionsproperty
4 Codecs.PreferredLoadImageFormat Property=CodecsPreferredLoadImageFormatproperty
4 Codecs.PreLoadCodecs Method=CodecsPreLoadCodecsmethod
4 Codecs.ProgressivePasses Property=CodecsProgressivePassesproperty
4 Codecs.ProgressStatus Delegate=CodecsProgressStatusdelegate
4 Codecs.QFactorConstants Enumeration=CodecsQFactorConstants
4 Codecs.RasterizeOptions Property=CodecsRasterizeOptionsproperty
4 Codecs.ReadComment Method=CodecsReadCommentmethod
4 Codecs.ReadExtensions Method=CodecsReadExtensionsmethod
4 Codecs.ReadLoadResolutions Method=CodecsReadLoadResolutionsmethod
4 Codecs.ReadTag Method=CodecsReadTagmethod
4 Codecs.ReadyState Property=CodecsReadyStateproperty
4 Codecs.ReadyStateChange Delegate=CodecsReadyStateChangedelegate
4 Codecs.Save Method=CodecsSavemethod
4 Codecs.SaveIFD Property=CodecsSaveIFDproperty
4 Codecs.SaveInterlaced Property=CodecsSaveInterlacedproperty
4 Codecs.SaveLSB Property=CodecsSaveLSBproperty
4 Codecs.SaveModifyConstants Enumeration=CodecsSaveModifyConstants
4 Codecs.SavePad4 Property=CodecsSavePad4property
4 Codecs.SavePage Property=CodecsSavePageproperty
4 Codecs.SavePageNumberTag Property=CodecsSavePageNumberTagproperty
4 Codecs.SaveResolutionSize Property=CodecsSaveResolutionSizeproperty
4 Codecs.SaveTileSize Property=CodecsSaveTileSizeproperty
4 Codecs.SetComment Method=CodecsSetCommentmethod
4 Codecs.SetLoadResolution Method=codecsSetLoadResolutionmethod
4 Codecs.SetSaveQFactor Method=CodecsSetSaveQFactormethod
4 Codecs.SetTag Method=CodecsSetTagmethod
4 Codecs.StartFeedLoad Method=CodecsStartFeedLoadmethod
4 Codecs.StopFeedLoad Method=CodecsStopFeedLoadmethod
4 Codecs.WMFOptions Property=CodecsWMFOptionsproperty
4 Codecs.WriteComment Method=CodecsWriteCommentmethod
4 Codecs.WriteTag Method=CodecsWriteTagmethod
3 ColorFactor
4 ColorFactor.Blue Property=ColorFactorBlueproperty
4 ColorFactor.Green Property=ColorFactorGreenproperty
4 ColorFactor.Red Property=ColorFactorRedproperty
3 ColorResDialog
4 ColorResDialog Constructor=ColorResDialogConstructor
4 ColorResDialog.ColorResDither Property=ColorResDialogColorResDitherproperty
4 ColorResDialog.ColorResPalette Property=ColorResDialogColorResPaletteproperty
4 ColorResDialog.PixelFormat Property=ColorResDialogPixelFormatproperty
4 ColorResDialog.ShowBit Property=ColorResDialogShowBitproperty
4 ColorResDialog.ShowDither Property=ColorResDialogShowDitherproperty
4 ColorResDialog.ShowOpenPaletteFileButton Property=ColorResDialogShowOpenPaletteFileButtonproperty
4 ColorResDialog.ShowOrder Property=ColorResDialogShowOrderproperty
4 ColorResDialog.ShowPalette Property=ColorResDialogShowPaletteproperty
4 ColorResDialog.UserPalette Property=ColorResDialogUserPaletteproperty
4 ShowBitsPerPixelConstants Enumeration=ShowBitsPerPixelConstants
4 ShowDitherConstants Enumeration=ShowDitherConstants
4 ShowPaletteConstants Enumeration=ShowPaletteConstants
3 Comment
4 Comment Constructor=CommentConstructor
4 Comment.ByteArray Property=CommentByteArrayproperty
4 Comment.Clear() method=CommentClearmethod
4 Comment.Clone Method=CommentClonemethod
4 Comment.DataType Property=CommentDataTypeproperty
4 Comment.DataTypeConstants Enumeration=CommentDataTypeConstantsEnumeration
4 Comment.IntArray Property=CommentIntArrayproperty
4 Comment.ShortArray Property=CommentShortArrayproperty
4 Comment.String Property=CommentStringproperty
4 Comment.Type Property=CommentTypeproperty
4 Comment.TypeConstants Enumeration=CommentTypeConstantsEnumeration
3 CommonDialog
4 CommonDialog.Font Property=CommonDialogFontproperty
4 CommonDialog.GetString method=CommonDialogGetStringmethod
4 CommonDialog.HelpDelegate Delegate=CommonDialogHelpDelegateDelegate
4 CommonDialog.OnHelp Event=CommonDialogOnHelpevent
4 CommonDialog.SetString method=CommonDialogSetStringmethod
4 CommonDialog.ShowDialog method=CommonDialogShowDialogmethod
4 CommonDialog.ShowHelpButton Property=CommonDialogShowHelpButtonproperty
4 StringConstants Enumeration=StringConstants
4 DialogConstants Enumeration=DialogConstantsEnumeration
3 ContourDialog
4 ContourDialog Constructor=ContourDialogConstructor
4 ContourDialog.DeltaDirection Property=ContourDialogDeltaDirectionproperty
4 ContourDialog.MaximumError Property=ContourDialogMaximumErrorproperty
4 ContourDialog.Option Property=ContourDialogOptionproperty
4 ContourDialog.ShowDeltaDirection Property=ContourDialogShowDeltaDirectionproperty
4 ContourDialog.ShowMaximumError Property=ContourDialogShowMaximumErrorproperty
4 ContourDialog.ShowOptions Property=ContourDialogShowOptionsproperty
4 ContourDialog.ShowOptionsItems Property=ContourDialogShowOptionsItemsproperty
4 ContourDialog.ShowThreshold Property=ContourDialogShowThresholdproperty
4 ContourDialog.ShowOptionConstants Enumeration=ContourDialogShowOptionConstants
4 ContourDialog.Threshold Property=ContourDialogThresholdproperty
3 Curve
4 Curve Constructor=CurveConstructor
4 Curve.CloseConstants Enumeration=CurveCloseConstants
4 Curve.Clone Method=CurveClonemethod
4 Curve.Close Property=CurveCloseproperty
4 Curve.FillMode Property=CurveFillModeproperty
4 Curve.Points Property=CurvePointsproperty
4 Curve.Tension Property=CurveTensionproperty
4 Curve.ToBezier Method=CurveToBeziermethod
4 Curve.Type Property=CurveTypeproperty
4 Curve.TypeConstants Enumeration=CurveTypeConstants
3 DocCleanFilter
4 DocCleanFilter Constructor=DocCleanFilterConstructor
4 DocCleanFilter.LEADRegion Property=DocCleanFilterLEADRegionproperty
4 DocCleanFilter.Region Property=DocCleanFilterRegionproperty
3 DotRemoveEventArgs
4 DotRemoveEventArgs Constructor=DotRemoveEventArgsconstructor
4 DotRemoveEventArgs.BlackCount Property=DotRemoveEventArgsBlackCountproperty
4 DotRemoveEventArgs.BoundingRectangle Property=DotRemoveEventArgsBoundingRectangleproperty
4 DotRemoveEventArgs.Region Property=DotRemoveEventArgsRegionproperty
4 DotRemoveEventArgs.Status Property=DotRemoveEventArgsStatusproperty
4 DotRemoveEventArgs.WhiteCount Property=DotRemoveEventArgsWhiteCountproperty
3 DotRemoveFilter
4 DotRemoveFilter Constructor=DotRemoveFilterConstructor
4 DotRemoveFilter.Flags Property=DotRemoveFilterFlagsproperty
4 DotRemoveFilter.FlagsConstants Enumeration=DotRemoveFilterFlagsConstants
4 DotRemoveFilter.Maximum Property=DotRemoveFilterMaximumproperty
4 DotRemoveFilter.Minimum Property=DotRemoveFilterMinimumproperty
3 EmbossDialog
4 EmbossDialog Constructor=EmbossDialogConstructor
4 EmbossDialog.Depth Property=EmbossDialogDepthproperty
4 EmbossDialog.Direction Property=EmbossDialogDirectionproperty
3 EnumerationCapability
4 EnumerationCapability Constructor=EnumerationCapabilityConstructor
4 EnumerationCapability.GetCurrentIndex Method=EnumerationCapabilityGetCurrentIndexmethod
4 EnumerationCapability.GetDefaultIndex Method=EnumerationCapabilityGetDefaultIndexmethod
4 EnumerationCapability.GetItemType Method=EnumerationCapabilityGetItemTypemethod
4 EnumerationCapability.GetNumberOfItems Method=EnumerationCapabilityGetNumberOfItemsmethod
4 EnumerationCapability.GetValue Method=EnumerationCapabilityGetValuemethod
4 EnumerationCapability.SetCurrentIndex Method=EnumerationCapabilitySetCurrentIndexmethod
4 EnumerationCapability.SetDefaultIndex Method=EnumerationCapabilitySetDefaultIndexmethod
4 EnumerationCapability.SetItemType Method=EnumerationCapabilitySetItemTypemethod
4 EnumerationCapability.SetValue Method=EnumerationCapabilitySetValuemethod
3 Exception
4 Exception Constructor=ExceptionConstructor
4 Exception.Code property=ExceptionCodeproperty
4 Exception.CodeConstants Enumeration=ExceptionCodeConstants
4 Exception.LastError property=ExceptionLastErrorproperty
4 Exception.Message property=ExceptionMessageproperty
3 ExtImageInfo
4 ExtImageInfo Constructor=ExtImageInfoConstructor
4 ExtImageInfo.GetInfo Method=ExtImageInfoGetInfomethod
4 ExtImageInfo.NumberOfInfos Property=ExtImageInfoNumberOfInfosproperty
3 Extensions
4 Extensions.AudioPresent property=ExtensionsAudioPresentproperty
4 Extensions.Count property=ExtensionsCountproperty
4 Extensions.Dispose Method=ExtensionsDisposemethod
4 Extensions.GetAudio Method=ExtensionsGetAudiomethod
4 Extensions.GetClsid Method=ExtensionsGetClsidmethod
4 Extensions.GetData Method=ExtensionsGetDatamethod
4 Extensions.GetName Method=ExtensionsGetNamemethod
4 Extensions.IsStream Method=ExtensionsIsStreammethod
4 Extensions.LoadStamp Method=ExtensionsLoadStampmethod
4 Extensions.StampPresent property=ExtensionsStampPresentproperty
3 FileDialog
4 FileDialog.FileName Property=FileDialogFileNameproperty
4 FileDialog.FileTitle Property=FileDialogFileTitleproperty
4 FileDialog.FilterIndex Property=FileDialogFilterIndexproperty
4 FileDialog.InitialDirectory Property=FileDialogInitialDirectoryproperty
4 FileDialog.NoChangeDirectory Property=FileDialogNoChangeDirectoryproperty
4 FileDialog.PageNumber Property=FileDialogPageNumberproperty
4 FileDialog.ProgressivePasses Property=FileDialogProgressivePassesproperty
4 FileDialog.ShowMultiPage Property=FileDialogShowMultiPageproperty
4 FileDialog.ShowProgressive Property=FileDialogShowProgressiveproperty
4 FileDialog.Title Property=FileDialogTitleproperty
3 FilterDialog
4 FilterConstants Enumeration=FilterConstants
4 FilterDialog Constructor=FilterDialogConstructor
4 FilterDialog.BinaryFilter Property=FilterDialogBinaryFilterproperty
4 FilterDialog.Filter Property=FilterDialogFilterproperty
4 FilterDialog.SpatialFilter Property=FilterDialogSpatialFilterproperty
3 Frame
4 Frame Constructor=FrameConstructor
4 Frame.BottomMargin Property=FrameBottomMarginproperty
4 Frame.Clone Method=FrameClonemethod
4 Frame.LeftMargin Property=FrameLeftMarginproperty
4 Frame.RightMargin Property=FrameRightMarginproperty
4 Frame.TopMargin Property=FrameTopMarginproperty
3 GammaDialog
4 GammaDialog Constructor=GammaDialogConstructor
4 GammaDialog.AllChannels Property=GammaDialogAllChannelsproperty
4 GammaDialog.BlueValue Property=GammaDialogBlueValueproperty
4 GammaDialog.ForceLinkChannels Property=GammaDialogForceLinkChannelsproperty
4 GammaDialog.GreenValue Property=GammaDialogGreenValueproperty
4 GammaDialog.RedValue Property=GammaDialogRedValueproperty
3 HalfToneDialog
4 HalfToneDialog Constructor=HalfToneDialogConstructor
4 HalfToneDialog.Angle Property=HalfToneDialogAngleproperty
4 HalfToneDialog.Type Property=HalfToneDialogTypeproperty
3 HelpEventArgs
4 HelpEventArgs Constructor=HelpEventArgsConstructor
4 HelpEventArgs.CtlId Property=HelpEventArgsCtlIdproperty
4 HelpEventArgs.Dialog Property=HelpEventArgsDialogproperty
3 HolePunchRemoveEventArgs
4 HolePunchRemoveEventArgs Constructor=HolePunchRemoveEventArgsConstructor
4 HolePunchRemoveEventArgs.BlackCount Property=HolePunchRemoveEventArgsBlackCountproperty
4 HolePunchRemoveEventArgs.BoundingRectangle Property=HolePunchRemoveEventArgsBoundingRectangleproperty
4 HolePunchRemoveEventArgs.Index Property=HolePunchRemoveEventArgsIndexproperty
4 HolePunchRemoveEventArgs.Region Property=HolePunchRemoveEventArgsRegionproperty
4 HolePunchRemoveEventArgs.Status Property=HolePunchRemoveEventArgsStatusproperty
4 HolePunchRemoveEventArgs.TotalCount Property=HolePunchRemoveEventArgsTotalCountproperty
4 HolePunchRemoveEventArgs.WhiteCount Property=HolePunchRemoveEventArgsWhiteCountproperty
3 HolePunchRemoveFilter
4 HolePunchRemoveFilter Constructor=HolePunchRemoveFilterConstructor
4 HolePunchRemoveFilter.Flags Property=HolePunchRemoveFilterFlagsproperty
4 HolePunchRemoveFilter.FlagsConstants Enumeration=HolePunchRemoveFilterFlagsConstants
4 HolePunchRemoveFilter.Location Property=HolePunchRemoveFilterLocationproperty
4 HolePunchRemoveFilter.LocationConstants Enumeration=HolePunchRemoveFilterLocationConstants
4 HolePunchRemoveFilter.MaxCount Property=HolePunchRemoveFilterMaxCountproperty
4 HolePunchRemoveFilter.Maximum Property=HolePunchRemoveFilterMaximumproperty
4 HolePunchRemoveFilter.MinCount Property=HolePunchRemoveFilterMinCountproperty
4 HolePunchRemoveFilter.Minimum Property=HolePunchRemoveFilterMinimumproperty
3 Image
4 Image.ActiveFrameIndex Property=ImageActiveFrameIndexproperty
4 Image.ChangeDelegate Delegate=ImageChangeDelegateDelegate
4 Image.Clone Method=ImageClonemethod
4 Image.DeleteFrame Method=ImageDeleteFramemethod
4 Image.Dispose Method=ImageDisposemethod
4 Image.Draw Method=ImageDrawmethod
4 Image.DrawMethod Property=ImageDrawMethodproperty
4 Image.DrawMethodConstants Enumeration=ImageDrawMethodConstants
4 Image.Empty Property=ImageEmptyproperty
4 Image.EnableEvents Property=ImageEnableEventsproperty
4 Image.Finalize Method=ImageFinalizemethod
4 Image.FireEvent Method=ImageFireEventmethod
4 Image.Flags Property=ImageFlagsproperty
4 Image.FrameCount Property=ImageFrameCountproperty
4 Image.FrameDimension Property=ImageFrameDimensionproperty
4 Image.FrameDimensionsList Property=ImageFrameDimensionsListproperty
4 Image.FromFile Method=ImageFromFilemethod
4 Image.FromHbitmap Method=ImageFromHbitmapmethod
4 Image.FromImage Method=ImageFromImagemethod
4 Image.FromLEADBitmapHandle Method=ImageFromLeadBitmapHandlemethod
4 Image.FromStream Method=ImageFromStreammethod
4 Image.GetBounds Method=ImageGetBoundsmethod
4 Image.GetFrameCount Method=ImageGetFrameCountmethod
4 Image.GetGraphics Method=ImageGetGraphicsmethod
4 Image.GetPixelFormatSize Method=ImageGetPixelFormatSizemethod
4 Image.GetThumbnailImage Method=ImageGetThumbnailImagemethod
4 Image.GetThumbnailImageAbort Delegate=ImageGetThumbnailImageAbortDelegate
4 Image.GetType Method=ImageGetTypemethod
4 Image.GrayScale Property=ImageGrayScaleproperty
4 Image.Height Property=ImageHeightproperty
4 Image.HorizontalResolution Property=ImageHorizontalResolutionproperty
4 Image.InsertFrame Method=ImageInsertFramemethod
4 Image.IsAlphaPixelFormat Method=ImageIsAlphaPixelFormatmethod
4 Image.IsCanonicalPixelFormat Method=ImageIsCanonicalPixelFormatmethod
4 Image.IsExtendedPixelFormat Method=ImageIsExtendedPixelFormatmethod
4 Image.IsIndexedPixelFormat Method=ImageIsIndexedPixelFormatmethod
4 Image.Link Method=ImageLinkmethod
4 Image.Linked Property=ImageLinkedproperty
4 Image.OnChange Event=ImageOnChangeevent
4 Image.Palette Property=ImagePaletteproperty
4 Image.PhysicalDimension Property=ImagePhysicalDimensionproperty
4 Image.PixelFormat Property=ImagePixelFormatproperty
4 Image.RawFormat Property=ImageRawFormatproperty
4 Image.ReleaseGraphics Method=ImageReleaseGraphicsmethod
4 Image.RotateFlip Method=ImageRotateFlipmethod
4 Image.Save Method=ImageSavemethod
4 Image.SelectActiveFrame Method=ImageSelectActiveFramemethod
4 Image.Size Property=ImageSizeproperty
4 Image.ToImage Method=ImageToImagemethod
4 Image.Unlink Method=ImageUnlinkmethod
4 Image.VerticalResolution Property=ImageVerticalResolutionproperty
4 Image.Width Property=ImageWidthproperty
3 ImageChangeEventArgs
4 ImageChangeEventArgs Constructor=ImageChangeEventArgsConstructor
4 ImageChangeEventArgs.Change Property=ImageChangeEventArgsChangeproperty
4 ImageChangeEventArgs.ChangeFlagsConstants Enumeration=ImageChangeEventArgsChangeFlagsConstantsEnumeration
3 ImageFormatConverter
4 ImageFormatConverter.Convert Method=ImageFormatConverterConvertmethod
4 ImageFormatConverter.ImageFormat Enumeration=ImageFormatConverterImageFormatEnumeration
3 ImageProcessing
4 ImageProcessing Constructor=ImageProcessingConstructor
4 ImageProcessing.Add Method=ImageProcessingAddmethod
4 ImageProcessing.AddConstants Enumeration=ImageProcessingAddConstants
4 ImageProcessing.AddNoise Method=ImageProcessingAddNoisemethod
4 ImageProcessing.AlphaBlend Method=ImageProcessingAlphaBlendmethod
4 ImageProcessing.AntiAlias Method=ImageProcessingAntiAliasmethod
4 ImageProcessing.AntiAliasFilterConstants Enumeration=ImageProcessingAntiAliasFilterConstants
4 ImageProcessing.AutoTrim Method=ImageProcessingAutoTrimmethod
4 ImageProcessing.Average Method=ImageProcessingAveragemethod
4 ImageProcessing.BalanceColors Method=ImageProcessingBalanceColorsmethod
4 ImageProcessing.Binary Method=ImageProcessingBinarymethod
4 ImageProcessing.BorderRemove Method=ImageProcessingBorderRemovemethod
4 ImageProcessing.BorderRemove Enumeration=ImageProcessingBorderRemove
4 ImageProcessing.BorderRemoveDelegate Delegate=ImageProcessingBorderRemoveDelegateDelegate
4 ImageProcessing.ChannelConstants Enumeration=ImageProcessingChannelConstants
4 ImageProcessing.ColorMerge Method=ImageProcessingColorMergemethod
4 ImageProcessing.ColorRes Method=ImageProcessingColorResmethod
4 ImageProcessing.ColorResDitherConstants Enumeration=ImageProcessingColorResDitherConstants
4 ImageProcessing.ColorResList Method=ImageProcessingColorResListmethod
4 ImageProcessing.ColorResPaletteConstants Enumeration=ImageProcessingColorResPaletteConstants
4 ImageProcessing.ColorSeparate Method=ImageProcessingColorSeparatemethod
4 ImageProcessing.ColorSeparationConstants Enumeration=ImageProcessingColorSeparationConstants
4 ImageProcessing.ColorSpaceConstants Enumeration=ImageProcessingColorSpaceConstants
4 ImageProcessing.Combine Method=ImageProcessingCombinemethod
4 ImageProcessing.CombineConstants Enumeration=ImageProcessingCombineConstants
4 ImageProcessing.Compress Method=ImageProcessingCompressmethod
4 ImageProcessing.Contour Method=ImageProcessingContourmethod
4 ImageProcessing.ContourConstants Enumeration=ImageProcessingContourConstants
4 ImageProcessing.Contrast Method=ImageProcessingContrastmethod
4 ImageProcessing.ConvertSignedToUnsigned Method=ImageProcessingConvertSignedToUnsignedmethod
4 ImageProcessing.ConvertSignedToUnsignedConstants Enumeration=ImageProcessingConvertSignedToUnsignedConstants
4 ImageProcessing.ConvertToColoredGray Method=ImageProcessingConvertToColoredGraymethod
4 ImageProcessing.CreateFadedMask Method=ImageProcessingCreateFadedMaskmethod
4 ImageProcessing.CreateFadedMaskConstants Enumeration=ImageProcessingCreateFadedMaskConstants
4 ImageProcessing.Deskew Method=ImageProcessingDeskewmethod
4 ImageProcessing.Despeckle Method=ImageProcessingDespecklemethod
4 ImageProcessing.DirectionConstants Enumeration=ImageProcessingDirectionConstants
4 ImageProcessing.DocCleanStatus Enumeration=ImageProcessingDocCleanStatus
4 ImageProcessing.DotRemove Method=ImageProcessingDotRemoveMethod
4 ImageProcessing.DotRemoveDelegate Delegate=ImageProcessingDotRemoveDelegateDelegate
4 ImageProcessing.EdgeDetector Method=ImageProcessingEdgeDetectormethod
4 ImageProcessing.EdgeDetectorFilterConstants Enumeration=ImageProcessingEdgeDetectorFilterConstants
4 ImageProcessing.Emboss Method=ImageProcessingEmbossmethod
4 ImageProcessing.FastRotate Method=ImageProcessingFastRotatemethod
4 ImageProcessing.FeatherAlphaBlend Method=ImageProcessingFeatherAlphaBlendmethod
4 ImageProcessing.Fill Method=ImageProcessingFillmethod
4 ImageProcessing.Flip Method=ImageProcessingFlipmethod
4 ImageProcessing.FunctionalLookupTableConstants Enumeration=ImageProcessingFunctionalLookupTableConstants
4 ImageProcessing.GammaCorrect Method=ImageProcessingGammaCorrectmethod
4 ImageProcessing.Gaussian Method=ImageProcessingGaussianmethod
4 ImageProcessing.GetAutoTrimRectangle Method=ImageProcessingGetAutoTrimRectanglemethod
4 ImageProcessing.GetColorCount Method=ImageProcessingGetColorCountmethod
4 ImageProcessing.GetFunctionalLookupTable Method=ImageProcessingGetFunctionalLookupTablemethod
4 ImageProcessing.GetHistogram Method=ImageProcessingGetHistogrammethod
4 ImageProcessing.GetRow Method=ImageProcessingGetRowmethod
4 ImageProcessing.GetRowSize Method=ImageProcessingGetRowSizemethod
4 ImageProcessing.GetUserLookupTable Method=ImageProcessingGetUserLookupTablemethod
4 ImageProcessing.GrayScale Method=ImageProcessingGrayScalemethod
4 ImageProcessing.HalfTone Method=ImageProcessingHalfTonemethod
4 ImageProcessing.HalfToneConstants Enumeration=ImageProcessingHalfToneConstants
4 ImageProcessing.HolePunchRemove Method=ImageProcessingHolePunchRemoveMethod
4 ImageProcessing.HolePunchRemoveDelegate Delegate=ImageProcessingHolePunchRemoveDelegateDelegate
4 ImageProcessing.HistoContrast Method=ImageProcessingHistoContrastmethod
4 ImageProcessing.HistoEqualize Method=ImageProcessingHistoEqualizemethod
4 ImageProcessing.Hue Method=ImageProcessingHuemethod
4 ImageProcessing.Intensity Method=ImageProcessingIntensitymethod
4 ImageProcessing.IntensityDetect Method=ImageProcessingIntensityDetectmethod
4 ImageProcessing.Invert Method=ImageProcessingInvertmethod
4 ImageProcessing.InvertedText Method=ImageProcessingInvertedTextMethod
4 ImageProcessing.InvertedTextDelegate Delegate=ImageProcessingInvertedTextDelegateDelegate
4 ImageProcessing.LineProfile Method=ImageProcessingLineProfilemethod
4 ImageProcessing.LineRemove Method=ImageProcessingLineRemoveMethod
4 ImageProcessing.LineRemoveDelegate Delegate=ImageProcessingLineRemoveDelegateDelegate
4 ImageProcessing.Max Method=ImageProcessingMaxmethod
4 ImageProcessing.Median Method=ImageProcessingMedianmethod
4 ImageProcessing.Min Method=ImageProcessingMinmethod
4 ImageProcessing.Mosaic Method=ImageProcessingMosaicmethod
4 ImageProcessing.MotionBlur Method=ImageProcessingMotionBlurmethod
4 ImageProcessing.Oilify Method=ImageProcessingOilifymethod
4 ImageProcessing.OnProgress Event=ImageProcessingOnProgressEvent
4 ImageProcessing.Picturize Method=ImageProcessingPicturizemethod
4 ImageProcessing.PicturizeConstants Enumeration=ImageProcessingPicturizeConstants
4 ImageProcessing.PicturizeDelegate Delegate=ImageProcessingPicturizeDelegateDelegate
4 ImageProcessing.Posterize Method=ImageProcessingPosterizemethod
4 ImageProcessing.ProgressDelegate Delegate=ImageProcessingProgressDelegateDelegate
4 ImageProcessing.PutRow Method=ImageProcessingPutRowmethod
4 ImageProcessing.RemapHue Method=ImageProcessingRemapHuemethod
4 ImageProcessing.RemapIntensity Method=ImageProcessingRemapIntensitymethod
4 ImageProcessing.RemoveRedEye Method=ImageProcessingRemoveRedEyemethod
4 ImageProcessing.ResizeConstants Enumeration=ImageProcessingResizeConstants
4 ImageProcessing.ResizeRegion Method=ImageProcessingResizeRegionmethod
4 ImageProcessing.ResizeRegionConstants Enumeration=ImageProcessingResizeRegionConstants
4 ImageProcessing.Reverse Method=ImageProcessingReversemethod
4 ImageProcessing.Rotate Method=ImageProcessingRotatemethod
4 ImageProcessing.RotateConstants Enumeration=ImageProcessingRotateConstants
4 ImageProcessing.Saturation Method=ImageProcessingSaturationmethod
4 ImageProcessing.Sharpen Method=ImageProcessingSharpenmethod
4 ImageProcessing.Shear Method=ImageProcessingShearmethod
4 ImageProcessing.Size Method=ImageProcessingSizemethod
4 ImageProcessing.Smooth Method=ImageProcessingSmoothMethod
4 ImageProcessing.SmoothDelegate Delegate=ImageProcessingSmoothDelegateDelegate
4 ImageProcessing.Solarize Method=ImageProcessingSolarizemethod
4 ImageProcessing.Spatial Method=ImageProcessingSpatialmethod
4 ImageProcessing.StretchIntensity Method=ImageProcessingStretchIntensitymethod
4 ImageProcessing.SwapColors Method=ImageProcessingSwapColorsmethod
4 ImageProcessing.SwapColorsConstants Enumeration=ImageProcessingSwapColorsConstants
4 ImageProcessing.Trim Method=ImageProcessingTrimmethod
4 ImageProcessing.Underlay Method=ImageProcessingUnderlaymethod
4 ImageProcessing.UnderlayConstants Enumeration=ImageProcessingUnderlayConstants
4 ImageProcessing.UnsharpMask Method=ImageProcessingUnsharpMaskmethod
4 ImageProcessing.WindowLevel Method=ImageProcessingWindowLevelmethod
3 Info
4 ExtendedImageConstants Enumeration=ExtendedImageConstants
4 Info Constructor=Infoconstructor
4 Info.ConditionCode Property=InfoConditionCodeproperty
4 Info.ConditionCodeConstants Enumeration=InfoConditionCodeConstants
4 Info.GetItem Method=InfoGetItemmethod
4 Info.InfoId Property=InfoInfoIdproperty
4 Info.ItemType Property=InfoItemTypeproperty
4 Info.NumberOfItems Property=InfoNumberOfItemsproperty
3 InvertedTextEventArgs
4 InvertedTextEventArgs Constructor=InvertedTextEventArgsConstructor
4 InvertedTextEventArgs.BlackCount Property=InvertedTextEventArgsBlackCountproperty
4 InvertedTextEventArgs.BoundingRectangle Property=InvertedTextEventArgsBoundingRectangleproperty
4 InvertedTextEventArgs.Region Property=InvertedTextEventArgsRegionproperty
4 InvertedTextEventArgs.Status Property=InvertedTextEventArgsStatusproperty
4 InvertedTextEventArgs.WhiteCount Property=InvertedTextEventArgsWhiteCountproperty
3 InvertedTextFilter
4 InvertedTextFilter Constructor=InvertedTextFilterConstructor
4 InvertedTextFilter.Flags Property=InvertedTextFilterFlagsproperty
4 InvertedTextFilter.FlagsConstants Enumeration=InvertedTextFilterFlagsConstants
4 InvertedTextFilter.MaxBlackPercent Property=InvertedTextFilterMaxBlackPercentproperty
4 InvertedTextFilter.MinBlackPercent Property=InvertedTextFilterMinBlackPercentproperty
4 InvertedTextFilter.MinInvert Property=InvertedTextFilterMinInvertproperty
3 J2KOptions
4 J2KOptions Constructor=J2KOptionsConstructor
4 J2KOptions.CBS_ErrorResilienceSymbol Property=J2KOptionsCBS_ErrorResilienceSymbolProperty
4 J2KOptions.CBS_PredictableTermination Property=J2KOptionsCBS_PredictableTerminationProperty
4 J2KOptions.CBS_ResetContextOnBoundaries Property=J2KOptionsCBS_ResetContextOnBoundariesProperty
4 J2KOptions.CBS_SelectiveACBypass Property=J2KOptionsCBS_SelectiveACBypassProperty
4 J2KOptions.CBS_TerminationOnEachPass Property=J2KOptionsCBS_TerminationOnEachPassProperty
4 J2KOptions.CBS_VerticallyCausalContext Property=J2KOptionsCBS_VerticallyCausalContextProperty
4 J2KOptions.CodBlockHeight Property Property=J2KOptionsCodBlockHeightProperty
4 J2KOptions.CodBlockWidth Property=J2KOptionsCodBlockWidthProperty
4 J2KOptions.CompressionControl Property=J2KOptionsCompressionControlProperty
4 J2KOptions.CompressionControlConstants Enumeration=J2KOptionsCompressionControlConstants
4 J2KOptions.CompressionRatio Property=J2KOptionsCompressionRatioProperty
4 J2KOptions.DecompLevel Property=J2KOptionsDecompLevelProperty
4 J2KOptions.DerivedBaseExponent Property=J2KOptionsDerivedBaseExponentProperty
4 J2KOptions.DerivedBaseMantissa Property=J2KOptionsDerivedBaseMantissaProperty
4 J2KOptions.DerivedQuantization Property=J2KOptionsDerivedQuantizationProperty
4 J2KOptions.GuardBits Property=J2KOptionsGuardBitsProperty
4 J2KOptions.ProgressionFlagsConstants Enumeration=J2KOptionsProgressionFlagsConstants
4 J2KOptions.ProgressOrder Property=J2KOptionsProgressOrderProperty
4 J2KOptions.SetDefaults Method=J2KOptionsSetDefaultsMethod
4 J2KOptions.TargetFileSize Property=J2KOptionsTargetFileSizeProperty
4 J2KOptions.UseColorTransform Property=J2KOptionsUseColorTransformProperty
4 J2KOptions.UseEPHMarker Property=J2KOptionsUseEPHMarkerProperty
4 J2KOptions.UseSOPMarker Property=J2KOptionsUseSOPMarkerProperty
4 J2KOptions.XOsiz Property=J2KOptionsXOsizProperty
4 J2KOptions.XTsiz Property=J2KOptionsXTsizProperty
4 J2KOptions.XTOsiz Property=J2KOptionsXTOsizProperty
4 J2KOptions.YOsiz Property=J2KOptionsYOsizProperty
4 J2KOptions.YTsiz Property=J2KOptionsYTsizProperty
4 J2KOptions.YTOsiz Property=J2KOptionsYTOsizProperty
3 LineRemoveEventArgs
4 LineRemoveEventArgs Constructor=LineRemoveEventArgsConstructor
4 LineRemoveEventArgs.Length Property=LineRemoveEventArgsLengthproperty
4 LineRemoveEventArgs.Region Property=LineRemoveEventArgsRegionproperty
4 LineRemoveEventArgs.StartCol Property=LineRemoveEventArgsStartColproperty
4 LineRemoveEventArgs.StartRow Property=LineRemoveEventArgsStartRowproperty
4 LineRemoveEventArgs.Status Property=LineRemoveEventArgsStatusproperty
3 LineRemoveFilter
4 LineRemoveFilter Constructor=LineRemoveFilterConstructor
4 LineRemoveFilter.Flags Property=LineRemoveFilterFlagsproperty
4 LineRemoveFilter.FlagsConstants Enumeration=LineRemoveFilterFlagsConstants
4 LineRemoveFilter.GapLength Property=LineRemoveFilterGapLengthproperty
4 LineRemoveFilter.LineVariance Property=LineRemoveFilterLineVarianceproperty
4 LineRemoveFilter.MaxLineWidth Property=LineRemoveFilterMaxLineWidthproperty
4 LineRemoveFilter.MaxWallPercent Property=LineRemoveFilterMaxWallPercentproperty
4 LineRemoveFilter.MinLineLength Property=LineRemoveFilterMinLineLengthproperty
4 LineRemoveFilter.Remove Property=LineRemoveFilterRemoveproperty
4 LineRemoveFilter.RemoveConstants Enumeration=LineRemoveFilterRemoveConstants
4 LineRemoveFilter.Wall Property=LineRemoveFilterWallproperty
3 NoiseDialog
4 NoiseDialog Constructor=NoiseDialogConstructor
4 NoiseDialog.Channel Property=NoiseDialogChannelproperty
4 NoiseDialog.Range Property=NoiseDialogRangeproperty
3 OneValueCapability
4 OneValueCapability Constructor=OneValueCapabilityconstructor
4 OneValueCapability.GetItemType Method=OneValueCapabilityGetItemTypemethod
4 OneValueCapability.GetValue Method=OneValueCapabilityGetValuemethod
4 OneValueCapability.SetItemType Method=OneValueCapabilitySetItemTypemethod
4 OneValueCapability.SetValue Method=OneValueCapabilitySetValuemethod
3 OpenFileDialog
4 OpenFileDialog Constructor=OpenFileDialogConstructor
4 OpenFileDialog.CheckFileExists Property=OpenFileDialogCheckFileExistsproperty
4 OpenFileDialog.CheckPathExists Property=OpenFileDialogCheckPathExistsproperty
4 OpenFileDialog.Compressed Property=OpenFileDialogCompressedproperty
4 OpenFileDialog.EnableResizing Property=OpenFileDialogEnableResizingproperty
4 OpenFileDialog.FileNames Property=OpenFileDialogFileNamesproperty
4 OpenFileDialog.Filter Property=OpenFileDialogFilterproperty
4 OpenFileDialog.MultiSelect Property=OpenFileDialogMultiSelectproperty
4 OpenFileDialog.PreviewEnabled Property=OpenFileDialogPreviewEnabledproperty
4 OpenFileDialog.Rotated Property=OpenFileDialogRotatedproperty
4 OpenFileDialog.ShowCompressed Property=OpenFileDialogShowCompressedproperty
4 OpenFileDialog.ShowDeletePage Property=OpenFileDialogShowDeletePageproperty
4 OpenFileDialog.ShowFileInfo Property=OpenFileDialogShowFileInfoproperty
4 OpenFileDialog.ShowGetResolutionContextHelp Property=OpenFileDialogShowGetResolutionContextHelpproperty
4 OpenFileDialog.ShowPdfDialog Property=OpenFileDialogShowPdfDialogproperty
4 OpenFileDialog.ShowResolutionDialog Property=OpenFileDialogShowResolutionDialogproperty
4 OpenFileDialog.ShowRotated Property=OpenFileDialogShowRotatedproperty
4 OpenFileDialog.ShowStamp Property=OpenFileDialogShowStampproperty
4 OpenFileDialog.Thumbnail Property=OpenFileDialogThumbnailproperty
3 PCDOptions
4 PCDOptions Constructor=PCDOptionsConstructor
4 PCDOptions [ResolutionConstants] Indexer property=PCDOptionsResolutionConstantsIndexerproperty
4 PCDOptions.Resolution Property=PCDOptionsResolutionproperty
4 PCDOptions.ResolutionConstants Enumeration=PCDOptionsResolutionConstantsEnumeration
3 PDFOptions
4 PDFOptions Constructor=PDFOptionsConstructor
4 PDFOptions.DisplayDepth Property=PDFOptionsDisplayDepthproperty
4 PDFOptions.GraphicsAlpha Property=PDFOptionsGraphicsAlphaproperty
4 PDFOptions.Resolution Property=PDFOptionsResolutionproperty
4 PDFOptions.SaveUseDPI Property=PDFOptionsSaveUseDPIproperty
4 PDFOptions.TextAlpha Property=PDFOptionsTextAlphaproperty
4 PDFOptions.UseLibFonts Property=PDFOptionsUseLibFontsproperty
3 PictureBox
4 PictureBox Constructor=PictureBoxConstructor
4 PictureBox.AutoRepaint property=PictureBoxAutoRepaintProperty
4 PictureBox.AutoScroll property=PictureBoxAutoScrollProperty
4 PictureBox.BackErase property=PictureBoxBackEraseProperty
4 PictureBox.BorderStyle property=PictureBoxBorderStyleProperty
4 PictureBox.ClientToPicture method=PictureBoxClientToPictureMethod
4 PictureBox.InteractiveMode Enumeration=PictureBoxInteractiveMode
4 PictureBox.LEADImage property=PictureBoxLEADImageProperty
4 PictureBox.LeftButtonInteractiveMode property=PictureBoxLeftButtonInteractiveModeProperty
4 PictureBox.PictureBoxSizeMode Enumeration=PictureBoxPictureBoxSizeMode
4 PictureBox.PictureToClient method=PictureBoxPictureToClientMethod
4 PictureBox.RightButtonInteractiveMode property=PictureBoxRightButtonInteractiveModeProperty
4 PictureBox.RubberBand property=PictureBoxRubberBandProperty
4 PictureBox.SizeMode property=PictureBoxSizeModeProperty
4 PictureBox.SourceRectangle property=PictureBoxSourceRectangleProperty
4 PictureBox.SystemImage property=PictureBoxSystemImageProperty
4 PictureBox.Zoom property=PictureBoxZoomProperty
4 PictureBox.ZoomToRect method=PictureBoxZoomToRectMethod
3 PicturizeEventArgs
4 PicturizeEventArgs Constructor=PicturizeEventArgsconstructor
4 PicturizeEventArgs.Canceled Property=PicturizeEventArgsCanceledproperty
4 PicturizeEventArgs.Position Property=PicturizeEventArgsPositionproperty
3 PreviewCommonDialog
4 PreviewCommonDialog Constructor=PreviewCommonDialogConstructor
4 PreviewCommonDialog.AutoProcess Property=PreviewCommonDialogAutoProcessproperty
4 PreviewCommonDialog.Image Property=PreviewCommonDialogImageproperty
4 PreviewCommonDialog.ShowPreview Property=PreviewCommonDialogShowPreviewproperty
4 PreviewCommonDialog.ShowToolbar Property=PreviewCommonDialogShowToolbarproperty
4 PreviewCommonDialog.Win95ContextHelp Property=PreviewCommonDialogWin95ContextHelpproperty
3 ProgressEventArgs
4 ProgressEventArgs Constructor=ProgressEventArgsConstructor
4 ProgressEventArgs.Canceled Property=ProgressEventArgsCanceledproperty
4 ProgressEventArgs.Percentage Property=ProgressEventArgsPercentageproperty
3 PixelFormatConverter
4 PixelFormatConverter.Convert Method=PixelFormatConverterConvertmethod
4 PixelFormatConverter.PixelFormat Enumeration=PixelFormatConverterPixelFormatenumeration
3 RangeCapability
4 RangeCapability Constructor=RangeCapabilityConstructor
4 RangeCapability.GetCurrentValue Method=RangeCapabilityGetCurrentValuemethod
4 RangeCapability.GetDefaultValue Method=RangeCapabilityGetDefaultValuemethod
4 RangeCapability.GetMaximumValue Method=RangeCapabilityGetMaximumValuemethod
4 RangeCapability.GetMinimumValue Method=RangeCapabilityGetMinimumValuemethod
4 RangeCapability.GetItemType Method=RangeCapabilityGetItemTypemethod
4 RangeCapability.GetStepSize Method=RangeCapabilityGetStepSizemethod
4 RangeCapability.SetCurrentValue Method=RangeCapabilitySetCurrentValuemethod
4 RangeCapability.SetDefaultValue Method=RangeCapabilitySetDefaultValuemethod
4 RangeCapability.SetItemType Method=RangeCapabilitySetItemTypemethod
4 RangeCapability.SetMaximumValue Method=RangeCapabilitySetMaximumValuemethod
4 RangeCapability.SetMinimumValue Method=RangeCapabilitySetMinimumValuemethod
4 RangeCapability.SetStepSize Method=RangeCapabilitySetStepSizemethod
3 RangeDialog
4 RangeDialog Constructor=RangeDialogConstructor
4 RangeDialog.High Property=RangeDialogHighproperty
4 RangeDialog.Low Property=RangeDialogLowproperty
3 RasterizeOptions
4 RasterizeOptions Constructor=RasterizeOptionsConstructor
4 RasterizeOptions.ViewportSize Property=RasterizeOptionsViewportSizeProperty
4 RasterizeOptions.ViewMode Property=RasterizeOptionsViewModeProperty
4 RasterizeOptions.ViewModeConstants Enumeration=RasterizeOptionsViewModeConstants
3 ReadyStateChangeEventArgs
4 ReadyStateChangeEventArgs Constructor=ReadyStateChangeEventArgsConstructor
4 ReadyStateChangeEventArgs.ReadyState Enumeration=ReadyStateChangeEventArgsReadyState
4 ReadyStateChangeEventArgs.State Property=ReadyStateChangeEventArgsStateproperty
3 RegionXForm
4 RegionXForm Constructor=RegionXFormConstructor
4 RegionXForm.Default Method=RegionXFormDefaultmethod
4 RegionXForm.ViewPerspective Property=RegionXFormViewPerspectiveproperty
4 RegionXForm.XOffset Property=RegionXFormXOffsetproperty
4 RegionXForm.XScalarDenominator Property=RegionXFormXScalarDenominatorproperty
4 RegionXForm.XScalarNumerator Property=RegionXFormXScalarNumeratorproperty
4 RegionXForm.YOffset Property=RegionXFormYOffsetproperty
4 RegionXForm.YScalarDenominator Property=RegionXFormYScalarDenominatorproperty
4 RegionXForm.YScalarNumerator Property=RegionXFormYScalarNumeratorproperty
3 RubberBand
4 RubberBand Constructor=RubberBandConstructor
4 RubberBand.Cancel method=RubberBandCancelMethod
4 RubberBand.ClipRectangle property=RubberBandClipRectangleProperty
4 RubberBand.Enabledproperty=RubberBandEnabledProperty
4 RubberBand[MouseButtons] Indexer=RubberBandMouseButtonsIndexer
4 RubberBand.OnTracking event=RubberBandOnTrackingEvent
4 RubberBand.Owner property=RubberBandOwnerProperty
4 RubberBand.OwnerDraw property=RubberBandOwnerDrawProperty
4 RubberBand.Tracking Delegate=RubberBandTrackingDelegate
4 RubberBand.UnmangleRectangle method=RubberBandUnmangleRectangleMethod
3 RubberBandEventArgs
4 RubberBandEventArgs Constructor=RubberBandEventArgsConstructor
4 RubberBandEventArgs.Button property=RubberBandEventArgsButtonproperty
4 RubberBandEventArgs.Rectangle property=RubberBandEventArgsRectangleproperty
4 RubberBandEventArgs.Status property=RubberBandEventArgsStatusproperty
4 RubberBandEventArgs.TrackingStatus Enumeration=RubberBandEventArgsTrackingStatusEnumeration
3 RubberBandButtonOptions
4 RubberBandButtonOptions.Enabled property=RubberBandButtonOptionsEnabledproperty
4 RubberBandButtonOptions.FrameStyle property=RubberBandButtonOptionsFrameStyleproperty
3 SaveFileDialog
4 SaveFileDialog Constructor=SaveFileDialogConstructor
4 SaveFileDialog.BitsPerPixel Property=SaveFileDialogBitsPerPixelproperty
4 SaveFileDialog.GetSaveQFactor method=SaveFileDialogGetSaveQFactormethod
4 SaveFileDialog.ImageFormat Property=SaveFileDialogImageFormatproperty
4 SaveFileDialog.Interlaced Property=SaveFileDialogInterlacedproperty
4 SaveFileDialog.MultiPageOperation Property=SaveFileDialogMultiPageOperationproperty
4 SaveFileDialog.OverwritePrompt Property=SaveFileDialogOverwritePromptproperty
4 SaveFileDialog.PixelFormat Property=SaveFileDialogPixelFormatproperty
4 SaveFileDialog.SaveFileFormats Property=SaveFileDialogSaveFileFormatsproperty
4 SaveFileDialog.SetSaveQFactor method=SaveFileDialogSetSaveQFactormethod
4 SaveFileDialog.ShowQFactor Property=SaveFileDialogShowQFactorproperty
4 SaveFileDialog.StampSize Property=SaveFileDialogStampSizeproperty
4 SaveFileDialog.ShowStamp Property=SaveFileDialogShowStampproperty
4 SaveFileDialog.StampBitsPerPixel Property=SaveFileDialogStampBitsPerPixelproperty
4 SaveFileDialog.StampPixelFormat Property=SaveFileDialogStampPixelFormatproperty
4 SaveFileDialog.SubTypeIndex Property=SaveFileDialogSubTypeIndexproperty
4 SaveFileDialog.WithStamp Property=SaveFileDialogWithStampproperty
3 SaveFileFormat
4 FormatConstants Enumeration=FormatConstants
4 SubFormatConstants Enumeration=SubFormatConstants
4 SaveFileFormat Constructor=SaveFileFormatConstructor
4 SaveFileFormat.Format Property=SaveFileFormatFormatproperty
4 SaveFileFormat.SubFormats Property=SaveFileFormatSubFormatsproperty
3 SizeDialog
4 SizeDialog Constructor=SizeDialogConstructor
4 SizeDialog.Resize Property=SizeDialogResizeproperty
4 SizeDialog.Resolution Property=SizeDialogResolutionproperty
4 SizeDialog.ShowIdenticalValue Property=SizeDialogShowIdenticalValueproperty
4 SizeDialog.ShowMaintainAspect Property=SizeDialogShowMaintainAspectproperty
4 SizeDialog.ShowPercentage Property=SizeDialogShowPercentageproperty
4 SizeDialog.ShowResolutionGroup Property=SizeDialogShowResolutionGroupproperty
4 SizeDialog.Size Property=SizeDialogSizeproperty
3 SmoothEventArgs
4 SmoothEventArgs Constructor=SmoothEventArgsConstructor
4 SmoothEventArgs.BumpOrNick Property=SmoothEventArgsBumpOrNickproperty
4 SmoothEventArgs.BumpOrNickConstants Enumeration=SmoothEventArgsBumpOrNickConstants
4 SmoothEventArgs.Element Property=SmoothEventArgsElementproperty
4 SmoothEventArgs.ElementConstants Enumeration=SmoothEventArgsElementConstants
4 SmoothEventArgs.Length Property=SmoothEventArgsLengthproperty
4 SmoothEventArgs.StartRow Property=SmoothEventArgsStartRowproperty
4 SmoothEventArgs.StartCol Property=SmoothEventArgsStartColproperty
4 SmoothEventArgs.Status Property=SmoothEventArgsStatusproperty
3 SmoothFilter
4 SmoothFilter Constructor=SmoothFilterConstructor
4 SmoothFilter.Flags Property=SmoothFilterFlagsproperty
4 SmoothFilter.FlagsConstants Enumeration=SmoothFilterFlagsConstants
4 SmoothFilter.Length Property=SmoothFilterLengthproperty
3 SpatialFilter
4 SpatialFilter Constructor=SpatialFilterConstructor
4 SpatialFilter.Bias Property=SpatialFilterBiasproperty
4 SpatialFilter.Dimension Property=SpatialFilterDimensionproperty
4 SpatialFilter.Divisor Property=SpatialFilterDivisorproperty
4 SpatialFilter.Item Property=SpatialFilterItemProperty
4 SpatialFilter.SetType Method=SpatialFilterSetTypemethod
4 SpatialFilter.TypeConstants Enumeration=SpatialFilterTypeConstants
3 Support
4 Support.FeatureConstants Enumeration=SupportFeatureConstants
4 Support.IsLocked method=SupportIsLockedmethod
4 Support.Unlock method=SupportUnlockmethod
3 Tag
4 Tag Constructor=TagConstructor
4 Tag.ByteArray Property=TagByteArrayproperty
4 Tag.Clear Property=TagClearproperty
4 TagClone Method=TagClonemethod
4 Tag.DataType Property=TagDataTypeproperty
4 Tag.DataTypeConstants Enumeration=TagDataTypeConstantsEnumeration
4 Tag.Dispose Method=TagDisposemethod
4 Tag.DoubleArray Property=TagDoubleArrayproperty
4 Tag.FloatArray Property=TagFloatArrayproperty
4 Tag.Id Property=TagIdproperty
4 Tag.IntArray Property=TagIntArrayproperty
4 Tag.ShortArray Property=TagShortArrayproperty
4 Tag.String Property=TagStringproperty
3 TemplateEventArgs
4 TemplateEventArgs Constructor=TemplateEventArgsConstructor
4 TemplateEventArgs.Capability Property=TemplateEventArgsCapabilityproperty
3 Twain
4 Twain Constructor=TwainConstructor
4 Twain.Acquire Method=TwainAcquiremethod
4 Twain.AcquirePageDelegate Delegate=TwainAcquirePageDelegateDelegate
4 Twain.ApplicationName Property=TwainApplicationNameproperty
4 Twain.CapabilityConstants Enumeration=TwainCapabilityConstants
4 Twain.ContainerTypeConstants Enumeration=TwainContainerTypeConstants
4 Twain.EndSession Method=TwainEndSessionmethod
4 Twain.FileTransferName Property=TwainFileTransferNameproperty
4 Twain.GetCapabilityConstants Enumeration=TwainGetCapabilityConstants
4 Twain.GetCapabilitiesCount Method=TwainGetCapabilitiesCountmethod
4 Twain.GetCapability Method=TwainGetCapabilitymethod
4 Twain.GetExtImageInfo Method=TwainGetExtImageInfomethod
4 Twain.GetSourceCount Method=TwainGetSourceCountmethod
4 Twain.GetSourceName Method=TwainGetSourceNamemethod
4 Twain.InitSession Method=TwainInitSessionmethod
4 Twain.ItemTypeConstants Enumeration=TwainItemTypeConstants
4 Twain.LoadTemplate Method=TwainLoadTemplatemethod
4 Twain.LoadTemplateDelegate Delegate=TwainLoadTemplateDelegateDelegate
4 Twain.ManufacturerName Property=TwainManufacturerNameproperty
4 Twain.MemoryBufferSize Property=TwainMemoryBufferSizeproperty
4 Twain.OnAcquirePage Event=TwainOnAcquirePageevent
4 Twain.OnLoadTemplate Event=TwainOnLoadTemplateevent
4 Twain.OnSaveTemplate Event=TwainOnSaveTemplateevent
4 Twain.ProductFamily Property=TwainProductFamilyproperty
4 Twain.QueryCapability Method=TwainQueryCapabilitymethod
4 Twain.SaveTemplate Method=TwainSaveTemplatemethod
4 Twain.SaveTemplateDelegate Delegate=TwainSaveTemplateDelegateDelegate
4 Twain.SelectSource Method=TwainSelectSourcemethod
4 Twain.SetAcquireSourceName Method=TwainSetAcquireSourceNamemethod
4 Twain.SetCapability Method=TwainSetCapabilitymethod
4 Twain.SetCapabilityConstants Enumeration=TwainSetCapabilityConstants
4 Twain.ShowTemplateDlg Method=TwainShowTemplateDlgmethod
4 Twain.UserInterfaceConstants Enumeration=TwainUserInterfaceConstants
4 Twain.Version Property=TwainVersionproperty
3 WindowLevelDialog
4 WindowLevelDialog Constructor=WindowLevelDialogConstructor
4 WindowLevelDialog.EndColor Property=WindowLevelDialogEndColorproperty
4 WindowLevelDialog.LookupTable Property=WindowLevelDialogLookupTableproperty
4 WindowLevelDialog.MaxBit Property=WindowLevelDialogMaxBitproperty
4 WindowLevelDialog.MaxLevel Property=WindowLevelDialogMaxLevelproperty
4 WindowLevelDialog.MinBit Property=WindowLevelDialogMinBitproperty
4 WindowLevelDialog.MinLevel Property=WindowLevelDialogMinLevelproperty
4 WindowLevelDialog.StartColor Property=WindowLevelDialogStartColorproperty
4 WindowLevelDialog.UseSysColorDlg Property=WindowLevelDialogUseSysColorDlgproperty
3 WMFOptions
4 WMFOptions Constructor=WMFOptionsConstructor
4 WMFOptions.Resolution Property=WMFOptionsResolutionProperty
2 Appendix
3 Document/Medical Features=ExpressEd
3 Document/Medical Support and Licensing Requirements=ExpressRoyal
3 JPEG 2000 Plug In Support Instructions=JPEG2000PlugInSupportInstructions
3 LEADTOOLS Nag Message=LEADTOOLS_Nag_Message
3 LZW License Instructions=LZWLicense
3 LZW Support=LZWExpress
3 Namespaces and Assemblies=NamespacesandAssemblies
3 File Comments
4 Types of File Comments=FileComments
4 GIF File Comments=GifComments
4 IPTC Comments=IPTC_Comments
4 JPEG, J2K and LEAD File Comments=JpegComments
4 PNG File Comments=PNGComments
4 TIFF File Comments=TiffComments
3 DICOM
4 DICOM File Comments=DicomComments
4 Modalities=Modalities
3 Exif
4 Exif File Comments=ExifComments
4 CFA=CFA
4 OECF=OECF
4 SFR=SFR
