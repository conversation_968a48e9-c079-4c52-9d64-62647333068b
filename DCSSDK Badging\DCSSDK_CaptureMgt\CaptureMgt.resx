<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="tab10Print.GenerateMember" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <data name="&gt;&gt;checkFingerprintBase.Name" xml:space="preserve">
    <value>checkFingerprintBase</value>
  </data>
  <data name="&gt;&gt;checkFingerprintBase.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkFingerprintBase.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;checkFingerprintBase.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;buttonTest10PrintDisplay.Name" xml:space="preserve">
    <value>buttonTest10PrintDisplay</value>
  </data>
  <data name="&gt;&gt;buttonTest10PrintDisplay.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonTest10PrintDisplay.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;buttonTest10PrintDisplay.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;checkBoxFingerMapping10Print.Name" xml:space="preserve">
    <value>checkBoxFingerMapping10Print</value>
  </data>
  <data name="&gt;&gt;checkBoxFingerMapping10Print.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBoxFingerMapping10Print.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;checkBoxFingerMapping10Print.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;check10Print.Name" xml:space="preserve">
    <value>check10Print</value>
  </data>
  <data name="&gt;&gt;check10Print.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;check10Print.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;check10Print.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;label10PrintInstances.Name" xml:space="preserve">
    <value>label10PrintInstances</value>
  </data>
  <data name="&gt;&gt;label10PrintInstances.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label10PrintInstances.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;label10PrintInstances.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;numericUpDown10PrintInstances.Name" xml:space="preserve">
    <value>numericUpDown10PrintInstances</value>
  </data>
  <data name="&gt;&gt;numericUpDown10PrintInstances.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numericUpDown10PrintInstances.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;numericUpDown10PrintInstances.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;label21.Name" xml:space="preserve">
    <value>label21</value>
  </data>
  <data name="&gt;&gt;label21.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label21.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;label21.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="&gt;&gt;textBox10PrintID.Name" xml:space="preserve">
    <value>textBox10PrintID</value>
  </data>
  <data name="&gt;&gt;textBox10PrintID.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox10PrintID.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;textBox10PrintID.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="&gt;&gt;label10PrintTestID.Name" xml:space="preserve">
    <value>label10PrintTestID</value>
  </data>
  <data name="&gt;&gt;label10PrintTestID.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label10PrintTestID.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;label10PrintTestID.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="&gt;&gt;buttonTest10PrintCapture.Name" xml:space="preserve">
    <value>buttonTest10PrintCapture</value>
  </data>
  <data name="&gt;&gt;buttonTest10PrintCapture.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonTest10PrintCapture.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;buttonTest10PrintCapture.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="&gt;&gt;button10PrintDeviceProperties.Name" xml:space="preserve">
    <value>button10PrintDeviceProperties</value>
  </data>
  <data name="&gt;&gt;button10PrintDeviceProperties.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button10PrintDeviceProperties.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;button10PrintDeviceProperties.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="&gt;&gt;button10PrintFinisherProperties.Name" xml:space="preserve">
    <value>button10PrintFinisherProperties</value>
  </data>
  <data name="&gt;&gt;button10PrintFinisherProperties.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button10PrintFinisherProperties.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;button10PrintFinisherProperties.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="&gt;&gt;label10PrintDevice.Name" xml:space="preserve">
    <value>label10PrintDevice</value>
  </data>
  <data name="&gt;&gt;label10PrintDevice.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label10PrintDevice.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;label10PrintDevice.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="&gt;&gt;cb10PrintDevice.Name" xml:space="preserve">
    <value>cb10PrintDevice</value>
  </data>
  <data name="&gt;&gt;cb10PrintDevice.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cb10PrintDevice.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;cb10PrintDevice.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="tab10Print.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tab10Print.Size" type="System.Drawing.Size, System.Drawing">
    <value>592, 214</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="tab10Print.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="tab10Print.Text" xml:space="preserve">
    <value>10 Print</value>
  </data>
  <data name="&gt;&gt;tab10Print.Name" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;tab10Print.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tab10Print.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tab10Print.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="checkFingerprintBase.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="checkFingerprintBase.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkFingerprintBase.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkFingerprintBase.Location" type="System.Drawing.Point, System.Drawing">
    <value>207, 73</value>
  </data>
  <data name="checkFingerprintBase.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 24</value>
  </data>
  <data name="checkFingerprintBase.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="checkFingerprintBase.Text" xml:space="preserve">
    <value>Enable fingerprint</value>
  </data>
  <data name="&gt;&gt;checkFingerprintBase.Name" xml:space="preserve">
    <value>checkFingerprintBase</value>
  </data>
  <data name="&gt;&gt;checkFingerprintBase.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkFingerprintBase.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;checkFingerprintBase.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="buttonTest10PrintDisplay.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonTest10PrintDisplay.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonTest10PrintDisplay.Location" type="System.Drawing.Point, System.Drawing">
    <value>416, 174</value>
  </data>
  <data name="buttonTest10PrintDisplay.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonTest10PrintDisplay.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="buttonTest10PrintDisplay.Text" xml:space="preserve">
    <value>Test 10 Print &amp;Display</value>
  </data>
  <data name="&gt;&gt;buttonTest10PrintDisplay.Name" xml:space="preserve">
    <value>buttonTest10PrintDisplay</value>
  </data>
  <data name="&gt;&gt;buttonTest10PrintDisplay.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonTest10PrintDisplay.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;buttonTest10PrintDisplay.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="checkBoxFingerMapping10Print.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="checkBoxFingerMapping10Print.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkBoxFingerMapping10Print.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBoxFingerMapping10Print.Location" type="System.Drawing.Point, System.Drawing">
    <value>207, 97</value>
  </data>
  <data name="checkBoxFingerMapping10Print.Size" type="System.Drawing.Size, System.Drawing">
    <value>116, 24</value>
  </data>
  <data name="checkBoxFingerMapping10Print.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="checkBoxFingerMapping10Print.Text" xml:space="preserve">
    <value>10 finger mapping </value>
  </data>
  <data name="&gt;&gt;checkBoxFingerMapping10Print.Name" xml:space="preserve">
    <value>checkBoxFingerMapping10Print</value>
  </data>
  <data name="&gt;&gt;checkBoxFingerMapping10Print.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBoxFingerMapping10Print.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;checkBoxFingerMapping10Print.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="check10Print.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="check10Print.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="check10Print.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 48</value>
  </data>
  <data name="check10Print.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="check10Print.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="check10Print.Text" xml:space="preserve">
    <value>Enable 10 Print Capture</value>
  </data>
  <data name="&gt;&gt;check10Print.Name" xml:space="preserve">
    <value>check10Print</value>
  </data>
  <data name="&gt;&gt;check10Print.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;check10Print.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;check10Print.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label10PrintInstances.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label10PrintInstances.Location" type="System.Drawing.Point, System.Drawing">
    <value>263, 49</value>
  </data>
  <data name="label10PrintInstances.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 24</value>
  </data>
  <data name="label10PrintInstances.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="label10PrintInstances.Text" xml:space="preserve">
    <value>Instances</value>
  </data>
  <data name="label10PrintInstances.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label10PrintInstances.Name" xml:space="preserve">
    <value>label10PrintInstances</value>
  </data>
  <data name="&gt;&gt;label10PrintInstances.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label10PrintInstances.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;label10PrintInstances.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="numericUpDown10PrintInstances.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="numericUpDown10PrintInstances.Location" type="System.Drawing.Point, System.Drawing">
    <value>207, 48</value>
  </data>
  <data name="numericUpDown10PrintInstances.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 20</value>
  </data>
  <data name="numericUpDown10PrintInstances.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;numericUpDown10PrintInstances.Name" xml:space="preserve">
    <value>numericUpDown10PrintInstances</value>
  </data>
  <data name="&gt;&gt;numericUpDown10PrintInstances.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numericUpDown10PrintInstances.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;numericUpDown10PrintInstances.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="label21.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label21.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 8</value>
  </data>
  <data name="label21.Size" type="System.Drawing.Size, System.Drawing">
    <value>288, 16</value>
  </data>
  <data name="label21.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="label21.Text" xml:space="preserve">
    <value>10 Print Path is DataRootDirectory + Fingerpr</value>
  </data>
  <data name="&gt;&gt;label21.Name" xml:space="preserve">
    <value>label21</value>
  </data>
  <data name="&gt;&gt;label21.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label21.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;label21.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="textBox10PrintID.Location" type="System.Drawing.Point, System.Drawing">
    <value>488, 112</value>
  </data>
  <data name="textBox10PrintID.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 20</value>
  </data>
  <data name="textBox10PrintID.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="textBox10PrintID.Text" xml:space="preserve">
    <value>test</value>
  </data>
  <data name="&gt;&gt;textBox10PrintID.Name" xml:space="preserve">
    <value>textBox10PrintID</value>
  </data>
  <data name="&gt;&gt;textBox10PrintID.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox10PrintID.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;textBox10PrintID.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="label10PrintTestID.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label10PrintTestID.Location" type="System.Drawing.Point, System.Drawing">
    <value>380, 112</value>
  </data>
  <data name="label10PrintTestID.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="label10PrintTestID.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="label10PrintTestID.Text" xml:space="preserve">
    <value>Test10 Print ID:</value>
  </data>
  <data name="label10PrintTestID.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;label10PrintTestID.Name" xml:space="preserve">
    <value>label10PrintTestID</value>
  </data>
  <data name="&gt;&gt;label10PrintTestID.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label10PrintTestID.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;label10PrintTestID.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="buttonTest10PrintCapture.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonTest10PrintCapture.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonTest10PrintCapture.Location" type="System.Drawing.Point, System.Drawing">
    <value>416, 144</value>
  </data>
  <data name="buttonTest10PrintCapture.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonTest10PrintCapture.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="buttonTest10PrintCapture.Text" xml:space="preserve">
    <value>&amp;Test 10 Print Capture</value>
  </data>
  <data name="&gt;&gt;buttonTest10PrintCapture.Name" xml:space="preserve">
    <value>buttonTest10PrintCapture</value>
  </data>
  <data name="&gt;&gt;buttonTest10PrintCapture.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonTest10PrintCapture.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;buttonTest10PrintCapture.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="button10PrintDeviceProperties.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="button10PrintDeviceProperties.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="button10PrintDeviceProperties.Location" type="System.Drawing.Point, System.Drawing">
    <value>184, 176</value>
  </data>
  <data name="button10PrintDeviceProperties.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="button10PrintDeviceProperties.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="button10PrintDeviceProperties.Text" xml:space="preserve">
    <value>Device &amp;Properties</value>
  </data>
  <data name="&gt;&gt;button10PrintDeviceProperties.Name" xml:space="preserve">
    <value>button10PrintDeviceProperties</value>
  </data>
  <data name="&gt;&gt;button10PrintDeviceProperties.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button10PrintDeviceProperties.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;button10PrintDeviceProperties.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="button10PrintFinisherProperties.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="button10PrintFinisherProperties.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="button10PrintFinisherProperties.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 176</value>
  </data>
  <data name="button10PrintFinisherProperties.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="button10PrintFinisherProperties.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="button10PrintFinisherProperties.Text" xml:space="preserve">
    <value>&amp;Finisher Properties</value>
  </data>
  <data name="&gt;&gt;button10PrintFinisherProperties.Name" xml:space="preserve">
    <value>button10PrintFinisherProperties</value>
  </data>
  <data name="&gt;&gt;button10PrintFinisherProperties.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button10PrintFinisherProperties.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;button10PrintFinisherProperties.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="label10PrintDevice.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label10PrintDevice.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 81</value>
  </data>
  <data name="label10PrintDevice.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 16</value>
  </data>
  <data name="label10PrintDevice.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="label10PrintDevice.Text" xml:space="preserve">
    <value>10 Print Capture Device</value>
  </data>
  <data name="&gt;&gt;label10PrintDevice.Name" xml:space="preserve">
    <value>label10PrintDevice</value>
  </data>
  <data name="&gt;&gt;label10PrintDevice.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label10PrintDevice.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;label10PrintDevice.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="cb10PrintDevice.ItemHeight" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="cb10PrintDevice.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 97</value>
  </data>
  <data name="cb10PrintDevice.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 21</value>
  </data>
  <data name="cb10PrintDevice.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="cb10PrintDevice.Text" xml:space="preserve">
    <value>No Devices Available</value>
  </data>
  <data name="&gt;&gt;cb10PrintDevice.Name" xml:space="preserve">
    <value>cb10PrintDevice</value>
  </data>
  <data name="&gt;&gt;cb10PrintDevice.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cb10PrintDevice.Parent" xml:space="preserve">
    <value>tab10Print</value>
  </data>
  <data name="&gt;&gt;cb10PrintDevice.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="labelDCSSDKPath.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelDCSSDKPath.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 8</value>
  </data>
  <data name="labelDCSSDKPath.Size" type="System.Drawing.Size, System.Drawing">
    <value>130, 13</value>
  </data>
  <data name="labelDCSSDKPath.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="labelDCSSDKPath.Text" xml:space="preserve">
    <value>Machine Index File:</value>
  </data>
  <data name="labelDCSSDKPath.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;labelDCSSDKPath.Name" xml:space="preserve">
    <value>labelDCSSDKPath</value>
  </data>
  <data name="&gt;&gt;labelDCSSDKPath.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelDCSSDKPath.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelDCSSDKPath.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="tbMachineIndexFile.Location" type="System.Drawing.Point, System.Drawing">
    <value>312, 8</value>
  </data>
  <data name="tbMachineIndexFile.Size" type="System.Drawing.Size, System.Drawing">
    <value>304, 13</value>
  </data>
  <data name="tbMachineIndexFile.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="tbMachineIndexFile.Text" xml:space="preserve">
    <value>index file</value>
  </data>
  <data name="&gt;&gt;tbMachineIndexFile.Name" xml:space="preserve">
    <value>tbMachineIndexFile</value>
  </data>
  <data name="&gt;&gt;tbMachineIndexFile.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbMachineIndexFile.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbMachineIndexFile.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="tbInstallDirectory.Location" type="System.Drawing.Point, System.Drawing">
    <value>312, 24</value>
  </data>
  <data name="tbInstallDirectory.Size" type="System.Drawing.Size, System.Drawing">
    <value>304, 13</value>
  </data>
  <data name="tbInstallDirectory.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="tbInstallDirectory.Text" xml:space="preserve">
    <value>install dir</value>
  </data>
  <data name="&gt;&gt;tbInstallDirectory.Name" xml:space="preserve">
    <value>tbInstallDirectory</value>
  </data>
  <data name="&gt;&gt;tbInstallDirectory.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbInstallDirectory.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbInstallDirectory.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="labelDCSDataPath.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelDCSDataPath.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 24</value>
  </data>
  <data name="labelDCSDataPath.Size" type="System.Drawing.Size, System.Drawing">
    <value>130, 13</value>
  </data>
  <data name="labelDCSDataPath.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="labelDCSDataPath.Text" xml:space="preserve">
    <value>Installation Directory:</value>
  </data>
  <data name="labelDCSDataPath.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;labelDCSDataPath.Name" xml:space="preserve">
    <value>labelDCSDataPath</value>
  </data>
  <data name="&gt;&gt;labelDCSDataPath.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelDCSDataPath.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelDCSDataPath.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="checkPortrait.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkPortrait.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkPortrait.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 48</value>
  </data>
  <data name="checkPortrait.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 24</value>
  </data>
  <data name="checkPortrait.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="checkPortrait.Text" xml:space="preserve">
    <value>Enable portrait</value>
  </data>
  <data name="&gt;&gt;checkPortrait.Name" xml:space="preserve">
    <value>checkPortrait</value>
  </data>
  <data name="&gt;&gt;checkPortrait.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkPortrait.Parent" xml:space="preserve">
    <value>tabPortrait</value>
  </data>
  <data name="&gt;&gt;checkPortrait.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="checkPortraitScanner.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkPortraitScanner.Location" type="System.Drawing.Point, System.Drawing">
    <value>208, 120</value>
  </data>
  <data name="checkPortraitScanner.Size" type="System.Drawing.Size, System.Drawing">
    <value>180, 24</value>
  </data>
  <data name="checkPortraitScanner.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="checkPortraitScanner.Text" xml:space="preserve">
    <value>Use Forms Scanner</value>
  </data>
  <data name="&gt;&gt;checkPortraitScanner.Name" xml:space="preserve">
    <value>checkPortraitScanner</value>
  </data>
  <data name="&gt;&gt;checkPortraitScanner.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkPortraitScanner.Parent" xml:space="preserve">
    <value>tabPortrait</value>
  </data>
  <data name="&gt;&gt;checkPortraitScanner.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>256, 48</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 24</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>Instances</value>
  </data>
  <data name="label4.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>tabPortrait</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="numericUpDownPortraitInstances.Location" type="System.Drawing.Point, System.Drawing">
    <value>208, 48</value>
  </data>
  <data name="numericUpDownPortraitInstances.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 20</value>
  </data>
  <data name="numericUpDownPortraitInstances.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;numericUpDownPortraitInstances.Name" xml:space="preserve">
    <value>numericUpDownPortraitInstances</value>
  </data>
  <data name="&gt;&gt;numericUpDownPortraitInstances.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numericUpDownPortraitInstances.Parent" xml:space="preserve">
    <value>tabPortrait</value>
  </data>
  <data name="&gt;&gt;numericUpDownPortraitInstances.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="buttonTestPortraitDisplay.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonTestPortraitDisplay.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonTestPortraitDisplay.Location" type="System.Drawing.Point, System.Drawing">
    <value>416, 176</value>
  </data>
  <data name="buttonTestPortraitDisplay.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonTestPortraitDisplay.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="buttonTestPortraitDisplay.Text" xml:space="preserve">
    <value>Test Portrait &amp;Display</value>
  </data>
  <data name="&gt;&gt;buttonTestPortraitDisplay.Name" xml:space="preserve">
    <value>buttonTestPortraitDisplay</value>
  </data>
  <data name="&gt;&gt;buttonTestPortraitDisplay.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonTestPortraitDisplay.Parent" xml:space="preserve">
    <value>tabPortrait</value>
  </data>
  <data name="&gt;&gt;buttonTestPortraitDisplay.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="checkPortraitDisplay.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkPortraitDisplay.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkPortraitDisplay.Location" type="System.Drawing.Point, System.Drawing">
    <value>208, 72</value>
  </data>
  <data name="checkPortraitDisplay.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 24</value>
  </data>
  <data name="checkPortraitDisplay.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="checkPortraitDisplay.Text" xml:space="preserve">
    <value>Enable display</value>
  </data>
  <data name="&gt;&gt;checkPortraitDisplay.Name" xml:space="preserve">
    <value>checkPortraitDisplay</value>
  </data>
  <data name="&gt;&gt;checkPortraitDisplay.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkPortraitDisplay.Parent" xml:space="preserve">
    <value>tabPortrait</value>
  </data>
  <data name="&gt;&gt;checkPortraitDisplay.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="tbTestPhotoID.Location" type="System.Drawing.Point, System.Drawing">
    <value>488, 112</value>
  </data>
  <data name="tbTestPhotoID.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 20</value>
  </data>
  <data name="tbTestPhotoID.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="tbTestPhotoID.Text" xml:space="preserve">
    <value>test</value>
  </data>
  <data name="&gt;&gt;tbTestPhotoID.Name" xml:space="preserve">
    <value>tbTestPhotoID</value>
  </data>
  <data name="&gt;&gt;tbTestPhotoID.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbTestPhotoID.Parent" xml:space="preserve">
    <value>tabPortrait</value>
  </data>
  <data name="&gt;&gt;tbTestPhotoID.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="checkPortraitCapture.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkPortraitCapture.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkPortraitCapture.Location" type="System.Drawing.Point, System.Drawing">
    <value>208, 96</value>
  </data>
  <data name="checkPortraitCapture.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 24</value>
  </data>
  <data name="checkPortraitCapture.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="checkPortraitCapture.Text" xml:space="preserve">
    <value>Capture Live</value>
  </data>
  <data name="&gt;&gt;checkPortraitCapture.Name" xml:space="preserve">
    <value>checkPortraitCapture</value>
  </data>
  <data name="&gt;&gt;checkPortraitCapture.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkPortraitCapture.Parent" xml:space="preserve">
    <value>tabPortrait</value>
  </data>
  <data name="&gt;&gt;checkPortraitCapture.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="cbPortraitDevice.ItemHeight" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="cbPortraitDevice.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 96</value>
  </data>
  <data name="cbPortraitDevice.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 21</value>
  </data>
  <data name="cbPortraitDevice.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;cbPortraitDevice.Name" xml:space="preserve">
    <value>cbPortraitDevice</value>
  </data>
  <data name="&gt;&gt;cbPortraitDevice.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbPortraitDevice.Parent" xml:space="preserve">
    <value>tabPortrait</value>
  </data>
  <data name="&gt;&gt;cbPortraitDevice.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="label1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>368, 112</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 16</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>Test Photo ID:</value>
  </data>
  <data name="label1.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>tabPortrait</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="buttonTestPortraitCapture.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonTestPortraitCapture.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonTestPortraitCapture.Location" type="System.Drawing.Point, System.Drawing">
    <value>416, 144</value>
  </data>
  <data name="buttonTestPortraitCapture.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonTestPortraitCapture.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="buttonTestPortraitCapture.Text" xml:space="preserve">
    <value>&amp;Test Portrait Capture</value>
  </data>
  <data name="&gt;&gt;buttonTestPortraitCapture.Name" xml:space="preserve">
    <value>buttonTestPortraitCapture</value>
  </data>
  <data name="&gt;&gt;buttonTestPortraitCapture.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonTestPortraitCapture.Parent" xml:space="preserve">
    <value>tabPortrait</value>
  </data>
  <data name="&gt;&gt;buttonTestPortraitCapture.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="buttonPortraitDeviceProperties.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonPortraitDeviceProperties.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonPortraitDeviceProperties.Location" type="System.Drawing.Point, System.Drawing">
    <value>184, 176</value>
  </data>
  <data name="buttonPortraitDeviceProperties.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonPortraitDeviceProperties.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="buttonPortraitDeviceProperties.Text" xml:space="preserve">
    <value>Device &amp;Properties</value>
  </data>
  <data name="&gt;&gt;buttonPortraitDeviceProperties.Name" xml:space="preserve">
    <value>buttonPortraitDeviceProperties</value>
  </data>
  <data name="&gt;&gt;buttonPortraitDeviceProperties.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPortraitDeviceProperties.Parent" xml:space="preserve">
    <value>tabPortrait</value>
  </data>
  <data name="&gt;&gt;buttonPortraitDeviceProperties.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="buttonPortraitFinisherProperties.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonPortraitFinisherProperties.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonPortraitFinisherProperties.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 176</value>
  </data>
  <data name="buttonPortraitFinisherProperties.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonPortraitFinisherProperties.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="buttonPortraitFinisherProperties.Text" xml:space="preserve">
    <value>&amp;Finisher Properties</value>
  </data>
  <data name="&gt;&gt;buttonPortraitFinisherProperties.Name" xml:space="preserve">
    <value>buttonPortraitFinisherProperties</value>
  </data>
  <data name="&gt;&gt;buttonPortraitFinisherProperties.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPortraitFinisherProperties.Parent" xml:space="preserve">
    <value>tabPortrait</value>
  </data>
  <data name="&gt;&gt;buttonPortraitFinisherProperties.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="labelPortraitDevice.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelPortraitDevice.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 80</value>
  </data>
  <data name="labelPortraitDevice.Size" type="System.Drawing.Size, System.Drawing">
    <value>160, 16</value>
  </data>
  <data name="labelPortraitDevice.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="labelPortraitDevice.Text" xml:space="preserve">
    <value>Portrait Capture Device</value>
  </data>
  <data name="&gt;&gt;labelPortraitDevice.Name" xml:space="preserve">
    <value>labelPortraitDevice</value>
  </data>
  <data name="&gt;&gt;labelPortraitDevice.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelPortraitDevice.Parent" xml:space="preserve">
    <value>tabPortrait</value>
  </data>
  <data name="&gt;&gt;labelPortraitDevice.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="labelPortraitPath.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelPortraitPath.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 8</value>
  </data>
  <data name="labelPortraitPath.Size" type="System.Drawing.Size, System.Drawing">
    <value>296, 16</value>
  </data>
  <data name="labelPortraitPath.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="labelPortraitPath.Text" xml:space="preserve">
    <value>Portrait Path is DataRootDirectory + Portrait</value>
  </data>
  <data name="&gt;&gt;labelPortraitPath.Name" xml:space="preserve">
    <value>labelPortraitPath</value>
  </data>
  <data name="&gt;&gt;labelPortraitPath.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelPortraitPath.Parent" xml:space="preserve">
    <value>tabPortrait</value>
  </data>
  <data name="&gt;&gt;labelPortraitPath.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="tabPortrait.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tabPortrait.Size" type="System.Drawing.Size, System.Drawing">
    <value>592, 214</value>
  </data>
  <data name="tabPortrait.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="tabPortrait.Text" xml:space="preserve">
    <value>Portrait</value>
  </data>
  <data name="&gt;&gt;tabPortrait.Name" xml:space="preserve">
    <value>tabPortrait</value>
  </data>
  <data name="&gt;&gt;tabPortrait.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabPortrait.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabPortrait.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="checkSignature.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkSignature.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkSignature.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 48</value>
  </data>
  <data name="checkSignature.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 24</value>
  </data>
  <data name="checkSignature.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="checkSignature.Text" xml:space="preserve">
    <value>Enable signature</value>
  </data>
  <data name="&gt;&gt;checkSignature.Name" xml:space="preserve">
    <value>checkSignature</value>
  </data>
  <data name="&gt;&gt;checkSignature.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkSignature.Parent" xml:space="preserve">
    <value>tabSignature</value>
  </data>
  <data name="&gt;&gt;checkSignature.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="checkSignatureScanner.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkSignatureScanner.Location" type="System.Drawing.Point, System.Drawing">
    <value>208, 120</value>
  </data>
  <data name="checkSignatureScanner.Size" type="System.Drawing.Size, System.Drawing">
    <value>180, 24</value>
  </data>
  <data name="checkSignatureScanner.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="checkSignatureScanner.Text" xml:space="preserve">
    <value>Use Forms Scanner</value>
  </data>
  <data name="&gt;&gt;checkSignatureScanner.Name" xml:space="preserve">
    <value>checkSignatureScanner</value>
  </data>
  <data name="&gt;&gt;checkSignatureScanner.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkSignatureScanner.Parent" xml:space="preserve">
    <value>tabSignature</value>
  </data>
  <data name="&gt;&gt;checkSignatureScanner.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label7.Location" type="System.Drawing.Point, System.Drawing">
    <value>256, 48</value>
  </data>
  <data name="label7.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 24</value>
  </data>
  <data name="label7.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="label7.Text" xml:space="preserve">
    <value>Instances</value>
  </data>
  <data name="label7.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label7.Name" xml:space="preserve">
    <value>label7</value>
  </data>
  <data name="&gt;&gt;label7.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label7.Parent" xml:space="preserve">
    <value>tabSignature</value>
  </data>
  <data name="&gt;&gt;label7.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="numericUpDownSignatureInstances.Location" type="System.Drawing.Point, System.Drawing">
    <value>208, 48</value>
  </data>
  <data name="numericUpDownSignatureInstances.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 20</value>
  </data>
  <data name="numericUpDownSignatureInstances.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;numericUpDownSignatureInstances.Name" xml:space="preserve">
    <value>numericUpDownSignatureInstances</value>
  </data>
  <data name="&gt;&gt;numericUpDownSignatureInstances.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numericUpDownSignatureInstances.Parent" xml:space="preserve">
    <value>tabSignature</value>
  </data>
  <data name="&gt;&gt;numericUpDownSignatureInstances.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="buttonTestSigDisplay.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonTestSigDisplay.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonTestSigDisplay.Location" type="System.Drawing.Point, System.Drawing">
    <value>416, 176</value>
  </data>
  <data name="buttonTestSigDisplay.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonTestSigDisplay.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="buttonTestSigDisplay.Text" xml:space="preserve">
    <value>Test Signature &amp;Display</value>
  </data>
  <data name="&gt;&gt;buttonTestSigDisplay.Name" xml:space="preserve">
    <value>buttonTestSigDisplay</value>
  </data>
  <data name="&gt;&gt;buttonTestSigDisplay.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonTestSigDisplay.Parent" xml:space="preserve">
    <value>tabSignature</value>
  </data>
  <data name="&gt;&gt;buttonTestSigDisplay.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="checkSignatureDisplay.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkSignatureDisplay.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkSignatureDisplay.Location" type="System.Drawing.Point, System.Drawing">
    <value>208, 72</value>
  </data>
  <data name="checkSignatureDisplay.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 24</value>
  </data>
  <data name="checkSignatureDisplay.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="checkSignatureDisplay.Text" xml:space="preserve">
    <value>Enable display</value>
  </data>
  <data name="&gt;&gt;checkSignatureDisplay.Name" xml:space="preserve">
    <value>checkSignatureDisplay</value>
  </data>
  <data name="&gt;&gt;checkSignatureDisplay.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkSignatureDisplay.Parent" xml:space="preserve">
    <value>tabSignature</value>
  </data>
  <data name="&gt;&gt;checkSignatureDisplay.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="tbTestSigID.Location" type="System.Drawing.Point, System.Drawing">
    <value>488, 112</value>
  </data>
  <data name="tbTestSigID.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 20</value>
  </data>
  <data name="tbTestSigID.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="tbTestSigID.Text" xml:space="preserve">
    <value>test</value>
  </data>
  <data name="&gt;&gt;tbTestSigID.Name" xml:space="preserve">
    <value>tbTestSigID</value>
  </data>
  <data name="&gt;&gt;tbTestSigID.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbTestSigID.Parent" xml:space="preserve">
    <value>tabSignature</value>
  </data>
  <data name="&gt;&gt;tbTestSigID.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="label2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>368, 112</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 16</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>Test Signature ID:</value>
  </data>
  <data name="label2.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>tabSignature</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="buttonTestSigCapture.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonTestSigCapture.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonTestSigCapture.Location" type="System.Drawing.Point, System.Drawing">
    <value>416, 144</value>
  </data>
  <data name="buttonTestSigCapture.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonTestSigCapture.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="buttonTestSigCapture.Text" xml:space="preserve">
    <value>&amp;Test Signature Capture</value>
  </data>
  <data name="&gt;&gt;buttonTestSigCapture.Name" xml:space="preserve">
    <value>buttonTestSigCapture</value>
  </data>
  <data name="&gt;&gt;buttonTestSigCapture.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonTestSigCapture.Parent" xml:space="preserve">
    <value>tabSignature</value>
  </data>
  <data name="&gt;&gt;buttonTestSigCapture.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="buttonSigDeviceProperties.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonSigDeviceProperties.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonSigDeviceProperties.Location" type="System.Drawing.Point, System.Drawing">
    <value>184, 176</value>
  </data>
  <data name="buttonSigDeviceProperties.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonSigDeviceProperties.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="buttonSigDeviceProperties.Text" xml:space="preserve">
    <value>Device &amp;Properties</value>
  </data>
  <data name="&gt;&gt;buttonSigDeviceProperties.Name" xml:space="preserve">
    <value>buttonSigDeviceProperties</value>
  </data>
  <data name="&gt;&gt;buttonSigDeviceProperties.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonSigDeviceProperties.Parent" xml:space="preserve">
    <value>tabSignature</value>
  </data>
  <data name="&gt;&gt;buttonSigDeviceProperties.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="buttonSigFinisherProperties.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonSigFinisherProperties.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonSigFinisherProperties.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 176</value>
  </data>
  <data name="buttonSigFinisherProperties.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonSigFinisherProperties.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="buttonSigFinisherProperties.Text" xml:space="preserve">
    <value>&amp;Finisher Properties</value>
  </data>
  <data name="&gt;&gt;buttonSigFinisherProperties.Name" xml:space="preserve">
    <value>buttonSigFinisherProperties</value>
  </data>
  <data name="&gt;&gt;buttonSigFinisherProperties.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonSigFinisherProperties.Parent" xml:space="preserve">
    <value>tabSignature</value>
  </data>
  <data name="&gt;&gt;buttonSigFinisherProperties.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="checkSignatureCapture.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkSignatureCapture.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkSignatureCapture.Location" type="System.Drawing.Point, System.Drawing">
    <value>208, 96</value>
  </data>
  <data name="checkSignatureCapture.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 24</value>
  </data>
  <data name="checkSignatureCapture.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="checkSignatureCapture.Text" xml:space="preserve">
    <value>Capture Live</value>
  </data>
  <data name="&gt;&gt;checkSignatureCapture.Name" xml:space="preserve">
    <value>checkSignatureCapture</value>
  </data>
  <data name="&gt;&gt;checkSignatureCapture.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkSignatureCapture.Parent" xml:space="preserve">
    <value>tabSignature</value>
  </data>
  <data name="&gt;&gt;checkSignatureCapture.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="labelSignatureDevice.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelSignatureDevice.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 80</value>
  </data>
  <data name="labelSignatureDevice.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 16</value>
  </data>
  <data name="labelSignatureDevice.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="labelSignatureDevice.Text" xml:space="preserve">
    <value>Signature Capture Device</value>
  </data>
  <data name="&gt;&gt;labelSignatureDevice.Name" xml:space="preserve">
    <value>labelSignatureDevice</value>
  </data>
  <data name="&gt;&gt;labelSignatureDevice.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelSignatureDevice.Parent" xml:space="preserve">
    <value>tabSignature</value>
  </data>
  <data name="&gt;&gt;labelSignatureDevice.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="labelSignaturePath.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelSignaturePath.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 8</value>
  </data>
  <data name="labelSignaturePath.Size" type="System.Drawing.Size, System.Drawing">
    <value>288, 16</value>
  </data>
  <data name="labelSignaturePath.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="labelSignaturePath.Text" xml:space="preserve">
    <value>Signature Path is DataRootDirectory + Signatur</value>
  </data>
  <data name="&gt;&gt;labelSignaturePath.Name" xml:space="preserve">
    <value>labelSignaturePath</value>
  </data>
  <data name="&gt;&gt;labelSignaturePath.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelSignaturePath.Parent" xml:space="preserve">
    <value>tabSignature</value>
  </data>
  <data name="&gt;&gt;labelSignaturePath.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="cbSignatureDevice.ItemHeight" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="cbSignatureDevice.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 96</value>
  </data>
  <data name="cbSignatureDevice.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 21</value>
  </data>
  <data name="cbSignatureDevice.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;cbSignatureDevice.Name" xml:space="preserve">
    <value>cbSignatureDevice</value>
  </data>
  <data name="&gt;&gt;cbSignatureDevice.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbSignatureDevice.Parent" xml:space="preserve">
    <value>tabSignature</value>
  </data>
  <data name="&gt;&gt;cbSignatureDevice.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="tabSignature.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tabSignature.Size" type="System.Drawing.Size, System.Drawing">
    <value>592, 214</value>
  </data>
  <data name="tabSignature.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="tabSignature.Text" xml:space="preserve">
    <value>Signature</value>
  </data>
  <data name="&gt;&gt;tabSignature.Name" xml:space="preserve">
    <value>tabSignature</value>
  </data>
  <data name="&gt;&gt;tabSignature.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabSignature.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabSignature.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="checkBoxQCVerify.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkBoxQCVerify.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBoxQCVerify.Location" type="System.Drawing.Point, System.Drawing">
    <value>416, 48</value>
  </data>
  <data name="checkBoxQCVerify.Size" type="System.Drawing.Size, System.Drawing">
    <value>173, 24</value>
  </data>
  <data name="checkBoxQCVerify.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="checkBoxQCVerify.Text" xml:space="preserve">
    <value>Verify after capture</value>
  </data>
  <data name="&gt;&gt;checkBoxQCVerify.Name" xml:space="preserve">
    <value>checkBoxQCVerify</value>
  </data>
  <data name="&gt;&gt;checkBoxQCVerify.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBoxQCVerify.Parent" xml:space="preserve">
    <value>tabFingerprint</value>
  </data>
  <data name="&gt;&gt;checkBoxQCVerify.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="buttonFingerMapping.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonFingerMapping.Location" type="System.Drawing.Point, System.Drawing">
    <value>148, 132</value>
  </data>
  <data name="buttonFingerMapping.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 24</value>
  </data>
  <data name="buttonFingerMapping.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="buttonFingerMapping.Text" xml:space="preserve">
    <value>&amp;Map</value>
  </data>
  <data name="&gt;&gt;buttonFingerMapping.Name" xml:space="preserve">
    <value>buttonFingerMapping</value>
  </data>
  <data name="&gt;&gt;buttonFingerMapping.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonFingerMapping.Parent" xml:space="preserve">
    <value>tabFingerprint</value>
  </data>
  <data name="&gt;&gt;buttonFingerMapping.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="checkBoxFingerMapping.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkBoxFingerMapping.Location" type="System.Drawing.Point, System.Drawing">
    <value>26, 132</value>
  </data>
  <data name="checkBoxFingerMapping.Size" type="System.Drawing.Size, System.Drawing">
    <value>116, 24</value>
  </data>
  <data name="checkBoxFingerMapping.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="checkBoxFingerMapping.Text" xml:space="preserve">
    <value>10 finger mapping </value>
  </data>
  <data name="&gt;&gt;checkBoxFingerMapping.Name" xml:space="preserve">
    <value>checkBoxFingerMapping</value>
  </data>
  <data name="&gt;&gt;checkBoxFingerMapping.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBoxFingerMapping.Parent" xml:space="preserve">
    <value>tabFingerprint</value>
  </data>
  <data name="&gt;&gt;checkBoxFingerMapping.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="checkGenFingerBIN.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkGenFingerBIN.Location" type="System.Drawing.Point, System.Drawing">
    <value>416, 23</value>
  </data>
  <data name="checkGenFingerBIN.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 24</value>
  </data>
  <data name="checkGenFingerBIN.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="checkGenFingerBIN.Text" xml:space="preserve">
    <value>Generate BIN</value>
  </data>
  <data name="&gt;&gt;checkGenFingerBIN.Name" xml:space="preserve">
    <value>checkGenFingerBIN</value>
  </data>
  <data name="&gt;&gt;checkGenFingerBIN.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkGenFingerBIN.Parent" xml:space="preserve">
    <value>tabFingerprint</value>
  </data>
  <data name="&gt;&gt;checkGenFingerBIN.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="checkDontSaveFingerImage.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="checkDontSaveFingerImage.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkDontSaveFingerImage.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkDontSaveFingerImage.Location" type="System.Drawing.Point, System.Drawing">
    <value>416, 72</value>
  </data>
  <data name="checkDontSaveFingerImage.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 24</value>
  </data>
  <data name="checkDontSaveFingerImage.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="checkDontSaveFingerImage.Text" xml:space="preserve">
    <value>Do not save images</value>
  </data>
  <data name="&gt;&gt;checkDontSaveFingerImage.Name" xml:space="preserve">
    <value>checkDontSaveFingerImage</value>
  </data>
  <data name="&gt;&gt;checkDontSaveFingerImage.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkDontSaveFingerImage.Parent" xml:space="preserve">
    <value>tabFingerprint</value>
  </data>
  <data name="&gt;&gt;checkDontSaveFingerImage.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="checkFingerprint.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkFingerprint.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkFingerprint.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 48</value>
  </data>
  <data name="checkFingerprint.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 24</value>
  </data>
  <data name="checkFingerprint.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="checkFingerprint.Text" xml:space="preserve">
    <value>Enable fingerprint</value>
  </data>
  <data name="&gt;&gt;checkFingerprint.Name" xml:space="preserve">
    <value>checkFingerprint</value>
  </data>
  <data name="&gt;&gt;checkFingerprint.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkFingerprint.Parent" xml:space="preserve">
    <value>tabFingerprint</value>
  </data>
  <data name="&gt;&gt;checkFingerprint.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="checkFingerprintScanner.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkFingerprintScanner.Location" type="System.Drawing.Point, System.Drawing">
    <value>208, 120</value>
  </data>
  <data name="checkFingerprintScanner.Size" type="System.Drawing.Size, System.Drawing">
    <value>180, 24</value>
  </data>
  <data name="checkFingerprintScanner.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="checkFingerprintScanner.Text" xml:space="preserve">
    <value>Use Forms Scanner</value>
  </data>
  <data name="&gt;&gt;checkFingerprintScanner.Name" xml:space="preserve">
    <value>checkFingerprintScanner</value>
  </data>
  <data name="&gt;&gt;checkFingerprintScanner.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkFingerprintScanner.Parent" xml:space="preserve">
    <value>tabFingerprint</value>
  </data>
  <data name="&gt;&gt;checkFingerprintScanner.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="label9.Location" type="System.Drawing.Point, System.Drawing">
    <value>256, 48</value>
  </data>
  <data name="label9.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 24</value>
  </data>
  <data name="label9.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="label9.Text" xml:space="preserve">
    <value>Instances</value>
  </data>
  <data name="label9.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label9.Name" xml:space="preserve">
    <value>label9</value>
  </data>
  <data name="&gt;&gt;label9.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label9.Parent" xml:space="preserve">
    <value>tabFingerprint</value>
  </data>
  <data name="&gt;&gt;label9.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="numericUpDownFingerInstances.Location" type="System.Drawing.Point, System.Drawing">
    <value>208, 48</value>
  </data>
  <data name="numericUpDownFingerInstances.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 20</value>
  </data>
  <data name="numericUpDownFingerInstances.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;numericUpDownFingerInstances.Name" xml:space="preserve">
    <value>numericUpDownFingerInstances</value>
  </data>
  <data name="&gt;&gt;numericUpDownFingerInstances.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numericUpDownFingerInstances.Parent" xml:space="preserve">
    <value>tabFingerprint</value>
  </data>
  <data name="&gt;&gt;numericUpDownFingerInstances.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="buttonTestFingerDisplay.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonTestFingerDisplay.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonTestFingerDisplay.Location" type="System.Drawing.Point, System.Drawing">
    <value>416, 176</value>
  </data>
  <data name="buttonTestFingerDisplay.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonTestFingerDisplay.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="buttonTestFingerDisplay.Text" xml:space="preserve">
    <value>Test Finger &amp;Display</value>
  </data>
  <data name="&gt;&gt;buttonTestFingerDisplay.Name" xml:space="preserve">
    <value>buttonTestFingerDisplay</value>
  </data>
  <data name="&gt;&gt;buttonTestFingerDisplay.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonTestFingerDisplay.Parent" xml:space="preserve">
    <value>tabFingerprint</value>
  </data>
  <data name="&gt;&gt;buttonTestFingerDisplay.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="checkFingerprintDisplay.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkFingerprintDisplay.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkFingerprintDisplay.Location" type="System.Drawing.Point, System.Drawing">
    <value>208, 72</value>
  </data>
  <data name="checkFingerprintDisplay.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 24</value>
  </data>
  <data name="checkFingerprintDisplay.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="checkFingerprintDisplay.Text" xml:space="preserve">
    <value>Enable display</value>
  </data>
  <data name="&gt;&gt;checkFingerprintDisplay.Name" xml:space="preserve">
    <value>checkFingerprintDisplay</value>
  </data>
  <data name="&gt;&gt;checkFingerprintDisplay.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkFingerprintDisplay.Parent" xml:space="preserve">
    <value>tabFingerprint</value>
  </data>
  <data name="&gt;&gt;checkFingerprintDisplay.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="tbFingerID.Location" type="System.Drawing.Point, System.Drawing">
    <value>488, 112</value>
  </data>
  <data name="tbFingerID.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 20</value>
  </data>
  <data name="tbFingerID.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="tbFingerID.Text" xml:space="preserve">
    <value>test</value>
  </data>
  <data name="&gt;&gt;tbFingerID.Name" xml:space="preserve">
    <value>tbFingerID</value>
  </data>
  <data name="&gt;&gt;tbFingerID.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbFingerID.Parent" xml:space="preserve">
    <value>tabFingerprint</value>
  </data>
  <data name="&gt;&gt;tbFingerID.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="label3.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>400, 112</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 16</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>Test Finger ID:</value>
  </data>
  <data name="label3.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>tabFingerprint</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="buttonTestFingerCapture.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonTestFingerCapture.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonTestFingerCapture.Location" type="System.Drawing.Point, System.Drawing">
    <value>416, 144</value>
  </data>
  <data name="buttonTestFingerCapture.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonTestFingerCapture.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="buttonTestFingerCapture.Text" xml:space="preserve">
    <value>&amp;Test Finger Capture</value>
  </data>
  <data name="&gt;&gt;buttonTestFingerCapture.Name" xml:space="preserve">
    <value>buttonTestFingerCapture</value>
  </data>
  <data name="&gt;&gt;buttonTestFingerCapture.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonTestFingerCapture.Parent" xml:space="preserve">
    <value>tabFingerprint</value>
  </data>
  <data name="&gt;&gt;buttonTestFingerCapture.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="buttonFingerDeviceProperties.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonFingerDeviceProperties.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonFingerDeviceProperties.Location" type="System.Drawing.Point, System.Drawing">
    <value>184, 176</value>
  </data>
  <data name="buttonFingerDeviceProperties.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonFingerDeviceProperties.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="buttonFingerDeviceProperties.Text" xml:space="preserve">
    <value>Device &amp;Properties</value>
  </data>
  <data name="&gt;&gt;buttonFingerDeviceProperties.Name" xml:space="preserve">
    <value>buttonFingerDeviceProperties</value>
  </data>
  <data name="&gt;&gt;buttonFingerDeviceProperties.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonFingerDeviceProperties.Parent" xml:space="preserve">
    <value>tabFingerprint</value>
  </data>
  <data name="&gt;&gt;buttonFingerDeviceProperties.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="buttonFingerFinisherProperties.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonFingerFinisherProperties.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonFingerFinisherProperties.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 176</value>
  </data>
  <data name="buttonFingerFinisherProperties.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonFingerFinisherProperties.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="buttonFingerFinisherProperties.Text" xml:space="preserve">
    <value>&amp;Finisher Properties</value>
  </data>
  <data name="&gt;&gt;buttonFingerFinisherProperties.Name" xml:space="preserve">
    <value>buttonFingerFinisherProperties</value>
  </data>
  <data name="&gt;&gt;buttonFingerFinisherProperties.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonFingerFinisherProperties.Parent" xml:space="preserve">
    <value>tabFingerprint</value>
  </data>
  <data name="&gt;&gt;buttonFingerFinisherProperties.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="checkFingerprintCapture.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkFingerprintCapture.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkFingerprintCapture.Location" type="System.Drawing.Point, System.Drawing">
    <value>208, 96</value>
  </data>
  <data name="checkFingerprintCapture.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 24</value>
  </data>
  <data name="checkFingerprintCapture.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="checkFingerprintCapture.Text" xml:space="preserve">
    <value>Capture Live</value>
  </data>
  <data name="&gt;&gt;checkFingerprintCapture.Name" xml:space="preserve">
    <value>checkFingerprintCapture</value>
  </data>
  <data name="&gt;&gt;checkFingerprintCapture.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkFingerprintCapture.Parent" xml:space="preserve">
    <value>tabFingerprint</value>
  </data>
  <data name="&gt;&gt;checkFingerprintCapture.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="labelFingerprintDevice.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelFingerprintDevice.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 80</value>
  </data>
  <data name="labelFingerprintDevice.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 16</value>
  </data>
  <data name="labelFingerprintDevice.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="labelFingerprintDevice.Text" xml:space="preserve">
    <value>Capture Device</value>
  </data>
  <data name="&gt;&gt;labelFingerprintDevice.Name" xml:space="preserve">
    <value>labelFingerprintDevice</value>
  </data>
  <data name="&gt;&gt;labelFingerprintDevice.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelFingerprintDevice.Parent" xml:space="preserve">
    <value>tabFingerprint</value>
  </data>
  <data name="&gt;&gt;labelFingerprintDevice.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="labelFingerprintPath.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelFingerprintPath.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 8</value>
  </data>
  <data name="labelFingerprintPath.Size" type="System.Drawing.Size, System.Drawing">
    <value>288, 16</value>
  </data>
  <data name="labelFingerprintPath.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelFingerprintPath.Text" xml:space="preserve">
    <value>Fingerprint Path is DataRootDirectory + Fingerpr</value>
  </data>
  <data name="&gt;&gt;labelFingerprintPath.Name" xml:space="preserve">
    <value>labelFingerprintPath</value>
  </data>
  <data name="&gt;&gt;labelFingerprintPath.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelFingerprintPath.Parent" xml:space="preserve">
    <value>tabFingerprint</value>
  </data>
  <data name="&gt;&gt;labelFingerprintPath.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="cbFingerprintDevice.ItemHeight" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="cbFingerprintDevice.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 96</value>
  </data>
  <data name="cbFingerprintDevice.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 21</value>
  </data>
  <data name="cbFingerprintDevice.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;cbFingerprintDevice.Name" xml:space="preserve">
    <value>cbFingerprintDevice</value>
  </data>
  <data name="&gt;&gt;cbFingerprintDevice.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbFingerprintDevice.Parent" xml:space="preserve">
    <value>tabFingerprint</value>
  </data>
  <data name="&gt;&gt;cbFingerprintDevice.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="tabFingerprint.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tabFingerprint.Size" type="System.Drawing.Size, System.Drawing">
    <value>592, 214</value>
  </data>
  <data name="tabFingerprint.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="tabFingerprint.Text" xml:space="preserve">
    <value>Fingerprint</value>
  </data>
  <data name="&gt;&gt;tabFingerprint.Name" xml:space="preserve">
    <value>tabFingerprint</value>
  </data>
  <data name="&gt;&gt;tabFingerprint.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabFingerprint.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabFingerprint.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;checkCertsDisplay.Name" xml:space="preserve">
    <value>checkCertsDisplay</value>
  </data>
  <data name="&gt;&gt;checkCertsDisplay.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkCertsDisplay.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;checkCertsDisplay.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;checkCertsCapture.Name" xml:space="preserve">
    <value>checkCertsCapture</value>
  </data>
  <data name="&gt;&gt;checkCertsCapture.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkCertsCapture.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;checkCertsCapture.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;tbScannerDeviceName.Name" xml:space="preserve">
    <value>tbScannerDeviceName</value>
  </data>
  <data name="&gt;&gt;tbScannerDeviceName.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbScannerDeviceName.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;tbScannerDeviceName.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;buttonSelectDevice.Name" xml:space="preserve">
    <value>buttonSelectDevice</value>
  </data>
  <data name="&gt;&gt;buttonSelectDevice.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonSelectDevice.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;buttonSelectDevice.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;checkCerts.Name" xml:space="preserve">
    <value>checkCerts</value>
  </data>
  <data name="&gt;&gt;checkCerts.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkCerts.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;checkCerts.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;labelCertsInstance.Name" xml:space="preserve">
    <value>labelCertsInstance</value>
  </data>
  <data name="&gt;&gt;labelCertsInstance.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelCertsInstance.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;labelCertsInstance.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;numericUpDownCertsInstance.Name" xml:space="preserve">
    <value>numericUpDownCertsInstance</value>
  </data>
  <data name="&gt;&gt;numericUpDownCertsInstance.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numericUpDownCertsInstance.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;numericUpDownCertsInstance.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="&gt;&gt;buttonTestCertsDisplay.Name" xml:space="preserve">
    <value>buttonTestCertsDisplay</value>
  </data>
  <data name="&gt;&gt;buttonTestCertsDisplay.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonTestCertsDisplay.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;buttonTestCertsDisplay.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="&gt;&gt;textBoxTestCertsID.Name" xml:space="preserve">
    <value>textBoxTestCertsID</value>
  </data>
  <data name="&gt;&gt;textBoxTestCertsID.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxTestCertsID.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;textBoxTestCertsID.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="&gt;&gt;labelCertsID.Name" xml:space="preserve">
    <value>labelCertsID</value>
  </data>
  <data name="&gt;&gt;labelCertsID.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelCertsID.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;labelCertsID.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="&gt;&gt;buttonTestCertsCapture.Name" xml:space="preserve">
    <value>buttonTestCertsCapture</value>
  </data>
  <data name="&gt;&gt;buttonTestCertsCapture.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonTestCertsCapture.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;buttonTestCertsCapture.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="&gt;&gt;buttonCertsFinisherProperties.Name" xml:space="preserve">
    <value>buttonCertsFinisherProperties</value>
  </data>
  <data name="&gt;&gt;buttonCertsFinisherProperties.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonCertsFinisherProperties.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;buttonCertsFinisherProperties.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="&gt;&gt;label20.Name" xml:space="preserve">
    <value>label20</value>
  </data>
  <data name="&gt;&gt;label20.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label20.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;label20.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="&gt;&gt;label22.Name" xml:space="preserve">
    <value>label22</value>
  </data>
  <data name="&gt;&gt;label22.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label22.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;label22.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="tabCerts.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tabCerts.Size" type="System.Drawing.Size, System.Drawing">
    <value>592, 214</value>
  </data>
  <data name="tabCerts.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="tabCerts.Text" xml:space="preserve">
    <value>Scan certificates</value>
  </data>
  <data name="&gt;&gt;tabCerts.Name" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;tabCerts.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabCerts.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabCerts.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;buttonReadFips.Name" xml:space="preserve">
    <value>buttonReadFips</value>
  </data>
  <data name="&gt;&gt;buttonReadFips.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonReadFips.Parent" xml:space="preserve">
    <value>tabCustomBiometric</value>
  </data>
  <data name="&gt;&gt;buttonReadFips.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;buttonGenFips.Name" xml:space="preserve">
    <value>buttonGenFips</value>
  </data>
  <data name="&gt;&gt;buttonGenFips.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonGenFips.Parent" xml:space="preserve">
    <value>tabCustomBiometric</value>
  </data>
  <data name="&gt;&gt;buttonGenFips.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;buttonGenDocBiometrics.Name" xml:space="preserve">
    <value>buttonGenDocBiometrics</value>
  </data>
  <data name="&gt;&gt;buttonGenDocBiometrics.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonGenDocBiometrics.Parent" xml:space="preserve">
    <value>tabCustomBiometric</value>
  </data>
  <data name="&gt;&gt;buttonGenDocBiometrics.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;checkLogBINErrors.Name" xml:space="preserve">
    <value>checkLogBINErrors</value>
  </data>
  <data name="&gt;&gt;checkLogBINErrors.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkLogBINErrors.Parent" xml:space="preserve">
    <value>tabCustomBiometric</value>
  </data>
  <data name="&gt;&gt;checkLogBINErrors.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;buttonGenBiometric.Name" xml:space="preserve">
    <value>buttonGenBiometric</value>
  </data>
  <data name="&gt;&gt;buttonGenBiometric.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonGenBiometric.Parent" xml:space="preserve">
    <value>tabCustomBiometric</value>
  </data>
  <data name="&gt;&gt;buttonGenBiometric.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;buttonShowBiometrics.Name" xml:space="preserve">
    <value>buttonShowBiometrics</value>
  </data>
  <data name="&gt;&gt;buttonShowBiometrics.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonShowBiometrics.Parent" xml:space="preserve">
    <value>tabCustomBiometric</value>
  </data>
  <data name="&gt;&gt;buttonShowBiometrics.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;tbBiometricID.Name" xml:space="preserve">
    <value>tbBiometricID</value>
  </data>
  <data name="&gt;&gt;tbBiometricID.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbBiometricID.Parent" xml:space="preserve">
    <value>tabCustomBiometric</value>
  </data>
  <data name="&gt;&gt;tbBiometricID.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="&gt;&gt;label10.Name" xml:space="preserve">
    <value>label10</value>
  </data>
  <data name="&gt;&gt;label10.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label10.Parent" xml:space="preserve">
    <value>tabCustomBiometric</value>
  </data>
  <data name="&gt;&gt;label10.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="&gt;&gt;groupBox2.Name" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;groupBox2.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox2.Parent" xml:space="preserve">
    <value>tabCustomBiometric</value>
  </data>
  <data name="&gt;&gt;groupBox2.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="tabCustomBiometric.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tabCustomBiometric.Size" type="System.Drawing.Size, System.Drawing">
    <value>592, 214</value>
  </data>
  <data name="tabCustomBiometric.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="tabCustomBiometric.Text" xml:space="preserve">
    <value>Biometrics</value>
  </data>
  <data name="&gt;&gt;tabCustomBiometric.Name" xml:space="preserve">
    <value>tabCustomBiometric</value>
  </data>
  <data name="&gt;&gt;tabCustomBiometric.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabCustomBiometric.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabCustomBiometric.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;comboBoxChipEncoderPort.Name" xml:space="preserve">
    <value>comboBoxChipEncoderPort</value>
  </data>
  <data name="&gt;&gt;comboBoxChipEncoderPort.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxChipEncoderPort.Parent" xml:space="preserve">
    <value>tabReaders</value>
  </data>
  <data name="&gt;&gt;comboBoxChipEncoderPort.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;label23.Name" xml:space="preserve">
    <value>label23</value>
  </data>
  <data name="&gt;&gt;label23.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label23.Parent" xml:space="preserve">
    <value>tabReaders</value>
  </data>
  <data name="&gt;&gt;label23.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;comboBoxBarcodeScannerPort.Name" xml:space="preserve">
    <value>comboBoxBarcodeScannerPort</value>
  </data>
  <data name="&gt;&gt;comboBoxBarcodeScannerPort.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxBarcodeScannerPort.Parent" xml:space="preserve">
    <value>tabReaders</value>
  </data>
  <data name="&gt;&gt;comboBoxBarcodeScannerPort.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;label17.Name" xml:space="preserve">
    <value>label17</value>
  </data>
  <data name="&gt;&gt;label17.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label17.Parent" xml:space="preserve">
    <value>tabReaders</value>
  </data>
  <data name="&gt;&gt;label17.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="tabReaders.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tabReaders.Size" type="System.Drawing.Size, System.Drawing">
    <value>592, 214</value>
  </data>
  <data name="tabReaders.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="tabReaders.Text" xml:space="preserve">
    <value>Readers</value>
  </data>
  <data name="&gt;&gt;tabReaders.Name" xml:space="preserve">
    <value>tabReaders</value>
  </data>
  <data name="&gt;&gt;tabReaders.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabReaders.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabReaders.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="&gt;&gt;label11.Name" xml:space="preserve">
    <value>label11</value>
  </data>
  <data name="&gt;&gt;label11.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label11.Parent" xml:space="preserve">
    <value>tabScanner</value>
  </data>
  <data name="&gt;&gt;label11.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;textBoxScannerName.Name" xml:space="preserve">
    <value>textBoxScannerName</value>
  </data>
  <data name="&gt;&gt;textBoxScannerName.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxScannerName.Parent" xml:space="preserve">
    <value>tabScanner</value>
  </data>
  <data name="&gt;&gt;textBoxScannerName.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;groupBox1.Name" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;groupBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox1.Parent" xml:space="preserve">
    <value>tabScanner</value>
  </data>
  <data name="&gt;&gt;groupBox1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;buttonScannerDisplay.Name" xml:space="preserve">
    <value>buttonScannerDisplay</value>
  </data>
  <data name="&gt;&gt;buttonScannerDisplay.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonScannerDisplay.Parent" xml:space="preserve">
    <value>tabScanner</value>
  </data>
  <data name="&gt;&gt;buttonScannerDisplay.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;textBoxScannerImageID.Name" xml:space="preserve">
    <value>textBoxScannerImageID</value>
  </data>
  <data name="&gt;&gt;textBoxScannerImageID.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxScannerImageID.Parent" xml:space="preserve">
    <value>tabScanner</value>
  </data>
  <data name="&gt;&gt;textBoxScannerImageID.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;label6.Name" xml:space="preserve">
    <value>label6</value>
  </data>
  <data name="&gt;&gt;label6.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label6.Parent" xml:space="preserve">
    <value>tabScanner</value>
  </data>
  <data name="&gt;&gt;label6.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;buttonScannerCapture.Name" xml:space="preserve">
    <value>buttonScannerCapture</value>
  </data>
  <data name="&gt;&gt;buttonScannerCapture.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonScannerCapture.Parent" xml:space="preserve">
    <value>tabScanner</value>
  </data>
  <data name="&gt;&gt;buttonScannerCapture.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="&gt;&gt;buttonScannerProperties.Name" xml:space="preserve">
    <value>buttonScannerProperties</value>
  </data>
  <data name="&gt;&gt;buttonScannerProperties.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonScannerProperties.Parent" xml:space="preserve">
    <value>tabScanner</value>
  </data>
  <data name="&gt;&gt;buttonScannerProperties.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="tabScanner.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tabScanner.Size" type="System.Drawing.Size, System.Drawing">
    <value>592, 214</value>
  </data>
  <data name="tabScanner.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="tabScanner.Text" xml:space="preserve">
    <value>Forms scanner</value>
  </data>
  <data name="&gt;&gt;tabScanner.Name" xml:space="preserve">
    <value>tabScanner</value>
  </data>
  <data name="&gt;&gt;tabScanner.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabScanner.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabScanner.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="tabControl1.ItemSize" type="System.Drawing.Size, System.Drawing">
    <value>45, 18</value>
  </data>
  <data name="tabControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 147</value>
  </data>
  <data name="tabControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>600, 240</value>
  </data>
  <data name="tabControl1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;tabControl1.Name" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabControl1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabControl, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tabControl1.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="checkCertsDisplay.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkCertsDisplay.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkCertsDisplay.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 70</value>
  </data>
  <data name="checkCertsDisplay.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 24</value>
  </data>
  <data name="checkCertsDisplay.TabIndex" type="System.Int32, mscorlib">
    <value>76</value>
  </data>
  <data name="checkCertsDisplay.Text" xml:space="preserve">
    <value>Enable display</value>
  </data>
  <data name="&gt;&gt;checkCertsDisplay.Name" xml:space="preserve">
    <value>checkCertsDisplay</value>
  </data>
  <data name="&gt;&gt;checkCertsDisplay.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkCertsDisplay.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;checkCertsDisplay.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="checkCertsCapture.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkCertsCapture.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkCertsCapture.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 94</value>
  </data>
  <data name="checkCertsCapture.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 24</value>
  </data>
  <data name="checkCertsCapture.TabIndex" type="System.Int32, mscorlib">
    <value>77</value>
  </data>
  <data name="checkCertsCapture.Text" xml:space="preserve">
    <value>Enable capture</value>
  </data>
  <data name="&gt;&gt;checkCertsCapture.Name" xml:space="preserve">
    <value>checkCertsCapture</value>
  </data>
  <data name="&gt;&gt;checkCertsCapture.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkCertsCapture.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;checkCertsCapture.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="tbScannerDeviceName.Location" type="System.Drawing.Point, System.Drawing">
    <value>25, 142</value>
  </data>
  <data name="tbScannerDeviceName.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 20</value>
  </data>
  <data name="tbScannerDeviceName.TabIndex" type="System.Int32, mscorlib">
    <value>75</value>
  </data>
  <data name="&gt;&gt;tbScannerDeviceName.Name" xml:space="preserve">
    <value>tbScannerDeviceName</value>
  </data>
  <data name="&gt;&gt;tbScannerDeviceName.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbScannerDeviceName.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;tbScannerDeviceName.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="buttonSelectDevice.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonSelectDevice.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonSelectDevice.Location" type="System.Drawing.Point, System.Drawing">
    <value>215, 139</value>
  </data>
  <data name="buttonSelectDevice.Size" type="System.Drawing.Size, System.Drawing">
    <value>59, 23</value>
  </data>
  <data name="buttonSelectDevice.TabIndex" type="System.Int32, mscorlib">
    <value>74</value>
  </data>
  <data name="buttonSelectDevice.Text" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="&gt;&gt;buttonSelectDevice.Name" xml:space="preserve">
    <value>buttonSelectDevice</value>
  </data>
  <data name="&gt;&gt;buttonSelectDevice.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonSelectDevice.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;buttonSelectDevice.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="checkCerts.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkCerts.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkCerts.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 40</value>
  </data>
  <data name="checkCerts.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 24</value>
  </data>
  <data name="checkCerts.TabIndex" type="System.Int32, mscorlib">
    <value>31</value>
  </data>
  <data name="checkCerts.Text" xml:space="preserve">
    <value>Enable Certificates</value>
  </data>
  <data name="&gt;&gt;checkCerts.Name" xml:space="preserve">
    <value>checkCerts</value>
  </data>
  <data name="&gt;&gt;checkCerts.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkCerts.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;checkCerts.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="labelCertsInstance.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelCertsInstance.Location" type="System.Drawing.Point, System.Drawing">
    <value>399, 103</value>
  </data>
  <data name="labelCertsInstance.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 16</value>
  </data>
  <data name="labelCertsInstance.TabIndex" type="System.Int32, mscorlib">
    <value>29</value>
  </data>
  <data name="labelCertsInstance.Text" xml:space="preserve">
    <value>Test Cert instance:</value>
  </data>
  <data name="labelCertsInstance.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;labelCertsInstance.Name" xml:space="preserve">
    <value>labelCertsInstance</value>
  </data>
  <data name="&gt;&gt;labelCertsInstance.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelCertsInstance.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;labelCertsInstance.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="numericUpDownCertsInstance.Location" type="System.Drawing.Point, System.Drawing">
    <value>519, 103</value>
  </data>
  <data name="numericUpDownCertsInstance.Size" type="System.Drawing.Size, System.Drawing">
    <value>50, 20</value>
  </data>
  <data name="numericUpDownCertsInstance.TabIndex" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="&gt;&gt;numericUpDownCertsInstance.Name" xml:space="preserve">
    <value>numericUpDownCertsInstance</value>
  </data>
  <data name="&gt;&gt;numericUpDownCertsInstance.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numericUpDownCertsInstance.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;numericUpDownCertsInstance.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="buttonTestCertsDisplay.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonTestCertsDisplay.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonTestCertsDisplay.Location" type="System.Drawing.Point, System.Drawing">
    <value>417, 177</value>
  </data>
  <data name="buttonTestCertsDisplay.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonTestCertsDisplay.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="buttonTestCertsDisplay.Text" xml:space="preserve">
    <value>Test Certificate &amp;Display</value>
  </data>
  <data name="&gt;&gt;buttonTestCertsDisplay.Name" xml:space="preserve">
    <value>buttonTestCertsDisplay</value>
  </data>
  <data name="&gt;&gt;buttonTestCertsDisplay.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonTestCertsDisplay.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;buttonTestCertsDisplay.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="textBoxTestCertsID.Location" type="System.Drawing.Point, System.Drawing">
    <value>489, 77</value>
  </data>
  <data name="textBoxTestCertsID.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 20</value>
  </data>
  <data name="textBoxTestCertsID.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="textBoxTestCertsID.Text" xml:space="preserve">
    <value>test</value>
  </data>
  <data name="&gt;&gt;textBoxTestCertsID.Name" xml:space="preserve">
    <value>textBoxTestCertsID</value>
  </data>
  <data name="&gt;&gt;textBoxTestCertsID.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxTestCertsID.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;textBoxTestCertsID.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="labelCertsID.Location" type="System.Drawing.Point, System.Drawing">
    <value>381, 77</value>
  </data>
  <data name="labelCertsID.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="labelCertsID.TabIndex" type="System.Int32, mscorlib">
    <value>33</value>
  </data>
  <data name="labelCertsID.Text" xml:space="preserve">
    <value>Test Cert ID:</value>
  </data>
  <data name="labelCertsID.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;labelCertsID.Name" xml:space="preserve">
    <value>labelCertsID</value>
  </data>
  <data name="&gt;&gt;labelCertsID.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelCertsID.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;labelCertsID.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="buttonTestCertsCapture.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonTestCertsCapture.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonTestCertsCapture.Location" type="System.Drawing.Point, System.Drawing">
    <value>417, 145</value>
  </data>
  <data name="buttonTestCertsCapture.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonTestCertsCapture.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="buttonTestCertsCapture.Text" xml:space="preserve">
    <value>&amp;Test Certificate Capture</value>
  </data>
  <data name="&gt;&gt;buttonTestCertsCapture.Name" xml:space="preserve">
    <value>buttonTestCertsCapture</value>
  </data>
  <data name="&gt;&gt;buttonTestCertsCapture.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonTestCertsCapture.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;buttonTestCertsCapture.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="buttonCertsFinisherProperties.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonCertsFinisherProperties.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonCertsFinisherProperties.Location" type="System.Drawing.Point, System.Drawing">
    <value>25, 177</value>
  </data>
  <data name="buttonCertsFinisherProperties.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonCertsFinisherProperties.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="buttonCertsFinisherProperties.Text" xml:space="preserve">
    <value>&amp;Finisher Properties</value>
  </data>
  <data name="&gt;&gt;buttonCertsFinisherProperties.Name" xml:space="preserve">
    <value>buttonCertsFinisherProperties</value>
  </data>
  <data name="&gt;&gt;buttonCertsFinisherProperties.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonCertsFinisherProperties.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;buttonCertsFinisherProperties.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="label20.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label20.Location" type="System.Drawing.Point, System.Drawing">
    <value>25, 121</value>
  </data>
  <data name="label20.Size" type="System.Drawing.Size, System.Drawing">
    <value>160, 16</value>
  </data>
  <data name="label20.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="label20.Text" xml:space="preserve">
    <value>Certificates Scanner Device</value>
  </data>
  <data name="&gt;&gt;label20.Name" xml:space="preserve">
    <value>label20</value>
  </data>
  <data name="&gt;&gt;label20.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label20.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;label20.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="label22.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label22.Location" type="System.Drawing.Point, System.Drawing">
    <value>25, 9</value>
  </data>
  <data name="label22.Size" type="System.Drawing.Size, System.Drawing">
    <value>296, 16</value>
  </data>
  <data name="label22.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="label22.Text" xml:space="preserve">
    <value>Certificates Path is DataRootDirectory + Certs</value>
  </data>
  <data name="&gt;&gt;label22.Name" xml:space="preserve">
    <value>label22</value>
  </data>
  <data name="&gt;&gt;label22.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label22.Parent" xml:space="preserve">
    <value>tabCerts</value>
  </data>
  <data name="&gt;&gt;label22.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="buttonReadFips.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonReadFips.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonReadFips.Location" type="System.Drawing.Point, System.Drawing">
    <value>448, 168</value>
  </data>
  <data name="buttonReadFips.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 32</value>
  </data>
  <data name="buttonReadFips.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="buttonReadFips.Text" xml:space="preserve">
    <value>&amp;Read FIPS portrait record</value>
  </data>
  <data name="buttonReadFips.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;buttonReadFips.Name" xml:space="preserve">
    <value>buttonReadFips</value>
  </data>
  <data name="&gt;&gt;buttonReadFips.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonReadFips.Parent" xml:space="preserve">
    <value>tabCustomBiometric</value>
  </data>
  <data name="&gt;&gt;buttonReadFips.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="buttonGenFips.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonGenFips.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonGenFips.Location" type="System.Drawing.Point, System.Drawing">
    <value>336, 168</value>
  </data>
  <data name="buttonGenFips.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 32</value>
  </data>
  <data name="buttonGenFips.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="buttonGenFips.Text" xml:space="preserve">
    <value>Gen &amp;FIPS portrait record</value>
  </data>
  <data name="buttonGenFips.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;buttonGenFips.Name" xml:space="preserve">
    <value>buttonGenFips</value>
  </data>
  <data name="&gt;&gt;buttonGenFips.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonGenFips.Parent" xml:space="preserve">
    <value>tabCustomBiometric</value>
  </data>
  <data name="&gt;&gt;buttonGenFips.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="buttonGenDocBiometrics.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonGenDocBiometrics.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonGenDocBiometrics.Location" type="System.Drawing.Point, System.Drawing">
    <value>448, 104</value>
  </data>
  <data name="buttonGenDocBiometrics.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 32</value>
  </data>
  <data name="buttonGenDocBiometrics.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="buttonGenDocBiometrics.Text" xml:space="preserve">
    <value>Show &amp;doc biometrics</value>
  </data>
  <data name="&gt;&gt;buttonGenDocBiometrics.Name" xml:space="preserve">
    <value>buttonGenDocBiometrics</value>
  </data>
  <data name="&gt;&gt;buttonGenDocBiometrics.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonGenDocBiometrics.Parent" xml:space="preserve">
    <value>tabCustomBiometric</value>
  </data>
  <data name="&gt;&gt;buttonGenDocBiometrics.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="checkLogBINErrors.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkLogBINErrors.Location" type="System.Drawing.Point, System.Drawing">
    <value>448, 24</value>
  </data>
  <data name="checkLogBINErrors.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 32</value>
  </data>
  <data name="checkLogBINErrors.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="checkLogBINErrors.Text" xml:space="preserve">
    <value>Log errors</value>
  </data>
  <data name="&gt;&gt;checkLogBINErrors.Name" xml:space="preserve">
    <value>checkLogBINErrors</value>
  </data>
  <data name="&gt;&gt;checkLogBINErrors.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkLogBINErrors.Parent" xml:space="preserve">
    <value>tabCustomBiometric</value>
  </data>
  <data name="&gt;&gt;checkLogBINErrors.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="buttonGenBiometric.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonGenBiometric.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonGenBiometric.Location" type="System.Drawing.Point, System.Drawing">
    <value>336, 64</value>
  </data>
  <data name="buttonGenBiometric.Size" type="System.Drawing.Size, System.Drawing">
    <value>208, 32</value>
  </data>
  <data name="buttonGenBiometric.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="buttonGenBiometric.Text" xml:space="preserve">
    <value>&amp;Gen biometrics</value>
  </data>
  <data name="&gt;&gt;buttonGenBiometric.Name" xml:space="preserve">
    <value>buttonGenBiometric</value>
  </data>
  <data name="&gt;&gt;buttonGenBiometric.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonGenBiometric.Parent" xml:space="preserve">
    <value>tabCustomBiometric</value>
  </data>
  <data name="&gt;&gt;buttonGenBiometric.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="buttonShowBiometrics.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonShowBiometrics.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonShowBiometrics.Location" type="System.Drawing.Point, System.Drawing">
    <value>336, 104</value>
  </data>
  <data name="buttonShowBiometrics.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 32</value>
  </data>
  <data name="buttonShowBiometrics.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="buttonShowBiometrics.Text" xml:space="preserve">
    <value>Show &amp;all biometrics</value>
  </data>
  <data name="&gt;&gt;buttonShowBiometrics.Name" xml:space="preserve">
    <value>buttonShowBiometrics</value>
  </data>
  <data name="&gt;&gt;buttonShowBiometrics.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonShowBiometrics.Parent" xml:space="preserve">
    <value>tabCustomBiometric</value>
  </data>
  <data name="&gt;&gt;buttonShowBiometrics.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="tbBiometricID.Location" type="System.Drawing.Point, System.Drawing">
    <value>336, 32</value>
  </data>
  <data name="tbBiometricID.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 20</value>
  </data>
  <data name="tbBiometricID.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="tbBiometricID.Text" xml:space="preserve">
    <value>test</value>
  </data>
  <data name="&gt;&gt;tbBiometricID.Name" xml:space="preserve">
    <value>tbBiometricID</value>
  </data>
  <data name="&gt;&gt;tbBiometricID.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbBiometricID.Parent" xml:space="preserve">
    <value>tabCustomBiometric</value>
  </data>
  <data name="&gt;&gt;tbBiometricID.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="label10.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label10.Location" type="System.Drawing.Point, System.Drawing">
    <value>336, 16</value>
  </data>
  <data name="label10.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 16</value>
  </data>
  <data name="label10.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="label10.Text" xml:space="preserve">
    <value>Test ID</value>
  </data>
  <data name="&gt;&gt;label10.Name" xml:space="preserve">
    <value>label10</value>
  </data>
  <data name="&gt;&gt;label10.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label10.Parent" xml:space="preserve">
    <value>tabCustomBiometric</value>
  </data>
  <data name="&gt;&gt;label10.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="&gt;&gt;buttonSearchBiometric.Name" xml:space="preserve">
    <value>buttonSearchBiometric</value>
  </data>
  <data name="&gt;&gt;buttonSearchBiometric.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonSearchBiometric.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;buttonSearchBiometric.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;label16.Name" xml:space="preserve">
    <value>label16</value>
  </data>
  <data name="&gt;&gt;label16.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label16.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label16.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;label15.Name" xml:space="preserve">
    <value>label15</value>
  </data>
  <data name="&gt;&gt;label15.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label15.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label15.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;numericUpDownMinutiaThreshold.Name" xml:space="preserve">
    <value>numericUpDownMinutiaThreshold</value>
  </data>
  <data name="&gt;&gt;numericUpDownMinutiaThreshold.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numericUpDownMinutiaThreshold.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;numericUpDownMinutiaThreshold.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;label12.Name" xml:space="preserve">
    <value>label12</value>
  </data>
  <data name="&gt;&gt;label12.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label12.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label12.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;numericUpDownBIN2Threshold.Name" xml:space="preserve">
    <value>numericUpDownBIN2Threshold</value>
  </data>
  <data name="&gt;&gt;numericUpDownBIN2Threshold.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numericUpDownBIN2Threshold.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;numericUpDownBIN2Threshold.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;cbVerifyHow.Name" xml:space="preserve">
    <value>cbVerifyHow</value>
  </data>
  <data name="&gt;&gt;cbVerifyHow.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbVerifyHow.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;cbVerifyHow.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="&gt;&gt;buttonVerifyBiometric.Name" xml:space="preserve">
    <value>buttonVerifyBiometric</value>
  </data>
  <data name="&gt;&gt;buttonVerifyBiometric.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonVerifyBiometric.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;buttonVerifyBiometric.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="&gt;&gt;listBoxFingerBioTypes.Name" xml:space="preserve">
    <value>listBoxFingerBioTypes</value>
  </data>
  <data name="&gt;&gt;listBoxFingerBioTypes.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;listBoxFingerBioTypes.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;listBoxFingerBioTypes.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="groupBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 8</value>
  </data>
  <data name="groupBox2.Size" type="System.Drawing.Size, System.Drawing">
    <value>296, 192</value>
  </data>
  <data name="groupBox2.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="groupBox2.Text" xml:space="preserve">
    <value>Fingerprint biometric</value>
  </data>
  <data name="&gt;&gt;groupBox2.Name" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;groupBox2.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox2.Parent" xml:space="preserve">
    <value>tabCustomBiometric</value>
  </data>
  <data name="&gt;&gt;groupBox2.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="buttonSearchBiometric.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonSearchBiometric.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonSearchBiometric.Location" type="System.Drawing.Point, System.Drawing">
    <value>157, 128</value>
  </data>
  <data name="buttonSearchBiometric.Size" type="System.Drawing.Size, System.Drawing">
    <value>125, 24</value>
  </data>
  <data name="buttonSearchBiometric.TabIndex" type="System.Int32, mscorlib">
    <value>29</value>
  </data>
  <data name="buttonSearchBiometric.Text" xml:space="preserve">
    <value>Test &amp;Search</value>
  </data>
  <data name="&gt;&gt;buttonSearchBiometric.Name" xml:space="preserve">
    <value>buttonSearchBiometric</value>
  </data>
  <data name="&gt;&gt;buttonSearchBiometric.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonSearchBiometric.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;buttonSearchBiometric.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label16.Location" type="System.Drawing.Point, System.Drawing">
    <value>146, 16</value>
  </data>
  <data name="label16.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 16</value>
  </data>
  <data name="label16.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="label16.Text" xml:space="preserve">
    <value>Matching thresholds</value>
  </data>
  <data name="&gt;&gt;label16.Name" xml:space="preserve">
    <value>label16</value>
  </data>
  <data name="&gt;&gt;label16.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label16.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label16.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label15.Location" type="System.Drawing.Point, System.Drawing">
    <value>146, 41</value>
  </data>
  <data name="label15.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 16</value>
  </data>
  <data name="label15.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label15.Text" xml:space="preserve">
    <value>SDS Native</value>
  </data>
  <data name="label15.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;label15.Name" xml:space="preserve">
    <value>label15</value>
  </data>
  <data name="&gt;&gt;label15.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label15.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label15.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="numericUpDownMinutiaThreshold.Location" type="System.Drawing.Point, System.Drawing">
    <value>224, 41</value>
  </data>
  <data name="numericUpDownMinutiaThreshold.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="numericUpDownMinutiaThreshold.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;numericUpDownMinutiaThreshold.Name" xml:space="preserve">
    <value>numericUpDownMinutiaThreshold</value>
  </data>
  <data name="&gt;&gt;numericUpDownMinutiaThreshold.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numericUpDownMinutiaThreshold.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;numericUpDownMinutiaThreshold.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label12.Location" type="System.Drawing.Point, System.Drawing">
    <value>146, 67</value>
  </data>
  <data name="label12.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 16</value>
  </data>
  <data name="label12.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="label12.Text" xml:space="preserve">
    <value>Liska BIN2</value>
  </data>
  <data name="label12.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;label12.Name" xml:space="preserve">
    <value>label12</value>
  </data>
  <data name="&gt;&gt;label12.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label12.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label12.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="numericUpDownBIN2Threshold.Location" type="System.Drawing.Point, System.Drawing">
    <value>224, 67</value>
  </data>
  <data name="numericUpDownBIN2Threshold.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="numericUpDownBIN2Threshold.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="numericUpDownBIN2Threshold.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;numericUpDownBIN2Threshold.Name" xml:space="preserve">
    <value>numericUpDownBIN2Threshold</value>
  </data>
  <data name="&gt;&gt;numericUpDownBIN2Threshold.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numericUpDownBIN2Threshold.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;numericUpDownBIN2Threshold.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="cbVerifyHow.Items" xml:space="preserve">
    <value>Verify versus Test ID</value>
  </data>
  <data name="cbVerifyHow.Items1" xml:space="preserve">
    <value>Verify versus Barcode</value>
  </data>
  <data name="cbVerifyHow.Items2" xml:space="preserve">
    <value>Verify versus Mag Stripe</value>
  </data>
  <data name="cbVerifyHow.Items3" xml:space="preserve">
    <value>Verify versus Smart Chip</value>
  </data>
  <data name="cbVerifyHow.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 160</value>
  </data>
  <data name="cbVerifyHow.Size" type="System.Drawing.Size, System.Drawing">
    <value>125, 21</value>
  </data>
  <data name="cbVerifyHow.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="cbVerifyHow.Text" xml:space="preserve">
    <value>Verify versus Test ID</value>
  </data>
  <data name="&gt;&gt;cbVerifyHow.Name" xml:space="preserve">
    <value>cbVerifyHow</value>
  </data>
  <data name="&gt;&gt;cbVerifyHow.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbVerifyHow.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;cbVerifyHow.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="buttonVerifyBiometric.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonVerifyBiometric.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonVerifyBiometric.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 128</value>
  </data>
  <data name="buttonVerifyBiometric.Size" type="System.Drawing.Size, System.Drawing">
    <value>125, 24</value>
  </data>
  <data name="buttonVerifyBiometric.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="buttonVerifyBiometric.Text" xml:space="preserve">
    <value>Test &amp;Verify</value>
  </data>
  <data name="&gt;&gt;buttonVerifyBiometric.Name" xml:space="preserve">
    <value>buttonVerifyBiometric</value>
  </data>
  <data name="&gt;&gt;buttonVerifyBiometric.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonVerifyBiometric.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;buttonVerifyBiometric.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="listBoxFingerBioTypes.Items" xml:space="preserve">
    <value>SDS Native</value>
  </data>
  <data name="listBoxFingerBioTypes.Items1" xml:space="preserve">
    <value>Innovatrics</value>
  </data>
  <data name="listBoxFingerBioTypes.Items2" xml:space="preserve">
    <value>Agora</value>
  </data>
  <data name="listBoxFingerBioTypes.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 24</value>
  </data>
  <data name="listBoxFingerBioTypes.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 69</value>
  </data>
  <data name="listBoxFingerBioTypes.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;listBoxFingerBioTypes.Name" xml:space="preserve">
    <value>listBoxFingerBioTypes</value>
  </data>
  <data name="&gt;&gt;listBoxFingerBioTypes.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;listBoxFingerBioTypes.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;listBoxFingerBioTypes.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="comboBoxChipEncoderPort.Items" xml:space="preserve">
    <value>COM1</value>
  </data>
  <data name="comboBoxChipEncoderPort.Items1" xml:space="preserve">
    <value>COM2</value>
  </data>
  <data name="comboBoxChipEncoderPort.Items2" xml:space="preserve">
    <value>COM3</value>
  </data>
  <data name="comboBoxChipEncoderPort.Items3" xml:space="preserve">
    <value>COM4</value>
  </data>
  <data name="comboBoxChipEncoderPort.Items4" xml:space="preserve">
    <value>COM5</value>
  </data>
  <data name="comboBoxChipEncoderPort.Items5" xml:space="preserve">
    <value>COM6</value>
  </data>
  <data name="comboBoxChipEncoderPort.Items6" xml:space="preserve">
    <value>COM7</value>
  </data>
  <data name="comboBoxChipEncoderPort.Items7" xml:space="preserve">
    <value>COM8</value>
  </data>
  <data name="comboBoxChipEncoderPort.Items8" xml:space="preserve">
    <value>COM9</value>
  </data>
  <data name="comboBoxChipEncoderPort.Items9" xml:space="preserve">
    <value>COM10</value>
  </data>
  <data name="comboBoxChipEncoderPort.Items10" xml:space="preserve">
    <value>COM11</value>
  </data>
  <data name="comboBoxChipEncoderPort.Items11" xml:space="preserve">
    <value>COM12</value>
  </data>
  <data name="comboBoxChipEncoderPort.Items12" xml:space="preserve">
    <value>COM13</value>
  </data>
  <data name="comboBoxChipEncoderPort.Items13" xml:space="preserve">
    <value>COM14</value>
  </data>
  <data name="comboBoxChipEncoderPort.Items14" xml:space="preserve">
    <value>COM15</value>
  </data>
  <data name="comboBoxChipEncoderPort.Location" type="System.Drawing.Point, System.Drawing">
    <value>223, 64</value>
  </data>
  <data name="comboBoxChipEncoderPort.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 21</value>
  </data>
  <data name="comboBoxChipEncoderPort.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="comboBoxChipEncoderPort.Text" xml:space="preserve">
    <value>COM4</value>
  </data>
  <data name="&gt;&gt;comboBoxChipEncoderPort.Name" xml:space="preserve">
    <value>comboBoxChipEncoderPort</value>
  </data>
  <data name="&gt;&gt;comboBoxChipEncoderPort.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxChipEncoderPort.Parent" xml:space="preserve">
    <value>tabReaders</value>
  </data>
  <data name="&gt;&gt;comboBoxChipEncoderPort.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label23.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label23.Location" type="System.Drawing.Point, System.Drawing">
    <value>223, 40</value>
  </data>
  <data name="label23.Size" type="System.Drawing.Size, System.Drawing">
    <value>333, 24</value>
  </data>
  <data name="label23.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="label23.Text" xml:space="preserve">
    <value>Stand-alone Chip Encoder/Scanner Port</value>
  </data>
  <data name="&gt;&gt;label23.Name" xml:space="preserve">
    <value>label23</value>
  </data>
  <data name="&gt;&gt;label23.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label23.Parent" xml:space="preserve">
    <value>tabReaders</value>
  </data>
  <data name="&gt;&gt;label23.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="comboBoxBarcodeScannerPort.Items" xml:space="preserve">
    <value>COM1</value>
  </data>
  <data name="comboBoxBarcodeScannerPort.Items1" xml:space="preserve">
    <value>COM2</value>
  </data>
  <data name="comboBoxBarcodeScannerPort.Items2" xml:space="preserve">
    <value>COM3</value>
  </data>
  <data name="comboBoxBarcodeScannerPort.Items3" xml:space="preserve">
    <value>COM4</value>
  </data>
  <data name="comboBoxBarcodeScannerPort.Items4" xml:space="preserve">
    <value>COM5</value>
  </data>
  <data name="comboBoxBarcodeScannerPort.Items5" xml:space="preserve">
    <value>COM6</value>
  </data>
  <data name="comboBoxBarcodeScannerPort.Items6" xml:space="preserve">
    <value>COM7</value>
  </data>
  <data name="comboBoxBarcodeScannerPort.Items7" xml:space="preserve">
    <value>COM8</value>
  </data>
  <data name="comboBoxBarcodeScannerPort.Items8" xml:space="preserve">
    <value>COM9</value>
  </data>
  <data name="comboBoxBarcodeScannerPort.Items9" xml:space="preserve">
    <value>COM10</value>
  </data>
  <data name="comboBoxBarcodeScannerPort.Items10" xml:space="preserve">
    <value>COM11</value>
  </data>
  <data name="comboBoxBarcodeScannerPort.Items11" xml:space="preserve">
    <value>COM12</value>
  </data>
  <data name="comboBoxBarcodeScannerPort.Items12" xml:space="preserve">
    <value>COM13</value>
  </data>
  <data name="comboBoxBarcodeScannerPort.Items13" xml:space="preserve">
    <value>COM14</value>
  </data>
  <data name="comboBoxBarcodeScannerPort.Items14" xml:space="preserve">
    <value>COM15</value>
  </data>
  <data name="comboBoxBarcodeScannerPort.Location" type="System.Drawing.Point, System.Drawing">
    <value>48, 64</value>
  </data>
  <data name="comboBoxBarcodeScannerPort.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 21</value>
  </data>
  <data name="comboBoxBarcodeScannerPort.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="comboBoxBarcodeScannerPort.Text" xml:space="preserve">
    <value>COM3</value>
  </data>
  <data name="&gt;&gt;comboBoxBarcodeScannerPort.Name" xml:space="preserve">
    <value>comboBoxBarcodeScannerPort</value>
  </data>
  <data name="&gt;&gt;comboBoxBarcodeScannerPort.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxBarcodeScannerPort.Parent" xml:space="preserve">
    <value>tabReaders</value>
  </data>
  <data name="&gt;&gt;comboBoxBarcodeScannerPort.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="label17.Location" type="System.Drawing.Point, System.Drawing">
    <value>48, 40</value>
  </data>
  <data name="label17.Size" type="System.Drawing.Size, System.Drawing">
    <value>169, 24</value>
  </data>
  <data name="label17.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="label17.Text" xml:space="preserve">
    <value>Barcode Scanner Port</value>
  </data>
  <data name="&gt;&gt;label17.Name" xml:space="preserve">
    <value>label17</value>
  </data>
  <data name="&gt;&gt;label17.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label17.Parent" xml:space="preserve">
    <value>tabReaders</value>
  </data>
  <data name="&gt;&gt;label17.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label11.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 16</value>
  </data>
  <data name="label11.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 40</value>
  </data>
  <data name="label11.TabIndex" type="System.Int32, mscorlib">
    <value>76</value>
  </data>
  <data name="label11.Text" xml:space="preserve">
    <value>Document Scanner device:</value>
  </data>
  <data name="&gt;&gt;label11.Name" xml:space="preserve">
    <value>label11</value>
  </data>
  <data name="&gt;&gt;label11.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label11.Parent" xml:space="preserve">
    <value>tabScanner</value>
  </data>
  <data name="&gt;&gt;label11.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="textBoxScannerName.Location" type="System.Drawing.Point, System.Drawing">
    <value>120, 16</value>
  </data>
  <data name="textBoxScannerName.Size" type="System.Drawing.Size, System.Drawing">
    <value>360, 20</value>
  </data>
  <data name="textBoxScannerName.TabIndex" type="System.Int32, mscorlib">
    <value>75</value>
  </data>
  <data name="&gt;&gt;textBoxScannerName.Name" xml:space="preserve">
    <value>textBoxScannerName</value>
  </data>
  <data name="&gt;&gt;textBoxScannerName.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxScannerName.Parent" xml:space="preserve">
    <value>tabScanner</value>
  </data>
  <data name="&gt;&gt;textBoxScannerName.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;labelNumFingerprints.Name" xml:space="preserve">
    <value>labelNumFingerprints</value>
  </data>
  <data name="&gt;&gt;labelNumFingerprints.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelNumFingerprints.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;labelNumFingerprints.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;label13.Name" xml:space="preserve">
    <value>label13</value>
  </data>
  <data name="&gt;&gt;label13.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label13.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label13.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;labelNumSignatures.Name" xml:space="preserve">
    <value>labelNumSignatures</value>
  </data>
  <data name="&gt;&gt;labelNumSignatures.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelNumSignatures.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;labelNumSignatures.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;label14.Name" xml:space="preserve">
    <value>label14</value>
  </data>
  <data name="&gt;&gt;label14.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label14.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label14.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;labelNumPortraits.Name" xml:space="preserve">
    <value>labelNumPortraits</value>
  </data>
  <data name="&gt;&gt;labelNumPortraits.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelNumPortraits.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;labelNumPortraits.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;label8.Name" xml:space="preserve">
    <value>label8</value>
  </data>
  <data name="&gt;&gt;label8.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label8.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label8.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="groupBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 56</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>176, 104</value>
  </data>
  <data name="groupBox1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="groupBox1.Text" xml:space="preserve">
    <value>Scanned image classes</value>
  </data>
  <data name="&gt;&gt;groupBox1.Name" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;groupBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox1.Parent" xml:space="preserve">
    <value>tabScanner</value>
  </data>
  <data name="&gt;&gt;groupBox1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="labelNumFingerprints.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 80</value>
  </data>
  <data name="labelNumFingerprints.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 16</value>
  </data>
  <data name="labelNumFingerprints.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="labelNumFingerprints.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelNumFingerprints.Name" xml:space="preserve">
    <value>labelNumFingerprints</value>
  </data>
  <data name="&gt;&gt;labelNumFingerprints.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelNumFingerprints.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;labelNumFingerprints.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label13.Location" type="System.Drawing.Point, System.Drawing">
    <value>56, 80</value>
  </data>
  <data name="label13.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 16</value>
  </data>
  <data name="label13.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="label13.Text" xml:space="preserve">
    <value>fingerprints</value>
  </data>
  <data name="&gt;&gt;label13.Name" xml:space="preserve">
    <value>label13</value>
  </data>
  <data name="&gt;&gt;label13.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label13.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label13.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelNumSignatures.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 56</value>
  </data>
  <data name="labelNumSignatures.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 16</value>
  </data>
  <data name="labelNumSignatures.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="labelNumSignatures.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelNumSignatures.Name" xml:space="preserve">
    <value>labelNumSignatures</value>
  </data>
  <data name="&gt;&gt;labelNumSignatures.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelNumSignatures.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;labelNumSignatures.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="label14.Location" type="System.Drawing.Point, System.Drawing">
    <value>56, 56</value>
  </data>
  <data name="label14.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 16</value>
  </data>
  <data name="label14.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="label14.Text" xml:space="preserve">
    <value>signatures</value>
  </data>
  <data name="&gt;&gt;label14.Name" xml:space="preserve">
    <value>label14</value>
  </data>
  <data name="&gt;&gt;label14.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label14.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label14.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="labelNumPortraits.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 32</value>
  </data>
  <data name="labelNumPortraits.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 16</value>
  </data>
  <data name="labelNumPortraits.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelNumPortraits.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelNumPortraits.Name" xml:space="preserve">
    <value>labelNumPortraits</value>
  </data>
  <data name="&gt;&gt;labelNumPortraits.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelNumPortraits.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;labelNumPortraits.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="label8.Location" type="System.Drawing.Point, System.Drawing">
    <value>56, 32</value>
  </data>
  <data name="label8.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 16</value>
  </data>
  <data name="label8.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="label8.Text" xml:space="preserve">
    <value>portraits</value>
  </data>
  <data name="&gt;&gt;label8.Name" xml:space="preserve">
    <value>label8</value>
  </data>
  <data name="&gt;&gt;label8.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label8.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label8.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="buttonScannerDisplay.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonScannerDisplay.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonScannerDisplay.Location" type="System.Drawing.Point, System.Drawing">
    <value>240, 176</value>
  </data>
  <data name="buttonScannerDisplay.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonScannerDisplay.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="buttonScannerDisplay.Text" xml:space="preserve">
    <value>Test Scanner &amp;Display</value>
  </data>
  <data name="&gt;&gt;buttonScannerDisplay.Name" xml:space="preserve">
    <value>buttonScannerDisplay</value>
  </data>
  <data name="&gt;&gt;buttonScannerDisplay.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonScannerDisplay.Parent" xml:space="preserve">
    <value>tabScanner</value>
  </data>
  <data name="&gt;&gt;buttonScannerDisplay.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="textBoxScannerImageID.Location" type="System.Drawing.Point, System.Drawing">
    <value>240, 88</value>
  </data>
  <data name="textBoxScannerImageID.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 20</value>
  </data>
  <data name="textBoxScannerImageID.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="textBoxScannerImageID.Text" xml:space="preserve">
    <value>test</value>
  </data>
  <data name="&gt;&gt;textBoxScannerImageID.Name" xml:space="preserve">
    <value>textBoxScannerImageID</value>
  </data>
  <data name="&gt;&gt;textBoxScannerImageID.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxScannerImageID.Parent" xml:space="preserve">
    <value>tabScanner</value>
  </data>
  <data name="&gt;&gt;textBoxScannerImageID.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="label6.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label6.Location" type="System.Drawing.Point, System.Drawing">
    <value>240, 64</value>
  </data>
  <data name="label6.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 16</value>
  </data>
  <data name="label6.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>Test Image ID</value>
  </data>
  <data name="&gt;&gt;label6.Name" xml:space="preserve">
    <value>label6</value>
  </data>
  <data name="&gt;&gt;label6.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label6.Parent" xml:space="preserve">
    <value>tabScanner</value>
  </data>
  <data name="&gt;&gt;label6.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="buttonScannerCapture.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonScannerCapture.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonScannerCapture.Location" type="System.Drawing.Point, System.Drawing">
    <value>240, 136</value>
  </data>
  <data name="buttonScannerCapture.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 24</value>
  </data>
  <data name="buttonScannerCapture.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="buttonScannerCapture.Text" xml:space="preserve">
    <value>&amp;Test Scanner Capture</value>
  </data>
  <data name="&gt;&gt;buttonScannerCapture.Name" xml:space="preserve">
    <value>buttonScannerCapture</value>
  </data>
  <data name="&gt;&gt;buttonScannerCapture.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonScannerCapture.Parent" xml:space="preserve">
    <value>tabScanner</value>
  </data>
  <data name="&gt;&gt;buttonScannerCapture.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="buttonScannerProperties.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonScannerProperties.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonScannerProperties.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 176</value>
  </data>
  <data name="buttonScannerProperties.Size" type="System.Drawing.Size, System.Drawing">
    <value>176, 24</value>
  </data>
  <data name="buttonScannerProperties.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="buttonScannerProperties.Text" xml:space="preserve">
    <value>Document Scanner &amp;Properties</value>
  </data>
  <data name="&gt;&gt;buttonScannerProperties.Name" xml:space="preserve">
    <value>buttonScannerProperties</value>
  </data>
  <data name="&gt;&gt;buttonScannerProperties.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonScannerProperties.Parent" xml:space="preserve">
    <value>tabScanner</value>
  </data>
  <data name="&gt;&gt;buttonScannerProperties.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="tbMachineConfigFile.Location" type="System.Drawing.Point, System.Drawing">
    <value>312, 41</value>
  </data>
  <data name="tbMachineConfigFile.Size" type="System.Drawing.Size, System.Drawing">
    <value>304, 13</value>
  </data>
  <data name="tbMachineConfigFile.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="tbMachineConfigFile.Text" xml:space="preserve">
    <value>config file</value>
  </data>
  <data name="&gt;&gt;tbMachineConfigFile.Name" xml:space="preserve">
    <value>tbMachineConfigFile</value>
  </data>
  <data name="&gt;&gt;tbMachineConfigFile.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbMachineConfigFile.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbMachineConfigFile.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="labelConfigFile.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelConfigFile.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 41</value>
  </data>
  <data name="labelConfigFile.Size" type="System.Drawing.Size, System.Drawing">
    <value>130, 13</value>
  </data>
  <data name="labelConfigFile.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="labelConfigFile.Text" xml:space="preserve">
    <value>Machine Config File:</value>
  </data>
  <data name="labelConfigFile.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;labelConfigFile.Name" xml:space="preserve">
    <value>labelConfigFile</value>
  </data>
  <data name="&gt;&gt;labelConfigFile.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelConfigFile.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelConfigFile.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="tbDataRootDir.Location" type="System.Drawing.Point, System.Drawing">
    <value>312, 70</value>
  </data>
  <data name="tbDataRootDir.Size" type="System.Drawing.Size, System.Drawing">
    <value>277, 20</value>
  </data>
  <data name="tbDataRootDir.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;tbDataRootDir.Name" xml:space="preserve">
    <value>tbDataRootDir</value>
  </data>
  <data name="&gt;&gt;tbDataRootDir.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbDataRootDir.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbDataRootDir.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="labelDataRootDirectory.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelDataRootDirectory.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 70</value>
  </data>
  <data name="labelDataRootDirectory.Size" type="System.Drawing.Size, System.Drawing">
    <value>130, 13</value>
  </data>
  <data name="labelDataRootDirectory.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="labelDataRootDirectory.Text" xml:space="preserve">
    <value>Data Root Directory:</value>
  </data>
  <data name="labelDataRootDirectory.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;labelDataRootDirectory.Name" xml:space="preserve">
    <value>labelDataRootDirectory</value>
  </data>
  <data name="&gt;&gt;labelDataRootDirectory.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelDataRootDirectory.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelDataRootDirectory.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="buttonAbout.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonAbout.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonAbout.Location" type="System.Drawing.Point, System.Drawing">
    <value>208, 408</value>
  </data>
  <data name="buttonAbout.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 24</value>
  </data>
  <data name="buttonAbout.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="buttonAbout.Text" xml:space="preserve">
    <value>&amp;About</value>
  </data>
  <data name="&gt;&gt;buttonAbout.Name" xml:space="preserve">
    <value>buttonAbout</value>
  </data>
  <data name="&gt;&gt;buttonAbout.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAbout.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAbout.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="buttonCancel.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>520, 408</value>
  </data>
  <data name="buttonCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 24</value>
  </data>
  <data name="buttonCancel.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="buttonCancel.Text" xml:space="preserve">
    <value>&amp;Cancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Name" xml:space="preserve">
    <value>buttonCancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonCancel.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="buttonAccept.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonAccept.Location" type="System.Drawing.Point, System.Drawing">
    <value>416, 408</value>
  </data>
  <data name="buttonAccept.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 24</value>
  </data>
  <data name="buttonAccept.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="buttonAccept.Text" xml:space="preserve">
    <value>&amp;OK</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Name" xml:space="preserve">
    <value>buttonAccept</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAccept.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="buttonValidate.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonValidate.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonValidate.Location" type="System.Drawing.Point, System.Drawing">
    <value>312, 408</value>
  </data>
  <data name="buttonValidate.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 24</value>
  </data>
  <data name="buttonValidate.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="buttonValidate.Text" xml:space="preserve">
    <value>C&amp;heck</value>
  </data>
  <data name="&gt;&gt;buttonValidate.Name" xml:space="preserve">
    <value>buttonValidate</value>
  </data>
  <data name="&gt;&gt;buttonValidate.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonValidate.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonValidate.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="comboBoxCaptureOrder.Items" xml:space="preserve">
    <value>Photo/Signature/Fingers</value>
  </data>
  <data name="comboBoxCaptureOrder.Items1" xml:space="preserve">
    <value>Signature/Fingers/Photo</value>
  </data>
  <data name="comboBoxCaptureOrder.Items2" xml:space="preserve">
    <value>Fingers/Signature/Photo</value>
  </data>
  <data name="comboBoxCaptureOrder.Location" type="System.Drawing.Point, System.Drawing">
    <value>472, 120</value>
  </data>
  <data name="comboBoxCaptureOrder.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 21</value>
  </data>
  <data name="comboBoxCaptureOrder.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="comboBoxCaptureOrder.Text" xml:space="preserve">
    <value>Photo/Signature/Fingers</value>
  </data>
  <data name="&gt;&gt;comboBoxCaptureOrder.Name" xml:space="preserve">
    <value>comboBoxCaptureOrder</value>
  </data>
  <data name="&gt;&gt;comboBoxCaptureOrder.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxCaptureOrder.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;comboBoxCaptureOrder.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="labelCaptureOrder.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelCaptureOrder.Location" type="System.Drawing.Point, System.Drawing">
    <value>376, 120</value>
  </data>
  <data name="labelCaptureOrder.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 13</value>
  </data>
  <data name="labelCaptureOrder.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="labelCaptureOrder.Text" xml:space="preserve">
    <value>Capture order:</value>
  </data>
  <data name="labelCaptureOrder.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>TopRight</value>
  </data>
  <data name="&gt;&gt;labelCaptureOrder.Name" xml:space="preserve">
    <value>labelCaptureOrder</value>
  </data>
  <data name="&gt;&gt;labelCaptureOrder.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelCaptureOrder.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelCaptureOrder.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="buttonSelectDataRootDir.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonSelectDataRootDir.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 8.25pt, style=Bold</value>
  </data>
  <data name="buttonSelectDataRootDir.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonSelectDataRootDir.Location" type="System.Drawing.Point, System.Drawing">
    <value>597, 70</value>
  </data>
  <data name="buttonSelectDataRootDir.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 20</value>
  </data>
  <data name="buttonSelectDataRootDir.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="buttonSelectDataRootDir.Text" xml:space="preserve">
    <value>&gt;</value>
  </data>
  <data name="&gt;&gt;buttonSelectDataRootDir.Name" xml:space="preserve">
    <value>buttonSelectDataRootDir</value>
  </data>
  <data name="&gt;&gt;buttonSelectDataRootDir.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonSelectDataRootDir.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonSelectDataRootDir.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <metadata name="toolTip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="label18.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 9.75pt, style=Bold</value>
  </data>
  <data name="label18.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label18.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 29</value>
  </data>
  <data name="label18.Size" type="System.Drawing.Size, System.Drawing">
    <value>173, 54</value>
  </data>
  <data name="label18.TabIndex" type="System.Int32, mscorlib">
    <value>49</value>
  </data>
  <data name="label18.Text" xml:space="preserve">
    <value>Secure Documents Solution</value>
  </data>
  <data name="label18.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>TopCenter</value>
  </data>
  <data name="&gt;&gt;label18.Name" xml:space="preserve">
    <value>label18</value>
  </data>
  <data name="&gt;&gt;label18.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label18.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label18.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="label19.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 9.75pt, style=Bold</value>
  </data>
  <data name="label19.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label19.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 8</value>
  </data>
  <data name="label19.Size" type="System.Drawing.Size, System.Drawing">
    <value>173, 21</value>
  </data>
  <data name="label19.TabIndex" type="System.Int32, mscorlib">
    <value>48</value>
  </data>
  <data name="label19.Text" xml:space="preserve">
    <value>ID Services</value>
  </data>
  <data name="label19.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>TopCenter</value>
  </data>
  <data name="&gt;&gt;label19.Name" xml:space="preserve">
    <value>label19</value>
  </data>
  <data name="&gt;&gt;label19.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label19.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label19.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="buttonOLEImageDB.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonOLEImageDB.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 8.25pt, style=Bold</value>
  </data>
  <data name="buttonOLEImageDB.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonOLEImageDB.Location" type="System.Drawing.Point, System.Drawing">
    <value>597, 93</value>
  </data>
  <data name="buttonOLEImageDB.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 20</value>
  </data>
  <data name="buttonOLEImageDB.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="buttonOLEImageDB.Text" xml:space="preserve">
    <value>&gt;</value>
  </data>
  <data name="&gt;&gt;buttonOLEImageDB.Name" xml:space="preserve">
    <value>buttonOLEImageDB</value>
  </data>
  <data name="&gt;&gt;buttonOLEImageDB.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonOLEImageDB.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonOLEImageDB.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label24.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label24.Location" type="System.Drawing.Point, System.Drawing">
    <value>348, 98</value>
  </data>
  <data name="label24.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 13</value>
  </data>
  <data name="label24.TabIndex" type="System.Int32, mscorlib">
    <value>51</value>
  </data>
  <data name="label24.Text" xml:space="preserve">
    <value>Image Database Storage Type</value>
  </data>
  <data name="label24.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>TopRight</value>
  </data>
  <data name="&gt;&gt;label24.Name" xml:space="preserve">
    <value>label24</value>
  </data>
  <data name="&gt;&gt;label24.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label24.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label24.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="textBoxImageDBType.Location" type="System.Drawing.Point, System.Drawing">
    <value>523, 94</value>
  </data>
  <data name="textBoxImageDBType.Size" type="System.Drawing.Size, System.Drawing">
    <value>66, 20</value>
  </data>
  <data name="textBoxImageDBType.TabIndex" type="System.Int32, mscorlib">
    <value>52</value>
  </data>
  <data name="&gt;&gt;textBoxImageDBType.Name" xml:space="preserve">
    <value>textBoxImageDBType</value>
  </data>
  <data name="&gt;&gt;textBoxImageDBType.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxImageDBType.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBoxImageDBType.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="checkBoxEnableImageHistory.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBoxEnableImageHistory.Location" type="System.Drawing.Point, System.Drawing">
    <value>200, 120</value>
  </data>
  <data name="checkBoxEnableImageHistory.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="checkBoxEnableImageHistory.Size" type="System.Drawing.Size, System.Drawing">
    <value>126, 17</value>
  </data>
  <data name="checkBoxEnableImageHistory.TabIndex" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="checkBoxEnableImageHistory.Text" xml:space="preserve">
    <value>Enable Image History</value>
  </data>
  <data name="&gt;&gt;checkBoxEnableImageHistory.Name" xml:space="preserve">
    <value>checkBoxEnableImageHistory</value>
  </data>
  <data name="&gt;&gt;checkBoxEnableImageHistory.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBoxEnableImageHistory.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;checkBoxEnableImageHistory.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>40</value>
  </metadata>
  <data name="$this.AutoScaleBaseSize" type="System.Drawing.Size, System.Drawing">
    <value>5, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>634, 448</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAIAICAQAAAAAADoAgAAJgAAABAQEAAAAAAAKAEAAA4DAAAoAAAAIAAAAEAAAAABAAQAAAAAAAAC
        AAAAAAAAAAAAABAAAAAQAAAAAAAAAAAAgAAAgAAAAICAAIAAAACAAIAAgIAAAICAgADAwMAAAAD/AAD/
        AAAA//8A/wAAAP8A/wD//wAA////AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAO4A7u4A
        AO4AAN3dAAAAAADuAO7uAADuAAC73QAAAAAAAO7uALu7AAC7vd0A3d0AAADu7gC7uwAAu73dAN3dAAAA
        AAC7u7sAu7sAAAAAAAAAAAAAu7u7ALu7AAAAAAAAAAAAu7sAAAAAv/////8AAAAAALu7AAAAAP//////
        AAAAAAAAu7u7u7sA////AAAAAAAAALu7u7u7AP///wAAAAAAAAC7u7u7AAD/////AAAAAAAAu7u7uwAA
        /////wAAAAAAALu7u7u7AP//////AAAAAAC7u7u7uwD//////wAAAAC7u7u7u7u7AP8A/wAAAAAAu7u7
        u7u7uwD/AP8AAAAAAAC7u7sAuwD//wAAAAAAAAAAu7u7ALsA//8AAAAAAAAAAAC7uwAAuwD///8AAAAA
        AAAAu7sAALsA////AAAAAAAAALu7uwC7uwAAALsAAAAAAAC7u7sAu7sAAAC7AAAAAAAAAAAAALu7u7u7
        AAAAAAAAAAAAAAC7u7u7uwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA////////
        //8AAAADAAAAA8AAAAPAAAADwAAAA8AAAAPwAAAD8AAAA8AAAAPAAAADwAAAA8AAAAMAAAAAAAAAAAAA
        AAMAAAADAAAAAwAAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAPAAAAPwAAAD/AA///wAP//////////
        //8oAAAAEAAAACAAAAABAAQAAAAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAgAAAAICAAIAA
        AACAAIAAgIAAAICAgADAwMAAAAD/AAD/AAAA//8A/wAAAP8A/wD//wAA////AAAAAAAAAAAAB3d3d3d3
        d3dERERERERER0////////hHT///////+EdP///////4R0////////hHT///////+EdP///////4R0//
        //////hHT///////+EdIiIiIiIiIR0zMzMzMzMxHxERERERERMAAAAAAAAAAAAAAAAAAAAAA//8AAIAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAD//wAA//8AAA==
</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Capture Management</value>
  </data>
  <data name="&gt;&gt;toolTip1.Name" xml:space="preserve">
    <value>toolTip1</value>
  </data>
  <data name="&gt;&gt;toolTip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolTip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>DCSSDK_CaptureMgt</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>