﻿  Processing .\Chip.idl
  Chip.idl
  Processing C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\oaidl.idl
  oaidl.idl
  Processing C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\objidl.idl
  objidl.idl
  Processing C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\unknwn.idl
  unknwn.idl
  Processing C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\wtypes.idl
  wtypes.idl
  Processing C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\wtypesbase.idl
  wtypesbase.idl
  Processing C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\basetsd.h
  basetsd.h
  Processing C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\guiddef.h
  guiddef.h
  Processing C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\oaidl.acf
  oaidl.acf
  stdafx.cpp
  Chip.cpp
D:\repos_D\SDS Collection\DCSSDK Badging\Chip\Chip.cpp(312,30): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file '/Chip.cpp')
  
D:\repos_D\SDS Collection\DCSSDK Badging\Chip\Chip.cpp(419,19): warning C4018: '<': signed/unsigned mismatch
  (compiling source file '/Chip.cpp')
  
D:\repos_D\SDS Collection\DCSSDK Badging\Chip\Chip.cpp(464,10): warning C4101: 'output': unreferenced local variable
  (compiling source file '/Chip.cpp')
  
D:\repos_D\SDS Collection\DCSSDK Badging\Chip\Chip.cpp(501,10): warning C4101: 'output': unreferenced local variable
  (compiling source file '/Chip.cpp')
  
D:\repos_D\SDS Collection\DCSSDK Badging\Chip\Chip.cpp(780,2): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc
  (compiling source file '/Chip.cpp')
  
D:\repos_D\SDS Collection\DCSSDK Badging\Chip\Chip.cpp(818,34): warning C4244: '=': conversion from 'ULONGLONG' to 'int', possible loss of data
  (compiling source file '/Chip.cpp')
  
D:\repos_D\SDS Collection\DCSSDK Badging\Chip\Chip.cpp(958,3): warning C4996: 'itoa': The POSIX name for this item is deprecated. Instead, use the ISO C and C++ conformant name: _itoa. See online help for details.
  (compiling source file '/Chip.cpp')
  
D:\repos_D\SDS Collection\DCSSDK Badging\Chip\Chip.cpp(437,1): warning C4172: returning address of local variable or temporary : output
D:\repos_D\SDS Collection\DCSSDK Badging\Chip\Chip.cpp(958,1): warning C4700: uninitialized local variable 'chp_tmp' used
Chip.obj : warning LNK4075: ignoring '/EDITANDCONTINUE' due to '/SAFESEH' specification
     Creating library .\debug\Chip.lib and object .\debug\Chip.exp
  Chip.vcxproj -> D:\repos_D\SDS Collection\DCSSDK Badging\Chip\debug\Chip.dll
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v170\Microsoft.CppCommon.targets(2448,5): warning MSB3075: The command "regsvr32 /s ".\debug\Chip.dll"" exited with code 5. Please verify that you have sufficient rights to run this command.
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v170\Microsoft.CppCommon.targets(2460,5): error MSB8011: Failed to register output. Please try enabling Per-user Redirection or register the component from a command prompt with elevated permissions.
