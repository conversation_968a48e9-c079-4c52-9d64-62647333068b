/*  $Id: haspds.h,v 1.29 2007/07/06 09:32:28 horatiu Exp $
**
**  Aladdin Knowledge Systems Ltd. (c) 1985-2007. All rights reserved.
**
**  haspds - API Interface for HASP SRM Run-time Environment Setup DLL
**
*/

#ifndef __HASPDS_H__
#define __HASPDS_H__

#pragma pack(push,_haspds_h_,1)

/*
 * data returned by the haspds_GetInfo function
 */
typedef struct Haspds_InfoItem {
    char  FileName[14];
    unsigned long InstalledVersion;
    unsigned long PackageVersion;
} HASPDS_INFO_ITEM, *PHASPDS_INFO_ITEM;

typedef struct HaspdsInfo {
    unsigned long ItemsNo;
    HASPDS_INFO_ITEM Items[1];
} HASPDS_INFO, *PHASPDS_INFO;


/*
 * vendor data that is included in the Run-Time Environment installer,
 * and data that is already installed on the machine
 */
typedef struct Haspds_VendorInfoItem {
    char FileName[128];
    unsigned long PackageVersion;
} HASPDS_VENDOR_INFO_ITEM, *PHASPDS_VENDOR_INFO_ITEM;

typedef struct HaspdsVendorInfo {
    unsigned long ItemsNo;
    HASPDS_VENDOR_INFO_ITEM Items[1];
} HASPDS_VENDOR_INFO, *PHASPDS_VENDOR_INFO;


/*
 * used for haspds_GetClientProcess und haspds_KillClientProcess
 */
#define HASPDS_PROCNAME_LEN         128
typedef struct _HaspdsClientProcess {
    DWORD ProcessType;
    DWORD PID;
    CHAR  Name[HASPDS_PROCNAME_LEN];
    DWORD Reserved1;
    DWORD Reserved2;
} HASPDS_CLIENTPROCESS, *PHASPDS_CLIENTPROCESS;


/*
 * internal errors that can be obtained using haspds_GetLastError function.
 */
typedef enum haspds_error {
    HASPDS_ERR_OK = 0,                    /* success */
    HASPDS_ERR_NO_ADMIN = 1,              /* not admin context */
    HASPDS_ERR_INVALID_PARAM = 2,         /* invalid parameter */
    HASPDS_ERR_OS_NOT_SUPPORTED = 3,      /* OS not supported */
    HASPDS_ERR_CAB_PCD = 4,               /* cab is inconsistent with the PCD */
    HASPDS_ERR_LOAD_LIB = 5,              /* error loading library */
    HASPDS_ERR_FCT_PTR = 6,               /* error getprocaddress */
    HASPDS_ERR_WIN_ERR = 7,               /* handle the error based on GetLastError() */
    HASPDS_ERR_NO_MEM = 8,                /* allocation failure */
    HASPDS_ERR_MAX_PATH = 9,              /* path > 256 */
    HASPDS_ERR_EOF = 10,                  /* end of file reached */
    HASPDS_ERR_INVALID_CFGFILE = 11,      /* config file is invalid */
    HASPDS_ERR_FILE_ERROR = 12,           /* file processing error */
    HASPDS_ERR_DISK_SPACE = 13,           /* insufficient disk space */
    HASPDS_ERR_SETUPAPI = 14,             /* setupapi function failed */
    HASPDS_ERR_UNKNOWN = 15,              /* unknown error */
    HASPDS_ERR_REGISTRY_ACCESS = 16,      /* error acessing registry */
    HASPDS_ERR_NEED_REINSERT = 17,        /* devices need to be reinserted to load new drivers */
    HASPDS_ERR_HLSERVER_RUNNING = 18,     /* Hardlock server is running */
    HASPDS_ERR_HSSERVER_RUNNING = 19,     /* LM Server Running */
    HASPDS_ERR_STILL_DRV_PROC = 20,       /* still processes accessing the hardlock driver */
    HASPDS_ERR_ALREADY_RUNNING = 21,      /* another installer is already running */
    HASPDS_ERR_WIN_SETUP_RUNNING = 22,    /* a windows setup is already running */
    HASPDS_ERR_INSERT_REQUIRED = 23,      /* on win2k ask for an insert to finish the installation */
    HASPDS_ERR_USEFR_REQUIRED = 24,       /* older installation present to uninstall the -fr option is required */
    HASPDS_ERR_USEHINST_REQUIRED = 25,    /* old installer present, need to be used to complete uninstall */
    HASPDS_ERR_REBOOT_REQUIRED = 26,      /* error to be used to provide also a message for that */
    HASPDS_SERVICE_NOT_INSTALLED = 27,    /* inconsistent name definition - remains for backward compatibility */
    HASPDS_ERR_SERVICE_NOT_INSTALLED = 27,/* drivers are not installed */
    HASPDS_UNKNOWN_PARAM = 28,            /* inconsistent name definition - remains for backward compatibility */
    HASPDS_ERR_UNKNOWN_PARAM = 28,        /* unknown driver parameter */
    HASPDS_ERR_INSTALL_OLD = 29,          /* current driver is older than installed one */
    HASPDS_ERR_SERVICE_NOT_STARTED = 30,  /* could not start hardlock service */
    HASPDS_ERR_SERVICE_NOT_STOPED = 31,   /* could not stop hardlock service */
    HASPDS_ERR_REMOVE_REBOOT_REQ = 32,    /* reboot needed after remove */
    HASPDS_ERR_START_PROCESS = 33,        /* start hasplm9x failed */
    HASPDS_ERR_INSTALL_CAT = 34,          /* problems installing a cat file */
    HASPDS_ERR_ALREADY_INSTALLED = 35,    /* drivers already installed */
    HASPDS_ERR_DRV_NEWER = 36,            /* installed drivers are newer */
    HASPDS_ERR_V2C = 37,                  /* error in V@C processing */
    HASPDS_ERR_OPEN_SCMANAGER = 38,       /* could not open service manager */
    HASPDS_ERR_OPEN_SERVICE = 39,         /* could not open service */
    HASPDS_ERR_QUERY_STATUS = 40,         /* error querying service status */
    HASPDS_ERR_DEL_SRV = 41,              /* could not uninstall service */
    HASPDS_ERR_INSTALL_PNP = 42,          /* installing an inf file failed */
    HASPDS_ERR_SET_FW = 43,               /* error open port 1947 */
    HASPDS_ERR_REMOVE_REBOOT = 44,        /* remove reboot required */
    HASPDS_ERR_OLD_BRANDED_INST = 45      /* old branded installer installed */
}haspds_error;

typedef enum haspds_status_t{
    HASPDS_STATUS_SUCCESS = 0,            /* success */
    HASPDS_STATUS_FAILED = 1,             /* function failed */
    HASPDS_STATUS_REBOOT_REQUIRED = 2,    /* reboot required */
    HASPDS_STATUS_SMALL_BUFFER = 3,       /* buffer too small */
    HASPDS_STATUS_REINSERT_REQUIRED = 4,  /* a device reinsertion is required */
    HASPDS_STATUS_USEFR_REQUIRED = 5,     /* older installation present to uninstall the -fr option is required */
    HASPDS_STATUS_USEHINST_REQUIRED = 6,  /* older installation present - use of hinstall is required */
    HASPDS_STATUS_INSERT_REQUIRED = 7,    /* a device insertion is required */
    HASPDS_STATUS_NEED_RESCAN = 8         /* need to ask a DevMgrrescan - internal only */
} haspds_status_t;


/*
 * Parameters for haspds_Install & haspds_Uninstall
 */
#define HASPDS_PARAM_KILLPROC    0x00000001  /* killproc argument present         */
#define HASPDS_PARAM_FREMOVE     0x00000002  /* fremove argument present          */
#define HASPDS_PARAM_FINSTALL    0x00000004  /* fi argument present - workaround  */
                                             /* for W2K3 issues.                  */
#define HASPDS_PARAM_NORESCAN    0x40000000  /* do not rescan                     */
#define HASPDS_PARAM_VERBOSE     0x80000000  /* verbose extended log file         */


/*
 * valid bits as parameters - add below with logical or (|) every new flag you use
 */
#define HASPDS_VALID_FLAGS HASPDS_PARAM_KILLPROC | \
                           HASPDS_PARAM_FREMOVE  | \
                           HASPDS_PARAM_VERBOSE  | \
                           HASPDS_PARAM_FINSTALL | \
                           HASPDS_PARAM_NORESCAN


/*
 * exported functions
 */
#ifdef __cplusplus
extern "C" {
#endif
haspds_status_t  __stdcall haspds_Install(unsigned long Param);
haspds_status_t  __stdcall haspds_UnInstall(unsigned long Param);
haspds_status_t  __stdcall haspds_GetInfo(PHASPDS_INFO pInfo,unsigned long* pSize);
haspds_status_t  __stdcall haspds_GetLastErrorMessage(char* pBuffer,unsigned long* pSize);
haspds_error     __stdcall haspds_GetLastError(void);
haspds_status_t  __stdcall haspds_GetClientProcess(PHASPDS_CLIENTPROCESS pClientList, unsigned long* pSize);
haspds_status_t  __stdcall haspds_KillClientProcess(PHASPDS_CLIENTPROCESS pClientList, unsigned long* pSize);
unsigned long    __stdcall haspds_GetVersion(void);
haspds_status_t  __stdcall haspds_GetVendorInfo(HASPDS_VENDOR_INFO* Buffer,unsigned long* size);
#ifdef __cplusplus
}
#endif

#pragma pack(pop,_haspds_h_)
#endif __HASPDS_H__
