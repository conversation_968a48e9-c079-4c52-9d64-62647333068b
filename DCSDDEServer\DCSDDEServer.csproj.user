<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <LastOpenVersion>7.10.3077</LastOpenVersion>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ReferencePath>C:\DCS.SDK\Badging\Referenced Files\Barcode\;C:\DCS.SDK\Lead.Net\;C:\DCS.SDK\Capture\Referenced Files\Cognitec FaceFinder\;D:\repos_D\SDS Collection\DCSSDK Badging\DCSSDK_BadgingMgt\;D:\repos_D\SDS Collection\DCSSDK Badging\DCSSDK_CaptureMgt\;D:\repos_D\SDS Collection\DCSSDK Capture\DCSSDK_Utilities\</ReferencePath>
    <CopyProjectDestinationFolder>
    </CopyProjectDestinationFolder>
    <CopyProjectUncPath>
    </CopyProjectUncPath>
    <CopyProjectOption>0</CopyProjectOption>
    <ProjectView>ProjectFiles</ProjectView>
    <ProjectTrust>0</ProjectTrust>
    <PublishUrlHistory>C:\test publish\|publish\</PublishUrlHistory>
    <InstallUrlHistory>
    </InstallUrlHistory>
    <SupportUrlHistory>
    </SupportUrlHistory>
    <UpdateUrlHistory>
    </UpdateUrlHistory>
    <BootstrapperUrlHistory>
    </BootstrapperUrlHistory>
    <FallbackCulture>en-US</FallbackCulture>
    <VerifyUploadedFiles>false</VerifyUploadedFiles>
    <EnableSecurityDebugging>false</EnableSecurityDebugging>
    <ErrorReportUrlHistory />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <EnableASPDebugging>false</EnableASPDebugging>
    <EnableASPXDebugging>false</EnableASPXDebugging>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <EnableSQLServerDebugging>false</EnableSQLServerDebugging>
    <RemoteDebugEnabled>false</RemoteDebugEnabled>
    <RemoteDebugMachine>
    </RemoteDebugMachine>
    <StartAction>Project</StartAction>
    <StartArguments>
    </StartArguments>
    <StartPage>
    </StartPage>
    <StartProgram>
    </StartProgram>
    <StartURL>
    </StartURL>
    <StartWorkingDirectory>
    </StartWorkingDirectory>
    <StartWithIE>true</StartWithIE>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <EnableASPDebugging>false</EnableASPDebugging>
    <EnableASPXDebugging>false</EnableASPXDebugging>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
    <EnableSQLServerDebugging>false</EnableSQLServerDebugging>
    <RemoteDebugEnabled>false</RemoteDebugEnabled>
    <RemoteDebugMachine>
    </RemoteDebugMachine>
    <StartAction>Project</StartAction>
    <StartArguments>
    </StartArguments>
    <StartPage>
    </StartPage>
    <StartProgram>
    </StartProgram>
    <StartURL>
    </StartURL>
    <StartWorkingDirectory>
    </StartWorkingDirectory>
    <StartWithIE>true</StartWithIE>
  </PropertyGroup>
</Project>