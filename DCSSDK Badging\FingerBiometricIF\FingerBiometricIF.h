// FingerBiometricIF.h : main header file for the FINGERBIOMETRICIF DLL
//

#if !defined(AFX_FINGERBIOMETRICIF_H__CF436713_1EB0_40CA_A41A_0E636AE71D82__INCLUDED_)
#define AFX_FINGERBIOMETRICIF_H__CF436713_1EB0_40CA_A41A_0E636AE71D82__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#ifndef __AFXWIN_H__
	#error include 'stdafx.h' before including this file for PCH
#endif

#include "resource.h"		// main symbols


extern "C" __declspec (dllexport) bool DCSExtractFeatures(char* szFingerImage, char* szFingerBiometric, long instance);
extern "C" __declspec (dllexport) long DCSVerifyFeatures(char* szFingerFeaturesFile1, char* szFingerFeaturesFile2);
extern "C" __declspec (dllexport) long DCSMinutiaMatch(char* pFingerFeaturesArray1, char* pFingerFeaturesArray2);
extern "C" __declspec (dllexport) long DCSVerifyFinger(char* szFingerImage, char* szFingerBiometric, long instance);
extern "C" __declspec (dllexport) bool DCSMeasureQuality(char* szFingerImage, long* lDryness, long* lSmudginess, long* lOrientation, long* lRoll, long* lQuality);

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_FINGERBIOMETRICIF_H__CF436713_1EB0_40CA_A41A_0E636AE71D82__INCLUDED_)
