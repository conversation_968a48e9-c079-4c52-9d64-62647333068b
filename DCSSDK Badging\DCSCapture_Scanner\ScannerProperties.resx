<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonOK.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="buttonOK.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="buttonOK.Location" type="System.Drawing.Point, System.Drawing">
    <value>360, 424</value>
  </data>
  <data name="buttonOK.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 24</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonOK.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="buttonOK.Text" xml:space="preserve">
    <value>&amp;OK</value>
  </data>
  <data name="&gt;&gt;buttonOK.Name" xml:space="preserve">
    <value>buttonOK</value>
  </data>
  <data name="&gt;&gt;buttonOK.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonOK.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonOK.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="checkBoxEnableGetFile.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkBoxEnableGetFile.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBoxEnableGetFile.Location" type="System.Drawing.Point, System.Drawing">
    <value>192, 192</value>
  </data>
  <data name="checkBoxEnableGetFile.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 16</value>
  </data>
  <data name="checkBoxEnableGetFile.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="checkBoxEnableGetFile.Text" xml:space="preserve">
    <value>Enable Get File</value>
  </data>
  <data name="checkBoxEnableGetFile.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;checkBoxEnableGetFile.Name" xml:space="preserve">
    <value>checkBoxEnableGetFile</value>
  </data>
  <data name="&gt;&gt;checkBoxEnableGetFile.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBoxEnableGetFile.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;checkBoxEnableGetFile.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="buttonCancel.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="buttonCancel.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>496, 424</value>
  </data>
  <data name="buttonCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 24</value>
  </data>
  <data name="buttonCancel.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="buttonCancel.Text" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Name" xml:space="preserve">
    <value>buttonCancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonCancel.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="pictureBox1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="pictureBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>304, 8</value>
  </data>
  <data name="pictureBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>320, 408</value>
  </data>
  <data name="pictureBox1.SizeMode" type="System.Windows.Forms.PictureBoxSizeMode, System.Windows.Forms">
    <value>StretchImage</value>
  </data>
  <data name="pictureBox1.TabIndex" type="System.Int32, mscorlib">
    <value>41</value>
  </data>
  <data name="&gt;&gt;pictureBox1.Name" xml:space="preserve">
    <value>pictureBox1</value>
  </data>
  <data name="&gt;&gt;pictureBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pictureBox1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pictureBox1.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="nbH.Location" type="System.Drawing.Point, System.Drawing">
    <value>96, 96</value>
  </data>
  <data name="nbH.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 16</value>
  </data>
  <data name="nbH.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;nbH.Name" xml:space="preserve">
    <value>nbH</value>
  </data>
  <data name="&gt;&gt;nbH.Type" xml:space="preserve">
    <value>DCSSDK_Utilities.UnitizedNumberBox, DCSSDK_Utilities, Version=1.2.709.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;nbH.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;nbH.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="nbW.Location" type="System.Drawing.Point, System.Drawing">
    <value>96, 72</value>
  </data>
  <data name="nbW.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 16</value>
  </data>
  <data name="nbW.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;nbW.Name" xml:space="preserve">
    <value>nbW</value>
  </data>
  <data name="&gt;&gt;nbW.Type" xml:space="preserve">
    <value>DCSSDK_Utilities.UnitizedNumberBox, DCSSDK_Utilities, Version=1.2.709.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;nbW.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;nbW.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="nbY.Location" type="System.Drawing.Point, System.Drawing">
    <value>96, 48</value>
  </data>
  <data name="nbY.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 16</value>
  </data>
  <data name="nbY.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;nbY.Name" xml:space="preserve">
    <value>nbY</value>
  </data>
  <data name="&gt;&gt;nbY.Type" xml:space="preserve">
    <value>DCSSDK_Utilities.UnitizedNumberBox, DCSSDK_Utilities, Version=1.2.709.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;nbY.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;nbY.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="nbX.Location" type="System.Drawing.Point, System.Drawing">
    <value>96, 24</value>
  </data>
  <data name="nbX.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 16</value>
  </data>
  <data name="nbX.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;nbX.Name" xml:space="preserve">
    <value>nbX</value>
  </data>
  <data name="&gt;&gt;nbX.Type" xml:space="preserve">
    <value>DCSSDK_Utilities.UnitizedNumberBox, DCSSDK_Utilities, Version=1.2.709.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;nbX.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;nbX.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 48</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 16</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>Y offset</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="label5.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 24</value>
  </data>
  <data name="label5.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 16</value>
  </data>
  <data name="label5.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>X offset</value>
  </data>
  <data name="&gt;&gt;label5.Name" xml:space="preserve">
    <value>label5</value>
  </data>
  <data name="&gt;&gt;label5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label5.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label5.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="label6.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 96</value>
  </data>
  <data name="label6.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 16</value>
  </data>
  <data name="label6.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>height</value>
  </data>
  <data name="&gt;&gt;label6.Name" xml:space="preserve">
    <value>label6</value>
  </data>
  <data name="&gt;&gt;label6.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label6.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label6.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="label7.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 72</value>
  </data>
  <data name="label7.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 16</value>
  </data>
  <data name="label7.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="label7.Text" xml:space="preserve">
    <value>width</value>
  </data>
  <data name="&gt;&gt;label7.Name" xml:space="preserve">
    <value>label7</value>
  </data>
  <data name="&gt;&gt;label7.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label7.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label7.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="buttonScanPreview.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 128</value>
  </data>
  <data name="buttonScanPreview.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 24</value>
  </data>
  <data name="buttonScanPreview.TabIndex" type="System.Int32, mscorlib">
    <value>44</value>
  </data>
  <data name="buttonScanPreview.Text" xml:space="preserve">
    <value>Scan Preview</value>
  </data>
  <data name="&gt;&gt;buttonScanPreview.Name" xml:space="preserve">
    <value>buttonScanPreview</value>
  </data>
  <data name="&gt;&gt;buttonScanPreview.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonScanPreview.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;buttonScanPreview.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="groupBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 56</value>
  </data>
  <data name="groupBox2.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 160</value>
  </data>
  <data name="groupBox2.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="groupBox2.Text" xml:space="preserve">
    <value>Preview scan</value>
  </data>
  <data name="&gt;&gt;groupBox2.Name" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;groupBox2.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBox2.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="buttonNext.Location" type="System.Drawing.Point, System.Drawing">
    <value>168, 24</value>
  </data>
  <data name="buttonNext.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 21</value>
  </data>
  <data name="buttonNext.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="buttonNext.Text" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="&gt;&gt;buttonNext.Name" xml:space="preserve">
    <value>buttonNext</value>
  </data>
  <data name="&gt;&gt;buttonNext.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonNext.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;buttonNext.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label17.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 136</value>
  </data>
  <data name="label17.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 16</value>
  </data>
  <data name="label17.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="label17.Text" xml:space="preserve">
    <value>Orientation</value>
  </data>
  <data name="label17.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label17.Name" xml:space="preserve">
    <value>label17</value>
  </data>
  <data name="&gt;&gt;label17.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label17.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label17.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pictureBoxRotation.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        Qk02DQAAAAAAADYEAAAoAAAAMAAAADAAAAABAAgAAAAAAAAAAADEDgAAxA4AAAABAAAAAQAAAAAA/wAA
        gP8AgAD/AICA/4AAAP+AAID/gIAA/8DAwP/A3MD/8Mik/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAAAP8AAAD/AAAA/wAA
        AP8AAAD/AAAA/wAAAP8AAAD/8Pv//6SgoP+AgID/AAD//wD/AP8A/////wAA//8A/////wD/////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        //////////////////////////////////////////////////////////8EBAQEBP//////////////
        ////////////////////////////////////BAQEBAQEBAQEBAQEBAQE////////////////////////
        /////////////////wQEBAQEBAT//////wQEBAQEBAT/////////////////////////////////////
        BAQE////////////////////BAQE/////////////////////////////////wQEBP//////////////
        //////////8EBAT/////////////////////////////BAT//////////////////////////////wQE
        //////////////////////////8EBP///////////////wT///////////////8EBP//////////////
        /////////wT//////////////////wT//////////////////wT/////////////////////BP//////
        ////////////BAQE//////////////////8E//////////////////8EBP//////////////////BAQE
        //////////////////8EBP////////////////8E//////////////////8EBAQEBP//////////////
        ////BP///////////////wT///////////////////8EBAQEBP///////////////////wT/////////
        ////BAT//////////////////wQEBAQEBAT//////////////////wQE////////////BP//////////
        //////////////////////////////////////8E//////////8EBP///////////////////////wT/
        //////////////////////8EBP////////8EBP///////////////////////wT/////////////////
        //////8EBP////////8EBP///////////////////////wT///////////////////////8EBP//////
        //8EBP///////////wT//////////wT//////////wT///////////8EBP////////8E//////////8E
        BAT//////////wT//////////wQEBP//////////BP///////wQE////////BAQEBAT//////////wT/
        /////////wQEBAQE////////BAT//////wQE/////wQEBAQEBAT/BAQEBAQEBAQEBAQEBAQE/wQEBAQE
        BAT/////BAT//////wQE////////BAQEBAT/////////+fn5/////////wQEBAQE////////BAT/////
        /wQE//////////8EBAT/////////+fn5/////////wQEBP//////////BAT///////8EBP//////////
        /wT/////////+fn5/////////wT///////////8EBP////////8EBP//////////////////////+fn5
        //////////////////////8EBP////////8EBP//////////////////////+fn5////////////////
        //////8EBP////////8EBP//////////////////////+fn5//////////////////////8EBP//////
        //8EBP//////////////////////+fn5//////////////////////8EBP//////////BAT/////////
        ///////5+fn5+fn5+fn5+f///////////////wQE////////////BAT/////////////////+fn5+fn5
        +fn5/////////////////wQE/////////////wQE//////////////////n5+fn5+fn/////////////
        ////BAT///////////////8EBP/////////////////5+fn5+f////////////////8EBP//////////
        //////8EBP/////////////////5+fn5+f////////////////8EBP//////////////////BAT/////
        ////////////+fn5/////////////////wQE/////////////////////wQE//////////////////n/
        ////////////////BAT///////////////////////8EBP////////////////n///////////////8E
        BP///////////////////////////wQE////////////////////////////BAT/////////////////
        //////////////8EBP////////////////////////8EBP//////////////////////////////////
        /wQE////////////////////BAT/////////////////////////////////////////BAQEBAT/////
        /wQEBAQE//////////////////////////////////////////////////8EBAQEBP//////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        /////////////////////////////w==
</value>
  </data>
  <data name="pictureBoxRotation.Location" type="System.Drawing.Point, System.Drawing">
    <value>72, 144</value>
  </data>
  <data name="pictureBoxRotation.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 48</value>
  </data>
  <data name="pictureBoxRotation.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="&gt;&gt;pictureBoxRotation.Name" xml:space="preserve">
    <value>pictureBoxRotation</value>
  </data>
  <data name="&gt;&gt;pictureBoxRotation.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pictureBoxRotation.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;pictureBoxRotation.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="label16.Location" type="System.Drawing.Point, System.Drawing">
    <value>144, 56</value>
  </data>
  <data name="label16.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 16</value>
  </data>
  <data name="label16.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="label16.Text" xml:space="preserve">
    <value>Bounds</value>
  </data>
  <data name="&gt;&gt;label16.Name" xml:space="preserve">
    <value>label16</value>
  </data>
  <data name="&gt;&gt;label16.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label16.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label16.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label15.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 56</value>
  </data>
  <data name="label15.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 16</value>
  </data>
  <data name="label15.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="label15.Text" xml:space="preserve">
    <value>Pixels:</value>
  </data>
  <data name="&gt;&gt;label15.Name" xml:space="preserve">
    <value>label15</value>
  </data>
  <data name="&gt;&gt;label15.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label15.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label15.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="textBoxPixelHeight.Location" type="System.Drawing.Point, System.Drawing">
    <value>72, 104</value>
  </data>
  <data name="textBoxPixelHeight.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="textBoxPixelHeight.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="textBoxPixelHeight.Text" xml:space="preserve">
    <value>300</value>
  </data>
  <data name="&gt;&gt;textBoxPixelHeight.Name" xml:space="preserve">
    <value>textBoxPixelHeight</value>
  </data>
  <data name="&gt;&gt;textBoxPixelHeight.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxPixelHeight.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;textBoxPixelHeight.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="label14.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 104</value>
  </data>
  <data name="label14.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 16</value>
  </data>
  <data name="label14.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="label14.Text" xml:space="preserve">
    <value>rows</value>
  </data>
  <data name="&gt;&gt;label14.Name" xml:space="preserve">
    <value>label14</value>
  </data>
  <data name="&gt;&gt;label14.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label14.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label14.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="textBoxPixelWidth.Location" type="System.Drawing.Point, System.Drawing">
    <value>72, 80</value>
  </data>
  <data name="textBoxPixelWidth.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="textBoxPixelWidth.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="textBoxPixelWidth.Text" xml:space="preserve">
    <value>300</value>
  </data>
  <data name="&gt;&gt;textBoxPixelWidth.Name" xml:space="preserve">
    <value>textBoxPixelWidth</value>
  </data>
  <data name="&gt;&gt;textBoxPixelWidth.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxPixelWidth.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;textBoxPixelWidth.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="label13.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 80</value>
  </data>
  <data name="label13.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 16</value>
  </data>
  <data name="label13.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="label13.Text" xml:space="preserve">
    <value>columns</value>
  </data>
  <data name="&gt;&gt;label13.Name" xml:space="preserve">
    <value>label13</value>
  </data>
  <data name="&gt;&gt;label13.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label13.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label13.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="nbObjH.Location" type="System.Drawing.Point, System.Drawing">
    <value>200, 152</value>
  </data>
  <data name="nbObjH.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 16</value>
  </data>
  <data name="nbObjH.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="&gt;&gt;nbObjH.Name" xml:space="preserve">
    <value>nbObjH</value>
  </data>
  <data name="&gt;&gt;nbObjH.Type" xml:space="preserve">
    <value>DCSSDK_Utilities.UnitizedNumberBox, DCSSDK_Utilities, Version=1.2.709.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;nbObjH.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;nbObjH.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="nbObjW.Location" type="System.Drawing.Point, System.Drawing">
    <value>200, 128</value>
  </data>
  <data name="nbObjW.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 16</value>
  </data>
  <data name="nbObjW.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="&gt;&gt;nbObjW.Name" xml:space="preserve">
    <value>nbObjW</value>
  </data>
  <data name="&gt;&gt;nbObjW.Type" xml:space="preserve">
    <value>DCSSDK_Utilities.UnitizedNumberBox, DCSSDK_Utilities, Version=1.2.709.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;nbObjW.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;nbObjW.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="nbObjY.Location" type="System.Drawing.Point, System.Drawing">
    <value>200, 104</value>
  </data>
  <data name="nbObjY.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 16</value>
  </data>
  <data name="nbObjY.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="&gt;&gt;nbObjY.Name" xml:space="preserve">
    <value>nbObjY</value>
  </data>
  <data name="&gt;&gt;nbObjY.Type" xml:space="preserve">
    <value>DCSSDK_Utilities.UnitizedNumberBox, DCSSDK_Utilities, Version=1.2.709.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;nbObjY.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;nbObjY.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="nbObjX.Location" type="System.Drawing.Point, System.Drawing">
    <value>200, 80</value>
  </data>
  <data name="nbObjX.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 16</value>
  </data>
  <data name="nbObjX.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="&gt;&gt;nbObjX.Name" xml:space="preserve">
    <value>nbObjX</value>
  </data>
  <data name="&gt;&gt;nbObjX.Type" xml:space="preserve">
    <value>DCSSDK_Utilities.UnitizedNumberBox, DCSSDK_Utilities, Version=1.2.709.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;nbObjX.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;nbObjX.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="label8.Location" type="System.Drawing.Point, System.Drawing">
    <value>144, 104</value>
  </data>
  <data name="label8.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 16</value>
  </data>
  <data name="label8.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="label8.Text" xml:space="preserve">
    <value>Y offset</value>
  </data>
  <data name="&gt;&gt;label8.Name" xml:space="preserve">
    <value>label8</value>
  </data>
  <data name="&gt;&gt;label8.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label8.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label8.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="label9.Location" type="System.Drawing.Point, System.Drawing">
    <value>144, 80</value>
  </data>
  <data name="label9.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 16</value>
  </data>
  <data name="label9.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="label9.Text" xml:space="preserve">
    <value>X offset</value>
  </data>
  <data name="&gt;&gt;label9.Name" xml:space="preserve">
    <value>label9</value>
  </data>
  <data name="&gt;&gt;label9.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label9.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label9.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="label10.Location" type="System.Drawing.Point, System.Drawing">
    <value>144, 152</value>
  </data>
  <data name="label10.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 16</value>
  </data>
  <data name="label10.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="label10.Text" xml:space="preserve">
    <value>height</value>
  </data>
  <data name="&gt;&gt;label10.Name" xml:space="preserve">
    <value>label10</value>
  </data>
  <data name="&gt;&gt;label10.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label10.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label10.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="label11.Location" type="System.Drawing.Point, System.Drawing">
    <value>144, 128</value>
  </data>
  <data name="label11.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 16</value>
  </data>
  <data name="label11.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="label11.Text" xml:space="preserve">
    <value>width</value>
  </data>
  <data name="&gt;&gt;label11.Name" xml:space="preserve">
    <value>label11</value>
  </data>
  <data name="&gt;&gt;label11.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label11.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label11.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="textBoxResolution.Location" type="System.Drawing.Point, System.Drawing">
    <value>200, 176</value>
  </data>
  <data name="textBoxResolution.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="textBoxResolution.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="textBoxResolution.Text" xml:space="preserve">
    <value>300</value>
  </data>
  <data name="&gt;&gt;textBoxResolution.Name" xml:space="preserve">
    <value>textBoxResolution</value>
  </data>
  <data name="&gt;&gt;textBoxResolution.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxResolution.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;textBoxResolution.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>136, 176</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 16</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>resolution</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="comboBoxObjects.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 24</value>
  </data>
  <data name="comboBoxObjects.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 21</value>
  </data>
  <data name="comboBoxObjects.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;comboBoxObjects.Name" xml:space="preserve">
    <value>comboBoxObjects</value>
  </data>
  <data name="&gt;&gt;comboBoxObjects.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxObjects.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;comboBoxObjects.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="groupBox3.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 224</value>
  </data>
  <data name="groupBox3.Size" type="System.Drawing.Size, System.Drawing">
    <value>272, 208</value>
  </data>
  <data name="groupBox3.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="groupBox3.Text" xml:space="preserve">
    <value>Scanned object</value>
  </data>
  <data name="&gt;&gt;groupBox3.Name" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;groupBox3.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBox3.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="checkBoxSinglePass.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkBoxSinglePass.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBoxSinglePass.Location" type="System.Drawing.Point, System.Drawing">
    <value>192, 104</value>
  </data>
  <data name="checkBoxSinglePass.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 16</value>
  </data>
  <data name="checkBoxSinglePass.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="checkBoxSinglePass.Text" xml:space="preserve">
    <value>Single pass scan</value>
  </data>
  <data name="&gt;&gt;checkBoxSinglePass.Name" xml:space="preserve">
    <value>checkBoxSinglePass</value>
  </data>
  <data name="&gt;&gt;checkBoxSinglePass.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBoxSinglePass.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;checkBoxSinglePass.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>248, 152</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 32</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>Scan margin</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="comboBoxUnits.Items" xml:space="preserve">
    <value>Inch</value>
  </data>
  <data name="comboBoxUnits.Items1" xml:space="preserve">
    <value>MM</value>
  </data>
  <data name="comboBoxUnits.Location" type="System.Drawing.Point, System.Drawing">
    <value>192, 64</value>
  </data>
  <data name="comboBoxUnits.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 21</value>
  </data>
  <data name="comboBoxUnits.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="comboBoxUnits.Text" xml:space="preserve">
    <value>Inch</value>
  </data>
  <data name="&gt;&gt;comboBoxUnits.Name" xml:space="preserve">
    <value>comboBoxUnits</value>
  </data>
  <data name="&gt;&gt;comboBoxUnits.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxUnits.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;comboBoxUnits.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="nbOverScan.Location" type="System.Drawing.Point, System.Drawing">
    <value>192, 152</value>
  </data>
  <data name="nbOverScan.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 16</value>
  </data>
  <data name="nbOverScan.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;nbOverScan.Name" xml:space="preserve">
    <value>nbOverScan</value>
  </data>
  <data name="&gt;&gt;nbOverScan.Type" xml:space="preserve">
    <value>DCSSDK_Utilities.UnitizedNumberBox, DCSSDK_Utilities, Version=1.2.709.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;nbOverScan.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;nbOverScan.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="label12.Location" type="System.Drawing.Point, System.Drawing">
    <value>256, 64</value>
  </data>
  <data name="label12.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 16</value>
  </data>
  <data name="label12.TabIndex" type="System.Int32, mscorlib">
    <value>71</value>
  </data>
  <data name="label12.Text" xml:space="preserve">
    <value>units</value>
  </data>
  <data name="&gt;&gt;label12.Name" xml:space="preserve">
    <value>label12</value>
  </data>
  <data name="&gt;&gt;label12.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label12.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label12.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="checkBoxCropFineTuning.Location" type="System.Drawing.Point, System.Drawing">
    <value>192, 128</value>
  </data>
  <data name="checkBoxCropFineTuning.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 21</value>
  </data>
  <data name="checkBoxCropFineTuning.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="checkBoxCropFineTuning.Text" xml:space="preserve">
    <value>Crop fine tuning</value>
  </data>
  <data name="&gt;&gt;checkBoxCropFineTuning.Name" xml:space="preserve">
    <value>checkBoxCropFineTuning</value>
  </data>
  <data name="&gt;&gt;checkBoxCropFineTuning.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBoxCropFineTuning.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;checkBoxCropFineTuning.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <metadata name="imageList1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="imageList1.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj0yLjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAABU
        FQAAAk1TRnQBSQFMAgEBBAEAAQkBAAEEAQABMAEAATABAAT/AQkBAAj/AUIBTQE2AQQGAAE2AQQCAAEo
        AwABwAMAAWADAAEBAQABCAYAAUgYAAGAAgABgAMAAoABAAGAAwABgAEAAYABAAKAAgADwAEAAcAB3AHA
        AQAB8AHKAaYBAAEzBQABMwEAATMBAAEzAQACMwIAAxYBAAMcAQADIgEAAykBAANVAQADTQEAA0IBAAM5
        AQABgAF8Af8BAAJQAf8BAAGTAQAB1gEAAf8B7AHMAQABxgHWAe8BAAHWAucBAAGQAakBrQIAAf8BMwMA
        AWYDAAGZAwABzAIAATMDAAIzAgABMwFmAgABMwGZAgABMwHMAgABMwH/AgABZgMAAWYBMwIAAmYCAAFm
        AZkCAAFmAcwCAAFmAf8CAAGZAwABmQEzAgABmQFmAgACmQIAAZkBzAIAAZkB/wIAAcwDAAHMATMCAAHM
        AWYCAAHMAZkCAALMAgABzAH/AgAB/wFmAgAB/wGZAgAB/wHMAQABMwH/AgAB/wEAATMBAAEzAQABZgEA
        ATMBAAGZAQABMwEAAcwBAAEzAQAB/wEAAf8BMwIAAzMBAAIzAWYBAAIzAZkBAAIzAcwBAAIzAf8BAAEz
        AWYCAAEzAWYBMwEAATMCZgEAATMBZgGZAQABMwFmAcwBAAEzAWYB/wEAATMBmQIAATMBmQEzAQABMwGZ
        AWYBAAEzApkBAAEzAZkBzAEAATMBmQH/AQABMwHMAgABMwHMATMBAAEzAcwBZgEAATMBzAGZAQABMwLM
        AQABMwHMAf8BAAEzAf8BMwEAATMB/wFmAQABMwH/AZkBAAEzAf8BzAEAATMC/wEAAWYDAAFmAQABMwEA
        AWYBAAFmAQABZgEAAZkBAAFmAQABzAEAAWYBAAH/AQABZgEzAgABZgIzAQABZgEzAWYBAAFmATMBmQEA
        AWYBMwHMAQABZgEzAf8BAAJmAgACZgEzAQADZgEAAmYBmQEAAmYBzAEAAWYBmQIAAWYBmQEzAQABZgGZ
        AWYBAAFmApkBAAFmAZkBzAEAAWYBmQH/AQABZgHMAgABZgHMATMBAAFmAcwBmQEAAWYCzAEAAWYBzAH/
        AQABZgH/AgABZgH/ATMBAAFmAf8BmQEAAWYB/wHMAQABzAEAAf8BAAH/AQABzAEAApkCAAGZATMBmQEA
        AZkBAAGZAQABmQEAAcwBAAGZAwABmQIzAQABmQEAAWYBAAGZATMBzAEAAZkBAAH/AQABmQFmAgABmQFm
        ATMBAAGZATMBZgEAAZkBZgGZAQABmQFmAcwBAAGZATMB/wEAApkBMwEAApkBZgEAA5kBAAKZAcwBAAKZ
        Af8BAAGZAcwCAAGZAcwBMwEAAWYBzAFmAQABmQHMAZkBAAGZAswBAAGZAcwB/wEAAZkB/wIAAZkB/wEz
        AQABmQHMAWYBAAGZAf8BmQEAAZkB/wHMAQABmQL/AQABzAMAAZkBAAEzAQABzAEAAWYBAAHMAQABmQEA
        AcwBAAHMAQABmQEzAgABzAIzAQABzAEzAWYBAAHMATMBmQEAAcwBMwHMAQABzAEzAf8BAAHMAWYCAAHM
        AWYBMwEAAZkCZgEAAcwBZgGZAQABzAFmAcwBAAGZAWYB/wEAAcwBmQIAAcwBmQEzAQABzAGZAWYBAAHM
        ApkBAAHMAZkBzAEAAcwBmQH/AQACzAIAAswBMwEAAswBZgEAAswBmQEAA8wBAALMAf8BAAHMAf8CAAHM
        Af8BMwEAAZkB/wFmAQABzAH/AZkBAAHMAf8BzAEAAcwC/wEAAcwBAAEzAQAB/wEAAWYBAAH/AQABmQEA
        AcwBMwIAAf8CMwEAAf8BMwFmAQAB/wEzAZkBAAH/ATMBzAEAAf8BMwH/AQAB/wFmAgAB/wFmATMBAAHM
        AmYBAAH/AWYBmQEAAf8BZgHMAQABzAFmAf8BAAH/AZkCAAH/AZkBMwEAAf8BmQFmAQAB/wKZAQAB/wGZ
        AcwBAAH/AZkB/wEAAf8BzAIAAf8BzAEzAQAB/wHMAWYBAAH/AcwBmQEAAf8CzAEAAf8BzAH/AQAC/wEz
        AQABzAH/AWYBAAL/AZkBAAL/AcwBAAJmAf8BAAFmAf8BZgEAAWYC/wEAAf8CZgEAAf8BZgH/AQAC/wFm
        AQABIQEAAaUBAANfAQADdwEAA4YBAAOWAQADywEAA7IBAAPXAQAD3QEAA+MBAAPqAQAD8QEAA/gBAAHw
        AfsB/wEAAaQCoAEAA4ADAAH/AgAB/wMAAv8BAAH/AwAB/wEAAf8BAAL/AgAD//8A/wD/AP8A/wD/AP8A
        /wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A
        /wB9AAUEKwAFBCsABQQrAAUEJgAPBCEADwQhAA8EIQAPBB8ABwQFAAcEHQAHBAUABwQdAAcEBQAHBB0A
        BwQFAAcEHAADBA8AAwQbAAMEDwADBBsAAwQPAAMEGwADBA8AAwQZAAMEEwADBBcAAwQTAAMEFwADBBMA
        AwQXAAMEEwADBBYAAgQXAAIEFQACBBcAAgQVAAIEFwACBBUAAgQXAAIEFAACBAwAAQQMAAIEEwACBAwA
        AQQMAAIEEwACBAwAAfkMAAIEEwACBAwAAQQMAAIEEgABBA4AAQQOAAEEEQABBA4AAQQOAAEEEQABBA4A
        AfkOAAEEEQABBA4AAQQOAAEEEAABBA4AAwQOAAEEDwABBA4AAwQOAAEEDwABBA4AA/kOAAEEDwABBA4A
        AwQOAAEEDgACBA4AAwQOAAIEDQACBA4AAwQOAAIEDQACBA0ABfkNAAIEDQACBA4AAwQOAAIEDQABBA4A
        BQQOAAEEDQABBA4ABQQOAAEEDQABBA0ABvkOAAEEDQABBA4ABQQOAAEEDAABBA8ABQQPAAEECwABBA8A
        BQQPAAEECwABBA4AB/kOAAEECwABBA8ABQQPAAEECgACBA4ABwQOAAIECQACBA4ABwQOAAIECQACBA0A
        CfkNAAIECQACBA4ABwQOAAIECQABBCUAAQQJAAEEJQABBAkAAQQNAAv5DQABBAkAAQQlAAEECAACBBIA
        AQQSAAIEBwACBBIAAQQSAAIEBwACBBEAA/kRAAIEBwACBBIAAQQSAAIEBwACBBIAAQQSAAIEBwACBBIA
        AQQHAAH5CgACBAcAAgQRAAP5EQACBAcAAgQKAAH5BwABBBIAAgQHAAIEEgABBBIAAgQHAAIEEgABBAcA
        AvkJAAIEBwACBBEAA/kRAAIEBwACBAkAAvkHAAEEEgACBAcAAgQJAAEECAABBAgAAQQJAAIEBwACBAkA
        AQQIAAEEBwAD+QgAAgQHAAIECQABBAcAA/kHAAEECQACBAcAAgQHAAT5BwABBAgAAQQJAAIEBwABBAgA
        AwQIAAEECAADBAgAAQQHAAEECAADBAgAAQQHAAX5BwABBAcAAQQIAAMEBwAD+QcAAwQIAAEEBwABBAcA
        BfkHAAEECAADBAgAAQQGAAIEBgAFBAgAAQQIAAUEBgACBAUAAgQGAAUECAABBA35BgACBAUAAgQGAAUE
        BwAD+QcABQQGAAIEBQACBAYADfkBBAgABQQGAAIEBQACBAQABwQBAA8EAQAHBAQAAgQFAAIEBAAHBAEA
        CAQP+QQAAgQFAAIEBAAHBAEADwQBAAcEBAACBAUAAgQEAA/5CAQBAAcEBAACBAUAAgQGAAUEBwAD+QcA
        BQQGAAIEBQACBAYABQQIAAEEDfkGAAIEBQACBAYABQQIAAEECAAFBAYAAgQFAAIEBgAN+QEECAAFBAYA
        AgQFAAIECAADBAcAA/kHAAMECAACBAUAAgQIAAMECAABBAcABfkHAAIEBQACBAgAAwQIAAEECAADBAgA
        AgQFAAIEBwAF+QcAAQQIAAMECAACBAYAAgQJAAEEBwAD+QcAAQQJAAIEBwACBAkAAQQIAAEEBwAD+QgA
        AgQHAAIECQABBAgAAQQIAAEECQACBAcAAgQHAAT5BwABBAgAAQQJAAIEBwACBBEAA/kRAAIEBwACBBIA
        AQQHAAL5CQACBAcAAgQSAAEEEgACBAcAAgQJAAL5BwABBBIAAgQHAAIEEQAD+REAAgQHAAIEEgABBAcA
        AfkKAAIEBwACBBIAAQQSAAIEBwACBAoAAfkHAAEEEgACBAcAAgQRAAP5EQACBAcAAgQSAAEEEgACBAcA
        AgQSAAEEEgACBAcAAgQSAAEEEgACBAcAAgQRAAP5EQACBAcAAgQSAAEEEgACBAcAAgQSAAEEEgACBAcA
        AgQSAAEEEgACBAgAAgQMAAv5DAACBAkAAgQjAAIECQACBCMAAgQJAAIEIwACBAkAAgQNAAn5DQACBAkA
        AgQOAAcEDgACBAkAAgQOAAcEDgACBAkAAgQOAAcEDgACBAoAAgQNAAf5DQACBAsAAgQOAAUEDgACBAsA
        AgQOAAUEDgACBAsAAgQOAAUEDgACBAwAAgQNAAX5DQACBA0AAgQNAAUEDQACBA0AAgQNAAUEDQACBA0A
        AgQNAAUEDQACBA0AAgQNAAX5DQACBA0AAgQOAAMEDgACBA0AAgQOAAMEDgACBA0AAgQOAAMEDgACBA4A
        AgQNAAP5DQACBA8AAgQNAAMEDQACBA8AAgQNAAMEDQACBA8AAgQNAAMEDQACBBAAAgQNAAH5DQACBBEA
        AgQNAAEEDQACBBEAAgQNAAEEDQACBBEAAgQNAAEEDQACBBIAAgQMAAH5DAACBBMAAgQMAAEEDAACBBMA
        AgQMAAEEDAACBBMAAgQMAAEEDAACBBUAAgQVAAIEFwACBBUAAgQXAAIEFQACBBcAAgQVAAIEGAACBBMA
        AgQZAAIEEwACBBkAAgQTAAIEGQACBBMAAgQbAAIEDwACBB0AAgQPAAIEHQACBA8AAgQdAAIEDwACBB8A
        BQQFAAUEIQAFBAUABQQhAAUEBQAFBCEABQQFAAUEJgAFBCsABQQrAAUEKwAFBP8A/wD/ABgAAUIBTQE+
        BwABPgMAASgDAAHAAwABYAMAAQEBAAEBBgABCRYAA///AP8A/wD/AIUASv8B/AEfBP8B/AEfBP8B/AEf
        BP8B/AEfBP8BgAEABP8BgAEABP8BgAEABP8BgAEAA/8B/gEDAeABPwL/Af4BAwHgAT8C/wH+AQMB4AE/
        Av8B/gEDAeABPwL/AfwBfwH/AR8C/wH8AX8B/wEfAv8B/AF/Af8BHwL/AfwBfwH/AR8C/wHxAv8BxwL/
        AfEC/wHHAv8B8QL/AccC/wHxAv8BxwL/AecC/wHzAv8B5wL/AfMC/wHnAv8B8wL/AecC/wHzAv8BzwH/
        AX8B+QL/Ac8B/wF/AfkC/wHPAf8BfwH5Av8BzwH/AX8B+QL/Ab8B/wF/Af4C/wG/Af8BfwH+Av8BvwH/
        AX8B/gL/Ab8B/wF/Af4C/wF/Af4BPwH/AX8B/wF/Af4BPwH/AX8B/wF/Af4BPwH/AX8B/wF/Af4BPwH/
        AX8B/gF/Af4BPwH/AT8B/gF/Af4BPwH/AT8B/gF/AfwBHwH/AT8B/gF/Af4BPwH/AT8B/gH/AfwBHwH/
        Ab8B/gH/AfwBHwH/Ab8B/gH/AfgBHwH/Ab8B/gH/AfwBHwH/Ab8B/QH/AfwBHwH/Ad8B/QH/AfwBHwH/
        Ad8B/QH/AfgBDwH/Ad8B/QH/AfwBHwH/Ad8B+QH/AfgBDwH/Ac8B+QH/AfgBDwH/Ac8B+QH/AfABBwH/
        Ac8B+QH/AfgBDwH/Ac8B+wT/Ae8B+wT/Ae8B+wH/AeABAwH/Ae8B+wT/Ae8B8wL/AX8B/wHnAfMC/wF/
        Af8B5wHzAf8B/gE/Af8B5wHzAv8BfwH/AecB8wL/AX8B/wHnAfMC/wJ/AecB8wH/Af4BPwH/AecB8wH/
        An8B/wHnAfMC/wF/Af8B5wHzAv8BfwE/AecB8wH/Af4BPwH/AecB8wH+An8B/wHnAfMB/gH/AX8BvwHn
        AfMB/gH/AX8BHwHnAfMC/gE/Ab8B5wHzAfgCfwG/AecB9wH4Af8BfwGPAvcB+AH/AX8BBwL3AfgB/gE/
        AY8C9wHwAn8BjwH3AecB4AH/AX8BgwHzAecB4AH/AQABAwHzAecB4AH+AT8BgwHzAecB4AEAAX8BgwHz
        AecCgAEAAYAB8wHnAoACAAHzAecCgAEAAYAB8wHnAYACAAGAAfMB5wHgAf4BPwGDAfMB5wHgAf8BAAED
        AfMB5wHgAf8BfwGDAfMB5wHgAQABfwGDAfMB5wH4Af4BPwGPAfMB5wH4Af8BfwEHAfMB5wH4Af8BfwGP
        AfMB5wHwAn8BjwLzAv4BPwG/AecB8wH+Af8BfwEfAecB8wH+Af8BfwG/AecB8wH4An8BvwHnAfMB/wH+
        AT8B/wHnAfMC/wF/AT8B5wHzAv8BfwH/AecB8wH+An8B/wHnAfMB/wH+AT8B/wHnAfMC/wJ/AecB8wL/
        AX8B/wHnAfMB/wJ/Af8B5wHzAf8B/gE/Af8B5wHzAv8BfwH/AecB8wL/AX8B/wHnAfMC/wF/Af8B5wHz
        Af8B/gE/Af8B5wHzAv8BfwH/AecB8wL/AX8B/wHnAfMC/wF/Af8B5wH5Af8B4AEDAf8BzwH5BP8BzwH5
        BP8BzwH5BP8BzwH5Af8B8AEHAf8BzwH5Af8B+AEPAf8BzwH5Af8B+AEPAf8BzwH5Af8B+AEPAf8BzwH8
        Af8B+AEPAf8BnwH8Af8B/AEfAf8BnwH8Af8B/AEfAf8BnwH8Af8B/AEfAf8BnwH+AX8B/AEfAf8BPwH+
        AX8B/AEfAf8BPwH+AX8B/AEfAf8BPwH+AX8B/AEfAf8BPwH+AX8B/AEfAf8BPwH+AX8B/gE/Af8BPwH+
        AX8B/gE/Af8BPwH+AX8B/gE/Af8BPwH/AT8B/gE/Af4BfwH/AT8B/gE/Af4BfwH/AT8B/gE/Af4BfwH/
        AT8B/gE/Af4BfwH/AZ8B/wF/AfwC/wGfAf8BfwH8Av8BnwH/AX8B/AL/AZ8B/wF/AfwC/wHPAf8BfwH5
        Av8BzwH/AX8B+QL/Ac8B/wF/AfkC/wHPAf8BfwH5Av8B8wL/AecC/wHzAv8B5wL/AfMC/wHnAv8B8wL/
        AecC/wH5Av8BzwL/AfkC/wHPAv8B+QL/Ac8C/wH5Av8BzwL/Af4BfwH/AT8C/wH+AX8B/wE/Av8B/gF/
        Af8BPwL/Af4BfwH/AT8D/wGDAeAE/wGDAeAE/wGDAeAE/wGDAeAE/wH8AR8E/wH8AR8E/wH8AR8E/wH8
        AR9i/ws=
</value>
  </data>
  <data name="buttonSelectDevice.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonSelectDevice.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 8</value>
  </data>
  <data name="buttonSelectDevice.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 23</value>
  </data>
  <data name="buttonSelectDevice.TabIndex" type="System.Int32, mscorlib">
    <value>72</value>
  </data>
  <data name="buttonSelectDevice.Text" xml:space="preserve">
    <value>Select Scanner Device</value>
  </data>
  <data name="&gt;&gt;buttonSelectDevice.Name" xml:space="preserve">
    <value>buttonSelectDevice</value>
  </data>
  <data name="&gt;&gt;buttonSelectDevice.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonSelectDevice.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonSelectDevice.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="deviceNameTextBox.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 32</value>
  </data>
  <data name="deviceNameTextBox.Size" type="System.Drawing.Size, System.Drawing">
    <value>280, 20</value>
  </data>
  <data name="deviceNameTextBox.TabIndex" type="System.Int32, mscorlib">
    <value>73</value>
  </data>
  <data name="&gt;&gt;deviceNameTextBox.Name" xml:space="preserve">
    <value>deviceNameTextBox</value>
  </data>
  <data name="&gt;&gt;deviceNameTextBox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;deviceNameTextBox.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;deviceNameTextBox.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>40</value>
  </metadata>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>631, 452</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Scanner Capture Properties</value>
  </data>
  <data name="&gt;&gt;imageList1.Name" xml:space="preserve">
    <value>imageList1</value>
  </data>
  <data name="&gt;&gt;imageList1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ImageList, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>ScannerProperties</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>