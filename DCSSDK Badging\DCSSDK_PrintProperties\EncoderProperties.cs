using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;

namespace DCSDEV.PrintProperties
{
	/// <summary>
	/// Summary description for EncoderProperties.
	/// </summary>
    public class EncoderProperties : System.Windows.Forms.Form
	{
		private System.Windows.Forms.CheckBox checkEnableTrack1;
		private System.Windows.Forms.CheckBox checkEnableTrack2;
		private System.Windows.Forms.CheckBox checkEnableTrack3;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.ComboBox comboBoxEncoderBrand;
		private System.Windows.Forms.TextBox tbPrefixTrack1;
		private System.Windows.Forms.TextBox tbSuffixTrack1;
		private System.Windows.Forms.TextBox tbSuffixTrack2;
		private System.Windows.Forms.TextBox tbPrefixTrack2;
		private System.Windows.Forms.TextBox tbSuffixTrack3;
		private System.Windows.Forms.TextBox tbPrefixTrack3;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.Button buttonCancel;
		private System.Windows.Forms.Button buttonAccept;
		private System.Windows.Forms.Button buttonDefaults;
        private Label labelTk1Comment;
        private Label labelTk2Comment;
        private Label labelTk3Comment;
        private Label labelTk3CommentAAMVA;
        private CheckBox checkEnableAAMVA;
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		public EncoderProperties()
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			this.AssignVisibilities();
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(EncoderProperties));
			this.checkEnableTrack1 = new System.Windows.Forms.CheckBox();
			this.checkEnableTrack2 = new System.Windows.Forms.CheckBox();
			this.checkEnableTrack3 = new System.Windows.Forms.CheckBox();
			this.label1 = new System.Windows.Forms.Label();
			this.comboBoxEncoderBrand = new System.Windows.Forms.ComboBox();
			this.tbPrefixTrack1 = new System.Windows.Forms.TextBox();
			this.tbSuffixTrack1 = new System.Windows.Forms.TextBox();
			this.tbSuffixTrack2 = new System.Windows.Forms.TextBox();
			this.tbPrefixTrack2 = new System.Windows.Forms.TextBox();
			this.tbSuffixTrack3 = new System.Windows.Forms.TextBox();
			this.tbPrefixTrack3 = new System.Windows.Forms.TextBox();
			this.label2 = new System.Windows.Forms.Label();
			this.label3 = new System.Windows.Forms.Label();
			this.buttonCancel = new System.Windows.Forms.Button();
			this.buttonAccept = new System.Windows.Forms.Button();
			this.buttonDefaults = new System.Windows.Forms.Button();
			this.labelTk1Comment = new System.Windows.Forms.Label();
			this.labelTk2Comment = new System.Windows.Forms.Label();
			this.labelTk3Comment = new System.Windows.Forms.Label();
			this.labelTk3CommentAAMVA = new System.Windows.Forms.Label();
			this.checkEnableAAMVA = new System.Windows.Forms.CheckBox();
			this.SuspendLayout();
			// 
			// checkEnableTrack1
			// 
			this.checkEnableTrack1.Checked = true;
			this.checkEnableTrack1.CheckState = System.Windows.Forms.CheckState.Checked;
			resources.ApplyResources(this.checkEnableTrack1, "checkEnableTrack1");
			this.checkEnableTrack1.Name = "checkEnableTrack1";
			this.checkEnableTrack1.Click += new System.EventHandler(this.checkEnableTrack1_Click);
			// 
			// checkEnableTrack2
			// 
			this.checkEnableTrack2.Checked = true;
			this.checkEnableTrack2.CheckState = System.Windows.Forms.CheckState.Checked;
			resources.ApplyResources(this.checkEnableTrack2, "checkEnableTrack2");
			this.checkEnableTrack2.Name = "checkEnableTrack2";
			this.checkEnableTrack2.Click += new System.EventHandler(this.checkEnableTrack2_Click);
			// 
			// checkEnableTrack3
			// 
			this.checkEnableTrack3.Checked = true;
			this.checkEnableTrack3.CheckState = System.Windows.Forms.CheckState.Checked;
			resources.ApplyResources(this.checkEnableTrack3, "checkEnableTrack3");
			this.checkEnableTrack3.Name = "checkEnableTrack3";
			this.checkEnableTrack3.Click += new System.EventHandler(this.checkEnableTrack3_Click);
			// 
			// label1
			// 
			resources.ApplyResources(this.label1, "label1");
			this.label1.Name = "label1";
			// 
			// comboBoxEncoderBrand
			// 
			this.comboBoxEncoderBrand.Items.AddRange(new object[] {
            resources.GetString("comboBoxEncoderBrand.Items"),
            resources.GetString("comboBoxEncoderBrand.Items1"),
            resources.GetString("comboBoxEncoderBrand.Items2"),
            resources.GetString("comboBoxEncoderBrand.Items3"),
            resources.GetString("comboBoxEncoderBrand.Items4"),
            resources.GetString("comboBoxEncoderBrand.Items5")});
			resources.ApplyResources(this.comboBoxEncoderBrand, "comboBoxEncoderBrand");
			this.comboBoxEncoderBrand.Name = "comboBoxEncoderBrand";
			this.comboBoxEncoderBrand.SelectedIndexChanged += new System.EventHandler(this.comboBoxEncoderBrand_SelectedIndexChanged);
			// 
			// tbPrefixTrack1
			// 
			resources.ApplyResources(this.tbPrefixTrack1, "tbPrefixTrack1");
			this.tbPrefixTrack1.Name = "tbPrefixTrack1";
			// 
			// tbSuffixTrack1
			// 
			resources.ApplyResources(this.tbSuffixTrack1, "tbSuffixTrack1");
			this.tbSuffixTrack1.Name = "tbSuffixTrack1";
			// 
			// tbSuffixTrack2
			// 
			resources.ApplyResources(this.tbSuffixTrack2, "tbSuffixTrack2");
			this.tbSuffixTrack2.Name = "tbSuffixTrack2";
			// 
			// tbPrefixTrack2
			// 
			resources.ApplyResources(this.tbPrefixTrack2, "tbPrefixTrack2");
			this.tbPrefixTrack2.Name = "tbPrefixTrack2";
			// 
			// tbSuffixTrack3
			// 
			resources.ApplyResources(this.tbSuffixTrack3, "tbSuffixTrack3");
			this.tbSuffixTrack3.Name = "tbSuffixTrack3";
			// 
			// tbPrefixTrack3
			// 
			resources.ApplyResources(this.tbPrefixTrack3, "tbPrefixTrack3");
			this.tbPrefixTrack3.Name = "tbPrefixTrack3";
			// 
			// label2
			// 
			resources.ApplyResources(this.label2, "label2");
			this.label2.Name = "label2";
			// 
			// label3
			// 
			resources.ApplyResources(this.label3, "label3");
			this.label3.Name = "label3";
			// 
			// buttonCancel
			// 
			this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			resources.ApplyResources(this.buttonCancel, "buttonCancel");
			this.buttonCancel.Name = "buttonCancel";
			// 
			// buttonAccept
			// 
			this.buttonAccept.DialogResult = System.Windows.Forms.DialogResult.OK;
			resources.ApplyResources(this.buttonAccept, "buttonAccept");
			this.buttonAccept.Name = "buttonAccept";
			// 
			// buttonDefaults
			// 
			resources.ApplyResources(this.buttonDefaults, "buttonDefaults");
			this.buttonDefaults.Name = "buttonDefaults";
			this.buttonDefaults.Click += new System.EventHandler(this.buttonDefaults_Click);
			// 
			// labelTk1Comment
			// 
			resources.ApplyResources(this.labelTk1Comment, "labelTk1Comment");
			this.labelTk1Comment.Name = "labelTk1Comment";
			// 
			// labelTk2Comment
			// 
			resources.ApplyResources(this.labelTk2Comment, "labelTk2Comment");
			this.labelTk2Comment.Name = "labelTk2Comment";
			// 
			// labelTk3Comment
			// 
			resources.ApplyResources(this.labelTk3Comment, "labelTk3Comment");
			this.labelTk3Comment.Name = "labelTk3Comment";
			// 
			// labelTk3CommentAAMVA
			// 
			resources.ApplyResources(this.labelTk3CommentAAMVA, "labelTk3CommentAAMVA");
			this.labelTk3CommentAAMVA.Name = "labelTk3CommentAAMVA";
			// 
			// checkEnableAAMVA
			// 
			this.checkEnableAAMVA.Checked = true;
			this.checkEnableAAMVA.CheckState = System.Windows.Forms.CheckState.Checked;
			resources.ApplyResources(this.checkEnableAAMVA, "checkEnableAAMVA");
			this.checkEnableAAMVA.Name = "checkEnableAAMVA";
			// 
			// EncoderProperties
			// 
			this.AcceptButton = this.buttonAccept;
			resources.ApplyResources(this, "$this");
			this.CancelButton = this.buttonCancel;
			this.Controls.Add(this.checkEnableAAMVA);
			this.Controls.Add(this.labelTk3CommentAAMVA);
			this.Controls.Add(this.labelTk3Comment);
			this.Controls.Add(this.labelTk2Comment);
			this.Controls.Add(this.labelTk1Comment);
			this.Controls.Add(this.buttonDefaults);
			this.Controls.Add(this.buttonCancel);
			this.Controls.Add(this.buttonAccept);
			this.Controls.Add(this.label3);
			this.Controls.Add(this.label2);
			this.Controls.Add(this.tbSuffixTrack3);
			this.Controls.Add(this.tbPrefixTrack3);
			this.Controls.Add(this.tbSuffixTrack2);
			this.Controls.Add(this.tbPrefixTrack2);
			this.Controls.Add(this.tbSuffixTrack1);
			this.Controls.Add(this.tbPrefixTrack1);
			this.Controls.Add(this.checkEnableTrack3);
			this.Controls.Add(this.checkEnableTrack2);
			this.Controls.Add(this.checkEnableTrack1);
			this.Controls.Add(this.comboBoxEncoderBrand);
			this.Controls.Add(this.label1);
			this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
			this.MaximizeBox = false;
			this.MinimizeBox = false;
			this.Name = "EncoderProperties";
			this.ShowInTaskbar = false;
			this.ResumeLayout(false);
			this.PerformLayout();

		}
		#endregion

		private void AssignVisibilities()
		{
			this.tbPrefixTrack1.Visible =
                this.tbSuffixTrack1.Visible = 
                this.labelTk1Comment.Visible = (this.checkEnableTrack1.Checked);
			this.tbPrefixTrack2.Visible = 
    			this.tbSuffixTrack2.Visible = 
                this.labelTk2Comment.Visible = (this.checkEnableTrack2.Checked);
			this.tbPrefixTrack3.Visible = 
    			this.tbSuffixTrack3.Visible = 
                this.checkEnableAAMVA.Visible =
                this.labelTk3Comment.Visible =
                this.labelTk3CommentAAMVA.Visible = (this.checkEnableTrack3.Checked);
		}

        public void MoveDataFromControls(ref DCSDEV.PrintProperties.PrinterTypeDatum bcDatum)
		{
			bcDatum.m_strMagEncoderDevice	= this.comboBoxEncoderBrand.Text;
            bcDatum.m_IfAAMVA               = this.checkEnableAAMVA.Checked;
			bcDatum.m_IfMagTrack1			= this.checkEnableTrack1.Checked;
			bcDatum.m_strPrefixTrack1		= this.tbPrefixTrack1.Text;
			bcDatum.m_strSuffixTrack1		= this.tbSuffixTrack1.Text;
			bcDatum.m_IfMagTrack2			= this.checkEnableTrack2.Checked;
			bcDatum.m_strPrefixTrack2		= this.tbPrefixTrack2.Text;
			bcDatum.m_strSuffixTrack2		= this.tbSuffixTrack2.Text;
			bcDatum.m_IfMagTrack3			= this.checkEnableTrack3.Checked;
			bcDatum.m_strPrefixTrack3		= this.tbPrefixTrack3.Text;
			bcDatum.m_strSuffixTrack3		= this.tbSuffixTrack3.Text;
		}

        public void MoveDataToControls(DCSDEV.PrintProperties.PrinterTypeDatum bcDatum)
		{
			this.comboBoxEncoderBrand.Text	= bcDatum.m_strMagEncoderDevice;
            this.checkEnableAAMVA.Checked   = bcDatum.m_IfAAMVA;
			this.checkEnableTrack1.Checked	= bcDatum.m_IfMagTrack1;
			this.tbPrefixTrack1.Text		= bcDatum.m_strPrefixTrack1;
			this.tbSuffixTrack1.Text		= bcDatum.m_strSuffixTrack1;
			this.checkEnableTrack2.Checked	= bcDatum.m_IfMagTrack2;
			this.tbPrefixTrack2.Text		= bcDatum.m_strPrefixTrack2;
			this.tbSuffixTrack2.Text		= bcDatum.m_strSuffixTrack2;
			this.checkEnableTrack3.Checked	= bcDatum.m_IfMagTrack3;
			this.tbPrefixTrack3.Text		= bcDatum.m_strPrefixTrack3;
			this.tbSuffixTrack3.Text		= bcDatum.m_strSuffixTrack3;
		}
		private void SetDefaultStrings(bool bIfSettingOther)
		{
			if (this.comboBoxEncoderBrand.Text == "Atlantec")
			{
				this.tbPrefixTrack1.Text = "~1=";
				this.tbSuffixTrack1.Text = "";
				this.tbPrefixTrack2.Text = "~2=";
				this.tbSuffixTrack2.Text = "";
				this.tbPrefixTrack3.Text = "~3=";
				this.tbSuffixTrack3.Text = "";
			}
			else if (this.comboBoxEncoderBrand.Text == "Eltron-Zebra")
			{
				this.tbPrefixTrack1.Text = "~1";
				this.tbSuffixTrack1.Text = "";
				this.tbPrefixTrack2.Text = "~2";
				this.tbSuffixTrack2.Text = "";
				this.tbPrefixTrack3.Text = "~3";
				this.tbSuffixTrack3.Text = "";
			}
			else if (this.comboBoxEncoderBrand.Text == "Fargo")
			{
				this.tbPrefixTrack1.Text = "~1%";
				this.tbSuffixTrack1.Text = "?";
				this.tbPrefixTrack2.Text = "~2;";
				this.tbSuffixTrack2.Text = "?";
				this.tbPrefixTrack3.Text = "~3;";
				this.tbSuffixTrack3.Text = "?";
			}
			else if (this.comboBoxEncoderBrand.Text == "Nisca")
			{
				this.tbPrefixTrack1.Text = "~1;";
				this.tbSuffixTrack1.Text = "";
				this.tbPrefixTrack2.Text = "~2;";
				this.tbSuffixTrack2.Text = "";
				this.tbPrefixTrack3.Text = "~3;";
				this.tbSuffixTrack3.Text = "";
			}
            else if (this.comboBoxEncoderBrand.Text == "XID")
            {
                this.tbPrefixTrack1.Text = "~1%";
                this.tbSuffixTrack1.Text = "?";
                this.tbPrefixTrack2.Text = "~2;";
                this.tbSuffixTrack2.Text = "?";
                this.tbPrefixTrack3.Text = "~3;";
                this.tbSuffixTrack3.Text = "?";
            }
            else if (bIfSettingOther && this.comboBoxEncoderBrand.Text == "Other")
			{
				this.tbPrefixTrack1.Text = "";
				this.tbSuffixTrack1.Text = "";
				this.tbPrefixTrack2.Text = "";
				this.tbSuffixTrack2.Text = "";
				this.tbPrefixTrack3.Text = "";
				this.tbSuffixTrack3.Text = "";
			}
		}
		private void buttonDefaults_Click(object sender, System.EventArgs e)
		{
			SetDefaultStrings(false);
		}

		private void comboBoxEncoderBrand_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			SetDefaultStrings(false);
		}

		private void checkEnableTrack1_Click(object sender, System.EventArgs e)
		{
			this.AssignVisibilities();
		}

		private void checkEnableTrack2_Click(object sender, System.EventArgs e)
		{
			this.AssignVisibilities();
		}

		private void checkEnableTrack3_Click(object sender, System.EventArgs e)
		{
			this.AssignVisibilities();
		}
	}
}
