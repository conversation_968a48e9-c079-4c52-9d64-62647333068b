
using System;
using System.Drawing;
using System.Drawing.Printing;

using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;
using System.Runtime.InteropServices;
using System.Runtime.Serialization.Formatters.Binary;
using System.Runtime.Serialization;

using DCSDEV;
using DCSDEV.DCSDesign;

namespace DCSDEV.DCSDesigner
{
	/// <summary>
	///    Summary description for Form1.
	/// </summary>
    public class DCSDesignerMain : System.Windows.Forms.Form
	{

		[DllImport("User32.dll")]
		static extern IntPtr SetForegroundWindow(IntPtr hWnd);

		/// <summary> 
		///    Required designer variable
		/// </summary>
		/// 

		private System.Drawing.Printing.PrinterSettings m_printersettingsChosen = null;
		internal ArrayList m_AllDBFieldNames = null;
        internal DCSDEV.PrintProperties.PrinterTypeArray m_printertypeArray = null;

		private DCSDEV.ParameterStore m_ps; 
		private ArrayList m_ClipBoardObjects = new ArrayList();
		private Point m_pointLastInsert = new Point(0,0);

		internal bool m_bTabAlignOn = false;
		internal bool m_bGridSnapOn = false;
		internal bool m_bAutoAlign = false;
		
		private bool m_bAlignToMax = true;
		private bool m_bAlignToMin = false;
		private bool m_bSizeToMax = true;
		private bool m_bSizeToMin = false;

		private System.ComponentModel.IContainer components;
		private System.Windows.Forms.HelpProvider helpProvider1;
		private System.Windows.Forms.StatusBar statusBar1;
		private System.Windows.Forms.ImageList imageList1;
		private System.Windows.Forms.ToolBarButton buttonHelp;
		private System.Windows.Forms.ToolBarButton buttonPrint;
		private System.Windows.Forms.ToolBarButton buttonPreview;
		private System.Windows.Forms.ToolBarButton buttonSave;
		private System.Windows.Forms.ToolBarButton openButton;
		private System.Windows.Forms.ToolBarButton newButton;
		private System.Windows.Forms.ToolBar toolBar1;
		private System.Drawing.Printing.PrintDocument printDoc;
		private System.Windows.Forms.MenuItem menuItemAbout;
		private System.Windows.Forms.MenuItem menuItem37;
		private System.Windows.Forms.MenuItem menuItemHelpTopics;
		private System.Windows.Forms.MenuItem menuItemTile;
		private System.Windows.Forms.MenuItem menuItemCascade;
		private System.Windows.Forms.MenuItem menuItemStatusbar;
		private System.Windows.Forms.MenuItem menuItemToolbar;
		private System.Windows.Forms.MenuItem menuItemExit;
		private System.Windows.Forms.MenuItem menuItem16;
		private System.Windows.Forms.MenuItem menuItemPreview;
		private System.Windows.Forms.MenuItem menuItemPrint;
		private System.Windows.Forms.MenuItem menuItem12;
		private System.Windows.Forms.MenuItem menuItemSaveAs;
		private System.Windows.Forms.MenuItem menuItemSave;
		private System.Windows.Forms.MenuItem menuItemClose;
		private System.Windows.Forms.MenuItem menuItemOpen;
		private System.Windows.Forms.MenuItem menuItemNew;
		private System.Windows.Forms.MenuItem menuItemHelp;
		private System.Windows.Forms.MenuItem menuItemWindow;
		private System.Windows.Forms.MenuItem menuItemView;
		private System.Windows.Forms.MenuItem menuItemEdit;
		private System.Windows.Forms.MenuItem menuItemFile;
		private System.Windows.Forms.MdiClient mdiClient1;
		private System.Windows.Forms.MenuItem menuItemInsert;
		private System.Windows.Forms.MenuItem menuItemInsertText;
		private System.Windows.Forms.MenuItem menuItemInsertImage;
		private System.Windows.Forms.MenuItem menuItemInsertPortrait;
		private System.Windows.Forms.MenuItem menuItemInsertSig;
		private System.Windows.Forms.MenuItem menuItemInsertFinger;
		private System.Windows.Forms.MenuItem menuItemInsertBarcode;
		private System.Windows.Forms.MenuItem menuItemInsertICAO;
		private System.Windows.Forms.MenuItem menuItemFlipSide;
		private System.Windows.Forms.MenuItem menuItemEditBadgeDesign;
		private System.Windows.Forms.MenuItem menuItemEditObjProperties;
		private System.Windows.Forms.MenuItem menuItem1;
		private System.Windows.Forms.MenuItem menuItemEditDelete;
		private System.Windows.Forms.ToolBarButton buttonDelete;
		private System.Windows.Forms.ToolBarButton buttonFlip;
		private System.Windows.Forms.ToolBarButton buttonUndo;
		private System.Windows.Forms.ToolBarButton buttonRedo;
		private System.Windows.Forms.MenuItem menuItemEditFront;
		private System.Windows.Forms.MenuItem menuItemEditBack;
		private System.Windows.Forms.MenuItem menuItemViewZoom;
		private System.Windows.Forms.MenuItem menuItemZoom50;
		private System.Windows.Forms.MenuItem menuItemZoom75;
		private System.Windows.Forms.MenuItem menuItemZoom200;
		private System.Windows.Forms.MenuItem menuItemZoom300;
		private System.Windows.Forms.MenuItem menuItemZoom400;
		private System.Windows.Forms.MenuItem menuItemZoom100;
		private System.Windows.Forms.MenuItem menuItemZoom150;
		private System.Windows.Forms.MenuItem menuItemEditCopy;
		private System.Windows.Forms.MenuItem menuItemEditPaste;
		private System.Windows.Forms.MenuItem menuItemEditCut;
		private System.Windows.Forms.ToolBarButton buttonEditObjProp;
		private System.Windows.Forms.ToolBarButton buttonInsertText;
		private System.Windows.Forms.ToolBarButton buttonInsertPortrait;
		private System.Windows.Forms.ToolBarButton buttonInsertImage;
		private System.Windows.Forms.ToolBarButton buttonInsertICAO;
		private System.Windows.Forms.ToolBarButton buttonInsertBarcode;
		private System.Windows.Forms.ToolBarButton buttonCopy;
		private System.Windows.Forms.ToolBarButton buttonCut;
		private System.Windows.Forms.ToolBarButton buttonPaste;
		private System.Windows.Forms.MenuItem menuItemEditPrinterTypes;
		private System.Windows.Forms.MenuItem menuItem2;
		private System.Windows.Forms.MenuItem menuItemEditUndo;
		private System.Windows.Forms.MenuItem menuItemEditRedo;
		private System.Windows.Forms.MenuItem menuItemEditSides;
		private System.Windows.Forms.MenuItem menuItem3;
		private System.Windows.Forms.MenuItem menuItem4;
		private System.Windows.Forms.MenuItem menuItemInsertGraphicBlock;
		private System.Windows.Forms.MenuItem menuItemInsertBarcode2D;
		private System.Windows.Forms.MenuItem menuItemSetGrid;
		private MenuItem menuItemAlignment;
		private MenuItem menuItemSnapToGrid;
		private MenuItem menuItemAlignObj;
		private MenuItem menuItemAlignLeft;
		private MenuItem menuItemAlignRight;
		private MenuItem menuItemAlignTop;
		private MenuItem menuItemAlignBottom;
		private MenuItem menuItemAlignToMax;
		private MenuItem menuItemAlignToMin;
		private MenuItem menuItemSize;
		private MenuItem menuItemSizeHeight;
		private MenuItem menuItemSizeWidth;
		private MenuItem menuItemSizeBoth;
		private MenuItem menuItemSizeToMax;
		private MenuItem menuItemSizeToMin;
		private MenuItem menuItemAutoAlign;
		private MenuItem menuItemSnapToGridLines;
		private MenuItem menuItem7;
		private MenuItem menuItem8;
		private MenuItem menuItem5;
		private MenuItem menuItem6;
		private MenuItem menuItemDefaultFonts;
		private System.Windows.Forms.MainMenu mainMenu1;

		/// <summary>
		///   Constructor
		/// </summary>
		public DCSDesignerMain()
		{        
			//
			// Required for Win Form Designer support
			//
			InitializeComponent();	

			m_ps = new DCSDEV.ParameterStore("DCSDesigner");

			m_AllDBFieldNames = new ArrayList();
			bool bRet = this.ReadAllDBFieldNames();

            m_printertypeArray = new DCSDEV.PrintProperties.PrinterTypeArray();
            m_printertypeArray.LoadPrinterTypeArray();

            string strDesignName = m_ps.GetStringParameter("CurrentDesignName", "");
			if (strDesignName != "")
			{
				string strFileName = DCSDEV.DCSDesignDataAccess.ExpandDesignName(strDesignName, true);
				if (strFileName != null) CreateDocument(strDesignName);
			}
			else EnableGUIItems();

			Rectangle rect = m_ps.GetRectParameter("CurrentBounds", this.Bounds);
			int x = rect.X;
			int y = rect.Y;
			int width = rect.Width;
			int height = rect.Height;
			Rectangle rectScreen = System.Windows.Forms.Screen.GetBounds(this);
			if (x<0) x = 0;
			if (y<0) y = 0;
			if (width < 100 || x+width > rectScreen.Width) {width = rectScreen.Width; x=0;}
			if (height < 100 || y+height > rectScreen.Height) {height = rectScreen.Height; y=0;}
			this.SetBounds(x,y,width,height);

			//get grid parameters
            if (DCSLicensing.IsLicensedOK(LicensedFeatures.AdvancedDesignTools))
            {
                this.m_bGridSnapOn = m_ps.GetBoolParameter("GridOn", false);
                this.m_bTabAlignOn = m_ps.GetBoolParameter("TabAlignOn", false);
                this.m_bAutoAlign = m_ps.GetBoolParameter("AutoAlign", true);
                if (this.m_bAutoAlign) this.m_bTabAlignOn = this.m_bGridSnapOn = false;
            }
            else
            {
                this.menuItemAutoAlign.Enabled = false;
                this.menuItemSnapToGridLines.Enabled = false;
                this.menuItemSnapToGrid.Enabled = false;
                this.menuItemSetGrid.Enabled = false;
            }

			this.menuItemSnapToGridLines.Checked = this.m_bTabAlignOn;
			this.menuItemSnapToGrid.Checked = this.m_bGridSnapOn;
			this.menuItemAutoAlign.Checked = this.m_bAutoAlign;

			// assure empty value is program does not end normally
			m_ps.WriteStringParameter("CurrentDesignName", "");
		}

		/// <summary>
		///    Clean up any resources being used
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if (components != null) 
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		///    Required method for Designer support - do not modify
		///    the contents of this method with the code editor
		/// </summary>
		private void InitializeComponent()
		{
			this.components = new System.ComponentModel.Container();
			System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(DCSDesignerMain));
			this.openButton = new System.Windows.Forms.ToolBarButton();
			this.printDoc = new System.Drawing.Printing.PrintDocument();
			this.menuItemOpen = new System.Windows.Forms.MenuItem();
			this.menuItemClose = new System.Windows.Forms.MenuItem();
			this.menuItemView = new System.Windows.Forms.MenuItem();
			this.menuItemToolbar = new System.Windows.Forms.MenuItem();
			this.menuItemStatusbar = new System.Windows.Forms.MenuItem();
			this.menuItemFlipSide = new System.Windows.Forms.MenuItem();
			this.menuItemViewZoom = new System.Windows.Forms.MenuItem();
			this.menuItemZoom50 = new System.Windows.Forms.MenuItem();
			this.menuItemZoom75 = new System.Windows.Forms.MenuItem();
			this.menuItemZoom100 = new System.Windows.Forms.MenuItem();
			this.menuItemZoom150 = new System.Windows.Forms.MenuItem();
			this.menuItemZoom200 = new System.Windows.Forms.MenuItem();
			this.menuItemZoom300 = new System.Windows.Forms.MenuItem();
			this.menuItemZoom400 = new System.Windows.Forms.MenuItem();
			this.menuItemSetGrid = new System.Windows.Forms.MenuItem();
			this.buttonSave = new System.Windows.Forms.ToolBarButton();
			this.menuItemHelp = new System.Windows.Forms.MenuItem();
			this.menuItemHelpTopics = new System.Windows.Forms.MenuItem();
			this.menuItem37 = new System.Windows.Forms.MenuItem();
			this.menuItemAbout = new System.Windows.Forms.MenuItem();
			this.menuItemNew = new System.Windows.Forms.MenuItem();
			this.menuItemFile = new System.Windows.Forms.MenuItem();
			this.menuItemSave = new System.Windows.Forms.MenuItem();
			this.menuItemSaveAs = new System.Windows.Forms.MenuItem();
			this.menuItem12 = new System.Windows.Forms.MenuItem();
			this.menuItemEditPrinterTypes = new System.Windows.Forms.MenuItem();
			this.menuItemPrint = new System.Windows.Forms.MenuItem();
			this.menuItemPreview = new System.Windows.Forms.MenuItem();
			this.menuItem3 = new System.Windows.Forms.MenuItem();
			this.menuItemExit = new System.Windows.Forms.MenuItem();
			this.menuItem16 = new System.Windows.Forms.MenuItem();
			this.menuItemEdit = new System.Windows.Forms.MenuItem();
			this.menuItemEditBadgeDesign = new System.Windows.Forms.MenuItem();
			this.menuItemEditSides = new System.Windows.Forms.MenuItem();
			this.menuItemEditObjProperties = new System.Windows.Forms.MenuItem();
			this.menuItemDefaultFonts = new System.Windows.Forms.MenuItem();
			this.menuItem1 = new System.Windows.Forms.MenuItem();
			this.menuItemEditDelete = new System.Windows.Forms.MenuItem();
			this.menuItemEditFront = new System.Windows.Forms.MenuItem();
			this.menuItemEditBack = new System.Windows.Forms.MenuItem();
			this.menuItemEditCut = new System.Windows.Forms.MenuItem();
			this.menuItemEditCopy = new System.Windows.Forms.MenuItem();
			this.menuItemEditPaste = new System.Windows.Forms.MenuItem();
			this.menuItem2 = new System.Windows.Forms.MenuItem();
			this.menuItemEditUndo = new System.Windows.Forms.MenuItem();
			this.menuItemEditRedo = new System.Windows.Forms.MenuItem();
			this.buttonPrint = new System.Windows.Forms.ToolBarButton();
			this.toolBar1 = new System.Windows.Forms.ToolBar();
			this.newButton = new System.Windows.Forms.ToolBarButton();
			this.buttonPreview = new System.Windows.Forms.ToolBarButton();
			this.buttonFlip = new System.Windows.Forms.ToolBarButton();
			this.buttonUndo = new System.Windows.Forms.ToolBarButton();
			this.buttonRedo = new System.Windows.Forms.ToolBarButton();
			this.buttonCopy = new System.Windows.Forms.ToolBarButton();
			this.buttonCut = new System.Windows.Forms.ToolBarButton();
			this.buttonPaste = new System.Windows.Forms.ToolBarButton();
			this.buttonDelete = new System.Windows.Forms.ToolBarButton();
			this.buttonInsertText = new System.Windows.Forms.ToolBarButton();
			this.buttonInsertPortrait = new System.Windows.Forms.ToolBarButton();
			this.buttonInsertImage = new System.Windows.Forms.ToolBarButton();
			this.buttonInsertBarcode = new System.Windows.Forms.ToolBarButton();
			this.buttonInsertICAO = new System.Windows.Forms.ToolBarButton();
			this.buttonEditObjProp = new System.Windows.Forms.ToolBarButton();
			this.buttonHelp = new System.Windows.Forms.ToolBarButton();
			this.imageList1 = new System.Windows.Forms.ImageList(this.components);
			this.helpProvider1 = new System.Windows.Forms.HelpProvider();
			this.mainMenu1 = new System.Windows.Forms.MainMenu(this.components);
			this.menuItemInsert = new System.Windows.Forms.MenuItem();
			this.menuItemInsertText = new System.Windows.Forms.MenuItem();
			this.menuItemInsertImage = new System.Windows.Forms.MenuItem();
			this.menuItemInsertPortrait = new System.Windows.Forms.MenuItem();
			this.menuItemInsertSig = new System.Windows.Forms.MenuItem();
			this.menuItemInsertFinger = new System.Windows.Forms.MenuItem();
			this.menuItemInsertBarcode = new System.Windows.Forms.MenuItem();
			this.menuItemInsertBarcode2D = new System.Windows.Forms.MenuItem();
			this.menuItemInsertICAO = new System.Windows.Forms.MenuItem();
			this.menuItem4 = new System.Windows.Forms.MenuItem();
			this.menuItemInsertGraphicBlock = new System.Windows.Forms.MenuItem();
			this.menuItemAlignment = new System.Windows.Forms.MenuItem();
			this.menuItemAlignObj = new System.Windows.Forms.MenuItem();
			this.menuItemAlignLeft = new System.Windows.Forms.MenuItem();
			this.menuItemAlignRight = new System.Windows.Forms.MenuItem();
			this.menuItemAlignTop = new System.Windows.Forms.MenuItem();
			this.menuItemAlignBottom = new System.Windows.Forms.MenuItem();
			this.menuItem7 = new System.Windows.Forms.MenuItem();
			this.menuItemAlignToMax = new System.Windows.Forms.MenuItem();
			this.menuItemAlignToMin = new System.Windows.Forms.MenuItem();
			this.menuItemSize = new System.Windows.Forms.MenuItem();
			this.menuItemSizeHeight = new System.Windows.Forms.MenuItem();
			this.menuItemSizeWidth = new System.Windows.Forms.MenuItem();
			this.menuItemSizeBoth = new System.Windows.Forms.MenuItem();
			this.menuItem8 = new System.Windows.Forms.MenuItem();
			this.menuItemSizeToMax = new System.Windows.Forms.MenuItem();
			this.menuItemSizeToMin = new System.Windows.Forms.MenuItem();
			this.menuItem5 = new System.Windows.Forms.MenuItem();
			this.menuItemAutoAlign = new System.Windows.Forms.MenuItem();
			this.menuItemSnapToGrid = new System.Windows.Forms.MenuItem();
			this.menuItemSnapToGridLines = new System.Windows.Forms.MenuItem();
			this.menuItem6 = new System.Windows.Forms.MenuItem();
			this.menuItemWindow = new System.Windows.Forms.MenuItem();
			this.menuItemCascade = new System.Windows.Forms.MenuItem();
			this.menuItemTile = new System.Windows.Forms.MenuItem();
			this.statusBar1 = new System.Windows.Forms.StatusBar();
			this.mdiClient1 = new System.Windows.Forms.MdiClient();
			this.SuspendLayout();
			// 
			// openButton
			// 
			resources.ApplyResources(this.openButton, "openButton");
			this.openButton.Name = "openButton";
			// 
			// printDoc
			// 
			this.printDoc.PrintPage += new System.Drawing.Printing.PrintPageEventHandler(this.DCSDesignerPrintPage);
			// 
			// menuItemOpen
			// 
			this.menuItemOpen.Index = 1;
			resources.ApplyResources(this.menuItemOpen, "menuItemOpen");
			this.menuItemOpen.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItemClose
			// 
			this.menuItemClose.Index = 2;
			resources.ApplyResources(this.menuItemClose, "menuItemClose");
			this.menuItemClose.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItemView
			// 
			this.menuItemView.Index = 3;
			this.menuItemView.MenuItems.AddRange(new System.Windows.Forms.MenuItem[] {
            this.menuItemToolbar,
            this.menuItemStatusbar,
            this.menuItemFlipSide,
            this.menuItemViewZoom});
			resources.ApplyResources(this.menuItemView, "menuItemView");
			// 
			// menuItemToolbar
			// 
			this.menuItemToolbar.Checked = true;
			this.menuItemToolbar.Index = 0;
			resources.ApplyResources(this.menuItemToolbar, "menuItemToolbar");
			this.menuItemToolbar.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItemStatusbar
			// 
			this.menuItemStatusbar.Checked = true;
			this.menuItemStatusbar.Index = 1;
			resources.ApplyResources(this.menuItemStatusbar, "menuItemStatusbar");
			this.menuItemStatusbar.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItemFlipSide
			// 
			this.menuItemFlipSide.Index = 2;
			resources.ApplyResources(this.menuItemFlipSide, "menuItemFlipSide");
			this.menuItemFlipSide.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItemViewZoom
			// 
			this.menuItemViewZoom.Index = 3;
			this.menuItemViewZoom.MenuItems.AddRange(new System.Windows.Forms.MenuItem[] {
            this.menuItemZoom50,
            this.menuItemZoom75,
            this.menuItemZoom100,
            this.menuItemZoom150,
            this.menuItemZoom200,
            this.menuItemZoom300,
            this.menuItemZoom400});
			resources.ApplyResources(this.menuItemViewZoom, "menuItemViewZoom");
			// 
			// menuItemZoom50
			// 
			this.menuItemZoom50.Index = 0;
			resources.ApplyResources(this.menuItemZoom50, "menuItemZoom50");
			this.menuItemZoom50.Click += new System.EventHandler(this.MenuItemZoomClick);
			// 
			// menuItemZoom75
			// 
			this.menuItemZoom75.Index = 1;
			resources.ApplyResources(this.menuItemZoom75, "menuItemZoom75");
			this.menuItemZoom75.Click += new System.EventHandler(this.MenuItemZoomClick);
			// 
			// menuItemZoom100
			// 
			this.menuItemZoom100.Checked = true;
			this.menuItemZoom100.Index = 2;
			resources.ApplyResources(this.menuItemZoom100, "menuItemZoom100");
			this.menuItemZoom100.Click += new System.EventHandler(this.MenuItemZoomClick);
			// 
			// menuItemZoom150
			// 
			this.menuItemZoom150.Index = 3;
			resources.ApplyResources(this.menuItemZoom150, "menuItemZoom150");
			this.menuItemZoom150.Click += new System.EventHandler(this.MenuItemZoomClick);
			// 
			// menuItemZoom200
			// 
			this.menuItemZoom200.Index = 4;
			resources.ApplyResources(this.menuItemZoom200, "menuItemZoom200");
			this.menuItemZoom200.Click += new System.EventHandler(this.MenuItemZoomClick);
			// 
			// menuItemZoom300
			// 
			this.menuItemZoom300.Index = 5;
			resources.ApplyResources(this.menuItemZoom300, "menuItemZoom300");
			this.menuItemZoom300.Click += new System.EventHandler(this.MenuItemZoomClick);
			// 
			// menuItemZoom400
			// 
			this.menuItemZoom400.Index = 6;
			resources.ApplyResources(this.menuItemZoom400, "menuItemZoom400");
			this.menuItemZoom400.Click += new System.EventHandler(this.MenuItemZoomClick);
			// 
			// menuItemSetGrid
			// 
			this.menuItemSetGrid.Index = 7;
			resources.ApplyResources(this.menuItemSetGrid, "menuItemSetGrid");
			this.menuItemSetGrid.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// buttonSave
			// 
			resources.ApplyResources(this.buttonSave, "buttonSave");
			this.buttonSave.Name = "buttonSave";
			// 
			// menuItemHelp
			// 
			this.menuItemHelp.Index = 6;
			this.menuItemHelp.MenuItems.AddRange(new System.Windows.Forms.MenuItem[] {
            this.menuItemHelpTopics,
            this.menuItem37,
            this.menuItemAbout});
			resources.ApplyResources(this.menuItemHelp, "menuItemHelp");
			// 
			// menuItemHelpTopics
			// 
			this.menuItemHelpTopics.Index = 0;
			resources.ApplyResources(this.menuItemHelpTopics, "menuItemHelpTopics");
			this.menuItemHelpTopics.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItem37
			// 
			this.menuItem37.Index = 1;
			resources.ApplyResources(this.menuItem37, "menuItem37");
			// 
			// menuItemAbout
			// 
			this.menuItemAbout.Index = 2;
			resources.ApplyResources(this.menuItemAbout, "menuItemAbout");
			this.menuItemAbout.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItemNew
			// 
			this.menuItemNew.Index = 0;
			resources.ApplyResources(this.menuItemNew, "menuItemNew");
			this.menuItemNew.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItemFile
			// 
			this.menuItemFile.Index = 0;
			this.menuItemFile.MenuItems.AddRange(new System.Windows.Forms.MenuItem[] {
            this.menuItemNew,
            this.menuItemOpen,
            this.menuItemClose,
            this.menuItemSave,
            this.menuItemSaveAs,
            this.menuItem12,
            this.menuItemEditPrinterTypes,
            this.menuItemPrint,
            this.menuItemPreview,
            this.menuItem3,
            this.menuItemExit});
			resources.ApplyResources(this.menuItemFile, "menuItemFile");
			// 
			// menuItemSave
			// 
			this.menuItemSave.Index = 3;
			resources.ApplyResources(this.menuItemSave, "menuItemSave");
			this.menuItemSave.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItemSaveAs
			// 
			this.menuItemSaveAs.Index = 4;
			resources.ApplyResources(this.menuItemSaveAs, "menuItemSaveAs");
			this.menuItemSaveAs.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItem12
			// 
			this.menuItem12.Index = 5;
			resources.ApplyResources(this.menuItem12, "menuItem12");
			// 
			// menuItemEditPrinterTypes
			// 
			this.menuItemEditPrinterTypes.Index = 6;
			resources.ApplyResources(this.menuItemEditPrinterTypes, "menuItemEditPrinterTypes");
			this.menuItemEditPrinterTypes.Click += new System.EventHandler(this.menuItemEditPrinterTypes_Click);
			// 
			// menuItemPrint
			// 
			this.menuItemPrint.Index = 7;
			resources.ApplyResources(this.menuItemPrint, "menuItemPrint");
			this.menuItemPrint.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItemPreview
			// 
			this.menuItemPreview.Index = 8;
			resources.ApplyResources(this.menuItemPreview, "menuItemPreview");
			this.menuItemPreview.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItem3
			// 
			this.menuItem3.Index = 9;
			resources.ApplyResources(this.menuItem3, "menuItem3");
			// 
			// menuItemExit
			// 
			this.menuItemExit.Index = 10;
			resources.ApplyResources(this.menuItemExit, "menuItemExit");
			this.menuItemExit.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItem16
			// 
			this.menuItem16.Index = 0;
			resources.ApplyResources(this.menuItem16, "menuItem16");
			// 
			// menuItemEdit
			// 
			this.menuItemEdit.Index = 1;
			this.menuItemEdit.MenuItems.AddRange(new System.Windows.Forms.MenuItem[] {
            this.menuItem16,
            this.menuItemEditBadgeDesign,
            this.menuItemEditSides,
            this.menuItemEditObjProperties,
            this.menuItemDefaultFonts,
            this.menuItem1,
            this.menuItemEditDelete,
            this.menuItemEditFront,
            this.menuItemEditBack,
            this.menuItemEditCut,
            this.menuItemEditCopy,
            this.menuItemEditPaste,
            this.menuItem2,
            this.menuItemEditUndo,
            this.menuItemEditRedo});
			resources.ApplyResources(this.menuItemEdit, "menuItemEdit");
			// 
			// menuItemEditBadgeDesign
			// 
			this.menuItemEditBadgeDesign.Index = 1;
			resources.ApplyResources(this.menuItemEditBadgeDesign, "menuItemEditBadgeDesign");
			this.menuItemEditBadgeDesign.Click += new System.EventHandler(this.menuItemEditBadgeDesign_Click);
			// 
			// menuItemEditSides
			// 
			this.menuItemEditSides.Index = 2;
			resources.ApplyResources(this.menuItemEditSides, "menuItemEditSides");
			this.menuItemEditSides.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItemEditObjProperties
			// 
			this.menuItemEditObjProperties.Index = 3;
			resources.ApplyResources(this.menuItemEditObjProperties, "menuItemEditObjProperties");
			this.menuItemEditObjProperties.Click += new System.EventHandler(this.menuItemEditObject_Click);
			// 
			// menuItemDefaultFonts
			// 
			this.menuItemDefaultFonts.Index = 4;
			resources.ApplyResources(this.menuItemDefaultFonts, "menuItemDefaultFonts");
			this.menuItemDefaultFonts.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItem1
			// 
			this.menuItem1.Index = 5;
			resources.ApplyResources(this.menuItem1, "menuItem1");
			// 
			// menuItemEditDelete
			// 
			this.menuItemEditDelete.Index = 6;
			resources.ApplyResources(this.menuItemEditDelete, "menuItemEditDelete");
			this.menuItemEditDelete.Click += new System.EventHandler(this.MenuItemHandler_SelectedItemClick);
			// 
			// menuItemEditFront
			// 
			this.menuItemEditFront.Index = 7;
			resources.ApplyResources(this.menuItemEditFront, "menuItemEditFront");
			this.menuItemEditFront.Click += new System.EventHandler(this.MenuItemHandler_SelectedItemClick);
			// 
			// menuItemEditBack
			// 
			this.menuItemEditBack.Index = 8;
			resources.ApplyResources(this.menuItemEditBack, "menuItemEditBack");
			this.menuItemEditBack.Click += new System.EventHandler(this.MenuItemHandler_SelectedItemClick);
			// 
			// menuItemEditCut
			// 
			this.menuItemEditCut.Index = 9;
			resources.ApplyResources(this.menuItemEditCut, "menuItemEditCut");
			this.menuItemEditCut.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItemEditCopy
			// 
			this.menuItemEditCopy.Index = 10;
			resources.ApplyResources(this.menuItemEditCopy, "menuItemEditCopy");
			this.menuItemEditCopy.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItemEditPaste
			// 
			this.menuItemEditPaste.Index = 11;
			resources.ApplyResources(this.menuItemEditPaste, "menuItemEditPaste");
			this.menuItemEditPaste.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItem2
			// 
			this.menuItem2.Index = 12;
			resources.ApplyResources(this.menuItem2, "menuItem2");
			// 
			// menuItemEditUndo
			// 
			this.menuItemEditUndo.Index = 13;
			resources.ApplyResources(this.menuItemEditUndo, "menuItemEditUndo");
			this.menuItemEditUndo.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItemEditRedo
			// 
			this.menuItemEditRedo.Index = 14;
			resources.ApplyResources(this.menuItemEditRedo, "menuItemEditRedo");
			this.menuItemEditRedo.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// buttonPrint
			// 
			resources.ApplyResources(this.buttonPrint, "buttonPrint");
			this.buttonPrint.Name = "buttonPrint";
			// 
			// toolBar1
			// 
			this.toolBar1.Buttons.AddRange(new System.Windows.Forms.ToolBarButton[] {
            this.newButton,
            this.openButton,
            this.buttonSave,
            this.buttonPreview,
            this.buttonPrint,
            this.buttonFlip,
            this.buttonUndo,
            this.buttonRedo,
            this.buttonCopy,
            this.buttonCut,
            this.buttonPaste,
            this.buttonDelete,
            this.buttonInsertText,
            this.buttonInsertPortrait,
            this.buttonInsertImage,
            this.buttonInsertBarcode,
            this.buttonInsertICAO,
            this.buttonEditObjProp,
            this.buttonHelp});
			resources.ApplyResources(this.toolBar1, "toolBar1");
			this.toolBar1.ImageList = this.imageList1;
			this.toolBar1.Name = "toolBar1";
			this.toolBar1.ButtonClick += new System.Windows.Forms.ToolBarButtonClickEventHandler(this.toolBar1_ButtonClick);
			// 
			// newButton
			// 
			resources.ApplyResources(this.newButton, "newButton");
			this.newButton.Name = "newButton";
			// 
			// buttonPreview
			// 
			resources.ApplyResources(this.buttonPreview, "buttonPreview");
			this.buttonPreview.Name = "buttonPreview";
			// 
			// buttonFlip
			// 
			resources.ApplyResources(this.buttonFlip, "buttonFlip");
			this.buttonFlip.Name = "buttonFlip";
			// 
			// buttonUndo
			// 
			resources.ApplyResources(this.buttonUndo, "buttonUndo");
			this.buttonUndo.Name = "buttonUndo";
			// 
			// buttonRedo
			// 
			resources.ApplyResources(this.buttonRedo, "buttonRedo");
			this.buttonRedo.Name = "buttonRedo";
			// 
			// buttonCopy
			// 
			resources.ApplyResources(this.buttonCopy, "buttonCopy");
			this.buttonCopy.Name = "buttonCopy";
			// 
			// buttonCut
			// 
			resources.ApplyResources(this.buttonCut, "buttonCut");
			this.buttonCut.Name = "buttonCut";
			// 
			// buttonPaste
			// 
			resources.ApplyResources(this.buttonPaste, "buttonPaste");
			this.buttonPaste.Name = "buttonPaste";
			// 
			// buttonDelete
			// 
			resources.ApplyResources(this.buttonDelete, "buttonDelete");
			this.buttonDelete.Name = "buttonDelete";
			// 
			// buttonInsertText
			// 
			resources.ApplyResources(this.buttonInsertText, "buttonInsertText");
			this.buttonInsertText.Name = "buttonInsertText";
			// 
			// buttonInsertPortrait
			// 
			resources.ApplyResources(this.buttonInsertPortrait, "buttonInsertPortrait");
			this.buttonInsertPortrait.Name = "buttonInsertPortrait";
			// 
			// buttonInsertImage
			// 
			resources.ApplyResources(this.buttonInsertImage, "buttonInsertImage");
			this.buttonInsertImage.Name = "buttonInsertImage";
			// 
			// buttonInsertBarcode
			// 
			resources.ApplyResources(this.buttonInsertBarcode, "buttonInsertBarcode");
			this.buttonInsertBarcode.Name = "buttonInsertBarcode";
			// 
			// buttonInsertICAO
			// 
			resources.ApplyResources(this.buttonInsertICAO, "buttonInsertICAO");
			this.buttonInsertICAO.Name = "buttonInsertICAO";
			// 
			// buttonEditObjProp
			// 
			resources.ApplyResources(this.buttonEditObjProp, "buttonEditObjProp");
			this.buttonEditObjProp.Name = "buttonEditObjProp";
			// 
			// buttonHelp
			// 
			resources.ApplyResources(this.buttonHelp, "buttonHelp");
			this.buttonHelp.Name = "buttonHelp";
			// 
			// imageList1
			// 
			this.imageList1.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList1.ImageStream")));
			this.imageList1.TransparentColor = System.Drawing.Color.Transparent;
			this.imageList1.Images.SetKeyName(0, "");
			this.imageList1.Images.SetKeyName(1, "");
			this.imageList1.Images.SetKeyName(2, "");
			this.imageList1.Images.SetKeyName(3, "");
			this.imageList1.Images.SetKeyName(4, "");
			this.imageList1.Images.SetKeyName(5, "");
			this.imageList1.Images.SetKeyName(6, "");
			this.imageList1.Images.SetKeyName(7, "");
			this.imageList1.Images.SetKeyName(8, "");
			this.imageList1.Images.SetKeyName(9, "");
			this.imageList1.Images.SetKeyName(10, "");
			this.imageList1.Images.SetKeyName(11, "");
			this.imageList1.Images.SetKeyName(12, "");
			this.imageList1.Images.SetKeyName(13, "");
			this.imageList1.Images.SetKeyName(14, "");
			this.imageList1.Images.SetKeyName(15, "");
			this.imageList1.Images.SetKeyName(16, "");
			this.imageList1.Images.SetKeyName(17, "");
			this.imageList1.Images.SetKeyName(18, "");
			// 
			// helpProvider1
			// 
			resources.ApplyResources(this.helpProvider1, "helpProvider1");
			// 
			// mainMenu1
			// 
			this.mainMenu1.MenuItems.AddRange(new System.Windows.Forms.MenuItem[] {
            this.menuItemFile,
            this.menuItemEdit,
            this.menuItemInsert,
            this.menuItemView,
            this.menuItemAlignment,
            this.menuItemWindow,
            this.menuItemHelp});
			// 
			// menuItemInsert
			// 
			this.menuItemInsert.Index = 2;
			this.menuItemInsert.MenuItems.AddRange(new System.Windows.Forms.MenuItem[] {
            this.menuItemInsertText,
            this.menuItemInsertImage,
            this.menuItemInsertPortrait,
            this.menuItemInsertSig,
            this.menuItemInsertFinger,
            this.menuItemInsertBarcode,
            this.menuItemInsertBarcode2D,
            this.menuItemInsertICAO,
            this.menuItem4});
			resources.ApplyResources(this.menuItemInsert, "menuItemInsert");
			// 
			// menuItemInsertText
			// 
			this.menuItemInsertText.Index = 0;
			resources.ApplyResources(this.menuItemInsertText, "menuItemInsertText");
			this.menuItemInsertText.Click += new System.EventHandler(this.menuItemInsertHandler);
			// 
			// menuItemInsertImage
			// 
			this.menuItemInsertImage.Index = 1;
			resources.ApplyResources(this.menuItemInsertImage, "menuItemInsertImage");
			this.menuItemInsertImage.Click += new System.EventHandler(this.menuItemInsertHandler);
			// 
			// menuItemInsertPortrait
			// 
			this.menuItemInsertPortrait.Index = 2;
			resources.ApplyResources(this.menuItemInsertPortrait, "menuItemInsertPortrait");
			this.menuItemInsertPortrait.Click += new System.EventHandler(this.menuItemInsertHandler);
			// 
			// menuItemInsertSig
			// 
			this.menuItemInsertSig.Index = 3;
			resources.ApplyResources(this.menuItemInsertSig, "menuItemInsertSig");
			this.menuItemInsertSig.Click += new System.EventHandler(this.menuItemInsertHandler);
			// 
			// menuItemInsertFinger
			// 
			this.menuItemInsertFinger.Index = 4;
			resources.ApplyResources(this.menuItemInsertFinger, "menuItemInsertFinger");
			this.menuItemInsertFinger.Click += new System.EventHandler(this.menuItemInsertHandler);
			// 
			// menuItemInsertBarcode
			// 
			this.menuItemInsertBarcode.Index = 5;
			resources.ApplyResources(this.menuItemInsertBarcode, "menuItemInsertBarcode");
			this.menuItemInsertBarcode.Click += new System.EventHandler(this.menuItemInsertHandler);
			// 
			// menuItemInsertBarcode2D
			// 
			this.menuItemInsertBarcode2D.Index = 6;
			resources.ApplyResources(this.menuItemInsertBarcode2D, "menuItemInsertBarcode2D");
			this.menuItemInsertBarcode2D.Click += new System.EventHandler(this.menuItemInsertHandler);
			// 
			// menuItemInsertICAO
			// 
			this.menuItemInsertICAO.Index = 7;
			resources.ApplyResources(this.menuItemInsertICAO, "menuItemInsertICAO");
			this.menuItemInsertICAO.Click += new System.EventHandler(this.menuItemInsertHandler);
			// 
			// menuItem4
			// 
			this.menuItem4.Index = 8;
			this.menuItem4.MenuItems.AddRange(new System.Windows.Forms.MenuItem[] {
            this.menuItemInsertGraphicBlock});
			resources.ApplyResources(this.menuItem4, "menuItem4");
			// 
			// menuItemInsertGraphicBlock
			// 
			this.menuItemInsertGraphicBlock.Index = 0;
			resources.ApplyResources(this.menuItemInsertGraphicBlock, "menuItemInsertGraphicBlock");
			this.menuItemInsertGraphicBlock.Click += new System.EventHandler(this.menuItemInsertHandler);
			// 
			// menuItemAlignment
			// 
			this.menuItemAlignment.Index = 4;
			this.menuItemAlignment.MenuItems.AddRange(new System.Windows.Forms.MenuItem[] {
            this.menuItemAlignObj,
            this.menuItemSize,
            this.menuItem5,
            this.menuItemAutoAlign,
            this.menuItemSnapToGrid,
            this.menuItemSnapToGridLines,
            this.menuItem6,
            this.menuItemSetGrid});
			resources.ApplyResources(this.menuItemAlignment, "menuItemAlignment");
			// 
			// menuItemAlignObj
			// 
			this.menuItemAlignObj.Index = 0;
			this.menuItemAlignObj.MenuItems.AddRange(new System.Windows.Forms.MenuItem[] {
            this.menuItemAlignLeft,
            this.menuItemAlignRight,
            this.menuItemAlignTop,
            this.menuItemAlignBottom,
            this.menuItem7,
            this.menuItemAlignToMax,
            this.menuItemAlignToMin});
			resources.ApplyResources(this.menuItemAlignObj, "menuItemAlignObj");
			// 
			// menuItemAlignLeft
			// 
			this.menuItemAlignLeft.Index = 0;
			resources.ApplyResources(this.menuItemAlignLeft, "menuItemAlignLeft");
			this.menuItemAlignLeft.Click += new System.EventHandler(this.MenuItemHandler_Align);
			// 
			// menuItemAlignRight
			// 
			this.menuItemAlignRight.Index = 1;
			resources.ApplyResources(this.menuItemAlignRight, "menuItemAlignRight");
			this.menuItemAlignRight.Click += new System.EventHandler(this.MenuItemHandler_Align);
			// 
			// menuItemAlignTop
			// 
			this.menuItemAlignTop.Index = 2;
			resources.ApplyResources(this.menuItemAlignTop, "menuItemAlignTop");
			this.menuItemAlignTop.Click += new System.EventHandler(this.MenuItemHandler_Align);
			// 
			// menuItemAlignBottom
			// 
			this.menuItemAlignBottom.Index = 3;
			resources.ApplyResources(this.menuItemAlignBottom, "menuItemAlignBottom");
			this.menuItemAlignBottom.Click += new System.EventHandler(this.MenuItemHandler_Align);
			// 
			// menuItem7
			// 
			this.menuItem7.Index = 4;
			resources.ApplyResources(this.menuItem7, "menuItem7");
			// 
			// menuItemAlignToMax
			// 
			this.menuItemAlignToMax.Index = 5;
			resources.ApplyResources(this.menuItemAlignToMax, "menuItemAlignToMax");
			this.menuItemAlignToMax.Click += new System.EventHandler(this.MenuItemHandler_Align);
			// 
			// menuItemAlignToMin
			// 
			this.menuItemAlignToMin.Index = 6;
			resources.ApplyResources(this.menuItemAlignToMin, "menuItemAlignToMin");
			this.menuItemAlignToMin.Click += new System.EventHandler(this.MenuItemHandler_Align);
			// 
			// menuItemSize
			// 
			this.menuItemSize.Index = 1;
			this.menuItemSize.MenuItems.AddRange(new System.Windows.Forms.MenuItem[] {
            this.menuItemSizeHeight,
            this.menuItemSizeWidth,
            this.menuItemSizeBoth,
            this.menuItem8,
            this.menuItemSizeToMax,
            this.menuItemSizeToMin});
			resources.ApplyResources(this.menuItemSize, "menuItemSize");
			// 
			// menuItemSizeHeight
			// 
			this.menuItemSizeHeight.Index = 0;
			resources.ApplyResources(this.menuItemSizeHeight, "menuItemSizeHeight");
			this.menuItemSizeHeight.Click += new System.EventHandler(this.MenuItemHandler_Size);
			// 
			// menuItemSizeWidth
			// 
			this.menuItemSizeWidth.Index = 1;
			resources.ApplyResources(this.menuItemSizeWidth, "menuItemSizeWidth");
			this.menuItemSizeWidth.Click += new System.EventHandler(this.MenuItemHandler_Size);
			// 
			// menuItemSizeBoth
			// 
			this.menuItemSizeBoth.Index = 2;
			resources.ApplyResources(this.menuItemSizeBoth, "menuItemSizeBoth");
			this.menuItemSizeBoth.Click += new System.EventHandler(this.MenuItemHandler_Size);
			// 
			// menuItem8
			// 
			this.menuItem8.Index = 3;
			resources.ApplyResources(this.menuItem8, "menuItem8");
			// 
			// menuItemSizeToMax
			// 
			this.menuItemSizeToMax.Index = 4;
			resources.ApplyResources(this.menuItemSizeToMax, "menuItemSizeToMax");
			this.menuItemSizeToMax.Click += new System.EventHandler(this.MenuItemHandler_Size);
			// 
			// menuItemSizeToMin
			// 
			this.menuItemSizeToMin.Index = 5;
			resources.ApplyResources(this.menuItemSizeToMin, "menuItemSizeToMin");
			this.menuItemSizeToMin.Click += new System.EventHandler(this.MenuItemHandler_Size);
			// 
			// menuItem5
			// 
			this.menuItem5.Index = 2;
			resources.ApplyResources(this.menuItem5, "menuItem5");
			// 
			// menuItemAutoAlign
			// 
			this.menuItemAutoAlign.Index = 3;
			resources.ApplyResources(this.menuItemAutoAlign, "menuItemAutoAlign");
			this.menuItemAutoAlign.Click += new System.EventHandler(this.MenuItemHandler_Align);
			// 
			// menuItemSnapToGrid
			// 
			this.menuItemSnapToGrid.Index = 4;
			resources.ApplyResources(this.menuItemSnapToGrid, "menuItemSnapToGrid");
			this.menuItemSnapToGrid.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItemSnapToGridLines
			// 
			this.menuItemSnapToGridLines.Index = 5;
			resources.ApplyResources(this.menuItemSnapToGridLines, "menuItemSnapToGridLines");
			this.menuItemSnapToGridLines.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItem6
			// 
			this.menuItem6.Index = 6;
			resources.ApplyResources(this.menuItem6, "menuItem6");
			// 
			// menuItemWindow
			// 
			this.menuItemWindow.Index = 5;
			this.menuItemWindow.MenuItems.AddRange(new System.Windows.Forms.MenuItem[] {
            this.menuItemCascade,
            this.menuItemTile});
			resources.ApplyResources(this.menuItemWindow, "menuItemWindow");
			// 
			// menuItemCascade
			// 
			this.menuItemCascade.Index = 0;
			resources.ApplyResources(this.menuItemCascade, "menuItemCascade");
			this.menuItemCascade.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// menuItemTile
			// 
			this.menuItemTile.Index = 1;
			resources.ApplyResources(this.menuItemTile, "menuItemTile");
			this.menuItemTile.Click += new System.EventHandler(this.MenuItemHandler);
			// 
			// statusBar1
			// 
			resources.ApplyResources(this.statusBar1, "statusBar1");
			this.statusBar1.Name = "statusBar1";
			// 
			// mdiClient1
			// 
			resources.ApplyResources(this.mdiClient1, "mdiClient1");
			this.mdiClient1.Name = "mdiClient1";
			// 
			// DCSDesignerMain
			// 
			resources.ApplyResources(this, "$this");
			this.Controls.Add(this.toolBar1);
			this.Controls.Add(this.statusBar1);
			this.Controls.Add(this.mdiClient1);
			this.IsMdiContainer = true;
			this.Menu = this.mainMenu1;
			this.Name = "DCSDesignerMain";
			this.helpProvider1.SetShowHelp(this, ((bool)(resources.GetObject("$this.ShowHelp"))));
			this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Show;
			this.MdiChildActivate += new System.EventHandler(this.DCSDesignerMain_MdiChildActivate);
			this.Closing += new System.ComponentModel.CancelEventHandler(this.ClosingMainAppHander);
			this.ResumeLayout(false);
			this.PerformLayout();

		}
		#endregion


		/////////////////////////////////////////////////////////
		// initialize m_lFieldNames from AllField.txt
		/////////////////////////////////////////////////////////
		private bool ReadAllDBFieldNames()
		{
			string strAllfield;
			strAllfield = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, "AllField.txt");
			if (!System.IO.File.Exists(strAllfield))
			{
				DCSMsg.Show("The table of designObject names " + strAllfield + " is missing.");
				return false;
			}
            
            System.IO.StreamReader sr = null;
            string line;
            try
			{
				sr = new System.IO.StreamReader(strAllfield);
				while ((line = sr.ReadLine()) != null) 
				{
					line.Trim();
					m_AllDBFieldNames.Add(line);
				}
			}
			catch
			{
				return false;
			}
			finally
			{
				if (sr != null) sr.Close();
			}
			return true;
		}
		
		//Handle the Menu Item clicks
		private void MenuItemHandler(object sender, System.EventArgs e)
		{
			if (sender == menuItemNew)
			{
				this.New();
			}
			else if (sender == menuItemOpen)
			{
				this.Open();
			}
			else if (sender == menuItemSave)
			{
				this.Save();
			}
			else if (sender == menuItemPreview)
			{
				this.PrintOrPreview(false);
			}
			else if (sender == menuItemPrint)
			{
				this.PrintOrPreview(true);
			}
			else if (sender == this.menuItemFlipSide)
			{
				this.FlipSide();
			}
			else if (sender == menuItemAbout)
			{
				this.AboutHelp();
			}
			else if (sender == menuItemExit)
			{
				this.Exit();
			}
			else if (sender == menuItemClose)
			{
				this.CloseView();
			}
			else if (sender == menuItemTile)
			{
				this.Tile();
			}
			else if (sender == menuItemCascade)
			{
				this.Cascade();
			}
			else if (sender == menuItemSaveAs)
			{
				this.SaveAs();
			}
			else if (sender == menuItemHelpTopics)
			{
				this.ShowHelpTopics();
			}
			else if (sender == menuItemToolbar)
			{
				toolBar1.Visible = menuItemToolbar.Checked = !toolBar1.Visible;
			}
			else if (sender == menuItemStatusbar)
			{
				statusBar1.Visible = menuItemStatusbar.Checked = !statusBar1.Visible;
			}
			else if (sender == menuItemEditCopy)
			{
				this.Copy();
			}
			else if (sender == menuItemEditPaste)
			{
				this.Paste();
			}
			else if (sender == menuItemEditCut)
			{
				this.Cut();
			}
			else if (sender == menuItemEditUndo)
			{
				this.Undo();
			}
			else if (sender == menuItemEditRedo)
			{
				this.Redo();
			}
			else if (sender == this.menuItemEditSides)
			{
				this.EliminateEmptySides();
			}
			else if (sender == this.menuItemSnapToGrid)
			{
				this.menuItemSnapToGrid.Checked = !this.menuItemSnapToGrid.Checked;
				this.m_bGridSnapOn = this.menuItemSnapToGrid.Checked;
				if (m_bGridSnapOn)
					this.menuItemSnapToGridLines.Checked = this.m_bTabAlignOn 
					= this.menuItemAutoAlign.Checked = this.m_bAutoAlign = false;
				this.ActiveMdiChild.Invalidate(true);
			}
			else if (sender == this.menuItemSetGrid)
			{
				this.SetGrid();
			}
			else if (sender == this.menuItemSnapToGridLines)
			{
				this.menuItemSnapToGridLines.Checked = !this.menuItemSnapToGridLines.Checked;
				this.m_bTabAlignOn = this.menuItemSnapToGridLines.Checked;
				if (m_bTabAlignOn)
				{
					this.menuItemSnapToGrid.Checked = this.m_bGridSnapOn
					= this.menuItemAutoAlign.Checked = this.m_bAutoAlign = false;

					DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
					if (activeView != null)
					{
						DCSDesignerDoc activeDoc = activeView.GetDocument();
						if ( (activeDoc.m_arrayTabStopsH == null || activeDoc.m_arrayTabStopsH.Count == 0)
							&& (activeDoc.m_arrayTabStopsV == null || activeDoc.m_arrayTabStopsV.Count == 0) )
							DCSMsg.Show("No tabular alignment lines have been defined.\nClick on Set Alignment Parameters to set them.");
					}
				}
				this.ActiveMdiChild.Invalidate(true);
			}
			else if (sender == this.menuItemDefaultFonts)
			{
				DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
				DCSDesignerDoc activeDoc = null;
				if (activeView != null) activeDoc = activeView.GetDocument();
				if (activeDoc == null) return;
				DCSDEV.DCSDesigner.DCSFontDefaults dlg = new DCSFontDefaults(activeDoc, activeView);
				dlg.ShowDialog(this);
				dlg.Dispose();
				activeDoc.m_isDirty = true;
				activeDoc.m_isViewDirty = true;
				this.ActiveMdiChild.Invalidate(true);
			}
		}

		private void MenuItemHandler_Align(object sender, System.EventArgs e)
		{
			if (sender == this.menuItemAutoAlign)
			{
				this.menuItemAutoAlign.Checked = !this.menuItemAutoAlign.Checked;
				this.m_bAutoAlign = this.menuItemAutoAlign.Checked;
				if (m_bAutoAlign)
					this.menuItemSnapToGridLines.Checked = this.m_bTabAlignOn 
					= menuItemSnapToGrid.Checked = this.m_bGridSnapOn = false;
				this.ActiveMdiChild.Invalidate(true);
			}
			else if (sender == this.menuItemAlignToMax)
			{
				this.menuItemAlignToMax.Checked = !this.menuItemAlignToMax.Checked;
				this.m_bAlignToMax = this.menuItemAlignToMax.Checked;

				if (this.menuItemAlignToMax.Checked) this.menuItemAlignToMin.Checked = this.m_bAlignToMin = false;
				return;
			}
			else if (sender == this.menuItemAlignToMin)
			{
				this.menuItemAlignToMin.Checked = !this.menuItemAlignToMin.Checked;
				this.m_bAlignToMin = this.menuItemAlignToMin.Checked;

				if (this.menuItemAlignToMin.Checked) this.menuItemAlignToMax.Checked = this.m_bAlignToMax = false;
				return;
			}

			DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
			if (activeView.m_designObjectsSelected.Count < 2) return;

			bool bMost = false;
			int most = -1;
			int idx = 0;
			int mostidx = -1;
			if (sender == this.menuItemAlignLeft)
			{
				foreach (DCSDesignObject designObject in activeView.m_designObjectsSelected)
				{
					if (most == -1) bMost = true;
					else if (this.m_bAlignToMax)
					{
						if (most > designObject.Bounds.Left) bMost = true;
					}
					else if (this.m_bAlignToMin)
					{
						if (most < designObject.Bounds.Left) bMost = true;
					}

					if (bMost)
					{
						most = designObject.Bounds.Left;
						mostidx = idx;
						bMost = false;
					}
					idx++;
				}
				idx = 0;
				foreach (DCSDesignObject designObject in activeView.m_designObjectsSelected)
				{
					if (idx++ == mostidx) continue;
					designObject.Bounds.X = most;
				}
			}
			else if (sender == this.menuItemAlignRight)
			{
				foreach (DCSDesignObject designObject in activeView.m_designObjectsSelected)
				{
					if (most == -1) bMost = true;
					else if (this.m_bAlignToMax)
					{
						if (most < designObject.Bounds.Right) bMost = true;
					}
					else if (this.m_bAlignToMin)
					{
						if (most > designObject.Bounds.Right) bMost = true;
					}

					if (bMost)
					{
						most = designObject.Bounds.Right;
						mostidx = idx;
						bMost = false;
					}
					idx++;
				}
				idx = 0;
				foreach (DCSDesignObject designObject in activeView.m_designObjectsSelected)
				{
					if (idx++ == mostidx) continue;
					designObject.Bounds.X = designObject.Bounds.X = most - designObject.Bounds.Width;
				}
			}
			else if (sender == this.menuItemAlignTop)
			{
				foreach (DCSDesignObject designObject in activeView.m_designObjectsSelected)
				{
					if (most == -1) bMost = true;
					else if (this.m_bAlignToMax)
					{
						if (most > designObject.Bounds.Top) bMost = true;
					}
					else if (this.m_bAlignToMin)
					{
						if (most < designObject.Bounds.Top) bMost = true;
					}

					if (bMost)
					{
						most = designObject.Bounds.Top;
						mostidx = idx;
						bMost = false;
					}
					idx++;
				}
				idx = 0;
				foreach (DCSDesignObject designObject in activeView.m_designObjectsSelected)
				{
					if (idx++ == mostidx) continue;
					designObject.Bounds.Y = most;
				}
			}
			else if (sender == this.menuItemAlignBottom)
			{
				foreach (DCSDesignObject designObject in activeView.m_designObjectsSelected)
				{
					if (most == -1) bMost = true;
					else if (this.m_bAlignToMax)
					{
						if (most < designObject.Bounds.Bottom) bMost = true;
					}
					else if (this.m_bAlignToMin)
					{
						if (most > designObject.Bounds.Bottom) bMost = true;
					}

					if (bMost)
					{
						most = designObject.Bounds.Bottom;
						mostidx = idx;
						bMost = false;
					}
					idx++;
				}
				idx = 0;
				foreach (DCSDesignObject designObject in activeView.m_designObjectsSelected)
				{
					if (idx++ == mostidx) continue;
					designObject.Bounds.Y = designObject.Bounds.Y = most - designObject.Bounds.Height;
				}
			}

			DCSDesignerDoc activeDoc = activeView.GetDocument();
			activeDoc.m_isDirty = true;
			activeDoc.m_isViewDirty = true;
			activeView.Invalidate(true);
		}

		private void MenuItemHandler_Size(object sender, System.EventArgs e)
		{
			if (sender == this.menuItemSizeToMax)
			{
				this.menuItemSizeToMax.Checked = !this.menuItemSizeToMax.Checked;
				this.m_bSizeToMax = this.menuItemSizeToMax.Checked;

				if (this.menuItemSizeToMax.Checked)
				{
					this.menuItemSizeToMin.Checked = false;
					this.m_bSizeToMin = false;
				}
				return;
			}
			else if (sender == this.menuItemSizeToMin)
			{
				this.menuItemSizeToMin.Checked = !this.menuItemSizeToMin.Checked;
				this.m_bSizeToMin = this.menuItemSizeToMin.Checked;

				if (this.menuItemSizeToMin.Checked)
				{
					this.menuItemSizeToMax.Checked = false;
					this.m_bSizeToMax = false;
				}
				return;
			}

			DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
			if (activeView.m_designObjectsSelected.Count < 2) return;

			if (sender == this.menuItemSizeWidth)
			{
				this.SizeByWidth();
			}
			if (sender == this.menuItemSizeHeight)
			{
				this.SizeByHeight();
			}
			if (sender == this.menuItemSizeBoth)
			{
				this.SizeByWidth();
				this.SizeByHeight();
			}

			DCSDesignerDoc activeDoc = activeView.GetDocument();
			activeDoc.m_isDirty = true;
			activeDoc.m_isViewDirty = true;
			activeView.Invalidate(true);
		}

		private void SizeByWidth()
		{
			DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
			if (activeView.m_designObjectsSelected.Count < 2) return;

			bool bMost = false;
			int most = -1;
			int idx = 0;
			int mostidx = -1;

			foreach (DCSDesignObject designObject in activeView.m_designObjectsSelected)
			{
				if (most == -1) bMost = true;
				else if (this.m_bSizeToMax)
				{
					if (most < designObject.Bounds.Width) bMost = true;
				}
				else if (this.m_bSizeToMin)
				{
					if (most > designObject.Bounds.Width) bMost = true;
				}

				if (bMost)
				{
					most = designObject.Bounds.Width;
					mostidx = idx;
					bMost = false;
				}
				idx++;
			}
			idx = 0;
			foreach (DCSDesignObject designObject in activeView.m_designObjectsSelected)
			{
				if (idx++ == mostidx) continue;
				designObject.Bounds.Width = most;
			}
		}

		private void SizeByHeight()
		{
			DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
			if (activeView.m_designObjectsSelected.Count < 2) return;

			bool bMost = false;
			int most = -1;
			int idx = 0;
			int mostidx = -1;

			foreach (DCSDesignObject designObject in activeView.m_designObjectsSelected)
			{
				if (most == -1) bMost = true;
				else if (this.m_bSizeToMax)
				{
					if (most < designObject.Bounds.Height) bMost = true;
				}
				else if (this.m_bSizeToMin)
				{
					if (most > designObject.Bounds.Height) bMost = true;
				}

				if (bMost)
				{
					most = designObject.Bounds.Height;
					mostidx = idx;
					bMost = false;
				}
				idx++;
			}
			idx = 0;
			foreach (DCSDesignObject designObject in activeView.m_designObjectsSelected)
			{
				if (idx++ == mostidx) continue;
				designObject.Bounds.Height = most;
			}
		}

		//Handle the Toolbar button clicks
		private void toolBar1_ButtonClick(object sender, System.Windows.Forms.ToolBarButtonClickEventArgs e)
		{
			if(e.Button == newButton)	
			{
				this.New();
			}
			else if(e.Button==openButton)
			{
				this.Open();
			}
			else if(e.Button==buttonSave)
			{
				this.Save();
			}
			else if(e.Button == buttonPreview)
			{
				this.PrintOrPreview(false);
			}
			else if(e.Button == buttonPrint)
			{
				this.PrintOrPreview(true);
			}
			else if(e.Button == buttonHelp)
			{
				this.ShowHelpTopics();
			}
			else if(e.Button == buttonDelete)
			{
				this.MenuItemHandler_SelectedItemClick(this.menuItemEditDelete, null);
			}
			else if(e.Button == buttonFlip)
			{
				this.FlipSide();
			}
			else if(e.Button==this.buttonUndo)
			{
				this.Undo();
			}
			else if(e.Button==this.buttonRedo)
			{
				this.Redo();
			}
			else if(e.Button == this.buttonEditObjProp)
			{
				this.menuItemEditObject_Click(null, null);
			}
			else if(e.Button == this.buttonInsertICAO)
			{
				this.menuItemInsertHandler(this.menuItemInsertICAO, null);
			}
			else if(e.Button == this.buttonInsertImage)
			{
				this.menuItemInsertHandler(this.menuItemInsertImage, null);
			}
			else if(e.Button == this.buttonInsertPortrait)
			{
				this.menuItemInsertHandler(this.menuItemInsertPortrait, null);
			}
			else if(e.Button == this.buttonInsertText)
			{
				this.menuItemInsertHandler(this.menuItemInsertText, null);
			}
			else if(e.Button == this.buttonInsertBarcode)
			{
				this.menuItemInsertHandler(this.menuItemInsertBarcode, null);
			}

			else if(e.Button == this.buttonCopy)
			{
				this.Copy();
			}
			else if(e.Button == this.buttonCut)
			{
				this.Cut();
			}
			else if(e.Button == this.buttonPaste)
			{
				this.Paste();
			}
		}
		//About Help
		private void AboutHelp()
		{
            using (DCSDEV.DCSDesigner.AboutForm dlg = new DCSDEV.DCSDesigner.AboutForm())
            {
                dlg.ShowDialog(this);
            }
		}

		//Help Topics
		private void ShowHelpTopics()
		{		
			Help.ShowHelp(this,"..\\..\\help\\DCSDesigner.chm");
		}
	
		//Print
		private void PrintOrPreview(bool bPrint)
		{
			try 
			{			
				DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
				if (activeView == null) return;
				DCSDesignerDoc activeDoc = activeView.GetDocument();

                DCSDEV.PrintProperties.PrinterTypeDatum bcDatum = new DCSDEV.PrintProperties.PrinterTypeDatum("");
				bcDatum.LoadPrinterTypeData(activeDoc.PrinterTypeIndex);
				
				// deal with printer type
				if (bcDatum.m_SelectedPrinterName == null || bcDatum.m_SelectedPrinterName == "Always Ask")
				{
					if (m_printersettingsChosen == null)
					{
						System.Windows.Forms.PrintDialog pdlg = new System.Windows.Forms.PrintDialog();
						pdlg.PrinterSettings = new System.Drawing.Printing.PrinterSettings();
						DialogResult result = pdlg.ShowDialog(this);
						if (result == DialogResult.Cancel)
							return;
						// remember the settings so it is not asked again
						m_printersettingsChosen = pdlg.PrinterSettings;
					}
					this.printDoc.PrinterSettings = m_printersettingsChosen;
					m_printersettingsChosen = null;	// NOTE: set to null causes "Always Ask"; otherwise answer will be remembered.
				}
				else if (bcDatum.m_SelectedPrinterName == "Windows Default Printer")
				{
					this.printDoc.PrinterSettings.PrinterName = null;
				}
				else // printer config specifes a printer
				{
					this.printDoc.PrinterSettings.PrinterName = bcDatum.m_SelectedPrinterName;
				}
				this.printDoc.DefaultPageSettings.Landscape = bcDatum.m_IfLandscape;
				if (bPrint)
				{
					this.printDoc.Print();
				}
				else
				{
                    using (PrintPreviewDialog prevDlg = new PrintPreviewDialog())
                    {
                        prevDlg.Document = this.printDoc;
                        prevDlg.Size = new System.Drawing.Size(600, 329);
                        prevDlg.ShowDialog(this);
                    }
				}
			} 
			catch(Exception e)
			{
				DCSMsg.Show(e);
			}
		}

		//PrintPage event handler
		private void DCSDesignerPrintPage(object sender,PrintPageEventArgs ev)
		{
			try
			{
				DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
				if (activeView == null) return;
				DCSDesignerDoc activeDoc = activeView.GetDocument();
				
				// get printer scale
				double dScale = ev.Graphics.DpiX / 100.0;
				//double dScale = ev.PageSettings.PrinterResolution.X / 100.0;

				// get badge size scaled and make a bmTemp
				int width, height;
				width = DCSMath.IntTimesDouble(activeDoc.Bounds.Width, dScale);
				height = DCSMath.IntTimesDouble(activeDoc.Bounds.Height, dScale);
				Bitmap bitmap = new Bitmap(width, height);
				Graphics gr = Graphics.FromImage(bitmap);

				// build bmTemp for the current side
                activeDoc.RipSideToGDI(activeView.CurrrentSide, gr, dScale, DCSDesign.DCSDesign.RipMode.RIPMODE_LAYOUTPRINT, true, true, true /* back fore text*/);

				// position the image on the page as it will be in final print
                DCSDEV.PrintProperties.PrinterTypeDatum bcDatum = new DCSDEV.PrintProperties.PrinterTypeDatum("");
				bcDatum.LoadPrinterTypeData(activeDoc.PrinterTypeIndex);
				int xLoc = activeDoc.Bounds.X + bcDatum.m_OffsetX;
				int yLoc = activeDoc.Bounds.Y + bcDatum.m_OffsetY;
				/***************************************************************************************
				//xLoc = (DCSMath.IntTimesDouble(ev.PageBounds.Width, dScale) - width) / 2;
				//yLoc = (DCSMath.IntTimesDouble(ev.PageBounds.Height, dScale) - height) / 2;
				xLoc = (ev.PageBounds.Width - activeDoc.Bounds.Width) / 2;
				yLoc = (ev.PageBounds.Height - activeDoc.Bounds.Height) / 2;
				if (xLoc < 0) xLoc = 0;
				if (yLoc < 0) yLoc = 0;
				****************************************************************************************/
				if (activeView.CurrrentSide > 0)
				{
					// reverse side aligns with front
					xLoc = ev.PageBounds.Width - xLoc - activeDoc.Bounds.Width;
				}

				Rectangle rectDst = new Rectangle(new Point(xLoc, yLoc), activeDoc.Bounds.Size);
				Rectangle rectSrc = new Rectangle(0,0,bitmap.Width, bitmap.Height);

				// put bmTemp for current side onto sheet
				ev.Graphics.DrawImage(bitmap,rectDst,rectSrc,System.Drawing.GraphicsUnit.Pixel);
				bitmap.Dispose();
				ev.HasMorePages = false;	//(m_iCurrentPage < m_design.m_designSides.Count);
			}
			catch (Exception ex)
			{
				DCSMsg.Show(ex);
			}
		}

		//Exit
		private void Exit()
		{
			Form[] childForm = this.MdiChildren ;
			//Make sure to ask for saving the doc before exiting the app
			for (int i=0; i < childForm.Length ; i++)
			{
				childForm[i].Close();
			}
			this.Close();
		}

		//Close the View
		private  void CloseView()
		{
			DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
			if (activeView == null) return;
			activeView.Close();		
		}
		//Tile
		private void Tile()
		{
			this.LayoutMdi(MdiLayout.TileHorizontal);
		
		}
		//Cascade
		private void Cascade()
		{
			this.LayoutMdi(MdiLayout.Cascade);
		
		}
	
		//Open an existing document
		private void Open()
		{
			string strDesignName = DCSDEV.DCSDesignDataAccess.SelectOpenDesignName("");
			if (strDesignName == null) return;

			CreateDocument(strDesignName);
		}

		private void SaveAs()
		{
			DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
			if (activeView == null) return;
			string strDesignName = activeView.GetDocument().m_strDesignName;	// name without path or extension

			strDesignName = DCSDEV.DCSDesignDataAccess.SelectSaveDesignName(strDesignName);
			if (strDesignName == null) return;

			Cursor cursorSave = this.Cursor;
			this.Cursor = Cursors.WaitCursor;
			activeView.GetDocument().SaveDocument(strDesignName);		
			activeView.GetDocument().m_strDesignName = strDesignName;
			activeView.m_strViewName = strDesignName;
			activeView.SetViewTitleBar();
			this.Cursor = cursorSave;
		}

		//Save the document
		private void Save()
		{
			DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild ;
			if (activeView == null) return;
			string strDesignName = activeView.GetDocument().m_strDesignName;	// name without path or extension

			if (strDesignName == null || strDesignName.Length == 0)
			{
				SaveAs();
				return;
			}

			Cursor cursorSave = this.Cursor;
			this.Cursor = Cursors.WaitCursor;
			activeView.GetDocument().SaveDocument(strDesignName);
			this.Cursor = cursorSave;
		}

		//Open new document
		private void New()
		{		
			bool bRet = CreateDocument(null);
			if (!bRet) this.CloseView();			
		}

		private void Copy()
		{
			DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
			if (activeView.m_designObjectsSelected.Count <= 0) return;

			Cursor cursorSave = this.Cursor;
			this.Cursor = Cursors.WaitCursor;
			m_ClipBoardObjects.Clear();
			foreach (DCSDesignObject designObjectIn in activeView.m_designObjectsSelected)
			{
				DCSDEV.DCSDesign.DCSDesignObject fieldCopy = designObjectIn.CloneObject(false);	//bCreating=false
				this.m_ClipBoardObjects.Add(fieldCopy);
			}
			this.Cursor = cursorSave;
		}
		private void Cut()
		{
			Copy();
			this.MenuItemHandler_SelectedItemClick(this.menuItemEditDelete, null);
		}

		private void Paste()
		{
			if (m_ClipBoardObjects.Count == 0) return;
			DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
			if (activeView == null) return;

			Cursor cursorSave = this.Cursor;
			this.Cursor = Cursors.WaitCursor;

            // determine location of any currently selected objects
            Rectangle rectSel = Rectangle.Empty;
            int intSelCount = activeView.m_designObjectsSelected.Count;
            if (intSelCount > 0)
            {
                rectSel = ((DCSDesignObject)activeView.m_designObjectsSelected[0]).Bounds;
                foreach (DCSDesignObject designObjectedSelected in activeView.m_designObjectsSelected)
                {
                    rectSel = Rectangle.Union(designObjectedSelected.Bounds, rectSel);
                }
                activeView.m_designObjectsSelected.Clear();
            }

			DCSDesignerDoc activeDoc = activeView.GetDocument();
			// determine new origin so set of pasted objects are centered
			Rectangle rectNet;
			rectNet = ((DCSDesignObject)m_ClipBoardObjects[0]).Bounds;
			foreach (DCSDesignObject designObjectIn in m_ClipBoardObjects)
			{
				rectNet = Rectangle.Union(designObjectIn.Bounds, rectNet);
			}
			int locX = 0;
			int locY = 0;
            if (intSelCount == 0)
            {
                if (rectNet.Width < activeDoc.Bounds.Width)
                    locX = (activeDoc.Bounds.Width - rectNet.Width) / 2;
                if (rectNet.Height < activeDoc.Bounds.Height)
                    locY = (activeDoc.Bounds.Height - rectNet.Height) / 2;
            }
            else
            {
                locX = rectSel.X;
                locY = rectSel.Bottom;
            }
			Size delta = new Size(locX - rectNet.X, locY - rectNet.Y);

			bool bFirst = true;
			foreach (DCSDesignObject designObjectIn in m_ClipBoardObjects)
			{
				DCSDEV.DCSDesign.DCSDesignObject designObjectPaste = designObjectIn.CloneObject(false);	//bCreating=false
				DCSDEV.DCSDesign.DCSDesignSide designSide = (DCSDEV.DCSDesign.DCSDesignSide)activeDoc.m_designSides[activeView.CurrrentSide];
				designSide.m_DCSDesignObjects.Add(designObjectPaste);
				activeDoc.m_UndoEventClass.Add(activeView.CurrrentSide, UndoType.Insert, designObjectPaste, bFirst);
				bFirst = false;
				activeView.m_designObjectsSelected.Add(designObjectPaste);
				designObjectPaste.Bounds.Location = designObjectPaste.Bounds.Location + delta;
				if (designObjectPaste.Bounds.X >= activeDoc.Bounds.Width)
					designObjectPaste.Bounds.X = activeDoc.Bounds.Width - designObjectPaste.Bounds.Width;
				if (designObjectPaste.Bounds.Y >= activeDoc.Bounds.Height)
					designObjectPaste.Bounds.Y = activeDoc.Bounds.Height - designObjectPaste.Bounds.Height;
			}
			activeDoc.m_isDirty = true;
			activeDoc.m_isViewDirty = true;
			activeView.Invalidate(true);
			this.EnableGUIItems();
			this.Cursor = cursorSave;
		}
		private void FlipSide()
		{
			DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
			if (activeView == null) return;
			DCSDesignerDoc activeDoc = activeView.GetDocument();
			activeView.m_designObjectsSelected.Clear();

			if (activeDoc.NumBadgeSides >= 2)
			{
				activeView.CurrrentSide = (activeView.CurrrentSide+1)%activeDoc.NumBadgeSides;

				activeView.SetPictureBoxSize(DCSMath.TimesDouble(activeDoc.Bounds.Size, activeView.ViewScale));
				
				activeView.SetViewTitleBar();
				activeDoc.m_isViewDirty = true;
				activeView.Invalidate(true);
			}
			this.EnableGUIItems();
		}

		private void Undo()
		{
			DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
			if (activeView == null) return;

			Cursor cursorSave = this.Cursor;
			this.Cursor = Cursors.WaitCursor;
			activeView.m_designObjectsSelected.Clear();
			DCSDesignerDoc activeDoc = activeView.GetDocument();

			activeDoc.m_UndoEventClass.Undo(activeDoc.m_RedoEventClass);
			// may need to flip side to show what is undone
			int undoneside = activeDoc.m_UndoEventClass.GetUndoneSide();
			if (activeDoc.NumBadgeSides >= 2 && undoneside != activeView.CurrrentSide && undoneside >= 0 && undoneside < activeDoc.NumBadgeSides)
			{
					activeView.CurrrentSide = (activeView.CurrrentSide+1)%activeDoc.NumBadgeSides;
					activeView.SetViewTitleBar();
			}
		
			activeDoc.m_isDirty = true;
			activeDoc.m_isViewDirty = true;
			activeView.Invalidate(true);
			this.EnableGUIItems();
			this.Cursor = cursorSave;
		}
		private void Redo()
		{
			DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
			if (activeView == null) return;

			Cursor cursorSave = this.Cursor;
			this.Cursor = Cursors.WaitCursor;
			activeView.m_designObjectsSelected.Clear();
			DCSDesignerDoc activeDoc = activeView.GetDocument();

			activeDoc.m_RedoEventClass.Undo(activeDoc.m_UndoEventClass);
			// may need to flip side to show what is undone
			int undoneside = activeDoc.m_RedoEventClass.GetUndoneSide();
			if (activeDoc.NumBadgeSides >= 2 && undoneside != activeView.CurrrentSide && undoneside >= 0 && undoneside < activeDoc.NumBadgeSides)
			{
				activeView.CurrrentSide = (activeView.CurrrentSide+1)%activeDoc.NumBadgeSides;
				activeView.SetViewTitleBar();
			}
		
			activeDoc.m_isDirty = true;
			activeDoc.m_isViewDirty = true;
			activeView.Invalidate(true);
			this.EnableGUIItems();
			this.Cursor = cursorSave;
		}

		//Creates a new MDI document
		//if strImageID is not null opens an exisiting doc file
		private bool CreateDocument(string strName)
		{
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.BadgeLayout, true)) return false;

			// create document
			DCSDesignerDoc newDoc;
			newDoc = new DCSDesignerDoc(this, strName);

			if (strName == null) 
			{
				// creating a new badge
				bool bRet = EditBadgeBackground(true);	// go right to properties if creating a new doc
				return bRet;
			}
			return true;
		}

		// return false if canceled
		private bool EditBadgeBackground(bool bNew)
		{
			DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
			if (activeView == null) return false;
			DCSDesignerDoc activeDoc = activeView.GetDocument();
			activeView.m_designObjectsSelected.Clear();

			DCSDEV.DCSDesigner.BadgeDesignProperties dlg = new DCSDEV.DCSDesigner.BadgeDesignProperties(activeDoc, activeView, activeView.CurrrentSide, bNew);
			DialogResult result = dlg.ShowDialog(this);
			if (result != DialogResult.Cancel)
			{
				//activeView.ClientSize
				Size size = DCSMath.TimesDouble(activeDoc.Bounds.Size, activeView.ViewScale);
				activeView.SetPictureBoxSize(size);	//activeView.ClientSize);
				activeView.SetViewTitleBar();

				activeDoc.m_isDirty = true;
				activeDoc.m_isViewDirty = true;
				activeView.Invalidate(true);
			}
			this.EnableGUIItems();
			return (result != DialogResult.Cancel);
		}

		private void EliminateEmptySides()
		{
			DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
			DCSDesignerDoc activeDoc = null;
			if (activeView == null) return; 
			activeDoc = activeView.GetDocument();
			int i;	// never remove the first side
			for (i = activeDoc.NumBadgeSides - 1; i > 0; i--)
			{
				if (((DCSDEV.DCSDesign.DCSDesignSide)activeDoc.m_designSides[i]).m_DCSDesignObjects.Count > 0)
					break;
			}
			if (i < activeDoc.NumBadgeSides - 1)
			{
				activeDoc.NumBadgeSides = i + 1;
				activeView.CurrrentSide = 0;
				activeView.SetViewTitleBar();

				activeDoc.m_isDirty = true;
				activeDoc.m_isViewDirty = true;
				activeView.Invalidate(true);
				this.EnableGUIItems();
			}
		}

		private void SetGrid()
		{
			DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
			if (activeView == null) return;		// set grid should be unavailable with no active view and doc

			DCSDesignerDoc activeDoc = activeView.GetDocument();

			DCSDEV.DCSDesigner.SetGridProperties dlg = new DCSDEV.DCSDesigner.SetGridProperties(activeDoc);
			DialogResult result = dlg.ShowDialog(this);
			if (result != DialogResult.Cancel)
			{
				// cause the design to be saved to save new grid values
				activeDoc.m_isDirty = true;
				// cause the view to be redrawn to show new grid status
				activeView.Invalidate(true);
			}
		}

		public void EnableGUIItems()
		{
			DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
			DCSDesignerDoc activeDoc = null;
			if (activeView != null) activeDoc = activeView.GetDocument();
		 
			bool bHasADoc = (activeView != null);
			bool bHasSelection = (bHasADoc && activeView.m_designObjectsSelected.Count != 0);
			bool bHas2 = bHasSelection && (activeView.m_designObjectsSelected.Count >= 2);

			//menus
			// menuItemFile;
			this.menuItemClose.Visible = bHasADoc;
			this.menuItemSave.Visible = bHasADoc;
			this.menuItemSaveAs.Visible = bHasADoc;
			this.menuItemPrint.Visible = bHasADoc;
			this.menuItemPreview.Visible = bHasADoc;
			
			this.menuItemEdit.Visible = bHasADoc;
			this.menuItemEditObjProperties.Visible = bHasSelection;
			this.menuItemEditDelete.Visible = bHasSelection;
			this.menuItemEditBack.Visible = bHasSelection;
			this.menuItemEditFront.Visible = bHasSelection;
			this.menuItemEditCut.Visible = bHasSelection;
			this.menuItemEditCopy.Visible = bHasSelection;
			this.menuItemEditPaste.Enabled = (this.m_ClipBoardObjects.Count > 0);
			this.menuItemEditUndo.Visible =  (bHasADoc && activeDoc.m_UndoEventClass.Count > 0);
			this.menuItemEditRedo.Visible = (bHasADoc && activeDoc.m_RedoEventClass.Count > 0);

			this.menuItemInsert.Visible = bHasADoc;
			
			this.menuItemView.Visible = bHasADoc;
			this.menuItemFlipSide.Visible = (bHasADoc && activeDoc.NumBadgeSides > 1);

			this.menuItemAlignment.Visible = bHasADoc;
			this.menuItemAlignObj.Enabled = bHas2;
			this.menuItemSize.Enabled = bHas2;
			this.menuItemAlignToMax.Checked = this.m_bAlignToMax;
			this.menuItemAlignToMin.Checked = this.m_bAlignToMin;

			this.menuItemSizeBoth.Enabled = bHas2;
			this.menuItemSizeHeight.Enabled = bHas2;
			this.menuItemSizeWidth.Enabled = bHas2;
			this.menuItemSizeToMax.Checked = this.m_bSizeToMax;
			this.menuItemSizeToMin.Checked = this.m_bSizeToMin;

			this.menuItemWindow.Visible = bHasADoc;
			this.menuItemHelpTopics.Visible = false;	// not implemented

			// buttons
			this.buttonSave.Visible = bHasADoc;
			this.buttonPreview.Visible = bHasADoc;
			this.buttonPrint.Visible = bHasADoc;
			this.buttonHelp.Visible = false;	// not implemented
			this.buttonUndo.Enabled = (bHasADoc && activeDoc.m_UndoEventClass.Count > 0);
			this.buttonRedo.Enabled = (bHasADoc && activeDoc.m_RedoEventClass.Count > 0);
			this.buttonDelete.Visible = bHasSelection;
			this.buttonFlip.Visible = (bHasADoc && activeDoc.NumBadgeSides > 1);
			this.buttonCopy.Visible = bHasSelection;
			this.buttonCut.Visible = bHasSelection;
			this.buttonPaste.Visible = (bHasADoc && this.m_ClipBoardObjects.Count > 0);

			this.buttonEditObjProp.Visible = bHasSelection;
			this.buttonInsertICAO.Visible = false; // not implemented
			this.buttonInsertImage.Visible = bHasADoc;
			this.buttonInsertPortrait.Visible = bHasADoc;
			this.buttonInsertText.Visible = bHasADoc;
			this.buttonInsertBarcode.Visible = bHasADoc;

			if (activeView != null)
			{
				double dScale = activeView.ViewScale;
				this.menuItemZoom50.Checked  = (dScale == 0.50);
				this.menuItemZoom75.Checked  = (dScale == 0.75);
				this.menuItemZoom100.Checked = (dScale == 1.00);
				this.menuItemZoom150.Checked = (dScale == 1.50);
				this.menuItemZoom200.Checked = (dScale == 2.00);
				this.menuItemZoom300.Checked = (dScale == 3.00);
				this.menuItemZoom400.Checked = (dScale == 4.00);
			}
		}

		//App closing handler
		private void ClosingMainAppHander(Object sender,CancelEventArgs e)
		{
			//if (DCSDEV.DCSMsg.ShowOKC("Do you want to exit DCSDesigner?") == DialogResult.Cancel)
			//{
			// 	e.Cancel = true;
			// 	return;
			//}

/*			// check each doc
			if (this.MdiChildren.Length > 0)
			{
				foreach (DCSDesignerView view in this.MdiChildren)
				{
					DCSDesignerDoc doc = view.GetDocument();
					if (doc.m_isDirty)
					{
						while (true)
						{
							DialogResult result = DCSDEV.DCSMsg.ShowYN(String.Format("Do you want to save the changes to design '{0}' ?", doc.m_strDesignName));
							if (result == DialogResult.Yes)
							{
								if (doc.m_strDesignName == null || doc.m_strDesignName.StartsWith("DCSDesign"))
								{
									string strDesignName = DCSDEV.DCSDesignDataAccess.SelectSaveDesignName(doc.m_strDesignName);
									if (strDesignName == null) 
									{
										continue;
									}
									doc.m_strDesignName = strDesignName;
								}
								doc.SaveDocument(doc.m_strDesignName);
							}
							else // if (result == DialogResult.No)
							{
								doc.m_isDirty = false;
							}
							break;
						}
					}
					continue;
				}
			}
*/

			if (this.MdiChildren.Length > 0)
			{
				DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
				if (activeView != null)
				{
					DCSDesignerDoc activeDoc = activeView.GetDocument();		
					m_ps.WriteStringParameter("CurrentDesignName", activeDoc.m_strDesignName);
				}
			}
			m_ps.WriteRectParameter("CurrentBounds", this.Bounds);

			//write grid parameters
			m_ps.WriteBoolParameter("TabAlignOn", this.m_bTabAlignOn);
			m_ps.WriteBoolParameter("GridOn", this.m_bGridSnapOn);
			m_ps.WriteBoolParameter("AutoAlign", this.m_bAutoAlign);

			foreach (DCSDesignerView view in this.MdiChildren)
			{
				view.Close();
			}
		}

		/*
		 * The main entry point for the application.
		 *
		 */
		[STAThread]
		public static void Main(string[] args) 
		{
			Application.EnableVisualStyles();
			Application.DoEvents();
			// Allow only one instance of this program

			// FindWindow only works if the title is not changed dynamically -
			// and multi doc windows have doc name added.  
			// ID Server used this method, and it would fail sometimes because of this restriction.
			//    if ((IntPtr hWnd = FindWindow(null, "DCSDesigner")) != 0) 
			//    {     SetForegroundWindow(hWnd); return; }

			System.Diagnostics.Process[] procs = System.Diagnostics.Process.GetProcessesByName(Application.ProductName);
			if (procs.Length > 1) // This code should prevent the number from ever getting above 2.
			{
				// the previously running instance will be at either index 0 or 1
				int index;
				if ((int)procs[0].MainWindowHandle != 0) index = 0;
				else index = 1;
				SetForegroundWindow(procs[index].MainWindowHandle);
				//ShowWindow is necessary to restore a minimized window - I think I want to leave it the way the opr left it.
				//ShowWindow(procs[index].MainWindowHandle, 9);
				return;
			}
			Application.Run(new DCSDesignerMain());
		}

		private void menuItemEditBadgeDesign_Click(object sender, System.EventArgs e)
		{
			EditBadgeBackground(false);		// bNew=false
		}
		private void menuItemInsertHandler(object sender, System.EventArgs e)
		{
			DCSDEV.DCSDatatypes.DCSDesignObjectTypes type;
			if (sender == this.menuItemInsertText) type = DCSDEV.DCSDatatypes.DCSDesignObjectTypes.TextObj;
			else if (sender == this.menuItemInsertImage) type = DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ImageObj;
			else if (sender == this.menuItemInsertPortrait) type = DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Portrait;
			else if (sender == this.menuItemInsertSig) type = DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Signature;
			else if (sender == this.menuItemInsertFinger) type = DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Fingerprint;
			else if (sender == this.menuItemInsertBarcode2D) type = DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode2D;
			else if (sender == this.menuItemInsertBarcode) type = DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode;
			else if (sender == this.menuItemInsertICAO) type = DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ICAOMRZ;
			else if (sender == this.menuItemInsertGraphicBlock) type = DCSDEV.DCSDatatypes.DCSDesignObjectTypes.GraphicBlock;
			else type = DCSDEV.DCSDatatypes.DCSDesignObjectTypes.TextObj;

			InsertCommon(type);
		}

		/***********************************************
		************************************************/
		private void InsertCommon(DCSDEV.DCSDatatypes.DCSDesignObjectTypes designObjectType)
		{
			System.Windows.Forms.DialogResult result;
			DCSDEV.DCSDesign.DCSDesignObject designObject;
			DCSDEV.DCSDesign.DCSDesignObject designObjectSelected = null;
			bool bSelectedObjectHasSameType = false;
			DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
			if (activeView == null) return;
			DCSDesignerDoc activeDoc = activeView.GetDocument();

			if (activeView.m_designObjectsSelected.Count > 0)
			{
				designObjectSelected = (DCSDEV.DCSDesign.DCSDesignObject)(activeView.m_designObjectsSelected[0]);
				if (designObjectSelected.DCSDesignObjectType == designObjectType) bSelectedObjectHasSameType = true;
			}

			if (bSelectedObjectHasSameType)
			{
				designObject = designObjectSelected.CloneObject(true);		// bCreating=true

				// try stacking objects adjacent to each other
				Point pointNew = designObject.Bounds.Location;
				int extraSpace = 0;
				if (designObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.GraphicBlock)
					extraSpace = 10;
				if (designObject.Bounds.Width < designObject.Bounds.Height)	// stack left to right 
					pointNew.X = extraSpace + designObjectSelected.Bounds.X + designObjectSelected.Bounds.Width;
				else											// stack top to bottom
					pointNew.Y = extraSpace + designObjectSelected.Bounds.Y + designObjectSelected.Bounds.Height;

				// if going outside the doc bounds then cascade down and right 
				if ((pointNew.X + designObject.Bounds.Width > activeDoc.Bounds.Width)
				||  (pointNew.Y + designObject.Bounds.Height > activeDoc.Bounds.Height))
					pointNew = designObjectSelected.Bounds.Location + new Size(10,10);
				designObject.Bounds.Location = pointNew;
			}
			else
			{
				designObject = new DCSDEV.DCSDesign.DCSDesignObject(designObjectType);
				designObject.Side = activeView.CurrrentSide;
				// locate cascading down and right from last insert
				designObject.Bounds.Location = m_pointLastInsert + new Size(10,10);
				
				// set certain font properties from document defaults 
				if (designObjectType == DCSDatatypes.DCSDesignObjectTypes.TextObj)
				{
					designObject.LabelFontIndex = activeDoc.PriorLabelFontIndex;
					designObject.LabelOffset = activeDoc.PriorLabelOffset;
					designObject.LabelOrientation = activeDoc.PriorLabelOrientation;

					designObject.Bounds.Height = activeDoc.PriorBoundsHeight;
					designObject.FontIndex = activeDoc.PriorFontIndex;
					designObject.FontEx = activeDoc.PriorFontEx.Clone();
					designObject.Alignment = activeDoc.PriorAlignment;
					designObject.Justification = activeDoc.PriorJustification;
				}
			}
			// revert to origin if out of bounds
			if (designObject.Bounds.X + designObject.Bounds.Width > activeDoc.Bounds.Width)
				designObject.Bounds.X = 0;
			if (designObject.Bounds.Y + designObject.Bounds.Height > activeDoc.Bounds.Height)
				designObject.Bounds.Y = 0;
			// remember last insert location
			m_pointLastInsert = designObject.Bounds.Location; 

			// dialogs need list of fields
			ArrayList list = new ArrayList();
			list.Add(designObject);

			// call object properties dialog based on object type
			result = System.Windows.Forms.DialogResult.Cancel;
			switch(designObjectType)
			{
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.TextObj:
					using (DCSDEV.DCSDesigner.TextObjProperties dlg = new DCSDEV.DCSDesigner.TextObjProperties(activeDoc, activeView, list, true /*bNew*/))
					{
						result = dlg.ShowDialog(this);
						if (result != DialogResult.Cancel)
						{
							if (designObject.LabelOn)
							{
								activeDoc.PriorLabelFontIndex = designObject.LabelFontIndex;
								activeDoc.PriorLabelOffset = designObject.LabelOffset;
								activeDoc.PriorLabelOrientation = designObject.LabelOrientation;
							}
							activeDoc.PriorBoundsHeight = designObject.Bounds.Height;
							activeDoc.PriorFontIndex = designObject.FontIndex;
							activeDoc.PriorFontEx = designObject.FontEx.Clone();
							activeDoc.PriorAlignment = designObject.Alignment;
							activeDoc.PriorJustification = designObject.Justification;
						}
					}
					break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode:
                    using (DCSDEV.DCSDesigner.TextObjProperties dlg = new DCSDEV.DCSDesigner.TextObjProperties(activeDoc, activeView, list, true /*bNew*/))
                    {
                        result = dlg.ShowDialog(this);
                    }
					break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode2D:
					if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.TwoDBarcodes, true))
					{
                        using (DCSDEV.DCSDesigner.TextObjProperties dlg = new DCSDEV.DCSDesigner.TextObjProperties(activeDoc, activeView, list, true /*bNew*/))
                        {
                            result = dlg.ShowDialog(this);
                        }
					}
					else result = DialogResult.Cancel;
					break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ImageObj:
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Portrait:
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Signature:
                    using (DCSDEV.DCSDesigner.ImageObjProperties imgdlg = new DCSDEV.DCSDesigner.ImageObjProperties(activeDoc, activeView, list, true /*bNew*/))
                    {
                        result = imgdlg.ShowDialog(this);
                    }
                    break;
                case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Fingerprint:
                    if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.FingerprintMgt, true))
                    {
                        using (DCSDEV.DCSDesigner.ImageObjProperties imgdlg = new DCSDEV.DCSDesigner.ImageObjProperties(activeDoc, activeView, list, true /*bNew*/))
                        {
                            result = imgdlg.ShowDialog(this);
                        }
                    }
                    else result = DialogResult.Cancel;
                    break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ICAOMRZ:
					if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.IcaoMRZ, true))
					{
                        using (DCSDEV.DCSDesigner.IcaoObjProperties icaodlg = new DCSDEV.DCSDesigner.IcaoObjProperties(activeDoc, activeView, list, true /*bNew*/))
                        {
                            result = icaodlg.ShowDialog(this);
                        }
					}
					else result = DialogResult.Cancel;
					break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.GraphicBlock:
                    using (DCSDEV.DCSDesigner.DrawingObjProperties drawingdlg = new DCSDEV.DCSDesigner.DrawingObjProperties(activeDoc, activeView, list, true /*bNew*/))
                    {
                        result = drawingdlg.ShowDialog(this);
                    }
					break;
				default:
					break;
			}
			if (result != System.Windows.Forms.DialogResult.OK) 
			{
				activeView.m_designObjectsSelected.Clear();
				this.EnableGUIItems();
				designObject.Dispose();
				this.Invalidate();
				return;
			}

			// set list of selected objects in prep for editing properties and other further interaction
			activeView.m_designObjectsSelected.Clear();
			activeView.m_designObjectsSelected.Add(designObject);

			DCSDEV.DCSDesign.DCSDesignSide designSide;
			designSide = (DCSDEV.DCSDesign.DCSDesignSide)activeDoc.m_designSides[activeView.CurrrentSide];
			designSide.m_DCSDesignObjects.Add(designObject);
			activeDoc.m_UndoEventClass.Add(activeView.CurrrentSide, UndoType.Insert, designObject, true);

			activeDoc.m_isDirty = true;
			activeDoc.m_isViewDirty = true;
			activeView.Invalidate(true);
			this.EnableGUIItems();
			return;
		}

		private void menuItemEditObject_Click(object sender, System.EventArgs e)
		{
			System.Windows.Forms.DialogResult result;
			DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
			if (activeView == null) return;
//syh			if (activeView.m_designObjectsSelected.Count != 1) return;
			DCSDesignerDoc activeDoc = activeView.GetDocument();
			activeDoc.m_UndoEventClass.Add(activeView.CurrrentSide, UndoType.PreEdit, (DCSDEV.DCSDesign.DCSDesignObject)activeView.m_designObjectsSelected[0], true);
			switch(((DCSDEV.DCSDesign.DCSDesignObject)activeView.m_designObjectsSelected[0]).DCSDesignObjectType)
			{
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.TextObj:
					using (DCSDEV.DCSDesigner.TextObjProperties dlg = new DCSDEV.DCSDesigner.TextObjProperties(activeDoc, activeView, activeView.m_designObjectsSelected, false /*bNew*/))
					{
						result = dlg.ShowDialog(this);
						if (result != DialogResult.Cancel)
						{
							DCSDEV.DCSDesign.DCSDesignObject dobj = (DCSDEV.DCSDesign.DCSDesignObject)activeView.m_designObjectsSelected[0];
							if (dobj.LabelOn)
							{
								activeDoc.PriorLabelFontIndex = dobj.LabelFontIndex;
								activeDoc.PriorLabelOffset = dobj.LabelOffset;
								activeDoc.PriorLabelOrientation = dobj.LabelOrientation;
							}
							activeDoc.PriorBoundsHeight = dobj.Bounds.Height;
							activeDoc.PriorFontIndex = dobj.FontIndex;
							activeDoc.PriorFontEx = dobj.FontEx.Clone();
							activeDoc.PriorAlignment = dobj.Alignment;
							activeDoc.PriorJustification = dobj.Justification;
						}
					}
					break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode2D:
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode:
                    using (DCSDEV.DCSDesigner.TextObjProperties dlg = new DCSDEV.DCSDesigner.TextObjProperties(activeDoc, activeView, activeView.m_designObjectsSelected, false /*bNew*/))
                    {
                        result = dlg.ShowDialog(this);
                    }
					break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ImageObj:
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Portrait:
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Signature:
                    using (DCSDEV.DCSDesigner.ImageObjProperties dlgimg = new DCSDEV.DCSDesigner.ImageObjProperties(activeDoc, activeView, activeView.m_designObjectsSelected, false /*bNew*/))
                    {
                        result = dlgimg.ShowDialog(this);
                    }
					break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ICAOMRZ:
                    using (DCSDEV.DCSDesigner.IcaoObjProperties icaodlg = new DCSDEV.DCSDesigner.IcaoObjProperties(activeDoc, activeView, activeView.m_designObjectsSelected, false /*bNew*/))
                    {
                        result = icaodlg.ShowDialog(this);
                    }
					break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.GraphicBlock:
                    using (DCSDEV.DCSDesigner.DrawingObjProperties drawingdlg = new DCSDEV.DCSDesigner.DrawingObjProperties(activeDoc, activeView, activeView.m_designObjectsSelected, false /*bNew*/))
                    {
                        result = drawingdlg.ShowDialog(this);
                    }
					break;
				default:
					return;
			}
			if (result != DialogResult.Cancel)
			{
				activeDoc.m_UndoEventClass.Add(activeView.CurrrentSide, UndoType.Edit, (DCSDEV.DCSDesign.DCSDesignObject)activeView.m_designObjectsSelected[0], true);
				activeDoc.m_isDirty = true;
				activeDoc.m_isViewDirty = true;
				activeView.Invalidate(true);
			}
		}

		private void MenuItemHandler_SelectedItemClick(object sender, System.EventArgs e)
		{
			DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
			if (activeView == null) return;
			if (activeView.m_designObjectsSelected.Count == 0) return;
			DCSDesignerDoc activeDoc = activeView.GetDocument();
			DCSDEV.DCSDesign.DCSDesignSide designSide = (DCSDEV.DCSDesign.DCSDesignSide)activeDoc.m_designSides[activeView.CurrrentSide];
			bool bFirst = true;
			foreach(DCSDEV.DCSDesign.DCSDesignObject designObject in activeView.m_designObjectsSelected)
			{
				if (sender == this.menuItemEditBack)
				{
					// find object in side list and put it at top of list
					activeDoc.m_UndoEventClass.Add(activeView.CurrrentSide, UndoType.Delete, designObject, bFirst);
					bFirst = false;
					designSide.m_DCSDesignObjects.Remove(designObject);
					designSide.m_DCSDesignObjects.Insert(0, designObject);
					activeDoc.m_UndoEventClass.Add(activeView.CurrrentSide, UndoType.Insert, designObject, false);
				}
				else if (sender == this.menuItemEditFront)
				{
					// find object in side list and put it at end of list
					activeDoc.m_UndoEventClass.Add(activeView.CurrrentSide, UndoType.Delete, designObject, bFirst);
					bFirst = false;
					designSide.m_DCSDesignObjects.Remove(designObject);
					designSide.m_DCSDesignObjects.Add(designObject);
					activeDoc.m_UndoEventClass.Add(activeView.CurrrentSide, UndoType.Insert, designObject, false);
				}
				else if (sender == this.menuItemEditDelete)
				{
					// find object in side list and delete it
					activeDoc.m_UndoEventClass.Add(activeView.CurrrentSide, UndoType.Delete, designObject, bFirst);
					bFirst = false;
					designSide.m_DCSDesignObjects.Remove(designObject);
				}
			}
			if (sender == this.menuItemEditDelete)
				activeView.m_designObjectsSelected.Clear();

			activeDoc.m_isDirty = true;
			activeDoc.m_isViewDirty = true;
			activeView.Invalidate(true);
			this.EnableGUIItems();
		}

		private void MenuItemZoomClick(object sender, System.EventArgs e)
		{
			DCSDesignerView activeView = (DCSDesignerView)this.ActiveMdiChild;
			if (activeView == null) return;
			DCSDesignerDoc activeDoc = activeView.GetDocument();

			double dScale = 1.0;
			if (sender == this.menuItemZoom50) 
			{ dScale = 0.50; this.menuItemZoom50.Checked = true; } 
			else this.menuItemZoom50.Checked = false; 
			if (sender == this.menuItemZoom75) 
			{ dScale = 0.75; this.menuItemZoom75.Checked = true; } 
			else this.menuItemZoom75.Checked = false; 
			if (sender == this.menuItemZoom100) 
			{ dScale = 1.00; this.menuItemZoom100.Checked = true; } 
			else this.menuItemZoom100.Checked = false; 
			if (sender == this.menuItemZoom150) 
			{ dScale = 1.50; this.menuItemZoom150.Checked = true; } 
			else this.menuItemZoom150.Checked = false; 
			if (sender == this.menuItemZoom200) 
			{ dScale = 2.00; this.menuItemZoom200.Checked = true; } 
			else this.menuItemZoom200.Checked = false; 
			if (sender == this.menuItemZoom300) 
			{ dScale = 3.00; this.menuItemZoom300.Checked = true; } 
			else this.menuItemZoom300.Checked = false; 
			if (sender == this.menuItemZoom400) 
			{ dScale = 4.00; this.menuItemZoom400.Checked = true; } 
			else this.menuItemZoom400.Checked = false; 

			activeView.ViewScale = dScale;
			activeView.SetPictureBoxSize(DCSMath.TimesDouble(activeDoc.Bounds.Size, activeView.ViewScale));
			activeView.SetViewTitleBar();

			activeDoc.m_isViewDirty = true;
			activeView.Invalidate(true);
		}

		private void DCSDesignerMain_MdiChildActivate(object sender, System.EventArgs e)
		{
			this.EnableGUIItems();
		}

		private void menuItemEditPrinterTypes_Click(object sender, System.EventArgs e)
		{
            using (DCSDEV.PrintProperties.BadgingMgtForm printProp = new DCSDEV.PrintProperties.BadgingMgtForm())
            {
                printProp.ShowDialog();
            }
            this.m_printertypeArray.Clear();
            this.m_printertypeArray.LoadPrinterTypeArray();
        }

		public string StatusBarText
		{
			get { return this.statusBar1.Text; }
			set { this.statusBar1.Text = value; }
		}
	}
}
