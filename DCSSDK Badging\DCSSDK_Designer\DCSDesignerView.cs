using System;
using System.IO ;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;

using DCSDEV;
using DCSDEV.DCSDesign;

namespace DCSDEV.DCSDesigner
{
	/// <summary>
	///    Summary description for Form2.
	/// </summary>
    internal class DCSDesignerView : System.Windows.Forms.Form
	{
		private enum ClickedHandle {None, TopLeft, TopRight, BottomRight, BottomLeft, Left, Top, Right, Bottom, Interior, InteriorSelected};

		private DCSDesignerDoc m_doc;
		internal DCSDesignerMain m_mainWin;
		public string m_strViewName;
		private int m_iCurrrentSide = 0;
		private double m_dCurrentScale = 2.0;

		private bool m_AutoAlignFoundX;	// for bAutoAlign
		private bool m_AutoAlignFoundY;	// for bAutoAlign
		private Point m_pointAutoAlign1X;
		private Point m_pointAutoAlign2X;
		private Point m_pointAutoAlign1Y;
		private Point m_pointAutoAlign2Y;

		public ArrayList m_designObjectsSelected;
		
		private int m_iLastClickIndex = 0;
		private Point m_pointDragStart = new Point(0,0);
		private Rectangle m_rectDragStart = new Rectangle(0,0,0,0);
		private bool m_bMouseIsDown = false;
		private ClickedHandle m_clickedHandle = ClickedHandle.None;
		private bool m_bMultiStretch;
		private Rectangle m_rectDragCurrent;	// in view scale
		private bool m_bNeedsInvaldate = false;
		private Cursor m_cursorSave;
		private int m_iLastImageTabIndex = 0;
		private int m_iLastTextTabIndex = 0;
		private int m_iLastICAOTabIndex = 0;

		private System.Windows.Forms.PictureBox pictureBox1;
		private System.Drawing.Pen m_penHighlight;
		private Bitmap m_bitmapView = null;
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		public DCSDesignerView(DCSDesignerDoc doc, DCSDesignerMain parent)
		{
			//
			// Required for Win Form Designer support
			//
			InitializeComponent();
			m_doc  = doc;
			this.MdiParent = parent; //Make this view Mdi child of the main window
			m_mainWin = parent;

			m_designObjectsSelected = new ArrayList();
			m_penHighlight = new Pen(System.Drawing.Color.Red, 2);
			m_cursorSave = this.Cursor;
			m_strViewName = m_doc.m_strDesignName;
		}

		/// <summary>
		///    Clean up any resources being used
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if (components != null) 
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		/// <summary>
		///    Required method for Designer support - do not modify
		///    the contents of this method with the code editor
		/// </summary>
		private void InitializeComponent()
		{
			this.pictureBox1 = new System.Windows.Forms.PictureBox();
			this.SuspendLayout();
			// 
			// pictureBox1
			// 
			this.pictureBox1.Enabled = false;
			this.pictureBox1.Location = new System.Drawing.Point(0, 0);
			this.pictureBox1.Name = "pictureBox1";
			this.pictureBox1.Size = new System.Drawing.Size(152, 96);
			this.pictureBox1.TabIndex = 0;
			this.pictureBox1.TabStop = false;
			this.pictureBox1.Paint += new System.Windows.Forms.PaintEventHandler(this.pictureBox1_Paint);
			// 
			// DCSDesignerView
			// 
			this.AutoScaleBaseSize = new System.Drawing.Size(5, 13);
			this.AutoScroll = true;
			this.BackColor = System.Drawing.Color.White;
			this.ClientSize = new System.Drawing.Size(344, 214);
			this.Controls.Add(this.pictureBox1);
			this.Name = "DCSDesignerView";
			this.Text = "DCSDesign";
			this.KeyDown += new System.Windows.Forms.KeyEventHandler(this.DCSDesignerView_KeyDown);
			this.MouseDown += new System.Windows.Forms.MouseEventHandler(this.DCSDesignerView_MouseDown);
			this.Closing += new System.ComponentModel.CancelEventHandler(this.ClosingHandler);
			this.DoubleClick += new System.EventHandler(this.DCSDesignerView_DoubleClick);
			this.MouseUp += new System.Windows.Forms.MouseEventHandler(this.DCSDesignerView_MouseUp);
			this.Activated += new System.EventHandler(this.DCSDesignerView_Activated);
			this.MouseMove += new System.Windows.Forms.MouseEventHandler(this.DCSDesignerView_MouseMove);
			this.ResumeLayout(false);
		}

		private bool AreAllTypesTheSame()
		{
			if (m_designObjectsSelected.Count <= 0) return false;
			if (m_designObjectsSelected.Count == 1) return true;
			DCSDEV.DCSDesign.DCSDesignObject designFirstObject = (DCSDEV.DCSDesign.DCSDesignObject)m_designObjectsSelected[0];

			foreach (DCSDEV.DCSDesign.DCSDesignObject designObject in m_designObjectsSelected)
			{
				if (designObject.DCSDesignObjectType != designFirstObject.DCSDesignObjectType) return false;
			}
			return true;
		}

		private void ClosingHandler(Object sender, CancelEventArgs e)
		{
			if(m_doc.m_isDirty)
			{
				DialogResult result = DCSDEV.DCSMsg.ShowYN(String.Format("Do you want to save the changes to design '{0}' ?", this.m_strViewName));	//m_doc.m_strDesignName));
				if (result == DialogResult.Yes)
				{
					if (m_doc.m_strDesignName == null || m_doc.m_strDesignName.StartsWith("DCSDesign"))
					{
						string strDesignName = DCSDEV.DCSDesignDataAccess.SelectSaveDesignName(m_doc.m_strDesignName);
						if (strDesignName == null) 
						{
							e.Cancel = true; //If user selected 'Cancel', don't close the form
							return;
						}
						m_doc.m_strDesignName = strDesignName;
					}
					m_doc.SaveDocument(m_doc.m_strDesignName);
					this.MdiParent = null; // remove this view(child) from the parent list
				}
				else // if (result == DialogResult.No)
				{
					m_doc.m_isDirty = false;
				}				
			}
		}

		private void CallPropertiesDlg(ArrayList designObjectsSelected)
		{
			DCSDEV.DCSDesign.DCSDesignObject designObject;
			System.Windows.Forms.DialogResult result;
			
			try
			{
				if (designObjectsSelected.Count > 0)
				{
					designObject = (DCSDEV.DCSDesign.DCSDesignObject)designObjectsSelected[0];
					m_doc.m_UndoEventClass.Add(m_iCurrrentSide, UndoType.PreEdit, designObject, true);	// new group is true
					switch(designObject.DCSDesignObjectType)
					{
						case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.TextObj:
							using (DCSDEV.DCSDesigner.TextObjProperties dlg = new DCSDEV.DCSDesigner.TextObjProperties(this.m_doc, this, this.m_designObjectsSelected, false /*bNew*/))
							{
								dlg.LastTab = m_iLastTextTabIndex;
								result = dlg.ShowDialog(this);
								m_iLastTextTabIndex = dlg.LastTab;
								if (result != DialogResult.Cancel)
								{
									if (designObject.LabelOn)
									{
										m_doc.PriorLabelFontIndex = designObject.LabelFontIndex;
										m_doc.PriorLabelOffset = designObject.LabelOffset;
										m_doc.PriorLabelOrientation = designObject.LabelOrientation;
									}
									m_doc.PriorBoundsHeight = designObject.Bounds.Height;
									m_doc.PriorFontIndex = designObject.FontIndex;
									m_doc.PriorFontEx = designObject.FontEx.Clone();
									m_doc.PriorAlignment = designObject.Alignment;
									m_doc.PriorJustification = designObject.Justification;
								}
							}
							break;
						case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode2D:
						case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode:
							using (DCSDEV.DCSDesigner.TextObjProperties dlg = new DCSDEV.DCSDesigner.TextObjProperties(this.m_doc, this, this.m_designObjectsSelected, false /*bNew*/))
                            {
                                dlg.LastTab = m_iLastTextTabIndex;
                                result = dlg.ShowDialog(this);
                                m_iLastTextTabIndex = dlg.LastTab;
                            }
							break;
						case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ImageObj:
						case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Portrait:
						case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Signature:
						case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Fingerprint:
                            using (DCSDEV.DCSDesigner.ImageObjProperties dlgimg = new DCSDEV.DCSDesigner.ImageObjProperties(this.m_doc, this, this.m_designObjectsSelected, false /*bNew*/))
                            {
                                dlgimg.LastTab = m_iLastImageTabIndex;
                                result = dlgimg.ShowDialog(this);
                                m_iLastImageTabIndex = dlgimg.LastTab;
                            }
							break;
						case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ICAOMRZ:
                            using (DCSDEV.DCSDesigner.IcaoObjProperties dlgicao = new DCSDEV.DCSDesigner.IcaoObjProperties(this.m_doc, this, this.m_designObjectsSelected, false /*bNew*/))
                            {
                                dlgicao.LastTab = m_iLastICAOTabIndex;
                                result = dlgicao.ShowDialog(this);
                                m_iLastICAOTabIndex = dlgicao.LastTab;
                            }
							break;
						case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.GraphicBlock:
                            using (DCSDEV.DCSDesigner.DrawingObjProperties drawingdlg = new DCSDEV.DCSDesigner.DrawingObjProperties(this.m_doc, this, this.m_designObjectsSelected, false /*bNew*/))
                            {
                                result = drawingdlg.ShowDialog(this);
                            }
							break;
						default:
							return;
					}
					if (result != DialogResult.Cancel)
					{
						m_doc.m_UndoEventClass.Add(m_iCurrrentSide, UndoType.Edit, designObject, true);
						m_doc.m_isDirty = true;
						m_doc.m_isViewDirty = true;
						SetSelectStatusText();
						this.Invalidate(true);
					}
				}
				else
				{
                    using (DCSDEV.DCSDesigner.BadgeDesignProperties dlg = new DCSDEV.DCSDesigner.BadgeDesignProperties(m_doc, this, m_iCurrrentSide, false))
                    {
                        result = dlg.ShowDialog(this);
                    }
					if (result != DialogResult.Cancel)
					{
						//number of sides may have changed - make sure still displaying an existing side.
						if (this.m_iCurrrentSide < this.m_doc.NumBadgeSides - 1)
							this.m_iCurrrentSide = 0;

						this.SetViewTitleBar();
						m_doc.m_isDirty = true;
						m_doc.m_isViewDirty = true;

						this.SetPictureBoxSize(DCSMath.TimesDouble(m_doc.Bounds.Size, this.ViewScale));

						SetSelectStatusText();
						this.Invalidate(true);
						this.m_mainWin.EnableGUIItems();
					}
				}
			}
			catch(Exception ex)
			{
				// just incase something goes wrong in any of the dialogs
				DCSDEV.DCSMsg.Show("ERROR: in design dialog.", ex);
			}
		}

		public DCSDesignerDoc GetDocument()
		{
			return m_doc;
		}	

		// get first object that contains the point
		private DCSDEV.DCSDesign.DCSDesignObject GetIncludingObject(Point point)
		{
			ArrayList arrayDesignObjects = ((DCSDesignSide)m_doc.m_designSides[m_iCurrrentSide]).m_DCSDesignObjects;
			if (arrayDesignObjects == null || arrayDesignObjects.Count == 0) return null;
			int len = arrayDesignObjects.Count;
			for (int i=0; i<len; i++)
			{
				DCSDesignObject designObject = (DCSDesignObject)arrayDesignObjects[i];
				Rectangle rect = DCSDEV.DCSMath.RectTimesDouble(designObject.Bounds, m_dCurrentScale);
				if (rect.Contains(point))
				{
					return designObject;
				}
			}
			return null;
		}
		// get next object that contains the point
		private DCSDEV.DCSDesign.DCSDesignObject GetNextClickedObject(Point point)
		{
			ArrayList arrayDesignObjects = ((DCSDesignSide)m_doc.m_designSides[m_iCurrrentSide]).m_DCSDesignObjects;
			if (arrayDesignObjects == null || arrayDesignObjects.Count == 0) return null;
			int len = arrayDesignObjects.Count;
			if (m_iLastClickIndex >= len) m_iLastClickIndex = 0;
			for (int i=1; i<=len; i++)
			{
				DCSDesignObject designObject = (DCSDesignObject)arrayDesignObjects[(i+m_iLastClickIndex)%len];
				Rectangle rect = DCSDEV.DCSMath.RectTimesDouble(designObject.Bounds, m_dCurrentScale);
				if (rect.Contains(point))
				{
					m_iLastClickIndex = (i+m_iLastClickIndex)%len;
					return designObject;
				}
			}
			return null;
		}
		private ClickedHandle GetClickedObjectHandle(Point point, out DCSDesignObject designObjectClicked)
		{
			ClickedHandle clicked;
			foreach (DCSDesignObject designObject in m_designObjectsSelected)
			{
				clicked = GetClickedObjectHandle(point, designObject);
				if (clicked != ClickedHandle.None)
				{
					designObjectClicked = designObject;
					return clicked;
				}
			}
			designObjectClicked = null;
			return ClickedHandle.None;
		}
		private ClickedHandle GetClickedObjectHandle(Point point, DCSDesignObject designObject)
		{
			Rectangle rectNet = DCSDEV.DCSMath.RectTimesDouble(designObject.Bounds, m_dCurrentScale);
			Rectangle rectBox = new Rectangle(-5, -5, 10, 10);
			Rectangle rect;

			rect = rectBox;
			rect.Offset(rectNet.Left, rectNet.Top);
			if (rect.Contains(point)) return ClickedHandle.TopLeft;
			rect = rectBox;
			rect.Offset(rectNet.Right, rectNet.Top);
			if (rect.Contains(point)) return ClickedHandle.TopRight;
			rect = rectBox;
			rect.Offset(rectNet.Left, rectNet.Bottom);
			if (rect.Contains(point)) return ClickedHandle.BottomLeft;
			rect = rectBox;
			rect.Offset(rectNet.Right, rectNet.Bottom);
			if (rect.Contains(point)) return ClickedHandle.BottomRight;

			if (!designObject.IfLockAspect)
			{
				rect = rectBox;
				rect.Offset(rectNet.Right, (rectNet.Bottom + rectNet.Top) / 2);
				if (rect.Contains(point)) return ClickedHandle.Right;
				rect = rectBox;
				rect.Offset((rectNet.Left + rectNet.Right) / 2, rectNet.Top);
				if (rect.Contains(point)) return ClickedHandle.Top;
				rect = rectBox;
				rect.Offset(rectNet.Left, (rectNet.Bottom + rectNet.Top) / 2);
				if (rect.Contains(point)) return ClickedHandle.Left;
				rect = rectBox;
				rect.Offset((rectNet.Left + rectNet.Right) / 2, rectNet.Bottom);
				if (rect.Contains(point)) return ClickedHandle.Bottom;
			}
			if (rectNet.Contains(point)) return ClickedHandle.InteriorSelected;
			else return ClickedHandle.None;
		}

		public void SetPictureBoxSize(Size size)
		{
			Size sizeAdj = size;
			// doc design bounds are pre adjusted for the orientation of front of document
			// To display second side swap h and w if back orientation is not same as front
			SwapIfSideLandscapeDiffers(ref sizeAdj);

			this.pictureBox1.Size = sizeAdj;
			this.SetClientSizeCore(sizeAdj.Width, sizeAdj.Height);
		}
		private Rectangle SnapGridLines(Rectangle rect)
		{
			int iDist;
			int iMinDist;
			int minCoord = 0;
			Rectangle rectOut = rect;

			if (m_doc.m_arrayTabStopsH != null)
			{
				int iStepX = m_doc.m_GridSpacingX;
				bool bMinIsLeft = true;
				iMinDist = -1;
				for (int i = 0; i < m_doc.m_arrayTabStopsH.Count; i++)
				{
					iDist = Math.Abs((int)m_doc.m_arrayTabStopsH[i] - rect.Left);
					if (iDist < iStepX && (iMinDist < 0 || iDist < iMinDist))
					{
						iMinDist = iDist;
						minCoord = (int)m_doc.m_arrayTabStopsH[i];
						bMinIsLeft = true;
					}
					iDist = Math.Abs((int)m_doc.m_arrayTabStopsH[i] - rect.Right);
					if (iDist < iStepX && (iMinDist < 0 || iDist < iMinDist))
					{
						iMinDist = iDist;
						minCoord = (int)m_doc.m_arrayTabStopsH[i];
						bMinIsLeft = false;
					}
				}
				if (iMinDist != -1)
				{
					if (bMinIsLeft) rectOut.X = minCoord;
					else rectOut.X = minCoord - rect.Width;
				}
			}
			if (m_doc.m_arrayTabStopsV != null)
			{
				int iStepY = m_doc.m_GridSpacingY;
				bool bMinIsTop = true;
				iMinDist = -1;
				for (int i = 0; i < m_doc.m_arrayTabStopsV.Count; i++)
				{
					iDist = Math.Abs((int)m_doc.m_arrayTabStopsV[i] - rect.Top);
					if (iDist < iStepY && (iMinDist < 0 || iDist < iMinDist))
					{
						iMinDist = iDist;
						minCoord = (int)m_doc.m_arrayTabStopsV[i];
						bMinIsTop = true;
					}
					iDist = Math.Abs((int)m_doc.m_arrayTabStopsV[i] - rect.Bottom);
					if (iDist < iStepY && (iMinDist < 0 || iDist < iMinDist))
					{
						iMinDist = iDist;
						minCoord = (int)m_doc.m_arrayTabStopsV[i];
						bMinIsTop = false;
					}
				}
				if (iMinDist != -1)
				{
					if (bMinIsTop) rectOut.Y = minCoord;
					else rectOut.Y = minCoord - rect.Height;
				}
			}
			return rectOut;
		}

		private int SnapGridX(int coord)
		{
			if (m_mainWin.m_bGridSnapOn)
			{
				int iStepX = m_doc.m_GridSpacingX;
				return ((coord + iStepX / 2) / iStepX) * iStepX;
			}
			else if (m_mainWin.m_bTabAlignOn && m_doc.m_arrayTabStopsH != null)
			{
				int iDist;
				int iMinDist;
				int minCoord = 0;
				int iRange = m_doc.m_TabRange;
				iMinDist = -1;
				for (int i = 0; i < m_doc.m_arrayTabStopsH.Count; i++)
				{
					iDist = Math.Abs((int)m_doc.m_arrayTabStopsH[i] - coord);
					if (iDist < iRange && (iMinDist < 0 || iDist < iMinDist))
					{
						iMinDist = iDist;
						minCoord = (int)m_doc.m_arrayTabStopsH[i];
					}
				}
				if (iMinDist != -1) coord = minCoord;
			}
			return coord;
		}
		private int SnapGridY(int coord)
		{
			if (m_mainWin.m_bGridSnapOn)
			{
				int iStepY = m_doc.m_GridSpacingY;
				return ((coord + iStepY / 2) / iStepY) * iStepY;
			}
			else if (m_mainWin.m_bTabAlignOn && m_doc.m_arrayTabStopsV != null)
			{
				int iDist;
				int iMinDist;
				int minCoord = 0;
				int iRange = m_doc.m_TabRange;
				iMinDist = -1;
				for (int i = 0; i < m_doc.m_arrayTabStopsV.Count; i++)
				{
					iDist = Math.Abs((int)m_doc.m_arrayTabStopsV[i] - coord);
					if (iDist < iRange && (iMinDist < 0 || iDist < iMinDist))
					{
						iMinDist = iDist;
						minCoord = (int)m_doc.m_arrayTabStopsV[i];
					}
				}
				if (iMinDist != -1) coord = minCoord;
			}
			return coord;
		}

		private void SwapIfSideLandscapeDiffers(ref Size size)
		{
			if (this.m_iCurrrentSide != 0)
			{
				bool bSide1IsLandscape = ((DCSDesignSide)(this.m_doc.m_designSides[0])).SideIsLandscape;
				bool bSide2IsLandscape = ((DCSDesignSide)(this.m_doc.m_designSides[1])).SideIsLandscape;
				if (bSide1IsLandscape != bSide2IsLandscape)
				{
					int temp = size.Height;
					size.Height = size.Width;
					size.Width = temp;
				}
			}
		}

		public void SetViewTitleBar()
		{
			if (this.m_doc.NumBadgeSides >= 2)
				this.Text = String.Format("{0} - {1}% - Side {2} of {3}", this.m_strViewName, (this.m_dCurrentScale*100.0).ToString(), (m_iCurrrentSide+1).ToString(), this.m_doc.NumBadgeSides.ToString());
			else
				this.Text = String.Format("{0} - {1}%", this.m_strViewName, (this.m_dCurrentScale*100.0).ToString());
		}

		// NOTE: I am drawing the badge in a picture box so that scrolling window work.
		// With a picture box, mouse events are picked up by it.
		private void pictureBox1_Paint(object sender, System.Windows.Forms.PaintEventArgs e)
		{
			//////////////////////////////////////////////////////////
			// Remake the bmTemp of the badge view if view is dirty //
			//////////////////////////////////////////////////////////

			// NOTE: could rip only portions inside  e.ClipRectangle
			if (m_doc.m_isViewDirty || m_bitmapView == null)
			{
				Cursor cursorSave = this.Cursor;
				try
				{
					this.Cursor = Cursors.WaitCursor;
					if (m_bitmapView != null) m_bitmapView.Dispose();
					m_doc.m_isViewDirty = false;
					m_bitmapView = new Bitmap(this.pictureBox1.Size.Width, this.pictureBox1.Size.Height);
					Graphics gr = Graphics.FromImage(m_bitmapView);
                    this.m_doc.RipSideToGDI(m_iCurrrentSide, gr, m_dCurrentScale, DCSDesign.DCSDesign.RipMode.RIPMODE_LAYOUT, true, true, true /* back fore text*/); 
					this.Cursor = cursorSave;
				}
				catch (Exception ex)
				{
					DCSDEV.DCSMsg.Show("ERROR calling RipSideToGDI", ex);
					this.Cursor = cursorSave;
				}
			}

			/////////////////////////////////////////////////////////////////////////////////////
			// Draw the image of the badge and the rectangles to indicate the selected objects //
			/////////////////////////////////////////////////////////////////////////////////////
			Rectangle rect;
			Rectangle rectNet = Rectangle.Empty;
			try
			{
				e.Graphics.DrawImage(m_bitmapView, 0, 0);

				// Draw grid
				if (this.m_mainWin.m_bGridSnapOn)
				{
					int iStepX = m_doc.m_GridSpacingX;
					int iStepY = m_doc.m_GridSpacingY;
					int i, j;
					int I, J;
					Pen penW = new Pen(Color.White);
					Pen penB = new Pen(Color.Blue);
					for (i = iStepX; i < m_doc.Bounds.Width; i += iStepX)
					{
						I = DCSDEV.DCSMath.TimesDouble(i, m_dCurrentScale);
						for (j = iStepY; j < m_doc.Bounds.Height; j += iStepY)
						{
							J = DCSDEV.DCSMath.TimesDouble(j, m_dCurrentScale);
							e.Graphics.DrawLine(penB, I, J, I, J+1);
							e.Graphics.DrawLine(penW, I + 1, J, I + 1, J+1);
						}
					}
				}
				else if (this.m_mainWin.m_bTabAlignOn)
				{
					Point p1 = Point.Empty;
					Point p2 = Point.Empty;
					Pen penW = new Pen(Color.White, 1);
					penW.DashStyle = System.Drawing.Drawing2D.DashStyle.DashDot;
					Pen penB = new Pen(Color.LightGray, 1);
					penB.DashStyle = System.Drawing.Drawing2D.DashStyle.DashDot;
					if (m_doc.m_arrayTabStopsH != null)
					{
						p1.Y = 0;
						p2.Y = DCSDEV.DCSMath.TimesDouble(m_doc.Bounds.Bottom, m_dCurrentScale);
						foreach (int coord in m_doc.m_arrayTabStopsH)
						{
							p2.X = p1.X = DCSDEV.DCSMath.TimesDouble(coord, m_dCurrentScale);
							e.Graphics.DrawLine(penB, p1, p2);
							p1.X++;
							p2.X++;
							e.Graphics.DrawLine(penW, p1, p2);
						}
					}
					if (m_doc.m_arrayTabStopsV != null)
					{
						p1.X = 0;
						p2.X = DCSDEV.DCSMath.TimesDouble(m_doc.Bounds.Right, m_dCurrentScale);
						foreach (int coord in m_doc.m_arrayTabStopsV)
						{
							p2.Y = p1.Y = DCSDEV.DCSMath.TimesDouble(coord, m_dCurrentScale);
							e.Graphics.DrawLine(penB, p1, p2);
							p1.Y++;
							p2.Y++;
							e.Graphics.DrawLine(penW, p1, p2);
						}
					}
				}

				foreach(DCSDesignObject designObject in m_designObjectsSelected)
				{
					rectNet = DCSDEV.DCSMath.RectTimesDouble(designObject.Bounds, m_dCurrentScale);
					m_penHighlight.DashStyle = System.Drawing.Drawing2D.DashStyle.Dash;
					e.Graphics.DrawRectangle(m_penHighlight, rectNet);

					// draw drag handles at corners of rect net
					m_penHighlight.DashStyle = System.Drawing.Drawing2D.DashStyle.Solid;
					Rectangle rectBox = new Rectangle(-3, -3, 6, 6);
					rect = rectBox;
					rect.Offset(rectNet.Left, rectNet.Top);
					e.Graphics.DrawRectangle(m_penHighlight, rect);
					rect = rectBox;
					rect.Offset(rectNet.Right, rectNet.Top);
					e.Graphics.DrawRectangle(m_penHighlight, rect);
					rect = rectBox;
					rect.Offset(rectNet.Left, rectNet.Bottom);
					e.Graphics.DrawRectangle(m_penHighlight, rect);
					rect = rectBox;
					rect.Offset(rectNet.Right, rectNet.Bottom);
					e.Graphics.DrawRectangle(m_penHighlight, rect);

					if (!designObject.IfLockAspect)
					{
						rect = rectBox;
						rect.Offset(rectNet.Right, (rectNet.Bottom + rectNet.Top)/2);
						e.Graphics.DrawRectangle(m_penHighlight, rect);
						rect = rectBox;
						rect.Offset((rectNet.Left + rectNet.Right)/2, rectNet.Top);
						e.Graphics.DrawRectangle(m_penHighlight, rect);
						rect = rectBox;
						rect.Offset(rectNet.Left, (rectNet.Bottom + rectNet.Top)/2);
						e.Graphics.DrawRectangle(m_penHighlight, rect);
						rect = rectBox;
						rect.Offset((rectNet.Left + rectNet.Right)/2, rectNet.Bottom);
						e.Graphics.DrawRectangle(m_penHighlight, rect);
					}
				}
			}
			catch (Exception ex)
			{
				DCSDEV.DCSMsg.Show("ERROR drawing document.", ex);
			}
		}
		private void DrawDragRect()
		{
			Rectangle rec = this.RectangleToScreen(m_rectDragCurrent);
			rec.Offset(this.AutoScrollPosition.X, this.AutoScrollPosition.Y);
			System.Windows.Forms.ControlPaint.DrawReversibleFrame(rec, Color.Black, System.Windows.Forms.FrameStyle.Thick);
			//System.Windows.Forms.ControlPaint.DrawReversibleFrame(this.RectangleToScreen(m_rectDragCurrent), Color.Black, System.Windows.Forms.FrameStyle.Thick);
		}
		private void DrawAutoAlignX()
		{
			Point pt1 = this.PointToScreen(m_pointAutoAlign1X);
			Point pt2 = this.PointToScreen(m_pointAutoAlign2X);
			pt1.Offset(this.AutoScrollPosition.X, this.AutoScrollPosition.Y);
			pt2.Offset(this.AutoScrollPosition.X, this.AutoScrollPosition.Y);
			System.Windows.Forms.ControlPaint.DrawReversibleLine(pt1, pt2, Color.Black);
		}
		private void DrawAutoAlignY()
		{
			Point pt1 = this.PointToScreen(m_pointAutoAlign1Y);
			Point pt2 = this.PointToScreen(m_pointAutoAlign2Y);
			pt1.Offset(this.AutoScrollPosition.X, this.AutoScrollPosition.Y);
			pt2.Offset(this.AutoScrollPosition.X, this.AutoScrollPosition.Y);
			System.Windows.Forms.ControlPaint.DrawReversibleLine(pt1, pt2, Color.Black);
		}

		private void SetSelectStatusText()
		{
			if (this.m_designObjectsSelected.Count > 0)
			{
				DCSDEV.DCSDesign.DCSDesignObject designObject = (DCSDEV.DCSDesign.DCSDesignObject)this.m_designObjectsSelected[0];
				this.SetStatusText(designObject.DCSDesignObjectType.ToString(), designObject.Bounds);
			}
			else this.m_mainWin.StatusBarText = "";
		}
		private void SetDragStatusText()
		{
			Rectangle rect = DCSDEV.DCSMath.DivDouble(m_rectDragCurrent, this.m_dCurrentScale);
			this.SetStatusText("mouse", rect);
		}
		private void SetStatusText(string strObjType, Rectangle rectIn)
		{
			if (strObjType == null)
			{
				this.m_mainWin.StatusBarText = "";
				return;
			}
			Rectangle rect;
			int iUnits = DCSDEV.DCSDesignDataAccess.GetUnits();
			if (iUnits == 1) rect = DCSDEV.DCSMath.INCH100toMM100(rectIn); 	// convert to MM100
			else rect = rectIn;

			double x,y,w,h;
			x = (double)rect.X / 100.0;
			y = (double)rect.Y / 100.0;
			w = (double)rect.Width / 100.0;
			h = (double)rect.Height / 100.0;
			this.m_mainWin.StatusBarText = String.Format("{0}, loc ({1},{2}) size ({3},{4}) {5}", strObjType, x.ToString("0.000"),y.ToString("0.000"),w.ToString("0.000"),h.ToString("0.000"), (iUnits==1?"mm":"inch"));
		}

		// dragging uses m_pointDragStart, m_rectDragStart, m_bMouseIsDown, m_clickedHandle, m_rectDragCurrent;
		private void DCSDesignerView_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
		{
			m_AutoAlignFoundX = false;	// for bAutoAlign
			m_AutoAlignFoundY = false;	// for bAutoAlign
			m_bMultiStretch = false;

			m_bMouseIsDown = true;
			//Point point = new Point(e.X, e.Y);
			Point point = new Point(e.X, e.Y) - (Size)this.AutoScrollPosition;	//scroll

			m_pointDragStart = point;	// remember mouse down position
			DCSDesignObject designObject;

			// if one designObject is selected see if clicked on drag handle //
			if (this.m_designObjectsSelected.Count == 1)
			{
				m_clickedHandle = GetClickedObjectHandle(point, (DCSDesignObject)m_designObjectsSelected[0]);
				if (m_clickedHandle != ClickedHandle.None)
				{ 
					// prepare to drag selected object - if there is no movement, MouseUp should select the next object
					// or to drag a corner or edge.
					// ClickedHandle.Interior will not occur here.
					designObject = (DCSDesignObject)this.m_designObjectsSelected[0];
					m_rectDragCurrent = DCSMath.TimesDouble(designObject.Bounds, this.m_dCurrentScale);		// convert to view scale
					m_rectDragStart = m_rectDragCurrent; 
					this.DrawDragRect();
					return;
				}
			}
			// multi select drag case
			else if (this.m_designObjectsSelected.Count > 1)
			{
				// detect MultiStretch case
				DCSDesignObject designObjectSelected;
				m_clickedHandle = this.GetClickedObjectHandle(point, out designObjectSelected);
				switch (m_clickedHandle)
				{
					case ClickedHandle.Left:
					case ClickedHandle.Right:
					case ClickedHandle.Top:
					case ClickedHandle.Bottom:
					case ClickedHandle.TopLeft:
					case ClickedHandle.TopRight:
					case ClickedHandle.BottomLeft:
					case ClickedHandle.BottomRight:
						m_bMultiStretch = true;
						m_rectDragCurrent = DCSMath.TimesDouble(designObjectSelected.Bounds, this.m_dCurrentScale);		// convert to view scale
						m_rectDragStart = m_rectDragCurrent;
						this.DrawDragRect();
						return;
					case ClickedHandle.Interior:
					case ClickedHandle.InteriorSelected:
						m_clickedHandle = ClickedHandle.InteriorSelected;

						// compute union of all selected rectangles
						{
							Rectangle rect;
							Rectangle rectNet = Rectangle.Empty;
							foreach (DCSDesignObject designObject2 in m_designObjectsSelected)
							{
								rect = DCSDEV.DCSMath.RectTimesDouble(designObject2.Bounds, m_dCurrentScale);	// convert rect to view scale
								if (rectNet == Rectangle.Empty)
									rectNet = rect;
								else
									rectNet = Rectangle.Union(rect, rectNet);
							}
							m_rectDragCurrent = rectNet;
							m_rectDragStart = m_rectDragCurrent;
							this.DrawDragRect();
						}
						return;
					case ClickedHandle.None:
						break;
				}
			}

			// comes here is click is outside all selected objects

			// bMultiSelect is enabled if shift key is down
			bool bMultiSelect = ((System.Windows.Forms.Control.ModifierKeys & Keys.Shift) == Keys.Shift);
			if (!bMultiSelect && this.m_designObjectsSelected.Count != 0)
			{
				this.m_designObjectsSelected.Clear();
				m_bNeedsInvaldate = true;
			}
			designObject = GetNextClickedObject(point);
			if (designObject != null)
			{
				m_clickedHandle = ClickedHandle.Interior;
				this.m_designObjectsSelected.Add(designObject);
				m_bNeedsInvaldate = true;
				// prepare to drag
				// dragging uses m_pointDragStart, m_bMouseIsDown, m_clickedHandle, 
				// m_rectDragCurrent;
				m_rectDragCurrent = DCSMath.TimesDouble(designObject.Bounds, this.m_dCurrentScale);
				m_rectDragStart = m_rectDragCurrent; 
				this.DrawDragRect();
			}
			else
			{
				m_clickedHandle = ClickedHandle.None;
				// lasso case
				m_rectDragCurrent = new Rectangle(point, new Size(0,0));
				m_rectDragStart = m_rectDragCurrent; 
				this.DrawDragRect();
			}
			if (m_bNeedsInvaldate) 
			{
				SetSelectStatusText();
				this.m_mainWin.EnableGUIItems();
				this.Invalidate(true);
			}
		}

		private void DCSDesignerView_MouseUp(object sender, System.Windows.Forms.MouseEventArgs e)
		{
			//number of sides may have changed - make sure still displaying an existing side.
			if (this.m_iCurrrentSide > this.m_doc.NumBadgeSides - 1)
				this.m_iCurrrentSide = 0;

			m_bMouseIsDown = false;
			//Point point = new Point(e.X, e.Y);
			Point point = new Point(e.X, e.Y) - (Size)this.AutoScrollPosition;	//scroll

			if (m_clickedHandle == ClickedHandle.None) 
			{
				this.m_mainWin.StatusBarText = "";
				// lasso case
				Rectangle rect;
				//this.DrawDragRect();	// erase old draw rectangle
				DCSDEV.DCSMath.Normalize(ref m_rectDragCurrent);
				ArrayList arrayDesignObjects = ((DCSDesignSide)m_doc.m_designSides[m_iCurrrentSide]).m_DCSDesignObjects;
				foreach (DCSDesignObject designObject in arrayDesignObjects)
				{
					rect = DCSDEV.DCSMath.RectTimesDouble(designObject.Bounds, m_dCurrentScale);
					if (rect.IntersectsWith(m_rectDragCurrent))
					{
						this.m_designObjectsSelected.Add(designObject);
						m_bNeedsInvaldate = true;
					}
				}
			}
			else if (m_clickedHandle == ClickedHandle.Interior || m_clickedHandle == ClickedHandle.InteriorSelected)
			{
				this.DrawDragRect();	// erase old draw rectangle
				
				Point pointDelta = m_rectDragCurrent.Location - (Size)m_rectDragStart.Location;
				// move already made the current rect be inside the bounds

				if (!pointDelta.IsEmpty)
				{
					// There is some actual movement of the selected objects
					pointDelta = DCSMath.DivDouble(pointDelta, this.m_dCurrentScale);
					bool bFirst = true;
					foreach(DCSDesignObject designObject in this.m_designObjectsSelected)
					{
						m_doc.m_UndoEventClass.Add(m_iCurrrentSide, UndoType.PreEdit, designObject, bFirst);
						Rectangle rect = designObject.Bounds;
						rect.Offset(pointDelta);
						designObject.Bounds = rect;
						m_doc.m_UndoEventClass.Add(m_iCurrrentSide, UndoType.Edit, designObject, bFirst);
						bFirst = false;
					}
					this.m_doc.m_isDirty = true;
					m_doc.m_isViewDirty = true;
				}
				else
				{
					// there is no movement = this is a selection action
					// bMultiSelect is enabled if shift key is down
					bool bMultiSelect = ((System.Windows.Forms.Control.ModifierKeys & Keys.Shift) == Keys.Shift);
					if (!bMultiSelect)
					{
						if (m_clickedHandle == ClickedHandle.InteriorSelected)
						{
							// select the next object
							this.m_designObjectsSelected.Clear();
							DCSDesignObject designObject = this.GetNextClickedObject(point);
							if (designObject != null) this.m_designObjectsSelected.Add(designObject);
						}
					}
					else
					{
						// In MultiSelect/shift case, if clicked on something already selected, remove the selection.
						if (m_clickedHandle == ClickedHandle.InteriorSelected)
						{
							foreach (DCSDesignObject designObject2 in m_designObjectsSelected)
							{
								if ((DCSDEV.DCSMath.RectTimesDouble(designObject2.Bounds, m_dCurrentScale)).Contains(point))
								{
									m_designObjectsSelected.Remove(designObject2);
									break;
								}
							}
						}
					}
				}
				m_bNeedsInvaldate = true;
			}
			else if (m_clickedHandle != ClickedHandle.None)
			{
				// drag by a handle
				//this.DrawDragRect();	// erase old draw rectangle
				Point pointDelta = point;
				string strEdges = "";
				int iDelta = 0;
				if (m_pointDragStart != point)
				{
					DCSDEV.DCSMath.Normalize(ref m_rectDragCurrent);
					if (this.m_bMultiStretch)
					{
						m_rectDragCurrent = DCSDEV.DCSMath.DivDouble(m_rectDragCurrent, m_dCurrentScale);
						m_rectDragStart = DCSDEV.DCSMath.DivDouble(m_rectDragStart, m_dCurrentScale);

						bool bFirst = true;	// mark the first of a set of events that are undone in one undo step.
						foreach (DCSDesignObject designObject in this.m_designObjectsSelected)
						{
							if (designObject.IfLockAspect) continue;	// do not stretch objects with locked aspect ratio
							m_doc.m_UndoEventClass.Add(m_iCurrrentSide, UndoType.PreEdit, designObject, bFirst);
							switch (m_clickedHandle)
							{
								case ClickedHandle.Left:
									strEdges = "L";
									break;
								case ClickedHandle.Right:
									strEdges = "R";
									break;
								case ClickedHandle.Top:
									strEdges = "T";
									break;
								case ClickedHandle.Bottom:
									strEdges = "B";
									break;
								case ClickedHandle.TopLeft:
									strEdges = "TL";
									break;
								case ClickedHandle.TopRight:
									strEdges = "TR";
									break;
								case ClickedHandle.BottomLeft:
									strEdges = "BL";
									break;
								case ClickedHandle.BottomRight:
									strEdges = "BR";
									break;
								default:
									return;
							}
							foreach (char c in strEdges)
							{
								switch (c)
								{
									case 'L':
										iDelta = m_rectDragCurrent.Left - m_rectDragStart.Left;
										designObject.Bounds.X = designObject.Bounds.X + iDelta;
										designObject.Bounds.Width = designObject.Bounds.Width - iDelta;
										break;
									case 'R':
										iDelta = m_rectDragCurrent.Right - m_rectDragStart.Right;
										designObject.Bounds.Width = designObject.Bounds.Right + iDelta - designObject.Bounds.Left;
										break;
									case 'T':
										iDelta = m_rectDragCurrent.Top - m_rectDragStart.Top;
										designObject.Bounds.Y = designObject.Bounds.Y + iDelta;
										designObject.Bounds.Height = designObject.Bounds.Height - iDelta;
										break;
									case 'B':
										iDelta = m_rectDragCurrent.Bottom - m_rectDragStart.Bottom;
										designObject.Bounds.Height = designObject.Bounds.Bottom + iDelta - designObject.Bounds.Top;
										break;
								}
							}
							// make the bounds stay inside the badge bounds
							Size sizeDocBounds = m_doc.Bounds.Size;
							this.SwapIfSideLandscapeDiffers(ref sizeDocBounds);
							DCSMath.ApplyBounds(ref designObject.Bounds, sizeDocBounds, strEdges);
							
							m_doc.m_UndoEventClass.Add(m_iCurrrentSide, UndoType.Edit, designObject, bFirst);
							bFirst = false;
						}
					}
					else
					{
						// select and move - otherwise select only
						bool bFirst = true;
						Rectangle rect;
						foreach (DCSDesignObject designObject in this.m_designObjectsSelected)
						{
							m_doc.m_UndoEventClass.Add(m_iCurrrentSide, UndoType.PreEdit, designObject, bFirst);
							rect = DCSDEV.DCSMath.DivDouble(m_rectDragCurrent, m_dCurrentScale);
							designObject.Bounds = rect;

							m_doc.m_UndoEventClass.Add(m_iCurrrentSide, UndoType.Edit, designObject, bFirst);
							bFirst = false;
						}
					}
					m_doc.m_isDirty = true;
					m_doc.m_isViewDirty = true;
				}
				m_bNeedsInvaldate = true;
			}

			if (m_bNeedsInvaldate)
			{
				this.m_mainWin.EnableGUIItems();
				this.Invalidate(true);
			}
			else
				this.DrawDragRect();	// erase old draw rectangle

			m_clickedHandle = ClickedHandle.None;

			this.SetSelectStatusText();
		}

		private void DCSDesignerView_MouseMove(object sender, System.Windows.Forms.MouseEventArgs e)
		{
			int iAutoAlignRange = m_doc.m_TabRange;

			//Point point = new Point(e.X, e.Y);
			Point point = new Point(e.X, e.Y) - (Size)this.AutoScrollPosition;	//scroll

			if (m_bMouseIsDown)
			{
				if (m_clickedHandle == ClickedHandle.None)
				{
					// lasso case
					this.DrawDragRect();	// erase old draw rectangle
					Point pointDelta = point;
					pointDelta.Offset(-m_pointDragStart.X, -m_pointDragStart.Y);
					m_rectDragCurrent.Height = m_rectDragStart.Height + pointDelta.Y;
					m_rectDragCurrent.Width = m_rectDragStart.Width + pointDelta.X;
					this.DrawDragRect();
					return;
				}
				else if (m_clickedHandle == ClickedHandle.Interior || m_clickedHandle == ClickedHandle.InteriorSelected)
				{
					this.DrawDragRect();	// erase old draw rectangle at m_rectDragCurrent

					Point pointDelta = point;
					pointDelta.Offset(-m_pointDragStart.X, -m_pointDragStart.Y);
					
					m_rectDragCurrent = m_rectDragStart;
					m_rectDragCurrent.Offset(pointDelta);

					if (m_mainWin.m_bGridSnapOn)
					{
						// move to nearest grid point
						// temporarily convert to design coords
						m_rectDragCurrent = DCSMath.DivDouble(m_rectDragCurrent, this.m_dCurrentScale);

						m_rectDragCurrent.X = SnapGridX(m_rectDragCurrent.X);
						m_rectDragCurrent.Y = SnapGridY(m_rectDragCurrent.Y);

						// convert back to view coords
						m_rectDragCurrent = DCSMath.TimesDouble(m_rectDragCurrent, this.m_dCurrentScale);
					}
					else if (m_mainWin.m_bTabAlignOn)
					{
						// move to nearest grid point
						// temporarily convert to design coords
						m_rectDragCurrent = DCSMath.DivDouble(m_rectDragCurrent, this.m_dCurrentScale);

						m_rectDragCurrent = SnapGridLines(m_rectDragCurrent);

						// convert back to view coords
						m_rectDragCurrent = DCSMath.TimesDouble(m_rectDragCurrent, this.m_dCurrentScale);
					}
					else if (m_mainWin.m_bAutoAlign)
					{
						if (this.m_AutoAlignFoundX) this.DrawAutoAlignX();
						if (this.m_AutoAlignFoundY) this.DrawAutoAlignY();
						this.m_AutoAlignFoundX = false;
						this.m_AutoAlignFoundY = false;

						// temporarily convert to design coords
						m_rectDragCurrent = DCSMath.DivDouble(m_rectDragCurrent, this.m_dCurrentScale);

						// move to nearest autoalign point
						bool bInSelectList = false;
						int iDist;

						int iMinDistX = -1;
						char cMinEdgeX = 'X';
						DCSDesignObject objClosestX = null;
						int iMinDistY = -1;
						char cMinEdgeY = 'X';
						DCSDesignObject objClosestY = null;
						
						foreach (DCSDesignObject objDesign in ((DCSDesignSide)this.m_doc.m_designSides[m_iCurrrentSide]).m_DCSDesignObjects)
						{
							// do not align with any object already selected
							bInSelectList = false;
							foreach (DCSDesignObject objSelected in this.m_designObjectsSelected)
							{
								if (objSelected == objDesign) bInSelectList = true;
								break;
							}
							if (bInSelectList) continue;

							iDist = Math.Abs(objDesign.Bounds.Left - m_rectDragCurrent.Left);
							if (iDist < iAutoAlignRange && (iMinDistX < 0 || iDist < iMinDistX))
							{
								iMinDistX = iDist;
								objClosestX = objDesign;
								cMinEdgeX = 'L'; 
							}
							iDist = Math.Abs(objDesign.Bounds.Right - m_rectDragCurrent.Right);
							if (iDist < iAutoAlignRange && (iMinDistX < 0 || iDist < iMinDistX))
							{
								iMinDistX = iDist;
								objClosestX = objDesign;
								cMinEdgeX = 'R';
							}
							iDist = Math.Abs(objDesign.Bounds.Top - m_rectDragCurrent.Top);
							if (iDist < iAutoAlignRange && (iMinDistY < 0 || iDist < iMinDistY))
							{
								iMinDistY = iDist;
								objClosestY = objDesign;
								cMinEdgeY = 'T';
							}
							iDist = Math.Abs(objDesign.Bounds.Bottom - m_rectDragCurrent.Bottom);
							if (iDist < iAutoAlignRange && (iMinDistY < 0 || iDist < iMinDistY))
							{
								iMinDistY = iDist;
								objClosestY = objDesign;
								cMinEdgeY = 'B';
							}
						}
						if (objClosestX != null)
						{
							this.m_AutoAlignFoundX = true;
							if (cMinEdgeX == 'L') m_rectDragCurrent.X = objClosestX.Bounds.Left;
							else m_rectDragCurrent.X = objClosestX.Bounds.Right - m_rectDragCurrent.Width;

							int top;
							if (m_rectDragCurrent.Top < objClosestX.Bounds.Top) top = m_rectDragCurrent.Top;
							else top = objClosestX.Bounds.Top;
							int bottom;
							if (m_rectDragCurrent.Bottom > objClosestX.Bounds.Bottom) bottom = m_rectDragCurrent.Bottom;
							else bottom = objClosestX.Bounds.Bottom;

							if (cMinEdgeX == 'L')
							{
								this.m_pointAutoAlign1X = new Point(objClosestX.Bounds.Left, top);
								this.m_pointAutoAlign2X = new Point(objClosestX.Bounds.Left, bottom);
							}
							else
							{
								this.m_pointAutoAlign1X = new Point(objClosestX.Bounds.Right, top);
								this.m_pointAutoAlign2X = new Point(objClosestX.Bounds.Right, bottom);
							}
							m_pointAutoAlign1X = DCSDEV.DCSMath.TimesDouble(m_pointAutoAlign1X, this.m_dCurrentScale);
							m_pointAutoAlign2X = DCSDEV.DCSMath.TimesDouble(m_pointAutoAlign2X, this.m_dCurrentScale);
							this.DrawAutoAlignX();
						}
						if (objClosestY != null)
						{
							this.m_AutoAlignFoundY = true;
							if (cMinEdgeY == 'T') m_rectDragCurrent.Y = objClosestY.Bounds.Top;
							else m_rectDragCurrent.Y = objClosestY.Bounds.Bottom - m_rectDragCurrent.Height;

							int left;
							if (m_rectDragCurrent.Left < objClosestY.Bounds.Left) left = m_rectDragCurrent.Left;
							else left = objClosestY.Bounds.Left;
							int right;
							if (m_rectDragCurrent.Right > objClosestY.Bounds.Right) right = m_rectDragCurrent.Right;
							else right = objClosestY.Bounds.Right;

							if (cMinEdgeY == 'T')
							{
								this.m_pointAutoAlign1Y = new Point(left, objClosestY.Bounds.Top);
								this.m_pointAutoAlign2Y = new Point(right, objClosestY.Bounds.Top);
							}
							else
							{
								this.m_pointAutoAlign1Y = new Point(left, objClosestY.Bounds.Bottom);
								this.m_pointAutoAlign2Y = new Point(right, objClosestY.Bounds.Bottom);
							}
							m_pointAutoAlign1Y = DCSDEV.DCSMath.TimesDouble(m_pointAutoAlign1Y, this.m_dCurrentScale);
							m_pointAutoAlign2Y = DCSDEV.DCSMath.TimesDouble(m_pointAutoAlign2Y, this.m_dCurrentScale);
							this.DrawAutoAlignY();
						}
						// convert back to view coords
						m_rectDragCurrent = DCSMath.TimesDouble(m_rectDragCurrent, this.m_dCurrentScale);
					}

					// make the bounds stay inside the badge bounds
					Size sizeDocView = DCSMath.TimesDouble(m_doc.Bounds.Size, this.m_dCurrentScale);
					this.SwapIfSideLandscapeDiffers(ref sizeDocView);
					DCSMath.ApplyBounds(ref m_rectDragCurrent, sizeDocView);

					this.DrawDragRect();	// draw new rectangle at m_rectDragCurrent
					this.SetDragStatusText();
				}
				else // a handle is selected
				{
					// drag by a handle
					Point pointDelta = point;
					pointDelta.Offset(-m_pointDragStart.X, -m_pointDragStart.Y);
					DCSDesignObject designObject = (DCSDesignObject)this.m_designObjectsSelected[0];		// only one obj can  be selected in this mode. - TO BE ENHABCED

					this.DrawDragRect();	// erase old draw rectangle

					// if (!pointDelta.IsEmpty)
					string strEdges = "";
					switch (m_clickedHandle)
					{
						case ClickedHandle.TopLeft:
							strEdges = "TL";
							m_rectDragCurrent.X = m_rectDragStart.Left + pointDelta.X;
							m_rectDragCurrent.Width = m_rectDragStart.Width - pointDelta.X;
							m_rectDragCurrent.Y = m_rectDragStart.Top + pointDelta.Y;
							m_rectDragCurrent.Height = m_rectDragStart.Height - pointDelta.Y;
							break;
						case ClickedHandle.TopRight:
							strEdges = "TR";
							m_rectDragCurrent.Width = m_rectDragStart.Width + pointDelta.X;
							m_rectDragCurrent.Y = m_rectDragStart.Top + pointDelta.Y;
							m_rectDragCurrent.Height = m_rectDragStart.Height - pointDelta.Y;
							break;
						case ClickedHandle.BottomLeft:
							strEdges = "BL";
							m_rectDragCurrent.X = m_rectDragStart.Left + pointDelta.X;
							m_rectDragCurrent.Width = m_rectDragStart.Width - pointDelta.X;
							m_rectDragCurrent.Height = m_rectDragStart.Height + pointDelta.Y;
							break;
						case ClickedHandle.BottomRight:
							strEdges = "BR";
							m_rectDragCurrent.Height = m_rectDragStart.Height + pointDelta.Y;
							m_rectDragCurrent.Width = m_rectDragStart.Width + pointDelta.X;
							break;
						case ClickedHandle.Left:
							strEdges = "L";
							m_rectDragCurrent.X = m_rectDragStart.Left + pointDelta.X;
							m_rectDragCurrent.Width = m_rectDragStart.Width - pointDelta.X;
							break;
						case ClickedHandle.Top:
							strEdges = "T";
							m_rectDragCurrent.Y = m_rectDragStart.Top + pointDelta.Y;
							m_rectDragCurrent.Height = m_rectDragStart.Height - pointDelta.Y;
							break;
						case ClickedHandle.Right:
							strEdges = "R";
							m_rectDragCurrent.Width = m_rectDragStart.Width + pointDelta.X;
							break;
						case ClickedHandle.Bottom:
							strEdges = "B";
							m_rectDragCurrent.Height = m_rectDragStart.Height + pointDelta.Y;
							break;
					}
					// adjust to lock aspect ratio
					if (designObject.IfLockAspect)
					{
						int dw=0, dh=0;
						double ratioField = (double)designObject.Bounds.Width / (double)designObject.Bounds.Height;
						double ratioDrag = (double)m_rectDragCurrent.Width / (double)m_rectDragCurrent.Height;
						if (ratioDrag > ratioField)
							dw = m_rectDragCurrent.Width - DCSMath.IntTimesDouble(m_rectDragCurrent.Height, ratioField);
						else
							dh = m_rectDragCurrent.Height - DCSMath.IntDivDouble(m_rectDragCurrent.Width, ratioField);

						switch (m_clickedHandle)
						{
							case ClickedHandle.TopLeft:
								m_rectDragCurrent.X += dw;
								m_rectDragCurrent.Width -= dw;
								m_rectDragCurrent.Y += dh;
								m_rectDragCurrent.Height -= dh;
								break;
							case ClickedHandle.TopRight:
								//m_rectDragCurrent.X += dw;
								m_rectDragCurrent.Width -= dw;
								m_rectDragCurrent.Y += dh;
								m_rectDragCurrent.Height -= dh;
								break;
							case ClickedHandle.BottomLeft:
								m_rectDragCurrent.X += dw;
								m_rectDragCurrent.Width -= dw;
								//m_rectDragCurrent.Y += dh;
								m_rectDragCurrent.Height -= dh;
								break;
							case ClickedHandle.BottomRight:
								//m_rectDragCurrent.X;
								m_rectDragCurrent.Width -= dw;
								//m_rectDragCurrent.Y;
								m_rectDragCurrent.Height -= dh;
								break;
						}
					}	// end IfLockAspect

					if (m_mainWin.m_bGridSnapOn || m_mainWin.m_bTabAlignOn)
					{
						// temporarily convert to design coords
						m_rectDragCurrent = DCSMath.DivDouble(m_rectDragCurrent, this.m_dCurrentScale);

						if (designObject.IfLockAspect)
						{
							int mx;
							switch (m_clickedHandle)
							{
								case ClickedHandle.BottomLeft:
								case ClickedHandle.BottomRight:
									mx = SnapGridY(m_rectDragCurrent.Bottom);
									m_rectDragCurrent.Y = mx - m_rectDragCurrent.Height;
									break;
								case ClickedHandle.TopRight:
								case ClickedHandle.TopLeft:
									// move top to nearest grid point
									m_rectDragCurrent.Y = SnapGridY(m_rectDragCurrent.Y);
									break;
							}
							switch (m_clickedHandle)
							{
								case ClickedHandle.TopLeft:
								case ClickedHandle.BottomLeft:
									// move left to nearest grid point
									m_rectDragCurrent.X = SnapGridX(m_rectDragCurrent.X);
									break;
								case ClickedHandle.TopRight:
								case ClickedHandle.BottomRight:
									mx = SnapGridX(m_rectDragCurrent.Right);
									m_rectDragCurrent.X = mx - m_rectDragCurrent.Width;
									break;
							}
						}
						else
						{
							// NOT Lock Aspect
							// move to nearest grid point
							int adj;
							switch (m_clickedHandle)
							{
								case ClickedHandle.TopRight:
								case ClickedHandle.TopLeft:
								case ClickedHandle.Top:
									// move top to nearest grid point
									adj = SnapGridY(m_rectDragCurrent.Top);
									m_rectDragCurrent.Height = m_rectDragCurrent.Bottom - adj;
									m_rectDragCurrent.Y = adj;
									break;
								case ClickedHandle.BottomLeft:
								case ClickedHandle.BottomRight:
								case ClickedHandle.Bottom:
									adj = SnapGridY(m_rectDragCurrent.Bottom);
									m_rectDragCurrent.Height = adj - m_rectDragCurrent.Top;
									break;
							}
							switch (m_clickedHandle)
							{
								case ClickedHandle.TopLeft:
								case ClickedHandle.BottomLeft:
								case ClickedHandle.Left:
									// move left to nearest grid point
									adj = SnapGridX(m_rectDragCurrent.Left);
									m_rectDragCurrent.Width = m_rectDragCurrent.Right - adj;
									m_rectDragCurrent.X = adj;
									break;
								case ClickedHandle.TopRight:
								case ClickedHandle.BottomRight:
								case ClickedHandle.Right:
									adj = SnapGridX(m_rectDragCurrent.Right);
									m_rectDragCurrent.Width = adj - m_rectDragCurrent.Left;
									break;
							}
						}
						// convert back to view coords
						m_rectDragCurrent = DCSMath.TimesDouble(m_rectDragCurrent, this.m_dCurrentScale);
					}

					// make the bounds stay inside the badge bounds
					Size sizeDocBounds = DCSMath.TimesDouble(m_doc.Bounds.Size, this.m_dCurrentScale);
					this.SwapIfSideLandscapeDiffers(ref sizeDocBounds);
					DCSMath.ApplyBounds(ref m_rectDragCurrent, sizeDocBounds, strEdges);

					this.DrawDragRect();
					this.SetDragStatusText();
				}
			}
			else
			{
				// mouse is up - show cursor that indicates where the mouse is
				if (this.m_designObjectsSelected.Count != 0)
				{
					ClickedHandle enumClicked;

					DCSDesignObject designObject2;
					enumClicked = GetClickedObjectHandle(point, out designObject2);
					switch (enumClicked)
					{
						case ClickedHandle.Right:
							this.Cursor = Cursors.SizeWE;
							break;
						case ClickedHandle.Top:
							this.Cursor = Cursors.SizeNS;
							break;
						case ClickedHandle.Left:
							this.Cursor = Cursors.SizeWE;
							break;
						case ClickedHandle.Bottom:
							this.Cursor = Cursors.SizeNS;
							break;
						case ClickedHandle.TopLeft:
							this.Cursor = Cursors.SizeNWSE;
							break;
						case ClickedHandle.TopRight:
							this.Cursor = Cursors.SizeNESW;
							break;
						case ClickedHandle.BottomLeft:
							this.Cursor = Cursors.SizeNESW;
							break;
						case ClickedHandle.BottomRight:
							this.Cursor = Cursors.SizeNWSE;
							break;
						case ClickedHandle.InteriorSelected:
							this.Cursor = Cursors.SizeAll;
							break;
						default:
							this.Cursor = m_cursorSave;
							break;
					}
				}
				else	// nothing is selected
				{
					// nothing is selected
					DCSDesignObject designObject = GetIncludingObject(point);
					if (designObject != null) this.Cursor = Cursors.SizeAll;
					else this.Cursor = m_cursorSave;
				}
			}
		}

		private void DCSDesignerView_DoubleClick(object sender, System.EventArgs e)
		{
			DCSDesignObject designObject = GetNextClickedObject(m_pointDragStart);

			// do not open properties with a double click if there are more than one objects at same point.
			if (designObject != null && designObject != GetNextClickedObject(m_pointDragStart)) return;

			if (designObject == null) 
				this.m_designObjectsSelected.Clear();
			else
				if (!AreAllTypesTheSame()) return;
			CallPropertiesDlg(this.m_designObjectsSelected);
		}

		protected override bool IsInputKey(Keys keyData)
		{
			switch (keyData)
			{
				case Keys.Tab:
				case Keys.Left:
				case Keys.Right:
				case Keys.Up:
				case Keys.Down:
					return true;
				default:
					return base.IsInputKey (keyData);
			}
		}

		private void DCSDesignerView_KeyDown(object sender, System.Windows.Forms.KeyEventArgs e)
		{
			if (e.KeyCode == Keys.Delete)
			{
				DCSDEV.DCSDesign.DCSDesignSide designSide = (DCSDEV.DCSDesign.DCSDesignSide)m_doc.m_designSides[m_iCurrrentSide];
				bool bFirst = true;
				foreach(DCSDEV.DCSDesign.DCSDesignObject designObject in m_designObjectsSelected)
				{
					// find object in side list and delete it
					m_doc.m_UndoEventClass.Add(m_iCurrrentSide, UndoType.Delete, designObject, bFirst);
					bFirst = false;
					designSide.m_DCSDesignObjects.Remove(designObject);
				}
				m_designObjectsSelected.Clear();
				SetSelectStatusText();
				this.m_mainWin.EnableGUIItems();

				m_doc.m_isDirty = true;
				m_doc.m_isViewDirty = true;
				Invalidate(true);
			}
			else if (e.KeyCode == Keys.Enter)
			{
				if (!AreAllTypesTheSame()) return;
				CallPropertiesDlg(this.m_designObjectsSelected);
			}

			else if (e.KeyCode == Keys.Tab)
			{
				DCSDEV.DCSDesign.DCSDesignSide designSide = (DCSDEV.DCSDesign.DCSDesignSide)m_doc.m_designSides[m_iCurrrentSide];
				if (designSide.m_DCSDesignObjects.Count == 0) return;	// no objects, return

				DCSDEV.DCSDesign.DCSDesignObject designObject = null;
				int index = -1;
				if (m_designObjectsSelected.Count > 0)
				{
					designObject = (DCSDEV.DCSDesign.DCSDesignObject)this.m_designObjectsSelected[0];
					index = designSide.m_DCSDesignObjects.IndexOf(designObject);
				}

				//if ((Control.ModifierKeys & Keys.Shift) == Keys.Shift)
				if ((e.Shift))
				{
					// syh shifted tab key is not getting here!!! This code does nothing
					index--;
					if (index < 0) index = designSide.m_DCSDesignObjects.Count - 1;
				}
				else
				{
					index++;
					if (index >= designSide.m_DCSDesignObjects.Count) index = 0;
				}
				m_designObjectsSelected.Clear();
				designObject = (DCSDEV.DCSDesign.DCSDesignObject)designSide.m_DCSDesignObjects[index];

				m_designObjectsSelected.Add(designObject);

				SetSelectStatusText();
				this.m_mainWin.EnableGUIItems();
				Invalidate(true);
			}
			else if (e.KeyCode == Keys.Left || e.KeyCode == Keys.Right || e.KeyCode == Keys.Up || e.KeyCode == Keys.Down)
			{
				if (m_designObjectsSelected.Count <= 0) return;

				DCSDEV.DCSDesign.DCSDesignSide designSide = (DCSDEV.DCSDesign.DCSDesignSide)m_doc.m_designSides[m_iCurrrentSide];
				bool bFirst = true;
				int iStepX = 1;	//size of a tab step defaults to 1 
				int iStepY = 1;
				if (m_mainWin.m_bGridSnapOn)		// use step size equal to grid if snap to grid is on
				{
					iStepX = m_doc.m_GridSpacingX;
					iStepY = m_doc.m_GridSpacingY;
				}
				foreach(DCSDEV.DCSDesign.DCSDesignObject designObject in m_designObjectsSelected)
				{
					m_doc.m_UndoEventClass.Add(m_iCurrrentSide, UndoType.PreEdit, designObject, bFirst);
					Rectangle rect = designObject.Bounds;
					if (e.KeyCode == Keys.Left)
						rect.X = rect.X - iStepX;
					else if (e.KeyCode == Keys.Right)
						rect.X = rect.X + iStepX;
					else if (e.KeyCode == Keys.Up)
						rect.Y = rect.Y - iStepY;
					else if (e.KeyCode == Keys.Down)
						rect.Y = rect.Y + iStepY;
					if (m_mainWin.m_bGridSnapOn)
					{
						// move to nearest grid point
						rect.X = SnapGridX(rect.X);
						rect.Y = SnapGridY(rect.Y);
					}
					designObject.Bounds = rect;
					m_doc.m_UndoEventClass.Add(m_iCurrrentSide, UndoType.Edit, designObject, bFirst);
					bFirst = false;
				}
				m_doc.m_isDirty = true;
				m_doc.m_isViewDirty = true;
				SetSelectStatusText();
				Invalidate(true);
				this.m_mainWin.EnableGUIItems();
			}
		}

		private void DCSDesignerView_Activated(object sender, System.EventArgs e)
		{
			this.SetViewTitleBar();
		}

		public double ViewScale
		{
			get { return m_dCurrentScale; }
			set { m_dCurrentScale = value; }
		}

		public int CurrrentSide
		{
			get { return m_iCurrrentSide; }
			set { m_iCurrrentSide = value; }
		}
	}
}
