using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Collections;
using System.IO;
using System.Runtime.Serialization.Formatters.Binary;
using System.Windows.Forms;
using System.Drawing.Text;

namespace DCSDEV.DCSDesign
{
	/// <summary>
	/// Summary description for DCSDesignObject.
	/// </summary>
	[Serializable()]
    public class DCSDesignObject : IDisposable
	{
		public int Side = 0;	// default to front side
		public DCSDEV.DCSDatatypes.DCSDesignObjectTypes DCSDesignObjectType;
		public int ObjectTypeInstance;
		public Rectangle Bounds;
		public RotateFlipType RotateFlip;
		
		// foreground
		public DCSDEV.DCSDatatypes.SourceTypes SourceType;
		public String SourceName;
		public String Formula;
		public bool VisibleIf;
		public String VisibleIfCondition;
		public bool VisibleIfEval;
		public string DesignObjectImageName;
		public Bitmap DesignObjectImage; //foreground image in image obj
		public String Text; // <= 5K

		// LabeledText
		public bool LabelOn;
		public String LabelText;
		public DCSDatatypes.DCSLabeledTextOrientations LabelOrientation;
		public int LabelOffset;
		public int LabelFontIndex;

		public int FontIndex;
		public DCSFontEx FontEx = new DCSFontEx();
		public int CaseIndex = 0;
		public string TxtFormat = "app format";
		public DCSDEV.DCSDatatypes.Alignments Alignment;
		public DCSDEV.DCSDatatypes.Justifications Justification;

		// background
		public DCSDEV.DCSDatatypes.BackFillTypes BackFillType;
		public Color BackColor = Color.White;
		public Color BackColor2 = Color.White;
		public System.Drawing.Drawing2D.LinearGradientMode BackGradientType;
		public Color ColorChoice1;
		public Color ColorChoice2;
		public Color ColorChoice3;
		public string ColorCondition1;
		public string ColorCondition2;
		public Color ColorEval;				//color avalued from conditions

		public int Transparency; // 0-99
		public string BackImageName; 
		public Bitmap BackImage; //background image in text
		public DCSDEV.DCSDatatypes.ScaleMode Scaling;
		public DCSDEV.DCSDatatypes.FramingMode Framing;

		// process
		public bool IfLockAspect;
		public int GroupID; // >= 0
		public bool Locked;
		public int ZOrder; // >= 0
        public int SpecialKPanel;

		// border
		public Color LineColor = Color.Black;
		public int LineWidth;
		public int Radius;

		public bool GrayScale;
		public bool BackDetectEnabled;
		public int ColorDetectThreshold;
		public Color ColorToDetect = Color.White;

		public bool AutoKey;
		public bool PortraitAutoKey;
		public short BarcodeCode; 
		public bool BarcodeShowText; 
		public short IcaoType;// new

		public DCSDesignObject(DCSDEV.DCSDatatypes.DCSDesignObjectTypes designObjectType)
		{
			//
			// TODO: Add constructor logic here
			//
			this.SetProperties(designObjectType);
		}

		public DCSDesignObject()
		{
			//
			// TODO: Add constructor logic here
			//
			this.SetProperties();
		}

		// Implement IDisposable.
		// Do not make this method virtual.
		// A derived class should not be able to override this method.
		public void Dispose()
		{
			if (this.DesignObjectImage != null) this.DesignObjectImage.Dispose();
			if (this.BackImage != null) this.BackImage.Dispose();
		}

		// set properties as a clone of another designObject
		// if creating do not copy source values
		public DCSDesignObject CloneObject(bool bCreating)
		{
			DCSDesignObject outDesignObject = null;
			
			outDesignObject = new DCSDesignObject(this.DCSDesignObjectType);
			outDesignObject.ObjectTypeInstance = this.ObjectTypeInstance;
			outDesignObject.Bounds = this.Bounds;
			outDesignObject.RotateFlip = this.RotateFlip;
			
			outDesignObject.SourceType = this.SourceType;
			if (!bCreating)
			{
				outDesignObject.SourceName = this.SourceName;
				outDesignObject.DesignObjectImageName = this.DesignObjectImageName;
				outDesignObject.Text = this.Text;
				outDesignObject.Formula = this.Formula;
				outDesignObject.VisibleIf = this.VisibleIf;
				outDesignObject.VisibleIfCondition = this.VisibleIfCondition;
				outDesignObject.VisibleIfEval = this.VisibleIfEval;
			}
			outDesignObject.FontIndex = this.FontIndex;
			outDesignObject.FontEx = this.FontEx.Clone();

			outDesignObject.TxtFormat = this.TxtFormat;
			outDesignObject.CaseIndex = this.CaseIndex;
			outDesignObject.Alignment = this.Alignment;
			outDesignObject.Justification = this.Justification;

			// background
			outDesignObject.BackFillType = this.BackFillType;
			outDesignObject.BackColor = this.BackColor;
			outDesignObject.BackColor2 = this.BackColor2;
			outDesignObject.BackGradientType = this.BackGradientType;
			outDesignObject.ColorChoice1 = this.ColorChoice1;
			outDesignObject.ColorChoice2 = this.ColorChoice2;
			outDesignObject.ColorChoice3 = this.ColorChoice3;
			outDesignObject.ColorCondition1 = this.ColorCondition1;
			outDesignObject.ColorCondition2 = this.ColorCondition2;
			outDesignObject.ColorEval = this.ColorEval;
			
			outDesignObject.BackImageName = this.BackImageName;
			outDesignObject.LineColor = this.LineColor;
			outDesignObject.LineWidth = this.LineWidth;
			outDesignObject.Radius = this.Radius;
			outDesignObject.IcaoType = this.IcaoType;
            outDesignObject.SpecialKPanel = this.SpecialKPanel;

			switch(this.DCSDesignObjectType)
			{
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.TextObj:
					outDesignObject.LabelOn = this.LabelOn;
					outDesignObject.LabelText = this.LabelText;
					outDesignObject.LabelFontIndex = this.LabelFontIndex;
					outDesignObject.LabelOrientation = this.LabelOrientation;
					outDesignObject.LabelOffset = this.LabelOffset;
					break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode2D:
					break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode:
					outDesignObject.BarcodeCode = this.BarcodeCode;
					outDesignObject.BarcodeShowText = this.BarcodeShowText;
					break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ImageObj:
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Portrait:
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Signature:
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Fingerprint:
					outDesignObject.Scaling = this.Scaling;
					outDesignObject.Framing = this.Framing;
					outDesignObject.Transparency = this.Transparency;
					outDesignObject.GrayScale = this.GrayScale;
					outDesignObject.BackDetectEnabled = this.BackDetectEnabled;
					outDesignObject.ColorToDetect = this.ColorToDetect;
					outDesignObject.ColorDetectThreshold = this.ColorDetectThreshold;
					outDesignObject.AutoKey = this.AutoKey;
					outDesignObject.PortraitAutoKey = this.PortraitAutoKey;
					break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ICAOMRZ:
					break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.GraphicBlock:
					break;
				default:
					break;
			}
			return outDesignObject;
		}
		// set properties as default of a specified designObject type
		private void SetProperties(DCSDEV.DCSDatatypes.DCSDesignObjectTypes designObjectType)
		{
			SetProperties();
			this.DCSDesignObjectType = designObjectType;
			switch(designObjectType)
			{
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.TextObj:
					this.Bounds.Width = 100;
					this.Bounds.Height = 16;
					this.IfLockAspect = false;
					break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode2D:
					this.Bounds.Width = 125;
					this.Bounds.Height = 30;
					this.Justification = DCSDEV.DCSDatatypes.Justifications.CENTER;
					this.Alignment = DCSDEV.DCSDatatypes.Alignments.MIDDLE;
					this.FontEx.ForeColor = Color.Black;
					this.BackFillType = DCSDEV.DCSDatatypes.BackFillTypes.FILL_COLOR;
					this.BackColor = Color.White;
					this.IfLockAspect = false;
					this.LineColor = Color.White;
					this.LineWidth = 5;
					this.Radius = 0;
                    break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode:
					this.Bounds.Width = 125;
					this.Bounds.Height = 30;
					this.Justification = DCSDEV.DCSDatatypes.Justifications.CENTER;
					this.Alignment = DCSDEV.DCSDatatypes.Alignments.MIDDLE;
					this.FontEx.ForeColor = Color.Black;
					this.BackFillType = DCSDEV.DCSDatatypes.BackFillTypes.FILL_COLOR;
					this.BackColor = Color.White;
					this.IfLockAspect = false;
                    break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ImageObj:
					this.Bounds.Width = 100;
					this.Bounds.Height = 100;
					this.IfLockAspect = false;
                    break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Portrait:
					this.Bounds.Width = 100;
					this.Bounds.Height = 125;
					this.IfLockAspect = true;
					this.Scaling = DCSDEV.DCSDatatypes.ScaleMode.KeepAspect;
                    break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Signature:
					this.Bounds.Width = 140;
					this.Bounds.Height = 40;
					this.IfLockAspect = true;
					this.Scaling = DCSDEV.DCSDatatypes.ScaleMode.KeepAspect;
                    break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Fingerprint:
					this.Bounds.Width = 75;
					this.Bounds.Height = 75;
					this.IfLockAspect = true;
					this.Scaling = DCSDEV.DCSDatatypes.ScaleMode.KeepAspect;
                    break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ICAOMRZ:
					this.Bounds.Width = 300;
					this.Bounds.Height = 40;
					this.SourceType = DCSDEV.DCSDatatypes.SourceTypes.Database;
					this.Justification = DCSDEV.DCSDatatypes.Justifications.CENTER;
					this.FontEx.ForeColor = Color.Black;
					this.BackFillType = DCSDEV.DCSDatatypes.BackFillTypes.FILL_COLOR;
					this.BackColor = Color.White;
					this.IfLockAspect = false;
                    break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.GraphicBlock:
					this.Bounds.Width = 300;
					this.Bounds.Height = 40;
					this.SourceType = DCSDEV.DCSDatatypes.SourceTypes.StaticValue;
					this.FontEx.ForeColor = Color.Gray;	// unused
					this.BackFillType = DCSDEV.DCSDatatypes.BackFillTypes.FILL_COLOR;
					this.BackColor = Color.Black;
                    break;
				default:
					break;
			}
		}

		private void SetProperties()
		{
			this.Side = 0;	// default to front side
			this.DCSDesignObjectType = DCSDEV.DCSDatatypes.DCSDesignObjectTypes.TextObj;
			this.ObjectTypeInstance = 0;
			this.Bounds = Rectangle.Empty;
			this.Bounds.Width = 100;
			this.Bounds.Height = 100;
			this.RotateFlip = RotateFlipType.RotateNoneFlipNone;
		
			// foreground
			this.SourceType = DCSDEV.DCSDatatypes.SourceTypes.StaticValue;
			this.SourceName = "";
			this.Formula = "";
			this.VisibleIf = false;
			this.VisibleIfCondition = null;
			this.VisibleIfEval = true;
			this.DesignObjectImageName = null;
			this.DesignObjectImage = null;
			this.Text = ""; // <= 5K

			// LabeledText
			LabelOn = false;
			LabelText = "label";
			LabelFontIndex = 0;
			LabelOrientation = DCSDatatypes.DCSLabeledTextOrientations.TOP;
			LabelOffset = 15;

			this.FontIndex = -1;
			this.FontEx = new DCSFontEx();

			this.CaseIndex = 0;
			this.TxtFormat = "app format";
			this.Alignment = DCSDEV.DCSDatatypes.Alignments.TOP;
			this.Justification = DCSDEV.DCSDatatypes.Justifications.LEFT;

			// background
			this.BackFillType = DCSDEV.DCSDatatypes.BackFillTypes.FILL_CLEAR;
			this.BackColor = Color.White;
			this.BackColor2 = Color.SeaGreen;
			this.BackGradientType = System.Drawing.Drawing2D.LinearGradientMode.Horizontal;
			this.ColorChoice1 = Color.White;
			this.ColorChoice2 = Color.Black;
			this.ColorChoice3 = Color.Gray;
			this.ColorCondition1 = "";
			this.ColorCondition2 = "";
			this.ColorEval = Color.Gray;

			this.Transparency = 0; // 0-99
			this.BackImageName = null; 
			this.BackImage = null;
			this.Scaling = DCSDEV.DCSDatatypes.ScaleMode.ScaleToFit;
			this.Framing = DCSDEV.DCSDatatypes.FramingMode.NONE;

			// process
			this.IfLockAspect = false;
			this.GroupID = 0; // >= 0
			this.Locked = false;
			this.ZOrder = 0; // >= 0
            this.SpecialKPanel = 0;

			// border
			this.LineColor = Color.Black;
			this.LineWidth = 0;
			this.Radius = 0;

			this.GrayScale = false;
			this.BackDetectEnabled = false;
			this.ColorDetectThreshold = 23;
			this.ColorToDetect = Color.Black;

			this.AutoKey=false;
			this.PortraitAutoKey = false;
			this.BarcodeCode = 5;	// syh need a good default
			this.BarcodeShowText = true;
			this.IcaoType = 0;
		}
	}
}
