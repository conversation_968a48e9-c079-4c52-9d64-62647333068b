using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;

using DCSDEV;

namespace DCSDEV.DCSDesigner
{
	/// <summary>
	/// Summary description for DCSFormulaDesigner.
	/// </summary>
    public class DCSFormulaDesigner : System.Windows.Forms.Form
	{
		enum InsertType { FIELD, TEXT, FUNCTION };
		enum FormulaType { BIO_FUNCTION, SQL_FORMULA };
		public enum FormulaModeType { SQL_IF, SQL_FORMULA, ALL_MODES };

		private FormulaModeType m_eFormulaDesignerMode;
		private FormulaType m_eFormulaType;
		private ArrayList m_arrayAllDBFieldNames;
		private ArrayList m_arrayBINDescription;
		private ArrayList m_arrayUndo;

		private System.Windows.Forms.TextBox textBoxFormula;
		private System.Windows.Forms.Button buttonAppendDatabaseField;
		private System.Windows.Forms.Button buttonAppendText;
		private System.Windows.Forms.ComboBox comboBoxBINToAppend;
		private System.Windows.Forms.Button buttonClear;
		private System.Windows.Forms.Button buttonOK;
		private System.Windows.Forms.Button buttonCancel;
		private System.Windows.Forms.Button buttonUndo;
		private System.Windows.Forms.TextBox textBoxBINDescription;
		private RadioButton radioButtonBiometricExpression;
		private RadioButton radioButtonFormula;
		private Button buttonAppendFunction;
		private Button buttonInsertFunction;
		private Button buttonInsertText;
		private Button buttonInsertDatabaseField;
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		public DCSFormulaDesigner(ArrayList arrayAllDBFieldNames)
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			m_arrayAllDBFieldNames = arrayAllDBFieldNames;

			m_arrayUndo = new ArrayList();
			this.buttonUndo.Enabled = false;

//syh/			foreach(string str in arrayAllDBFieldNames) 
//				this.comboBoxFieldToAppend.Items.Add(str);
			
			this.comboBoxBINToAppend.Items.Add("BADGE.DAT");

			this.comboBoxBINToAppend.Items.Add("BIN2_ALL");
			this.comboBoxBINToAppend.Items.Add("BIN2_L1");
			this.comboBoxBINToAppend.Items.Add("BIN2_L2");
			this.comboBoxBINToAppend.Items.Add("BIN2_A1");
			this.comboBoxBINToAppend.Items.Add("BIN2_L1A1");
			this.comboBoxBINToAppend.Items.Add("BIN2_L2A1");
			this.comboBoxBINToAppend.Items.Add("BIN2_A2");

			m_arrayBINDescription = new ArrayList();
			m_arrayBINDescription.Add("BADGE.DAT = Contents of Badge Data File.");
			m_arrayBINDescription.Add("BIN2_ALL = All BINs.");
			m_arrayBINDescription.Add("BIN2_L1 = 1 Liska BIN.");
			m_arrayBINDescription.Add("BIN2_L2 = 2 Liska BIN2s or less.");
			m_arrayBINDescription.Add("BIN2_A1 = 2 Liska BINs or 1 AIS BIN or less.");
			m_arrayBINDescription.Add("BIN2_L1A1 = 2 Liska BINs or 1 Liska Bin and 1 AIS BIN or less.");
			m_arrayBINDescription.Add("BIN2_L2A1 = 2 Liska BINs and 1 AIS BIN or less.");
			m_arrayBINDescription.Add("BIN2_A2 = 2 AIS BINs or less.");

			m_eFormulaType = FormulaType.SQL_FORMULA;
			m_eFormulaDesignerMode = FormulaModeType.ALL_MODES;
			this.radioButtonBiometricExpression.Checked = false;
			this.radioButtonFormula.Checked = true;
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(DCSFormulaDesigner));
			this.textBoxFormula = new System.Windows.Forms.TextBox();
			this.buttonAppendDatabaseField = new System.Windows.Forms.Button();
			this.buttonAppendText = new System.Windows.Forms.Button();
			this.comboBoxBINToAppend = new System.Windows.Forms.ComboBox();
			this.buttonClear = new System.Windows.Forms.Button();
			this.buttonOK = new System.Windows.Forms.Button();
			this.buttonCancel = new System.Windows.Forms.Button();
			this.buttonUndo = new System.Windows.Forms.Button();
			this.textBoxBINDescription = new System.Windows.Forms.TextBox();
			this.radioButtonBiometricExpression = new System.Windows.Forms.RadioButton();
			this.radioButtonFormula = new System.Windows.Forms.RadioButton();
			this.buttonAppendFunction = new System.Windows.Forms.Button();
			this.buttonInsertFunction = new System.Windows.Forms.Button();
			this.buttonInsertText = new System.Windows.Forms.Button();
			this.buttonInsertDatabaseField = new System.Windows.Forms.Button();
			this.SuspendLayout();
			// 
			// textBoxFormula
			// 
			resources.ApplyResources(this.textBoxFormula, "textBoxFormula");
			this.textBoxFormula.Name = "textBoxFormula";
			// 
			// buttonAppendDatabaseField
			// 
			resources.ApplyResources(this.buttonAppendDatabaseField, "buttonAppendDatabaseField");
			this.buttonAppendDatabaseField.Name = "buttonAppendDatabaseField";
			this.buttonAppendDatabaseField.Click += new System.EventHandler(this.buttonAppendDatabaseField_Click);
			// 
			// buttonAppendText
			// 
			resources.ApplyResources(this.buttonAppendText, "buttonAppendText");
			this.buttonAppendText.Name = "buttonAppendText";
			this.buttonAppendText.Click += new System.EventHandler(this.buttonAppendText_Click);
			// 
			// comboBoxBINToAppend
			// 
			resources.ApplyResources(this.comboBoxBINToAppend, "comboBoxBINToAppend");
			this.comboBoxBINToAppend.Name = "comboBoxBINToAppend";
			this.comboBoxBINToAppend.SelectedIndexChanged += new System.EventHandler(this.comboBoxBINToAppend_SelectedIndexChanged);
			// 
			// buttonClear
			// 
			resources.ApplyResources(this.buttonClear, "buttonClear");
			this.buttonClear.Name = "buttonClear";
			this.buttonClear.Click += new System.EventHandler(this.buttonClear_Click);
			// 
			// buttonOK
			// 
			this.buttonOK.DialogResult = System.Windows.Forms.DialogResult.OK;
			resources.ApplyResources(this.buttonOK, "buttonOK");
			this.buttonOK.Name = "buttonOK";
			this.buttonOK.Click += new System.EventHandler(this.buttonOK_Click);
			// 
			// buttonCancel
			// 
			this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			resources.ApplyResources(this.buttonCancel, "buttonCancel");
			this.buttonCancel.Name = "buttonCancel";
			this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
			// 
			// buttonUndo
			// 
			resources.ApplyResources(this.buttonUndo, "buttonUndo");
			this.buttonUndo.Name = "buttonUndo";
			this.buttonUndo.Click += new System.EventHandler(this.buttonUndo_Click);
			// 
			// textBoxBINDescription
			// 
			this.textBoxBINDescription.BackColor = System.Drawing.SystemColors.Control;
			this.textBoxBINDescription.BorderStyle = System.Windows.Forms.BorderStyle.None;
			resources.ApplyResources(this.textBoxBINDescription, "textBoxBINDescription");
			this.textBoxBINDescription.Name = "textBoxBINDescription";
			// 
			// radioButtonBiometricExpression
			// 
			resources.ApplyResources(this.radioButtonBiometricExpression, "radioButtonBiometricExpression");
			this.radioButtonBiometricExpression.Name = "radioButtonBiometricExpression";
			this.radioButtonBiometricExpression.TabStop = true;
			this.radioButtonBiometricExpression.UseVisualStyleBackColor = true;
			this.radioButtonBiometricExpression.Click += new System.EventHandler(this.radioButtonBiometricExpression_Click);
			// 
			// radioButtonFormula
			// 
			resources.ApplyResources(this.radioButtonFormula, "radioButtonFormula");
			this.radioButtonFormula.Name = "radioButtonFormula";
			this.radioButtonFormula.TabStop = true;
			this.radioButtonFormula.UseVisualStyleBackColor = true;
			this.radioButtonFormula.Click += new System.EventHandler(this.radioButtonFormula_Click);
			// 
			// buttonAppendFunction
			// 
			resources.ApplyResources(this.buttonAppendFunction, "buttonAppendFunction");
			this.buttonAppendFunction.Name = "buttonAppendFunction";
			this.buttonAppendFunction.Click += new System.EventHandler(this.buttonAppendFunction_Click);
			// 
			// buttonInsertFunction
			// 
			resources.ApplyResources(this.buttonInsertFunction, "buttonInsertFunction");
			this.buttonInsertFunction.Name = "buttonInsertFunction";
			this.buttonInsertFunction.Click += new System.EventHandler(this.buttonInsertFunction_Click);
			// 
			// buttonInsertText
			// 
			resources.ApplyResources(this.buttonInsertText, "buttonInsertText");
			this.buttonInsertText.Name = "buttonInsertText";
			this.buttonInsertText.Click += new System.EventHandler(this.buttonInsertText_Click);
			// 
			// buttonInsertDatabaseField
			// 
			resources.ApplyResources(this.buttonInsertDatabaseField, "buttonInsertDatabaseField");
			this.buttonInsertDatabaseField.Name = "buttonInsertDatabaseField";
			this.buttonInsertDatabaseField.Click += new System.EventHandler(this.buttonInsertDatabaseField_Click);
			// 
			// DCSFormulaDesigner
			// 
			this.AcceptButton = this.buttonOK;
			resources.ApplyResources(this, "$this");
			this.CancelButton = this.buttonCancel;
			this.Controls.Add(this.buttonInsertFunction);
			this.Controls.Add(this.buttonInsertText);
			this.Controls.Add(this.buttonInsertDatabaseField);
			this.Controls.Add(this.buttonAppendFunction);
			this.Controls.Add(this.radioButtonFormula);
			this.Controls.Add(this.radioButtonBiometricExpression);
			this.Controls.Add(this.textBoxBINDescription);
			this.Controls.Add(this.textBoxFormula);
			this.Controls.Add(this.buttonUndo);
			this.Controls.Add(this.buttonCancel);
			this.Controls.Add(this.buttonOK);
			this.Controls.Add(this.buttonClear);
			this.Controls.Add(this.comboBoxBINToAppend);
			this.Controls.Add(this.buttonAppendText);
			this.Controls.Add(this.buttonAppendDatabaseField);
			this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
			this.MaximizeBox = false;
			this.MinimizeBox = false;
			this.Name = "DCSFormulaDesigner";
			this.ShowInTaskbar = false;
			this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
			this.ResumeLayout(false);
			this.PerformLayout();

		}
		#endregion

		private string GetStringToInput(InsertType insertType)
		{
			DCSDEV.DCSDesigner.DCSFormulaAddField dlg_field;
			DCSDEV.DCSDesigner.DCSFormulaAddText dlg_text;
			DCSDEV.DCSDesigner.DCSFormulaAddFunction dlg_func;
			DialogResult result;
			string strToInsert = "";
			switch (insertType)
			{
				case InsertType.FIELD:
					dlg_field = new DCSDEV.DCSDesigner.DCSFormulaAddField(m_arrayAllDBFieldNames);
					result = dlg_field.ShowDialog();
					if (result == DialogResult.Cancel) return "";
					strToInsert = dlg_field.FieldToAppend;
					break;
				case InsertType.TEXT:
					dlg_text = new DCSDEV.DCSDesigner.DCSFormulaAddText();
					result = dlg_text.ShowDialog();
					if (result == DialogResult.Cancel) return "";
					strToInsert = dlg_text.TextToAppend;
					break;
				case InsertType.FUNCTION:
					dlg_func = new DCSDEV.DCSDesigner.DCSFormulaAddFunction();
					result = dlg_func.ShowDialog();
					if (result == DialogResult.Cancel) return "";
					strToInsert = dlg_func.FunctionToAdd;
					break;
			}
			return strToInsert;
		}

		private void AppendStringToInsert(string strToInsert)
		{
			m_arrayUndo.Add(this.textBoxFormula.Text);
			this.buttonUndo.Enabled = true;

			// add plus for concatenate if the is something to append to
			if (this.textBoxFormula.Text != "") this.textBoxFormula.Text += " + ";

			this.textBoxFormula.Text += strToInsert;

			// set insert location to the end
			this.textBoxFormula.SelectionStart = this.textBoxFormula.Text.Length;
			this.textBoxFormula.SelectionLength = 0;
		}

		private void buttonAppendDatabaseField_Click(object sender, System.EventArgs e)
		{
			string strToAppend = this.GetStringToInput(InsertType.FIELD);
			if (strToAppend != "") this.AppendStringToInsert("[" + strToAppend + "]");
		}

		private void buttonAppendText_Click(object sender, System.EventArgs e)
		{
			string strToAppend = this.GetStringToInput(InsertType.TEXT);
			if (strToAppend != "") this.AppendStringToInsert("\"" + strToAppend + "\"");
		}

		private void buttonAppendFunction_Click(object sender, EventArgs e)
		{
			string strToAppend = this.GetStringToInput(InsertType.FUNCTION);
			if (strToAppend != "") this.AppendStringToInsert(strToAppend);
		}

		private void InsertStringToInsert(string strToInsert)
		{
			m_arrayUndo.Add(this.textBoxFormula.Text);
			this.buttonUndo.Enabled = true;

			string str;
			str = this.textBoxFormula.Text;
			int start = this.textBoxFormula.SelectionStart;
			int len = this.textBoxFormula.SelectionLength;
			if (this.textBoxFormula.SelectedText != "")
			{
				bool bAddSpace = (this.textBoxFormula.SelectedText).EndsWith(" ");
				this.textBoxFormula.Text = str.Substring(0, start)
					+ strToInsert
					+ (bAddSpace ? " " : "")
					+ str.Substring(start + len);
			}
			else if (str.Length == 0)
			{
				this.textBoxFormula.Text = strToInsert;
			}
			else
			{
				// look for previous operator
				string strPrevious = null;
				bool bPreviousToken = false;
				for (int i = start - 1; i >= 0; i--)
				{
					strPrevious = str.Substring(i,1);
					if (strPrevious == " ") continue;
					if ("+&=<>(,)".IndexOf(strPrevious) < 0) bPreviousToken = true;
					break;
				}
				// look for following operator
				string strFollow = null;
				bool bFollowToken = false;
				for (int i = start+len; i < str.Length; i++)
				{
					strFollow = str.Substring(i, 1);
					if (strFollow == " ") continue;
					if ("+&=<>(,)".IndexOf(strFollow) < 0) bFollowToken = true;
					break;
				}
				string strToInsertEx;
				if (bPreviousToken) strToInsertEx = " + " + strToInsert;
				else if (bFollowToken) strToInsertEx = strToInsert + " + ";
				else strToInsertEx = strToInsert;

				this.textBoxFormula.Text = str.Substring(0, start)
					+ strToInsertEx
					+ str.Substring(start + len);
			}
			this.textBoxFormula.SelectionStart = this.textBoxFormula.Text.Length;
			this.textBoxFormula.SelectionLength = 0;
		}

		private void buttonInsertDatabaseField_Click(object sender, EventArgs e)
		{
			string strToInsert = this.GetStringToInput(InsertType.FIELD);
			if (strToInsert != "") this.InsertStringToInsert("[" + strToInsert + "]");
		}

		private void buttonInsertText_Click(object sender, EventArgs e)
		{
			string strToInsert = this.GetStringToInput(InsertType.TEXT);
			if (strToInsert != "") this.InsertStringToInsert("\"" + strToInsert + "\"");
		}

		private void buttonInsertFunction_Click(object sender, EventArgs e)
		{
			string strToInsert = this.GetStringToInput(InsertType.FUNCTION);
			if (strToInsert != "") this.InsertStringToInsert(strToInsert);
		}

		private void buttonClear_Click(object sender, System.EventArgs e)
		{
			m_arrayUndo.Add(this.textBoxFormula.Text);
			this.buttonUndo.Enabled = true;
			this.textBoxFormula.Text = "";
		}
		private void buttonOK_Click(object sender, System.EventArgs e)
		{
			if (m_eFormulaType == FormulaType.BIO_FUNCTION)
			{
				if (this.comboBoxBINToAppend.Text == "")
				{
					this.DialogResult = DialogResult.Cancel;
					DCSMsg.Show("The biometric expression is blank");
					return;
				}
			}
			else
			{
				if (this.textBoxFormula.Text == "")
				{
					this.DialogResult = DialogResult.Cancel;
					DCSMsg.Show("The formula expression is blank");
					return;
				}
			}
			this.Close();
		}

		private void buttonCancel_Click(object sender, System.EventArgs e)
		{
			this.Close();
		}

		private void buttonUndo_Click(object sender, System.EventArgs e)
		{
			int idx = this.m_arrayUndo.Count;
			if (idx == 0) return;
			idx--;
			this.textBoxFormula.Text = (string)this.m_arrayUndo[idx];
			this.m_arrayUndo.RemoveAt(idx);
			this.buttonUndo.Enabled = (this.m_arrayUndo.Count > 0);
		}

		private void comboBoxBINToAppend_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			this.textBoxBINDescription.Text = (string)m_arrayBINDescription[this.comboBoxBINToAppend.SelectedIndex];
		}

		public string Formula
		{
			get 
			{
				if (this.radioButtonBiometricExpression.Checked)
				{
					if (this.comboBoxBINToAppend.Text == "") return "";
					return "@" + this.comboBoxBINToAppend.Text;
				}
				else
					return this.textBoxFormula.Text; 
			}
			set 
			{
				if (value.Length > 0 && value.Substring(0, 1) == "@")
				{
					this.comboBoxBINToAppend.Text = value.Substring(1);
					m_eFormulaType = FormulaType.BIO_FUNCTION;
					this.radioButtonBiometricExpression.Checked = true;
					this.radioButtonFormula.Checked = false;
				}
				else
				{
					this.textBoxFormula.Text = value;
					m_eFormulaType = FormulaType.SQL_FORMULA;
					this.radioButtonBiometricExpression.Checked = false;
					this.radioButtonFormula.Checked = true;
				}
				this.AdjustViz();
			}
		}
		public FormulaModeType FormulaDesignerMode
		{
			get { return m_eFormulaDesignerMode; }
			set 
			{ 
				m_eFormulaDesignerMode = value;
				if (m_eFormulaDesignerMode == FormulaModeType.SQL_IF || m_eFormulaDesignerMode == FormulaModeType.SQL_FORMULA)
				{
					m_eFormulaType = FormulaType.SQL_FORMULA;
					this.radioButtonBiometricExpression.Visible = false;
				}
			}
		}
		private void AdjustViz()
		{
			bool ifViz;
			ifViz = (m_eFormulaType == FormulaType.BIO_FUNCTION);
			this.comboBoxBINToAppend.Visible = ifViz;
			this.textBoxFormula.Visible = !ifViz;
			this.radioButtonBiometricExpression.Checked = ifViz;
			this.radioButtonFormula.Checked = !ifViz;
			this.buttonClear.Visible = !ifViz;
			this.buttonUndo.Visible = !ifViz;

			this.buttonAppendDatabaseField.Visible = !ifViz;
			this.buttonAppendText.Visible = !ifViz;
			this.buttonAppendFunction.Visible = !ifViz;
			this.buttonInsertDatabaseField.Visible = !ifViz;
			this.buttonInsertText.Visible = !ifViz;
			this.buttonInsertFunction.Visible = !ifViz;

			this.comboBoxBINToAppend.Visible = ifViz;
			this.textBoxBINDescription.Visible = ifViz;
		}

        internal static void CallFormulaBuilder(ArrayList arrayAllDBFieldNames, ref string strFormula, FormulaModeType formulaMode )
	    {
            DCSDEV.DCSDesigner.DCSFormulaDesigner dlg = new DCSDEV.DCSDesigner.DCSFormulaDesigner(arrayAllDBFieldNames);
		    if (strFormula != null) dlg.Formula = strFormula;
		    else dlg.Formula = "";
			dlg.FormulaDesignerMode = formulaMode;
		    DialogResult result = dlg.ShowDialog();
		    if (result == DialogResult.OK) strFormula = dlg.Formula;
		    else strFormula = null;
		    return;
	    }

		private void radioButtonFormula_Click(object sender, EventArgs e)
		{
			if (m_eFormulaType == FormulaType.BIO_FUNCTION) m_eFormulaType = FormulaType.SQL_FORMULA;
			else m_eFormulaType = FormulaType.BIO_FUNCTION;
			this.AdjustViz();
		}

		private void radioButtonBiometricExpression_Click(object sender, EventArgs e)
		{
			if (m_eFormulaType == FormulaType.BIO_FUNCTION) m_eFormulaType = FormulaType.SQL_FORMULA;
			else m_eFormulaType = FormulaType.BIO_FUNCTION;
			this.AdjustViz();
		}
	}
}
