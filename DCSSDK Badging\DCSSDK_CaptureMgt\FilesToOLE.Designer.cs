namespace DCSDEV.FilesToOle
{
    partial class FilesToOLE
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBoxOleDBType = new System.Windows.Forms.GroupBox();
            this.radioButtonOracle = new System.Windows.Forms.RadioButton();
            this.radioButtonSQL = new System.Windows.Forms.RadioButton();
            this.radioButtonAccess = new System.Windows.Forms.RadioButton();
            this.groupBoxExistingFiles = new System.Windows.Forms.GroupBox();
            this.radioButtonDontCopy = new System.Windows.Forms.RadioButton();
            this.radioButtonCopyFiles = new System.Windows.Forms.RadioButton();
            this.radioButtonMoveFiles = new System.Windows.Forms.RadioButton();
            this.label1 = new System.Windows.Forms.Label();
            this.labelInstructions = new System.Windows.Forms.Label();
            this.buttonAccept = new System.Windows.Forms.Button();
            this.buttonCancel = new System.Windows.Forms.Button();
            this.labelServerName = new System.Windows.Forms.Label();
            this.comboBoxServerName = new System.Windows.Forms.ComboBox();
            this.labelAccessDB = new System.Windows.Forms.Label();
            this.labelAccessDBText = new System.Windows.Forms.Label();
            this.labelDBName = new System.Windows.Forms.Label();
            this.labelTableNameText = new System.Windows.Forms.Label();
            this.labelTableName = new System.Windows.Forms.Label();
            this.labelDataRootDirectory = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.labelWait = new System.Windows.Forms.Label();
            this.comboBoxDBCatalog = new System.Windows.Forms.ComboBox();
            this.labelUsersDBFields = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.groupBoxOleDBType.SuspendLayout();
            this.groupBoxExistingFiles.SuspendLayout();
            this.SuspendLayout();
            // 
            // groupBoxOleDBType
            // 
            this.groupBoxOleDBType.Controls.Add(this.radioButtonOracle);
            this.groupBoxOleDBType.Controls.Add(this.radioButtonSQL);
            this.groupBoxOleDBType.Controls.Add(this.radioButtonAccess);
            this.groupBoxOleDBType.Location = new System.Drawing.Point(34, 96);
            this.groupBoxOleDBType.Name = "groupBoxOleDBType";
            this.groupBoxOleDBType.Size = new System.Drawing.Size(136, 107);
            this.groupBoxOleDBType.TabIndex = 2;
            this.groupBoxOleDBType.TabStop = false;
            this.groupBoxOleDBType.Text = "OLE DB Type";
            // 
            // radioButtonOracle
            // 
            this.radioButtonOracle.AutoSize = true;
            this.radioButtonOracle.Location = new System.Drawing.Point(30, 72);
            this.radioButtonOracle.Name = "radioButtonOracle";
            this.radioButtonOracle.Size = new System.Drawing.Size(56, 17);
            this.radioButtonOracle.TabIndex = 2;
            this.radioButtonOracle.TabStop = true;
            this.radioButtonOracle.Text = "Oracle";
            this.radioButtonOracle.UseVisualStyleBackColor = true;
            this.radioButtonOracle.Visible = false;
            this.radioButtonOracle.CheckedChanged += new System.EventHandler(this.radioButtonOracle_CheckedChanged);
            // 
            // radioButtonSQL
            // 
            this.radioButtonSQL.AutoSize = true;
            this.radioButtonSQL.Location = new System.Drawing.Point(30, 49);
            this.radioButtonSQL.Name = "radioButtonSQL";
            this.radioButtonSQL.Size = new System.Drawing.Size(65, 17);
            this.radioButtonSQL.TabIndex = 1;
            this.radioButtonSQL.TabStop = true;
            this.radioButtonSQL.Text = "MS SQL";
            this.radioButtonSQL.UseVisualStyleBackColor = true;
            this.radioButtonSQL.CheckedChanged += new System.EventHandler(this.radioButtonSQL_CheckedChanged);
            // 
            // radioButtonAccess
            // 
            this.radioButtonAccess.AutoSize = true;
            this.radioButtonAccess.Location = new System.Drawing.Point(30, 26);
            this.radioButtonAccess.Name = "radioButtonAccess";
            this.radioButtonAccess.Size = new System.Drawing.Size(79, 17);
            this.radioButtonAccess.TabIndex = 0;
            this.radioButtonAccess.TabStop = true;
            this.radioButtonAccess.Text = "MS Access";
            this.radioButtonAccess.UseVisualStyleBackColor = true;
            this.radioButtonAccess.CheckedChanged += new System.EventHandler(this.radioButtonAccess_CheckedChanged);
            // 
            // groupBoxExistingFiles
            // 
            this.groupBoxExistingFiles.Controls.Add(this.radioButtonDontCopy);
            this.groupBoxExistingFiles.Controls.Add(this.radioButtonCopyFiles);
            this.groupBoxExistingFiles.Controls.Add(this.radioButtonMoveFiles);
            this.groupBoxExistingFiles.Location = new System.Drawing.Point(241, 96);
            this.groupBoxExistingFiles.Name = "groupBoxExistingFiles";
            this.groupBoxExistingFiles.Size = new System.Drawing.Size(317, 107);
            this.groupBoxExistingFiles.TabIndex = 3;
            this.groupBoxExistingFiles.TabStop = false;
            this.groupBoxExistingFiles.Text = "What to do with existng image files";
            // 
            // radioButtonDontCopy
            // 
            this.radioButtonDontCopy.AutoSize = true;
            this.radioButtonDontCopy.Location = new System.Drawing.Point(26, 68);
            this.radioButtonDontCopy.Name = "radioButtonDontCopy";
            this.radioButtonDontCopy.Size = new System.Drawing.Size(104, 17);
            this.radioButtonDontCopy.TabIndex = 2;
            this.radioButtonDontCopy.TabStop = true;
            this.radioButtonDontCopy.Text = "Do not copy files";
            this.radioButtonDontCopy.TextAlign = System.Drawing.ContentAlignment.TopRight;
            this.radioButtonDontCopy.UseVisualStyleBackColor = true;
            // 
            // radioButtonCopyFiles
            // 
            this.radioButtonCopyFiles.AutoSize = true;
            this.radioButtonCopyFiles.Location = new System.Drawing.Point(26, 48);
            this.radioButtonCopyFiles.Name = "radioButtonCopyFiles";
            this.radioButtonCopyFiles.Size = new System.Drawing.Size(153, 17);
            this.radioButtonCopyFiles.TabIndex = 1;
            this.radioButtonCopyFiles.TabStop = true;
            this.radioButtonCopyFiles.Text = "Copy files to OLE database";
            this.radioButtonCopyFiles.UseVisualStyleBackColor = true;
            // 
            // radioButtonMoveFiles
            // 
            this.radioButtonMoveFiles.AutoSize = true;
            this.radioButtonMoveFiles.Location = new System.Drawing.Point(26, 25);
            this.radioButtonMoveFiles.Name = "radioButtonMoveFiles";
            this.radioButtonMoveFiles.Size = new System.Drawing.Size(256, 17);
            this.radioButtonMoveFiles.TabIndex = 0;
            this.radioButtonMoveFiles.TabStop = true;
            this.radioButtonMoveFiles.Text = "Move files to OLE database.  Delete original files.";
            this.radioButtonMoveFiles.UseVisualStyleBackColor = true;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label1.Location = new System.Drawing.Point(31, 16);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(333, 16);
            this.label1.TabIndex = 2;
            this.label1.Text = "The existing image storage uses files in subdirectories.";
            // 
            // labelInstructions
            // 
            this.labelInstructions.AutoSize = true;
            this.labelInstructions.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelInstructions.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(192)))));
            this.labelInstructions.Location = new System.Drawing.Point(30, 339);
            this.labelInstructions.Name = "labelInstructions";
            this.labelInstructions.Size = new System.Drawing.Size(512, 20);
            this.labelInstructions.TabIndex = 3;
            this.labelInstructions.Text = "Click ACCEPT to convert image storage to records in an OLE database.";
            // 
            // buttonAccept
            // 
            this.buttonAccept.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.buttonAccept.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.buttonAccept.Location = new System.Drawing.Point(365, 381);
            this.buttonAccept.Name = "buttonAccept";
            this.buttonAccept.Size = new System.Drawing.Size(96, 24);
            this.buttonAccept.TabIndex = 0;
            this.buttonAccept.Text = "&Accept";
            this.buttonAccept.Click += new System.EventHandler(this.buttonAccept_Click);
            // 
            // buttonCancel
            // 
            this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.buttonCancel.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.buttonCancel.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.buttonCancel.Location = new System.Drawing.Point(477, 381);
            this.buttonCancel.Name = "buttonCancel";
            this.buttonCancel.Size = new System.Drawing.Size(96, 24);
            this.buttonCancel.TabIndex = 1;
            this.buttonCancel.Text = "Cancel";
            this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
            // 
            // labelServerName
            // 
            this.labelServerName.AutoSize = true;
            this.labelServerName.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelServerName.Location = new System.Drawing.Point(31, 224);
            this.labelServerName.Name = "labelServerName";
            this.labelServerName.Size = new System.Drawing.Size(85, 16);
            this.labelServerName.TabIndex = 5;
            this.labelServerName.Text = "Server name";
            this.labelServerName.Visible = false;
            // 
            // comboBoxServerName
            // 
            this.comboBoxServerName.FormattingEnabled = true;
            this.comboBoxServerName.Location = new System.Drawing.Point(241, 223);
            this.comboBoxServerName.Name = "comboBoxServerName";
            this.comboBoxServerName.Size = new System.Drawing.Size(204, 21);
            this.comboBoxServerName.TabIndex = 6;
            this.comboBoxServerName.Visible = false;
            this.comboBoxServerName.SelectedValueChanged += new System.EventHandler(this.comboBoxServerName_SelectedValueChanged);
            // 
            // labelAccessDB
            // 
            this.labelAccessDB.AutoSize = true;
            this.labelAccessDB.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelAccessDB.Location = new System.Drawing.Point(31, 278);
            this.labelAccessDB.Name = "labelAccessDB";
            this.labelAccessDB.Size = new System.Drawing.Size(95, 16);
            this.labelAccessDB.TabIndex = 7;
            this.labelAccessDB.Text = "Access DB file";
            this.labelAccessDB.Visible = false;
            // 
            // labelAccessDBText
            // 
            this.labelAccessDBText.AutoSize = true;
            this.labelAccessDBText.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelAccessDBText.Location = new System.Drawing.Point(237, 276);
            this.labelAccessDBText.Name = "labelAccessDBText";
            this.labelAccessDBText.Size = new System.Drawing.Size(114, 15);
            this.labelAccessDBText.TabIndex = 8;
            this.labelAccessDBText.Text = "SDSImages.Mdb";
            this.labelAccessDBText.Visible = false;
            // 
            // labelDBName
            // 
            this.labelDBName.AutoSize = true;
            this.labelDBName.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelDBName.Location = new System.Drawing.Point(31, 257);
            this.labelDBName.Name = "labelDBName";
            this.labelDBName.Size = new System.Drawing.Size(144, 16);
            this.labelDBName.TabIndex = 9;
            this.labelDBName.Text = "Image database name";
            this.labelDBName.Visible = false;
            // 
            // labelTableNameText
            // 
            this.labelTableNameText.AutoSize = true;
            this.labelTableNameText.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelTableNameText.Location = new System.Drawing.Point(237, 296);
            this.labelTableNameText.Name = "labelTableNameText";
            this.labelTableNameText.Size = new System.Drawing.Size(152, 15);
            this.labelTableNameText.TabIndex = 12;
            this.labelTableNameText.Text = "DCSDAT_ImagesTable";
            this.labelTableNameText.Visible = false;
            // 
            // labelTableName
            // 
            this.labelTableName.AutoSize = true;
            this.labelTableName.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelTableName.Location = new System.Drawing.Point(31, 298);
            this.labelTableName.Name = "labelTableName";
            this.labelTableName.Size = new System.Drawing.Size(116, 16);
            this.labelTableName.TabIndex = 11;
            this.labelTableName.Text = "Image table name";
            this.labelTableName.Visible = false;
            // 
            // labelDataRootDirectory
            // 
            this.labelDataRootDirectory.AutoSize = true;
            this.labelDataRootDirectory.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelDataRootDirectory.Location = new System.Drawing.Point(292, 40);
            this.labelDataRootDirectory.Name = "labelDataRootDirectory";
            this.labelDataRootDirectory.Size = new System.Drawing.Size(32, 15);
            this.labelDataRootDirectory.TabIndex = 15;
            this.labelDataRootDirectory.Text = "root";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(31, 39);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(118, 16);
            this.label4.TabIndex = 14;
            this.label4.Text = "Data root directory";
            // 
            // labelWait
            // 
            this.labelWait.AutoSize = true;
            this.labelWait.Font = new System.Drawing.Font("Microsoft Sans Serif", 15.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelWait.ForeColor = System.Drawing.Color.Blue;
            this.labelWait.Location = new System.Drawing.Point(29, 334);
            this.labelWait.Name = "labelWait";
            this.labelWait.Size = new System.Drawing.Size(157, 25);
            this.labelWait.TabIndex = 29;
            this.labelWait.Text = "Please Wait ....";
            this.labelWait.Visible = false;
            // 
            // comboBoxDBCatalog
            // 
            this.comboBoxDBCatalog.FormattingEnabled = true;
            this.comboBoxDBCatalog.Location = new System.Drawing.Point(241, 250);
            this.comboBoxDBCatalog.Name = "comboBoxDBCatalog";
            this.comboBoxDBCatalog.Size = new System.Drawing.Size(204, 21);
            this.comboBoxDBCatalog.TabIndex = 30;
            this.comboBoxDBCatalog.Visible = false;
            // 
            // labelUsersDBFields
            // 
            this.labelUsersDBFields.AutoSize = true;
            this.labelUsersDBFields.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelUsersDBFields.Location = new System.Drawing.Point(292, 56);
            this.labelUsersDBFields.Name = "labelUsersDBFields";
            this.labelUsersDBFields.Size = new System.Drawing.Size(39, 15);
            this.labelUsersDBFields.TabIndex = 32;
            this.labelUsersDBFields.Text = "none";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label2.Location = new System.Drawing.Point(31, 55);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(192, 16);
            this.label2.TabIndex = 31;
            this.label2.Text = "Image types in users database";
            // 
            // FilesToOLE
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(624, 442);
            this.Controls.Add(this.labelUsersDBFields);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.comboBoxDBCatalog);
            this.Controls.Add(this.labelWait);
            this.Controls.Add(this.labelDataRootDirectory);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.labelTableNameText);
            this.Controls.Add(this.labelTableName);
            this.Controls.Add(this.labelDBName);
            this.Controls.Add(this.labelAccessDBText);
            this.Controls.Add(this.labelAccessDB);
            this.Controls.Add(this.comboBoxServerName);
            this.Controls.Add(this.labelServerName);
            this.Controls.Add(this.buttonAccept);
            this.Controls.Add(this.buttonCancel);
            this.Controls.Add(this.labelInstructions);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.groupBoxExistingFiles);
            this.Controls.Add(this.groupBoxOleDBType);
            this.Name = "FilesToOLE";
            this.Text = "FilesToOLE";
            this.groupBoxOleDBType.ResumeLayout(false);
            this.groupBoxOleDBType.PerformLayout();
            this.groupBoxExistingFiles.ResumeLayout(false);
            this.groupBoxExistingFiles.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBoxOleDBType;
        private System.Windows.Forms.RadioButton radioButtonOracle;
        private System.Windows.Forms.RadioButton radioButtonSQL;
        private System.Windows.Forms.RadioButton radioButtonAccess;
        private System.Windows.Forms.GroupBox groupBoxExistingFiles;
        private System.Windows.Forms.RadioButton radioButtonCopyFiles;
        private System.Windows.Forms.RadioButton radioButtonMoveFiles;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label labelInstructions;
        private System.Windows.Forms.Button buttonAccept;
        private System.Windows.Forms.Button buttonCancel;
        private System.Windows.Forms.RadioButton radioButtonDontCopy;
        private System.Windows.Forms.Label labelServerName;
        private System.Windows.Forms.ComboBox comboBoxServerName;
        private System.Windows.Forms.Label labelAccessDB;
        private System.Windows.Forms.Label labelAccessDBText;
        private System.Windows.Forms.Label labelDBName;
        private System.Windows.Forms.Label labelTableNameText;
        private System.Windows.Forms.Label labelTableName;
        private System.Windows.Forms.Label labelDataRootDirectory;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label labelWait;
        private System.Windows.Forms.ComboBox comboBoxDBCatalog;
        private System.Windows.Forms.Label labelUsersDBFields;
        private System.Windows.Forms.Label label2;
    }
}