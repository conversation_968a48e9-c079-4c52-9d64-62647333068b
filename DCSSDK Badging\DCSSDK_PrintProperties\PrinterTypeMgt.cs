using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;
using System.Data;

using DCSDEV;

namespace DCSDEV.PrintProperties
{
	/// <summary>
	/// Summary description for BadgingMgtForm.
	/// </summary>
	public class BadgingMgtForm : System.Windows.Forms.Form
	{
		enum ChipIFType { MIFARE_RFID_CHIP_FORMULA, MIFARE_RFID_CHIP_FILE, ICAO_CONTACT_CHIP, MULTIFILE_CONTACT_CHIP, EDL_RFID_CHIP_FILE }; // MULTIFILE_CONTACT_CHIP not implemented

		// values for labelBadgeDataDir.Text
		private const string m_strOverrideLabel = "OVERRIDE Badge Data Directory";
		private const string m_strDefaultLabel = "Default Badge Data Directory";

		private static int m_indexCurrent = 0;
		private int m_iUnits = 0;

        private DCSDEV.PrintProperties.PrinterTypeArray m_printertypeArray;  
		private DCSDEV.ParameterStore m_ps;
		private bool m_bUseBadgeDataDirOverride = false;
		private string m_strBadgeDataOverride = "";

		private System.Windows.Forms.Button buttonCancel;
        private System.Windows.Forms.Button buttonAccept;
		private System.Windows.Forms.Button buttonValidate;
		private System.Windows.Forms.TabControl tabControl1;
		private System.Windows.Forms.TabPage tabPagePrinterTypes;
		private System.Windows.Forms.TabPage tabPagePathsAndOptions;
		private System.Windows.Forms.TextBox tbScrollInfo;
        private System.Windows.Forms.HScrollBar hScrollBar1;
		private System.Windows.Forms.Button buttonRemove;
		private System.Windows.Forms.Button buttonAddPrinterType;
		private System.Windows.Forms.CheckBox checkANSIStyle;
		private System.Windows.Forms.CheckBox checkUseSheetPrinter;
		private System.Windows.Forms.Button buttonSelectBadgeDataDirOverride;
		private System.Windows.Forms.TextBox tbBadgeDataDir;
		private System.Windows.Forms.TextBox tbDataRootDir;
		private System.Windows.Forms.TextBox tbQueueDir;
		private System.Windows.Forms.Button buttonSelectDataRootDir;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.Label labelBadgeDataDir;
		private System.Windows.Forms.Button buttonSelectQueueDir;
		private System.Windows.Forms.Label label6;
		private System.Windows.Forms.Label label13;
		private System.Windows.Forms.ComboBox cbAppDateFormat;
		private PrinterTypeInstanceControl printerTypeInstanceControl;
		private TabPage tabPageChipEncoding;
		private Label label2;
		private ComboBox comboBoxChipIFType;
		private ComboBox comboBoxChipEncoderPort;
		private Label label3;
		private ComboBox comboBoxPrinterChipEncoderPort;
		private Label label23;
		private CheckBox checkBoxLockAfterWrite;
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		public BadgingMgtForm()
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//

			this.comboBoxChipIFType.Items.Clear();
			this.comboBoxChipIFType.Items.Add(ChipIFType.MIFARE_RFID_CHIP_FORMULA.ToString());
			this.comboBoxChipIFType.Items.Add(ChipIFType.MIFARE_RFID_CHIP_FILE.ToString());
			this.comboBoxChipIFType.Items.Add(ChipIFType.ICAO_CONTACT_CHIP.ToString());
			this.comboBoxChipIFType.Items.Add(ChipIFType.MULTIFILE_CONTACT_CHIP.ToString());
			this.comboBoxChipIFType.Items.Add(ChipIFType.EDL_RFID_CHIP_FILE.ToString());

			m_ps = new ParameterStore("DCSSDK_Mgt");
			
			// get Badge Data Directory
			string strDataRootDir;
			strDataRootDir = m_ps.GetStringParameter("DataRootDir", m_ps.m_strDCSInstallDirectory);
			// with no network permissions, data root dir must be equal to or under install directory
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.NetworkPaths))
			{
				if (!strDataRootDir.ToUpper().StartsWith(m_ps.m_strDCSInstallDirectory.ToUpper()))
				{
					strDataRootDir = m_ps.m_strDCSInstallDirectory;
				}
			}
			this.tbDataRootDir.Text = strDataRootDir;

			// Normal configurations have badge data in "BadgeData" under the data root directory
			this.tbBadgeDataDir.Text = System.IO.Path.Combine(strDataRootDir, "BadgeData");
			this.labelBadgeDataDir.Text = m_strDefaultLabel;

			// Override allows badge data to be in a place separate from data root and the Portraits etc below it.
			m_bUseBadgeDataDirOverride = m_ps.GetBoolParameter("UseBadgeDataDirOverride", false);
			if (m_bUseBadgeDataDirOverride)
			{
				if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.NetworkPaths)) 
				{
					m_strBadgeDataOverride = m_ps.GetStringParameter("BadgeDataDirOverride", "");
					if (m_strBadgeDataOverride != "")
					{
						this.tbBadgeDataDir.Text = m_strBadgeDataOverride;
					}
					else
					{
						m_strBadgeDataOverride = this.tbBadgeDataDir.Text = this.tbDataRootDir.Text;
					}
				}
				else
				{
					// override in non-network case only allows dir to be the install directory
					this.tbBadgeDataDir.Text = this.tbDataRootDir.Text;
				}
				this.labelBadgeDataDir.Text = m_strOverrideLabel;
			}
			else this.labelBadgeDataDir.Text = m_strDefaultLabel;

			this.tbQueueDir.Text = m_ps.GetStringParameter("PrinterQueueDir", System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, "Queue"));
			this.cbAppDateFormat.Text = m_ps.GetStringParameter("AppDateFormat", "default");

            if (DCSDEV.DCSLicensing.IsLicensedOK(LicensedFeatures.EncodeChips))
            {
                //this.comboBoxChipIFType.Text = m_ps.GetStringParameter("ChipIFType", "MIFARE_RFID_CHIP_FORMULA");
                ChipIFType eChipIFType = (ChipIFType)m_ps.GetIntParameter("ChipIFType", 0);
                this.comboBoxChipIFType.SelectedIndex = (int)eChipIFType;
                this.comboBoxChipEncoderPort.Text = m_ps.GetStringParameter("ChipEncoderPort", "COM4");		// "DCSSDK_Mgt"
                this.comboBoxPrinterChipEncoderPort.Text = m_ps.GetStringParameter("PrinterChipEncoderPort", "COM5");		// "DCSSDK_Mgt"
                this.checkBoxLockAfterWrite.Checked = m_ps.GetBoolParameter("LockChipAfterWrite", false);
            }
            else
            {
                ((TabPage)(this.tabControl1.Controls[2])).Enabled = false;
            }

			bool bUseSheetPrinter = m_ps.GetBoolParameter("UseSheetPrinter", false);
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.SheetPrinting)) bUseSheetPrinter = false;
			this.checkUseSheetPrinter.Checked = bUseSheetPrinter;
            if (bUseSheetPrinter) m_indexCurrent = DCSDEV.PrintProperties.PrinterTypeArray.NUMBASECLASSES - 1;

			this.checkANSIStyle.Checked = m_ps.GetBoolParameter("ANSIStyle", true);
			m_iUnits = m_ps.GetIntParameter("DisplayUnits", 0);

			// get badge class data
            m_printertypeArray = new DCSDEV.PrintProperties.PrinterTypeArray();  
			m_printertypeArray.LoadPrinterTypeArray();
			if (m_indexCurrent >= m_printertypeArray.Count)
				m_indexCurrent = 0;

			// set scroller
			this.SetScroller();

            DCSDEV.PrintProperties.PrinterTypeDatum bcDatum = (DCSDEV.PrintProperties.PrinterTypeDatum)m_printertypeArray[m_indexCurrent];
			this.printerTypeInstanceControl.Units = m_iUnits;
			this.printerTypeInstanceControl.SetPrinterTypeDatum(bcDatum, m_indexCurrent);
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if (components != null) 
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		/*
			System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(BadgingMgtForm));
			this.printerTypeInstanceControl = new PrinterTypeInstanceControl();
		 */
		private void InitializeComponent()
		{
			System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(BadgingMgtForm));
			this.printerTypeInstanceControl = new PrinterTypeInstanceControl();
			this.buttonCancel = new System.Windows.Forms.Button();
			this.buttonAccept = new System.Windows.Forms.Button();
			this.buttonValidate = new System.Windows.Forms.Button();
			this.tabControl1 = new System.Windows.Forms.TabControl();
			this.tabPagePrinterTypes = new System.Windows.Forms.TabPage();
			this.tbScrollInfo = new System.Windows.Forms.TextBox();
			this.hScrollBar1 = new System.Windows.Forms.HScrollBar();
			this.buttonRemove = new System.Windows.Forms.Button();
			this.buttonAddPrinterType = new System.Windows.Forms.Button();
			this.tabPagePathsAndOptions = new System.Windows.Forms.TabPage();
			this.label13 = new System.Windows.Forms.Label();
			this.cbAppDateFormat = new System.Windows.Forms.ComboBox();
			this.buttonSelectBadgeDataDirOverride = new System.Windows.Forms.Button();
			this.tbBadgeDataDir = new System.Windows.Forms.TextBox();
			this.tbDataRootDir = new System.Windows.Forms.TextBox();
			this.tbQueueDir = new System.Windows.Forms.TextBox();
			this.buttonSelectDataRootDir = new System.Windows.Forms.Button();
			this.label1 = new System.Windows.Forms.Label();
			this.labelBadgeDataDir = new System.Windows.Forms.Label();
			this.buttonSelectQueueDir = new System.Windows.Forms.Button();
			this.label6 = new System.Windows.Forms.Label();
			this.checkANSIStyle = new System.Windows.Forms.CheckBox();
			this.tabPageChipEncoding = new System.Windows.Forms.TabPage();
			this.comboBoxChipEncoderPort = new System.Windows.Forms.ComboBox();
			this.label3 = new System.Windows.Forms.Label();
			this.comboBoxPrinterChipEncoderPort = new System.Windows.Forms.ComboBox();
			this.label23 = new System.Windows.Forms.Label();
			this.label2 = new System.Windows.Forms.Label();
			this.comboBoxChipIFType = new System.Windows.Forms.ComboBox();
			this.checkUseSheetPrinter = new System.Windows.Forms.CheckBox();
			this.checkBoxLockAfterWrite = new System.Windows.Forms.CheckBox();
			this.tabControl1.SuspendLayout();
			this.tabPagePrinterTypes.SuspendLayout();
			this.tabPagePathsAndOptions.SuspendLayout();
			this.tabPageChipEncoding.SuspendLayout();
			this.SuspendLayout();
			// 
			// printerTypeInstanceControl
			// 
			this.printerTypeInstanceControl.BackColor = System.Drawing.SystemColors.Control;
			this.printerTypeInstanceControl.Location = new System.Drawing.Point(0, 0);
			this.printerTypeInstanceControl.Name = "printerTypeInstanceControl";
			this.printerTypeInstanceControl.Size = new System.Drawing.Size(608, 272);
			this.printerTypeInstanceControl.TabIndex = 16;
			// 
			// buttonCancel
			// 
			this.buttonCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.buttonCancel.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonCancel.Location = new System.Drawing.Point(424, 416);
			this.buttonCancel.Name = "buttonCancel";
			this.buttonCancel.Size = new System.Drawing.Size(96, 24);
			this.buttonCancel.TabIndex = 13;
			this.buttonCancel.Text = "Cancel";
			this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
			// 
			// buttonAccept
			// 
			this.buttonAccept.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.buttonAccept.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonAccept.Location = new System.Drawing.Point(528, 416);
			this.buttonAccept.Name = "buttonAccept";
			this.buttonAccept.Size = new System.Drawing.Size(96, 24);
			this.buttonAccept.TabIndex = 14;
			this.buttonAccept.Text = "&OK";
			this.buttonAccept.Click += new System.EventHandler(this.buttonAccept_Click);
			// 
			// buttonValidate
			// 
			this.buttonValidate.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.buttonValidate.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonValidate.ImeMode = System.Windows.Forms.ImeMode.NoControl;
			this.buttonValidate.Location = new System.Drawing.Point(320, 416);
			this.buttonValidate.Name = "buttonValidate";
			this.buttonValidate.Size = new System.Drawing.Size(96, 24);
			this.buttonValidate.TabIndex = 12;
			this.buttonValidate.Text = "C&heck";
			this.buttonValidate.Click += new System.EventHandler(this.buttonValidate_Click);
			// 
			// tabControl1
			// 
			this.tabControl1.Controls.Add(this.tabPagePrinterTypes);
			this.tabControl1.Controls.Add(this.tabPagePathsAndOptions);
			this.tabControl1.Controls.Add(this.tabPageChipEncoding);
			this.tabControl1.Location = new System.Drawing.Point(8, 80);
			this.tabControl1.Name = "tabControl1";
			this.tabControl1.SelectedIndex = 0;
			this.tabControl1.Size = new System.Drawing.Size(616, 320);
			this.tabControl1.TabIndex = 38;
			// 
			// tabPagePrinterTypes
			// 
			this.tabPagePrinterTypes.Controls.Add(this.tbScrollInfo);
			this.tabPagePrinterTypes.Controls.Add(this.hScrollBar1);
			this.tabPagePrinterTypes.Controls.Add(this.buttonRemove);
			this.tabPagePrinterTypes.Controls.Add(this.buttonAddPrinterType);
			this.tabPagePrinterTypes.Controls.Add(this.printerTypeInstanceControl);
			this.tabPagePrinterTypes.Location = new System.Drawing.Point(4, 22);
			this.tabPagePrinterTypes.Name = "tabPagePrinterTypes";
			this.tabPagePrinterTypes.Size = new System.Drawing.Size(608, 294);
			this.tabPagePrinterTypes.TabIndex = 0;
			this.tabPagePrinterTypes.Text = "Printer Types";
			this.tabPagePrinterTypes.UseVisualStyleBackColor = true;
			// 
			// tbScrollInfo
			// 
			this.tbScrollInfo.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.tbScrollInfo.BorderStyle = System.Windows.Forms.BorderStyle.None;
			this.tbScrollInfo.Location = new System.Drawing.Point(400, 278);
			this.tbScrollInfo.Name = "tbScrollInfo";
			this.tbScrollInfo.ReadOnly = true;
			this.tbScrollInfo.Size = new System.Drawing.Size(40, 13);
			this.tbScrollInfo.TabIndex = 15;
			this.tbScrollInfo.TabStop = false;
			this.tbScrollInfo.Text = "1 of 3";
			// 
			// hScrollBar1
			// 
			this.hScrollBar1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.hScrollBar1.LargeChange = 1;
			this.hScrollBar1.Location = new System.Drawing.Point(0, 270);
			this.hScrollBar1.Name = "hScrollBar1";
			this.hScrollBar1.Size = new System.Drawing.Size(336, 24);
			this.hScrollBar1.TabIndex = 12;
			this.hScrollBar1.TabStop = true;
			this.hScrollBar1.ValueChanged += new System.EventHandler(this.hScrollBar1_ValueChanged);
			// 
			// buttonRemove
			// 
			this.buttonRemove.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.buttonRemove.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonRemove.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.buttonRemove.ForeColor = System.Drawing.Color.Red;
			this.buttonRemove.Location = new System.Drawing.Point(368, 270);
			this.buttonRemove.Name = "buttonRemove";
			this.buttonRemove.Size = new System.Drawing.Size(24, 24);
			this.buttonRemove.TabIndex = 14;
			this.buttonRemove.Text = "X";
			this.buttonRemove.Click += new System.EventHandler(this.buttonRemove_Click);
			// 
			// buttonAddPrinterType
			// 
			this.buttonAddPrinterType.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.buttonAddPrinterType.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonAddPrinterType.Font = new System.Drawing.Font("Arial", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.buttonAddPrinterType.Location = new System.Drawing.Point(344, 270);
			this.buttonAddPrinterType.Name = "buttonAddPrinterType";
			this.buttonAddPrinterType.Size = new System.Drawing.Size(24, 24);
			this.buttonAddPrinterType.TabIndex = 13;
			this.buttonAddPrinterType.Text = "+";
			this.buttonAddPrinterType.Click += new System.EventHandler(this.buttonAddPrinterType_Click);
			// 
			// tabPagePathsAndOptions
			// 
			this.tabPagePathsAndOptions.Controls.Add(this.label13);
			this.tabPagePathsAndOptions.Controls.Add(this.cbAppDateFormat);
			this.tabPagePathsAndOptions.Controls.Add(this.buttonSelectBadgeDataDirOverride);
			this.tabPagePathsAndOptions.Controls.Add(this.tbBadgeDataDir);
			this.tabPagePathsAndOptions.Controls.Add(this.tbDataRootDir);
			this.tabPagePathsAndOptions.Controls.Add(this.tbQueueDir);
			this.tabPagePathsAndOptions.Controls.Add(this.buttonSelectDataRootDir);
			this.tabPagePathsAndOptions.Controls.Add(this.label1);
			this.tabPagePathsAndOptions.Controls.Add(this.labelBadgeDataDir);
			this.tabPagePathsAndOptions.Controls.Add(this.buttonSelectQueueDir);
			this.tabPagePathsAndOptions.Controls.Add(this.label6);
			this.tabPagePathsAndOptions.Controls.Add(this.checkANSIStyle);
			this.tabPagePathsAndOptions.Location = new System.Drawing.Point(4, 22);
			this.tabPagePathsAndOptions.Name = "tabPagePathsAndOptions";
			this.tabPagePathsAndOptions.Size = new System.Drawing.Size(608, 294);
			this.tabPagePathsAndOptions.TabIndex = 1;
			this.tabPagePathsAndOptions.Text = "Paths and Options";
			this.tabPagePathsAndOptions.UseVisualStyleBackColor = true;
			// 
			// label13
			// 
			this.label13.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.label13.ForeColor = System.Drawing.Color.Black;
			this.label13.Location = new System.Drawing.Point(32, 160);
			this.label13.Name = "label13";
			this.label13.Size = new System.Drawing.Size(212, 16);
			this.label13.TabIndex = 78;
			this.label13.Text = "Date format passed from the application";
			this.label13.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// cbAppDateFormat
			// 
			this.cbAppDateFormat.ForeColor = System.Drawing.Color.Black;
			this.cbAppDateFormat.Items.AddRange(new object[] {
            "default",
            "MM/dd/yy",
            "MM/dd/yyyy",
            "dd/MM/yy",
            "dd/MM/yyyy",
            "dd-MM-yy",
            "dd-MM-yyyy",
            "dd.MM.yy",
            "dd.MM.yyyy",
            "yy-MM-dd",
            "yyyy-MM-dd"});
			this.cbAppDateFormat.Location = new System.Drawing.Point(32, 179);
			this.cbAppDateFormat.Name = "cbAppDateFormat";
			this.cbAppDateFormat.Size = new System.Drawing.Size(120, 21);
			this.cbAppDateFormat.TabIndex = 7;
			this.cbAppDateFormat.Text = "default";
			// 
			// buttonSelectBadgeDataDirOverride
			// 
			this.buttonSelectBadgeDataDirOverride.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonSelectBadgeDataDirOverride.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.buttonSelectBadgeDataDirOverride.Location = new System.Drawing.Point(408, 77);
			this.buttonSelectBadgeDataDirOverride.Name = "buttonSelectBadgeDataDirOverride";
			this.buttonSelectBadgeDataDirOverride.Size = new System.Drawing.Size(72, 20);
			this.buttonSelectBadgeDataDirOverride.TabIndex = 4;
			this.buttonSelectBadgeDataDirOverride.Text = "Override >";
			this.buttonSelectBadgeDataDirOverride.Click += new System.EventHandler(this.buttonSelectBadgeDataDirOverride_Click);
			// 
			// tbBadgeDataDir
			// 
			this.tbBadgeDataDir.BorderStyle = System.Windows.Forms.BorderStyle.None;
			this.tbBadgeDataDir.Location = new System.Drawing.Point(32, 77);
			this.tbBadgeDataDir.Name = "tbBadgeDataDir";
			this.tbBadgeDataDir.ReadOnly = true;
			this.tbBadgeDataDir.Size = new System.Drawing.Size(368, 13);
			this.tbBadgeDataDir.TabIndex = 3;
			this.tbBadgeDataDir.TabStop = false;
			this.tbBadgeDataDir.Text = "path";
			// 
			// tbDataRootDir
			// 
			this.tbDataRootDir.Location = new System.Drawing.Point(32, 29);
			this.tbDataRootDir.Name = "tbDataRootDir";
			this.tbDataRootDir.ReadOnly = true;
			this.tbDataRootDir.Size = new System.Drawing.Size(416, 20);
			this.tbDataRootDir.TabIndex = 0;
			this.tbDataRootDir.TabStop = false;
			// 
			// tbQueueDir
			// 
			this.tbQueueDir.Location = new System.Drawing.Point(32, 125);
			this.tbQueueDir.Name = "tbQueueDir";
			this.tbQueueDir.ReadOnly = true;
			this.tbQueueDir.Size = new System.Drawing.Size(416, 20);
			this.tbQueueDir.TabIndex = 5;
			this.tbQueueDir.TabStop = false;
			// 
			// buttonSelectDataRootDir
			// 
			this.buttonSelectDataRootDir.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonSelectDataRootDir.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.buttonSelectDataRootDir.Location = new System.Drawing.Point(460, 29);
			this.buttonSelectDataRootDir.Name = "buttonSelectDataRootDir";
			this.buttonSelectDataRootDir.Size = new System.Drawing.Size(20, 20);
			this.buttonSelectDataRootDir.TabIndex = 1;
			this.buttonSelectDataRootDir.Text = ">";
			this.buttonSelectDataRootDir.Click += new System.EventHandler(this.buttonSelectDataRootDir_Click);
			// 
			// label1
			// 
			this.label1.Location = new System.Drawing.Point(32, 13);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(120, 16);
			this.label1.TabIndex = 48;
			this.label1.Text = "Data Root Directory";
			this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// labelBadgeDataDir
			// 
			this.labelBadgeDataDir.Location = new System.Drawing.Point(32, 61);
			this.labelBadgeDataDir.Name = "labelBadgeDataDir";
			this.labelBadgeDataDir.Size = new System.Drawing.Size(328, 16);
			this.labelBadgeDataDir.TabIndex = 2;
			this.labelBadgeDataDir.Text = "Badge Data Dir";
			this.labelBadgeDataDir.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// buttonSelectQueueDir
			// 
			this.buttonSelectQueueDir.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonSelectQueueDir.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.buttonSelectQueueDir.Location = new System.Drawing.Point(460, 125);
			this.buttonSelectQueueDir.Name = "buttonSelectQueueDir";
			this.buttonSelectQueueDir.Size = new System.Drawing.Size(20, 20);
			this.buttonSelectQueueDir.TabIndex = 6;
			this.buttonSelectQueueDir.Text = ">";
			this.buttonSelectQueueDir.Click += new System.EventHandler(this.buttonSelectQueueDir_Click);
			// 
			// label6
			// 
			this.label6.Location = new System.Drawing.Point(32, 109);
			this.label6.Name = "label6";
			this.label6.Size = new System.Drawing.Size(120, 16);
			this.label6.TabIndex = 46;
			this.label6.Text = "Queue Directory";
			this.label6.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// checkANSIStyle
			// 
			this.checkANSIStyle.Checked = true;
			this.checkANSIStyle.CheckState = System.Windows.Forms.CheckState.Checked;
			this.checkANSIStyle.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.checkANSIStyle.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.checkANSIStyle.Location = new System.Drawing.Point(32, 221);
			this.checkANSIStyle.Name = "checkANSIStyle";
			this.checkANSIStyle.Size = new System.Drawing.Size(296, 16);
			this.checkANSIStyle.TabIndex = 9;
			this.checkANSIStyle.Text = "ANSI encoding in badge data files (old style)";
			// 
			// tabPageChipEncoding
			// 
			this.tabPageChipEncoding.Controls.Add(this.checkBoxLockAfterWrite);
			this.tabPageChipEncoding.Controls.Add(this.comboBoxChipEncoderPort);
			this.tabPageChipEncoding.Controls.Add(this.label3);
			this.tabPageChipEncoding.Controls.Add(this.comboBoxPrinterChipEncoderPort);
			this.tabPageChipEncoding.Controls.Add(this.label23);
			this.tabPageChipEncoding.Controls.Add(this.label2);
			this.tabPageChipEncoding.Controls.Add(this.comboBoxChipIFType);
			this.tabPageChipEncoding.Location = new System.Drawing.Point(4, 22);
			this.tabPageChipEncoding.Name = "tabPageChipEncoding";
			this.tabPageChipEncoding.Size = new System.Drawing.Size(608, 294);
			this.tabPageChipEncoding.TabIndex = 2;
			this.tabPageChipEncoding.Text = "Chip encoding";
			this.tabPageChipEncoding.UseVisualStyleBackColor = true;
			// 
			// comboBoxChipEncoderPort
			// 
			this.comboBoxChipEncoderPort.Items.AddRange(new object[] {
            "COM1",
            "COM2",
            "COM3",
            "COM4",
            "COM5",
            "COM6",
            "COM7",
            "COM8",
            "COM9",
            "COM10",
            "COM11",
            "COM12",
            "COM13",
            "COM14",
            "COM15"});
			this.comboBoxChipEncoderPort.Location = new System.Drawing.Point(98, 215);
			this.comboBoxChipEncoderPort.Name = "comboBoxChipEncoderPort";
			this.comboBoxChipEncoderPort.Size = new System.Drawing.Size(80, 21);
			this.comboBoxChipEncoderPort.TabIndex = 86;
			this.comboBoxChipEncoderPort.Text = "COM5";
			// 
			// label3
			// 
			this.label3.BackColor = System.Drawing.Color.Transparent;
			this.label3.ImeMode = System.Windows.Forms.ImeMode.NoControl;
			this.label3.Location = new System.Drawing.Point(98, 191);
			this.label3.Name = "label3";
			this.label3.Size = new System.Drawing.Size(295, 24);
			this.label3.TabIndex = 85;
			this.label3.Text = "Stand-alone Chip Encoder/Scanner Port";
			// 
			// comboBoxPrinterChipEncoderPort
			// 
			this.comboBoxPrinterChipEncoderPort.Items.AddRange(new object[] {
            "COM1",
            "COM2",
            "COM3",
            "COM4",
            "COM5",
            "COM6",
            "COM7",
            "COM8",
            "COM9",
            "COM10",
            "COM11",
            "COM12",
            "COM13",
            "COM14",
            "COM15"});
			this.comboBoxPrinterChipEncoderPort.Location = new System.Drawing.Point(98, 139);
			this.comboBoxPrinterChipEncoderPort.Name = "comboBoxPrinterChipEncoderPort";
			this.comboBoxPrinterChipEncoderPort.Size = new System.Drawing.Size(80, 21);
			this.comboBoxPrinterChipEncoderPort.TabIndex = 84;
			this.comboBoxPrinterChipEncoderPort.Text = "COM4";
			// 
			// label23
			// 
			this.label23.BackColor = System.Drawing.Color.Transparent;
			this.label23.ImeMode = System.Windows.Forms.ImeMode.NoControl;
			this.label23.Location = new System.Drawing.Point(98, 115);
			this.label23.Name = "label23";
			this.label23.Size = new System.Drawing.Size(244, 24);
			this.label23.TabIndex = 83;
			this.label23.Text = "Printer Chip Encoder/Scanner Port";
			// 
			// label2
			// 
			this.label2.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.label2.ForeColor = System.Drawing.Color.Black;
			this.label2.Location = new System.Drawing.Point(98, 42);
			this.label2.Name = "label2";
			this.label2.Size = new System.Drawing.Size(125, 24);
			this.label2.TabIndex = 82;
			this.label2.Text = "Chip / interface type";
			this.label2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			// 
			// comboBoxChipIFType
			// 
			this.comboBoxChipIFType.ForeColor = System.Drawing.Color.Black;
			this.comboBoxChipIFType.Items.AddRange(new object[] {
            "Mifare RFID Chip",
            "ICAO Contact Chip"});
			this.comboBoxChipIFType.Location = new System.Drawing.Point(98, 69);
			this.comboBoxChipIFType.Name = "comboBoxChipIFType";
			this.comboBoxChipIFType.Size = new System.Drawing.Size(218, 21);
			this.comboBoxChipIFType.TabIndex = 81;
			this.comboBoxChipIFType.Text = "MIFARE_RFID_CHIP_FORMULA";
			// 
			// checkUseSheetPrinter
			// 
			this.checkUseSheetPrinter.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.checkUseSheetPrinter.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.checkUseSheetPrinter.Location = new System.Drawing.Point(344, 36);
			this.checkUseSheetPrinter.Name = "checkUseSheetPrinter";
			this.checkUseSheetPrinter.Size = new System.Drawing.Size(280, 16);
			this.checkUseSheetPrinter.TabIndex = 38;
			this.checkUseSheetPrinter.Text = "Print everything using multi-badge sheet printer type.";
			this.checkUseSheetPrinter.Click += new System.EventHandler(this.checkUseSheetPrinter_Click);
			// 
			// checkBoxLockAfterWrite
			// 
			this.checkBoxLockAfterWrite.AutoSize = true;
			this.checkBoxLockAfterWrite.Checked = true;
			this.checkBoxLockAfterWrite.CheckState = System.Windows.Forms.CheckState.Checked;
			this.checkBoxLockAfterWrite.Location = new System.Drawing.Point(373, 69);
			this.checkBoxLockAfterWrite.Name = "checkBoxLockAfterWrite";
			this.checkBoxLockAfterWrite.Size = new System.Drawing.Size(135, 17);
			this.checkBoxLockAfterWrite.TabIndex = 87;
			this.checkBoxLockAfterWrite.Text = "Lock Chip After Writing";
			this.checkBoxLockAfterWrite.UseVisualStyleBackColor = true;
			// 
			// BadgingMgtForm
			// 
			this.AcceptButton = this.buttonAccept;
			this.AutoScaleBaseSize = new System.Drawing.Size(5, 13);
			this.CancelButton = this.buttonCancel;
			this.ClientSize = new System.Drawing.Size(634, 448);
			this.Controls.Add(this.tabControl1);
			this.Controls.Add(this.buttonValidate);
			this.Controls.Add(this.buttonCancel);
			this.Controls.Add(this.buttonAccept);
			this.Controls.Add(this.checkUseSheetPrinter);
			this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
			this.MaximizeBox = false;
			this.MinimizeBox = false;
			this.Name = "BadgingMgtForm";
			this.ShowInTaskbar = false;
			this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
			this.Text = "Document Print Properties";
			this.tabControl1.ResumeLayout(false);
			this.tabPagePrinterTypes.ResumeLayout(false);
			this.tabPagePrinterTypes.PerformLayout();
			this.tabPagePathsAndOptions.ResumeLayout(false);
			this.tabPagePathsAndOptions.PerformLayout();
			this.tabPageChipEncoding.ResumeLayout(false);
			this.tabPageChipEncoding.PerformLayout();
			this.ResumeLayout(false);

		}
		#endregion

		/******************************************************************
		/// <summary>
		/// The main entry point for the application.
		/// </summary>
		[STAThread]
		static void Main() 
		{
			Application.Run(new BadgingMgtForm());
		}
		******************************************************************/

		private void SetScroller()
		{
			this.hScrollBar1.Maximum = m_printertypeArray.Count - 1;
			this.hScrollBar1.Value = m_indexCurrent;
			this.tbScrollInfo.Text = (m_indexCurrent+1).ToString() + " of " + (m_printertypeArray.Count).ToString();
            this.buttonRemove.Enabled = (m_indexCurrent >= DCSDEV.PrintProperties.PrinterTypeArray.NUMBASECLASSES);
		}

		private bool ValidateQueryAndFix(bool bQuiet, bool bCheckOnly)
		{
			bool bRet;
			bRet = DCSDEV.DCSServerStuff.ValidateDirectoryPath("Data root", this.tbDataRootDir.Text, bQuiet, bCheckOnly);
			if (!bRet) return false;
			bRet = DCSDEV.DCSServerStuff.ValidateDirectoryPath("Badge data", System.IO.Path.Combine(this.tbDataRootDir.Text, "BadgeData"), bQuiet, bCheckOnly);
			if (!bRet) return false;
			bRet = DCSDEV.DCSServerStuff.ValidateDirectoryPath("Print queue", this.tbQueueDir.Text, bQuiet, bCheckOnly);
			if (!bRet) return false;
			if (!bQuiet && this.checkUseSheetPrinter.Checked)
			{
				DCSDEV.PrintProperties.PrinterTypeDatum bcDatum;
				bcDatum = (DCSDEV.PrintProperties.PrinterTypeDatum)m_printertypeArray[m_indexCurrent];
				this.printerTypeInstanceControl.GetPrinterTypeDatum(ref bcDatum);
				if ((bcDatum.m_bPrinterHasChipEncoder && (bcDatum.m_bScanChipIDWhenPrinting || bcDatum.m_bEncodeChipWhenPrinting))
					|| (bcDatum.m_IfMagStripe || bcDatum.m_IfKpanel))
				{
					DCSMsg.Show(String.Format("Warning! Printer type '{0}' has settings which may conflict with '{1}'", bcDatum.m_PrinterTypeName, this.checkUseSheetPrinter.Text));
					return true;
				}
			}
			return true;
		}

		private void buttonAddPrinterType_Click(object sender, System.EventArgs e)
		{
            DCSDEV.PrintProperties.PrinterTypeDatum bcDatum;

			// find unused badge class name
			bool bFound;
			int iUnused;
			for (iUnused=1; ; iUnused++)
			{
				bFound = false;
				for (int i=0; i<m_printertypeArray.Count; i++)
				{
					if (((PrinterTypeDatum)m_printertypeArray[i]).m_PrinterTypeName == "PrinterType" + iUnused.ToString())
					{
						bFound = true;
						break;
					}
				}
				if (!bFound) break;
			}

            bcDatum = new DCSDEV.PrintProperties.PrinterTypeDatum("PrinterType" + iUnused.ToString());
			m_printertypeArray.Add(bcDatum);
			int idx = m_printertypeArray.Count - 1;

			m_indexCurrent = idx;
			this.SetScroller();

			this.printerTypeInstanceControl.SetPrinterTypeDatum(bcDatum, idx);
		}

		private void buttonRemove_Click(object sender, System.EventArgs e)
		{
			int idx = this.hScrollBar1.Value;
            if (idx < DCSDEV.PrintProperties.PrinterTypeArray.NUMBASECLASSES) return;

			this.m_printertypeArray.RemoveAt(idx);
			m_indexCurrent--;

			this.SetScroller();

			// put data for new class into data display.
            DCSDEV.PrintProperties.PrinterTypeDatum bcDatum = (DCSDEV.PrintProperties.PrinterTypeDatum)m_printertypeArray[m_indexCurrent];
			this.printerTypeInstanceControl.SetPrinterTypeDatum(bcDatum, m_indexCurrent);

		}

		private void buttonAccept_Click(object sender, System.EventArgs e)
		{
			bool bRet = ValidateQueryAndFix(false, false);	// bool bQuiet, bool bCheckOnly
			if (!bRet) return;

			m_ps.WriteStringParameter("DataRootDir", this.tbDataRootDir.Text);
			m_ps.WriteBoolParameter("UseBadgeDataDirOverride", m_bUseBadgeDataDirOverride);
			m_ps.WriteStringParameter("BadgeDataDirOverride", m_strBadgeDataOverride);

			m_ps.WriteStringParameter("PrinterQueueDir", this.tbQueueDir.Text);
			m_ps.WriteBoolParameter("UseSheetPrinter", this.checkUseSheetPrinter.Checked);
			m_ps.WriteBoolParameter("ANSIStyle", this.checkANSIStyle.Checked);
			m_ps.WriteIntParameter("DisplayUnits", this.printerTypeInstanceControl.Units);
			
			m_ps.WriteStringParameter("AppDateFormat", this.cbAppDateFormat.Text);
			// m_ps.WriteStringParameter("ChipIFType", this.comboBoxChipIFType.Text);
			m_ps.WriteIntParameter("ChipIFType", this.comboBoxChipIFType.SelectedIndex);
			m_ps.WriteStringParameter("ChipEncoderPort", this.comboBoxChipEncoderPort.Text);
			m_ps.WriteStringParameter("PrinterChipEncoderPort", this.comboBoxPrinterChipEncoderPort.Text);
			m_ps.WriteBoolParameter("LockChipAfterWrite", this.checkBoxLockAfterWrite.Checked);

            DCSDEV.PrintProperties.PrinterTypeDatum bcDatum;
            bcDatum = (DCSDEV.PrintProperties.PrinterTypeDatum)m_printertypeArray[m_indexCurrent];
			this.printerTypeInstanceControl.GetPrinterTypeDatum(ref bcDatum);
			m_printertypeArray.SavePrinterTypeArray();

			DCSDEV.DCSDesignDataAccess.Reinit();
			Close();
		}

		private void buttonCancel_Click(object sender, System.EventArgs e)
		{
			bool bRet = ValidateQueryAndFix(true, true);	// bool bQuiet, bool bCheckOnly
			if (!bRet)
			{
				DCSMsg.Show(
					"Some of the original configuration parameters are invalid." + Environment.NewLine +
					"You will need to return to this dialog to fix these problems.", System.Windows.Forms.MessageBoxIcon.Error);
			}
			Close();
		}

		private void buttonSelectQueueDir_Click(object sender, System.EventArgs e)
		{
			System.Windows.Forms.FolderBrowserDialog folderDialog;
			folderDialog = new System.Windows.Forms.FolderBrowserDialog();
			folderDialog.Description = "Select Queue Directory";

			//folderDialog.RootFolder = Environment.SpecialFolder.Personal; = My Documents
			folderDialog.SelectedPath = m_ps.m_strDCSInstallDirectory;  // initial dir

			DialogResult result = folderDialog.ShowDialog(this);
			if (result != DialogResult.Cancel)
				this.tbQueueDir.Text = folderDialog.SelectedPath;
		}

		private void hScrollBar1_ValueChanged(object sender, System.EventArgs e)
		{
            DCSDEV.PrintProperties.PrinterTypeDatum bcDatum;
			int idx = this.hScrollBar1.Value;
			if (idx < 0) return;

			// update the list box display
            bcDatum = (DCSDEV.PrintProperties.PrinterTypeDatum)m_printertypeArray[m_indexCurrent];

			// save current values of old badge class data display into the data list
			if (idx != m_indexCurrent)
			{
                bcDatum = (DCSDEV.PrintProperties.PrinterTypeDatum)m_printertypeArray[m_indexCurrent];
				this.printerTypeInstanceControl.GetPrinterTypeDatum(ref bcDatum);
				m_indexCurrent = idx;
				this.SetScroller();
			}
			// put data for new class into data display.
            bcDatum = (DCSDEV.PrintProperties.PrinterTypeDatum)m_printertypeArray[idx];
			this.printerTypeInstanceControl.SetPrinterTypeDatum(bcDatum, idx);
		}

		private void checkUseSheetPrinter_Click(object sender, System.EventArgs e)
		{
			if (this.checkUseSheetPrinter.Checked)
			{
				if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.SheetPrinting, true))
				{
					this.checkUseSheetPrinter.Checked = false;
					return;
				}
				if (m_printertypeArray != null
                    && m_printertypeArray.Count >= DCSDEV.PrintProperties.PrinterTypeArray.NUMBASECLASSES
                    && m_indexCurrent != DCSDEV.PrintProperties.PrinterTypeArray.NUMBASECLASSES - 1)
				{
                    m_indexCurrent = DCSDEV.PrintProperties.PrinterTypeArray.NUMBASECLASSES - 1;

					// set scroller
					this.SetScroller();

                    DCSDEV.PrintProperties.PrinterTypeDatum bcDatum = (DCSDEV.PrintProperties.PrinterTypeDatum)m_printertypeArray[m_indexCurrent];
					this.printerTypeInstanceControl.SetPrinterTypeDatum(bcDatum, m_indexCurrent);
				}
			}
		}

		private void buttonValidate_Click(object sender, System.EventArgs e)
		{
			bool bRet = ValidateQueryAndFix(false, false);	// bool bQuiet, bool bCheckOnly
			if (bRet) DCSMsg.Show("Checks OK");
			return;
		}

		private void buttonSelectDataRootDir_Click(object sender, System.EventArgs e)
		{
			System.Windows.Forms.FolderBrowserDialog folderDialog;
			folderDialog = new System.Windows.Forms.FolderBrowserDialog();
			folderDialog.Description = "Select Data Root Directory";

			//folderDialog.RootFolder = Environment.SpecialFolder.Personal; = My Documents
			folderDialog.SelectedPath = this.tbDataRootDir.Text;  // initial dir

			DialogResult result = folderDialog.ShowDialog(this);
			if (result != DialogResult.Cancel)
			{
				string strDataRootDir;
				strDataRootDir = folderDialog.SelectedPath;
				// with no network permissions, data root dir must be equal to or under install directory
				if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.NetworkPaths))
				{
					if (!strDataRootDir.ToUpper().StartsWith(m_ps.m_strDCSInstallDirectory.ToUpper()))
					{
						strDataRootDir = m_ps.m_strDCSInstallDirectory;
						DCSMsg.Show(String.Format("You have no networking permissions.\nThe Data Root Directory must be equal to or a subdirectory of {0}.", strDataRootDir));
					}
				}

				this.tbDataRootDir.Text = strDataRootDir;
				if (m_bUseBadgeDataDirOverride && m_strBadgeDataOverride != "")
					this.tbBadgeDataDir.Text = m_strBadgeDataOverride;
				else
					this.tbBadgeDataDir.Text = System.IO.Path.Combine(this.tbDataRootDir.Text, "BadgeData");
			}
		}

		private void buttonSelectBadgeDataDirOverride_Click(object sender, System.EventArgs e)
		{
			bool bCanNetwork = DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.NetworkPaths);
			DialogResult dr;
			string msg;

			msg = "The OVERRIDE feature is intended for compatibility with older systems and is not normally used." +
			"\r\n\nThe Badge Data Directory normally defaults to the Data Root Directory plus \"BadgeData\"." +
			String.Format("\r\nIn this case that would be \"{0}\".", System.IO.Path.Combine(this.tbDataRootDir.Text, "BadgeData")) +
			"\r\n\n\nClick NO to use the default directory (recommended).";
			if (bCanNetwork)
				msg = msg + "\r\n\nClick YES to choose your own directory as an override to this default.";
			else
				msg = msg + string.Format("\r\n\nClick YES to override the default directory and use \"{0}\".", this.tbDataRootDir.Text);

			dr = DCSDEV.DCSMsg.ShowYNC(msg);
			if (dr == DialogResult.No)
			{
				m_bUseBadgeDataDirOverride = false;
				m_strBadgeDataOverride = "";
				this.tbBadgeDataDir.Text = System.IO.Path.Combine(this.tbDataRootDir.Text, "BadgeData");
				this.labelBadgeDataDir.Text = m_strDefaultLabel;
			}
			else if (dr == DialogResult.Yes)
			{
				try
				{
					if (bCanNetwork)
					{
						string strBadgeDataOverride = m_strBadgeDataOverride;
						if (strBadgeDataOverride == "") strBadgeDataOverride = this.tbDataRootDir.Text;
						System.Windows.Forms.FolderBrowserDialog folderDialog;
						folderDialog = new System.Windows.Forms.FolderBrowserDialog();
						folderDialog.Description = "Select Override for Badge Data Directory";

						folderDialog.SelectedPath = strBadgeDataOverride;  // initial dir

						DialogResult result = folderDialog.ShowDialog(this);
						if (result != DialogResult.Cancel)
						{
							m_strBadgeDataOverride = folderDialog.SelectedPath;
							m_bUseBadgeDataDirOverride = true;
							this.tbBadgeDataDir.Text = m_strBadgeDataOverride; 
							this.labelBadgeDataDir.Text = m_strOverrideLabel;
						}
						else return;
					}
					else
					{
						m_strBadgeDataOverride = this.tbDataRootDir.Text;
							m_bUseBadgeDataDirOverride = true;
							this.tbBadgeDataDir.Text = m_strBadgeDataOverride; 
							this.labelBadgeDataDir.Text = m_strOverrideLabel;
					}
				}
				catch (Exception ex)
				{
					DCSDEV.DCSMsg.Show("ERROR: in SelectBadgeDataDirOverride dialog.", ex);
					return;
				}
			}
			else return;
		}
	}
}
