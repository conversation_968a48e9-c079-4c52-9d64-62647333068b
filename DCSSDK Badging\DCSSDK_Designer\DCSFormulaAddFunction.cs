using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace DCSDEV.DCSDesigner
{
	public partial class DCSFormulaAddFunction : Form
	{
		public DCSFormulaAddFunction()
		{
			InitializeComponent();

			this.listBoxFunctionToAppend.Items.Add("IIF( *ConditionalExpression* , *valueiftrue* , *valueiffalse* )");
			this.listBoxFunctionToAppend.Items.Add("ISEMPTY( *field* )");
			this.listBoxFunctionToAppend.Items.Add("ISNOTEMPTY( *field* )");
			this.listBoxFunctionToAppend.Items.Add("LCASE( *text* )");
			this.listBoxFunctionToAppend.Items.Add("LEFT( *text* , *length* )");
			this.listBoxFunctionToAppend.Items.Add("LEN( *text* )");
			this.listBoxFunctionToAppend.Items.Add("MID( *text* , *position* )");
			this.listBoxFunctionToAppend.Items.Add("MID( *text* , *position* , *length* )");
			this.listBoxFunctionToAppend.Items.Add("RIGHT( *text* , *length* )");
			this.listBoxFunctionToAppend.Items.Add("TRIM( *text* )");
			this.listBoxFunctionToAppend.Items.Add("UCASE( *text* )");
		}

		private void buttonOK_Click(object sender, EventArgs e)
		{
			if (this.listBoxFunctionToAppend.Text == null || this.listBoxFunctionToAppend.Text == "")
			{
				DialogResult = DialogResult.Cancel;
				DCSDEV.DCSMsg.Show("A function must be selected.");
				return;
			}
			DialogResult = DialogResult.OK;
			this.Close();
		}

		private void buttonCancel_Click(object sender, EventArgs e)
		{
			this.Close();
		}

		public string FunctionToAdd
		{
			get
			{
				return this.listBoxFunctionToAppend.Text;
			}
		}
	}
}