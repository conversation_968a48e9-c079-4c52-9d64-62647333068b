using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;

using DCSDEV;

namespace DCSDEV.DCSDesigner
{
	/// <summary>
	/// Summary description for IcaoObjProperties.
	/// </summary>
    internal class IcaoObjProperties : System.Windows.Forms.Form
	{
		private ArrayList m_DesignObjectsSelected;
		private DCSDEV.DCSDesigner.DCSDesignerView m_view;
		private DCSDEV.DCSDesign.DCSDesign m_design;
		string prop_SourceName = String.Empty;
		private System.Windows.Forms.Button buttonApply;
		private System.Windows.Forms.Button buttonCancel;
		private System.Windows.Forms.Button buttonAccept;
		private System.Windows.Forms.TabControl tabControl1;
		private System.Windows.Forms.TabPage tabPage1;
		private System.Windows.Forms.GroupBox gb_samplevalues;
		private System.Windows.Forms.TextBox em_sample_doctype;
		private System.Windows.Forms.TextBox em_sample_surnames;
		private System.Windows.Forms.TextBox em_sample_opt2;
		private System.Windows.Forms.TextBox em_sample_docno;
		private System.Windows.Forms.TextBox em_sample_names;
		private System.Windows.Forms.TextBox em_sample_opt1;
		private System.Windows.Forms.DateTimePicker em_sample_dateofbirth;
		private System.Windows.Forms.DateTimePicker em_sample_dateofexpiry;
		private System.Windows.Forms.TextBox em_sample_nationality;
		private System.Windows.Forms.TextBox em_sample_issuingstate;
		private System.Windows.Forms.ComboBox ddlb_sample_sex;
		private System.Windows.Forms.GroupBox gb_icao_db;
		private System.Windows.Forms.ComboBox ddlb_icaodatasource_surnames;
		private System.Windows.Forms.ComboBox ddlb_icaodatasource_names;
		private System.Windows.Forms.ComboBox ddlb_icaodatasource_nationality;
		private System.Windows.Forms.ComboBox ddlb_icaodatasource_dateofbirth;
		private System.Windows.Forms.ComboBox ddlb_icaodatasource_opt2;
		private System.Windows.Forms.ComboBox ddlb_icaodatasource_sex;
		private System.Windows.Forms.ComboBox ddlb_icaodatasource_dateofexpiry;
		private System.Windows.Forms.ComboBox ddlb_icaodatasource_doctype;
		private System.Windows.Forms.ComboBox ddlb_icaodatasource_docno;
		private System.Windows.Forms.ComboBox ddlb_icaodatasource_opt1;
		private System.Windows.Forms.ComboBox ddlb_icaodatasource_issuingstate;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.Label label4;
		private System.Windows.Forms.Label label5;
		private System.Windows.Forms.Label label6;
		private System.Windows.Forms.Label label7;
		private System.Windows.Forms.Label label8;
		private System.Windows.Forms.Label label9;
		private System.Windows.Forms.Label label10;
		private System.Windows.Forms.Label label11;
		private System.Windows.Forms.TabPage tabPage2;
		private DCSDEV.DCSDesigner.DCSFontProperties dcsFontProperties1;
        private DCSDEV.DCSDesigner.DCSRotationProperties dcsRotationProperties1;
        private DCSDEV.DCSDesigner.DCSAlignmentProperties dcsAlignmentProperties1;
        private DCSDEV.DCSDesigner.DCSPositionSizeProperties dcsPositionSizeProperties1;
		private System.Windows.Forms.GroupBox gb_type;
		private System.Windows.Forms.RadioButton rb_3lines;
		private System.Windows.Forms.RadioButton rb_2linesp;
		private System.Windows.Forms.RadioButton rb_2lines;
		private System.Windows.Forms.Button buttonRotateCW;
		private System.Windows.Forms.Button buttonRotateCCW;
		private System.Windows.Forms.ToolTip toolTip1;
        private Label labelPanel;
        private ComboBox comboBoxPanel;
		private System.ComponentModel.IContainer components;

		public IcaoObjProperties(DCSDEV.DCSDesign.DCSDesign activeDoc, DCSDEV.DCSDesigner.DCSDesignerView activeView, ArrayList designObjectsSelected, bool bNew)
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			m_view = activeView;
			m_design = activeDoc;
			m_DesignObjectsSelected = designObjectsSelected;

			this.AssignDropDownItems(ddlb_icaodatasource_surnames);
			this.AssignDropDownItems(ddlb_icaodatasource_names);
			this.AssignDropDownItems(ddlb_icaodatasource_dateofbirth);
			this.AssignDropDownItems(ddlb_icaodatasource_nationality);
			this.AssignDropDownItems(ddlb_icaodatasource_sex);
			this.AssignDropDownItems(ddlb_icaodatasource_doctype);
			this.AssignDropDownItems(ddlb_icaodatasource_docno);
			this.AssignDropDownItems(ddlb_icaodatasource_dateofexpiry);
			this.AssignDropDownItems(ddlb_icaodatasource_issuingstate);
			this.AssignDropDownItems(ddlb_icaodatasource_opt1);
			this.AssignDropDownItems(ddlb_icaodatasource_opt2);

			if (m_DesignObjectsSelected.Count > 0)
			{
				DCSDEV.DCSDesign.DCSDesignObject designObject = (DCSDEV.DCSDesign.DCSDesignObject)m_DesignObjectsSelected[0];
				this.MoveDataToDlg(designObject);
			}

            DCSDEV.PrintProperties.PrinterTypeDatum bcDatum = (DCSDEV.PrintProperties.PrinterTypeDatum)this.m_view.m_mainWin.m_printertypeArray[m_design.PrinterTypeIndex];
            this.comboBoxPanel.Visible = this.labelPanel.Visible = bcDatum.m_IfKpanel;

			this.toolTip1.SetToolTip(this.buttonRotateCCW, "Rotate object and its frame 90 degrees counter clockwise");
			this.toolTip1.SetToolTip(this.buttonRotateCW,  "Rotate object and its frame 90 degrees clockwise");
			this.toolTip1.SetToolTip(this.buttonApply,  "Save changes, apply changes to display, and keep dialog open.");
			this.toolTip1.SetToolTip(this.buttonAccept,  "Save changes, close dialog, and apply changes to display.");
			this.toolTip1.SetToolTip(this.buttonCancel,  "Undo changes and close dialog.");
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(IcaoObjProperties));
            this.buttonApply = new System.Windows.Forms.Button();
            this.buttonCancel = new System.Windows.Forms.Button();
            this.buttonAccept = new System.Windows.Forms.Button();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.gb_type = new System.Windows.Forms.GroupBox();
            this.rb_3lines = new System.Windows.Forms.RadioButton();
            this.rb_2linesp = new System.Windows.Forms.RadioButton();
            this.rb_2lines = new System.Windows.Forms.RadioButton();
            this.dcsAlignmentProperties1 = new DCSDEV.DCSDesigner.DCSAlignmentProperties();
            this.dcsRotationProperties1 = new DCSDEV.DCSDesigner.DCSRotationProperties();
            this.dcsFontProperties1 = new DCSDEV.DCSDesigner.DCSFontProperties();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.gb_samplevalues = new System.Windows.Forms.GroupBox();
            this.em_sample_doctype = new System.Windows.Forms.TextBox();
            this.em_sample_surnames = new System.Windows.Forms.TextBox();
            this.em_sample_opt2 = new System.Windows.Forms.TextBox();
            this.em_sample_docno = new System.Windows.Forms.TextBox();
            this.em_sample_names = new System.Windows.Forms.TextBox();
            this.em_sample_opt1 = new System.Windows.Forms.TextBox();
            this.em_sample_dateofbirth = new System.Windows.Forms.DateTimePicker();
            this.em_sample_dateofexpiry = new System.Windows.Forms.DateTimePicker();
            this.em_sample_nationality = new System.Windows.Forms.TextBox();
            this.em_sample_issuingstate = new System.Windows.Forms.TextBox();
            this.ddlb_sample_sex = new System.Windows.Forms.ComboBox();
            this.gb_icao_db = new System.Windows.Forms.GroupBox();
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label8 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.ddlb_icaodatasource_surnames = new System.Windows.Forms.ComboBox();
            this.ddlb_icaodatasource_names = new System.Windows.Forms.ComboBox();
            this.ddlb_icaodatasource_nationality = new System.Windows.Forms.ComboBox();
            this.ddlb_icaodatasource_dateofbirth = new System.Windows.Forms.ComboBox();
            this.ddlb_icaodatasource_opt2 = new System.Windows.Forms.ComboBox();
            this.ddlb_icaodatasource_sex = new System.Windows.Forms.ComboBox();
            this.ddlb_icaodatasource_dateofexpiry = new System.Windows.Forms.ComboBox();
            this.ddlb_icaodatasource_doctype = new System.Windows.Forms.ComboBox();
            this.ddlb_icaodatasource_docno = new System.Windows.Forms.ComboBox();
            this.ddlb_icaodatasource_opt1 = new System.Windows.Forms.ComboBox();
            this.ddlb_icaodatasource_issuingstate = new System.Windows.Forms.ComboBox();
            this.buttonRotateCW = new System.Windows.Forms.Button();
            this.buttonRotateCCW = new System.Windows.Forms.Button();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.labelPanel = new System.Windows.Forms.Label();
            this.comboBoxPanel = new System.Windows.Forms.ComboBox();
            this.dcsPositionSizeProperties1 = new DCSDEV.DCSDesigner.DCSPositionSizeProperties();
            this.tabControl1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.gb_type.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.gb_samplevalues.SuspendLayout();
            this.gb_icao_db.SuspendLayout();
            this.SuspendLayout();
            // 
            // buttonApply
            // 
            resources.ApplyResources(this.buttonApply, "buttonApply");
            this.buttonApply.Name = "buttonApply";
            this.buttonApply.Click += new System.EventHandler(this.buttonApply_Click);
            // 
            // buttonCancel
            // 
            this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            resources.ApplyResources(this.buttonCancel, "buttonCancel");
            this.buttonCancel.Name = "buttonCancel";
            this.buttonCancel.ContextMenuChanged += new System.EventHandler(this.buttonCancel_Click);
            // 
            // buttonAccept
            // 
            resources.ApplyResources(this.buttonAccept, "buttonAccept");
            this.buttonAccept.Name = "buttonAccept";
            this.buttonAccept.Click += new System.EventHandler(this.buttonAccept_Click);
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Controls.Add(this.tabPage1);
            resources.ApplyResources(this.tabControl1, "tabControl1");
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.gb_type);
            this.tabPage2.Controls.Add(this.dcsAlignmentProperties1);
            this.tabPage2.Controls.Add(this.dcsRotationProperties1);
            this.tabPage2.Controls.Add(this.dcsFontProperties1);
            resources.ApplyResources(this.tabPage2, "tabPage2");
            this.tabPage2.Name = "tabPage2";
            // 
            // gb_type
            // 
            this.gb_type.Controls.Add(this.rb_3lines);
            this.gb_type.Controls.Add(this.rb_2linesp);
            this.gb_type.Controls.Add(this.rb_2lines);
            resources.ApplyResources(this.gb_type, "gb_type");
            this.gb_type.Name = "gb_type";
            this.gb_type.TabStop = false;
            // 
            // rb_3lines
            // 
            resources.ApplyResources(this.rb_3lines, "rb_3lines");
            this.rb_3lines.Name = "rb_3lines";
            // 
            // rb_2linesp
            // 
            resources.ApplyResources(this.rb_2linesp, "rb_2linesp");
            this.rb_2linesp.Name = "rb_2linesp";
            // 
            // rb_2lines
            // 
            resources.ApplyResources(this.rb_2lines, "rb_2lines");
            this.rb_2lines.Name = "rb_2lines";
            // 
            // dcsAlignmentProperties1
            // 
            resources.ApplyResources(this.dcsAlignmentProperties1, "dcsAlignmentProperties1");
            this.dcsAlignmentProperties1.Name = "dcsAlignmentProperties1";
            // 
            // dcsRotationProperties1
            // 
            this.dcsRotationProperties1.Cursor = System.Windows.Forms.Cursors.Default;
            resources.ApplyResources(this.dcsRotationProperties1, "dcsRotationProperties1");
            this.dcsRotationProperties1.Name = "dcsRotationProperties1";
            this.dcsRotationProperties1.RotateFlip = System.Drawing.RotateFlipType.RotateNoneFlipNone;
            // 
            // dcsFontProperties1
            // 
            this.dcsFontProperties1.DCSColor = System.Drawing.Color.Black;
            this.dcsFontProperties1.DCSFont = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dcsFontProperties1.DCSShadow = false;
            this.dcsFontProperties1.DCSShadowColor = System.Drawing.Color.Black;
            this.dcsFontProperties1.DCSSimple = 'I';
            this.dcsFontProperties1.DCSSizeToFit = false;
            this.dcsFontProperties1.DCSWordWrap = false;
            this.dcsFontProperties1.LineSpacing = 0;
            resources.ApplyResources(this.dcsFontProperties1, "dcsFontProperties1");
            this.dcsFontProperties1.Name = "dcsFontProperties1";
            this.dcsFontProperties1.Units = 0;
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.gb_samplevalues);
            this.tabPage1.Controls.Add(this.gb_icao_db);
            resources.ApplyResources(this.tabPage1, "tabPage1");
            this.tabPage1.Name = "tabPage1";
            // 
            // gb_samplevalues
            // 
            this.gb_samplevalues.Controls.Add(this.em_sample_doctype);
            this.gb_samplevalues.Controls.Add(this.em_sample_surnames);
            this.gb_samplevalues.Controls.Add(this.em_sample_opt2);
            this.gb_samplevalues.Controls.Add(this.em_sample_docno);
            this.gb_samplevalues.Controls.Add(this.em_sample_names);
            this.gb_samplevalues.Controls.Add(this.em_sample_opt1);
            this.gb_samplevalues.Controls.Add(this.em_sample_dateofbirth);
            this.gb_samplevalues.Controls.Add(this.em_sample_dateofexpiry);
            this.gb_samplevalues.Controls.Add(this.em_sample_nationality);
            this.gb_samplevalues.Controls.Add(this.em_sample_issuingstate);
            this.gb_samplevalues.Controls.Add(this.ddlb_sample_sex);
            resources.ApplyResources(this.gb_samplevalues, "gb_samplevalues");
            this.gb_samplevalues.Name = "gb_samplevalues";
            this.gb_samplevalues.TabStop = false;
            // 
            // em_sample_doctype
            // 
            resources.ApplyResources(this.em_sample_doctype, "em_sample_doctype");
            this.em_sample_doctype.Name = "em_sample_doctype";
            // 
            // em_sample_surnames
            // 
            resources.ApplyResources(this.em_sample_surnames, "em_sample_surnames");
            this.em_sample_surnames.Name = "em_sample_surnames";
            // 
            // em_sample_opt2
            // 
            resources.ApplyResources(this.em_sample_opt2, "em_sample_opt2");
            this.em_sample_opt2.Name = "em_sample_opt2";
            // 
            // em_sample_docno
            // 
            resources.ApplyResources(this.em_sample_docno, "em_sample_docno");
            this.em_sample_docno.Name = "em_sample_docno";
            // 
            // em_sample_names
            // 
            resources.ApplyResources(this.em_sample_names, "em_sample_names");
            this.em_sample_names.Name = "em_sample_names";
            // 
            // em_sample_opt1
            // 
            resources.ApplyResources(this.em_sample_opt1, "em_sample_opt1");
            this.em_sample_opt1.Name = "em_sample_opt1";
            // 
            // em_sample_dateofbirth
            // 
            resources.ApplyResources(this.em_sample_dateofbirth, "em_sample_dateofbirth");
            this.em_sample_dateofbirth.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.em_sample_dateofbirth.MaxDate = new System.DateTime(2100, 12, 31, 0, 0, 0, 0);
            this.em_sample_dateofbirth.MinDate = new System.DateTime(1850, 1, 1, 0, 0, 0, 0);
            this.em_sample_dateofbirth.Name = "em_sample_dateofbirth";
            this.em_sample_dateofbirth.Value = new System.DateTime(2006, 2, 20, 0, 0, 0, 0);
            // 
            // em_sample_dateofexpiry
            // 
            resources.ApplyResources(this.em_sample_dateofexpiry, "em_sample_dateofexpiry");
            this.em_sample_dateofexpiry.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.em_sample_dateofexpiry.MaxDate = new System.DateTime(2100, 12, 31, 0, 0, 0, 0);
            this.em_sample_dateofexpiry.MinDate = new System.DateTime(1850, 1, 1, 0, 0, 0, 0);
            this.em_sample_dateofexpiry.Name = "em_sample_dateofexpiry";
            this.em_sample_dateofexpiry.Value = new System.DateTime(2006, 1, 29, 22, 27, 36, 530);
            // 
            // em_sample_nationality
            // 
            resources.ApplyResources(this.em_sample_nationality, "em_sample_nationality");
            this.em_sample_nationality.Name = "em_sample_nationality";
            // 
            // em_sample_issuingstate
            // 
            resources.ApplyResources(this.em_sample_issuingstate, "em_sample_issuingstate");
            this.em_sample_issuingstate.Name = "em_sample_issuingstate";
            // 
            // ddlb_sample_sex
            // 
            this.ddlb_sample_sex.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.ddlb_sample_sex.Items.AddRange(new object[] {
            resources.GetString("ddlb_sample_sex.Items"),
            resources.GetString("ddlb_sample_sex.Items1"),
            resources.GetString("ddlb_sample_sex.Items2")});
            resources.ApplyResources(this.ddlb_sample_sex, "ddlb_sample_sex");
            this.ddlb_sample_sex.Name = "ddlb_sample_sex";
            // 
            // gb_icao_db
            // 
            this.gb_icao_db.Controls.Add(this.label1);
            this.gb_icao_db.Controls.Add(this.label2);
            this.gb_icao_db.Controls.Add(this.label3);
            this.gb_icao_db.Controls.Add(this.label4);
            this.gb_icao_db.Controls.Add(this.label5);
            this.gb_icao_db.Controls.Add(this.label6);
            this.gb_icao_db.Controls.Add(this.label7);
            this.gb_icao_db.Controls.Add(this.label8);
            this.gb_icao_db.Controls.Add(this.label9);
            this.gb_icao_db.Controls.Add(this.label10);
            this.gb_icao_db.Controls.Add(this.label11);
            this.gb_icao_db.Controls.Add(this.ddlb_icaodatasource_surnames);
            this.gb_icao_db.Controls.Add(this.ddlb_icaodatasource_names);
            this.gb_icao_db.Controls.Add(this.ddlb_icaodatasource_nationality);
            this.gb_icao_db.Controls.Add(this.ddlb_icaodatasource_dateofbirth);
            this.gb_icao_db.Controls.Add(this.ddlb_icaodatasource_opt2);
            this.gb_icao_db.Controls.Add(this.ddlb_icaodatasource_sex);
            this.gb_icao_db.Controls.Add(this.ddlb_icaodatasource_dateofexpiry);
            this.gb_icao_db.Controls.Add(this.ddlb_icaodatasource_doctype);
            this.gb_icao_db.Controls.Add(this.ddlb_icaodatasource_docno);
            this.gb_icao_db.Controls.Add(this.ddlb_icaodatasource_opt1);
            this.gb_icao_db.Controls.Add(this.ddlb_icaodatasource_issuingstate);
            resources.ApplyResources(this.gb_icao_db, "gb_icao_db");
            this.gb_icao_db.Name = "gb_icao_db";
            this.gb_icao_db.TabStop = false;
            // 
            // label1
            // 
            resources.ApplyResources(this.label1, "label1");
            this.label1.Name = "label1";
            // 
            // label2
            // 
            resources.ApplyResources(this.label2, "label2");
            this.label2.Name = "label2";
            // 
            // label3
            // 
            resources.ApplyResources(this.label3, "label3");
            this.label3.Name = "label3";
            // 
            // label4
            // 
            resources.ApplyResources(this.label4, "label4");
            this.label4.Name = "label4";
            // 
            // label5
            // 
            resources.ApplyResources(this.label5, "label5");
            this.label5.Name = "label5";
            // 
            // label6
            // 
            resources.ApplyResources(this.label6, "label6");
            this.label6.Name = "label6";
            // 
            // label7
            // 
            resources.ApplyResources(this.label7, "label7");
            this.label7.Name = "label7";
            // 
            // label8
            // 
            resources.ApplyResources(this.label8, "label8");
            this.label8.Name = "label8";
            // 
            // label9
            // 
            resources.ApplyResources(this.label9, "label9");
            this.label9.Name = "label9";
            // 
            // label10
            // 
            resources.ApplyResources(this.label10, "label10");
            this.label10.Name = "label10";
            // 
            // label11
            // 
            resources.ApplyResources(this.label11, "label11");
            this.label11.Name = "label11";
            // 
            // ddlb_icaodatasource_surnames
            // 
            this.ddlb_icaodatasource_surnames.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            resources.ApplyResources(this.ddlb_icaodatasource_surnames, "ddlb_icaodatasource_surnames");
            this.ddlb_icaodatasource_surnames.Name = "ddlb_icaodatasource_surnames";
            // 
            // ddlb_icaodatasource_names
            // 
            this.ddlb_icaodatasource_names.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            resources.ApplyResources(this.ddlb_icaodatasource_names, "ddlb_icaodatasource_names");
            this.ddlb_icaodatasource_names.Name = "ddlb_icaodatasource_names";
            // 
            // ddlb_icaodatasource_nationality
            // 
            this.ddlb_icaodatasource_nationality.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            resources.ApplyResources(this.ddlb_icaodatasource_nationality, "ddlb_icaodatasource_nationality");
            this.ddlb_icaodatasource_nationality.Name = "ddlb_icaodatasource_nationality";
            // 
            // ddlb_icaodatasource_dateofbirth
            // 
            this.ddlb_icaodatasource_dateofbirth.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            resources.ApplyResources(this.ddlb_icaodatasource_dateofbirth, "ddlb_icaodatasource_dateofbirth");
            this.ddlb_icaodatasource_dateofbirth.Name = "ddlb_icaodatasource_dateofbirth";
            // 
            // ddlb_icaodatasource_opt2
            // 
            this.ddlb_icaodatasource_opt2.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            resources.ApplyResources(this.ddlb_icaodatasource_opt2, "ddlb_icaodatasource_opt2");
            this.ddlb_icaodatasource_opt2.Name = "ddlb_icaodatasource_opt2";
            // 
            // ddlb_icaodatasource_sex
            // 
            this.ddlb_icaodatasource_sex.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            resources.ApplyResources(this.ddlb_icaodatasource_sex, "ddlb_icaodatasource_sex");
            this.ddlb_icaodatasource_sex.Name = "ddlb_icaodatasource_sex";
            // 
            // ddlb_icaodatasource_dateofexpiry
            // 
            this.ddlb_icaodatasource_dateofexpiry.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            resources.ApplyResources(this.ddlb_icaodatasource_dateofexpiry, "ddlb_icaodatasource_dateofexpiry");
            this.ddlb_icaodatasource_dateofexpiry.Name = "ddlb_icaodatasource_dateofexpiry";
            // 
            // ddlb_icaodatasource_doctype
            // 
            this.ddlb_icaodatasource_doctype.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            resources.ApplyResources(this.ddlb_icaodatasource_doctype, "ddlb_icaodatasource_doctype");
            this.ddlb_icaodatasource_doctype.Name = "ddlb_icaodatasource_doctype";
            // 
            // ddlb_icaodatasource_docno
            // 
            this.ddlb_icaodatasource_docno.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            resources.ApplyResources(this.ddlb_icaodatasource_docno, "ddlb_icaodatasource_docno");
            this.ddlb_icaodatasource_docno.Name = "ddlb_icaodatasource_docno";
            // 
            // ddlb_icaodatasource_opt1
            // 
            this.ddlb_icaodatasource_opt1.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            resources.ApplyResources(this.ddlb_icaodatasource_opt1, "ddlb_icaodatasource_opt1");
            this.ddlb_icaodatasource_opt1.Name = "ddlb_icaodatasource_opt1";
            // 
            // ddlb_icaodatasource_issuingstate
            // 
            this.ddlb_icaodatasource_issuingstate.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            resources.ApplyResources(this.ddlb_icaodatasource_issuingstate, "ddlb_icaodatasource_issuingstate");
            this.ddlb_icaodatasource_issuingstate.Name = "ddlb_icaodatasource_issuingstate";
            // 
            // buttonRotateCW
            // 
            resources.ApplyResources(this.buttonRotateCW, "buttonRotateCW");
            this.buttonRotateCW.Name = "buttonRotateCW";
            this.buttonRotateCW.Click += new System.EventHandler(this.buttonRotate_Click);
            // 
            // buttonRotateCCW
            // 
            resources.ApplyResources(this.buttonRotateCCW, "buttonRotateCCW");
            this.buttonRotateCCW.Name = "buttonRotateCCW";
            this.buttonRotateCCW.Click += new System.EventHandler(this.buttonRotate_Click);
            // 
            // labelPanel
            // 
            resources.ApplyResources(this.labelPanel, "labelPanel");
            this.labelPanel.Name = "labelPanel";
            // 
            // comboBoxPanel
            // 
            this.comboBoxPanel.FormattingEnabled = true;
            this.comboBoxPanel.Items.AddRange(new object[] {
            resources.GetString("comboBoxPanel.Items"),
            resources.GetString("comboBoxPanel.Items1")});
            resources.ApplyResources(this.comboBoxPanel, "comboBoxPanel");
            this.comboBoxPanel.Name = "comboBoxPanel";
            // 
            // dcsPositionSizeProperties1
            // 
            this.dcsPositionSizeProperties1.DisplayBounds = new System.Drawing.Rectangle(0, 0, 0, 0);
            resources.ApplyResources(this.dcsPositionSizeProperties1, "dcsPositionSizeProperties1");
            this.dcsPositionSizeProperties1.Name = "dcsPositionSizeProperties1";
            this.dcsPositionSizeProperties1.Units = 0;
            this.dcsPositionSizeProperties1.UnitsChanged += new DCSDEV.DCSDesigner.DCSPositionSizeProperties.UnitsChangedEventHandler(this.dcsPositionSizeProperties1_UnitsChanged);
            // 
            // IcaoObjProperties
            // 
            this.AcceptButton = this.buttonAccept;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.CancelButton = this.buttonCancel;
            resources.ApplyResources(this, "$this");
            this.Controls.Add(this.labelPanel);
            this.Controls.Add(this.comboBoxPanel);
            this.Controls.Add(this.buttonRotateCW);
            this.Controls.Add(this.buttonRotateCCW);
            this.Controls.Add(this.dcsPositionSizeProperties1);
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.buttonApply);
            this.Controls.Add(this.buttonCancel);
            this.Controls.Add(this.buttonAccept);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.Name = "IcaoObjProperties";
            this.tabControl1.ResumeLayout(false);
            this.tabPage2.ResumeLayout(false);
            this.gb_type.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.gb_samplevalues.ResumeLayout(false);
            this.gb_samplevalues.PerformLayout();
            this.gb_icao_db.ResumeLayout(false);
            this.ResumeLayout(false);

		}
		#endregion

		private bool ApplyData()
		{
			// check validity of the data

			// All check pass .  It should be safe to procede
			DCSDEV.DCSDesign.DCSDesignObject designObject = (DCSDEV.DCSDesign.DCSDesignObject)m_DesignObjectsSelected[0];
			this.MoveDataFromDlg(designObject);

			return true;
		}

		private void AssignDropDownItems(System.Windows.Forms.ComboBox combo)
		{
			combo.Items.Clear();
			combo.Items.Add("");
			foreach(string str in this.m_view.m_mainWin.m_AllDBFieldNames)
				combo.Items.Add(str);
		}

		private void MoveDataFromDlg(DCSDEV.DCSDesign.DCSDesignObject designObject)
		{
			designObject.SourceName =
				this.ddlb_icaodatasource_surnames.SelectedItem + "|" + 
				this.ddlb_icaodatasource_names.SelectedItem + "|" + 
				this.ddlb_icaodatasource_dateofbirth.SelectedItem + "|" + 
				this.ddlb_icaodatasource_nationality.SelectedItem + "|" + 
				this.ddlb_icaodatasource_sex.SelectedItem + "|" + 
				this.ddlb_icaodatasource_doctype.SelectedItem + "|" + 
				this.ddlb_icaodatasource_docno.SelectedItem + "|" + 
				this.ddlb_icaodatasource_dateofexpiry.SelectedItem + "|" + 
				this.ddlb_icaodatasource_issuingstate.SelectedItem + "|" + 
				this.ddlb_icaodatasource_opt1.SelectedItem + "|" + 
				this.ddlb_icaodatasource_opt2.SelectedItem;

			designObject.Text =
				this.em_sample_surnames.Text + "|" + 
				this.em_sample_names.Text + "|" + 
				this.em_sample_dateofbirth.Text + "|" + 
				this.em_sample_nationality.Text + "|" + 
				this.ddlb_sample_sex.Text + "|" + 
				this.em_sample_doctype.Text + "|" + 
				this.em_sample_docno.Text + "|" + 
				this.em_sample_dateofexpiry.Text + "|" + 
				this.em_sample_issuingstate.Text + "|" + 
				this.em_sample_opt1.Text + "|" + 
				this.em_sample_opt2.Text;

			if (this.rb_2linesp.Checked) designObject.IcaoType = 0;
			else if (this.rb_3lines.Checked) designObject.IcaoType = 1;
			else if (this.rb_2lines.Checked) designObject.IcaoType = 2;

			// position size
			designObject.Bounds = this.dcsPositionSizeProperties1.DisplayBounds;
			DCSDEV.DCSDesignDataAccess.SetUnits(this.dcsPositionSizeProperties1.Units);
			designObject.RotateFlip = this.dcsRotationProperties1.RotateFlip;

			designObject.FontEx.Font = this.dcsFontProperties1.DCSFont;
			designObject.FontEx.FontLineSpacing = this.dcsFontProperties1.LineSpacing;
			designObject.FontEx.ForeColor = this.dcsFontProperties1.DCSColor;
			designObject.FontEx.Wrap = this.dcsFontProperties1.DCSWordWrap;
			designObject.FontEx.SizeToFit = this.dcsFontProperties1.DCSSizeToFit;
			designObject.FontEx.Shadow = this.dcsFontProperties1.DCSShadow;
			designObject.FontEx.ShadowColor = this.dcsFontProperties1.DCSShadowColor;

            designObject.SpecialKPanel = (this.comboBoxPanel.Text == "default") ? 0 : 1;

			// alignment
			int hj, vj;
			this.dcsAlignmentProperties1.GetAlignment(out hj, out vj);
			designObject.Justification = (DCSDEV.DCSDatatypes.Justifications)vj;
			designObject.Alignment = (DCSDEV.DCSDatatypes.Alignments)hj;
		}

		private void MoveDataToDlg(DCSDEV.DCSDesign.DCSDesignObject designObject)
		{
			// position size
			this.dcsPositionSizeProperties1.DisplayBounds = designObject.Bounds;
			this.dcsPositionSizeProperties1.Units = DCSDEV.DCSDesignDataAccess.GetUnits();
			this.dcsRotationProperties1.RotateFlip = designObject.RotateFlip;

			this.dcsFontProperties1.Units = DCSDEV.DCSDesignDataAccess.GetUnits();
			this.dcsFontProperties1.DCSFont = designObject.FontEx.Font;
			this.dcsFontProperties1.LineSpacing = designObject.FontEx.FontLineSpacing;
			this.dcsFontProperties1.DCSColor = designObject.FontEx.ForeColor;
			this.dcsFontProperties1.DCSWordWrap = designObject.FontEx.Wrap;
			this.dcsFontProperties1.DCSSizeToFit = designObject.FontEx.SizeToFit;
			this.dcsFontProperties1.DCSShadow = designObject.FontEx.Shadow;
			this.dcsFontProperties1.DCSShadowColor = designObject.FontEx.ShadowColor;
            
            this.comboBoxPanel.Text = designObject.SpecialKPanel == 0 ? "default" : "K panel";

			// alignment
			this.dcsAlignmentProperties1.SetAlignment(designObject.Justification, designObject.Alignment);

			// source
			string [] tokens = designObject.SourceName.Split("|".ToCharArray(),11);
			int cnt = tokens.Length;
			if (cnt > 0) this.ddlb_icaodatasource_surnames.SelectedItem = tokens[0];
			if (cnt > 1) this.ddlb_icaodatasource_names.SelectedItem = tokens[1];
			if (cnt > 2) this.ddlb_icaodatasource_dateofbirth.SelectedItem = tokens[2];
			if (cnt > 3) this.ddlb_icaodatasource_nationality.SelectedItem = tokens[3];
			if (cnt > 4) this.ddlb_icaodatasource_sex.SelectedItem = tokens[4];
			if (cnt > 5) this.ddlb_icaodatasource_doctype.SelectedItem = tokens[5];
			if (cnt > 6) this.ddlb_icaodatasource_docno.SelectedItem = tokens[6];
			if (cnt > 7) this.ddlb_icaodatasource_dateofexpiry.SelectedItem = tokens[7];
			if (cnt > 8) this.ddlb_icaodatasource_issuingstate.SelectedItem = tokens[8];
			if (cnt > 9) this.ddlb_icaodatasource_opt1.SelectedItem = tokens[9];
			if (cnt > 10) this.ddlb_icaodatasource_opt2.SelectedItem = tokens[10];

			tokens = designObject.Text.Split("|".ToCharArray(),11);
			cnt = tokens.Length;
			if (cnt > 0) this.em_sample_surnames.Text = tokens[0];
			if (cnt > 1) this.em_sample_names.Text = tokens[1];
			if (cnt > 2) this.em_sample_dateofbirth.Text = tokens[2];
			if (cnt > 3) this.em_sample_nationality.Text = tokens[3];
			if (cnt > 4) this.ddlb_sample_sex.Text = tokens[4];
			if (cnt > 5) this.em_sample_doctype.Text = tokens[5];
			if (cnt > 6) this.em_sample_docno.Text = tokens[6];
			if (cnt > 7) this.em_sample_dateofexpiry.Text = tokens[7];
			if (cnt > 8) this.em_sample_issuingstate.Text = tokens[8];
			if (cnt > 9) this.em_sample_opt1.Text = tokens[9];
			if (cnt > 10) this.em_sample_opt2.Text = tokens[10];

			switch (designObject.IcaoType)
			{
				case 0: this.rb_2linesp.Checked = true; break;  
				case 1: this.rb_3lines.Checked = true; break;  
				case 2: this.rb_2lines.Checked = true; break;
			}
		}

		private void buttonAccept_Click(object sender, System.EventArgs e)
		{
			bool bRet = this.ApplyData();
			if (bRet) 
			{
				this.DialogResult = System.Windows.Forms.DialogResult.OK;
				this.Close();
			}
		}

		private void buttonCancel_Click(object sender, System.EventArgs e)
		{
			this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.Close();
		}

		private void buttonApply_Click(object sender, System.EventArgs e)
		{
			this.ApplyData();
			m_design.m_isDirty = true;
			m_design.m_isViewDirty = true;
			m_view.Invalidate(true);
		}

		private void buttonRotate_Click(object sender, System.EventArgs e)
		{
			Rectangle rect = this.dcsPositionSizeProperties1.DisplayBounds;
			int width = rect.Width;
			rect.Width = rect.Height;
			rect.Height = width;
			this.dcsPositionSizeProperties1.DisplayBounds = rect;
			if (sender == this.buttonRotateCCW)
				this.dcsRotationProperties1.Rotate270();
			else
				this.dcsRotationProperties1.Rotate90();
			this.dcsRotationProperties1.Invalidate();
			this.tabControl1.SelectedIndex = 0;
		}

		private void dcsPositionSizeProperties1_UnitsChanged(object sender, System.EventArgs ev)
		{
			int iSpace = this.dcsFontProperties1.LineSpacing;
			this.dcsFontProperties1.Units = this.dcsPositionSizeProperties1.Units;
			this.dcsFontProperties1.LineSpacing = iSpace;
		}
	
		public int LastTab
		{
			get { return this.tabControl1.SelectedIndex; }
			set { this.tabControl1.SelectedIndex = value; }
		}
	}
}
