using System;
using System.Collections;
using System.Windows.Forms;
using System.Drawing.Printing;
using System.IO;
using System.Drawing;

using DCSDEV;
//using DCSSDK.CaptureMgt;

namespace DCSDEV.DCSDesign
{
	public class FieldInfo
	{
		public string fieldName;
		public object FieldValue;

		public FieldInfo()
		{
		}
	}

	/// <summary>
	/// Summary description for DCSBadgeDataset.
	/// </summary>
    public class DCSBadgeDataset
	{
		public ArrayList m_lFieldNamesAndValues;
		public string m_strBadgeDatFilename = null;
		public string m_strDesignName = null;
		public string m_strDocumentName = null;
		public string m_strPortraitName = null;
		public string m_strSignatureName = null;
		public string m_strFingerprintName = null;
		private bool m_ANSIStyle = true;

		public DCSBadgeDataset()
		{
			//
			// TODO: Add constructor logic here
			//
			m_lFieldNamesAndValues = new ArrayList();
		}

		public void Clear()
		{
			m_lFieldNamesAndValues.Clear();
			m_strBadgeDatFilename = null;
			m_strDesignName = null;
			m_strDocumentName = null;
			m_strPortraitName = null;
			m_strSignatureName = null;
			m_strFingerprintName = null;
		}

		public bool LoadAll(string allfield, string strBadgeDat)
		{
			DCSDEV.ParameterStore ps = new ParameterStore("DCSSDK_Mgt");
			m_ANSIStyle = ps.GetBoolParameter("ANSIStyle", m_ANSIStyle);

			bool bRet;
			bRet = this.ReadAllFieldsFile(allfield);
			if (!bRet) 
			{
				DCSMsg.Show("The field names file " + allfield + " is missing or cannot be read.");
				return false;
			}
			/////////////////////////////////////////
			// Read Badge.DAT (badge dataset file) //
			/////////////////////////////////////////

			bRet = this.ReadBadgeDatFile(strBadgeDat);
			if (!bRet) 
				DCSMsg.Show("The badge data file " + strBadgeDat + " is missing or cannot be read.");
			return bRet;
		}

		// return null if field not found; Empty string if no value for a field otherwise found
		public string LookupFieldValue(string strFieldName)
		{
			foreach(FieldInfo fi in this.m_lFieldNamesAndValues) 
			{
				if (strFieldName.ToUpper() == fi.fieldName.ToUpper())
					return fi.FieldValue.ToString();
			}
			return null;
		}

		private bool ReadAllFieldsFile(string allfield)
		{
			StreamReader sr = null;
			string line;
			try
			{
				if (m_ANSIStyle)
					sr = new StreamReader(allfield, System.Text.Encoding.Default);
				else
					sr = new StreamReader(allfield);

				while ((line = sr.ReadLine()) != null) 
				{
                    DCSDEV.DCSDesign.FieldInfo thisfld = new FieldInfo();
					thisfld.fieldName = line.ToString();
					thisfld.FieldValue = String.Empty;
					m_lFieldNamesAndValues.Add(thisfld);
				}
			}
			catch
			{
				return false;
			}
			finally
			{
				if (sr != null) sr.Close();
			}
			return true;
		}

		public bool ReadBadgeDatFile(string strBadgeDat)
		{
			this.m_strBadgeDatFilename = strBadgeDat;
			this.m_strDesignName = "default";
			this.m_strDocumentName = "document";
			StreamReader sr = null;
			String line;
		
			char [] separator = new char []{' '};
			string  lineparams1, lineparams2;
						
			try
			{
				if (!File.Exists(m_strBadgeDatFilename))
				{
					DCSDEV.ParameterStore ps = new ParameterStore("DCSSDK_Mgt");
					this.m_strBadgeDatFilename = System.IO.Path.Combine(ps.m_strDCSInstallDirectory, this.m_strBadgeDatFilename);
					if (!File.Exists(this.m_strBadgeDatFilename))
						return false;
				}
				if (m_ANSIStyle)
					sr = new StreamReader(this.m_strBadgeDatFilename, System.Text.Encoding.Default);
				else
					sr = new StreamReader(this.m_strBadgeDatFilename);

				int split;
				while ((line = sr.ReadLine()) != null) 
				{
					if (line.StartsWith("["))
					{
						split = line.IndexOf("] ");
						if (split <= 0) continue;
						if (split > line.Length - 2) continue;
						lineparams1 = line.Substring(1, split-1);
						lineparams2 = line.Substring(split + 2);
					}
					else
					{
						split = line.IndexOf(" ");
						if (split <= 0) continue;
						if (split > line.Length - 2) continue;
						lineparams1 = line.Substring(0, split);
						lineparams2 = line.Substring(split + 1);
					}

					if (lineparams1 == "BadgeDesignName") m_strDesignName = lineparams2;
					else if (lineparams1 == "DocumentName") m_strDocumentName = lineparams2;
					else if (lineparams1 == "Portrait") m_strPortraitName = lineparams2;
					else if (lineparams1 == "Signature") m_strSignatureName = lineparams2;
					else if (lineparams1 == "Fingerprint") m_strFingerprintName = lineparams2;
					else
					{
						FieldInfo fi;
						// Ok. Not a keyword, must be a field value
						for (int i = 0; i < m_lFieldNamesAndValues.Count; ++i)
						{
							fi = (FieldInfo)m_lFieldNamesAndValues[i];
							if (fi.fieldName == lineparams1)
							{
								fi.FieldValue = lineparams2;	  
							}
						}
					}
				}
			}
			catch /*(Exception ex)*/
			{
				return false;
				//m_errorMessage = ex.Message;
			}
			finally
			{
				if (sr != null)
					sr.Close();
			}
			return true;
		}
	}
}
