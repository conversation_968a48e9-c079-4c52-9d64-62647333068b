using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;

using DCSDEV;

namespace DCSDEV
{
	/// <summary>
	/// Summary description for ScannerIF.
	/// </summary>
	public class ScannerIF
	{
        bool m_bScannerCanDoOffsets = false;  // some scanners ignore requests to scan with an offset so can not use the feature.
        bool m_bGetfromfile = false;    // syh 10-27-2011 set true to test without a device.

		public bool m_bScannerIsInitialized = false;
		public string m_strSelectedName = null;
		public string m_strSelectedID = null;
		private Dcs.Devices.Scanners.Scanner m_scanner = null;

		public ScannerIF()
		{
			//
			// TODO: Add constructor logic here
			//
		}

		// return device display name or empty string
		public bool SelectDevice()
		{
			Dcs.Devices.Scanners.Scanner.GetDeviceList();
			m_scanner = Dcs.Devices.Scanners.Scanner.ShowDeviceSelectionDialog();
			if (m_scanner == null || m_scanner.DeviceId == null)
			{
				m_bScannerIsInitialized = false;
				m_strSelectedName = string.Empty;
				m_strSelectedID = string.Empty;
				return false;
			}
			else
			{
				m_scanner = Dcs.Devices.Scanners.Scanner.ConnectToScanner(m_scanner.DeviceId);
				if (m_scanner != null)
				{
					m_strSelectedName = m_scanner.DisplayName;
					m_strSelectedID = m_scanner.DeviceId;
					m_bScannerIsInitialized = true;
					return true;
				}
				else
				{
					DCSDEV.DCSMsg.Show("Cannot connect to selected device: " + m_scanner.DisplayName);
					m_strSelectedName = string.Empty;
					m_strSelectedID = string.Empty;
					m_bScannerIsInitialized = false;
					return false;
				}
			}
		}

		public bool SetSelectedDevice(string strSelectedName, string strSelectedID)
		{
			m_strSelectedName = strSelectedName;
			m_strSelectedID = strSelectedID;

			m_scanner = Dcs.Devices.Scanners.Scanner.ConnectToScanner(m_strSelectedID);
			if (m_scanner != null)
			{
				m_bScannerIsInitialized = true;
				return true;
			}
			else
			{
				DCSDEV.DCSMsg.Show("Cannot connect to selected device: " + strSelectedName);
				m_strSelectedName = string.Empty;
				m_strSelectedID = string.Empty;
				m_bScannerIsInitialized = false;
				return false;
			}
		}

		// initialize scanner if not already initialized
		public bool InitializeScanner()
		{
            if (m_bGetfromfile)
            {
                m_bScannerIsInitialized = true;
                return true;
            }
            if (!m_bScannerIsInitialized)
			{
				if (m_strSelectedID == null)
				{
					m_scanner = Dcs.Devices.Scanners.Scanner.ShowDeviceSelectionDialog();
					if (m_scanner == null || m_scanner.DeviceId == null)
					{
						return false;
					}
					else
					{
						m_strSelectedName = m_scanner.DisplayName;
						m_strSelectedID = m_scanner.DeviceId;
					}
				}

				// so now scanner device is selected - try to connect
				m_scanner = Dcs.Devices.Scanners.Scanner.ConnectToScanner(m_strSelectedID);
				if (m_scanner != null)
				{
					m_bScannerIsInitialized = true;
					return true;
				}
				else
				{
					m_bScannerIsInitialized = false;
					return false;
				}
			}
			return true;
		}

		public Image ScanRect(Rectangle bounds, int dpi)
		{
            //MessageBox.Show("Scanning " + bounds.ToString() + " at " + dpi.ToString());
			//acquire image from scanner
            if (!m_bGetfromfile)
			{
				int nRetries = 0;
			RETRY_SCAN:
				nRetries++;
				try
				{
					float x = (float)bounds.X / 100.0F;
					float y = (float)bounds.Y / 100.0F;
					float width = (float)bounds.Width / 100.0F;
					float height = (float)bounds.Height / 100.0F;
					Bitmap bitmapScan = null;

                    if (!m_bScannerCanDoOffsets)
                    {
                        height = height + y;
                        width = width + x;
                        x = y = 0;
                    }

					//SCANNER INTERFACE V2
					//bitmapScan = (Bitmap)m_scanner.ParametricCapture(new Dcs.Devices.Scanners.CaptureRequest(dpi, x, y, width, height));
					//bitmapScan = (Bitmap)m_scanner.ParametricCapture(captureRequests);

					//SCANNER INTERFACE V3
					Dcs.Devices.Scanners.ImageFormat imageFormat = Dcs.Devices.Scanners.ImageFormat.Color24Bpp;
					Dcs.Devices.Scanners.CaptureRequest captureRequest = new Dcs.Devices.Scanners.CaptureRequest(dpi, x, y, width, height, imageFormat);
					Dcs.Devices.Scanners.CaptureRequest[] captureRequests = { captureRequest };
					Image[] bitmapScans;
					bitmapScans = m_scanner.ParametricCapture(captureRequests);
					if (bitmapScans.Length >= 1) bitmapScan = (Bitmap)bitmapScans[0];

                    if (!m_bScannerCanDoOffsets)
                    {
                        Rectangle rectCrop = bounds;
                        rectCrop = DCSMath.TimesDouble(rectCrop, (double)dpi / 100.0);
                        Image bitmapCropped = DCSImageProcessing.Trim(bitmapScan, rectCrop);
                        bitmapScan = (Bitmap)bitmapCropped;
                    }
					if (bitmapScan == null)
					{
						m_bScannerIsInitialized = false;
						DCSMsg.Show("ERROR: Scanner interface returns null.");
					}
					return bitmapScan;
				}
				catch (Dcs.Devices.DeviceBusyException ex)
				{
					if (nRetries < 10)
					{
						System.Threading.Thread.Sleep(1000);
						goto RETRY_SCAN;	// scanner not ready
					}
					DCSMsg.Show("ERROR from scanner DeviceBusyException", ex);
					return null;
				}
				// DeviceWarmingUpException
				catch (Dcs.Devices.DeviceWarmingUpException ex)
				{
					if (nRetries < 10)
					{
						System.Threading.Thread.Sleep(1000);
						goto RETRY_SCAN;	// scanner not ready
					}
					DCSMsg.Show("ERROR from scanner DeviceWarmingUpException", ex);
					return null;
				}


				catch (Dcs.Devices.DeviceException ex)
				{
					if (nRetries < 10 && ex.Message.IndexOf("80210006") >= 0)
					{
						System.Threading.Thread.Sleep(1000);
						goto RETRY_SCAN;	// scanner not ready
					}
					DCSMsg.Show("ERROR from scanner", ex);
					m_bScannerIsInitialized = false;
					return null;
				}
				catch (Exception ex)
				{
					DCSMsg.Show("ERROR from scanner - unspecified error", ex);
					m_bScannerIsInitialized = false;
					return null;
				}
			}
			else
			{
				// THIS CODE IS NOT USED WITH A LIVE SCANNER
				// IT WAS FOR TESTING WITH NO DEVICE
				// LATER IT MAY BE USEFUL FOR PROCESSING A BATCH OF SCANNED FORMS.
				DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("DCSSDK_Mgt"); 
				string strName = System.IO.Path.Combine(ps.m_strDCSInstallDirectory, "_Test_ScannerPreview.Bmp");
                // "_Test_ScannerPreview.Bmp" is at 100 dpi, size 600 x 750 pixels
                DCSMsg.Show("TEST MODE: Scanning " + bounds.ToString() + " at dpi " + dpi.ToString() + " from " + strName);
                if (!System.IO.File.Exists(strName))
				{
					DCSMsg.Show("Implement scanner file: " + strName);
					return null;
				}
				else
				{
					Bitmap bitmap = null;
					Bitmap bitmapScan;
					try
					{
						bitmap = new Bitmap(strName);
						// crop to specified size.
						//Rectangle rectCrop = DCSDEV.DCSMath.GetBiggestInnerRect(bounds.Size, new Rectangle(new Point(0,0), bitmap.Size));
						Rectangle rectCrop = DCSDEV.DCSMath.TimesDouble(bounds, 0.5); // source is at 50 dpi
						Rectangle boundsOut = DCSMath.TimesDouble(bounds, (double)dpi / 100.0);
						boundsOut.X = boundsOut.Y = 0;
						bitmapScan = new Bitmap(boundsOut.Width, boundsOut.Height);
						Graphics gr = Graphics.FromImage(bitmapScan);
						gr.Clear((Color.Gray));

						bitmapScan.SetResolution(bitmap.HorizontalResolution, bitmap.VerticalResolution);
						gr.DrawImage(bitmap, boundsOut, rectCrop, System.Drawing.GraphicsUnit.Pixel);
						bitmap.Dispose();
						gr.Dispose();
						return bitmapScan;
					}
					catch (Exception ex)
					{
						DCSMsg.Show("Implement scanner", ex);
						if (bitmap != null) bitmap.Dispose();
						return null;
					}
				}
			}
		}
	}
}
