<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="checkEnableTrack1.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="checkEnableTrack1.Location" type="System.Drawing.Point, System.Drawing">
    <value>118, 200</value>
  </data>
  <data name="checkEnableTrack1.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 24</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="checkEnableTrack1.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="checkEnableTrack1.Text" xml:space="preserve">
    <value>Enable track 1</value>
  </data>
  <data name="&gt;&gt;checkEnableTrack1.Name" xml:space="preserve">
    <value>checkEnableTrack1</value>
  </data>
  <data name="&gt;&gt;checkEnableTrack1.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkEnableTrack1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;checkEnableTrack1.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="checkEnableTrack2.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkEnableTrack2.Location" type="System.Drawing.Point, System.Drawing">
    <value>118, 232</value>
  </data>
  <data name="checkEnableTrack2.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 24</value>
  </data>
  <data name="checkEnableTrack2.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="checkEnableTrack2.Text" xml:space="preserve">
    <value>Enable track 2</value>
  </data>
  <data name="&gt;&gt;checkEnableTrack2.Name" xml:space="preserve">
    <value>checkEnableTrack2</value>
  </data>
  <data name="&gt;&gt;checkEnableTrack2.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkEnableTrack2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;checkEnableTrack2.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="checkEnableTrack3.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkEnableTrack3.Location" type="System.Drawing.Point, System.Drawing">
    <value>118, 264</value>
  </data>
  <data name="checkEnableTrack3.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 24</value>
  </data>
  <data name="checkEnableTrack3.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="checkEnableTrack3.Text" xml:space="preserve">
    <value>Enable track 3</value>
  </data>
  <data name="&gt;&gt;checkEnableTrack3.Name" xml:space="preserve">
    <value>checkEnableTrack3</value>
  </data>
  <data name="&gt;&gt;checkEnableTrack3.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkEnableTrack3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;checkEnableTrack3.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="label1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>267, 46</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>252, 24</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>Encoder device</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="comboBoxEncoderBrand.Items" xml:space="preserve">
    <value>Atlantec</value>
  </data>
  <data name="comboBoxEncoderBrand.Items1" xml:space="preserve">
    <value>Eltron-Zebra</value>
  </data>
  <data name="comboBoxEncoderBrand.Items2" xml:space="preserve">
    <value>Fargo</value>
  </data>
  <data name="comboBoxEncoderBrand.Items3" xml:space="preserve">
    <value>Nisca</value>
  </data>
  <data name="comboBoxEncoderBrand.Items4" xml:space="preserve">
    <value>XID</value>
  </data>
  <data name="comboBoxEncoderBrand.Items5" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="comboBoxEncoderBrand.Location" type="System.Drawing.Point, System.Drawing">
    <value>267, 70</value>
  </data>
  <data name="comboBoxEncoderBrand.Size" type="System.Drawing.Size, System.Drawing">
    <value>160, 21</value>
  </data>
  <data name="comboBoxEncoderBrand.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="comboBoxEncoderBrand.Text" xml:space="preserve">
    <value>Eltron-Zebra</value>
  </data>
  <data name="&gt;&gt;comboBoxEncoderBrand.Name" xml:space="preserve">
    <value>comboBoxEncoderBrand</value>
  </data>
  <data name="&gt;&gt;comboBoxEncoderBrand.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxEncoderBrand.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;comboBoxEncoderBrand.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="tbPrefixTrack1.Location" type="System.Drawing.Point, System.Drawing">
    <value>267, 200</value>
  </data>
  <data name="tbPrefixTrack1.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="tbPrefixTrack1.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="tbPrefixTrack1.Text" xml:space="preserve">
    <value>~1</value>
  </data>
  <data name="&gt;&gt;tbPrefixTrack1.Name" xml:space="preserve">
    <value>tbPrefixTrack1</value>
  </data>
  <data name="&gt;&gt;tbPrefixTrack1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbPrefixTrack1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbPrefixTrack1.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="tbSuffixTrack1.Location" type="System.Drawing.Point, System.Drawing">
    <value>339, 200</value>
  </data>
  <data name="tbSuffixTrack1.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="tbSuffixTrack1.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;tbSuffixTrack1.Name" xml:space="preserve">
    <value>tbSuffixTrack1</value>
  </data>
  <data name="&gt;&gt;tbSuffixTrack1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbSuffixTrack1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbSuffixTrack1.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="tbSuffixTrack2.Location" type="System.Drawing.Point, System.Drawing">
    <value>339, 232</value>
  </data>
  <data name="tbSuffixTrack2.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="tbSuffixTrack2.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;tbSuffixTrack2.Name" xml:space="preserve">
    <value>tbSuffixTrack2</value>
  </data>
  <data name="&gt;&gt;tbSuffixTrack2.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbSuffixTrack2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbSuffixTrack2.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="tbPrefixTrack2.Location" type="System.Drawing.Point, System.Drawing">
    <value>267, 232</value>
  </data>
  <data name="tbPrefixTrack2.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="tbPrefixTrack2.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="tbPrefixTrack2.Text" xml:space="preserve">
    <value>~2</value>
  </data>
  <data name="&gt;&gt;tbPrefixTrack2.Name" xml:space="preserve">
    <value>tbPrefixTrack2</value>
  </data>
  <data name="&gt;&gt;tbPrefixTrack2.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbPrefixTrack2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbPrefixTrack2.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="tbSuffixTrack3.Location" type="System.Drawing.Point, System.Drawing">
    <value>339, 264</value>
  </data>
  <data name="tbSuffixTrack3.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="tbSuffixTrack3.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="&gt;&gt;tbSuffixTrack3.Name" xml:space="preserve">
    <value>tbSuffixTrack3</value>
  </data>
  <data name="&gt;&gt;tbSuffixTrack3.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbSuffixTrack3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbSuffixTrack3.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="tbPrefixTrack3.Location" type="System.Drawing.Point, System.Drawing">
    <value>267, 264</value>
  </data>
  <data name="tbPrefixTrack3.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="tbPrefixTrack3.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="tbPrefixTrack3.Text" xml:space="preserve">
    <value>~3</value>
  </data>
  <data name="&gt;&gt;tbPrefixTrack3.Name" xml:space="preserve">
    <value>tbPrefixTrack3</value>
  </data>
  <data name="&gt;&gt;tbPrefixTrack3.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbPrefixTrack3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbPrefixTrack3.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>267, 176</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 16</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>prefix</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>339, 176</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 16</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>suffix</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="buttonCancel.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>437, 395</value>
  </data>
  <data name="buttonCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 24</value>
  </data>
  <data name="buttonCancel.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="buttonCancel.Text" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Name" xml:space="preserve">
    <value>buttonCancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonCancel.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="buttonAccept.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonAccept.Location" type="System.Drawing.Point, System.Drawing">
    <value>267, 395</value>
  </data>
  <data name="buttonAccept.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 24</value>
  </data>
  <data name="buttonAccept.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="buttonAccept.Text" xml:space="preserve">
    <value>Accept</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Name" xml:space="preserve">
    <value>buttonAccept</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAccept.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="buttonDefaults.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonDefaults.Location" type="System.Drawing.Point, System.Drawing">
    <value>267, 343</value>
  </data>
  <data name="buttonDefaults.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 24</value>
  </data>
  <data name="buttonDefaults.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="buttonDefaults.Text" xml:space="preserve">
    <value>Device defaults</value>
  </data>
  <data name="&gt;&gt;buttonDefaults.Name" xml:space="preserve">
    <value>buttonDefaults</value>
  </data>
  <data name="&gt;&gt;buttonDefaults.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonDefaults.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonDefaults.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="labelTk1Comment.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelTk1Comment.Location" type="System.Drawing.Point, System.Drawing">
    <value>401, 201</value>
  </data>
  <data name="labelTk1Comment.Size" type="System.Drawing.Size, System.Drawing">
    <value>225, 24</value>
  </data>
  <data name="labelTk1Comment.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="labelTk1Comment.Text" xml:space="preserve">
    <value>76 alphanumeric chars; delimiter ^</value>
  </data>
  <data name="&gt;&gt;labelTk1Comment.Name" xml:space="preserve">
    <value>labelTk1Comment</value>
  </data>
  <data name="&gt;&gt;labelTk1Comment.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelTk1Comment.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelTk1Comment.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="labelTk2Comment.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelTk2Comment.Location" type="System.Drawing.Point, System.Drawing">
    <value>401, 232</value>
  </data>
  <data name="labelTk2Comment.Size" type="System.Drawing.Size, System.Drawing">
    <value>225, 24</value>
  </data>
  <data name="labelTk2Comment.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="labelTk2Comment.Text" xml:space="preserve">
    <value>37 numeric chars; delimiter is =</value>
  </data>
  <data name="&gt;&gt;labelTk2Comment.Name" xml:space="preserve">
    <value>labelTk2Comment</value>
  </data>
  <data name="&gt;&gt;labelTk2Comment.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelTk2Comment.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelTk2Comment.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="labelTk3Comment.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelTk3Comment.Location" type="System.Drawing.Point, System.Drawing">
    <value>401, 264</value>
  </data>
  <data name="labelTk3Comment.Size" type="System.Drawing.Size, System.Drawing">
    <value>225, 20</value>
  </data>
  <data name="labelTk3Comment.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="labelTk3Comment.Text" xml:space="preserve">
    <value>104 numeric; delimiter is =</value>
  </data>
  <data name="&gt;&gt;labelTk3Comment.Name" xml:space="preserve">
    <value>labelTk3Comment</value>
  </data>
  <data name="&gt;&gt;labelTk3Comment.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelTk3Comment.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelTk3Comment.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="labelTk3CommentAAMVA.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelTk3CommentAAMVA.Location" type="System.Drawing.Point, System.Drawing">
    <value>401, 284</value>
  </data>
  <data name="labelTk3CommentAAMVA.Size" type="System.Drawing.Size, System.Drawing">
    <value>225, 17</value>
  </data>
  <data name="labelTk3CommentAAMVA.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="labelTk3CommentAAMVA.Text" xml:space="preserve">
    <value>  (or 76 alphanumeric AAMVA)</value>
  </data>
  <data name="&gt;&gt;labelTk3CommentAAMVA.Name" xml:space="preserve">
    <value>labelTk3CommentAAMVA</value>
  </data>
  <data name="&gt;&gt;labelTk3CommentAAMVA.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelTk3CommentAAMVA.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelTk3CommentAAMVA.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="checkEnableAAMVA.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkEnableAAMVA.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkEnableAAMVA.Location" type="System.Drawing.Point, System.Drawing">
    <value>118, 294</value>
  </data>
  <data name="checkEnableAAMVA.Size" type="System.Drawing.Size, System.Drawing">
    <value>277, 24</value>
  </data>
  <data name="checkEnableAAMVA.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="checkEnableAAMVA.Text" xml:space="preserve">
    <value>Enable AAMVA encoding of Track 3</value>
  </data>
  <data name="&gt;&gt;checkEnableAAMVA.Name" xml:space="preserve">
    <value>checkEnableAAMVA</value>
  </data>
  <data name="&gt;&gt;checkEnableAAMVA.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkEnableAAMVA.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;checkEnableAAMVA.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleBaseSize" type="System.Drawing.Size, System.Drawing">
    <value>5, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>632, 446</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterParent</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Magnetic Stripe Encoder Properties</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>EncoderProperties</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>