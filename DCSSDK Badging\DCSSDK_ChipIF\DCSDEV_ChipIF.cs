using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Runtime.InteropServices;
using System.IO;

using DCSDEV;
using DCSDEV.DCSDesign;

namespace DCSDEV.DCSChipIF
{
	public class DCSDEV_ChipIF
	{
		// program constants
		private const string CONST_MIFARE_CHIP_CONFIG = "ChipConfig.Ini";
		private const string CONST_EDL_CHIP_CONFIG = "ChipConfig_EDL.Ini";

		private const string CONST_ICAO_ENCODE_PROGRAM = "EncodeICAOContactChip.Bat";
		private const string CONST_ICAO_READ_PROGRAM = "ReadICAOContactChip.Bat";
		private const string CONST_ICAO_CHIP_STATUS_FILE = "contactchip_status.txt";
		private const string CONST_ICAO_CHIP_CONFIG = "contactchip.cfg";
		private const string CONST_ICAO_READ_DIRECTORY_FILE = "_ChipRead.dat";
		private const string CONST_ICAO_WRITE_DIRECTORY_FILE = "_ChipWrite.dat";
		private const string CONST_ICAO_TEXT_FIELD = "1 ";
		private const string CONST_ICAO_PORTRAIT_FIELD = "3 ";
		private const string CONST_ICAO_FINGER1_FIELD = "4 ";
		private const string CONST_ICAO_FINGER2_FIELD = "5 ";
		private const string CONST_ICAO_TEXT_FILE_READ = "Chip_ICAOMRZ.txt";
		private const string CONST_ICAO_PORTRAIT_FILE_READ = "Chip_Portrait.jpg";
		public const string CONST_ICAO_FINGER1_FILE_READ = "Chip_Finger1.wsq";
		public const string CONST_ICAO_FINGER2_FILE_READ = "Chip_Finger2.wsq";

		private const string CONST_MULTI_CHIP_ENCODE_PROGRAM = "EncodeMultiContactChip.Bat";
		private const string CONST_MULTI_CHIP_READ_PROGRAM = "ReadMultiContactChip.Bat";

		// linked in DLLs to support chip IF type = MIFARE_RFID_CHIP
		// Other chip IF types are supported by calls to EXEs
		[DllImport("Chip.dll")]
		private static extern int DCSCHIP_Init(string strIniName);

		[DllImport("Chip.dll")]
		private static extern string DCSCHIP_GetChipID();

		[DllImport("Chip.dll")]
		private static extern string DCSCHIP_GetErrorMessage(int iError);

		[DllImport("Chip.dll")]
		private static extern int DCSCHIP_File2Chip(string strFilename);

		[DllImport("Chip.dll")]
		private static extern int DCSCHIP_Chip2File(string strFilename);

		public enum ChipIFType { MIFARE_RFID_CHIP_FORMULA, MIFARE_RFID_CHIP_FILE, ICAO_CONTACT_CHIP, MULTIFILE_CONTACT_CHIP, EDL_RFID_CHIP_FILE }; // MULTIFILE_CONTACT_CHIP not implemented
        private ChipIFType m_eChipIFType;
		private SmartChipIO.ChipIOMode m_eChipIOMode;
		private string m_strDataFile;
		
		private string m_strICAODirectoryFile;
		private string m_strICAOTextFile;
		private string m_strICAOPortraitFile;
		private string m_strICAOFinger1File;
		private string m_strICAOFinger2File;

		private bool m_bDataIsInited = false;
		private bool m_bDeviceIsInited = false;
		private DCS_EDLChip m_EDLChipIF;
		private int m_iChipError = 0;	// -1 general error; 0=OK; -2=data error
		private string m_strErrorStatus = null;
		private DCSDEV.ParameterStore m_ps;
		private string m_ConfigFilename;
		private bool m_bStandAloneEncoder;

		public DCSDEV_ChipIF(ChipIFType eChipIFType, DCSDEV.SmartChipIO.ChipIOMode eChipIOMode, string strDataFile, bool bStandAloneEncoder)
		{
			m_eChipIFType = eChipIFType;
			m_eChipIOMode = eChipIOMode;
			m_strDataFile = strDataFile;
			m_bStandAloneEncoder = bStandAloneEncoder;

			m_ps = new ParameterStore("DCSSDK_Mgt");
			m_bDataIsInited = this.DCSDEV_InitData();
			this.DCSDEV_ReInitDevice();
		}

        private void ReportChipIFTypeError(string strProgram)
        {
            DCSMsg.Show(String.Format("ERROR: Chip IF type '{0}' is unrecognized in {1}", m_eChipIFType.ToString(), strProgram));
        }

        // Called for m_eChipIFType == MIFARE_RFID_CHIP_FORMULA or MIFARE_RFID_CHIP_FILE
		public bool Chip2File(out string strChipID_out)
		{
			m_iChipError = -2;
			strChipID_out = null;
            if (m_eChipIFType != ChipIFType.MIFARE_RFID_CHIP_FORMULA && m_eChipIFType != ChipIFType.MIFARE_RFID_CHIP_FILE)
            {
                this.ReportChipIFTypeError("Chip2File");
                return false;
            }

			// these two lines seem redundent with the status checking in SmartChipIO
            if (!m_bDeviceIsInited) DCSDEV_ReInitDevice();
            if (!m_bDeviceIsInited) return false;

            string strGetChipID = DCSCHIP_GetChipID();
            if (strGetChipID == "") return false;

			m_iChipError = DCSCHIP_Chip2File((m_strDataFile == null) ? "" : m_strDataFile);
            if (m_iChipError == 0)
            {
                strChipID_out = strGetChipID;
                return true;
            }
            else return false;
		}
		public bool EDLChip2File(out string strChipID_out)
		{
			m_iChipError = -2;
			strChipID_out = null;
			if (m_eChipIFType != ChipIFType.EDL_RFID_CHIP_FILE)
			{
				this.ReportChipIFTypeError("EDLChip2File");
				return false;
			}

			// these two lines seem redundent with the status checking in SmartChipIO
			if (!m_bDeviceIsInited) DCSDEV_ReInitDevice();
			if (!m_bDeviceIsInited) return false;
			
			string strGetChipID = "error";
			uint uNumChips = 0;
			m_iChipError = (int)m_EDLChipIF.CheckChipCount(out uNumChips);	// return error if any number but one card
			if (m_iChipError == 0)
			{
				m_iChipError = (int)m_EDLChipIF.GetChipTID(out strGetChipID);
				if (m_iChipError == 0)
				{
					if (m_strDataFile != null)
					{
						m_iChipError = (int)m_EDLChipIF.Chip2File(m_strDataFile);
					}
				}
			}

			if (m_iChipError == 0)
			{
				strChipID_out = strGetChipID;
				return true;
			}
			else
			{
				return false;
			}
		}

        // Called for m_eChipIFType == ChipIFType.ICAO_CONTACT_CHIP
        public bool Chip2ICAOFile(out string strChipID_out)
		{
			m_iChipError = -2;
			strChipID_out = null;

            if (m_eChipIFType != ChipIFType.ICAO_CONTACT_CHIP)
            {
				this.ReportChipIFTypeError("Chip2ICAOFile");
                return false;
            }
			// call to read ICAO data from chip");
			string strChipID;
			bool bRet = CallICAOProgram(true, m_strICAODirectoryFile, out strChipID);	// true indicates reading
			if (strChipID == null)
				strChipID_out = "err";
			else
				strChipID_out = strChipID;
			return bRet;
		}

        // Called for m_eChipIFType == ChipIFType.MULTIFILE_CONTACT_CHIP
        public bool Chip2MultiFile(out string strChipID_out)
		{
			m_iChipError = -2;
			strChipID_out = null;
            if (m_eChipIFType != ChipIFType.MULTIFILE_CONTACT_CHIP)
            {
				this.ReportChipIFTypeError("Chip2MultiFile");
                return false;
            }

			StreamReader sr = null;
			String line;
			char[] separator = new char[] { ' ' };
			string strFieldNo, strFieldFilename;
			int iFieldNo;

			try
			{
				if (!File.Exists(m_strDataFile)) return false;

				strChipID_out = null;

				// these two lines seem redundent with the status checking in SmartChipIO
				if (!m_bDeviceIsInited) DCSDEV_ReInitDevice();
				if (!m_bDeviceIsInited) return false;

				m_iChipError = 0;
				sr = new StreamReader(m_strDataFile);
				while ((line = sr.ReadLine()) != null)
				{
					int split = line.IndexOf(" ");
					if (split <= 0) continue;
					if (split > line.Length - 2) continue;

					strFieldNo = line.Substring(0, split);
					strFieldFilename = line.Substring(split + 1);
					try
					{
						iFieldNo = Convert.ToInt32(strFieldNo);
					}
					catch
					{
						continue;
					}
					if (iFieldNo == 0) continue;

                    // call to contact chip MULTI FILE reading");
                    bool bRet = CallMultiProgram(true, strFieldNo, strFieldFilename);	// true indicates reading
					if (!bRet)
					{
						m_iChipError = -1;
					}
				}
			}
			catch //(Exception ex)
			{
				return false;
			}
			finally
			{
				if (sr != null)
					sr.Close();
			}
			return (m_iChipError == 0);
		}

        // Called for m_eChipIFType == ChipIFType.MIFARE_RFID_CHIP_FORMULA or ChipIFType.MIFARE_RFID_CHIP_FILE
        // Always write a single file. If ChipIFType.MIFARE_RFID_CHIP_FORMULA caller evaluates formula into a temp file
		public bool File2Chip(out string strChipID_out)
		{
			strChipID_out = null;
			m_iChipError = -2;

			if (m_eChipIFType != ChipIFType.MIFARE_RFID_CHIP_FORMULA && m_eChipIFType != ChipIFType.MIFARE_RFID_CHIP_FILE)
			{
				this.ReportChipIFTypeError("File2Chip");
                return false;
            }

			// these two lines seem redundent with the status checking in SmartChipIO
			if (!m_bDeviceIsInited) DCSDEV_ReInitDevice();
            if (!m_bDeviceIsInited) return false;
            
			string strGetChipID = DCSCHIP_GetChipID();
            if (strGetChipID == "") return false;

            m_iChipError = DCSCHIP_File2Chip((m_strDataFile == null) ? "" : m_strDataFile);
            if (m_iChipError == 0)
            {
                strChipID_out = strGetChipID;
                return true;
            }
            return false;
		}
		// Called for m_eChipIFType == ChipIFType.EDL_RFID_CHIP_FILE
		public bool File2EDLChip(out string strChipID_out)
		{
			strChipID_out = null;
			m_iChipError = -2;

			if (m_eChipIFType != ChipIFType.EDL_RFID_CHIP_FILE)
			{
				this.ReportChipIFTypeError("File2EDLChip");
				return false;
			}

			// these two lines seem redundent with the status checking in SmartChipIO
			if (!m_bDeviceIsInited) DCSDEV_ReInitDevice();
			if (!m_bDeviceIsInited) return false;

			string strGetChipID = "error";
			uint uNumChips = 0;
			m_iChipError = (int)m_EDLChipIF.CheckChipCount(out uNumChips);	// return error if any number but one card
			if (m_iChipError == 0)
			{
				m_iChipError = (int)m_EDLChipIF.GetChipTID(out strGetChipID);
				if (m_iChipError == 0 && m_strDataFile != null)
				{
					bool bLockChipAfterWrite = m_ps.GetBoolParameter("LockChipAfterWrite", true);
					m_iChipError = (int)m_EDLChipIF.File2Chip(m_strDataFile, bLockChipAfterWrite);
				}
				if (m_iChipError == 0)
				{
					strChipID_out = strGetChipID;
					return true;
				}
			}
			return false;
		}

        // Called for m_eChipIFType == ChipIFType.MULTIFILE_CONTACT_CHIP
        // gather up and format all data for writing to a MaskTech ICAO format contact chip (ICAO_CONTACT_CHIP)
		public bool ICAOFile2Chip(out string strChipID_out)
		{
			m_iChipError = -2;
			strChipID_out = null;
            if (m_eChipIFType != ChipIFType.ICAO_CONTACT_CHIP)
            {
				this.ReportChipIFTypeError("ICAOFile2Chip");
                return false;
            }

			// call write ICAO encoding to chip");
			string strChipID;
			bool bRet = CallICAOProgram(false, m_strICAODirectoryFile, out strChipID);		// false indicates writing
			if (!bRet || strChipID == null)
			{
				strChipID_out = "err";
				m_iChipError = -1;
			}
			else
			{
				strChipID_out = strChipID;
				m_iChipError = 0;
			}
			return (m_iChipError == 0);
		}

        // Called for m_eChipIFType == ChipIFType.MULTIFILE_CONTACT_CHIP
        // low level interface to chip is not available
		public bool MultiFile2Chip(out string strChipID_out)
		{
			m_iChipError = -2;
			strChipID_out = null;

            if (m_eChipIFType != ChipIFType.MULTIFILE_CONTACT_CHIP)
            {
				this.ReportChipIFTypeError("MultiFileChip");
                return false;
            }
            
            StreamReader sr = null;
			String line;
			int nFilesTried = 0;
			int nFilesEncoded = 0;
		
			char [] separator = new char []{' '};
			string  strFieldNo, strFieldFilename;
			int iFieldNo;

			try
			{
				if (!File.Exists(m_strDataFile)) return false;

				// these two lines seem redundent with the status checking in SmartChipIO
				if (!m_bDeviceIsInited) DCSDEV_ReInitDevice();
				if (!m_bDeviceIsInited) return false;
				
				sr = new StreamReader(m_strDataFile);
				m_iChipError = 0;
				while ((line = sr.ReadLine()) != null) 
				{
					int split = line.IndexOf(" ");
					if (split <= 0) continue;
					if (split > line.Length-2) continue;
				
					strFieldNo = line.Substring(0,split);
					strFieldFilename = line.Substring(split+1);
					try
					{
						iFieldNo = Convert.ToInt32(strFieldNo);
					}
					catch
					{
						continue;
					}
					if (iFieldNo == 0) continue;
					if (!File.Exists(strFieldFilename))
					{
						DCSMsg.Show(String.Format("Multi file '{0}' does not exist.", strFieldFilename));
						continue;
					}

                    // call write one MULTI file to chip");
					nFilesTried++;
                    bool bRet = CallMultiProgram(false, strFieldNo, strFieldFilename);		// false indicates writing
					if (bRet)
					{
						nFilesEncoded++;
					}
					else
					{
						m_iChipError = -1;
					}
				}
			}
			catch /*(Exception ex)*/
			{
				return false;
				//m_errorMessage = ex.Message;
			}
			finally
			{
				if (sr != null)
					sr.Close();
			}
			if (nFilesTried == 0)
			{
				DCSMsg.Show(String.Format("Directory file '{0}' contained no files for encoding.", m_strDataFile));
				m_iChipError = -2;
			}
			else if (nFilesEncoded == 0)
			{
				DCSMsg.Show(String.Format("0 of {0} files in directory '{1}' were encoded.", nFilesTried, m_strDataFile));
			}
			return (m_iChipError==0);
		}

		private bool DCSDEV_InitData()
		{
			switch (m_eChipIFType)
			{
				case ChipIFType.MIFARE_RFID_CHIP_FORMULA:
				case ChipIFType.MIFARE_RFID_CHIP_FILE:
					m_ConfigFilename = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, CONST_MIFARE_CHIP_CONFIG);
					break;
				case ChipIFType.EDL_RFID_CHIP_FILE:
					m_ConfigFilename = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, CONST_EDL_CHIP_CONFIG);
					break;
				case ChipIFType.ICAO_CONTACT_CHIP:
					if (m_eChipIOMode == SmartChipIO.ChipIOMode.ENCODE)
					{
						string strDesignName = "error";
						try
						{
							return InitICAO_Writing(out strDesignName);
						}
						catch (Exception ex)
						{
							DCSMsg.Show(String.Format
								("Cannot initialize process for ICAO encoding. \nDocument design = '{0}' \nBadge.Dat = '{1}'",
								strDesignName, m_strDataFile), ex);
							return false;
						}
					}
					else
					{
						try
						{
							return InitICAO_Reading();
						}
						catch (Exception ex)
						{
							DCSMsg.Show("Cannot initialize process for ICAO reading.", ex);
							return false;
						}
					}
				case ChipIFType.MULTIFILE_CONTACT_CHIP:
					if (!System.IO.File.Exists(m_strDataFile))
					{
						DCSDEV.DCSMsg.Show(String.Format("The directory file '{0}' does not exist.", m_strDataFile));
						return false;
					}
					break;
			}
			return true;
		}

		public void DCSDEV_ReInitDevice()
		{
			m_bDeviceIsInited = false;
			switch (m_eChipIFType)
			{
				case ChipIFType.MIFARE_RFID_CHIP_FILE:
				case ChipIFType.MIFARE_RFID_CHIP_FORMULA:
					int iErr = DCSCHIP_Init(m_ConfigFilename);
					m_bDeviceIsInited = (iErr == 0);
					break;
				case ChipIFType.ICAO_CONTACT_CHIP:
				case ChipIFType.MULTIFILE_CONTACT_CHIP:
					// there is no initialization for chip IF types that call EXEs
					m_bDeviceIsInited = true;
					break;
				case ChipIFType.EDL_RFID_CHIP_FILE:
					if (m_EDLChipIF != null)
						m_EDLChipIF.CloseDevice();
					else
					{
						string strPort;
						if (m_bStandAloneEncoder)
							strPort = m_ps.GetStringParameter("ChipEncoderPort", "COM4");
						else
							strPort = m_ps.GetStringParameter("PrinterChipEncoderPort", "COM5");
						m_EDLChipIF = new DCS_EDLChip(strPort, 9600);
					}

					m_iChipError = (int)this.m_EDLChipIF.InitDevice();

					m_bDeviceIsInited = (m_iChipError == 0);
					break;
			}
			return;
		}

		// INITIALIZATION for ICAOFile2Chip //
		private bool InitICAO_Writing(out string strDesignName)
		{
			strDesignName = "Error";

			///////////////////////////////////////////////////
			// generate text file containing ICAO MRZ lines  //
			///////////////////////////////////////////////////
			string strICAO = null;
			DCSDEV.DCSDesign.DCSDesign design = new DCSDEV.DCSDesign.DCSDesign();
			// load badge design and merge with badge design
			if (!design.LoadBadgeDesignAndData(m_strDataFile))
			{
				// DCSMsg.Show(String.Format("Cannot read document design '{0}'.", design.m_strDesignName));
				design.Dispose();
				return false;
			}
			strDesignName = design.m_strDesignName;

			// find ICAO object
			bool bFound = false;
			DCSDEV.DCSDesign.DCSDesignSide designSide;
			for (int iSide = 0; iSide < design.m_designSides.Count; iSide++)
			{
				designSide = (DCSDEV.DCSDesign.DCSDesignSide)design.m_designSides[iSide];
				foreach (DCSDEV.DCSDesign.DCSDesignObject designObject in designSide.m_DCSDesignObjects)
				{
					if (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ICAOMRZ)
					{
						// Build ICAO strings
						DCSDEV.DCSDesign.DCSIcaoBuilder icaoBuilder = new DCSDEV.DCSDesign.DCSIcaoBuilder(designObject);
						icaoBuilder.BuildMRZ();
						strICAO = icaoBuilder.ICAO_Line1 + "\r\n" + icaoBuilder.ICAO_Line2;
						if (icaoBuilder.ICAO_Line3.Length > 0)
							strICAO = strICAO + "\r\n" + icaoBuilder.ICAO_Line3;
						else
						{
							DCSMsg.Show(String.Format("ICAO MRZ must have 3 line to work with this implementation of chip encoding.  Check document design '{0}'", design.m_strDesignName));
							design.Dispose();
							return false;
						}
						bFound = true;
						break;
					}
				}
				if (bFound) break;
			}
			if (!bFound)
			{
				DCSMsg.Show(String.Format("Cannot find an ICAO MRZ object in document design '{0}' referenced in badge data file '{1}'", design.m_strDesignName, m_strDataFile));
				design.Dispose();
				return false;
			}
			// write the data to encode on the chip temporarily to a file
			m_strICAOTextFile = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, "_ChipICAO.dat");
			using (System.IO.StreamWriter stream = new System.IO.StreamWriter(m_strICAOTextFile))
			{
				stream.Write(strICAO);
				stream.Close();
			}

			// get path to portrait file
			string strImageID = design.m_BadgeData.m_strPortraitName;

			string strTempFile = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, "_TempICAOPhoto" + ".JPG");
			bool bRet = DCSDatabaseIF.GetStoredImageFile(strTempFile, strImageID, DCSDatabaseIF.ImageClass.Portrait, 0);
			if (bRet) m_strICAOPortraitFile = strTempFile;
			else m_strICAOPortraitFile = null;

			if (m_strICAOPortraitFile == null)
			{
				DCSDEV.DCSMsg.Show(String.Format("Portrait with ID '{0}' does not exist.", strImageID));
			}
			else
			{
				// reduce resolution of portrait file so it can be compressed by DCSE MaskTech encoder program.
				try
				{
					LEAD.Drawing.Image leadImage = LEAD.Drawing.Image.FromFile(m_strICAOPortraitFile);
					System.Drawing.Size size = leadImage.Size;
					if (size.Width > 300)
					{
						// resample when necessary
						LEAD.Drawing.Imaging.ImageProcessing.ImageProcessing leadImageProcessing = new LEAD.Drawing.Imaging.ImageProcessing.ImageProcessing();
						LEAD.Drawing.Imaging.Codecs.Codecs codecs = new LEAD.Drawing.Imaging.Codecs.Codecs();
						double ratio = 200.0 / (double)size.Width;
						size = DCSMath.TimesDouble(size, ratio);
						leadImageProcessing.Size(leadImage, size, LEAD.Drawing.Imaging.ImageProcessing.ImageProcessing.ResizeConstants.Resample);
						m_strICAOPortraitFile = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, "_TempICAOPhotoReduced.jpg");
						codecs.SetSaveQFactor(LEAD.Drawing.Imaging.Codecs.Codecs.QFactorConstants.Value, 80); // 0 or 2-255 255=most compression
						codecs.Save(leadImage, m_strICAOPortraitFile,
							LEAD.Drawing.Imaging.ImageFormat.Jfif,
							LEAD.Drawing.Imaging.PixelFormat.Format24bppRgb,
							LEAD.Drawing.Imaging.Codecs.Codecs.SaveModifyConstants.Overwrite);
					}
					leadImage.Dispose();
					if (strTempFile != m_strICAOPortraitFile) System.IO.File.Delete(strTempFile);
				}
				catch (Exception ex)
				{
					DCSMsg.Show("ERROR: preparing portrait image for encoding.", ex);
					m_strICAOPortraitFile = null;
				}
			}

			// get path to finger file
			string strFingerID = design.m_BadgeData.m_strFingerprintName;
			ArrayList array = DCSDatabaseIF.GetOrderedFingerIndices(strFingerID);
			if (array.Count == 0)
			{
				m_strICAOFinger1File = null;
				m_strICAOFinger2File = null;
				DCSDEV.DCSMsg.Show(String.Format("Fingerprint image with ID {0} does not exist.", strFingerID));
				design.Dispose();
				return false;
			}
			else if (array.Count == 1)
			{
				m_strICAOFinger1File = DCSDatabaseIF.GetFullnameOfImage(strFingerID, DCSDatabaseIF.ImageClass.Fingerprint, (int)array[0], true);
				m_strICAOFinger2File = null;
			}
			else
			{
				m_strICAOFinger1File = DCSDatabaseIF.GetFullnameOfImage(strFingerID, DCSDatabaseIF.ImageClass.Fingerprint, (int)array[0], true);
				m_strICAOFinger2File = DCSDatabaseIF.GetFullnameOfImage(strFingerID, DCSDatabaseIF.ImageClass.Fingerprint, (int)array[1], true);
			}

			// generate directory file for ICAO contact chip encoder
			m_strICAODirectoryFile = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, CONST_ICAO_WRITE_DIRECTORY_FILE);
			using (System.IO.StreamWriter stream = new System.IO.StreamWriter(m_strICAODirectoryFile))
			{
				string str = CONST_ICAO_TEXT_FIELD + m_strICAOTextFile + "\r\n";
				if (m_strICAOPortraitFile != null) str += CONST_ICAO_PORTRAIT_FIELD + m_strICAOPortraitFile + "\r\n";
				if (m_strICAOFinger1File != null) str += CONST_ICAO_FINGER1_FIELD + m_strICAOFinger1File + "\r\n";
				if (m_strICAOFinger2File != null) str += CONST_ICAO_FINGER2_FIELD + m_strICAOFinger2File + "\r\n";
				stream.Write(str);
				stream.Close();
			}
			design.Dispose();
			return true;
		}

		private bool InitICAO_Reading()
		{
			// Directory file
			bool bMakeDirectoryFile;
			if (m_strDataFile == null || m_strDataFile == "")
			{
				m_strICAODirectoryFile = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, CONST_ICAO_READ_DIRECTORY_FILE);
				bMakeDirectoryFile = true;
			}
			else if (!System.IO.File.Exists(m_strDataFile))
			{
				m_strICAODirectoryFile = m_strDataFile;
				bMakeDirectoryFile = true;
			}
			else
			{
				m_strICAODirectoryFile = m_strDataFile;
				if (!System.IO.File.Exists(m_strICAODirectoryFile))
				{
					DCSDEV.DCSMsg.Show(String.Format("The ICAO directory file '{0}' does not exist.", m_strICAODirectoryFile));
					return false;
				}
				bMakeDirectoryFile = false;
			}

			if (bMakeDirectoryFile)
			{
				m_strICAOTextFile = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, CONST_ICAO_TEXT_FILE_READ);
				m_strICAOPortraitFile = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, CONST_ICAO_PORTRAIT_FILE_READ);
				m_strICAOFinger1File = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, CONST_ICAO_FINGER1_FILE_READ);
				m_strICAOFinger2File = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, CONST_ICAO_FINGER2_FILE_READ);

				if (System.IO.File.Exists(m_strICAOTextFile)) System.IO.File.Delete(m_strICAOTextFile);
				if (System.IO.File.Exists(m_strICAOPortraitFile)) System.IO.File.Delete(m_strICAOPortraitFile);
				if (System.IO.File.Exists(m_strICAOFinger1File)) System.IO.File.Delete(m_strICAOFinger1File);
				if (System.IO.File.Exists(m_strICAOFinger2File)) System.IO.File.Delete(m_strICAOFinger2File);

				using (System.IO.StreamWriter stream = new System.IO.StreamWriter(m_strICAODirectoryFile))
				{
					string str = CONST_ICAO_TEXT_FIELD + m_strICAOTextFile + "\r\n";
					if (m_strICAOPortraitFile != null) str += CONST_ICAO_PORTRAIT_FIELD + m_strICAOPortraitFile + "\r\n";
					if (m_strICAOFinger1File != null) str += CONST_ICAO_FINGER1_FIELD + m_strICAOFinger1File + "\r\n";
					if (m_strICAOFinger2File != null) str += CONST_ICAO_FINGER2_FIELD + m_strICAOFinger2File + "\r\n";
					stream.Write(str);
					stream.Close();
				}
			}
			else
			{
				// remove target data files
				StreamReader sr = null;
				String line;

				char[] separator = new char[] { ' ' };
				string strFieldNo, strFieldFilename;
				int iFieldNo;

				try
				{
					sr = new StreamReader(m_strDataFile);
					while ((line = sr.ReadLine()) != null)
					{
						int split = line.IndexOf(" ");
						if (split <= 0) continue;
						if (split > line.Length - 2) continue;

						strFieldNo = line.Substring(0, split);
						strFieldFilename = line.Substring(split + 1);
						try
						{
							iFieldNo = Convert.ToInt32(strFieldNo);
						}
						catch
						{
							iFieldNo = 0;
							continue;
						}
						if (iFieldNo == 0) continue;
						if (iFieldNo == 1) m_strICAOTextFile = strFieldFilename;
						else if (iFieldNo == 3) m_strICAOPortraitFile = strFieldFilename;
						else if (iFieldNo == 4) m_strICAOFinger1File = strFieldFilename;
						else if (iFieldNo == 5) m_strICAOFinger2File = strFieldFilename;
					}
				}
				catch /*(Exception ex)*/
				{
					return false;
					//m_errorMessage = ex.Message;
				}
				finally
				{
					if (sr != null)
						sr.Close();
				}
			}

			return true;
		}

		/// <]summary>
		/// 
		/// </summary>
		/// <param name="port"></param>
		/// <param name="strSourceFileName"></param>
		/// <returns>0 if cancel; pos if OK; neg if error</returns>
		private int WriteDataFromFile(string port, string strSourceFileName)
		{
			System.Windows.Forms.OpenFileDialog openFileDialog1;
			openFileDialog1 = new System.Windows.Forms.OpenFileDialog();
			openFileDialog1.FileName = "";
			openFileDialog1.InitialDirectory = "";
			openFileDialog1.CheckFileExists = false;
			openFileDialog1.Filter = "Text Files(*.txt)|*.txt";
			openFileDialog1.Title = "Select stand-in file for the chip";

			if (openFileDialog1.ShowDialog() != DialogResult.OK) return 0;

			try { System.IO.File.Delete(openFileDialog1.FileName); }
			catch { ; }
			System.IO.File.Copy(strSourceFileName, openFileDialog1.FileName);
			return 1;
		}

		// test program to standin for the control by DCSE
		private string ReadDataIntoFile(string port, string strDestFileName)
		{
			System.Windows.Forms.OpenFileDialog openFileDialog1;
			openFileDialog1 = new System.Windows.Forms.OpenFileDialog();
			openFileDialog1.FileName = "";
			openFileDialog1.InitialDirectory = "";
			openFileDialog1.Filter = "Text Files(*.txt)|*.txt|All files (*.*)|*.*";
			openFileDialog1.Title = "Select standin file for the chip";

			if (openFileDialog1.ShowDialog() != DialogResult.OK) return "";

			try { System.IO.File.Delete(strDestFileName); }
			catch { ; }
			System.IO.File.Copy(openFileDialog1.FileName, strDestFileName);
			return "sn#001Test";
		}

		private bool CallICAOProgram(bool bIfReading, string strFileName, out string strChipID)
		{
			string strProgName;
			m_strErrorStatus = null;
			strChipID = null;

			if (bIfReading)
				strProgName = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, CONST_ICAO_READ_PROGRAM);
			else
				strProgName = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, CONST_ICAO_ENCODE_PROGRAM);

			if (!System.IO.File.Exists(strProgName))
			{
				m_strErrorStatus = String.Format("ERROR: program {0} does not exist.", strProgName);
				DCSMsg.Show(m_strErrorStatus);
				return false;
			}
			string strStatusFile = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, CONST_ICAO_CHIP_STATUS_FILE);
			if (System.IO.File.Exists(strStatusFile)) System.IO.File.Delete(strStatusFile);
			try
			{
				System.Diagnostics.Process proc = new System.Diagnostics.Process();
				proc.StartInfo.FileName = strProgName;
				proc.StartInfo.Arguments = " \"" + strFileName + "\" \"" + m_ps.m_strDCSInstallDirectory + "\"";
				proc.StartInfo.WindowStyle = System.Diagnostics.ProcessWindowStyle.Minimized;
				proc.Start();
				proc.WaitForExit(); // wait to finish
				//SYH find out if there was success.
				proc.Dispose();
			}
			catch (Exception ex)
			{
				m_strErrorStatus = String.Format("ERROR: cannot run {0} for {1}.", strProgName, strFileName);
				DCSMsg.Show(m_strErrorStatus, ex);
				return false;
			}

			// read status file - see if it is OK
			string line;
			System.IO.StreamReader stream = new System.IO.StreamReader(strStatusFile);
			bool bOK;
			m_strErrorStatus = stream.ReadLine();
			if (m_strErrorStatus != null && m_strErrorStatus.Substring(0, 2).ToUpper() == "OK")
			{
				bOK = true;
				strChipID = stream.ReadLine();
			}
			else
			{
				bOK = false;
				line = stream.ReadLine();
				m_strErrorStatus = stream.ReadLine();
			}
			stream.Close();
			return bOK;
		}

		// call program to write one of MULTI files to a field in the chip
		private bool CallMultiProgram(bool bIfReading, string strFieldNo, string strFileName)
		{
			string strProgName;
			if (bIfReading)
				strProgName = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, CONST_MULTI_CHIP_READ_PROGRAM);
			else
				strProgName = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, CONST_MULTI_CHIP_ENCODE_PROGRAM);

			if (!System.IO.File.Exists(strProgName))
			{
				DCSDEV.DCSMsg.Show("Multi file chip encoding is not supported.");
				return false;
			}

			try
			{
				System.Diagnostics.Process proc = new System.Diagnostics.Process();
				proc.StartInfo.FileName = strProgName;
				proc.StartInfo.Arguments = strFieldNo + " " + strFileName;
				proc.Start();
				proc.WaitForExit(); // wait max 10 sec to finish
				//SYH find out if there was success.
				proc.Dispose();
			}
			catch (Exception ex)
			{
				DCSMsg.Show(ex.Message);
				return false;
			}
			return true;
		}

		public string ConfigFileName
		{
			get
			{
                if (m_eChipIFType == ChipIFType.MIFARE_RFID_CHIP_FORMULA
					|| m_eChipIFType == ChipIFType.MIFARE_RFID_CHIP_FILE
					|| m_eChipIFType == ChipIFType.EDL_RFID_CHIP_FILE)
				{
					return m_ConfigFilename;
				}
				else if (m_eChipIFType == ChipIFType.ICAO_CONTACT_CHIP)
				{
					return CONST_ICAO_CHIP_CONFIG;
				}
                else if (m_eChipIFType == ChipIFType.MULTIFILE_CONTACT_CHIP)
                {
                    return "not implemented";
                }
                else
				{
					this.ReportChipIFTypeError("ConfigFileName");
					return "unknown";
				}
			}
		}

		public bool IsDataInited
		{
			get
			{
				return m_bDataIsInited;
			}
		}

		public bool IsDeviceInited
		{
			get
			{
				return m_bDeviceIsInited;
			}
		}

		public int LastErrorNum
		{
			get
			{
				return m_iChipError;
			}
		}

		public string LastErrorString
		{
			get
			{
				if (m_eChipIFType == ChipIFType.MIFARE_RFID_CHIP_FILE
					|| m_eChipIFType == ChipIFType.MIFARE_RFID_CHIP_FORMULA)
				{
					return DCSCHIP_GetErrorMessage(m_iChipError);
				}
				else if (m_eChipIFType == ChipIFType.EDL_RFID_CHIP_FILE)
				{
					return m_EDLChipIF.GetErrormessage((DCS_EDLChip.ErrorCode)m_iChipError);
				}
				else if (m_eChipIFType == ChipIFType.ICAO_CONTACT_CHIP)
				{
					return m_strErrorStatus;
				}
				else
				{
					return m_strErrorStatus;
				}
			}
		}
		public string ICAOTextFile
		{
			get
			{
				return m_strICAOTextFile;
			}
		}
		public string ICAOFinger1File
		{
			get
			{
				return m_strICAOFinger1File;
			}
		}
		public string ICAOFinger2File
		{
			get
			{
				return m_strICAOFinger2File;
			}
		}
		public string ICAOPortraitFile
		{
			get
			{
				return m_strICAOPortraitFile;
			}
		}
	}
}
