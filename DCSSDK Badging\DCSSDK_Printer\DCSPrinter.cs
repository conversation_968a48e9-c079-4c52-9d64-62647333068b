using System;
using System.Collections;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Windows.Forms;
using System.Drawing.Printing;

using System.Runtime.InteropServices;
using System.Diagnostics;

using DCSDEV;
using DCSDEV.DCSDesign;


namespace DCSDEV.DCSPrinter
{
	/// <summary>
	/// Summary description for DCSPrinter.
	/// </summary>
	public class DCSPrinter : System.Windows.Forms.Form
	{
		//[DllImport("User32.dll")]
		//static extern IntPtr SetForegroundWindow(IntPtr hWnd);

        // used by PrintThread
        internal static int m_iBadgesPerSheet = 1;
        internal static ArrayList m_arrayQueueSheet = null;

		private ArrayList m_arrayQueueSingle = null;
		
		DCSDEV.ParameterStore m_ps;
        private const int TIMER_INTERVAL = 1000;
		private string m_strQueueDirectory;
        private enum PrintModes { PROGRAM_PAUSED, PAUSED, ACTIVE, PRINTING, PAUSE_PENDING };
		private PrintModes m_ePrintMode = PrintModes.PROGRAM_PAUSED;
		private bool m_bInitOK = false;
        private bool m_bQueuesNeedUpdate = false;

		private System.Windows.Forms.Button buttonStart;
		private System.Windows.Forms.Button buttonPause;
        private System.Windows.Forms.Button buttonProperties;
		private System.Windows.Forms.TextBox tbPrintMode;
		private System.Windows.Forms.TextBox tbQueueLength;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.Timer timer1;
		private System.Windows.Forms.Button buttonRefreshQ;
		private System.Windows.Forms.Button buttonPrintNow;
		private System.Windows.Forms.TextBox tbPerSheet;
		private System.Windows.Forms.Label labelPerSheet;
		private System.Windows.Forms.Button buttonEmptyQ;
        private TextBox tbSheetQueue;
        private Label label1;
        private TextBox tbSingleQueue;
        private Label label4;
        private Label label5;
		private System.ComponentModel.IContainer components;

		public DCSPrinter()
		{
			// This call is required by the Windows.Forms Form Designer.
			InitializeComponent();

			// TODO: Add any initialization after the InitComponent call
            m_ps = new DCSDEV.ParameterStore("DCSSDK_Mgt");
            Rectangle rectPtr = m_ps.GetRectParameter("DCSPrinterRect", Rectangle.Empty);
            if (rectPtr != Rectangle.Empty)
            {
                Rectangle rectScreen = System.Windows.Forms.Screen.GetBounds(this);
                rectScreen.Intersect(rectPtr);
                if (rectScreen.Equals(rectPtr)) this.Location = rectPtr.Location;
            }

            m_arrayQueueSingle = new ArrayList();
            m_arrayQueueSheet = new ArrayList();

            this.DoInit();

			if (TIMER_INTERVAL>0) this.timer1.Interval = TIMER_INTERVAL;
			this.timer1.Enabled = false;
            m_bQueuesNeedUpdate = true;
            this.RefreshArray();
            this.SetPrintMode(PrintModes.PAUSED);
			// wait for a start command before printing
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if( components != null )
					components.Dispose();
			}
			base.Dispose( disposing );
		}

		/********************************************************
		/// <summary>
		/// The main entry point for the application.
		/// </summary>
		[STAThread]
		static void Main() 
		{
			Application.EnableVisualStyles();
			Application.DoEvents();

			// Allow only one instance of this program
			Process[] procs = Process.GetProcessesByName(Application.ProductName);
			if (procs.Length > 1) // This code should prevent the number from ever getting above 2.
			{
				// the previously running instance will be at either index 0 or 1
				int index;
				if ((int)procs[0].MainWindowHandle != 0) index = 0;
				else index = 1;
				SetForegroundWindow(procs[index].MainWindowHandle);
				//ShowWindow is necessary to restore a minimized window - I think I want to leave it the way the opr left it.
				//ShowWindow(procs[index].MainWindowHandle, 9);
				return;
			}
			Application.Run(new DCSPrinter());
		}
		*************************************************************/

        public void DoSetWindowState(System.Windows.Forms.FormWindowState windowState)
        {
            this.WindowState = windowState;
        }

        public void DoInit()
		{
			m_bInitOK = true;
			m_strQueueDirectory = m_ps.GetStringParameter("PrinterQueueDir", "");
			if (m_strQueueDirectory == "") m_strQueueDirectory = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, "Queue");
			if (!System.IO.Directory.Exists(m_strQueueDirectory)) System.IO.Directory.CreateDirectory(m_strQueueDirectory);

			int nColumns = 1;
			int nRows = 1;
			nColumns = m_ps.GetIntParameter("SheetLayout", "Columns", nColumns);
			nRows   = m_ps.GetIntParameter("SheetLayout", "Rows", nRows);
			m_iBadgesPerSheet = nColumns * nRows;
			this.tbPerSheet.Text = m_iBadgesPerSheet.ToString();
			//this.labelPerSheet.Visible = true;
			//this.tbPerSheet.Visible = true;

			// check directories
			if (!System.IO.Directory.Exists(m_strQueueDirectory))
			{
				DCSMsg.Show(String.Format("The queue directory '{0}' does not exist.  It will be created when you continue.", m_strQueueDirectory));
				// create directory
				System.IO.Directory.CreateDirectory(m_strQueueDirectory);
			}
            m_bInitOK = true;
        }

        public void DoPause()
		{
			if (m_ePrintMode == PrintModes.PRINTING)
				SetPrintMode(PrintModes.PAUSE_PENDING);
			else
				SetPrintMode(PrintModes.PAUSED);
		}
		
		private void DoProperties()
		{
			PrintModes ePrintModeSave = m_ePrintMode;
            this.SetPrintMode(PrintModes.PAUSED);

            DCSDEV.PrintProperties.BadgingMgtForm printProp = new DCSDEV.PrintProperties.BadgingMgtForm();
            printProp.ShowDialog();
            printProp.Dispose();
			
            this.DoInit();
            this.SetPrintMode(ePrintModeSave);
		}

        internal void DoRestoreXnq()
        {
            string strEnq;
            // read all XNQ aborted badges currently in the queue directory
            string[] strFiles = System.IO.Directory.GetFiles(m_strQueueDirectory, "*.XNQ");

            if (strFiles.Length > 0)
            {
                if (DCSMsg.ShowOKC(String.Format("OK to restore {0} documents to the queue?", strFiles.Length)) == DialogResult.OK)
                {
                    foreach (string strXnq in strFiles)
                    {
                        strEnq = strXnq.Substring(0, strXnq.Length - 3) + "ENQ";

                        if (System.IO.File.Exists(strEnq))
                            System.IO.File.Delete(strXnq);
                        else
                            System.IO.File.Move(strXnq, strEnq);
                    }
                }
            }
            this.DoRefresh();
        }

        public void DoRefresh()
        {
            if (!m_bInitOK) return;
            this.m_bQueuesNeedUpdate = true;
            this.RefreshArray();
        }

        public void DoActivate()
        {
            this.SetPrintMode(PrintModes.ACTIVE);
            this.timer1_Tick(null, null);
        }

        public void DoPrintNow()
        {
            this.DoStartPrint(true);
        }

        // Refresh the queue arrays. If nothing to print returns; 
        // else sets print mode and prints the arrays.
		private void DoStartPrint(bool bCloseOut)
		{
			if (!m_bInitOK) return;
            bool bRet;

			// skip if empty queues and not closing out sheets
            if (m_arrayQueueSheet.Count >= (bCloseOut ? 1 : m_iBadgesPerSheet) || m_arrayQueueSingle.Count > 0)
                SetPrintMode(PrintModes.PRINTING);
            else
                return;

            // print all the full sheets in the sheet queue - use a print-refresh cycle until there are none
			while (m_arrayQueueSheet.Count >= (bCloseOut? 1 : m_iBadgesPerSheet))
			{
                this.Activate();

                m_bQueuesNeedUpdate = true;
                bRet = this.PrintSheetFromArray();
                if (!bRet) 
                    m_ePrintMode = PrintModes.PROGRAM_PAUSED;

                this.tbSingleQueue.Text = m_arrayQueueSingle.Count.ToString();
                this.tbSheetQueue.Text = m_arrayQueueSheet.Count.ToString();
                this.tbQueueLength.Text = (m_arrayQueueSingle.Count + m_arrayQueueSheet.Count).ToString();
                Application.DoEvents();
                if (m_ePrintMode == PrintModes.PAUSE_PENDING || m_ePrintMode == PrintModes.PROGRAM_PAUSED)
				{
                    return;
				}
			}

			// non sheet
            if (m_arrayQueueSingle.Count > 0)
            {
                this.PrintFromArray();
            }
            return;
        }

        // non sheet
		private void PrintFromArray()
		{
			bool bRet;
			string strEnq;
			string strXnq;
			QueueEntry q;

            m_bQueuesNeedUpdate = true;
            // print all the badges that are in the queue - use a print-refresh cycle until there are none
            while (m_arrayQueueSingle.Count > 0)
			{
                this.Activate();    // equivalent to SetForegroundWindow(this.Handle)

                q = (QueueEntry)m_arrayQueueSingle[0];
				strEnq = q.strDatFile;
				m_arrayQueueSingle.RemoveAt(0);
				strXnq = strEnq.Substring(0, strEnq.Length - 3) + "XNQ";

				if (System.IO.File.Exists(strXnq))
					System.IO.File.Delete(strXnq);

				System.IO.File.Move(strEnq, strXnq);
                bRet = PrintOneCard(strXnq);    // returns true if ok and not canceled

                Application.DoEvents(); //syh ?
                if (bRet)
				{
					System.IO.File.Delete(strXnq);	// if successful remove badge dat from the queue
				}
				else
				{
					m_ePrintMode = PrintModes.PROGRAM_PAUSED;
					if (DCSMsg.ShowYN("Do you want to restore the interrupted document to the queue?") == DialogResult.Yes)
					{
						// restore badge dat to the queue
						System.IO.File.Move(strXnq, strEnq);	// src, dst
					}
					else
					{
						System.IO.File.Delete(strXnq);	// if successful remove badge dat from the queue
					}
				}
                this.tbSingleQueue.Text = m_arrayQueueSingle.Count.ToString();
                this.tbSheetQueue.Text = m_arrayQueueSheet.Count.ToString();
                this.tbQueueLength.Text = (m_arrayQueueSingle.Count + m_arrayQueueSheet.Count).ToString();
				Application.DoEvents();

                if (m_ePrintMode == PrintModes.PAUSE_PENDING || m_ePrintMode == PrintModes.PROGRAM_PAUSED)
                {
                    break;
                }
			}
        }

        // return false if canceled
        public bool PrintOneCard(string strXnq)
        {
            System.Threading.ThreadStart threadStart;
            System.Threading.Thread thread;
            DCSDEV.DCSPrinter.PrintThread printThread = new DCSDEV.DCSPrinter.PrintThread();
			printThread.m_strBadgeDataFile = strXnq;
			printThread.m_iBadgesPerSheet = m_iBadgesPerSheet;
			printThread.m_arrayQueueSheet = m_arrayQueueSheet;

            threadStart = new System.Threading.ThreadStart(printThread.Thread_PrintOneCard);
            thread = new System.Threading.Thread(threadStart);
            thread.Start();
            thread.Join();

			return printThread.m_bCardPrintOK;
        }

        private bool PrintSheetFromArray()
        {
            System.Threading.ThreadStart threadStart;
            System.Threading.Thread thread;
            DCSDEV.DCSPrinter.PrintThread printThread = new DCSDEV.DCSPrinter.PrintThread();
			printThread.m_iBadgesPerSheet = m_iBadgesPerSheet;
			printThread.m_arrayQueueSheet = m_arrayQueueSheet;

            threadStart = new System.Threading.ThreadStart(printThread.Thread_PrintSheetFromArray);
            thread = new System.Threading.Thread(threadStart);
            thread.Start();
            thread.Join();

			return printThread.m_bCardPrintOK;
        }

        // read the badge data file to get the design and read the design to get the printer/sheet parameter
        public static bool IsSheetType(string strBadgeDataFile)
        {
            // read badge dat file to get badge design name
            DCSDEV.DCSDesign.DCSBadgeDataset badgeData = new DCSDEV.DCSDesign.DCSBadgeDataset();
            bool bRet = badgeData.ReadBadgeDatFile(strBadgeDataFile);
            if (!bRet) return false;

            int iPrinterType;
            using (DCSDEV.DCSDesign.DCSDesign design = new DCSDEV.DCSDesign.DCSDesign())
            {
                iPrinterType = design.ReadBadgeDesign_GetPrinterType(badgeData.m_strDesignName);
            }
            if (iPrinterType < 0) iPrinterType = 0;
			if (iPrinterType == 0 || iPrinterType == 1) return false;	// hardwired cards type

            // get parameters for that printer type
            DCSDEV.PrintProperties.PrinterTypeDatum bcDatum = new DCSDEV.PrintProperties.PrinterTypeDatum("");
            bcDatum.LoadPrinterTypeData(iPrinterType);
            return (bcDatum.m_IfMultiPerSheet);
        }

		// read the badge data file to get the design and read the design to learn 
		// if card has chip and if printer type will can scan for smart chip id, return true
		public static bool IsScanChipIDType(string strBadgeDataFile)
		{
			// read badge dat file to get badge design name
			DCSDEV.DCSDesign.DCSBadgeDataset badgeData = new DCSDEV.DCSDesign.DCSBadgeDataset();
			bool bRet = badgeData.ReadBadgeDatFile(strBadgeDataFile);
			if (!bRet) return false;

			bool bHasChip;
			int iPrinterType;
			using (DCSDEV.DCSDesign.DCSDesign design = new DCSDEV.DCSDesign.DCSDesign())
			{
				if (!design.ReadBadgeDesign(badgeData.m_strDesignName)) return false;
				iPrinterType = design.PrinterTypeIndex;
				bHasChip = design.HasChip;
			}
			if (!bHasChip) return false;
			if (iPrinterType < 0) iPrinterType = 0;

			// get parameters for that printer type
			DCSDEV.PrintProperties.PrinterTypeDatum bcDatum = new DCSDEV.PrintProperties.PrinterTypeDatum("");
			bcDatum.LoadPrinterTypeData(iPrinterType);
			return (bcDatum.m_bPrinterHasChipEncoder && bcDatum.m_bScanChipIDWhenPrinting);
		}

		private void RefreshArray()
		{
			if (!m_bInitOK) return;

			bool bUseSheetPrinterAlways = false;
			if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.SheetPrinting))
			{
				bUseSheetPrinterAlways = m_ps.GetBoolParameter("UseSheetPrinter", false);
			}
			

            // read all badges currently in the queue directory
            string[] strFiles = System.IO.Directory.GetFiles(m_strQueueDirectory, "*.ENQ");

            if ((m_bQueuesNeedUpdate) || (strFiles.Length != m_arrayQueueSingle.Count + m_arrayQueueSheet.Count))
            {
                if (m_arrayQueueSheet != null)
                    m_arrayQueueSheet.Clear();
                else
                    m_arrayQueueSheet = new ArrayList();

                if (m_arrayQueueSingle != null)
                    m_arrayQueueSingle.Clear();
                else
                    m_arrayQueueSingle = new ArrayList();

                if (strFiles.Length > 0)
                {
                    // put them into internal queue array - in order by name/number
                    QueueEntry q;
                    foreach (string str in strFiles)
                    {
                        q = new QueueEntry();
                        q.strDatFile = str;
                        // q.time = System.IO.File.GetLastWriteTime(str);

						if (bUseSheetPrinterAlways || DCSDEV.DCSPrinter.DCSPrinter.IsSheetType(str))
                            m_arrayQueueSheet.Add(q);
                        else
                            m_arrayQueueSingle.Add(q);
                    }
                    QueueCompare comparer = new QueueCompare();
                    m_arrayQueueSingle.Sort(comparer);
                    m_arrayQueueSheet.Sort(comparer);
                }
                m_bQueuesNeedUpdate = false;    // is something printed since last refresh
            }
            else
            {
                m_bQueuesNeedUpdate = false;
                return;
            }
            this.tbSingleQueue.Text = m_arrayQueueSingle.Count.ToString();
            this.tbSheetQueue.Text = m_arrayQueueSheet.Count.ToString();
            this.tbQueueLength.Text = (m_arrayQueueSingle.Count + m_arrayQueueSheet.Count).ToString();
        
        }

		private void EmptyArray()
		{
			if (!m_bInitOK) return;

            QueueEntry q;
            if (m_arrayQueueSingle != null && m_arrayQueueSingle.Count != 0)
            {
                for (int i = 0; i < m_arrayQueueSingle.Count; i++)
                {
                    q = (QueueEntry)m_arrayQueueSingle[i];
                    if (q.strDatFile != null) System.IO.File.Delete(q.strDatFile);
                }
            }
            if (m_arrayQueueSheet != null && m_arrayQueueSheet.Count != 0)
            {
                for (int i = 0; i < m_arrayQueueSheet.Count; i++)
                {
                    q = (QueueEntry)m_arrayQueueSheet[i];
                    if (q.strDatFile != null) System.IO.File.Delete(q.strDatFile);
                }
            }
        }

		private void SetPrintMode(PrintModes mode)
		{
            this.timer1.Stop();
			m_ePrintMode = mode;
			this.tbPrintMode.Text = mode.ToString();
			this.Text = "DCSPrinter - " + mode.ToString();
			if (mode == PrintModes.PROGRAM_PAUSED)
			{
                this.buttonPrintNow.Visible = (m_arrayQueueSingle.Count + m_arrayQueueSheet.Count > 0);  // && m_arrayQueue.Count < m_iBadgesPerSheet);
				this.buttonPause.Visible = false;
				this.buttonStart.Visible = this.buttonRefreshQ.Visible = this.buttonEmptyQ.Visible = this.buttonProperties.Visible = true;
            }
            else if (mode == PrintModes.PAUSED)
            {
                this.buttonPrintNow.Visible = (m_arrayQueueSingle.Count + m_arrayQueueSheet.Count > 0);  // && m_arrayQueue.Count < m_iBadgesPerSheet);
                this.buttonPause.Visible = false;
                this.buttonStart.Visible = this.buttonRefreshQ.Visible = this.buttonEmptyQ.Visible = this.buttonProperties.Visible = true;
                //syh this.timer1.Start();
            }
            else if (mode == PrintModes.ACTIVE)
			{
                this.buttonPrintNow.Visible = (m_arrayQueueSingle.Count + m_arrayQueueSheet.Count > 0);  // && m_arrayQueue.Count < m_iBadgesPerSheet);
				this.buttonPause.Visible = true;
				this.buttonStart.Visible = this.buttonRefreshQ.Visible = this.buttonEmptyQ.Visible = this.buttonProperties.Visible = false;
                this.timer1.Start();
            }
			else if (mode == PrintModes.PAUSE_PENDING)
			{
                this.buttonPrintNow.Visible = false;
				this.buttonPause.Visible = false;
				this.buttonStart.Visible = this.buttonRefreshQ.Visible = this.buttonEmptyQ.Visible = this.buttonProperties.Visible = false;
			}
			else	//if (mode == PrintModes.PRINTING)
			{
				this.buttonPause.Visible = true;
				this.buttonStart.Visible = this.buttonRefreshQ.Visible = this.buttonEmptyQ.Visible = this.buttonProperties.Visible = false;
				this.buttonPrintNow.Visible = false;
			}
			Application.DoEvents();	// this allows screen redraw before going on to other things
		}

		#region Component Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify 
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(DCSPrinter));
            this.buttonStart = new System.Windows.Forms.Button();
            this.buttonPause = new System.Windows.Forms.Button();
            this.buttonProperties = new System.Windows.Forms.Button();
            this.tbPrintMode = new System.Windows.Forms.TextBox();
            this.tbQueueLength = new System.Windows.Forms.TextBox();
            this.label3 = new System.Windows.Forms.Label();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.buttonRefreshQ = new System.Windows.Forms.Button();
            this.buttonPrintNow = new System.Windows.Forms.Button();
            this.tbPerSheet = new System.Windows.Forms.TextBox();
            this.labelPerSheet = new System.Windows.Forms.Label();
            this.buttonEmptyQ = new System.Windows.Forms.Button();
            this.tbSheetQueue = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.tbSingleQueue = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.SuspendLayout();
            // 
            // buttonStart
            // 
            resources.ApplyResources(this.buttonStart, "buttonStart");
            this.buttonStart.Name = "buttonStart";
            this.buttonStart.Click += new System.EventHandler(this.buttonStart_Click);
            // 
            // buttonPause
            // 
            resources.ApplyResources(this.buttonPause, "buttonPause");
            this.buttonPause.Name = "buttonPause";
            this.buttonPause.Click += new System.EventHandler(this.buttonPause_Click);
            // 
            // buttonProperties
            // 
            resources.ApplyResources(this.buttonProperties, "buttonProperties");
            this.buttonProperties.Name = "buttonProperties";
            this.buttonProperties.Click += new System.EventHandler(this.buttonProperties_Click);
            // 
            // tbPrintMode
            // 
            this.tbPrintMode.BackColor = System.Drawing.SystemColors.Control;
            this.tbPrintMode.BorderStyle = System.Windows.Forms.BorderStyle.None;
            resources.ApplyResources(this.tbPrintMode, "tbPrintMode");
            this.tbPrintMode.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(192)))));
            this.tbPrintMode.Name = "tbPrintMode";
            this.tbPrintMode.ReadOnly = true;
            this.tbPrintMode.TabStop = false;
            // 
            // tbQueueLength
            // 
            this.tbQueueLength.BackColor = System.Drawing.SystemColors.Control;
            this.tbQueueLength.BorderStyle = System.Windows.Forms.BorderStyle.None;
            resources.ApplyResources(this.tbQueueLength, "tbQueueLength");
            this.tbQueueLength.Name = "tbQueueLength";
            this.tbQueueLength.ReadOnly = true;
            this.tbQueueLength.TabStop = false;
            // 
            // label3
            // 
            resources.ApplyResources(this.label3, "label3");
            this.label3.Name = "label3";
            // 
            // timer1
            // 
            this.timer1.Interval = 3000;
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // buttonRefreshQ
            // 
            resources.ApplyResources(this.buttonRefreshQ, "buttonRefreshQ");
            this.buttonRefreshQ.Name = "buttonRefreshQ";
            this.buttonRefreshQ.Click += new System.EventHandler(this.buttonRefreshQ_Click);
            // 
            // buttonPrintNow
            // 
            this.buttonPrintNow.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            resources.ApplyResources(this.buttonPrintNow, "buttonPrintNow");
            this.buttonPrintNow.Name = "buttonPrintNow";
            this.buttonPrintNow.Click += new System.EventHandler(this.buttonPrintNow_Click);
            // 
            // tbPerSheet
            // 
            this.tbPerSheet.BackColor = System.Drawing.SystemColors.Control;
            this.tbPerSheet.BorderStyle = System.Windows.Forms.BorderStyle.None;
            resources.ApplyResources(this.tbPerSheet, "tbPerSheet");
            this.tbPerSheet.Name = "tbPerSheet";
            this.tbPerSheet.ReadOnly = true;
            this.tbPerSheet.TabStop = false;
            // 
            // labelPerSheet
            // 
            resources.ApplyResources(this.labelPerSheet, "labelPerSheet");
            this.labelPerSheet.Name = "labelPerSheet";
            // 
            // buttonEmptyQ
            // 
            resources.ApplyResources(this.buttonEmptyQ, "buttonEmptyQ");
            this.buttonEmptyQ.Name = "buttonEmptyQ";
            this.buttonEmptyQ.Click += new System.EventHandler(this.buttonEmptyQ_Click);
            // 
            // tbSheetQueue
            // 
            this.tbSheetQueue.BackColor = System.Drawing.SystemColors.Control;
            this.tbSheetQueue.BorderStyle = System.Windows.Forms.BorderStyle.None;
            resources.ApplyResources(this.tbSheetQueue, "tbSheetQueue");
            this.tbSheetQueue.Name = "tbSheetQueue";
            this.tbSheetQueue.ReadOnly = true;
            this.tbSheetQueue.TabStop = false;
            // 
            // label1
            // 
            resources.ApplyResources(this.label1, "label1");
            this.label1.Name = "label1";
            // 
            // tbSingleQueue
            // 
            this.tbSingleQueue.BackColor = System.Drawing.SystemColors.Control;
            this.tbSingleQueue.BorderStyle = System.Windows.Forms.BorderStyle.None;
            resources.ApplyResources(this.tbSingleQueue, "tbSingleQueue");
            this.tbSingleQueue.Name = "tbSingleQueue";
            this.tbSingleQueue.ReadOnly = true;
            this.tbSingleQueue.TabStop = false;
            // 
            // label4
            // 
            resources.ApplyResources(this.label4, "label4");
            this.label4.Name = "label4";
            // 
            // label5
            // 
            resources.ApplyResources(this.label5, "label5");
            this.label5.Name = "label5";
            // 
            // DCSPrinter
            // 
            resources.ApplyResources(this, "$this");
            this.Controls.Add(this.label5);
            this.Controls.Add(this.tbSingleQueue);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.tbSheetQueue);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.buttonEmptyQ);
            this.Controls.Add(this.tbPerSheet);
            this.Controls.Add(this.tbQueueLength);
            this.Controls.Add(this.tbPrintMode);
            this.Controls.Add(this.labelPerSheet);
            this.Controls.Add(this.buttonPrintNow);
            this.Controls.Add(this.buttonRefreshQ);
            this.Controls.Add(this.label3);
            this.Controls.Add(this.buttonProperties);
            this.Controls.Add(this.buttonPause);
            this.Controls.Add(this.buttonStart);
            this.MaximizeBox = false;
            this.Name = "DCSPrinter";
            this.WindowState = System.Windows.Forms.FormWindowState.Minimized;
            this.Closing += new System.ComponentModel.CancelEventHandler(this.DCSPrinter_Closing);
            this.ResumeLayout(false);
            this.PerformLayout();

		}
		#endregion

		private void buttonStart_Click(object sender, System.EventArgs e)
		{
            this.DoInit();
            this.SetPrintMode(PrintModes.ACTIVE);
		}

		private void buttonPause_Click(object sender, System.EventArgs e)
		{
			this.DoPause();
		}

		private void buttonProperties_Click(object sender, System.EventArgs e)
		{
			this.DoProperties();
		}

		private void timer1_Tick(object sender, System.EventArgs e)
		{
            this.timer1.Stop();
            switch (this.m_ePrintMode)
            {
                default:
                case PrintModes.PRINTING:
                case PrintModes.PAUSE_PENDING:
                case PrintModes.PROGRAM_PAUSED:
                    return;
                case PrintModes.PAUSED:
                    //this.RefreshArray();
                    //this.timer1.Start();
                    return;
                case PrintModes.ACTIVE:
                    
                    this.RefreshArray();
                    this.DoStartPrint(false);  // returns immediately if array is empty 
                    if (this.m_ePrintMode == PrintModes.PAUSE_PENDING || this.m_ePrintMode == PrintModes.PROGRAM_PAUSED)
                        this.SetPrintMode(PrintModes.PAUSED);
                    else
                        if (this.m_ePrintMode != PrintModes.ACTIVE) this.SetPrintMode(PrintModes.ACTIVE);
                    this.timer1.Start();
                    return;            
            }
		}

        // restore all XNQ files to ENQ status and refresh all counts
		private void buttonRefreshQ_Click(object sender, System.EventArgs e)
		{
			this.DoInit();
            this.DoRestoreXnq();
		}

		private void DCSPrinter_Closing(object sender, System.ComponentModel.CancelEventArgs e)
		{
            this.timer1.Stop();

            if (!m_bInitOK) return;	// if not inited just close without further processing

			if (m_ePrintMode == PrintModes.PRINTING || m_ePrintMode == PrintModes.PAUSE_PENDING)
			{
				// need to wait until current print finishes
				// syh note: it would best to automatically delay closing until print finishes
				// rather than cancel as done here.
				e.Cancel = true;
				return;
			}

			SetPrintMode(PrintModes.PROGRAM_PAUSED);
			
			this.RefreshArray();
            if (m_arrayQueueSingle.Count + m_arrayQueueSheet.Count > 0)
			{
				System.Windows.Forms.DialogResult result;
				result = DCSMsg.ShowYNC(
					"ID Services Printer is shutting down and there are documents in the queue.\r\n" + 
					"Unprinted documents will remain in the queue if you do not print them now.\r\n\r\n" +
					"Do you want to print the queued documents now?");
				if (result == System.Windows.Forms.DialogResult.Cancel)
				{
					e.Cancel = true;
				}
				else if (result == System.Windows.Forms.DialogResult.Yes)
				{
                    this.RefreshArray();
                    this.DoStartPrint(true);
                    SetPrintMode(PrintModes.PROGRAM_PAUSED);

                    this.RefreshArray();
                    this.tbQueueLength.Text = (m_arrayQueueSingle.Count + m_arrayQueueSheet.Count).ToString();
                    this.tbSheetQueue.Text = m_arrayQueueSheet.Count.ToString();
                    Application.DoEvents();	// allows the user to change the mode to cause an interrupt during printing
				}
			}
		}

		private void buttonPrintNow_Click(object sender, System.EventArgs e)
		{
			PrintModes ePrintModeSaved = m_ePrintMode;
            this.timer1.Stop();

            // start printing
            this.RefreshArray();
            this.DoStartPrint(true);

            this.RefreshArray();
            this.tbQueueLength.Text = (m_arrayQueueSingle.Count + m_arrayQueueSheet.Count).ToString();
            this.tbSheetQueue.Text = m_arrayQueueSheet.Count.ToString();
            Application.DoEvents();	// allows the user to change the mode to cause an interrupt during printing

            SetPrintMode(ePrintModeSaved);
		}

		private void buttonEmptyQ_Click(object sender, System.EventArgs e)
		{
			this.DoInit();

            this.EmptyArray();
            this.DoRefresh();
		}

        protected override void OnClosing(CancelEventArgs e)
        {
            m_ps.WriteRectParameter("DCSPrinterRect", new Rectangle(this.Location, this.ClientRectangle.Size));

            base.OnClosing(e);
        }
    }

	internal struct QueueEntry
	{
		internal string strDatFile;
		// internal System.DateTime time;
	}

	internal class QueueCompare : IComparer
	{
		#region IComparer Members

		public int Compare(object x, object y)
		{
			// TODO:  Add QueueCompare.Compare implementation
			// syh Note: sorting no on time is not necessary - QueueEntry could be simplified to just a name string
/*
 *			if (((QueueEntry)x).time == ((QueueEntry)y).time)	// if time is equal, use file name
				return String.Compare(((QueueEntry)x).strDatFile, ((QueueEntry)y).strDatFile);
			return (((QueueEntry)x).time < ((QueueEntry)y).time ? -1 : 1);
*/
			return String.Compare(((QueueEntry)x).strDatFile, ((QueueEntry)y).strDatFile);
		}

		#endregion
	}

}
