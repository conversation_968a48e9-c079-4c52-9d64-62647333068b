﻿BODY
{
	BACKGROUND-COLOR: white;
	FONT-FAMILY: "Verdana", sans-serif;
	FONT-SIZE: 100%;
	MARGIN-LEFT: 0px;
	MARGIN-TOP: 0px
}
P
{
	FONT-FAMILY: "Verdana", sans-serif;
	FONT-SIZE: 70%;
	LINE-HEIGHT: 12pt;
	MARGIN-BOTTOM: 0px;
	MARGIN-LEFT: 10px;
	MARGIN-TOP: 10px
}
.note
{
	BACKGROUND-COLOR:  #ffffff;
	COLOR: #336699;
	FONT-FAMILY: "Verdana", sans-serif;
	FONT-SIZE: 100%;
	MARGIN-BOTTOM: 0px;
	MARGIN-LEFT: 0px;
	MARGIN-TOP: 0px;
	PADDING-RIGHT: 10px
}
.infotable
{
	BACKGROUND-COLOR: #f0f0e0;
	BORDER-BOTTOM: #ffffff 0px solid;
	BORDER-COLLAPSE: collapse;
	BORDER-LEFT: #ffffff 0px solid;
	BORDER-RIGHT: #ffffff 0px solid;
	BORDER-TOP: #ffffff 0px solid;
	FONT-SIZE: 70%;
	MARGIN-LEFT: 10px
}
.issuetable
{
	BACKGROUND-COLOR: #ffffe8;
	BORDER-COLLAPSE: collapse;
	COLOR: #000000;
	FONT-SIZE: 100%;
	MARGIN-BOTTOM: 10px;
	MARGIN-LEFT: 13px;
	MARGIN-TOP: 0px
}
.issuetitle
{
	BACKGROUND-COLOR: #ffffff;
	BORDER-BOTTOM: #dcdcdc 1px solid;
	BORDER-TOP: #dcdcdc 1px;
	COLOR: #003366;
	FONT-WEIGHT: normal
}
.header
{
	BACKGROUND-COLOR: #cecf9c;
	BORDER-BOTTOM: #ffffff 1px solid;
	BORDER-LEFT: #ffffff 1px solid;
	BORDER-RIGHT: #ffffff 1px solid;
	BORDER-TOP: #ffffff 1px solid;
	COLOR: #000000;
	FONT-WEIGHT: bold
}
.issuehdr
{
	BACKGROUND-COLOR: #E0EBF5;
	BORDER-BOTTOM: #dcdcdc 1px solid;
	BORDER-TOP: #dcdcdc 1px solid;
	COLOR: #000000;
	FONT-WEIGHT: normal
}
.issuenone
{
	BACKGROUND-COLOR: #ffffff;
	BORDER-BOTTOM: 0px;
	BORDER-LEFT: 0px;
	BORDER-RIGHT: 0px;
	BORDER-TOP: 0px;
	COLOR: #000000;
	FONT-WEIGHT: normal
}
.content
{
	BACKGROUND-COLOR: #e7e7ce;
	BORDER-BOTTOM: #ffffff 1px solid;
	BORDER-LEFT: #ffffff 1px solid;
	BORDER-RIGHT: #ffffff 1px solid;
	BORDER-TOP: #ffffff 1px solid;
	PADDING-LEFT: 3px
}
.issuecontent
{
	BACKGROUND-COLOR: #ffffff;
	BORDER-BOTTOM: #dcdcdc 1px solid;
	BORDER-TOP: #dcdcdc 1px solid;
	PADDING-LEFT: 3px
}
A:link
{
	COLOR: #cc6633;
	TEXT-DECORATION: underline
}
A:visited
{
	COLOR: #cc6633;
}
A:active
{
	COLOR: #cc6633;
}
A:hover
{
	COLOR: #cc3300;
	TEXT-DECORATION: underline
}
H1
{
	BACKGROUND-COLOR: #003366;
	BORDER-BOTTOM: #336699 6px solid;
	COLOR: #ffffff;
	FONT-SIZE: 130%;
	FONT-WEIGHT: normal;
	MARGIN: 0em 0em 0em -20px;
	PADDING-BOTTOM: 8px;
	PADDING-LEFT: 30px;
	PADDING-TOP: 16px
}
H2
{
	COLOR: #000000;
	FONT-SIZE: 80%;
	FONT-WEIGHT: bold;
	MARGIN-BOTTOM: 3px;
	MARGIN-LEFT: 10px;
	MARGIN-TOP: 20px;
	PADDING-LEFT: 0px
}
H3
{
	COLOR: #000000;
	FONT-SIZE: 80%;
	FONT-WEIGHT: bold;
	MARGIN-BOTTOM: -5px;
	MARGIN-LEFT: 10px;
	MARGIN-TOP: 20px
}
H4
{
	COLOR: #000000;
	FONT-SIZE: 70%;
	FONT-WEIGHT: bold;
	MARGIN-BOTTOM: 0px;
	MARGIN-TOP: 15px;
	PADDING-BOTTOM: 0px
}
UL
{
	COLOR: #000000;
	FONT-SIZE: 70%;
	LIST-STYLE: square;
	MARGIN-BOTTOM: 0pt;
	MARGIN-TOP: 0pt
}
OL
{
	COLOR: #000000;
	FONT-SIZE: 70%;
	LIST-STYLE: square;
	MARGIN-BOTTOM: 0pt;
	MARGIN-TOP: 0pt
}
LI
{
	LIST-STYLE: square;
	MARGIN-LEFT: 0px
}
.expandable
{
	CURSOR: hand
}
.expanded
{
	color: black
}
.collapsed
{
	DISPLAY: none
}
.foot
{
BACKGROUND-COLOR: #ffffff;
BORDER-BOTTOM: #cecf9c 1px solid;
BORDER-TOP: #cecf9c 2px solid
}
.settings
{
MARGIN-LEFT: 25PX;
}
.help
{
TEXT-ALIGN: right;
margin-right: 10px;
}
