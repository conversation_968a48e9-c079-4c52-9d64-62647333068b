<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="dcsBackGroundProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>32, 70</value>
  </data>
  <data name="dcsBackGroundProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>216, 170</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="dcsBackGroundProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;dcsBackGroundProperties1.Name" xml:space="preserve">
    <value>dcsBackGroundProperties1</value>
  </data>
  <data name="&gt;&gt;dcsBackGroundProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSBackGroundProperties, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsBackGroundProperties1.Parent" xml:space="preserve">
    <value>groupBoxBackground</value>
  </data>
  <data name="&gt;&gt;dcsBackGroundProperties1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="checkBoxLandscape2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBoxLandscape2.Location" type="System.Drawing.Point, System.Drawing">
    <value>136, 40</value>
  </data>
  <data name="checkBoxLandscape2.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 24</value>
  </data>
  <data name="checkBoxLandscape2.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="checkBoxLandscape2.Text" xml:space="preserve">
    <value>Landscape</value>
  </data>
  <data name="&gt;&gt;checkBoxLandscape2.Name" xml:space="preserve">
    <value>checkBoxLandscape2</value>
  </data>
  <data name="&gt;&gt;checkBoxLandscape2.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBoxLandscape2.Parent" xml:space="preserve">
    <value>groupBoxBackground</value>
  </data>
  <data name="&gt;&gt;checkBoxLandscape2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="checkBoxLandscape1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBoxLandscape1.Location" type="System.Drawing.Point, System.Drawing">
    <value>32, 40</value>
  </data>
  <data name="checkBoxLandscape1.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 24</value>
  </data>
  <data name="checkBoxLandscape1.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="checkBoxLandscape1.Text" xml:space="preserve">
    <value>Landscape</value>
  </data>
  <data name="&gt;&gt;checkBoxLandscape1.Name" xml:space="preserve">
    <value>checkBoxLandscape1</value>
  </data>
  <data name="&gt;&gt;checkBoxLandscape1.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBoxLandscape1.Parent" xml:space="preserve">
    <value>groupBoxBackground</value>
  </data>
  <data name="&gt;&gt;checkBoxLandscape1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="radioSide2.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="radioSide2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="radioSide2.Location" type="System.Drawing.Point, System.Drawing">
    <value>136, 24</value>
  </data>
  <data name="radioSide2.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 16</value>
  </data>
  <data name="radioSide2.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="radioSide2.Text" xml:space="preserve">
    <value>Side 2</value>
  </data>
  <data name="&gt;&gt;radioSide2.Name" xml:space="preserve">
    <value>radioSide2</value>
  </data>
  <data name="&gt;&gt;radioSide2.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioSide2.Parent" xml:space="preserve">
    <value>groupBoxBackground</value>
  </data>
  <data name="&gt;&gt;radioSide2.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="radioSide1.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="radioSide1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="radioSide1.Location" type="System.Drawing.Point, System.Drawing">
    <value>32, 24</value>
  </data>
  <data name="radioSide1.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 16</value>
  </data>
  <data name="radioSide1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="radioSide1.Text" xml:space="preserve">
    <value>Side 1</value>
  </data>
  <data name="&gt;&gt;radioSide1.Name" xml:space="preserve">
    <value>radioSide1</value>
  </data>
  <data name="&gt;&gt;radioSide1.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioSide1.Parent" xml:space="preserve">
    <value>groupBoxBackground</value>
  </data>
  <data name="&gt;&gt;radioSide1.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="groupBoxBackground.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 184</value>
  </data>
  <data name="groupBoxBackground.Size" type="System.Drawing.Size, System.Drawing">
    <value>320, 240</value>
  </data>
  <data name="groupBoxBackground.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="groupBoxBackground.Text" xml:space="preserve">
    <value>Background</value>
  </data>
  <data name="&gt;&gt;groupBoxBackground.Name" xml:space="preserve">
    <value>groupBoxBackground</value>
  </data>
  <data name="&gt;&gt;groupBoxBackground.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBoxBackground.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBoxBackground.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="buttonCancel.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonCancel.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>555, 412</value>
  </data>
  <data name="buttonCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 24</value>
  </data>
  <data name="buttonCancel.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="buttonCancel.Text" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Name" xml:space="preserve">
    <value>buttonCancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonCancel.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="buttonAccept.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonAccept.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonAccept.Location" type="System.Drawing.Point, System.Drawing">
    <value>431, 412</value>
  </data>
  <data name="buttonAccept.Size" type="System.Drawing.Size, System.Drawing">
    <value>118, 24</value>
  </data>
  <data name="buttonAccept.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="buttonAccept.Text" xml:space="preserve">
    <value>&amp;OK- apply and close</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Name" xml:space="preserve">
    <value>buttonAccept</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAccept.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="buttonApply.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonApply.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonApply.Location" type="System.Drawing.Point, System.Drawing">
    <value>352, 412</value>
  </data>
  <data name="buttonApply.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 24</value>
  </data>
  <data name="buttonApply.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="buttonApply.Text" xml:space="preserve">
    <value>&amp;Apply</value>
  </data>
  <data name="&gt;&gt;buttonApply.Name" xml:space="preserve">
    <value>buttonApply</value>
  </data>
  <data name="&gt;&gt;buttonApply.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonApply.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonApply.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="dcsPositionSizeProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 16</value>
  </data>
  <data name="dcsPositionSizeProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 128</value>
  </data>
  <data name="dcsPositionSizeProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;dcsPositionSizeProperties1.Name" xml:space="preserve">
    <value>dcsPositionSizeProperties1</value>
  </data>
  <data name="&gt;&gt;dcsPositionSizeProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSPositionSizeProperties, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsPositionSizeProperties1.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;dcsPositionSizeProperties1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="groupBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>168, 16</value>
  </data>
  <data name="groupBox2.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 152</value>
  </data>
  <data name="groupBox2.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="groupBox2.Text" xml:space="preserve">
    <value>Print offset / Size</value>
  </data>
  <data name="&gt;&gt;groupBox2.Name" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;groupBox2.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBox2.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="checkBoxDoubleSided.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkBoxDoubleSided.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBoxDoubleSided.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 144</value>
  </data>
  <data name="checkBoxDoubleSided.Size" type="System.Drawing.Size, System.Drawing">
    <value>136, 24</value>
  </data>
  <data name="checkBoxDoubleSided.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="checkBoxDoubleSided.Text" xml:space="preserve">
    <value>Double sided</value>
  </data>
  <data name="&gt;&gt;checkBoxDoubleSided.Name" xml:space="preserve">
    <value>checkBoxDoubleSided</value>
  </data>
  <data name="&gt;&gt;checkBoxDoubleSided.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBoxDoubleSided.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;checkBoxDoubleSided.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="comboPrinterType.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 24</value>
  </data>
  <data name="comboPrinterType.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 21</value>
  </data>
  <data name="comboPrinterType.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;comboPrinterType.Name" xml:space="preserve">
    <value>comboPrinterType</value>
  </data>
  <data name="&gt;&gt;comboPrinterType.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboPrinterType.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;comboPrinterType.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="labelPreferredPrinterType.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelPreferredPrinterType.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 8</value>
  </data>
  <data name="labelPreferredPrinterType.Size" type="System.Drawing.Size, System.Drawing">
    <value>136, 16</value>
  </data>
  <data name="labelPreferredPrinterType.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelPreferredPrinterType.Text" xml:space="preserve">
    <value>Printer type</value>
  </data>
  <data name="&gt;&gt;labelPreferredPrinterType.Name" xml:space="preserve">
    <value>labelPreferredPrinterType</value>
  </data>
  <data name="&gt;&gt;labelPreferredPrinterType.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelPreferredPrinterType.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelPreferredPrinterType.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="checkHasMagStripe.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkHasMagStripe.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkHasMagStripe.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 96</value>
  </data>
  <data name="checkHasMagStripe.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 16</value>
  </data>
  <data name="checkHasMagStripe.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="checkHasMagStripe.Text" xml:space="preserve">
    <value>Has magnetic stripe</value>
  </data>
  <data name="&gt;&gt;checkHasMagStripe.Name" xml:space="preserve">
    <value>checkHasMagStripe</value>
  </data>
  <data name="&gt;&gt;checkHasMagStripe.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkHasMagStripe.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;checkHasMagStripe.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="dcsMagStripeProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 19</value>
  </data>
  <data name="dcsMagStripeProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>240, 214</value>
  </data>
  <data name="dcsMagStripeProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;dcsMagStripeProperties1.Name" xml:space="preserve">
    <value>dcsMagStripeProperties1</value>
  </data>
  <data name="&gt;&gt;dcsMagStripeProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSMagStripeProperties, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsMagStripeProperties1.Parent" xml:space="preserve">
    <value>groupBoxMagStripe</value>
  </data>
  <data name="&gt;&gt;dcsMagStripeProperties1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="groupBoxMagStripe.Location" type="System.Drawing.Point, System.Drawing">
    <value>352, 155</value>
  </data>
  <data name="groupBoxMagStripe.Size" type="System.Drawing.Size, System.Drawing">
    <value>264, 251</value>
  </data>
  <data name="groupBoxMagStripe.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="groupBoxMagStripe.Text" xml:space="preserve">
    <value>Mag stripe encoding</value>
  </data>
  <data name="&gt;&gt;groupBoxMagStripe.Name" xml:space="preserve">
    <value>groupBoxMagStripe</value>
  </data>
  <data name="&gt;&gt;groupBoxMagStripe.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBoxMagStripe.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBoxMagStripe.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="buttonPrinterTypes.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonPrinterTypes.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 8.25pt, style=Bold</value>
  </data>
  <data name="buttonPrinterTypes.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonPrinterTypes.Location" type="System.Drawing.Point, System.Drawing">
    <value>136, 24</value>
  </data>
  <data name="buttonPrinterTypes.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 21</value>
  </data>
  <data name="buttonPrinterTypes.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="buttonPrinterTypes.Text" xml:space="preserve">
    <value>&gt;</value>
  </data>
  <data name="&gt;&gt;buttonPrinterTypes.Name" xml:space="preserve">
    <value>buttonPrinterTypes</value>
  </data>
  <data name="&gt;&gt;buttonPrinterTypes.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPrinterTypes.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonPrinterTypes.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="checkHasChip.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkHasChip.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkHasChip.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 72</value>
  </data>
  <data name="checkHasChip.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 16</value>
  </data>
  <data name="checkHasChip.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="checkHasChip.Text" xml:space="preserve">
    <value>Has smart chip</value>
  </data>
  <data name="&gt;&gt;checkHasChip.Name" xml:space="preserve">
    <value>checkHasChip</value>
  </data>
  <data name="&gt;&gt;checkHasChip.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkHasChip.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;checkHasChip.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="tbFormulaChip.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 108</value>
  </data>
  <data name="tbFormulaChip.Size" type="System.Drawing.Size, System.Drawing">
    <value>232, 20</value>
  </data>
  <data name="tbFormulaChip.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;tbFormulaChip.Name" xml:space="preserve">
    <value>tbFormulaChip</value>
  </data>
  <data name="&gt;&gt;tbFormulaChip.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbFormulaChip.Parent" xml:space="preserve">
    <value>groupBoxChip</value>
  </data>
  <data name="&gt;&gt;tbFormulaChip.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="buttonEditChipFormula.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonEditChipFormula.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 8.25pt</value>
  </data>
  <data name="buttonEditChipFormula.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonEditChipFormula.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 76</value>
  </data>
  <data name="buttonEditChipFormula.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 20</value>
  </data>
  <data name="buttonEditChipFormula.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="buttonEditChipFormula.Text" xml:space="preserve">
    <value>Edit formula</value>
  </data>
  <data name="&gt;&gt;buttonEditChipFormula.Name" xml:space="preserve">
    <value>buttonEditChipFormula</value>
  </data>
  <data name="&gt;&gt;buttonEditChipFormula.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonEditChipFormula.Parent" xml:space="preserve">
    <value>groupBoxChip</value>
  </data>
  <data name="&gt;&gt;buttonEditChipFormula.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="groupBoxChip.Location" type="System.Drawing.Point, System.Drawing">
    <value>352, 16</value>
  </data>
  <data name="groupBoxChip.Size" type="System.Drawing.Size, System.Drawing">
    <value>264, 133</value>
  </data>
  <data name="groupBoxChip.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="groupBoxChip.Text" xml:space="preserve">
    <value>Chip encoding formula</value>
  </data>
  <data name="&gt;&gt;groupBoxChip.Name" xml:space="preserve">
    <value>groupBoxChip</value>
  </data>
  <data name="&gt;&gt;groupBoxChip.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBoxChip.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBoxChip.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="toolTip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>45</value>
  </metadata>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>634, 448</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterParent</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Document Properties</value>
  </data>
  <data name="&gt;&gt;toolTip1.Name" xml:space="preserve">
    <value>toolTip1</value>
  </data>
  <data name="&gt;&gt;toolTip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolTip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>BadgeDesignProperties</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>