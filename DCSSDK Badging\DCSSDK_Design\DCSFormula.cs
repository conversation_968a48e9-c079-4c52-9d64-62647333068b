using System;
using DCSDEV;
//using DCSSDK.CaptureMgt;
using System.Collections;
using System.Windows.Forms;

namespace DCSDEV.DCSDesign
{
	/// <summary>
	/// Summary description for DCSFormula.
	/// </summary>
	public class DCSFormula
	{
		enum TokenType { TOKEN_FIELDNAME, TOKEN_AMPERSAND, TOKEN_MINUS, TOKEN_PLUS, TOKEN_TEXT, TOKEN_NUMBER, TOKEN_PAREN, TOKEN_FUNCTION, TOKEN_COMMA, TOKEN_EQUAL, TOKEN_AND, TOKEN_OR, TOKEN_UNKNOWN };
		enum PendingFunction { COMPARE_EQ, COMPARE_NE, COMPARE_LT, COMPARE_GT, COMPARE_GE, COMPARE_LE, CONCATENATE, MATHADD, MATHMINUS, PENDING_AND, PENDING_OR }

		private static string m_strErrors;

		public DCSFormula()
		{
			//
			// TODO: Add constructor logic here
			//
		}

		public static string FormulaEval(string strFormula, DCSDEV.DCSDesign.DCSBadgeDataset badgeDataset)
		{
			string strEvaluation = "";
			if (strFormula.Length < 3) return "???";
			string strRet;
			if (strFormula.ToUpper().StartsWith("@BIN2")) 
			{
				strRet = EvaluateBIN2Formula(strFormula, badgeDataset);
			}
			else if (strFormula.StartsWith("@"))
			{
				strRet = EvaluateAtsignFormula(strFormula, badgeDataset);
			}
			else    // non @ case
			{
				bool bRet;
				m_strErrors = null;
				string strRemainder = null;
				bRet = EvaluateRegularFormula(strFormula, badgeDataset, ref strEvaluation, ref strRemainder);
				if (bRet) strRet = strEvaluation;
				else strRet = "??";
				if (m_strErrors != null) DCSMsg.Show(m_strErrors);
			}
			return strRet;
		}

		private static bool EvaluateRegularFormula(
			string strFormula, DCSDEV.DCSDesign.DCSBadgeDataset badgeDataset, ref string strEvaluation, ref string strRemainder)
		{
			PendingFunction pendingFunction = PendingFunction.CONCATENATE;
			string strLocalEval;
			string strToken;
			TokenType tokentype;
			string str = (string)strFormula.Clone();
			bool bRet;
			string strNext = "";
			bool doit = false;
			int iCompare;
			string strRemainderNull = null;

			try
			{
				while (true)
				{
					strToken = "";
					tokentype = TokenType.TOKEN_UNKNOWN;
					bRet = StripToken(ref str, ref strToken, ref tokentype);
					if (!bRet) break;

					doit = false;
					switch (tokentype)
					{
						case TokenType.TOKEN_FIELDNAME:
							strNext = badgeDataset.LookupFieldValue(strToken);
							doit = true;
							break;
						case TokenType.TOKEN_FUNCTION:
							strNext = EvaluateFunction(strToken, badgeDataset);
							doit = true;
							break;
						case TokenType.TOKEN_PAREN:
							strLocalEval = "";
							bRet = EvaluateRegularFormula(strToken, badgeDataset, ref strLocalEval, ref strRemainderNull);
							if (!bRet) return false;
							strNext = strLocalEval;
							doit = true;
							break;
						case TokenType.TOKEN_NUMBER:
						case TokenType.TOKEN_TEXT:
							strNext = strToken;
							doit = true;
							break;
						case TokenType.TOKEN_UNKNOWN:
							strNext = "??";
							doit = true;
							break;
					}
					if (doit)
					{
						if (pendingFunction == PendingFunction.CONCATENATE)
						{
							strEvaluation += strNext;
						}
						else if (pendingFunction == PendingFunction.MATHADD)
						{
							int iEvaluation = 0;
							int iNext = 0;
							try
							{
								iEvaluation = Convert.ToInt32(strEvaluation);
								iNext = Convert.ToInt32(strNext);
								strEvaluation = (iEvaluation + iNext).ToString();
							}
							catch
							{
								strEvaluation += strNext;
							}
						}
						else if (pendingFunction == PendingFunction.MATHMINUS)
						{
							int iEvaluation = 0;
							int iNext = 0;
							try
							{
								iEvaluation = Convert.ToInt32(strEvaluation);
								iNext = Convert.ToInt32(strNext);
								strEvaluation = (iEvaluation - iNext).ToString();
							}
							catch
							{
								strEvaluation += "-" + strNext;
							}
						}
						else
						{
							iCompare = strEvaluation.ToUpper().CompareTo(strNext.ToUpper());
							strEvaluation = "0";
							switch (pendingFunction)
							{
								case PendingFunction.COMPARE_EQ:
									if (iCompare == 0) strEvaluation = "1";
									break;
								case PendingFunction.COMPARE_NE:
									if (iCompare != 0) strEvaluation = "1";
									break;
								case PendingFunction.COMPARE_LT:
									if (iCompare < 0) strEvaluation = "1";
									break;
								case PendingFunction.COMPARE_GT:
									if (iCompare > 0) strEvaluation = "1";
									break;
								case PendingFunction.COMPARE_LE:
									if (!(iCompare > 0)) strEvaluation = "1";
									break;
								case PendingFunction.COMPARE_GE:
									if (!(iCompare < 0)) strEvaluation = "1";
									break;
								case PendingFunction.PENDING_AND:
									if (strEvaluation != "0" && strNext != "0") strEvaluation = "1";
									break;
								case PendingFunction.PENDING_OR:
									if (strEvaluation != "0" || strNext != "0") strEvaluation = "1";
									break;
							}
						}
						continue;
					}
					switch (tokentype)
					{
						case TokenType.TOKEN_AMPERSAND:
							pendingFunction = PendingFunction.CONCATENATE;
							break;
						case TokenType.TOKEN_PLUS:
							pendingFunction = PendingFunction.MATHADD;
							break;
						case TokenType.TOKEN_MINUS:
							pendingFunction = PendingFunction.MATHMINUS;
							break;
						case TokenType.TOKEN_EQUAL:
							if (strToken == "=") pendingFunction = PendingFunction.COMPARE_EQ;
							else if (strToken == "<>") pendingFunction = PendingFunction.COMPARE_NE;
							else if (strToken == "<") pendingFunction = PendingFunction.COMPARE_LT;
							else if (strToken == ">") pendingFunction = PendingFunction.COMPARE_GT;
							else if (strToken == "<=") pendingFunction = PendingFunction.COMPARE_LE;
							else if (strToken == "=>") pendingFunction = PendingFunction.COMPARE_GE;
							break;
						case TokenType.TOKEN_COMMA:
							if (strRemainder != null)
							{
								strRemainder = str;
								return true;
							}
							break;
						case TokenType.TOKEN_AND:
							pendingFunction = PendingFunction.PENDING_AND;
							break;
						case TokenType.TOKEN_OR:
							pendingFunction = PendingFunction.PENDING_OR;
							break;
					}
				}
			}
			catch (Exception Ex)
			{
				DCSMsg.Show("ERROR: Error evaluating expression:\n\n" + strFormula, Ex);
				strEvaluation = "??";
				return false;
			}
			return true;	// done 
		}
		private static string EvaluateFunction(string str, DCSDEV.DCSDesign.DCSBadgeDataset badgeDataset)
		{
			bool bRet;
			int idx;
			int iArgCnt = 0;
			string strArgs = null;
			string strArg1 = null;
			string strArg2 = null;
			string strArg3 = null;

			try
			{
				idx = str.IndexOf("(");
				if (idx < 0) return "??";
				string strFunction = (str.Substring(0, idx)).Trim().ToUpper();

				// strip off function and parentheses - this serves as a single argument
				strArgs = str.Substring(idx + 1, str.Length - 2 - idx);	

				// get up to three arguments
				string strRemainder = "";
				bRet = EvaluateRegularFormula(strArgs, badgeDataset, ref strArg1, ref strRemainder);
				if (bRet)
				{
					iArgCnt++;
					if (strRemainder.Length > 0)
					{
						strArgs = strRemainder;
						strRemainder = "";
						bRet = EvaluateRegularFormula(strArgs, badgeDataset, ref strArg2, ref strRemainder);
						if (bRet)
						{
							iArgCnt++;
							if (strRemainder.Length > 0)
							{
								strArgs = strRemainder;
								strRemainder = "";
								bRet = EvaluateRegularFormula(strArgs, badgeDataset, ref strArg3, ref strRemainder);
								if (bRet) iArgCnt++;
							}
						}
					}
				}

				if (strFunction == "IIF")
				{
					if (iArgCnt != 3) goto INCORRECT_ARGS;
					if (strArg1 != "0") return strArg2;
					else return strArg3;
				}
				else if (strFunction == "ISEMPTY")
				{
					if (iArgCnt != 1) goto INCORRECT_ARGS;
					return (strArg1.Length == 0 ? "1" : "0");
				}
				else if (strFunction == "ISNOTEMPTY")
				{
					if (iArgCnt != 1) goto INCORRECT_ARGS;
					return (strArg1.Length == 0 ? "0" : "1");
				}
				else if (strFunction == "LCASE")
				{
					if (iArgCnt != 1) goto INCORRECT_ARGS;
					return strArg1.ToLower();
				}
				else if (strFunction == "LEFT")
				{
					if (iArgCnt != 2) goto INCORRECT_ARGS;
					int len = System.Math.Min(strArg1.Length, Convert.ToInt32(strArg2));
					if (len == 0) return "";
					return strArg1.Substring(0, len);
				}
				else if (strFunction == "LEN")
				{
					if (iArgCnt != 1) goto INCORRECT_ARGS;
					return (strArg1.Length).ToString();
				}
				else if (strFunction == "MID")
				{
					if (iArgCnt == 2)
					{
						int pos = Convert.ToInt32(strArg2);
						if (pos <= 0) return strArg1;
						if (pos > strArg1.Length) return "";
						return strArg1.Substring(pos - 1);
					}
					else if (iArgCnt == 3)
					{
						int pos = Convert.ToInt32(strArg2);
						int len = System.Math.Min(strArg1.Length, Convert.ToInt32(strArg3));
						if ((pos + len) > strArg1.Length)
						{
							if (pos <= 0) return strArg1;
							if (pos > strArg1.Length) return "";
							return strArg1.Substring(pos - 1);
						}
						return strArg1.Substring(Convert.ToInt32(strArg2)-1, Convert.ToInt32(strArg3));
					}
					else goto INCORRECT_ARGS;
				}
				else if (strFunction == "RIGHT")
				{
					if (iArgCnt != 2) goto INCORRECT_ARGS;
					int len = System.Math.Min(strArg1.Length, Convert.ToInt32(strArg2));
					return strArg1.Substring(strArg1.Length - len);
				}
				else if (strFunction == "TRIM")
				{
					if (iArgCnt != 1) goto INCORRECT_ARGS;
					return strArg1.Trim();
				}
				else if (strFunction == "UCASE")
				{
					if (iArgCnt != 1) goto INCORRECT_ARGS;
					return strArg1.ToUpper();
				}
				else
				{
					m_strErrors = m_strErrors + "Unrecognized function " + strFunction + "\n";
					return "??";
				}

				INCORRECT_ARGS:
				m_strErrors = m_strErrors + "Incorrect number of arguments for function: " + strFunction + "\n";
				return "??";
			}
			catch (Exception Ex)
			{
				DCSMsg.Show("ERROR: Error evaluating function:\n\n" + str, Ex);
				return "??";
			}
		}

		private static bool StripToken(ref string strInOut, ref string strToken, ref TokenType tokentype)
		{
			char c;
			int idx;
			int idx2;

			if (strInOut.Length == 0) return false;

			string str = (string)strInOut.Clone();
			// remove initial spaces
			str = str.Trim();
			if (str.Length == 0) return false;

			c = str[0];
			switch (c)
			{
				case '[':
					idx = str.IndexOf("]");
					if (idx == -1)
					{
						m_strErrors = m_strErrors + "Cannot find matching close brace.\n";
						return false;
					}
					strToken = (str.Substring(1, idx - 1)).Trim();
					strInOut = str.Substring(idx + 1);
					tokentype = TokenType.TOKEN_FIELDNAME;
					return true;
				case '\"':
					idx = str.IndexOf("\"", 1);
					if (idx == -1)
					{
						m_strErrors = m_strErrors + "Cannot find matching close quote.\n";
						return false;
					}
					strToken = str.Substring(1, idx - 1);
					strInOut = str.Substring(idx + 1);
					tokentype = TokenType.TOKEN_TEXT;
					return true;
				case '&':
					strToken = str.Substring(0, 1);
					strInOut = str.Substring(1);
					tokentype = TokenType.TOKEN_AMPERSAND;
					return true;
				case '+':
					strToken = str.Substring(0, 1);
					strInOut = str.Substring(1);
					tokentype = TokenType.TOKEN_PLUS;
					return true;
				case '-':
					strToken = str.Substring(0, 1);
					strInOut = str.Substring(1);
					tokentype = TokenType.TOKEN_MINUS;
					return true;
				case ',':
					strToken = str.Substring(0, 1);
					strInOut = str.Substring(1);
					tokentype = TokenType.TOKEN_COMMA;
					return true;
				case '=':
					strToken = str.Substring(0, 1);
					strInOut = str.Substring(1);
					tokentype = TokenType.TOKEN_EQUAL;
					return true;
				case '<':
					if (str.Length >= 2 && str[1] == '>')	// <>
					{
						strToken = str.Substring(0, 2);
						strInOut = str.Substring(2);
						tokentype = TokenType.TOKEN_EQUAL;
						return true;
					}
					else if (str.Length >= 2 && str[1] == '=')	// <=
					{
						strToken = str.Substring(0, 2);
						strInOut = str.Substring(2);
						tokentype = TokenType.TOKEN_EQUAL;
						return true;
					}
					else					// <
					{
						strToken = str.Substring(0, 1);
						strInOut = str.Substring(1);
						tokentype = TokenType.TOKEN_EQUAL;
						return true;
					}
				case '>':
					if (str.Length >= 2 && str[1] == '=')	// <=
					{
						strToken = str.Substring(0, 2);
						strInOut = str.Substring(2);
						tokentype = TokenType.TOKEN_EQUAL;
						return true;
					}
					else					// >
					{
						strToken = str.Substring(0, 1);
						strInOut = str.Substring(1);
						tokentype = TokenType.TOKEN_EQUAL;
						return true;
					}
				case '(':
					idx2 = FindMatchingParen(str, 0);
					if (idx2 == -1) return false;
					strToken = (str.Substring(1, idx2 + 1 - 2)).Trim();
					strInOut = str.Substring(idx2 + 1);
					tokentype = TokenType.TOKEN_PAREN;
					return true;
				case '0':
				case '1':
				case '2':
				case '3':
				case '4':
				case '5':
				case '6':
				case '7':
				case '8':
				case '9':
					for (idx = 1; idx < str.Length; idx++)
					{
						if ("0123456789".IndexOf(str[idx]) < 0)
						{
							strToken = str.Substring(0, idx);
							strInOut = str.Substring(idx);
							tokentype = TokenType.TOKEN_NUMBER;
							return true;
						}
						continue;
					}
					strToken = str.Substring(0, idx);
					strInOut = str.Substring(idx);
					tokentype = TokenType.TOKEN_NUMBER;
					return true;
				default:
					if (Char.IsLetter(str[0]))
					{
						int k;
						for (k = 1; k < str.Length; k++)
						{
							if (Char.IsLetter(str[k])) continue;
							else break;
						}
						string strTest = str.Substring(0, k).ToUpper();
						if (strTest == "AND")
						{
							strToken = str.Substring(0, k);
							strInOut = str.Substring(k);
							tokentype = TokenType.TOKEN_AND;
							return true;
						}
						if (strTest == "OR")
						{
							strToken = str.Substring(0, k);
							strInOut = str.Substring(k);
							tokentype = TokenType.TOKEN_OR;
							return true;
						}

						idx = str.IndexOf("(");
						if (idx == -1)
						{
							m_strErrors = m_strErrors + "No parens ( ) after function '" + str + "'.\n";
							return false;
						}
						idx2 = FindMatchingParen(str, idx);
						if (idx2 == -1) return false;
						strToken = (str.Substring(0, idx2 + 1)).Trim();
						strInOut = str.Substring(idx2 + 1);
						tokentype = TokenType.TOKEN_FUNCTION;
						return true;
					}
					m_strErrors = m_strErrors + "Illegal character in string: " + str + "\n";
					return false;
			}
			//return false;
		}

		private static int FindMatchingParen(string str, int iStart)
		{
			int ctr = 0;
			for (int i = iStart + 1; i < str.Length; i++ )
			{
				if (str[i] == ')')
				{
					if (ctr == 0) return i;
					ctr--;
				}
				else if (str[i] == '(') ctr++;
			}
			m_strErrors = m_strErrors + "Cannot find matching close paren.\n";
			return -1;
		}

		private static string EvaluateAtsignFormula(string strFormula, DCSDEV.DCSDesign.DCSBadgeDataset badgeDataset)
		{
			string strEval = "";
			string strFormulaUC = strFormula.ToUpper().Trim();

			if (strFormulaUC == "@BADGE.DAT")
			{
				if (System.IO.File.Exists(badgeDataset.m_strBadgeDatFilename))
				{
					System.IO.StreamReader stream = new System.IO.StreamReader(badgeDataset.m_strBadgeDatFilename);
					strEval = stream.ReadToEnd();
					stream.Close();
				}
			}
			return strEval;
		}

		private static string EvaluateBIN2Formula(string strFormula, DCSDEV.DCSDesign.DCSBadgeDataset badgeDataset)
		{
			ArrayList arrayBINs = new ArrayList();
				
			////////////////////////////////////////////////////
			// get all BINS for primary and secondary fingers //
			////////////////////////////////////////////////////
            string strDocBin = DCSDatabaseIF.GetDocumentBIN(badgeDataset.m_strFingerprintName, badgeDataset.m_strDocumentName);
			if (strDocBin.StartsWith("ERROR")) return strDocBin;

			string strFormulaUC = strFormula.ToUpper().Trim();

			if (strFormulaUC == "@BIN2_ALL")
				return strDocBin;	// return all BINs

			/////////////////////////////////////////////////////////////////
			// Scan and categorize all BINs wrt Primary/Seondary and LS/AS //
			/////////////////////////////////////////////////////////////////
			int posOffset = 0;
			int posStart;
			int posEnd;
			int lenHeader = 0;
			int iPrimaryLS = -1, iPrimaryAS = -1;
			int iPrimarySubclass = -1;
			int iSecondaryLS = -1, iSecondaryAS = -1;
			int idx = 0;
			while (true)
			{
				posStart = strDocBin.ToUpper().IndexOf("BIN=", posOffset);	// dont know why scanned pdf417 can come back lower case
				if (posStart < 0) break;
				if (posOffset == 0) lenHeader = posStart;
				posEnd = strDocBin.IndexOf(";", posStart+1);
				if (posEnd < 0) break;
				arrayBINs.Add(strDocBin.Substring(posStart, posEnd - posStart + 1));
				posOffset = posEnd+1;
				if (iPrimarySubclass == -1) iPrimarySubclass = strDocBin[posStart+9] - '0';
				if (iPrimarySubclass == strDocBin[posStart+9] - '0')
				{
					if (strDocBin.Substring(posStart+4, 2) == "LS" || strDocBin.Substring(posStart+4, 2) == "ls") iPrimaryLS = idx;
					else if (strDocBin.Substring(posStart+4, 2) == "AS" || strDocBin.Substring(posStart+4, 2) == "as") iPrimaryAS = idx;
				}
				else
				{
					if (strDocBin.Substring(posStart+4, 2) == "LS" || strDocBin.Substring(posStart+4, 2) == "ls") iSecondaryLS = idx;
					else if (strDocBin.Substring(posStart+4, 2) == "AS" || strDocBin.Substring(posStart+4, 2) == "as") iSecondaryAS = idx;
				}
				idx++;
				continue;
			}

			// check count of BINs for consistancy
			int iCount = 0;
			if (iPrimaryLS != -1) iCount++;
			if (iPrimaryAS != -1) iCount++;
			if (iSecondaryLS != -1) iCount++;
			if (iSecondaryAS != -1) iCount++;
            if (iCount == 0 || iCount != arrayBINs.Count) return strDocBin;   // allow Doc= with no bins following 
			string strAssem = strDocBin.Substring(0, lenHeader);

			// @BIN2_L1		one Liska BIN
			if (strFormulaUC == "@BIN2_L1")
			{
				iPrimaryAS = -1;
				iSecondaryAS = -1;
				if (iPrimaryLS != -1) iSecondaryLS = -1;
				else if (iPrimaryLS == -1 && iSecondaryLS == -1) return "ERROR: cannot parse BINs";
			}
			// @BIN2_L2		2 Liska BIN2s or less
			else if (strFormulaUC == "@BIN2_L2")
			{
				iPrimaryAS = -1;
				iSecondaryAS = -1;
				if (iPrimaryLS == -1 && iSecondaryLS == -1) return "ERROR: cannot parse BINs";
			}
			// @BIN2_A1		2 Liska BINs or 1 AIS BIN or less
			else if (strFormulaUC == "@BIN2_A1")
			{
				if (iPrimaryLS == -1 && iSecondaryLS == -1)
				{
					if (iPrimaryAS != -1 && iSecondaryAS != -1) iSecondaryAS = -1;
				}
				else iPrimaryAS = iSecondaryAS = -1;
			}
			// @BIN2_L1A1		2 Liska BINs or 1 Liska Bin and 1 AIS BIN less
			else if (strFormulaUC == "@BIN2_L1A1")
			{
				if (iPrimaryLS != -1 && iSecondaryLS != -1) { iPrimaryAS = -1; iSecondaryAS = -1; }
				else if (iPrimaryLS != -1 && iSecondaryAS != -1) { iPrimaryAS = -1; }
				else if (iPrimaryAS != -1 && iSecondaryLS != -1) { iSecondaryAS = -1; }
				else if (iPrimaryAS != -1) iSecondaryAS = -1;
			}
			// @BIN2_L2A1		2 Liska BINs and 1 AIS BIN
			else if (strFormulaUC == "@BIN2_L2A1")
			{
				if (iPrimaryLS != -1 && iSecondaryLS != -1)
				{
					if (iPrimaryAS != -1) iSecondaryAS = -1;
				}
				else if (iPrimaryLS != -1) iPrimaryAS = -1;
				else if (iSecondaryLS != -1) iSecondaryAS = -1;
				else if (iPrimaryAS != -1) iSecondaryAS = -1;
			}
			// @BIN2_A2		2 AIS BINs or less
			else if (strFormulaUC == "@BIN2_A2")
			{
				if (iPrimaryLS != -1 && iSecondaryLS != -1)
				{
					if (iPrimaryAS != -1) iSecondaryAS = -1;
				}
				else if (iPrimaryLS != -1) iPrimaryAS = -1;
				else if (iSecondaryLS != -1) iSecondaryAS = -1;
			}

			if (iPrimaryLS != -1) strAssem = strAssem + (string)arrayBINs[iPrimaryLS];
			if (iPrimaryAS != -1) strAssem = strAssem + (string)arrayBINs[iPrimaryAS];
			if (iSecondaryLS != -1) strAssem = strAssem + (string)arrayBINs[iSecondaryLS];
			if (iSecondaryAS != -1) strAssem = strAssem + (string)arrayBINs[iSecondaryAS];
			return  strAssem;
		}
	}
}
