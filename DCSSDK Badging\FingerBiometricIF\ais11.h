/*****************************************************************************
 *
 * NAME:        ais11.h
 *
 * SYNOPSIS     public include file for the OTO (one-to-one) toolkit component
 *
 * VERSION:     1.4
 *
 * DATE:        98/07/27
 *
 * COPYRIGHT:   IBM Corporation, 1998
 *
 *
 ******************************************************************************/


/* prevent multiple inclusion */
#ifndef AIS11_H
#define AIS11_H 

/* handle name mangling for any C++ user */
#if defined(__cplusplus)
extern "C" {
#endif

#include <windows.h>
#define DllImport	__declspec( dllimport )
#define DllExport	__declspec( dllexport )
#define AISAPI __stdcall
 
typedef void *HAIS;

/* Return codes */
#define AIS_RC_OK                           0
#define AIS_RC_INVALID_HANDLE               1
#define AIS_RC_BUFFER_TOO_SMALL             2
#define AIS_RC_NOT_A_VALID_PACKED_OBJECT    3
#define AIS_RC_INVALID_BMP                  4
#define AIS_RC_NO_MATCH                     5
#define AIS_RC_INVALID_IMAGE_TYPE           6
#define AIS_RC_NO_FINGERPRINT               7
#define AIS_RC_MEMORY_ERROR                 8
#define AIS_RC_INVALID_INPUT                9
#define AIS_RC_TOO_FEW_MINUTIAE            10
#define AIS_RC_NO_CORE                     11
#define AIS_RC_UNABLE_TO_EXTRACT           12  // WARNING - AisRCToString() relies on 
                                               // AIS_RC_UNABLE_TO_EXTRACT being the
                                               // last entry in this list 

/* Image types */
#define AIS_IMG_WINDOWS_BMP                 1

/* Quality directions */
#define AIS_DIRECTION_UNKNOWN               0
#define AIS_DIRECTION_UP                    1
#define AIS_DIRECTION_DOWN                  2
#define AIS_DIRECTION_LEFT                  3
#define AIS_DIRECTION_RIGHT                 4
#define AIS_DIRECTION_UP_LEFT               5
#define AIS_DIRECTION_UP_RIGHT              6
#define AIS_DIRECTION_DOWN_LEFT             7
#define AIS_DIRECTION_DOWN_RIGHT            8
#define AIS_DIRECTION_CENTERED              9


/******************************************************************
// The following four calls support the minimum functionality
// for 1:1 matching
******************************************************************/

DllExport HAIS AISAPI AisCreateVerifyTemplate(IN  const char *pImage,
                                              IN  const unsigned long ImageType,
                                              OUT unsigned long *pRC);


DllExport unsigned long AISAPI AisMatchTemplates(IN  const HAIS haisTemplate1,
						                                     IN  const HAIS haisTemplate2,
	    					                                 OUT unsigned long *pRC);


DllExport void AISAPI AisFree(IN  HAIS hAisObject,
	    					              OUT unsigned long *pRC);


DllExport const char * AISAPI AisRCToString(IN const unsigned long RC);


/*******************************************************************
// The following calls support the functionality for saving the
// verify template to a file or sending over the network
*******************************************************************/

DllExport unsigned long AISAPI AisGetPackedSize(IN  const HAIS hAisObject,
						                                    OUT unsigned long *pRC);

DllExport void AISAPI AisPack(IN  const HAIS hAisObject,
						                  IN  const unsigned long uBufferLength,
						                  OUT char *pBuffer,
						                  OUT unsigned long *pRC);

DllExport HAIS AISAPI AisUnpack(IN  const char *pBuffer,
						                    OUT unsigned long *pRC);


/*******************************************************************
// The following calls extract data from a fingerprint template
*******************************************************************/

DllExport unsigned long AISAPI AisTemplateGetNumMinutiae(IN  const HAIS hAis,
                                                         OUT unsigned long *pRC);

DllExport void AISAPI AisTemplateGetMinutiaeData(IN  const HAIS hAis,
                                                 OUT unsigned long *xValues,
                                                 OUT unsigned long *yValues,
                                                 OUT unsigned long *angles,
                                                 OUT unsigned long *pRC);

DllExport void AISAPI AisTemplateGetCoreData(IN  const HAIS hAis,
                                             OUT unsigned long *xValue,
                                             OUT unsigned long *yValue,
                                             OUT unsigned long *pRC);

 
                                                                                 
/*******************************************************************
// The following call will determine whether there is a fingerprint
// image within the image.
// Use this within a loop to determine when to extract a template
// or when to call a quality function
*******************************************************************/

DllExport unsigned long AISAPI AisIsFingerPresent(IN  const char *pImage,
						                                      IN  const unsigned long uImageFormat,
						                                      OUT unsigned long *pRC);


/*******************************************************************
// The following calls support quality checking of fingerprint images
*******************************************************************/

DllExport HAIS AISAPI AisCreateQualityInfo(IN  const char *pImage,
                                           IN  const unsigned long ImageFormat,
                                           OUT unsigned long *pRC);

DllExport unsigned long AISAPI AisQualityGetGeneralValue(IN  const HAIS hAis,
                                                         OUT unsigned long *pRC);

DllExport unsigned long AISAPI AisQualityGetDrynessValue(IN  const HAIS hAis,
                                                         OUT unsigned long *pRC);

DllExport unsigned long AISAPI AisQualityGetSmudgenessValue(IN  const HAIS hAis,
                                                            OUT unsigned long *pRC);

DllExport unsigned long AISAPI AisQualityGetOverallValue(IN  const HAIS hAis,
                                                         OUT unsigned long *pRC);

DllExport long AISAPI AisQualityGetOrientationValue(IN  const HAIS hAis,
                                                    OUT unsigned long *pRC);

DllExport unsigned long AISAPI AisQualityGetTranslateValue(IN  const HAIS hAis,
                                                           OUT unsigned long *pRC);

DllExport unsigned long AISAPI AisQualityGetTranslateDirection(IN  const HAIS hAis,
                                                               OUT unsigned long *pRC);

DllExport unsigned long AISAPI AisQualityGetRollValue(IN  const HAIS hAis,
                                                      OUT unsigned long *pRC);

DllExport unsigned long AISAPI AisQualityGetRollDirection(IN  const HAIS hAis,
                                                          OUT unsigned long *pRC);


#if defined(__cplusplus)
}
#endif


#endif /*AIS_TOOLKIT_H */
