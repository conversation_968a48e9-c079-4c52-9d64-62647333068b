using System;
using System.Collections;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Windows.Forms;

namespace DCSDEV.DCSDesigner
{
	/// <summary>
	/// Summary description for DCSMagStripeProperties.
	/// </summary>
    public class DCSMagStripeProperties : System.Windows.Forms.UserControl
	{
		ArrayList m_AllDBFieldNames;

		private System.Windows.Forms.Button buttonEditTrack1Formula;
		private System.Windows.Forms.ComboBox comboTrack1;
		private System.Windows.Forms.CheckBox checkTrack1;
		private System.Windows.Forms.Button buttonEditTrack3Formula;
		private System.Windows.Forms.ComboBox comboTrack3;
		private System.Windows.Forms.CheckBox checkTrack3;
		private System.Windows.Forms.Button buttonEditTrack2Formula;
		private System.Windows.Forms.ComboBox comboTrack2;
		private System.Windows.Forms.CheckBox checkTrack2;
		private System.Windows.Forms.TextBox tbTrack1;
		private System.Windows.Forms.TextBox tbTrack2;
        private System.Windows.Forms.TextBox tbTrack3;
        private Label labelTk3Comment;
        private Label labelTk2Comment;
        private Label labelTk1Comment;
		/// <summary> 
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		public DCSMagStripeProperties()
		{
			// This call is required by the Windows.Forms Form Designer.
			InitializeComponent();

			// TODO: Add any initialization after the InitializeComponent call
			m_AllDBFieldNames = new ArrayList();
		}

		/// <summary> 
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Component Designer generated code
		/// <summary> 
		/// Required method for Designer support - do not modify 
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            this.buttonEditTrack1Formula = new System.Windows.Forms.Button();
            this.comboTrack1 = new System.Windows.Forms.ComboBox();
            this.checkTrack1 = new System.Windows.Forms.CheckBox();
            this.buttonEditTrack3Formula = new System.Windows.Forms.Button();
            this.comboTrack3 = new System.Windows.Forms.ComboBox();
            this.checkTrack3 = new System.Windows.Forms.CheckBox();
            this.buttonEditTrack2Formula = new System.Windows.Forms.Button();
            this.comboTrack2 = new System.Windows.Forms.ComboBox();
            this.checkTrack2 = new System.Windows.Forms.CheckBox();
            this.tbTrack1 = new System.Windows.Forms.TextBox();
            this.tbTrack2 = new System.Windows.Forms.TextBox();
            this.tbTrack3 = new System.Windows.Forms.TextBox();
            this.labelTk3Comment = new System.Windows.Forms.Label();
            this.labelTk2Comment = new System.Windows.Forms.Label();
            this.labelTk1Comment = new System.Windows.Forms.Label();
            this.SuspendLayout();
            // 
            // buttonEditTrack1Formula
            // 
            this.buttonEditTrack1Formula.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.buttonEditTrack1Formula.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.buttonEditTrack1Formula.Location = new System.Drawing.Point(217, 49);
            this.buttonEditTrack1Formula.Name = "buttonEditTrack1Formula";
            this.buttonEditTrack1Formula.Size = new System.Drawing.Size(20, 20);
            this.buttonEditTrack1Formula.TabIndex = 2;
            this.buttonEditTrack1Formula.Text = ">";
            this.buttonEditTrack1Formula.Click += new System.EventHandler(this.buttonEditTrack1Formula_Click);
            // 
            // comboTrack1
            // 
            this.comboTrack1.Items.AddRange(new object[] {
            "MagTrack1",
            "Formula"});
            this.comboTrack1.Location = new System.Drawing.Point(50, 25);
            this.comboTrack1.Name = "comboTrack1";
            this.comboTrack1.Size = new System.Drawing.Size(104, 21);
            this.comboTrack1.TabIndex = 1;
            this.comboTrack1.Text = "MagTrack1";
            this.comboTrack1.SelectedIndexChanged += new System.EventHandler(this.comboTrack1_SelectedIndexChanged);
            // 
            // checkTrack1
            // 
            this.checkTrack1.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.checkTrack1.Location = new System.Drawing.Point(0, 0);
            this.checkTrack1.Name = "checkTrack1";
            this.checkTrack1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.checkTrack1.Size = new System.Drawing.Size(63, 24);
            this.checkTrack1.TabIndex = 0;
            this.checkTrack1.Text = "Track1";
            this.checkTrack1.Click += new System.EventHandler(this.checkTrack1_Click);
            // 
            // buttonEditTrack3Formula
            // 
            this.buttonEditTrack3Formula.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.buttonEditTrack3Formula.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.buttonEditTrack3Formula.Location = new System.Drawing.Point(217, 187);
            this.buttonEditTrack3Formula.Name = "buttonEditTrack3Formula";
            this.buttonEditTrack3Formula.Size = new System.Drawing.Size(20, 20);
            this.buttonEditTrack3Formula.TabIndex = 10;
            this.buttonEditTrack3Formula.Text = ">";
            this.buttonEditTrack3Formula.Click += new System.EventHandler(this.buttonEditTrack3Formula_Click);
            // 
            // comboTrack3
            // 
            this.comboTrack3.ItemHeight = 13;
            this.comboTrack3.Items.AddRange(new object[] {
            "MagTrack3",
            "Formula"});
            this.comboTrack3.Location = new System.Drawing.Point(50, 163);
            this.comboTrack3.Name = "comboTrack3";
            this.comboTrack3.Size = new System.Drawing.Size(104, 21);
            this.comboTrack3.TabIndex = 9;
            this.comboTrack3.Text = "MagTrack3";
            this.comboTrack3.SelectedIndexChanged += new System.EventHandler(this.comboTrack3_SelectedIndexChanged);
            // 
            // checkTrack3
            // 
            this.checkTrack3.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.checkTrack3.Location = new System.Drawing.Point(0, 143);
            this.checkTrack3.Name = "checkTrack3";
            this.checkTrack3.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.checkTrack3.Size = new System.Drawing.Size(63, 24);
            this.checkTrack3.TabIndex = 8;
            this.checkTrack3.Text = "Track3";
            this.checkTrack3.Click += new System.EventHandler(this.checkTrack3_Click);
            // 
            // buttonEditTrack2Formula
            // 
            this.buttonEditTrack2Formula.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.buttonEditTrack2Formula.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.buttonEditTrack2Formula.Location = new System.Drawing.Point(217, 119);
            this.buttonEditTrack2Formula.Name = "buttonEditTrack2Formula";
            this.buttonEditTrack2Formula.Size = new System.Drawing.Size(20, 20);
            this.buttonEditTrack2Formula.TabIndex = 6;
            this.buttonEditTrack2Formula.Text = ">";
            this.buttonEditTrack2Formula.Click += new System.EventHandler(this.buttonEditTrack2Formula_Click);
            // 
            // comboTrack2
            // 
            this.comboTrack2.ItemHeight = 13;
            this.comboTrack2.Items.AddRange(new object[] {
            "MagTrack2",
            "Formula"});
            this.comboTrack2.Location = new System.Drawing.Point(50, 95);
            this.comboTrack2.Name = "comboTrack2";
            this.comboTrack2.Size = new System.Drawing.Size(104, 21);
            this.comboTrack2.TabIndex = 5;
            this.comboTrack2.Text = "MagTrack2";
            this.comboTrack2.SelectedIndexChanged += new System.EventHandler(this.comboTrack2_SelectedIndexChanged);
            // 
            // checkTrack2
            // 
            this.checkTrack2.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.checkTrack2.Location = new System.Drawing.Point(0, 73);
            this.checkTrack2.Name = "checkTrack2";
            this.checkTrack2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.checkTrack2.Size = new System.Drawing.Size(63, 24);
            this.checkTrack2.TabIndex = 4;
            this.checkTrack2.Text = "Track2";
            this.checkTrack2.Click += new System.EventHandler(this.checkTrack2_Click);
            // 
            // tbTrack1
            // 
            this.tbTrack1.Location = new System.Drawing.Point(50, 49);
            this.tbTrack1.Name = "tbTrack1";
            this.tbTrack1.Size = new System.Drawing.Size(164, 20);
            this.tbTrack1.TabIndex = 3;
            // 
            // tbTrack2
            // 
            this.tbTrack2.Location = new System.Drawing.Point(50, 119);
            this.tbTrack2.Name = "tbTrack2";
            this.tbTrack2.Size = new System.Drawing.Size(164, 20);
            this.tbTrack2.TabIndex = 7;
            // 
            // tbTrack3
            // 
            this.tbTrack3.Location = new System.Drawing.Point(50, 187);
            this.tbTrack3.Name = "tbTrack3";
            this.tbTrack3.Size = new System.Drawing.Size(164, 20);
            this.tbTrack3.TabIndex = 11;
            this.tbTrack3.TextChanged += new System.EventHandler(this.tbTrack3_TextChanged);
            // 
            // labelTk3Comment
            // 
            this.labelTk3Comment.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.labelTk3Comment.Location = new System.Drawing.Point(69, 147);
            this.labelTk3Comment.Name = "labelTk3Comment";
            this.labelTk3Comment.Size = new System.Drawing.Size(168, 14);
            this.labelTk3Comment.TabIndex = 21;
            this.labelTk3Comment.Text = "104 numeric or = (76 a/n AAMVA)";
            // 
            // labelTk2Comment
            // 
            this.labelTk2Comment.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.labelTk2Comment.Location = new System.Drawing.Point(69, 78);
            this.labelTk2Comment.Name = "labelTk2Comment";
            this.labelTk2Comment.Size = new System.Drawing.Size(154, 15);
            this.labelTk2Comment.TabIndex = 20;
            this.labelTk2Comment.Text = "37 numeric chars or =";
            // 
            // labelTk1Comment
            // 
            this.labelTk1Comment.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.labelTk1Comment.Location = new System.Drawing.Point(69, 5);
            this.labelTk1Comment.Name = "labelTk1Comment";
            this.labelTk1Comment.Size = new System.Drawing.Size(154, 17);
            this.labelTk1Comment.TabIndex = 19;
            this.labelTk1Comment.Text = "76 alphanumeric chars or ^";
            // 
            // DCSMagStripeProperties
            // 
            this.Controls.Add(this.labelTk3Comment);
            this.Controls.Add(this.labelTk2Comment);
            this.Controls.Add(this.labelTk1Comment);
            this.Controls.Add(this.tbTrack3);
            this.Controls.Add(this.tbTrack2);
            this.Controls.Add(this.tbTrack1);
            this.Controls.Add(this.buttonEditTrack3Formula);
            this.Controls.Add(this.comboTrack3);
            this.Controls.Add(this.checkTrack3);
            this.Controls.Add(this.buttonEditTrack2Formula);
            this.Controls.Add(this.comboTrack2);
            this.Controls.Add(this.checkTrack2);
            this.Controls.Add(this.buttonEditTrack1Formula);
            this.Controls.Add(this.comboTrack1);
            this.Controls.Add(this.checkTrack1);
            this.Name = "DCSMagStripeProperties";
            this.Size = new System.Drawing.Size(240, 215);
            this.ResumeLayout(false);
            this.PerformLayout();

		}
		#endregion

		private void SetVisibilities()
		{
			this.comboTrack1.Enabled = this.checkTrack1.Checked;
			this.tbTrack1.Visible = this.buttonEditTrack1Formula.Visible = (this.checkTrack1.Checked && this.comboTrack1.Text == "Formula");

			this.comboTrack2.Enabled = this.checkTrack2.Checked;
			this.tbTrack2.Visible = this.buttonEditTrack2Formula.Visible = (this.checkTrack2.Checked && this.comboTrack2.Text == "Formula");

			this.comboTrack3.Enabled = this.checkTrack3.Checked;
			this.tbTrack3.Visible = this.buttonEditTrack3Formula.Visible = (this.checkTrack3.Checked && this.comboTrack3.Text == "Formula");
		}

		private void checkTrack1_Click(object sender, System.EventArgs e)
		{
			SetVisibilities();
		}

		private void checkTrack2_Click(object sender, System.EventArgs e)
		{
			SetVisibilities();
		}

		private void checkTrack3_Click(object sender, System.EventArgs e)
		{
			SetVisibilities();
		}

		private void buttonEditTrack1Formula_Click(object sender, System.EventArgs e)
		{
			string strFormula = this.tbTrack1.Text;
			DCSDEV.DCSDesigner.DCSFormulaDesigner.CallFormulaBuilder(m_AllDBFieldNames, ref strFormula, DCSFormulaDesigner.FormulaModeType.ALL_MODES);
			if (strFormula != null) this.tbTrack1.Text = strFormula;
		}

		private void buttonEditTrack2Formula_Click(object sender, System.EventArgs e)
		{
			string strFormula = this.tbTrack2.Text;
			DCSDEV.DCSDesigner.DCSFormulaDesigner.CallFormulaBuilder(m_AllDBFieldNames, ref strFormula, DCSFormulaDesigner.FormulaModeType.ALL_MODES);
			if (strFormula != null) this.tbTrack2.Text = strFormula;
		}

		private void buttonEditTrack3Formula_Click(object sender, System.EventArgs e)
		{
			string strFormula = this.tbTrack3.Text;
			DCSDEV.DCSDesigner.DCSFormulaDesigner.CallFormulaBuilder(m_AllDBFieldNames, ref strFormula, DCSFormulaDesigner.FormulaModeType.ALL_MODES);
			if (strFormula != null) this.tbTrack3.Text = strFormula;
		}

		private void comboTrack1_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			SetVisibilities();
		}

		private void comboTrack2_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			SetVisibilities();
		}

		private void comboTrack3_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			SetVisibilities();
		}

		private void tbTrack3_TextChanged(object sender, System.EventArgs e)
		{
		
		}

		public bool IfTrack1
		{
			get
			{
				return this.checkTrack1.Checked;
			}
			set
			{
				this.checkTrack1.Checked = value;
				SetVisibilities();
			}
		}
		public bool IfTrack2
		{
			get
			{
				return this.checkTrack2.Checked;
			}
			set
			{
				this.checkTrack2.Checked = value;
				SetVisibilities();
			}
		}
		public bool IfTrack3
		{
			get
			{
				return this.checkTrack3.Checked;
			}
			set
			{
				this.checkTrack3.Checked = value;
				SetVisibilities();
			}
		}

		public string SourceTrack1
		{
			get
			{
				return this.comboTrack1.Text;
			}
			set
			{
				this.comboTrack1.Text = value;
				SetVisibilities();
			}
		}
		public string SourceTrack2
		{
			get
			{
				return this.comboTrack2.Text;
			}
			set
			{
				this.comboTrack2.Text = value;
				SetVisibilities();
			}
		}
		public string SourceTrack3
		{
			get
			{
				return this.comboTrack3.Text;
			}
			set
			{
				this.comboTrack3.Text = value;
				SetVisibilities();
			}
		}

		public string FormulaTrack1
		{
			get
			{
				return this.tbTrack1.Text;
			}
			set
			{
				this.tbTrack1.Text = value;
			}
		}
		public string FormulaTrack2
		{
			get
			{
				return this.tbTrack2.Text;
			}
			set
			{
				this.tbTrack2.Text = value;
			}
		}
		public string FormulaTrack3
		{
			get
			{
				return this.tbTrack3.Text;
			}
			set
			{
				this.tbTrack3.Text = value;
			}
		}

		public bool IfTrack1Visible
		{
			get
			{
				return this.checkTrack1.Visible;
			}
			set
			{
				this.tbTrack1.Visible = 
					this.comboTrack1.Visible = 
					this.buttonEditTrack1Formula.Visible =
					this.checkTrack1.Visible = value;
				SetVisibilities();
			}
		}
		public bool IfTrack2Visible
		{
			get
			{
				return this.checkTrack2.Visible;
			}
			set
			{
				this.tbTrack2.Visible = 
					this.comboTrack2.Visible = 
					this.buttonEditTrack2Formula.Visible =
					this.checkTrack2.Visible = value;
				SetVisibilities();
			}
		}
		public bool IfTrack3Visible
		{
			get
			{
				return this.checkTrack3.Checked;
			}
			set
			{
				this.tbTrack3.Visible = 
					this.comboTrack3.Visible = 
					this.buttonEditTrack3Formula.Visible =
					this.checkTrack3.Visible = value;
				SetVisibilities();
			}
		}

		public ArrayList AllDBFieldNames
		{
			set
			{
				this.comboTrack1.Items.Clear();
				this.comboTrack1.Items.Add("Formula");
				foreach(string str in value) this.comboTrack1.Items.Add(str);

				this.comboTrack2.Items.Clear();
				this.comboTrack2.Items.Add("Formula");
				foreach(string str in value) this.comboTrack2.Items.Add(str);
				
				this.comboTrack3.Items.Clear();
				this.comboTrack3.Items.Add("Formula");
				foreach(string str in value) this.comboTrack3.Items.Add(str);

				foreach(string str in value) m_AllDBFieldNames.Add(str);
			}
		}
	}
}
