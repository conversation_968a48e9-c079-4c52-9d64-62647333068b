using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Data.SqlClient;

namespace DCSDEV.FilesToOle
{
    public partial class FilesToOLE : Form
    {
        private int m_iCurrentImageDBSize;
        private ParameterStore m_ps;
        public string m_strImageDBType;    // old type until change is accepted
        private bool m_ServerNamesPopulated = false;

        public FilesToOLE(ParameterStore ps, string strDataRootDirectory)
        {
            InitializeComponent();

            m_ps = ps;
            m_strImageDBType = "FILES";

            // check on sizes of current and new image DBs
            m_iCurrentImageDBSize = DCSDatabaseIF.GetImageDBSize();
            this.groupBoxExistingFiles.Visible = (m_iCurrentImageDBSize > 0);
            this.labelDataRootDirectory.Text = strDataRootDirectory;
            char[] Separator = { '\\' };
            string[] strDRD = strDataRootDirectory.Split(Separator, StringSplitOptions.RemoveEmptyEntries);
            this.comboBoxDBCatalog.Text = strDRD[strDRD.Length-1] + "_SDSImages";

            int nImageInUsersDB = Convert.ToInt32(m_ps.GetStringParameter("DCSSDK_UsersDB", "UsersDBHasImages", "0"));
            string strFields = "none"; ;
            if ((nImageInUsersDB & 1) == 1) strFields = "Portrait; ";
            if ((nImageInUsersDB & 2) == 2) strFields += "Signature; ";
            this.labelUsersDBFields.Text = strFields;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void buttonAccept_Click(object sender, EventArgs e)
        {
            string strNewImageDBType = null;
            bool bCopy = false;
            bool bDelete = false;
            int iNewImageDBSize;

            if (this.radioButtonAccess.Checked)
                strNewImageDBType = "ACCESS";
            else if (this.radioButtonSQL.Checked)
                strNewImageDBType = "SQL";
            else if (this.radioButtonOracle.Checked)
                strNewImageDBType = "ORACLE";
            else
            {
                DCSMsg.Show("Must select a value of OLE database Type.");
                return;
            }

            if (m_iCurrentImageDBSize > 0)
            {
                if (this.radioButtonCopyFiles.Checked)
                {
                    bCopy = true;
                    bDelete = false;
                }
                else if (this.radioButtonMoveFiles.Checked)
                {
                    bCopy = true;
                    bDelete = true;
                }
                else if (this.radioButtonDontCopy.Checked)
                {
                    bCopy = false;
                    bDelete = false;
                }
                else
                {
                    DCSMsg.Show("Must indicate how to handle existing image files.");
                    return;
                }
            }
            if (strNewImageDBType != "ACCESS")
            {
                if (this.comboBoxServerName.Text == null || this.comboBoxServerName.Text == "")
                {
                    DCSMsg.Show("You must enter a value for server name.");
                    return;
                }
                if (this.comboBoxDBCatalog.Text == null || this.comboBoxDBCatalog.Text == "")
                {
                    DCSMsg.Show("You must enter a value for database name.");
                    return;
                }
            }

            // see if new image db can be opened - and see if it is empty
            m_ps.WriteStringParameter("DCSSDK_ImageDB", "ImageDBType", strNewImageDBType);
            m_ps.WriteStringParameter("DCSSDK_ImageDB", "ImageDBDataSource", this.comboBoxServerName.Text);
            m_ps.WriteStringParameter("DCSSDK_ImageDB", "ImageDBCatalog", this.comboBoxDBCatalog.Text);
            DCSDatabaseIF.Reinit();
            iNewImageDBSize = DCSDatabaseIF.GetImageDBSize();
            if (iNewImageDBSize < 0)
            {
                m_ps.WriteStringParameter("DCSSDK_ImageDB", "ImageDBType", "FILES");
                DCSDatabaseIF.Reinit();
                return;
            }

            if (m_iCurrentImageDBSize <= 0)
            {
                m_strImageDBType = strNewImageDBType;
                this.DialogResult = DialogResult.OK;
                this.Close();
                return;
            }
            // see if operator wants to clean out the new DB - it must be emptied if copying
            if (iNewImageDBSize > 0)
            {
                if (bCopy)
                {
                    if (DCSMsg.ShowOKC("The OLE image database is not empty.  It must be emptied. OK to continue?") == DialogResult.Cancel)
                    {
                        m_ps.WriteStringParameter("DCSSDK_ImageDB", "ImageDBType", "FILES");
                        DCSDatabaseIF.Reinit();
                        return;
                    }
                    OleImageDB.EmptyImageDB();
                }
                else
                {
                    if (DCSMsg.ShowYN("The OLE image database is not empty.  Do you want to empty it?") == DialogResult.Yes)
                    {
                        OleImageDB.EmptyImageDB();
                        iNewImageDBSize = 0;
                    }
                }
            }

            if (bCopy)
            {
                // move the images
                this.labelInstructions.Visible = false;
                this.labelWait.Visible = true;

                bool bRet = DCSDatabaseIF.ConvertDBType(strNewImageDBType, bDelete);
                if (!bRet)
                {
                    m_ps.WriteStringParameter("DCSSDK_ImageDB", "ImageDBType", "FILES");
                    DCSDatabaseIF.Reinit();

                    this.labelInstructions.Visible = true;
                    this.labelWait.Visible = false;
                    return;
                }
            }

            m_strImageDBType = strNewImageDBType;
            this.DialogResult = DialogResult.OK;
            this.Close();
            return;
        }

        private void radioButtonAccess_CheckedChanged(object sender, EventArgs e)
        {
            this.DBTypeChanged();
        }

        private void radioButtonSQL_CheckedChanged(object sender, EventArgs e)
        {
            this.DBTypeChanged();
        }

        private void radioButtonOracle_CheckedChanged(object sender, EventArgs e)
        {
            this.DBTypeChanged();
        }
        private void DBTypeChanged()
        {
            bool bVisible = (!this.radioButtonAccess.Checked);

            if (bVisible && (!m_ServerNamesPopulated))
            {
                this.labelInstructions.Visible = false;
                this.labelWait.Visible = true;
                this.Refresh();

                // add options to server list
                string strLastSQL = m_ps.GetStringParameter("DCSSDK_ImageDB", "ImageDBDataSource", "");
                if (strLastSQL != "") this.comboBoxServerName.Items.Add(strLastSQL);

                System.Data.Sql.SqlDataSourceEnumerator sdse = System.Data.Sql.SqlDataSourceEnumerator.Instance;
                DataTable dt = sdse.GetDataSources();

                string strSource;
                foreach (DataRow row in dt.Rows)
                {
                    strSource = row[0].ToString() + "\\" + row[1].ToString();
                    if (strSource.ToUpper() != strLastSQL.ToUpper())
                        this.comboBoxServerName.Items.Add(strSource);
                }
                m_ServerNamesPopulated = true;

                this.labelInstructions.Visible = true;
                this.labelWait.Visible = false;
            }
            this.labelServerName.Visible = bVisible;
            this.comboBoxServerName.Visible = bVisible;

            this.labelAccessDB.Visible = !bVisible;
            this.labelAccessDBText.Visible = !bVisible;

            this.comboBoxDBCatalog.Visible = this.comboBoxServerName.Visible && this.comboBoxServerName.Text != null && this.comboBoxServerName.Text != "";
            this.labelDBName.Visible = this.comboBoxDBCatalog.Visible;

            this.labelTableName.Visible = true;
            this.labelTableNameText.Visible = true;
        }

        private void comboBoxServerName_SelectedValueChanged(object sender, EventArgs e)
        {
            if (this.comboBoxServerName.Text != null && this.comboBoxServerName.Text != "") 
                this.labelDBName.Visible = this.comboBoxDBCatalog.Visible = true;

            this.labelInstructions.Visible = false;
            this.labelWait.Visible = true;
            this.Refresh();

            System.Data.SqlClient.SqlConnection conn = new SqlConnection(String.Format("Data Source={0};Integrated Security=SSPI;", this.comboBoxServerName.Text));
            try
            {
                conn.Open();
            }
            catch
            {
                this.labelInstructions.Visible = true;
                this.labelWait.Visible = false;
                this.Refresh();
                return;
            }
            DataTable dt = conn.GetSchema("Databases");
            conn.Close();

            this.comboBoxDBCatalog.Items.Clear();
            foreach (DataRow row in dt.Rows)
            {
                this.comboBoxDBCatalog.Items.Add(row["database_name"].ToString());
            }
            this.labelInstructions.Visible = true;
            this.labelWait.Visible = false;
            this.Refresh();
        }

        private void Test_ListDatabases()
        {
            System.Data.SqlClient.SqlConnection conn = new SqlConnection(String.Format("Data Source={0};Integrated Security=SSPI;", this.comboBoxServerName.Text));
            try
            {
                conn.Open();
            }
            catch // (Exception ex)
            {
                DCSDEV.DCSMsg.Show("Cannot connect to SQL server: " + this.comboBoxServerName.Text);
                return;
            }
            DataTable dt = conn.GetSchema("Databases");
            conn.Close();

            string strDBs = "";
            foreach (DataRow row in dt.Rows)
            {
                strDBs += row["database_name"].ToString() + ";";
            }
            DCSMsg.Show(strDBs);
        }

        private void Test_CreateDB()
        {
            string strSQL = "CREATE DATABASE SDSImages;";

            // connect to any db that exists
            string strConnection = String.Format("Data Source={0};Initial Catalog={1};Integrated Security=SSPI;", this.comboBoxServerName.Text, "master");
            SqlConnection conn = new SqlConnection(strConnection);
            conn.Open();
            SqlCommand cmd = new SqlCommand(strSQL, conn);
            try
            {
                cmd.ExecuteNonQuery();
            }
            catch (SqlException ex)
            {
                DCSMsg.Show(ex);
            }
        }
    }
}