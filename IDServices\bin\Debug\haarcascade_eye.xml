<?xml version="1.0"?>
<!--
    Stump-based 20x20 frontal eye detector.
    Created by <PERSON><PERSON><PERSON> (http://umich.edu/~shameem)

////////////////////////////////////////////////////////////////////////////////////////

  IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.

  By downloading, copying, installing or using the software you agree to this license.
  If you do not agree to this license, do not download, install,
  copy or use the software.


                        Intel License Agreement
                For Open Source Computer Vision Library

 Copyright (C) 2000, Intel Corporation, all rights reserved.
 Third party copyrights are property of their respective owners.

 Redistribution and use in source and binary forms, with or without modification,
 are permitted provided that the following conditions are met:

   * Redistribution's of source code must retain the above copyright notice,
     this list of conditions and the following disclaimer.

   * Redistribution's in binary form must reproduce the above copyright notice,
     this list of conditions and the following disclaimer in the documentation
     and/or other materials provided with the distribution.

   * The name of Intel Corporation may not be used to endorse or promote products
     derived from this software without specific prior written permission.

 This software is provided by the copyright holders and contributors "as is" and
 any express or implied warranties, including, but not limited to, the implied
 warranties of merchantability and fitness for a particular purpose are disclaimed.
 In no event shall the Intel Corporation or contributors be liable for any direct,
 indirect, incidental, special, exemplary, or consequential damages
 (including, but not limited to, procurement of substitute goods or services;
 loss of use, data, or profits; or business interruption) however caused
 and on any theory of liability, whether in contract, strict liability,
 or tort (including negligence or otherwise) arising in any way out of
 the use of this software, even if advised of the possibility of such damage.
-->
<opencv_storage>
<haarcascade_frontaleye type_id="opencv-haar-classifier">
  <size>
    20 20</size>
  <stages>
    <_>
      <!-- stage 0 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 8 20 12 -1.</_>
                <_>
                  0 14 20 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.1296395957469940</threshold>
            <left_val>-0.7730420827865601</left_val>
            <right_val>0.6835014820098877</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 1 4 15 -1.</_>
                <_>
                  9 6 4 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0463268086314201</threshold>
            <left_val>0.5735275149345398</left_val>
            <right_val>-0.4909768998622894</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 10 9 2 -1.</_>
                <_>
                  9 10 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0161730907857418</threshold>
            <left_val>0.6025434136390686</left_val>
            <right_val>-0.3161070942878723</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 0 10 9 -1.</_>
                <_>
                  7 3 10 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0458288416266441</threshold>
            <left_val>0.6417754888534546</left_val>
            <right_val>-0.1554504036903381</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 2 2 18 -1.</_>
                <_>
                  12 8 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0537596195936203</threshold>
            <left_val>0.5421931743621826</left_val>
            <right_val>-0.2048082947731018</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 6 8 6 -1.</_>
                <_>
                  8 9 8 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0341711901128292</threshold>
            <left_val>-0.2338819056749344</left_val>
            <right_val>0.4841090142726898</right_val></_></_></trees>
      <stage_threshold>-1.4562760591506958</stage_threshold>
      <parent>-1</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 1 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 0 17 18 -1.</_>
                <_>
                  2 6 17 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.2172762006521225</threshold>
            <left_val>0.7109889984130859</left_val>
            <right_val>-0.5936073064804077</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 10 1 8 -1.</_>
                <_>
                  10 14 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0120719699189067</threshold>
            <left_val>-0.2824048101902008</left_val>
            <right_val>0.5901355147361755</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 10 9 2 -1.</_>
                <_>
                  10 10 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0178541392087936</threshold>
            <left_val>0.5313752293586731</left_val>
            <right_val>-0.2275896072387695</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 1 6 6 -1.</_>
                <_>
                  5 3 6 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0223336108028889</threshold>
            <left_val>-0.1755609959363937</left_val>
            <right_val>0.6335613727569580</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 1 15 9 -1.</_>
                <_>
                  3 4 15 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0914200171828270</threshold>
            <left_val>0.6156309247016907</left_val>
            <right_val>-0.1689953058958054</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 3 9 6 -1.</_>
                <_>
                  6 5 9 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0289736501872540</threshold>
            <left_val>-0.1225007995963097</left_val>
            <right_val>0.7440117001533508</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 17 6 3 -1.</_>
                <_>
                  10 17 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.8203463926911354e-003</threshold>
            <left_val>0.1697437018156052</left_val>
            <right_val>-0.6544165015220642</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 10 9 1 -1.</_>
                <_>
                  12 10 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0203404892235994</threshold>
            <left_val>-0.1255664974451065</left_val>
            <right_val>0.8271045088768005</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 7 6 11 -1.</_>
                <_>
                  3 7 2 11 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0119261499494314</threshold>
            <left_val>0.3860568106174469</left_val>
            <right_val>-0.2099234014749527</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 18 3 1 -1.</_>
                <_>
                  10 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.7281101625412703e-004</threshold>
            <left_val>-0.6376119256019592</left_val>
            <right_val>0.1295239031314850</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 16 1 2 -1.</_>
                <_>
                  16 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.8322050891583785e-005</threshold>
            <left_val>-0.3463147878646851</left_val>
            <right_val>0.2292426973581314</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 17 6 3 -1.</_>
                <_>
                  11 17 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.0854417756199837e-003</threshold>
            <left_val>-0.6366580128669739</left_val>
            <right_val>0.1307865977287293</right_val></_></_></trees>
      <stage_threshold>-1.2550230026245117</stage_threshold>
      <parent>0</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 2 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 0 5 18 -1.</_>
                <_>
                  8 6 5 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1181226968765259</threshold>
            <left_val>0.6784452199935913</left_val>
            <right_val>-0.5004578232765198</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 7 9 7 -1.</_>
                <_>
                  9 7 3 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0343327596783638</threshold>
            <left_val>0.6718636155128479</left_val>
            <right_val>-0.3574487864971161</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 6 6 10 -1.</_>
                <_>
                  16 6 2 10 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0215307995676994</threshold>
            <left_val>0.7222070097923279</left_val>
            <right_val>-0.1819241940975189</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 8 9 5 -1.</_>
                <_>
                  12 8 3 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0219099707901478</threshold>
            <left_val>0.6652938723564148</left_val>
            <right_val>-0.2751022875308991</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 7 9 6 -1.</_>
                <_>
                  6 7 3 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0287135392427444</threshold>
            <left_val>0.6995570063591003</left_val>
            <right_val>-0.1961558014154434</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 7 6 6 -1.</_>
                <_>
                  3 7 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0114674801006913</threshold>
            <left_val>0.5926734805107117</left_val>
            <right_val>-0.2209735065698624</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 0 4 18 -1.</_>
                <_>
                  16 6 4 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0226111691445112</threshold>
            <left_val>0.3448306918144226</left_val>
            <right_val>-0.3837955892086029</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 17 3 3 -1.</_>
                <_>
                  0 18 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.9308089977130294e-003</threshold>
            <left_val>-0.7944571971893311</left_val>
            <right_val>0.1562865972518921</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 0 2 1 -1.</_>
                <_>
                  17 0 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.6419910833938047e-005</threshold>
            <left_val>-0.3089601099491119</left_val>
            <right_val>0.3543108999729157</right_val></_></_></trees>
      <stage_threshold>-1.3728189468383789</stage_threshold>
      <parent>1</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 3 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 8 20 12 -1.</_>
                <_>
                  0 14 20 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.1988652050495148</threshold>
            <left_val>-0.5286070108413696</left_val>
            <right_val>0.3553672134876251</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 6 9 8 -1.</_>
                <_>
                  9 6 3 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0360089391469955</threshold>
            <left_val>0.4210968911647797</left_val>
            <right_val>-0.3934898078441620</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 3 12 9 -1.</_>
                <_>
                  5 6 12 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0775698497891426</threshold>
            <left_val>0.4799154102802277</left_val>
            <right_val>-0.2512216866016388</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 16 1 2 -1.</_>
                <_>
                  4 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.2630853285081685e-005</threshold>
            <left_val>-0.3847548961639404</left_val>
            <right_val>0.3184922039508820</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 10 2 1 -1.</_>
                <_>
                  19 10 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.2773229759186506e-004</threshold>
            <left_val>-0.2642731964588165</left_val>
            <right_val>0.3254724144935608</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 8 6 5 -1.</_>
                <_>
                  11 8 2 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0185748506337404</threshold>
            <left_val>0.4673658907413483</left_val>
            <right_val>-0.1506727039813995</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 2 1 -1.</_>
                <_>
                  1 0 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.0008762122597545e-005</threshold>
            <left_val>0.2931315004825592</left_val>
            <right_val>-0.2536509931087494</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 8 6 6 -1.</_>
                <_>
                  8 8 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0185521300882101</threshold>
            <left_val>0.4627366065979004</left_val>
            <right_val>-0.1314805001020432</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 7 6 7 -1.</_>
                <_>
                  13 7 2 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0130304200574756</threshold>
            <left_val>0.4162721931934357</left_val>
            <right_val>-0.1775148957967758</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 14 1 2 -1.</_>
                <_>
                  19 15 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.5694141085259616e-005</threshold>
            <left_val>-0.2803510129451752</left_val>
            <right_val>0.2668074071407318</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 17 1 2 -1.</_>
                <_>
                  6 18 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.7005260451696813e-004</threshold>
            <left_val>-0.2702724933624268</left_val>
            <right_val>0.2398165017366409</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 7 2 7 -1.</_>
                <_>
                  15 7 1 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.3129199873656034e-003</threshold>
            <left_val>0.4441143870353699</left_val>
            <right_val>-0.1442888975143433</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 8 2 4 -1.</_>
                <_>
                  7 8 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.7583490116521716e-003</threshold>
            <left_val>-0.1612619012594223</left_val>
            <right_val>0.4294076859951019</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 8 12 6 -1.</_>
                <_>
                  5 10 12 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0251947492361069</threshold>
            <left_val>0.4068729877471924</left_val>
            <right_val>-0.1820258051156998</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 17 1 3 -1.</_>
                <_>
                  2 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.4031709870323539e-003</threshold>
            <left_val>0.0847597867250443</left_val>
            <right_val>-0.8001856803894043</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 7 3 6 -1.</_>
                <_>
                  7 7 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.3991729877889156e-003</threshold>
            <left_val>0.5576609969139099</left_val>
            <right_val>-0.1184315979480743</right_val></_></_></trees>
      <stage_threshold>-1.2879480123519897</stage_threshold>
      <parent>2</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 4 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 7 9 12 -1.</_>
                <_>
                  9 7 3 12 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0299430806189775</threshold>
            <left_val>0.3581081032752991</left_val>
            <right_val>-0.3848763108253479</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 2 11 12 -1.</_>
                <_>
                  6 6 11 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1256738007068634</threshold>
            <left_val>0.3931693136692047</left_val>
            <right_val>-0.3001225888729096</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 12 5 8 -1.</_>
                <_>
                  1 16 5 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.3635272197425365e-003</threshold>
            <left_val>-0.4390861988067627</left_val>
            <right_val>0.1925701051950455</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 7 6 7 -1.</_>
                <_>
                  16 7 2 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.0971820279955864e-003</threshold>
            <left_val>0.3990666866302490</left_val>
            <right_val>-0.2340787053108215</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 8 6 6 -1.</_>
                <_>
                  12 8 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0165979098528624</threshold>
            <left_val>0.4209528863430023</left_val>
            <right_val>-0.2267484068870544</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 18 4 2 -1.</_>
                <_>
                  16 19 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.0199299324303865e-003</threshold>
            <left_val>-0.7415673136711121</left_val>
            <right_val>0.1260118931531906</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 17 2 3 -1.</_>
                <_>
                  18 18 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5202340437099338e-003</threshold>
            <left_val>-0.7615460157394409</left_val>
            <right_val>0.0863736122846603</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 7 3 7 -1.</_>
                <_>
                  10 7 1 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.9663940444588661e-003</threshold>
            <left_val>0.4218223989009857</left_val>
            <right_val>-0.1790491938591003</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 6 6 8 -1.</_>
                <_>
                  7 6 2 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0192076005041599</threshold>
            <left_val>0.4689489901065826</left_val>
            <right_val>-0.1437875032424927</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 6 6 11 -1.</_>
                <_>
                  4 6 2 11 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0122226802632213</threshold>
            <left_val>0.3284207880496979</left_val>
            <right_val>-0.2180214971303940</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 10 12 8 -1.</_>
                <_>
                  8 14 12 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0575486682355404</threshold>
            <left_val>-0.3676880896091461</left_val>
            <right_val>0.2435711026191711</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 17 6 3 -1.</_>
                <_>
                  9 17 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.5794079825282097e-003</threshold>
            <left_val>-0.7224506735801697</left_val>
            <right_val>0.0636645630002022</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 9 3 3 -1.</_>
                <_>
                  11 9 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.9545740690082312e-003</threshold>
            <left_val>0.3584643900394440</left_val>
            <right_val>-0.1669632941484451</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 8 3 6 -1.</_>
                <_>
                  9 8 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.2017991654574871e-003</threshold>
            <left_val>0.3909480869770050</left_val>
            <right_val>-0.1204179003834724</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 0 6 5 -1.</_>
                <_>
                  9 0 2 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0136249903589487</threshold>
            <left_val>-0.5876771807670593</left_val>
            <right_val>0.0884047299623489</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 17 1 3 -1.</_>
                <_>
                  6 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.2853112467564642e-005</threshold>
            <left_val>-0.2634845972061157</left_val>
            <right_val>0.2141927927732468</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 18 4 2 -1.</_>
                <_>
                  0 19 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.6782939676195383e-003</threshold>
            <left_val>-0.7839016914367676</left_val>
            <right_val>0.0805269628763199</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 1 11 9 -1.</_>
                <_>
                  4 4 11 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0705971792340279</threshold>
            <left_val>0.4146926105022430</left_val>
            <right_val>-0.1398995965719223</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 1 14 9 -1.</_>
                <_>
                  3 4 14 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0920936465263367</threshold>
            <left_val>-0.1305518001317978</left_val>
            <right_val>0.5043578147888184</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 9 6 4 -1.</_>
                <_>
                  2 9 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.8004386052489281e-003</threshold>
            <left_val>0.3660975098609924</left_val>
            <right_val>-0.1403664946556091</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 13 1 2 -1.</_>
                <_>
                  18 14 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.5080977694597095e-005</threshold>
            <left_val>-0.2970443964004517</left_val>
            <right_val>0.2070294022560120</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 5 3 11 -1.</_>
                <_>
                  14 5 1 11 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.9870450962334871e-003</threshold>
            <left_val>0.3561570048332214</left_val>
            <right_val>-0.1544596999883652</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 18 8 2 -1.</_>
                <_>
                  0 18 4 1 2.</_>
                <_>
                  4 19 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.6441509835422039e-003</threshold>
            <left_val>-0.5435351729393005</left_val>
            <right_val>0.1029511019587517</right_val></_></_></trees>
      <stage_threshold>-1.2179850339889526</stage_threshold>
      <parent>3</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 5 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 8 12 5 -1.</_>
                <_>
                  9 8 4 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0478624701499939</threshold>
            <left_val>0.4152823984622955</left_val>
            <right_val>-0.3418582081794739</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 7 11 10 -1.</_>
                <_>
                  4 12 11 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0873505324125290</threshold>
            <left_val>-0.3874978125095367</left_val>
            <right_val>0.2420420050621033</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 9 6 4 -1.</_>
                <_>
                  16 9 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0168494991958141</threshold>
            <left_val>0.5308247804641724</left_val>
            <right_val>-0.1728291064500809</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 7 6 8 -1.</_>
                <_>
                  3 7 3 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0288700293749571</threshold>
            <left_val>0.3584350943565369</left_val>
            <right_val>-0.2240259051322937</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 16 3 3 -1.</_>
                <_>
                  0 17 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.5679389946162701e-003</threshold>
            <left_val>0.1499049961566925</left_val>
            <right_val>-0.6560940742492676</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 11 12 1 -1.</_>
                <_>
                  11 11 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0241166595369577</threshold>
            <left_val>0.5588967800140381</left_val>
            <right_val>-0.1481028050184250</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 8 9 4 -1.</_>
                <_>
                  7 8 3 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0328266583383083</threshold>
            <left_val>0.4646868109703064</left_val>
            <right_val>-0.1078552976250649</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 16 6 4 -1.</_>
                <_>
                  7 16 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0152330603450537</threshold>
            <left_val>-0.7395442724227905</left_val>
            <right_val>0.0562368817627430</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 17 1 3 -1.</_>
                <_>
                  18 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.0209511169232428e-004</threshold>
            <left_val>-0.4554882049560547</left_val>
            <right_val>0.0970698371529579</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 17 1 3 -1.</_>
                <_>
                  18 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.5365108205005527e-004</threshold>
            <left_val>0.0951472967863083</left_val>
            <right_val>-0.5489501953125000</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 9 4 10 -1.</_>
                <_>
                  4 9 2 5 2.</_>
                <_>
                  6 14 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0106389503926039</threshold>
            <left_val>0.4091297090053558</left_val>
            <right_val>-0.1230840981006622</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 8 6 4 -1.</_>
                <_>
                  6 8 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.5217830017209053e-003</threshold>
            <left_val>0.4028914868831635</left_val>
            <right_val>-0.1604878008365631</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 2 2 18 -1.</_>
                <_>
                  10 8 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1067709997296333</threshold>
            <left_val>0.6175932288169861</left_val>
            <right_val>-0.0730911865830421</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 5 8 6 -1.</_>
                <_>
                  0 5 4 3 2.</_>
                <_>
                  4 8 4 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0162569191306829</threshold>
            <left_val>-0.1310368031263351</left_val>
            <right_val>0.3745365142822266</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 0 6 5 -1.</_>
                <_>
                  8 0 2 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0206793602555990</threshold>
            <left_val>-0.7140290737152100</left_val>
            <right_val>0.0523900091648102</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 0 2 14 -1.</_>
                <_>
                  18 7 2 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0170523691922426</threshold>
            <left_val>0.1282286047935486</left_val>
            <right_val>-0.3108068108558655</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 18 4 2 -1.</_>
                <_>
                  10 18 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.7122060097754002e-003</threshold>
            <left_val>-0.6055650711059570</left_val>
            <right_val>0.0818847566843033</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 17 6 3 -1.</_>
                <_>
                  1 18 6 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.0851430235779844e-005</threshold>
            <left_val>-0.2681298851966858</left_val>
            <right_val>0.1445384025573731</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 8 3 5 -1.</_>
                <_>
                  12 8 1 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.9284431412816048e-003</threshold>
            <left_val>-0.0787953510880470</left_val>
            <right_val>0.5676258206367493</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 8 3 4 -1.</_>
                <_>
                  12 8 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.5217379443347454e-003</threshold>
            <left_val>0.3706862926483154</left_val>
            <right_val>-0.1362057030200958</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 0 6 5 -1.</_>
                <_>
                  13 0 2 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0224261991679668</threshold>
            <left_val>-0.6870499849319458</left_val>
            <right_val>0.0510628595948219</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 7 6 7 -1.</_>
                <_>
                  3 7 2 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.6451441273093224e-003</threshold>
            <left_val>0.2349222004413605</left_val>
            <right_val>-0.1790595948696137</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 13 1 3 -1.</_>
                <_>
                  0 14 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.1175329564139247e-003</threshold>
            <left_val>-0.5986905097961426</left_val>
            <right_val>0.0743244364857674</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 2 9 6 -1.</_>
                <_>
                  3 4 9 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0192127898335457</threshold>
            <left_val>-0.1570255011320114</left_val>
            <right_val>0.2973746955394745</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 6 9 2 -1.</_>
                <_>
                  8 7 9 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.6293429806828499e-003</threshold>
            <left_val>-0.0997690185904503</left_val>
            <right_val>0.4213027060031891</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 14 3 6 -1.</_>
                <_>
                  0 16 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.5671862363815308e-003</threshold>
            <left_val>-0.6085879802703857</left_val>
            <right_val>0.0735062584280968</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 11 6 4 -1.</_>
                <_>
                  3 11 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0112179601565003</threshold>
            <left_val>-0.1032081022858620</left_val>
            <right_val>0.4190984964370728</right_val></_></_></trees>
      <stage_threshold>-1.2905240058898926</stage_threshold>
      <parent>4</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 6 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 9 9 3 -1.</_>
                <_>
                  9 9 3 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0174864400178194</threshold>
            <left_val>0.3130728006362915</left_val>
            <right_val>-0.3368118107318878</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 0 9 6 -1.</_>
                <_>
                  6 2 9 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0307146497070789</threshold>
            <left_val>-0.1876619011163712</left_val>
            <right_val>0.5378080010414124</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 5 6 6 -1.</_>
                <_>
                  8 7 6 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0221887193620205</threshold>
            <left_val>0.3663788139820099</left_val>
            <right_val>-0.1612481027841568</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 12 2 1 -1.</_>
                <_>
                  2 12 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.0700771680567414e-005</threshold>
            <left_val>0.2124571055173874</left_val>
            <right_val>-0.2844462096691132</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 10 6 2 -1.</_>
                <_>
                  12 10 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.0170420221984386e-003</threshold>
            <left_val>0.3954311013221741</left_val>
            <right_val>-0.1317359060049057</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 8 6 6 -1.</_>
                <_>
                  15 8 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.8563609384000301e-003</threshold>
            <left_val>0.3037385940551758</left_val>
            <right_val>-0.2065781950950623</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 16 6 4 -1.</_>
                <_>
                  8 16 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0141292596235871</threshold>
            <left_val>-0.7650300860404968</left_val>
            <right_val>0.0982131883502007</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 0 9 9 -1.</_>
                <_>
                  8 3 9 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0479154810309410</threshold>
            <left_val>0.4830738902091980</left_val>
            <right_val>-0.1300680935382843</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 17 1 3 -1.</_>
                <_>
                  18 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.7032979637151584e-005</threshold>
            <left_val>-0.2521657049655914</left_val>
            <right_val>0.2438668012619019</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 17 1 3 -1.</_>
                <_>
                  18 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.0221180273219943e-003</threshold>
            <left_val>0.0688576027750969</left_val>
            <right_val>-0.6586114168167114</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 10 3 3 -1.</_>
                <_>
                  8 10 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.6056109927594662e-003</threshold>
            <left_val>0.4294202923774719</left_val>
            <right_val>-0.1302246004343033</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 14 2 2 -1.</_>
                <_>
                  9 14 1 1 2.</_>
                <_>
                  10 15 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.4505340813193470e-005</threshold>
            <left_val>-0.1928862035274506</left_val>
            <right_val>0.2895849943161011</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 14 2 2 -1.</_>
                <_>
                  9 14 1 1 2.</_>
                <_>
                  10 15 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.6721157054416835e-005</threshold>
            <left_val>0.3029071092605591</left_val>
            <right_val>-0.1985436975955963</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 8 19 12 -1.</_>
                <_>
                  0 14 19 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.2628143131732941</threshold>
            <left_val>-0.2329394072294235</left_val>
            <right_val>0.2369246035814285</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 6 9 14 -1.</_>
                <_>
                  10 6 3 14 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0235696695744991</threshold>
            <left_val>0.1940104067325592</left_val>
            <right_val>-0.2848461866378784</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 8 3 4 -1.</_>
                <_>
                  14 8 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.9120172150433064e-003</threshold>
            <left_val>0.5537897944450378</left_val>
            <right_val>-0.0956656783819199</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 17 1 3 -1.</_>
                <_>
                  4 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.0788799853762612e-005</threshold>
            <left_val>-0.2391265928745270</left_val>
            <right_val>0.2179948985576630</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 9 6 3 -1.</_>
                <_>
                  6 9 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.8732017427682877e-003</threshold>
            <left_val>0.4069742858409882</left_val>
            <right_val>-0.1276804059743881</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 18 5 2 -1.</_>
                <_>
                  2 19 5 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.6778609715402126e-003</threshold>
            <left_val>-0.5774465799331665</left_val>
            <right_val>0.0973247885704041</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 8 2 2 -1.</_>
                <_>
                  7 8 1 1 2.</_>
                <_>
                  8 9 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.6832430739887059e-004</threshold>
            <left_val>0.2902188003063202</left_val>
            <right_val>-0.1683126986026764</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 8 2 2 -1.</_>
                <_>
                  7 8 1 1 2.</_>
                <_>
                  8 9 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.8687182394787669e-005</threshold>
            <left_val>-0.1955157071352005</left_val>
            <right_val>0.2772096991539002</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 10 13 2 -1.</_>
                <_>
                  5 11 13 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0129535002633929</threshold>
            <left_val>-0.0968383178114891</left_val>
            <right_val>0.4032387137413025</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 8 1 9 -1.</_>
                <_>
                  10 11 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0130439596250653</threshold>
            <left_val>0.4719856977462769</left_val>
            <right_val>-0.0892875492572784</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 8 2 12 -1.</_>
                <_>
                  15 8 1 6 2.</_>
                <_>
                  16 14 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.0261781066656113e-003</threshold>
            <left_val>-0.1362338066101074</left_val>
            <right_val>0.3068627119064331</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 0 3 5 -1.</_>
                <_>
                  5 0 1 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.0438038781285286e-003</threshold>
            <left_val>-0.7795410156250000</left_val>
            <right_val>0.0573163107037544</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 6 3 7 -1.</_>
                <_>
                  13 6 1 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2507249377667904e-003</threshold>
            <left_val>0.3087705969810486</left_val>
            <right_val>-0.1500630974769592</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 16 6 4 -1.</_>
                <_>
                  9 16 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0158268101513386</threshold>
            <left_val>0.0645518898963928</left_val>
            <right_val>-0.7245556712150574</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 16 2 1 -1.</_>
                <_>
                  10 16 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.5864507632795721e-005</threshold>
            <left_val>-0.1759884059429169</left_val>
            <right_val>0.2321038991212845</right_val></_></_></trees>
      <stage_threshold>-1.1600480079650879</stage_threshold>
      <parent>5</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 7 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 10 9 2 -1.</_>
                <_>
                  9 10 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0278548691421747</threshold>
            <left_val>0.4551844894886017</left_val>
            <right_val>-0.1809991002082825</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 6 15 14 -1.</_>
                <_>
                  0 13 15 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.1289504021406174</threshold>
            <left_val>-0.5256553292274475</left_val>
            <right_val>0.1618890017271042</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 1 5 6 -1.</_>
                <_>
                  9 3 5 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0244031809270382</threshold>
            <left_val>-0.1497496068477631</left_val>
            <right_val>0.4235737919807434</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 9 3 4 -1.</_>
                <_>
                  4 9 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.4458570405840874e-003</threshold>
            <left_val>0.3294866979122162</left_val>
            <right_val>-0.1744769066572189</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 7 3 6 -1.</_>
                <_>
                  6 7 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.5336529836058617e-003</threshold>
            <left_val>0.4742664098739624</left_val>
            <right_val>-0.0736183598637581</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 16 1 2 -1.</_>
                <_>
                  17 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.1358150813030079e-005</threshold>
            <left_val>-0.3042193055152893</left_val>
            <right_val>0.1563327014446259</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 8 6 12 -1.</_>
                <_>
                  11 8 2 12 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0162256807088852</threshold>
            <left_val>0.2300218045711517</left_val>
            <right_val>-0.2035982012748718</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 10 6 1 -1.</_>
                <_>
                  8 10 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.6007009223103523e-003</threshold>
            <left_val>0.4045926928520203</left_val>
            <right_val>-0.1348544061183929</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 17 9 3 -1.</_>
                <_>
                  10 17 3 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0219289995729923</threshold>
            <left_val>-0.6872448921203613</left_val>
            <right_val>0.0806842669844627</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 18 6 2 -1.</_>
                <_>
                  14 19 6 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.8971210122108459e-003</threshold>
            <left_val>-0.6961960792541504</left_val>
            <right_val>0.0485452190041542</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 5 3 14 -1.</_>
                <_>
                  10 5 1 14 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.4074649922549725e-003</threshold>
            <left_val>0.2516626119613648</left_val>
            <right_val>-0.1623664945363998</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 16 9 4 -1.</_>
                <_>
                  11 16 3 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0284371692687273</threshold>
            <left_val>0.0603942610323429</left_val>
            <right_val>-0.6674445867538452</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 4 14 -1.</_>
                <_>
                  0 7 4 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0832128822803497</threshold>
            <left_val>0.0643579214811325</left_val>
            <right_val>-0.5362604260444641</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 1 6 3 -1.</_>
                <_>
                  10 1 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0124193299561739</threshold>
            <left_val>-0.7081686258316040</left_val>
            <right_val>0.0575266107916832</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 8 3 4 -1.</_>
                <_>
                  7 8 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.6992599964141846e-003</threshold>
            <left_val>0.5125433206558228</left_val>
            <right_val>-0.0873508006334305</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 8 3 4 -1.</_>
                <_>
                  5 8 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.8025809489190578e-004</threshold>
            <left_val>0.2668766081333160</left_val>
            <right_val>-0.1796150952577591</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 1 6 5 -1.</_>
                <_>
                  7 1 2 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0197243392467499</threshold>
            <left_val>-0.6756373047828674</left_val>
            <right_val>0.0729419067502022</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 18 1 2 -1.</_>
                <_>
                  1 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.0269250487908721e-003</threshold>
            <left_val>0.0539193190634251</left_val>
            <right_val>-0.5554018020629883</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 0 6 6 -1.</_>
                <_>
                  7 2 6 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0259571895003319</threshold>
            <left_val>0.5636252760887146</left_val>
            <right_val>-0.0718983933329582</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 18 4 2 -1.</_>
                <_>
                  0 19 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.2552699772641063e-003</threshold>
            <left_val>-0.5034663081169128</left_val>
            <right_val>0.0896914526820183</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 3 8 12 -1.</_>
                <_>
                  12 7 8 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0499705784022808</threshold>
            <left_val>0.1768511980772018</left_val>
            <right_val>-0.2230195999145508</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 9 3 4 -1.</_>
                <_>
                  13 9 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.9899610672146082e-003</threshold>
            <left_val>0.3912242054939270</left_val>
            <right_val>-0.1014975011348724</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 8 3 5 -1.</_>
                <_>
                  13 8 1 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.8546842299401760e-003</threshold>
            <left_val>-0.1177017986774445</left_val>
            <right_val>0.4219093918800354</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 0 2 1 -1.</_>
                <_>
                  17 0 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.0448860120959580e-004</threshold>
            <left_val>-0.1733397990465164</left_val>
            <right_val>0.2234444022178650</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 17 1 3 -1.</_>
                <_>
                  5 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.9689260524464771e-005</threshold>
            <left_val>-0.2340963035821915</left_val>
            <right_val>0.1655824035406113</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 2 3 6 -1.</_>
                <_>
                  10 4 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0134239196777344</threshold>
            <left_val>0.4302381873130798</left_val>
            <right_val>-0.0997236520051956</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 17 2 3 -1.</_>
                <_>
                  4 18 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.2581999655812979e-003</threshold>
            <left_val>0.0727209895849228</left_val>
            <right_val>-0.5750101804733276</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 7 1 9 -1.</_>
                <_>
                  12 10 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0125462803989649</threshold>
            <left_val>0.3618457913398743</left_val>
            <right_val>-0.1145701035857201</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 6 3 9 -1.</_>
                <_>
                  8 6 1 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.8705769218504429e-003</threshold>
            <left_val>0.2821053862571716</left_val>
            <right_val>-0.1236755028367043</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 13 3 6 -1.</_>
                <_>
                  17 15 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0197856407612562</threshold>
            <left_val>0.0478767491877079</left_val>
            <right_val>-0.8066623806953430</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 7 3 8 -1.</_>
                <_>
                  8 7 1 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.7588930465281010e-003</threshold>
            <left_val>-0.1092538982629776</left_val>
            <right_val>0.3374697864055634</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 0 3 5 -1.</_>
                <_>
                  6 0 1 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.9974269717931747e-003</threshold>
            <left_val>-0.8029593825340271</left_val>
            <right_val>0.0457067005336285</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 6 9 8 -1.</_>
                <_>
                  7 6 3 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0130334803834558</threshold>
            <left_val>0.1868043988943100</left_val>
            <right_val>-0.1768891066312790</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 9 3 3 -1.</_>
                <_>
                  3 9 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3742579612880945e-003</threshold>
            <left_val>0.2772547900676727</left_val>
            <right_val>-0.1280900985002518</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 18 4 2 -1.</_>
                <_>
                  16 19 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.7657810132950544e-003</threshold>
            <left_val>0.0907589420676231</left_val>
            <right_val>-0.4259473979473114</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 10 3 10 -1.</_>
                <_>
                  17 15 3 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.8941841446794569e-004</threshold>
            <left_val>-0.3881632983684540</left_val>
            <right_val>0.0892677977681160</right_val></_></_></trees>
      <stage_threshold>-1.2257250547409058</stage_threshold>
      <parent>6</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 8 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 9 6 4 -1.</_>
                <_>
                  10 9 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0144692296162248</threshold>
            <left_val>0.3750782907009125</left_val>
            <right_val>-0.2492828965187073</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 2 10 12 -1.</_>
                <_>
                  5 6 10 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1331762969493866</threshold>
            <left_val>0.3016637861728668</left_val>
            <right_val>-0.2241407036781311</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 9 6 3 -1.</_>
                <_>
                  8 9 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0101321600377560</threshold>
            <left_val>0.3698559105396271</left_val>
            <right_val>-0.1785001009702683</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 7 3 7 -1.</_>
                <_>
                  12 7 1 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.8511182218790054e-003</threshold>
            <left_val>0.4608676135540009</left_val>
            <right_val>-0.1293139010667801</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 8 6 4 -1.</_>
                <_>
                  14 8 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0142958397045732</threshold>
            <left_val>0.4484142959117889</left_val>
            <right_val>-0.1022624000906944</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 8 6 5 -1.</_>
                <_>
                  16 8 2 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.9606940485537052e-003</threshold>
            <left_val>0.2792798876762390</left_val>
            <right_val>-0.1532382965087891</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 12 2 4 -1.</_>
                <_>
                  12 14 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0109327696263790</threshold>
            <left_val>-0.1514174044132233</left_val>
            <right_val>0.3988964855670929</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 15 1 2 -1.</_>
                <_>
                  3 16 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.0430990086169913e-005</threshold>
            <left_val>-0.2268157005310059</left_val>
            <right_val>0.2164438962936401</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 7 3 4 -1.</_>
                <_>
                  13 7 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.8431681245565414e-003</threshold>
            <left_val>0.4542014896869659</left_val>
            <right_val>-0.1258715987205505</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 0 6 6 -1.</_>
                <_>
                  12 0 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0223462097346783</threshold>
            <left_val>-0.6269019246101379</left_val>
            <right_val>0.0824031233787537</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 6 3 8 -1.</_>
                <_>
                  11 6 1 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.8836669884622097e-003</threshold>
            <left_val>0.2635925114154816</left_val>
            <right_val>-0.1468663066625595</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 17 1 2 -1.</_>
                <_>
                  16 18 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.5506002758629620e-005</threshold>
            <left_val>-0.2450702041387558</left_val>
            <right_val>0.1667888015508652</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 16 1 3 -1.</_>
                <_>
                  16 17 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.9026997294276953e-004</threshold>
            <left_val>-0.4264996051788330</left_val>
            <right_val>0.0899735614657402</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 11 1 2 -1.</_>
                <_>
                  11 12 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.4861579984426498e-003</threshold>
            <left_val>-0.1204025000333786</left_val>
            <right_val>0.3009765148162842</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 7 6 9 -1.</_>
                <_>
                  5 7 2 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0119883399456739</threshold>
            <left_val>0.2785247862339020</left_val>
            <right_val>-0.1224434003233910</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 18 9 1 -1.</_>
                <_>
                  7 18 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0105022396892309</threshold>
            <left_val>0.0404527597129345</left_val>
            <right_val>-0.7405040860176086</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 11 4 9 -1.</_>
                <_>
                  0 14 4 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0309630092233419</threshold>
            <left_val>-0.6284269094467163</left_val>
            <right_val>0.0480137616395950</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 17 6 3 -1.</_>
                <_>
                  11 17 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0114145204424858</threshold>
            <left_val>0.0394052118062973</left_val>
            <right_val>-0.7167412042617798</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 8 6 12 -1.</_>
                <_>
                  9 8 2 12 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0123370001092553</threshold>
            <left_val>0.1994132995605469</left_val>
            <right_val>-0.1927430033683777</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 8 3 4 -1.</_>
                <_>
                  7 8 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.9942267835140228e-003</threshold>
            <left_val>0.5131816267967224</left_val>
            <right_val>-0.0616580583155155</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 17 1 3 -1.</_>
                <_>
                  3 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.1923230485990644e-003</threshold>
            <left_val>-0.7260529994964600</left_val>
            <right_val>0.0506527200341225</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 9 6 4 -1.</_>
                <_>
                  13 9 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.4582789093255997e-003</threshold>
            <left_val>0.2960307896137238</left_val>
            <right_val>-0.1175478994846344</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 1 3 2 -1.</_>
                <_>
                  7 1 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.7877509128302336e-003</threshold>
            <left_val>0.0450687110424042</left_val>
            <right_val>-0.6953541040420532</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 2 1 -1.</_>
                <_>
                  2 0 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2503209766000509e-004</threshold>
            <left_val>0.2004725039005280</left_val>
            <right_val>-0.1577524989843369</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 2 14 -1.</_>
                <_>
                  1 0 1 7 2.</_>
                <_>
                  2 7 1 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.0367889925837517e-003</threshold>
            <left_val>0.2929981946945190</left_val>
            <right_val>-0.1170049980282784</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 5 11 8 -1.</_>
                <_>
                  5 9 11 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0747421607375145</threshold>
            <left_val>-0.1139231994748116</left_val>
            <right_val>0.3025662004947662</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 3 5 6 -1.</_>
                <_>
                  9 5 5 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0202555190771818</threshold>
            <left_val>-0.1051589027047157</left_val>
            <right_val>0.4067046046257019</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 9 5 10 -1.</_>
                <_>
                  7 14 5 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0442145094275475</threshold>
            <left_val>-0.2763164043426514</left_val>
            <right_val>0.1236386969685555</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 10 2 2 -1.</_>
                <_>
                  16 10 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.7259558495134115e-004</threshold>
            <left_val>0.2435503005981445</left_val>
            <right_val>-0.1330094933509827</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 18 8 2 -1.</_>
                <_>
                  0 19 8 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.4453739169985056e-003</threshold>
            <left_val>-0.5386617183685303</left_val>
            <right_val>0.0625106468796730</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 17 1 3 -1.</_>
                <_>
                  7 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.2725353422574699e-005</threshold>
            <left_val>-0.2077220976352692</left_val>
            <right_val>0.1627043932676315</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 2 11 6 -1.</_>
                <_>
                  7 4 11 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0366271100938320</threshold>
            <left_val>0.3656840920448303</left_val>
            <right_val>-0.0903302803635597</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 3 9 3 -1.</_>
                <_>
                  8 4 9 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.0996399000287056e-003</threshold>
            <left_val>-0.1318302005529404</left_val>
            <right_val>0.2535429894924164</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 9 2 2 -1.</_>
                <_>
                  0 10 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.4709280114620924e-003</threshold>
            <left_val>-0.5685349702835083</left_val>
            <right_val>0.0535054318606853</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 5 3 6 -1.</_>
                <_>
                  0 7 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0141146704554558</threshold>
            <left_val>-0.4859901070594788</left_val>
            <right_val>0.0584852509200573</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 7 2 2 -1.</_>
                <_>
                  6 7 1 1 2.</_>
                <_>
                  7 8 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.4537261864170432e-004</threshold>
            <left_val>-0.0800936371088028</left_val>
            <right_val>0.4026564955711365</right_val></_></_>
        <_>
          <!-- tree 36 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 6 3 6 -1.</_>
                <_>
                  8 6 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.1098632179200649e-003</threshold>
            <left_val>0.4470323920249939</left_val>
            <right_val>-0.0629474371671677</right_val></_></_>
        <_>
          <!-- tree 37 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 1 6 4 -1.</_>
                <_>
                  14 1 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0191259607672691</threshold>
            <left_val>-0.6642286777496338</left_val>
            <right_val>0.0498227700591087</right_val></_></_>
        <_>
          <!-- tree 38 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 11 6 8 -1.</_>
                <_>
                  11 11 2 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.0773010589182377e-003</threshold>
            <left_val>0.1737940013408661</left_val>
            <right_val>-0.1685059964656830</right_val></_></_>
        <_>
          <!-- tree 39 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 15 3 3 -1.</_>
                <_>
                  17 16 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.9198289848864079e-003</threshold>
            <left_val>-0.6011028289794922</left_val>
            <right_val>0.0574279390275478</right_val></_></_>
        <_>
          <!-- tree 40 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 6 3 9 -1.</_>
                <_>
                  6 9 3 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0249021500349045</threshold>
            <left_val>0.2339798063039780</left_val>
            <right_val>-0.1181845963001251</right_val></_></_>
        <_>
          <!-- tree 41 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 5 8 6 -1.</_>
                <_>
                  0 5 4 3 2.</_>
                <_>
                  4 8 4 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0201477799564600</threshold>
            <left_val>-0.0894598215818405</left_val>
            <right_val>0.3602440059185028</right_val></_></_>
        <_>
          <!-- tree 42 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 6 1 3 -1.</_>
                <_>
                  0 7 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.7597640398889780e-003</threshold>
            <left_val>0.0494584403932095</left_val>
            <right_val>-0.6310262084007263</right_val></_></_>
        <_>
          <!-- tree 43 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 0 2 6 -1.</_>
                <_>
                  18 0 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.3812039978802204e-003</threshold>
            <left_val>-0.1521805971860886</left_val>
            <right_val>0.1897173970937729</right_val></_></_>
        <_>
          <!-- tree 44 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 17 6 3 -1.</_>
                <_>
                  12 17 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0109045403078198</threshold>
            <left_val>-0.5809738039970398</left_val>
            <right_val>0.0448627285659313</right_val></_></_>
        <_>
          <!-- tree 45 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 15 2 2 -1.</_>
                <_>
                  13 15 1 1 2.</_>
                <_>
                  14 16 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.5157178798690438e-005</threshold>
            <left_val>-0.1377734988927841</left_val>
            <right_val>0.1954316049814224</right_val></_></_>
        <_>
          <!-- tree 46 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 0 12 3 -1.</_>
                <_>
                  4 1 12 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.8649770431220531e-003</threshold>
            <left_val>-0.1030222997069359</left_val>
            <right_val>0.2537496984004974</right_val></_></_></trees>
      <stage_threshold>-1.2863140106201172</stage_threshold>
      <parent>7</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 9 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 3 10 9 -1.</_>
                <_>
                  5 6 10 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1021588966250420</threshold>
            <left_val>0.4168125987052918</left_val>
            <right_val>-0.1665562987327576</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 7 9 7 -1.</_>
                <_>
                  10 7 3 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0519398190081120</threshold>
            <left_val>0.3302395045757294</left_val>
            <right_val>-0.2071571052074432</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 8 9 6 -1.</_>
                <_>
                  8 8 3 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0427177809178829</threshold>
            <left_val>0.2609373033046722</left_val>
            <right_val>-0.1601389050483704</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 16 6 2 -1.</_>
                <_>
                  0 17 6 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.3890418601222336e-004</threshold>
            <left_val>-0.3475053012371063</left_val>
            <right_val>0.1391891986131668</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 6 7 14 -1.</_>
                <_>
                  12 13 7 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0242643896490335</threshold>
            <left_val>-0.4255205988883972</left_val>
            <right_val>0.1357838064432144</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 7 6 8 -1.</_>
                <_>
                  15 7 2 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0238205995410681</threshold>
            <left_val>0.3174980878829956</left_val>
            <right_val>-0.1665204018354416</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 10 6 3 -1.</_>
                <_>
                  4 10 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.0518180727958679e-003</threshold>
            <left_val>0.3094717860221863</left_val>
            <right_val>-0.1333830058574677</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 17 1 3 -1.</_>
                <_>
                  18 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.8517157342284918e-004</threshold>
            <left_val>-0.6008226275444031</left_val>
            <right_val>0.0877470001578331</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 1 6 2 -1.</_>
                <_>
                  7 2 6 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.3705149330198765e-003</threshold>
            <left_val>-0.1231144964694977</left_val>
            <right_val>0.3833355009555817</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 0 6 4 -1.</_>
                <_>
                  6 2 6 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0134035395458341</threshold>
            <left_val>0.3387736976146698</left_val>
            <right_val>-0.1014048978686333</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 18 6 2 -1.</_>
                <_>
                  10 18 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.6856360062956810e-003</threshold>
            <left_val>-0.6119359731674194</left_val>
            <right_val>0.0477402210235596</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 6 5 2 -1.</_>
                <_>
                  7 7 5 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.2887418530881405e-003</threshold>
            <left_val>0.2527579069137573</left_val>
            <right_val>-0.1443451046943665</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 7 3 6 -1.</_>
                <_>
                  7 7 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0108767496421933</threshold>
            <left_val>0.5477573275566101</left_val>
            <right_val>-0.0594554804265499</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 18 2 2 -1.</_>
                <_>
                  18 18 1 1 2.</_>
                <_>
                  19 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.7882640026509762e-004</threshold>
            <left_val>0.0834103003144264</left_val>
            <right_val>-0.4422636926174164</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 8 3 7 -1.</_>
                <_>
                  17 8 1 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.4550149682909250e-003</threshold>
            <left_val>0.2333099991083145</left_val>
            <right_val>-0.1396448016166687</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 16 2 3 -1.</_>
                <_>
                  0 17 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.2721839593723416e-003</threshold>
            <left_val>0.0604802891612053</left_val>
            <right_val>-0.4945608973503113</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 19 6 1 -1.</_>
                <_>
                  7 19 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.8933159559965134e-003</threshold>
            <left_val>-0.6683326959609985</left_val>
            <right_val>0.0462184995412827</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 5 6 6 -1.</_>
                <_>
                  9 7 6 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0264499895274639</threshold>
            <left_val>-0.0732353627681732</left_val>
            <right_val>0.4442596137523651</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 10 2 4 -1.</_>
                <_>
                  0 12 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.3706070389598608e-003</threshold>
            <left_val>-0.4246433973312378</left_val>
            <right_val>0.0686765611171722</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 9 4 3 -1.</_>
                <_>
                  2 9 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.9559480026364326e-003</threshold>
            <left_val>0.1621803939342499</left_val>
            <right_val>-0.1822299957275391</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 10 6 9 -1.</_>
                <_>
                  3 10 2 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0306199099868536</threshold>
            <left_val>-0.0586433410644531</left_val>
            <right_val>0.5326362848281860</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 0 6 2 -1.</_>
                <_>
                  11 0 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.5765907317399979e-003</threshold>
            <left_val>-0.6056268215179443</left_val>
            <right_val>0.0533459894359112</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 1 2 1 -1.</_>
                <_>
                  15 1 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.6372493165545166e-005</threshold>
            <left_val>-0.1668083965778351</left_val>
            <right_val>0.1928416043519974</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 8 1 4 -1.</_>
                <_>
                  0 10 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.0975950434803963e-003</threshold>
            <left_val>0.0441195107996464</left_val>
            <right_val>-0.5745884180068970</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 6 2 2 -1.</_>
                <_>
                  15 6 1 1 2.</_>
                <_>
                  16 7 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.7112718564458191e-004</threshold>
            <left_val>-0.1108639985322952</left_val>
            <right_val>0.2310539036989212</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 5 3 6 -1.</_>
                <_>
                  8 5 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.6607588455080986e-003</threshold>
            <left_val>0.4045628905296326</left_val>
            <right_val>-0.0624460913240910</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 17 1 3 -1.</_>
                <_>
                  19 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.7489158613607287e-004</threshold>
            <left_val>0.0648751482367516</left_val>
            <right_val>-0.4487104117870331</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 10 3 1 -1.</_>
                <_>
                  8 10 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.1120870476588607e-003</threshold>
            <left_val>-0.0938614606857300</left_val>
            <right_val>0.3045391142368317</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 1 6 6 -1.</_>
                <_>
                  14 1 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0238378196954727</threshold>
            <left_val>-0.5888742804527283</left_val>
            <right_val>0.0466594211757183</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 5 2 1 -1.</_>
                <_>
                  16 5 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.2272899514064193e-004</threshold>
            <left_val>-0.1489859968423843</left_val>
            <right_val>0.1770195066928864</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 2 7 4 -1.</_>
                <_>
                  8 4 7 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0244674701243639</threshold>
            <left_val>-0.0557896010577679</left_val>
            <right_val>0.4920830130577087</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 0 14 15 -1.</_>
                <_>
                  4 5 14 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1423932015895844</threshold>
            <left_val>0.1519200056791306</left_val>
            <right_val>-0.1877889931201935</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 8 6 6 -1.</_>
                <_>
                  9 8 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0201231203973293</threshold>
            <left_val>0.2178010046482086</left_val>
            <right_val>-0.1208190023899078</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 17 1 3 -1.</_>
                <_>
                  11 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.1513679783092812e-004</threshold>
            <left_val>-0.1685658991336823</left_val>
            <right_val>0.1645192950963974</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 16 2 4 -1.</_>
                <_>
                  12 16 1 2 2.</_>
                <_>
                  13 18 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.7556740678846836e-003</threshold>
            <left_val>-0.6944203972816467</left_val>
            <right_val>0.0394494682550430</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 13 2 1 -1.</_>
                <_>
                  11 13 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.5843912782147527e-005</threshold>
            <left_val>0.1894136965274811</left_val>
            <right_val>-0.1518384069204330</right_val></_></_>
        <_>
          <!-- tree 36 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 8 3 3 -1.</_>
                <_>
                  12 8 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.0697711780667305e-003</threshold>
            <left_val>0.4706459939479828</left_val>
            <right_val>-0.0579276196658611</right_val></_></_>
        <_>
          <!-- tree 37 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 0 6 8 -1.</_>
                <_>
                  4 0 2 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0373931787908077</threshold>
            <left_val>-0.7589244842529297</left_val>
            <right_val>0.0341160483658314</right_val></_></_>
        <_>
          <!-- tree 38 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 5 6 6 -1.</_>
                <_>
                  3 5 3 3 2.</_>
                <_>
                  6 8 3 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0159956105053425</threshold>
            <left_val>0.3067046999931335</left_val>
            <right_val>-0.0875255763530731</right_val></_></_>
        <_>
          <!-- tree 39 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 8 3 3 -1.</_>
                <_>
                  11 8 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.1183990649878979e-003</threshold>
            <left_val>0.2619537115097046</left_val>
            <right_val>-0.0912148877978325</right_val></_></_>
        <_>
          <!-- tree 40 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 17 4 2 -1.</_>
                <_>
                  5 18 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.0651360498741269e-003</threshold>
            <left_val>-0.1742756068706513</left_val>
            <right_val>0.1527764052152634</right_val></_></_>
        <_>
          <!-- tree 41 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 16 5 2 -1.</_>
                <_>
                  8 17 5 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.6029420075938106e-003</threshold>
            <left_val>0.3561263084411621</left_val>
            <right_val>-0.0766299962997437</right_val></_></_>
        <_>
          <!-- tree 42 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 4 3 3 -1.</_>
                <_>
                  0 5 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.3619908392429352e-003</threshold>
            <left_val>0.0493569709360600</left_val>
            <right_val>-0.5922877192497253</right_val></_></_>
        <_>
          <!-- tree 43 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 3 6 2 -1.</_>
                <_>
                  8 3 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0107799097895622</threshold>
            <left_val>-0.6392217874526978</left_val>
            <right_val>0.0332045406103134</right_val></_></_>
        <_>
          <!-- tree 44 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 4 9 3 -1.</_>
                <_>
                  7 4 3 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.3590869754552841e-003</threshold>
            <left_val>0.1610738933086395</left_val>
            <right_val>-0.1522132009267807</right_val></_></_>
        <_>
          <!-- tree 45 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 13 1 4 -1.</_>
                <_>
                  0 15 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.4596069753170013e-003</threshold>
            <left_val>0.0331729613244534</left_val>
            <right_val>-0.7500774264335632</right_val></_></_>
        <_>
          <!-- tree 46 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 17 8 3 -1.</_>
                <_>
                  0 18 8 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.1385448575019836e-003</threshold>
            <left_val>0.0263252798467875</left_val>
            <right_val>-0.7173116207122803</right_val></_></_>
        <_>
          <!-- tree 47 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 1 11 6 -1.</_>
                <_>
                  6 3 11 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0333384908735752</threshold>
            <left_val>0.3353661000728607</left_val>
            <right_val>-0.0708035901188850</right_val></_></_></trees>
      <stage_threshold>-1.1189440488815308</stage_threshold>
      <parent>8</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 10 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 10 6 2 -1.</_>
                <_>
                  6 10 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0195539798587561</threshold>
            <left_val>-0.1043972000479698</left_val>
            <right_val>0.5312895178794861</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 8 1 12 -1.</_>
                <_>
                  10 14 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0221229195594788</threshold>
            <left_val>-0.2474727034568787</left_val>
            <right_val>0.2084725052118301</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 8 3 4 -1.</_>
                <_>
                  6 8 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.1829389519989491e-003</threshold>
            <left_val>0.3828943967819214</left_val>
            <right_val>-0.1471157968044281</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 17 1 3 -1.</_>
                <_>
                  0 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.6381728760898113e-004</threshold>
            <left_val>-0.6263288855552673</left_val>
            <right_val>0.1199325993657112</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 17 1 3 -1.</_>
                <_>
                  0 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.9958612332120538e-004</threshold>
            <left_val>0.0925734713673592</left_val>
            <right_val>-0.5516883134841919</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 8 3 4 -1.</_>
                <_>
                  14 8 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.1527570039033890e-003</threshold>
            <left_val>-0.0729298070073128</left_val>
            <right_val>0.5551251173019409</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 5 5 4 -1.</_>
                <_>
                  1 7 5 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.9388681761920452e-003</threshold>
            <left_val>0.2019603997468948</left_val>
            <right_val>-0.2091203927993774</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 14 1 2 -1.</_>
                <_>
                  18 15 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.4613410166930407e-004</threshold>
            <left_val>-0.2786181867122650</left_val>
            <right_val>0.1381741017103195</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 8 2 4 -1.</_>
                <_>
                  14 8 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.1691689509898424e-003</threshold>
            <left_val>0.3668589890003204</left_val>
            <right_val>-0.0763082429766655</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 6 6 8 -1.</_>
                <_>
                  12 6 2 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0221893899142742</threshold>
            <left_val>0.3909659981727600</left_val>
            <right_val>-0.1097154021263123</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 6 6 10 -1.</_>
                <_>
                  10 6 2 10 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.4523608200252056e-003</threshold>
            <left_val>0.1283859014511108</left_val>
            <right_val>-0.2415986955165863</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 16 1 3 -1.</_>
                <_>
                  17 17 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.7997002517804503e-004</threshold>
            <left_val>0.0719780698418617</left_val>
            <right_val>-0.4397650063037872</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 7 2 10 -1.</_>
                <_>
                  2 7 1 10 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.6783639118075371e-003</threshold>
            <left_val>0.2156984955072403</left_val>
            <right_val>-0.1420592069625855</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 9 6 3 -1.</_>
                <_>
                  7 9 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0151886399835348</threshold>
            <left_val>0.3645878136157990</left_val>
            <right_val>-0.0826759263873100</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 8 5 12 -1.</_>
                <_>
                  0 14 5 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.0619798712432384e-003</threshold>
            <left_val>-0.3438040912151337</left_val>
            <right_val>0.0920682325959206</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 11 1 3 -1.</_>
                <_>
                  0 12 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7351920250803232e-003</threshold>
            <left_val>-0.6172549724578857</left_val>
            <right_val>0.0492144785821438</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 16 6 4 -1.</_>
                <_>
                  8 16 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0124234501272440</threshold>
            <left_val>-0.5855895280838013</left_val>
            <right_val>0.0461126007139683</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 6 2 6 -1.</_>
                <_>
                  0 8 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0130314296111465</threshold>
            <left_val>-0.5971078872680664</left_val>
            <right_val>0.0406724587082863</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 18 2 1 -1.</_>
                <_>
                  12 18 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.2369629694148898e-003</threshold>
            <left_val>-0.6833416819572449</left_val>
            <right_val>0.0331561788916588</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 1 9 2 -1.</_>
                <_>
                  5 2 9 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.1022108420729637e-003</threshold>
            <left_val>-0.0947292372584343</left_val>
            <right_val>0.3010224103927612</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 1 2 -1.</_>
                <_>
                  0 1 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.6952849738299847e-004</threshold>
            <left_val>0.0818168669939041</left_val>
            <right_val>-0.3519603013992310</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 9 3 3 -1.</_>
                <_>
                  16 9 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7970580374822021e-003</threshold>
            <left_val>0.2371897995471954</left_val>
            <right_val>-0.1176870986819267</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 16 1 3 -1.</_>
                <_>
                  18 17 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.1074528386816382e-004</threshold>
            <left_val>-0.4476378858089447</left_val>
            <right_val>0.0576824806630611</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 10 6 1 -1.</_>
                <_>
                  13 10 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.9126471169292927e-003</threshold>
            <left_val>0.4342541098594666</left_val>
            <right_val>-0.0668685734272003</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 3 4 4 -1.</_>
                <_>
                  3 3 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.3132149837911129e-003</threshold>
            <left_val>0.1815001070499420</left_val>
            <right_val>-0.1418032050132752</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 2 1 18 -1.</_>
                <_>
                  11 8 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0608146600425243</threshold>
            <left_val>0.4722171127796173</left_val>
            <right_val>-0.0614106394350529</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 1 5 12 -1.</_>
                <_>
                  9 5 5 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0967141836881638</threshold>
            <left_val>0.2768316864967346</left_val>
            <right_val>-0.0944900363683701</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 0 8 1 -1.</_>
                <_>
                  16 0 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.9073550142347813e-003</threshold>
            <left_val>-0.1227853000164032</left_val>
            <right_val>0.2105740010738373</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 6 3 10 -1.</_>
                <_>
                  9 6 1 10 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.0431869029998779e-003</threshold>
            <left_val>0.3564156889915466</left_val>
            <right_val>-0.0778062269091606</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 2 1 6 -1.</_>
                <_>
                  19 4 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.8800031654536724e-003</threshold>
            <left_val>-0.4103479087352753</left_val>
            <right_val>0.0696943774819374</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 6 2 2 -1.</_>
                <_>
                  18 7 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.3547428213059902e-003</threshold>
            <left_val>-0.7301788926124573</left_val>
            <right_val>0.0366551503539085</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 7 3 4 -1.</_>
                <_>
                  8 7 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.6500627696514130e-003</threshold>
            <left_val>0.5518112778663635</left_val>
            <right_val>-0.0531680807471275</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 0 6 5 -1.</_>
                <_>
                  7 0 2 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0173973105847836</threshold>
            <left_val>-0.5708423256874085</left_val>
            <right_val>0.0502140894532204</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 3 7 3 -1.</_>
                <_>
                  0 4 7 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.8304329179227352e-003</threshold>
            <left_val>-0.4618028104305267</left_val>
            <right_val>0.0502026900649071</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 6 2 1 -1.</_>
                <_>
                  2 6 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.3255619928240776e-004</threshold>
            <left_val>-0.0953627303242683</left_val>
            <right_val>0.2598375976085663</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 8 2 10 -1.</_>
                <_>
                  4 8 1 5 2.</_>
                <_>
                  5 13 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.3100529797375202e-003</threshold>
            <left_val>0.2287247031927109</left_val>
            <right_val>-0.1053353026509285</right_val></_></_>
        <_>
          <!-- tree 36 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 18 18 2 -1.</_>
                <_>
                  2 18 9 1 2.</_>
                <_>
                  11 19 9 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.5426651164889336e-003</threshold>
            <left_val>-0.5699051022529602</left_val>
            <right_val>0.0488634593784809</right_val></_></_>
        <_>
          <!-- tree 37 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 7 4 4 -1.</_>
                <_>
                  2 7 2 2 2.</_>
                <_>
                  4 9 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.2723060362040997e-003</threshold>
            <left_val>0.3514518141746521</left_val>
            <right_val>-0.0823901072144508</right_val></_></_>
        <_>
          <!-- tree 38 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 3 3 4 -1.</_>
                <_>
                  18 3 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.8578968271613121e-003</threshold>
            <left_val>-0.6041762232780457</left_val>
            <right_val>0.0445394404232502</right_val></_></_>
        <_>
          <!-- tree 39 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 9 2 8 -1.</_>
                <_>
                  16 9 1 4 2.</_>
                <_>
                  17 13 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.5867310576140881e-003</threshold>
            <left_val>-0.1034090965986252</left_val>
            <right_val>0.2328201979398727</right_val></_></_>
        <_>
          <!-- tree 40 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 7 1 6 -1.</_>
                <_>
                  15 9 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.7427811659872532e-003</threshold>
            <left_val>0.2849028110504150</left_val>
            <right_val>-0.0980904996395111</right_val></_></_>
        <_>
          <!-- tree 41 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 2 2 2 -1.</_>
                <_>
                  14 3 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3515240279957652e-003</threshold>
            <left_val>0.2309643030166626</left_val>
            <right_val>-0.1136184036731720</right_val></_></_>
        <_>
          <!-- tree 42 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 0 2 3 -1.</_>
                <_>
                  17 1 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.2526069078594446e-003</threshold>
            <left_val>0.0644783228635788</left_val>
            <right_val>-0.4220589101314545</right_val></_></_>
        <_>
          <!-- tree 43 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 18 2 2 -1.</_>
                <_>
                  16 18 1 1 2.</_>
                <_>
                  17 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.8038659840822220e-004</threshold>
            <left_val>-0.3807620108127594</left_val>
            <right_val>0.0600432902574539</right_val></_></_>
        <_>
          <!-- tree 44 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 4 4 3 -1.</_>
                <_>
                  10 5 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.9043921753764153e-003</threshold>
            <left_val>-0.0761049985885620</left_val>
            <right_val>0.3323217034339905</right_val></_></_>
        <_>
          <!-- tree 45 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 2 8 6 -1.</_>
                <_>
                  4 2 4 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.0969670563936234e-003</threshold>
            <left_val>0.1428779065608978</left_val>
            <right_val>-0.1688780039548874</right_val></_></_>
        <_>
          <!-- tree 46 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 14 6 6 -1.</_>
                <_>
                  7 16 6 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.9317929446697235e-003</threshold>
            <left_val>0.2725540995597839</left_val>
            <right_val>-0.0928795635700226</right_val></_></_>
        <_>
          <!-- tree 47 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 15 2 2 -1.</_>
                <_>
                  11 16 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.1471060570329428e-003</threshold>
            <left_val>-0.1527305990457535</left_val>
            <right_val>0.1970240026712418</right_val></_></_>
        <_>
          <!-- tree 48 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 1 9 4 -1.</_>
                <_>
                  10 1 3 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0376628898084164</threshold>
            <left_val>-0.5932043790817261</left_val>
            <right_val>0.0407386012375355</right_val></_></_>
        <_>
          <!-- tree 49 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 7 3 7 -1.</_>
                <_>
                  10 7 1 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.8165571428835392e-003</threshold>
            <left_val>0.2549408972263336</left_val>
            <right_val>-0.0940819606184959</right_val></_></_>
        <_>
          <!-- tree 50 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 17 2 2 -1.</_>
                <_>
                  6 17 1 1 2.</_>
                <_>
                  7 18 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.6205562325194478e-004</threshold>
            <left_val>0.0467957183718681</left_val>
            <right_val>-0.4845437109470367</right_val></_></_>
        <_>
          <!-- tree 51 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 6 3 9 -1.</_>
                <_>
                  5 6 1 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.2202551849186420e-003</threshold>
            <left_val>0.2468214929103851</left_val>
            <right_val>-0.0946739763021469</right_val></_></_>
        <_>
          <!-- tree 52 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 10 19 10 -1.</_>
                <_>
                  0 15 19 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0689865127205849</threshold>
            <left_val>-0.6651480197906494</left_val>
            <right_val>0.0359263904392719</right_val></_></_>
        <_>
          <!-- tree 53 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 17 6 1 -1.</_>
                <_>
                  7 17 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.1707608401775360e-003</threshold>
            <left_val>0.0258333198726177</left_val>
            <right_val>-0.7268627285957336</right_val></_></_>
        <_>
          <!-- tree 54 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 12 6 3 -1.</_>
                <_>
                  3 12 3 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0105362497270107</threshold>
            <left_val>-0.0818289965391159</left_val>
            <right_val>0.2976079881191254</right_val></_></_></trees>
      <stage_threshold>-1.1418989896774292</stage_threshold>
      <parent>9</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 11 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 5 18 5 -1.</_>
                <_>
                  8 5 6 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0627587288618088</threshold>
            <left_val>0.2789908051490784</left_val>
            <right_val>-0.2965610921382904</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 15 6 4 -1.</_>
                <_>
                  1 17 6 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.4516479354351759e-003</threshold>
            <left_val>-0.3463588058948517</left_val>
            <right_val>0.2090384066104889</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 10 6 6 -1.</_>
                <_>
                  16 10 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.8699486330151558e-003</threshold>
            <left_val>0.2414488941431046</left_val>
            <right_val>-0.1920557022094727</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 14 4 3 -1.</_>
                <_>
                  0 15 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.4624869003891945e-003</threshold>
            <left_val>-0.5915178060531616</left_val>
            <right_val>0.1248644962906838</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 7 6 11 -1.</_>
                <_>
                  3 7 2 11 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.4818761572241783e-003</threshold>
            <left_val>0.1839154064655304</left_val>
            <right_val>-0.2485826015472412</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 17 7 2 -1.</_>
                <_>
                  13 18 7 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.3226840130519122e-004</threshold>
            <left_val>-0.3304725885391235</left_val>
            <right_val>0.1099926009774208</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 14 2 3 -1.</_>
                <_>
                  0 15 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.8101120367646217e-003</threshold>
            <left_val>0.0987440124154091</left_val>
            <right_val>-0.4963478147983551</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 6 2 -1.</_>
                <_>
                  3 0 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.4422430694103241e-003</threshold>
            <left_val>0.2934441864490509</left_val>
            <right_val>-0.1309475004673004</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 1 6 3 -1.</_>
                <_>
                  3 1 3 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.4148122221231461e-003</threshold>
            <left_val>-0.1476269960403442</left_val>
            <right_val>0.3327716886997223</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 8 2 6 -1.</_>
                <_>
                  0 10 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0155651401728392</threshold>
            <left_val>-0.6840490102767944</left_val>
            <right_val>0.0998726934194565</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 2 6 14 -1.</_>
                <_>
                  1 2 3 7 2.</_>
                <_>
                  4 9 3 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0287205204367638</threshold>
            <left_val>-0.1483328044414520</left_val>
            <right_val>0.3090257942676544</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 5 2 2 -1.</_>
                <_>
                  17 5 1 1 2.</_>
                <_>
                  18 6 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.6687392215244472e-005</threshold>
            <left_val>-0.1743104010820389</left_val>
            <right_val>0.2140295952558518</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 10 9 4 -1.</_>
                <_>
                  14 10 3 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0523710586130619</threshold>
            <left_val>-0.0701568573713303</left_val>
            <right_val>0.4922299087047577</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 9 12 4 -1.</_>
                <_>
                  6 9 4 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0864856913685799</threshold>
            <left_val>0.5075724720954895</left_val>
            <right_val>-0.0752942115068436</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 10 12 2 -1.</_>
                <_>
                  11 10 4 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0421698689460754</threshold>
            <left_val>0.4568096101284027</left_val>
            <right_val>-0.0902199000120163</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 13 1 2 -1.</_>
                <_>
                  2 14 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.5369830331765115e-005</threshold>
            <left_val>-0.2653827965259552</left_val>
            <right_val>0.1618953943252564</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 7 4 3 -1.</_>
                <_>
                  16 8 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.2918000146746635e-003</threshold>
            <left_val>0.0748901516199112</left_val>
            <right_val>-0.5405467152595520</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 16 1 3 -1.</_>
                <_>
                  19 17 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.5511651812121272e-004</threshold>
            <left_val>-0.4926199018955231</left_val>
            <right_val>0.0587239488959312</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 11 1 2 -1.</_>
                <_>
                  18 12 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.5108138844370842e-005</threshold>
            <left_val>-0.2143210023641586</left_val>
            <right_val>0.1407776027917862</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 7 8 2 -1.</_>
                <_>
                  12 7 4 1 2.</_>
                <_>
                  16 8 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.9981209449470043e-003</threshold>
            <left_val>-0.0905473381280899</left_val>
            <right_val>0.3571606874465942</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 9 2 4 -1.</_>
                <_>
                  15 9 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.4929979806765914e-003</threshold>
            <left_val>0.2562345862388611</left_val>
            <right_val>-0.1422906965017319</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 2 6 4 -1.</_>
                <_>
                  14 2 3 2 2.</_>
                <_>
                  17 4 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.7239411137998104e-003</threshold>
            <left_val>-0.1564925014972687</left_val>
            <right_val>0.2108871042728424</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 0 6 1 -1.</_>
                <_>
                  17 0 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.2218320518732071e-003</threshold>
            <left_val>-0.1507298946380615</left_val>
            <right_val>0.2680186927318573</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 12 2 1 -1.</_>
                <_>
                  4 12 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.3993072146549821e-004</threshold>
            <left_val>0.2954699099063873</left_val>
            <right_val>-0.1069239005446434</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 2 3 1 -1.</_>
                <_>
                  18 2 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.0113459322601557e-003</threshold>
            <left_val>0.0506143495440483</left_val>
            <right_val>-0.7168337106704712</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 16 18 2 -1.</_>
                <_>
                  7 16 6 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0114528704434633</threshold>
            <left_val>-0.1271906942129135</left_val>
            <right_val>0.2415277957916260</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 19 8 1 -1.</_>
                <_>
                  6 19 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0782170575112104e-003</threshold>
            <left_val>0.2481300979852676</left_val>
            <right_val>-0.1346119940280914</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 17 4 3 -1.</_>
                <_>
                  1 18 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.3417691010981798e-003</threshold>
            <left_val>0.0535783097147942</left_val>
            <right_val>-0.5227416753768921</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 13 1 2 -1.</_>
                <_>
                  19 14 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.9398651248775423e-005</threshold>
            <left_val>-0.2169874012470245</left_val>
            <right_val>0.1281217932701111</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 16 10 4 -1.</_>
                <_>
                  9 16 5 2 2.</_>
                <_>
                  14 18 5 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.0982551872730255e-003</threshold>
            <left_val>0.2440188974142075</left_val>
            <right_val>-0.1157058998942375</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 9 2 4 -1.</_>
                <_>
                  12 9 1 2 2.</_>
                <_>
                  13 11 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.6289720078930259e-003</threshold>
            <left_val>0.2826147079467773</left_val>
            <right_val>-0.1065946966409683</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 11 1 9 -1.</_>
                <_>
                  19 14 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0139848599210382</threshold>
            <left_val>0.0427158996462822</left_val>
            <right_val>-0.7364631295204163</right_val></_></_></trees>
      <stage_threshold>-1.1255199909210205</stage_threshold>
      <parent>10</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 12 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 6 14 14 -1.</_>
                <_>
                  6 13 14 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.1641651988029480</threshold>
            <left_val>-0.4896030128002167</left_val>
            <right_val>0.1760770976543427</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 17 4 2 -1.</_>
                <_>
                  2 18 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.3413062384352088e-004</threshold>
            <left_val>-0.2822043001651764</left_val>
            <right_val>0.2419957965612412</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 2 1 3 -1.</_>
                <_>
                  0 3 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7193210078403354e-003</threshold>
            <left_val>-0.7148588895797730</left_val>
            <right_val>0.0861622169613838</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 12 1 3 -1.</_>
                <_>
                  0 13 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5654950402677059e-003</threshold>
            <left_val>-0.7297238111495972</left_val>
            <right_val>0.0940706729888916</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 15 4 4 -1.</_>
                <_>
                  15 17 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.9124479731544852e-003</threshold>
            <left_val>-0.3118715882301331</left_val>
            <right_val>0.1814339011907578</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 5 18 7 -1.</_>
                <_>
                  8 5 6 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1351236999034882</threshold>
            <left_val>0.2957729995250702</left_val>
            <right_val>-0.2217925041913986</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 16 5 3 -1.</_>
                <_>
                  1 17 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.0300549007952213e-003</threshold>
            <left_val>-0.6659513711929321</left_val>
            <right_val>0.0854310169816017</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 4 2 3 -1.</_>
                <_>
                  0 5 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.8640460222959518e-003</threshold>
            <left_val>-0.6208636164665222</left_val>
            <right_val>0.0531060211360455</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 6 2 6 -1.</_>
                <_>
                  1 6 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.4065420255064964e-003</threshold>
            <left_val>0.2234628945589066</left_val>
            <right_val>-0.2021100968122482</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 14 4 3 -1.</_>
                <_>
                  16 15 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.5820449702441692e-003</threshold>
            <left_val>-0.5403040051460266</left_val>
            <right_val>0.0682136192917824</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 10 6 -1.</_>
                <_>
                  0 0 5 3 2.</_>
                <_>
                  5 3 5 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0415444709360600</threshold>
            <left_val>-0.0652158409357071</left_val>
            <right_val>0.6210923194885254</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 2 3 6 -1.</_>
                <_>
                  3 2 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.1709550470113754e-003</threshold>
            <left_val>-0.7555329799652100</left_val>
            <right_val>0.0526404492557049</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 0 3 10 -1.</_>
                <_>
                  3 0 1 10 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.1552738770842552e-003</threshold>
            <left_val>0.0909394025802612</left_val>
            <right_val>-0.4424613118171692</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 5 2 2 -1.</_>
                <_>
                  5 6 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0043520014733076e-003</threshold>
            <left_val>0.2429233044385910</left_val>
            <right_val>-0.1866979002952576</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 6 4 4 -1.</_>
                <_>
                  12 8 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0115198297426105</threshold>
            <left_val>-0.1176315024495125</left_val>
            <right_val>0.3672345876693726</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 5 7 3 -1.</_>
                <_>
                  13 6 7 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.9040733873844147e-003</threshold>
            <left_val>-0.4893133044242859</left_val>
            <right_val>0.1089702025055885</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 13 1 2 -1.</_>
                <_>
                  10 14 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.3973670583218336e-004</threshold>
            <left_val>-0.2185039967298508</left_val>
            <right_val>0.1848998963832855</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 16 4 2 -1.</_>
                <_>
                  18 16 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.3727260520681739e-003</threshold>
            <left_val>-0.1507291048765183</left_val>
            <right_val>0.2917312979698181</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 12 4 7 -1.</_>
                <_>
                  18 12 2 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0108073903247714</threshold>
            <left_val>0.4289745092391968</left_val>
            <right_val>-0.1028013974428177</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 17 1 3 -1.</_>
                <_>
                  16 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.2670770520344377e-003</threshold>
            <left_val>0.0741921588778496</left_val>
            <right_val>-0.6420825123786926</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 9 1 3 -1.</_>
                <_>
                  19 10 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.2991129662841558e-003</threshold>
            <left_val>0.0471002794802189</left_val>
            <right_val>-0.7233523130416870</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 7 2 6 -1.</_>
                <_>
                  19 7 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.7187510859221220e-003</threshold>
            <left_val>-0.1708686947822571</left_val>
            <right_val>0.2351350933313370</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 1 3 4 -1.</_>
                <_>
                  9 1 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.6619180142879486e-003</threshold>
            <left_val>-0.7897542715072632</left_val>
            <right_val>0.0450846701860428</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 0 6 9 -1.</_>
                <_>
                  16 0 2 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0482666492462158</threshold>
            <left_val>-0.6957991719245911</left_val>
            <right_val>0.0419760793447495</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 2 10 2 -1.</_>
                <_>
                  9 2 5 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0152146900072694</threshold>
            <left_val>-0.1081828027963638</left_val>
            <right_val>0.3646062016487122</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 12 8 4 -1.</_>
                <_>
                  2 12 4 2 2.</_>
                <_>
                  6 14 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.0080131515860558e-003</threshold>
            <left_val>0.3097099065780640</left_val>
            <right_val>-0.1135921031236649</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 4 7 3 -1.</_>
                <_>
                  0 5 7 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.6127157770097256e-003</threshold>
            <left_val>0.0806653425097466</left_val>
            <right_val>-0.4665853083133698</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 14 3 3 -1.</_>
                <_>
                  15 14 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.9607013612985611e-003</threshold>
            <left_val>-0.8720194101333618</left_val>
            <right_val>0.0367745906114578</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 3 4 3 -1.</_>
                <_>
                  2 3 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.8847199175506830e-003</threshold>
            <left_val>-0.1166628971695900</left_val>
            <right_val>0.3307026922702789</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 2 7 -1.</_>
                <_>
                  2 0 1 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0988810099661350e-003</threshold>
            <left_val>0.2387257069349289</left_val>
            <right_val>-0.1765675991773605</right_val></_></_></trees>
      <stage_threshold>-1.1729990243911743</stage_threshold>
      <parent>11</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 13 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 16 4 4 -1.</_>
                <_>
                  15 18 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.5903379321098328e-003</threshold>
            <left_val>-0.2368807941675186</left_val>
            <right_val>0.2463164031505585</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 8 12 4 -1.</_>
                <_>
                  5 10 12 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.4815930090844631e-003</threshold>
            <left_val>-0.3137362003326416</left_val>
            <right_val>0.1867575943470001</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 17 1 2 -1.</_>
                <_>
                  3 18 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.3048402555286884e-005</threshold>
            <left_val>-0.2764435112476349</left_val>
            <right_val>0.1649623960256577</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 1 3 4 -1.</_>
                <_>
                  7 1 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.8514640182256699e-003</threshold>
            <left_val>-0.5601450800895691</left_val>
            <right_val>0.1129473969340324</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 2 3 4 -1.</_>
                <_>
                  7 2 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.8588210009038448e-003</threshold>
            <left_val>0.0398489981889725</left_val>
            <right_val>-0.5807185769081116</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 8 9 12 -1.</_>
                <_>
                  9 8 3 12 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0246512200683355</threshold>
            <left_val>0.1675501018762589</left_val>
            <right_val>-0.2534367144107819</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 1 8 6 -1.</_>
                <_>
                  8 3 8 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0472455210983753</threshold>
            <left_val>-0.1066208034753799</left_val>
            <right_val>0.3945198059082031</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 2 6 3 -1.</_>
                <_>
                  17 2 3 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.5964651294052601e-003</threshold>
            <left_val>-0.1774425059556961</left_val>
            <right_val>0.2728019058704376</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 6 1 3 -1.</_>
                <_>
                  0 7 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3177490327507257e-003</threshold>
            <left_val>-0.5427265167236328</left_val>
            <right_val>0.0486065894365311</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 0 10 2 -1.</_>
                <_>
                  15 0 5 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.0261709839105606e-003</threshold>
            <left_val>0.2439424991607666</left_val>
            <right_val>-0.1314364969730377</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 0 3 2 -1.</_>
                <_>
                  12 0 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.4632768947631121e-003</threshold>
            <left_val>0.0690493434667587</left_val>
            <right_val>-0.7033624053001404</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 19 10 1 -1.</_>
                <_>
                  8 19 5 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.1692588925361633e-003</threshold>
            <left_val>-0.1328946053981781</left_val>
            <right_val>0.2209852933883667</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 4 7 16 -1.</_>
                <_>
                  0 12 7 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0293958708643913</threshold>
            <left_val>-0.2853052020072937</left_val>
            <right_val>0.1354399025440216</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 16 1 3 -1.</_>
                <_>
                  2 17 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.6181448316201568e-004</threshold>
            <left_val>-0.5804138183593750</left_val>
            <right_val>0.0374506488442421</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 8 12 6 -1.</_>
                <_>
                  11 8 4 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1082099974155426</threshold>
            <left_val>0.3946728110313416</left_val>
            <right_val>-0.0786559432744980</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 9 6 7 -1.</_>
                <_>
                  16 9 2 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0180248692631722</threshold>
            <left_val>0.2735562920570374</left_val>
            <right_val>-0.1341529935598373</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 17 6 1 -1.</_>
                <_>
                  14 17 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.2509840354323387e-003</threshold>
            <left_val>0.0233880598098040</left_val>
            <right_val>-0.8008859157562256</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 1 3 1 -1.</_>
                <_>
                  17 1 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.6088379779830575e-003</threshold>
            <left_val>-0.5676252245903015</left_val>
            <right_val>0.0412156693637371</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 17 8 2 -1.</_>
                <_>
                  0 17 4 1 2.</_>
                <_>
                  4 18 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.7564752427861094e-004</threshold>
            <left_val>-0.1489126980304718</left_val>
            <right_val>0.1908618062734604</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 0 2 1 -1.</_>
                <_>
                  18 0 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.7122338300105184e-005</threshold>
            <left_val>-0.1555753052234650</left_val>
            <right_val>0.1942822039127350</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 15 6 5 -1.</_>
                <_>
                  6 15 2 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0207553207874298</threshold>
            <left_val>-0.6300653219223023</left_val>
            <right_val>0.0361343808472157</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 2 8 2 -1.</_>
                <_>
                  7 3 8 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.2931738793849945e-003</threshold>
            <left_val>0.2560924887657166</left_val>
            <right_val>-0.1058826968073845</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 1 8 4 -1.</_>
                <_>
                  4 3 8 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0108441496267915</threshold>
            <left_val>-0.1012485027313232</left_val>
            <right_val>0.3032212853431702</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 19 2 1 -1.</_>
                <_>
                  6 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.3752777350600809e-005</threshold>
            <left_val>0.1911157965660095</left_val>
            <right_val>-0.1384923011064529</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 19 2 1 -1.</_>
                <_>
                  6 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.6480963141657412e-005</threshold>
            <left_val>-0.1520525068044663</left_val>
            <right_val>0.2170630991458893</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 17 1 3 -1.</_>
                <_>
                  16 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.3560829684138298e-003</threshold>
            <left_val>0.0494317896664143</left_val>
            <right_val>-0.6427984237670898</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 11 2 3 -1.</_>
                <_>
                  1 11 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.0662558795884252e-004</threshold>
            <left_val>0.1798201054334641</left_val>
            <right_val>-0.1404460966587067</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 19 4 1 -1.</_>
                <_>
                  2 19 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.0473709553480148e-003</threshold>
            <left_val>-0.1093354970216751</left_val>
            <right_val>0.2426594048738480</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 18 4 2 -1.</_>
                <_>
                  2 18 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0243969736620784e-003</threshold>
            <left_val>0.2716268002986908</left_val>
            <right_val>-0.1182091981172562</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 17 1 3 -1.</_>
                <_>
                  2 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.2024149764329195e-003</threshold>
            <left_val>-0.7015110254287720</left_val>
            <right_val>0.0394898988306522</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 7 11 2 -1.</_>
                <_>
                  5 8 11 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.6911649666726589e-003</threshold>
            <left_val>-0.0922189131379128</left_val>
            <right_val>0.3104628920555115</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 2 4 10 -1.</_>
                <_>
                  9 7 4 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1396654993295670</threshold>
            <left_val>0.6897938847541809</left_val>
            <right_val>-0.0397061184048653</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 2 4 3 -1.</_>
                <_>
                  0 3 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.1276050247251987e-003</threshold>
            <left_val>0.0972776114940643</left_val>
            <right_val>-0.2884179949760437</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 19 10 1 -1.</_>
                <_>
                  15 19 5 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.7594310231506824e-003</threshold>
            <left_val>0.2416867017745972</left_val>
            <right_val>-0.1127782016992569</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 17 8 3 -1.</_>
                <_>
                  15 17 4 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.2236132323741913e-003</threshold>
            <left_val>-0.1143027991056442</left_val>
            <right_val>0.2425678074359894</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 19 3 1 -1.</_>
                <_>
                  9 19 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.2590440455824137e-003</threshold>
            <left_val>-0.5967938899993897</left_val>
            <right_val>0.0476639606058598</right_val></_></_>
        <_>
          <!-- tree 36 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 0 3 4 -1.</_>
                <_>
                  15 0 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.7192099262028933e-003</threshold>
            <left_val>-0.4641413092613220</left_val>
            <right_val>0.0528476908802986</right_val></_></_>
        <_>
          <!-- tree 37 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 6 4 3 -1.</_>
                <_>
                  10 7 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.9696151874959469e-003</threshold>
            <left_val>-0.0732442885637283</left_val>
            <right_val>0.3874309062957764</right_val></_></_>
        <_>
          <!-- tree 38 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 8 3 2 -1.</_>
                <_>
                  0 9 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.1776720210909843e-003</threshold>
            <left_val>-0.7419322729110718</left_val>
            <right_val>0.0404967106878757</right_val></_></_>
        <_>
          <!-- tree 39 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 12 3 6 -1.</_>
                <_>
                  7 14 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.0035100430250168e-003</threshold>
            <left_val>-0.1388880014419556</left_val>
            <right_val>0.1876762062311173</right_val></_></_>
        <_>
          <!-- tree 40 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 18 1 2 -1.</_>
                <_>
                  1 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.2013457752764225e-004</threshold>
            <left_val>-0.5494061708450317</left_val>
            <right_val>0.0494178496301174</right_val></_></_>
        <_>
          <!-- tree 41 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 12 4 4 -1.</_>
                <_>
                  2 12 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.3168768063187599e-003</threshold>
            <left_val>-0.0824829787015915</left_val>
            <right_val>0.3174056112766266</right_val></_></_>
        <_>
          <!-- tree 42 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 8 6 7 -1.</_>
                <_>
                  3 8 2 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0147745897993445</threshold>
            <left_val>0.2081609964370728</left_val>
            <right_val>-0.1211555972695351</right_val></_></_>
        <_>
          <!-- tree 43 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 8 4 5 -1.</_>
                <_>
                  2 8 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0414164513349533</threshold>
            <left_val>-0.8243780732154846</left_val>
            <right_val>0.0333291888237000</right_val></_></_></trees>
      <stage_threshold>-1.0368299484252930</stage_threshold>
      <parent>12</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 14 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 16 1 3 -1.</_>
                <_>
                  19 17 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.0962520334869623e-004</threshold>
            <left_val>0.0845799669623375</left_val>
            <right_val>-0.5611841082572937</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 5 18 6 -1.</_>
                <_>
                  7 5 6 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0561397895216942</threshold>
            <left_val>0.1534174978733063</left_val>
            <right_val>-0.2696731984615326</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 15 4 2 -1.</_>
                <_>
                  2 16 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.0292009683325887e-003</threshold>
            <left_val>-0.2048998028039932</left_val>
            <right_val>0.2015317976474762</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 6 2 11 -1.</_>
                <_>
                  19 6 1 11 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.8783010784536600e-003</threshold>
            <left_val>-0.1735114008188248</left_val>
            <right_val>0.2129794955253601</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 12 2 6 -1.</_>
                <_>
                  0 14 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.4144392274320126e-003</threshold>
            <left_val>-0.5962486863136292</left_val>
            <right_val>0.0470779500901699</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 5 3 2 -1.</_>
                <_>
                  12 6 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.4831849839538336e-003</threshold>
            <left_val>0.1902461051940918</left_val>
            <right_val>-0.1598639041185379</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 3 2 3 -1.</_>
                <_>
                  1 4 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.5968941412866116e-003</threshold>
            <left_val>0.0314471311867237</left_val>
            <right_val>-0.6869434118270874</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 14 4 4 -1.</_>
                <_>
                  16 16 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.4255330208688974e-003</threshold>
            <left_val>-0.2360935956239700</left_val>
            <right_val>0.1103610992431641</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 8 12 5 -1.</_>
                <_>
                  10 8 4 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0849505662918091</threshold>
            <left_val>0.2310716062784195</left_val>
            <right_val>-0.1377653032541275</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 7 2 7 -1.</_>
                <_>
                  14 7 1 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.0145681016147137e-003</threshold>
            <left_val>0.3867610991001129</left_val>
            <right_val>-0.0562173798680305</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 8 2 6 -1.</_>
                <_>
                  2 8 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.1482061129063368e-003</threshold>
            <left_val>0.1819159984588623</left_val>
            <right_val>-0.1761569976806641</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 0 3 7 -1.</_>
                <_>
                  16 0 1 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0103967702016234</threshold>
            <left_val>-0.7535138130187988</left_val>
            <right_val>0.0240919701755047</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 2 6 2 -1.</_>
                <_>
                  6 2 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0134667502716184</threshold>
            <left_val>-0.7211886048316956</left_val>
            <right_val>0.0349493697285652</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 9 20 9 -1.</_>
                <_>
                  0 12 20 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0844354778528214</threshold>
            <left_val>-0.3379263877868652</left_val>
            <right_val>0.0711138173937798</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 14 2 2 -1.</_>
                <_>
                  10 15 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.4771490134298801e-003</threshold>
            <left_val>-0.1176510974764824</left_val>
            <right_val>0.2254198938608170</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 5 10 4 -1.</_>
                <_>
                  6 7 10 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0158280506730080</threshold>
            <left_val>-0.0695362165570259</left_val>
            <right_val>0.3139536976814270</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 1 5 9 -1.</_>
                <_>
                  6 4 5 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0649169832468033</threshold>
            <left_val>-0.0750435888767242</left_val>
            <right_val>0.4067733883857727</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 18 2 2 -1.</_>
                <_>
                  16 18 1 1 2.</_>
                <_>
                  17 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.9652469675056636e-004</threshold>
            <left_val>0.0739533603191376</left_val>
            <right_val>-0.3454400897026062</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 14 2 4 -1.</_>
                <_>
                  0 16 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.3129520229995251e-003</threshold>
            <left_val>-0.1690943986177445</left_val>
            <right_val>0.1525837033987045</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 8 2 5 -1.</_>
                <_>
                  11 8 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.8032129891216755e-003</threshold>
            <left_val>0.3526014983654022</left_val>
            <right_val>-0.0834440663456917</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 7 12 7 -1.</_>
                <_>
                  7 7 4 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1479167938232422</threshold>
            <left_val>0.4300465881824493</left_val>
            <right_val>-0.0573099292814732</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 6 6 -1.</_>
                <_>
                  3 0 3 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0165841504931450</threshold>
            <left_val>0.2343268990516663</left_val>
            <right_val>-0.1090764030814171</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 4 4 -1.</_>
                <_>
                  3 0 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.0183270573616028e-003</threshold>
            <left_val>-0.1360093951225281</left_val>
            <right_val>0.2640928924083710</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 6 8 -1.</_>
                <_>
                  2 0 2 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0364719182252884</threshold>
            <left_val>-0.6280974149703980</left_val>
            <right_val>0.0435451082885265</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 2 1 -1.</_>
                <_>
                  1 0 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.3119226726703346e-005</threshold>
            <left_val>0.1647063046693802</left_val>
            <right_val>-0.1646378040313721</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 3 3 -1.</_>
                <_>
                  0 1 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.6719450727105141e-003</threshold>
            <left_val>-0.4742136001586914</left_val>
            <right_val>0.0485869199037552</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 4 2 4 -1.</_>
                <_>
                  5 6 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.0151178836822510e-003</threshold>
            <left_val>0.1822218000888825</left_val>
            <right_val>-0.1409751027822495</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 10 9 1 -1.</_>
                <_>
                  5 10 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0199480205774307</threshold>
            <left_val>-0.0697876587510109</left_val>
            <right_val>0.3670746088027954</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 17 1 3 -1.</_>
                <_>
                  1 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.6699437340721488e-004</threshold>
            <left_val>0.0557292997837067</left_val>
            <right_val>-0.4458543062210083</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 17 2 3 -1.</_>
                <_>
                  0 18 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.1806039838120341e-003</threshold>
            <left_val>-0.4687662124633789</left_val>
            <right_val>0.0489022210240364</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 15 16 3 -1.</_>
                <_>
                  8 15 8 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0158473495393991</threshold>
            <left_val>-0.1212020963430405</left_val>
            <right_val>0.2056653052568436</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 5 4 1 -1.</_>
                <_>
                  2 5 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.1985700111836195e-003</threshold>
            <left_val>0.2026209980249405</left_val>
            <right_val>-0.1282382011413574</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 6 20 -1.</_>
                <_>
                  3 0 2 20 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1096495985984802</threshold>
            <left_val>-0.8661919236183167</left_val>
            <right_val>0.0303518492728472</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 5 4 6 -1.</_>
                <_>
                  2 5 2 3 2.</_>
                <_>
                  4 8 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.2532606795430183e-003</threshold>
            <left_val>0.2934311926364899</left_val>
            <right_val>-0.0853619500994682</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 16 6 3 -1.</_>
                <_>
                  11 16 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0146865304559469</threshold>
            <left_val>0.0327986218035221</left_val>
            <right_val>-0.7755656242370606</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 17 6 1 -1.</_>
                <_>
                  14 17 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3514430029317737e-003</threshold>
            <left_val>0.2442699968814850</left_val>
            <right_val>-0.1150325015187264</right_val></_></_>
        <_>
          <!-- tree 36 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 17 15 2 -1.</_>
                <_>
                  8 17 5 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.3728090822696686e-003</threshold>
            <left_val>0.2168767005205154</left_val>
            <right_val>-0.1398448050022125</right_val></_></_>
        <_>
          <!-- tree 37 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 0 2 3 -1.</_>
                <_>
                  18 1 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.4263390116393566e-003</threshold>
            <left_val>0.0456142202019691</left_val>
            <right_val>-0.5456771254539490</right_val></_></_>
        <_>
          <!-- tree 38 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 1 7 4 -1.</_>
                <_>
                  13 3 7 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.8404068909585476e-003</threshold>
            <left_val>0.1494950056076050</left_val>
            <right_val>-0.1506250947713852</right_val></_></_>
        <_>
          <!-- tree 39 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 6 4 4 -1.</_>
                <_>
                  13 6 2 2 2.</_>
                <_>
                  15 8 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.7988980766385794e-003</threshold>
            <left_val>-0.0873016268014908</left_val>
            <right_val>0.2548153102397919</right_val></_></_>
        <_>
          <!-- tree 40 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 6 3 4 -1.</_>
                <_>
                  17 8 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.0094281062483788e-003</threshold>
            <left_val>0.1725907027721405</left_val>
            <right_val>-0.1428847014904022</right_val></_></_>
        <_>
          <!-- tree 41 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 9 2 2 -1.</_>
                <_>
                  15 9 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.4370709434151649e-003</threshold>
            <left_val>0.2684809863567352</left_val>
            <right_val>-0.0818982198834419</right_val></_></_>
        <_>
          <!-- tree 42 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 17 1 3 -1.</_>
                <_>
                  17 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.0485399980098009e-003</threshold>
            <left_val>0.0461132600903511</left_val>
            <right_val>-0.4724327921867371</right_val></_></_>
        <_>
          <!-- tree 43 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 19 8 1 -1.</_>
                <_>
                  7 19 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.7460780218243599e-003</threshold>
            <left_val>-0.1103043034672737</left_val>
            <right_val>0.2037972956895828</right_val></_></_>
        <_>
          <!-- tree 44 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 9 3 6 -1.</_>
                <_>
                  0 12 3 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.8608627878129482e-003</threshold>
            <left_val>-0.1561965942382813</left_val>
            <right_val>0.1592743992805481</right_val></_></_>
        <_>
          <!-- tree 45 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 7 15 5 -1.</_>
                <_>
                  9 7 5 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0277249794453382</threshold>
            <left_val>0.1134911999106407</left_val>
            <right_val>-0.2188514024019241</right_val></_></_>
        <_>
          <!-- tree 46 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 9 9 5 -1.</_>
                <_>
                  9 9 3 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0470806397497654</threshold>
            <left_val>-0.0416887290775776</left_val>
            <right_val>0.5363004803657532</right_val></_></_>
        <_>
          <!-- tree 47 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 1 6 2 -1.</_>
                <_>
                  10 1 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.9283770173788071e-003</threshold>
            <left_val>-0.5359513163566589</left_val>
            <right_val>0.0442375093698502</right_val></_></_>
        <_>
          <!-- tree 48 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 0 12 2 -1.</_>
                <_>
                  10 0 6 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0128805404528975</threshold>
            <left_val>0.2323794960975647</left_val>
            <right_val>-0.1024625003337860</right_val></_></_>
        <_>
          <!-- tree 49 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 0 10 3 -1.</_>
                <_>
                  12 0 5 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0236047692596912</threshold>
            <left_val>-0.0882914364337921</left_val>
            <right_val>0.3056105971336365</right_val></_></_>
        <_>
          <!-- tree 50 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 0 9 6 -1.</_>
                <_>
                  5 2 9 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0159022007137537</threshold>
            <left_val>-0.1223810985684395</left_val>
            <right_val>0.1784912049770355</right_val></_></_>
        <_>
          <!-- tree 51 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 3 6 4 -1.</_>
                <_>
                  8 5 6 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.9939495772123337e-003</threshold>
            <left_val>-0.0837290063500404</left_val>
            <right_val>0.3231959044933319</right_val></_></_>
        <_>
          <!-- tree 52 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 4 2 3 -1.</_>
                <_>
                  17 5 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.7100867852568626e-003</threshold>
            <left_val>0.0384792089462280</left_val>
            <right_val>-0.6813815236091614</right_val></_></_></trees>
      <stage_threshold>-1.0492420196533203</stage_threshold>
      <parent>13</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 15 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 2 4 3 -1.</_>
                <_>
                  5 3 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.2480720654129982e-003</threshold>
            <left_val>-0.1641687005758286</left_val>
            <right_val>0.4164853096008301</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 9 2 6 -1.</_>
                <_>
                  6 9 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.5813550241291523e-003</threshold>
            <left_val>-0.1246595978736877</left_val>
            <right_val>0.4038512110710144</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 10 2 6 -1.</_>
                <_>
                  15 10 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.6073239967226982e-003</threshold>
            <left_val>0.2608245909214020</left_val>
            <right_val>-0.2028252035379410</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 4 3 3 -1.</_>
                <_>
                  7 5 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.5205370038747787e-003</threshold>
            <left_val>-0.1055722981691361</left_val>
            <right_val>0.3666911125183106</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 4 8 2 -1.</_>
                <_>
                  12 4 4 1 2.</_>
                <_>
                  16 5 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.4119189474731684e-003</threshold>
            <left_val>-0.1387760043144226</left_val>
            <right_val>0.2995991110801697</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 8 1 6 -1.</_>
                <_>
                  15 10 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.7156179100275040e-003</threshold>
            <left_val>-0.0776834636926651</left_val>
            <right_val>0.4848192036151886</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 17 11 3 -1.</_>
                <_>
                  4 18 11 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.1093840952962637e-003</threshold>
            <left_val>-0.1122900024056435</left_val>
            <right_val>0.2921550869941711</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 0 16 20 -1.</_>
                <_>
                  3 10 16 10 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0868366286158562</threshold>
            <left_val>-0.3677960038185120</left_val>
            <right_val>0.0725972428917885</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 4 4 6 -1.</_>
                <_>
                  12 6 4 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.2652182057499886e-003</threshold>
            <left_val>-0.1089029014110565</left_val>
            <right_val>0.3179126083850861</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 0 6 6 -1.</_>
                <_>
                  13 0 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0199135299772024</threshold>
            <left_val>-0.5337343811988831</left_val>
            <right_val>0.0705857127904892</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 1 6 4 -1.</_>
                <_>
                  13 1 3 2 2.</_>
                <_>
                  16 3 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.8297839928418398e-003</threshold>
            <left_val>-0.1357591003179550</left_val>
            <right_val>0.2278887927532196</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 0 6 4 -1.</_>
                <_>
                  13 0 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0104318596422672</threshold>
            <left_val>0.0887979120016098</left_val>
            <right_val>-0.4795897006988525</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 6 6 9 -1.</_>
                <_>
                  10 6 2 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0200404394418001</threshold>
            <left_val>0.1574553996324539</left_val>
            <right_val>-0.1777157038450241</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 0 3 4 -1.</_>
                <_>
                  8 0 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.2967290394008160e-003</threshold>
            <left_val>-0.6843491792678833</left_val>
            <right_val>0.0356714613735676</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 17 14 2 -1.</_>
                <_>
                  0 17 7 1 2.</_>
                <_>
                  7 18 7 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.1624139044433832e-003</threshold>
            <left_val>0.2831803858280182</left_val>
            <right_val>-0.0985112786293030</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 18 2 2 -1.</_>
                <_>
                  6 18 1 1 2.</_>
                <_>
                  7 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.5464888787828386e-004</threshold>
            <left_val>-0.3707734048366547</left_val>
            <right_val>0.0809329524636269</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 17 1 3 -1.</_>
                <_>
                  18 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.8152060511056334e-004</threshold>
            <left_val>-0.3220703005790710</left_val>
            <right_val>0.0775510594248772</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 18 2 2 -1.</_>
                <_>
                  17 18 1 1 2.</_>
                <_>
                  18 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.7563021285459399e-004</threshold>
            <left_val>-0.3244127929210663</left_val>
            <right_val>0.0879494771361351</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 7 1 9 -1.</_>
                <_>
                  5 10 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.3823810778558254e-003</threshold>
            <left_val>-0.0889247134327888</left_val>
            <right_val>0.3172721862792969</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 3 6 4 -1.</_>
                <_>
                  7 3 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0111509095877409</threshold>
            <left_val>0.0710198432207108</left_val>
            <right_val>-0.4049403965473175</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 9 6 2 -1.</_>
                <_>
                  1 9 3 1 2.</_>
                <_>
                  4 10 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0593760525807738e-003</threshold>
            <left_val>0.2605066895484924</left_val>
            <right_val>-0.1176564022898674</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 9 2 3 -1.</_>
                <_>
                  7 9 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.3906480055302382e-003</threshold>
            <left_val>-0.0843886211514473</left_val>
            <right_val>0.3123055100440979</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 8 6 12 -1.</_>
                <_>
                  8 8 2 12 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0110007496550679</threshold>
            <left_val>0.1915224939584732</left_val>
            <right_val>-0.1521002054214478</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 18 2 2 -1.</_>
                <_>
                  4 18 1 1 2.</_>
                <_>
                  5 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.4643228971399367e-004</threshold>
            <left_val>-0.3176515996456146</left_val>
            <right_val>0.0865822583436966</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 1 6 6 -1.</_>
                <_>
                  9 3 6 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0230532698333263</threshold>
            <left_val>-0.1008976027369499</left_val>
            <right_val>0.2576929032802582</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 17 6 2 -1.</_>
                <_>
                  6 18 6 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2135660983622074e-003</threshold>
            <left_val>0.4568921029567719</left_val>
            <right_val>-0.0524047911167145</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 18 16 2 -1.</_>
                <_>
                  3 19 16 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.7139709396287799e-004</threshold>
            <left_val>-0.3551838099956513</left_val>
            <right_val>0.0800943821668625</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 0 3 11 -1.</_>
                <_>
                  4 0 1 11 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.5676229959353805e-003</threshold>
            <left_val>0.1009142026305199</left_val>
            <right_val>-0.2160304039716721</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 18 3 1 -1.</_>
                <_>
                  14 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.5460801599547267e-004</threshold>
            <left_val>0.0578961782157421</left_val>
            <right_val>-0.4046111106872559</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 0 9 6 -1.</_>
                <_>
                  6 2 9 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0206989701837301</threshold>
            <left_val>0.3154363036155701</left_val>
            <right_val>-0.0807130485773087</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 2 12 4 -1.</_>
                <_>
                  1 2 6 2 2.</_>
                <_>
                  7 4 6 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0206199400126934</threshold>
            <left_val>0.2718166112899780</left_val>
            <right_val>-0.0763586163520813</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 3 6 4 -1.</_>
                <_>
                  5 3 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0216111298650503</threshold>
            <left_val>0.0394934490323067</left_val>
            <right_val>-0.5942965149879456</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 0 8 1 -1.</_>
                <_>
                  16 0 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.5676742233335972e-003</threshold>
            <left_val>-0.0983536690473557</left_val>
            <right_val>0.2364927977323532</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 0 6 2 -1.</_>
                <_>
                  11 0 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.8434796780347824e-003</threshold>
            <left_val>-0.5252342820167542</left_val>
            <right_val>0.0430999211966991</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 3 12 1 -1.</_>
                <_>
                  9 3 6 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.4260741025209427e-003</threshold>
            <left_val>0.2466513067483902</left_val>
            <right_val>-0.0941307172179222</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 7 6 2 -1.</_>
                <_>
                  2 7 3 1 2.</_>
                <_>
                  5 8 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.9830230157822371e-003</threshold>
            <left_val>0.2674370110034943</left_val>
            <right_val>-0.0900693163275719</right_val></_></_>
        <_>
          <!-- tree 36 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 8 4 6 -1.</_>
                <_>
                  0 10 4 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7358399927616119e-003</threshold>
            <left_val>0.1594001948833466</left_val>
            <right_val>-0.1578941047191620</right_val></_></_>
        <_>
          <!-- tree 37 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 6 3 7 -1.</_>
                <_>
                  10 6 1 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0135138696059585</threshold>
            <left_val>0.4079233109951019</left_val>
            <right_val>-0.0642231181263924</right_val></_></_>
        <_>
          <!-- tree 38 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 6 6 13 -1.</_>
                <_>
                  11 6 2 13 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0193940103054047</threshold>
            <left_val>0.1801564991474152</left_val>
            <right_val>-0.1373140066862106</right_val></_></_>
        <_>
          <!-- tree 39 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 12 6 1 -1.</_>
                <_>
                  13 12 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.2684770412743092e-003</threshold>
            <left_val>0.2908039093017578</left_val>
            <right_val>-0.0801619067788124</right_val></_></_>
        <_>
          <!-- tree 40 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 9 2 6 -1.</_>
                <_>
                  18 12 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.1773589327931404e-004</threshold>
            <left_val>-0.2141298055648804</left_val>
            <right_val>0.1127343997359276</right_val></_></_>
        <_>
          <!-- tree 41 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 2 3 9 -1.</_>
                <_>
                  18 2 1 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.6351119205355644e-003</threshold>
            <left_val>-0.4536595940589905</left_val>
            <right_val>0.0546250604093075</right_val></_></_>
        <_>
          <!-- tree 42 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 8 4 6 -1.</_>
                <_>
                  13 8 2 3 2.</_>
                <_>
                  15 11 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.3652976900339127e-003</threshold>
            <left_val>0.2647292017936707</left_val>
            <right_val>-0.0943341106176376</right_val></_></_>
        <_>
          <!-- tree 43 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 2 12 6 -1.</_>
                <_>
                  10 2 6 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0277684498578310</threshold>
            <left_val>-0.1013671010732651</left_val>
            <right_val>0.2074397951364517</right_val></_></_>
        <_>
          <!-- tree 44 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 14 16 6 -1.</_>
                <_>
                  12 14 8 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0548912286758423</threshold>
            <left_val>0.2884030938148499</left_val>
            <right_val>-0.0753120407462120</right_val></_></_>
        <_>
          <!-- tree 45 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 19 10 1 -1.</_>
                <_>
                  11 19 5 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.5793339591473341e-003</threshold>
            <left_val>-0.1108852997422218</left_val>
            <right_val>0.2172496020793915</right_val></_></_>
        <_>
          <!-- tree 46 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 17 1 3 -1.</_>
                <_>
                  6 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.6196516854688525e-005</threshold>
            <left_val>-0.1887210011482239</left_val>
            <right_val>0.1444068998098373</right_val></_></_>
        <_>
          <!-- tree 47 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 14 10 3 -1.</_>
                <_>
                  4 15 10 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.0907251425087452e-003</threshold>
            <left_val>-0.0776012316346169</left_val>
            <right_val>0.2939837872982025</right_val></_></_>
        <_>
          <!-- tree 48 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 0 12 12 -1.</_>
                <_>
                  6 4 12 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1044425964355469</threshold>
            <left_val>0.2013310939073563</left_val>
            <right_val>-0.1090397015213966</right_val></_></_>
        <_>
          <!-- tree 49 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 7 4 2 -1.</_>
                <_>
                  5 7 2 1 2.</_>
                <_>
                  7 8 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.7273090826347470e-004</threshold>
            <left_val>0.1794590055942535</left_val>
            <right_val>-0.1202367022633553</right_val></_></_>
        <_>
          <!-- tree 50 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 5 3 2 -1.</_>
                <_>
                  18 5 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.2412849832326174e-003</threshold>
            <left_val>0.0406881310045719</left_val>
            <right_val>-0.5460057258605957</right_val></_></_></trees>
      <stage_threshold>-1.1122100353240967</stage_threshold>
      <parent>14</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 16 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 13 6 3 -1.</_>
                <_>
                  8 14 6 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.2965320646762848e-003</threshold>
            <left_val>-0.1215452998876572</left_val>
            <right_val>0.6442037224769592</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 13 5 3 -1.</_>
                <_>
                  8 14 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.5326260365545750e-003</threshold>
            <left_val>0.5123322010040283</left_val>
            <right_val>-0.1110825985670090</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 2 1 18 -1.</_>
                <_>
                  13 11 1 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.9183230362832546e-003</threshold>
            <left_val>-0.5061542987823486</left_val>
            <right_val>0.1150197982788086</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 10 9 2 -1.</_>
                <_>
                  9 10 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0236923396587372</threshold>
            <left_val>0.3716728091239929</left_val>
            <right_val>-0.1467268019914627</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 0 7 4 -1.</_>
                <_>
                  11 2 7 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0201774705201387</threshold>
            <left_val>-0.1738884001970291</left_val>
            <right_val>0.4775949120521545</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 6 8 -1.</_>
                <_>
                  3 0 2 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0217232108116150</threshold>
            <left_val>-0.4388009011745453</left_val>
            <right_val>0.1357689946889877</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 15 3 3 -1.</_>
                <_>
                  9 16 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.8369780629873276e-003</threshold>
            <left_val>-0.1251206994056702</left_val>
            <right_val>0.4678902924060822</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 17 9 3 -1.</_>
                <_>
                  9 18 9 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.7148420922458172e-003</threshold>
            <left_val>-0.0880188569426537</left_val>
            <right_val>0.3686651885509491</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 12 3 3 -1.</_>
                <_>
                  12 13 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.2625689636915922e-003</threshold>
            <left_val>-0.0853353068232536</left_val>
            <right_val>0.5164473056793213</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 1 3 5 -1.</_>
                <_>
                  5 1 1 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.5618850961327553e-003</threshold>
            <left_val>-0.4450393021106720</left_val>
            <right_val>0.0917381718754768</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 14 2 3 -1.</_>
                <_>
                  10 15 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.9227749435231090e-003</threshold>
            <left_val>-0.1107731014490128</left_val>
            <right_val>0.3941699862480164</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 17 2 2 -1.</_>
                <_>
                  18 17 1 1 2.</_>
                <_>
                  19 18 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.5111969918943942e-004</threshold>
            <left_val>-0.3777570128440857</left_val>
            <right_val>0.1216617003083229</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 18 2 2 -1.</_>
                <_>
                  18 18 1 1 2.</_>
                <_>
                  19 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.9121779769193381e-004</threshold>
            <left_val>0.0748160183429718</left_val>
            <right_val>-0.4076710045337677</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 18 2 2 -1.</_>
                <_>
                  18 18 1 1 2.</_>
                <_>
                  19 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.6525629800744355e-004</threshold>
            <left_val>-0.3315171897411346</left_val>
            <right_val>0.1129112020134926</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 10 9 1 -1.</_>
                <_>
                  7 10 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0200867000967264</threshold>
            <left_val>-0.0615981183946133</left_val>
            <right_val>0.5612881779670715</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 9 6 5 -1.</_>
                <_>
                  5 9 2 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0367832481861115</threshold>
            <left_val>-0.0602513886988163</left_val>
            <right_val>0.5219249129295349</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 8 1 12 -1.</_>
                <_>
                  18 14 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.3941619545221329e-003</threshold>
            <left_val>-0.3550305068492889</left_val>
            <right_val>0.1086302027106285</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 2 8 6 -1.</_>
                <_>
                  0 2 4 3 2.</_>
                <_>
                  4 5 4 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0151816699653864</threshold>
            <left_val>0.2273965030908585</left_val>
            <right_val>-0.1625299006700516</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 4 3 3 -1.</_>
                <_>
                  9 5 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.6796840615570545e-003</threshold>
            <left_val>-0.0575350411236286</left_val>
            <right_val>0.4812423884868622</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 18 2 2 -1.</_>
                <_>
                  3 18 1 1 2.</_>
                <_>
                  4 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7988319450523704e-004</threshold>
            <left_val>-0.3058767020702362</left_val>
            <right_val>0.1086815968155861</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 4 4 3 -1.</_>
                <_>
                  6 5 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.5850999411195517e-003</threshold>
            <left_val>0.3859694004058838</left_val>
            <right_val>-0.0921940729022026</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 7 4 2 -1.</_>
                <_>
                  16 7 2 1 2.</_>
                <_>
                  18 8 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.0793360415846109e-003</threshold>
            <left_val>-0.1119038984179497</left_val>
            <right_val>0.3112520873546600</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 17 1 3 -1.</_>
                <_>
                  5 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.3285802500322461e-005</threshold>
            <left_val>-0.2023991048336029</left_val>
            <right_val>0.1558668017387390</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 0 15 20 -1.</_>
                <_>
                  2 10 15 10 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.1367873996496201</threshold>
            <left_val>-0.2167285978794098</left_val>
            <right_val>0.1442039012908936</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 11 6 4 -1.</_>
                <_>
                  8 11 3 2 2.</_>
                <_>
                  11 13 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0117292599752545</threshold>
            <left_val>0.4350377023220062</left_val>
            <right_val>-0.0748865306377411</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 16 4 3 -1.</_>
                <_>
                  8 17 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.9230841211974621e-003</threshold>
            <left_val>-0.0502893291413784</left_val>
            <right_val>0.5883116126060486</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 18 2 2 -1.</_>
                <_>
                  8 18 1 1 2.</_>
                <_>
                  9 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.9819121118634939e-004</threshold>
            <left_val>-0.3823240101337433</left_val>
            <right_val>0.0924511328339577</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 16 13 3 -1.</_>
                <_>
                  2 17 13 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.7992770560085773e-003</threshold>
            <left_val>0.4848878979682922</left_val>
            <right_val>-0.0731365233659744</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 16 2 2 -1.</_>
                <_>
                  16 16 1 1 2.</_>
                <_>
                  17 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.0155890271998942e-004</threshold>
            <left_val>-0.3575735986232758</left_val>
            <right_val>0.1058188006281853</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 1 6 3 -1.</_>
                <_>
                  10 1 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0103907696902752</threshold>
            <left_val>0.0529204681515694</left_val>
            <right_val>-0.5724965929985046</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 7 2 2 -1.</_>
                <_>
                  16 7 1 1 2.</_>
                <_>
                  17 8 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.4488041941076517e-004</threshold>
            <left_val>0.4496682882308960</left_val>
            <right_val>-0.0830755233764648</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 7 4 2 -1.</_>
                <_>
                  14 7 2 1 2.</_>
                <_>
                  16 8 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.2651870492845774e-003</threshold>
            <left_val>-0.0966954380273819</left_val>
            <right_val>0.3130227029323578</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 0 14 1 -1.</_>
                <_>
                  11 0 7 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0170945394784212</threshold>
            <left_val>-0.0812489762902260</left_val>
            <right_val>0.3611383140087128</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 4 8 2 -1.</_>
                <_>
                  10 4 4 1 2.</_>
                <_>
                  14 5 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.5973359588533640e-003</threshold>
            <left_val>-0.1133835017681122</left_val>
            <right_val>0.2223394960165024</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 2 3 2 -1.</_>
                <_>
                  9 2 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.4527440071105957e-003</threshold>
            <left_val>0.0697504431009293</left_val>
            <right_val>-0.3672071099281311</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 11 6 3 -1.</_>
                <_>
                  12 12 6 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.7638658434152603e-003</threshold>
            <left_val>-0.0657889619469643</left_val>
            <right_val>0.3832854032516480</right_val></_></_>
        <_>
          <!-- tree 36 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 5 1 4 -1.</_>
                <_>
                  1 7 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.2501081265509129e-003</threshold>
            <left_val>-0.7075446844100952</left_val>
            <right_val>0.0383501984179020</right_val></_></_>
        <_>
          <!-- tree 37 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 1 1 18 -1.</_>
                <_>
                  1 7 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.1765329185873270e-003</threshold>
            <left_val>0.1375540047883987</left_val>
            <right_val>-0.2324002981185913</right_val></_></_>
        <_>
          <!-- tree 38 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 13 3 2 -1.</_>
                <_>
                  11 14 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.2191169448196888e-003</threshold>
            <left_val>-0.1293545067310333</left_val>
            <right_val>0.2273788005113602</right_val></_></_>
        <_>
          <!-- tree 39 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 1 12 2 -1.</_>
                <_>
                  0 1 6 1 2.</_>
                <_>
                  6 2 6 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.6365579366683960e-003</threshold>
            <left_val>0.3806715011596680</left_val>
            <right_val>-0.0672468394041061</right_val></_></_>
        <_>
          <!-- tree 40 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 18 2 2 -1.</_>
                <_>
                  10 18 1 1 2.</_>
                <_>
                  11 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.3844049428589642e-004</threshold>
            <left_val>-0.3112238049507141</left_val>
            <right_val>0.0838383585214615</right_val></_></_>
        <_>
          <!-- tree 41 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 5 4 4 -1.</_>
                <_>
                  4 5 2 2 2.</_>
                <_>
                  6 7 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.1017560288310051e-003</threshold>
            <left_val>0.2606728076934815</left_val>
            <right_val>-0.1044974029064179</right_val></_></_>
        <_>
          <!-- tree 42 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 7 1 3 -1.</_>
                <_>
                  6 8 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.3336989795789123e-003</threshold>
            <left_val>-0.0582501403987408</left_val>
            <right_val>0.4768244028091431</right_val></_></_>
        <_>
          <!-- tree 43 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 10 6 2 -1.</_>
                <_>
                  16 10 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.2090239906683564e-003</threshold>
            <left_val>0.1483450978994370</left_val>
            <right_val>-0.1732946932315826</right_val></_></_></trees>
      <stage_threshold>-1.2529590129852295</stage_threshold>
      <parent>15</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 17 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 8 3 6 -1.</_>
                <_>
                  17 8 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.1760931015014648e-003</threshold>
            <left_val>0.3333333134651184</left_val>
            <right_val>-0.1664234995841980</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 10 6 2 -1.</_>
                <_>
                  6 10 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0248580798506737</threshold>
            <left_val>-0.0727288722991943</left_val>
            <right_val>0.5667458176612854</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 5 3 7 -1.</_>
                <_>
                  7 5 1 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.7597280032932758e-003</threshold>
            <left_val>0.4625856876373291</left_val>
            <right_val>-0.0931121781468391</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 13 6 6 -1.</_>
                <_>
                  0 16 6 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.8239021822810173e-003</threshold>
            <left_val>-0.2741461098194122</left_val>
            <right_val>0.1324304938316345</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 5 1 9 -1.</_>
                <_>
                  12 8 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0109488395974040</threshold>
            <left_val>0.2234548032283783</left_val>
            <right_val>-0.1496544927358627</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 9 3 3 -1.</_>
                <_>
                  6 9 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.4349008928984404e-003</threshold>
            <left_val>0.3872498869895935</left_val>
            <right_val>-0.0661217272281647</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 5 6 13 -1.</_>
                <_>
                  9 5 2 13 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0311562903225422</threshold>
            <left_val>0.2407827973365784</left_val>
            <right_val>-0.1140690967440605</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 8 1 10 -1.</_>
                <_>
                  19 13 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.1100519914180040e-003</threshold>
            <left_val>-0.2820797860622406</left_val>
            <right_val>0.1327542960643768</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 18 6 1 -1.</_>
                <_>
                  13 18 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.1762740109115839e-003</threshold>
            <left_val>0.0345859304070473</left_val>
            <right_val>-0.5137431025505066</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 7 6 12 -1.</_>
                <_>
                  11 7 2 12 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0279774591326714</threshold>
            <left_val>0.2392677962779999</left_val>
            <right_val>-0.1325591951608658</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 7 6 6 -1.</_>
                <_>
                  14 7 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0230979397892952</threshold>
            <left_val>0.3901962041854858</left_val>
            <right_val>-0.0784780085086823</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 8 3 4 -1.</_>
                <_>
                  16 8 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.9731930010020733e-003</threshold>
            <left_val>0.3069106936454773</left_val>
            <right_val>-0.0706014037132263</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 11 4 2 -1.</_>
                <_>
                  6 12 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.0335749033838511e-003</threshold>
            <left_val>-0.1400219053030014</left_val>
            <right_val>0.1913485974073410</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 6 6 8 -1.</_>
                <_>
                  3 6 2 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0108443703502417</threshold>
            <left_val>0.1654873043298721</left_val>
            <right_val>-0.1565777957439423</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 15 6 5 -1.</_>
                <_>
                  13 15 2 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0181505102664232</threshold>
            <left_val>-0.6324359178543091</left_val>
            <right_val>0.0395618192851543</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 17 4 2 -1.</_>
                <_>
                  15 18 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.1052298881113529e-004</threshold>
            <left_val>-0.1851557046175003</left_val>
            <right_val>0.1340880990028381</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 11 6 1 -1.</_>
                <_>
                  15 11 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0108933402225375</threshold>
            <left_val>-0.0267302300781012</left_val>
            <right_val>0.6097180247306824</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 18 2 2 -1.</_>
                <_>
                  5 18 1 1 2.</_>
                <_>
                  6 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.8780900174751878e-004</threshold>
            <left_val>-0.3006514012813568</left_val>
            <right_val>0.0731714591383934</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 8 4 4 -1.</_>
                <_>
                  4 8 2 2 2.</_>
                <_>
                  6 10 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.5855069290846586e-003</threshold>
            <left_val>0.2621760964393616</left_val>
            <right_val>-0.0797140970826149</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 7 9 3 -1.</_>
                <_>
                  11 8 9 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0197592806071043</threshold>
            <left_val>-0.5903922915458679</left_val>
            <right_val>0.0406989715993404</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 3 10 4 -1.</_>
                <_>
                  0 3 5 2 2.</_>
                <_>
                  5 5 5 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0108452104032040</threshold>
            <left_val>0.1636455953121185</left_val>
            <right_val>-0.1258606016635895</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 18 6 1 -1.</_>
                <_>
                  9 18 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.3183090165257454e-003</threshold>
            <left_val>-0.5747488141059876</left_val>
            <right_val>0.0376443117856979</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 8 3 3 -1.</_>
                <_>
                  0 9 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.4913700288161635e-003</threshold>
            <left_val>0.0609134696424007</left_val>
            <right_val>-0.3022292852401733</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 6 8 -1.</_>
                <_>
                  0 0 3 4 2.</_>
                <_>
                  3 4 3 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0156756993383169</threshold>
            <left_val>-0.0731459110975266</left_val>
            <right_val>0.2937945127487183</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 6 3 8 -1.</_>
                <_>
                  8 6 1 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0110335601493716</threshold>
            <left_val>0.3931880891323090</left_val>
            <right_val>-0.0470843203365803</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 7 7 3 -1.</_>
                <_>
                  13 8 7 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.8555756956338882e-003</threshold>
            <left_val>0.0376013815402985</left_val>
            <right_val>-0.4910849034786224</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 3 2 2 -1.</_>
                <_>
                  3 4 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.9665671112015843e-004</threshold>
            <left_val>0.1795202046632767</left_val>
            <right_val>-0.1108623966574669</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 3 3 3 -1.</_>
                <_>
                  0 4 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.0592409893870354e-003</threshold>
            <left_val>-0.4442946016788483</left_val>
            <right_val>0.0510054305195808</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 3 5 2 -1.</_>
                <_>
                  9 4 5 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.3201179727911949e-003</threshold>
            <left_val>-0.0528410896658897</left_val>
            <right_val>0.3719710111618042</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 5 9 4 -1.</_>
                <_>
                  9 5 3 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0206828303635120</threshold>
            <left_val>0.0576671697199345</left_val>
            <right_val>-0.3690159916877747</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 10 12 3 -1.</_>
                <_>
                  7 10 4 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0998226627707481</threshold>
            <left_val>-0.0373770184814930</left_val>
            <right_val>0.5816559195518494</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 7 3 6 -1.</_>
                <_>
                  9 7 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.5854229032993317e-003</threshold>
            <left_val>0.2850944101810455</left_val>
            <right_val>-0.0609780699014664</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 5 6 5 -1.</_>
                <_>
                  8 5 3 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0609003007411957</threshold>
            <left_val>-0.5103176832199097</left_val>
            <right_val>0.0377874001860619</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 5 2 3 -1.</_>
                <_>
                  0 6 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.9991709161549807e-003</threshold>
            <left_val>-0.4794301092624664</left_val>
            <right_val>0.0388338901102543</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 7 3 4 -1.</_>
                <_>
                  10 7 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.8906438797712326e-003</threshold>
            <left_val>0.4060907959938049</left_val>
            <right_val>-0.0478696487843990</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 6 15 -1.</_>
                <_>
                  3 0 2 15 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0826889276504517</threshold>
            <left_val>-0.7067118287086487</left_val>
            <right_val>0.0274877492338419</right_val></_></_>
        <_>
          <!-- tree 36 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 1 3 5 -1.</_>
                <_>
                  16 1 1 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.0060399807989597e-003</threshold>
            <left_val>0.0282084401696920</left_val>
            <right_val>-0.5290969014167786</right_val></_></_>
        <_>
          <!-- tree 37 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 2 3 10 -1.</_>
                <_>
                  10 2 1 10 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.1695030890405178e-003</threshold>
            <left_val>-0.0545548610389233</left_val>
            <right_val>0.3283798098564148</right_val></_></_>
        <_>
          <!-- tree 38 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 8 6 12 -1.</_>
                <_>
                  10 8 2 12 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.3914761152118444e-003</threshold>
            <left_val>0.0921176671981812</left_val>
            <right_val>-0.2163711041212082</right_val></_></_>
        <_>
          <!-- tree 39 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 4 3 4 -1.</_>
                <_>
                  16 6 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.6131230406463146e-003</threshold>
            <left_val>0.1365101933479309</left_val>
            <right_val>-0.1378113031387329</right_val></_></_>
        <_>
          <!-- tree 40 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 7 2 2 -1.</_>
                <_>
                  16 7 1 1 2.</_>
                <_>
                  17 8 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.0490659456700087e-004</threshold>
            <left_val>-0.0686371102929115</left_val>
            <right_val>0.3358106911182404</right_val></_></_>
        <_>
          <!-- tree 41 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 0 6 9 -1.</_>
                <_>
                  13 3 6 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0381065085530281</threshold>
            <left_val>0.2944543063640595</left_val>
            <right_val>-0.0682392269372940</right_val></_></_>
        <_>
          <!-- tree 42 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 17 1 3 -1.</_>
                <_>
                  7 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.2450799052603543e-005</threshold>
            <left_val>-0.1675013005733490</left_val>
            <right_val>0.1217823028564453</right_val></_></_>
        <_>
          <!-- tree 43 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 1 4 2 -1.</_>
                <_>
                  12 2 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.5837959945201874e-003</threshold>
            <left_val>-0.0920428484678268</left_val>
            <right_val>0.2134899049997330</right_val></_></_>
        <_>
          <!-- tree 44 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 3 1 3 -1.</_>
                <_>
                  17 4 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.2924340553581715e-003</threshold>
            <left_val>0.0629172325134277</left_val>
            <right_val>-0.3617450892925263</right_val></_></_>
        <_>
          <!-- tree 45 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 16 9 3 -1.</_>
                <_>
                  0 17 9 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.9146775901317596e-003</threshold>
            <left_val>0.0195340607315302</left_val>
            <right_val>-0.8101503849029541</right_val></_></_>
        <_>
          <!-- tree 46 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 6 2 4 -1.</_>
                <_>
                  3 6 1 2 2.</_>
                <_>
                  4 8 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7086310544982553e-003</threshold>
            <left_val>0.2552523910999298</left_val>
            <right_val>-0.0682294592261314</right_val></_></_>
        <_>
          <!-- tree 47 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 18 3 1 -1.</_>
                <_>
                  14 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.1844399161636829e-003</threshold>
            <left_val>0.0233140494674444</left_val>
            <right_val>-0.8429678082466126</right_val></_></_>
        <_>
          <!-- tree 48 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 18 4 2 -1.</_>
                <_>
                  2 18 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.4244330599904060e-003</threshold>
            <left_val>0.2721368968486786</left_val>
            <right_val>-0.0763952285051346</right_val></_></_>
        <_>
          <!-- tree 49 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 19 2 1 -1.</_>
                <_>
                  2 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.7591470279730856e-004</threshold>
            <left_val>-0.1074284017086029</left_val>
            <right_val>0.2288897037506104</right_val></_></_>
        <_>
          <!-- tree 50 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 18 4 2 -1.</_>
                <_>
                  0 19 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.0005177510902286e-004</threshold>
            <left_val>-0.2985421121120453</left_val>
            <right_val>0.0634797364473343</right_val></_></_>
        <_>
          <!-- tree 51 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 17 1 3 -1.</_>
                <_>
                  2 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.5001438916660845e-004</threshold>
            <left_val>-0.2717896997928619</left_val>
            <right_val>0.0696150064468384</right_val></_></_>
        <_>
          <!-- tree 52 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 8 3 5 -1.</_>
                <_>
                  5 8 1 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.8751391954720020e-003</threshold>
            <left_val>-0.0571858994662762</left_val>
            <right_val>0.3669595122337341</right_val></_></_>
        <_>
          <!-- tree 53 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 1 6 7 -1.</_>
                <_>
                  4 1 2 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0127619002014399</threshold>
            <left_val>0.0679556876420975</left_val>
            <right_val>-0.2853415012359619</right_val></_></_>
        <_>
          <!-- tree 54 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 6 2 8 -1.</_>
                <_>
                  3 6 1 4 2.</_>
                <_>
                  4 10 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.4752789866179228e-003</threshold>
            <left_val>0.2068066000938416</left_val>
            <right_val>-0.1005939021706581</right_val></_></_>
        <_>
          <!-- tree 55 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 5 11 10 -1.</_>
                <_>
                  4 10 11 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.1213881969451904</threshold>
            <left_val>-0.0971267968416214</left_val>
            <right_val>0.1978961974382401</right_val></_></_>
        <_>
          <!-- tree 56 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 13 20 2 -1.</_>
                <_>
                  10 13 10 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0500812791287899</threshold>
            <left_val>0.2841717898845673</left_val>
            <right_val>-0.0678799971938133</right_val></_></_>
        <_>
          <!-- tree 57 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 13 16 3 -1.</_>
                <_>
                  9 13 8 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0314549505710602</threshold>
            <left_val>-0.0894686728715897</left_val>
            <right_val>0.2129842042922974</right_val></_></_>
        <_>
          <!-- tree 58 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 4 4 4 -1.</_>
                <_>
                  16 4 2 2 2.</_>
                <_>
                  18 6 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.8878319533541799e-003</threshold>
            <left_val>-0.1165644004940987</left_val>
            <right_val>0.1666352003812790</right_val></_></_>
        <_>
          <!-- tree 59 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 0 4 12 -1.</_>
                <_>
                  16 0 2 6 2.</_>
                <_>
                  18 6 2 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.7211960665881634e-003</threshold>
            <left_val>0.2370214015245438</left_val>
            <right_val>-0.0907766073942184</right_val></_></_>
        <_>
          <!-- tree 60 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 15 3 1 -1.</_>
                <_>
                  15 15 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.8076719425152987e-004</threshold>
            <left_val>0.1795192956924439</left_val>
            <right_val>-0.1079348027706146</right_val></_></_>
        <_>
          <!-- tree 61 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 4 12 10 -1.</_>
                <_>
                  3 9 12 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1976184993982315</threshold>
            <left_val>0.4567429125308991</left_val>
            <right_val>-0.0404801592230797</right_val></_></_>
        <_>
          <!-- tree 62 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 18 2 2 -1.</_>
                <_>
                  9 18 1 1 2.</_>
                <_>
                  10 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.3846809926908463e-004</threshold>
            <left_val>-0.2373300939798355</left_val>
            <right_val>0.0759221613407135</right_val></_></_>
        <_>
          <!-- tree 63 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 18 2 2 -1.</_>
                <_>
                  9 18 1 1 2.</_>
                <_>
                  10 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.1540730085689574e-004</threshold>
            <left_val>0.0816880166530609</left_val>
            <right_val>-0.2868503034114838</right_val></_></_>
        <_>
          <!-- tree 64 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 4 2 14 -1.</_>
                <_>
                  13 4 1 7 2.</_>
                <_>
                  14 11 1 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0101630901917815</threshold>
            <left_val>-0.0412500202655792</left_val>
            <right_val>0.4803834855556488</right_val></_></_>
        <_>
          <!-- tree 65 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 2 6 4 -1.</_>
                <_>
                  7 2 3 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.2184870950877666e-003</threshold>
            <left_val>0.1745858043432236</left_val>
            <right_val>-0.1014650017023087</right_val></_></_>
        <_>
          <!-- tree 66 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 18 20 -1.</_>
                <_>
                  0 0 9 10 2.</_>
                <_>
                  9 10 9 10 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.2426317036151886</threshold>
            <left_val>0.0534264817833900</left_val>
            <right_val>-0.3231852948665619</right_val></_></_>
        <_>
          <!-- tree 67 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 11 1 2 -1.</_>
                <_>
                  15 12 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.9304101634770632e-004</threshold>
            <left_val>-0.1149917989969254</left_val>
            <right_val>0.1479393988847733</right_val></_></_>
        <_>
          <!-- tree 68 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 10 2 4 -1.</_>
                <_>
                  16 10 1 2 2.</_>
                <_>
                  17 12 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.5475199110805988e-003</threshold>
            <left_val>-0.0394249781966209</left_val>
            <right_val>0.5312618017196655</right_val></_></_>
        <_>
          <!-- tree 69 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 17 2 2 -1.</_>
                <_>
                  18 17 1 1 2.</_>
                <_>
                  19 18 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.1403690334409475e-004</threshold>
            <left_val>0.0697538331151009</left_val>
            <right_val>-0.2731958031654358</right_val></_></_>
        <_>
          <!-- tree 70 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 17 1 2 -1.</_>
                <_>
                  9 18 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.7119462871924043e-004</threshold>
            <left_val>0.3436990082263947</left_val>
            <right_val>-0.0576990097761154</right_val></_></_>
        <_>
          <!-- tree 71 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 4 9 6 -1.</_>
                <_>
                  11 4 3 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.6290069371461868e-003</threshold>
            <left_val>0.1175848990678787</left_val>
            <right_val>-0.1502013951539993</right_val></_></_></trees>
      <stage_threshold>-1.1188739538192749</stage_threshold>
      <parent>16</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 18 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 9 9 10 -1.</_>
                <_>
                  9 9 3 10 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0265134498476982</threshold>
            <left_val>0.2056864053010941</left_val>
            <right_val>-0.2647390067577362</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 0 5 4 -1.</_>
                <_>
                  5 2 5 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.7727458924055099e-003</threshold>
            <left_val>-0.1119284033775330</left_val>
            <right_val>0.3257054984569550</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 7 11 4 -1.</_>
                <_>
                  5 9 11 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0322903506457806</threshold>
            <left_val>-0.0985747575759888</left_val>
            <right_val>0.3177917003631592</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 4 2 14 -1.</_>
                <_>
                  3 4 1 14 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.8103240765631199e-003</threshold>
            <left_val>0.1521389931440353</left_val>
            <right_val>-0.1968640983104706</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 6 3 5 -1.</_>
                <_>
                  9 6 1 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0109914299100637</threshold>
            <left_val>0.5140765905380249</left_val>
            <right_val>-0.0437072105705738</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 4 3 9 -1.</_>
                <_>
                  9 4 1 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.3133831135928631e-003</threshold>
            <left_val>-0.0927810221910477</left_val>
            <right_val>0.3470247089862824</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 8 20 6 -1.</_>
                <_>
                  0 10 20 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0871059820055962</threshold>
            <left_val>0.0300536490976810</left_val>
            <right_val>-0.8281481862068176</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 16 6 1 -1.</_>
                <_>
                  17 16 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.1799359926953912e-003</threshold>
            <left_val>-0.1292842030525208</left_val>
            <right_val>0.2064612060785294</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 18 2 2 -1.</_>
                <_>
                  17 19 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.3056890182197094e-004</threshold>
            <left_val>-0.5002143979072571</left_val>
            <right_val>0.0936669930815697</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 17 6 3 -1.</_>
                <_>
                  10 17 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0136871701106429</threshold>
            <left_val>-0.7935814857482910</left_val>
            <right_val>-6.6733639687299728e-003</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 1 9 15 -1.</_>
                <_>
                  7 1 3 15 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0759174525737762</threshold>
            <left_val>0.3046964108943939</left_val>
            <right_val>-0.0796558931469917</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 5 3 12 -1.</_>
                <_>
                  12 5 1 12 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.8559709899127483e-003</threshold>
            <left_val>0.2096146047115326</left_val>
            <right_val>-0.1273255050182343</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 15 4 3 -1.</_>
                <_>
                  0 16 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.0231510065495968e-003</threshold>
            <left_val>-0.6581727862358093</left_val>
            <right_val>0.0506836399435997</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 15 1 -1.</_>
                <_>
                  5 0 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0175580400973558</threshold>
            <left_val>-0.0853826925158501</left_val>
            <right_val>0.3617455959320068</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 0 6 4 -1.</_>
                <_>
                  8 0 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0219882391393185</threshold>
            <left_val>0.0629436969757080</left_val>
            <right_val>-0.7089633941650391</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 0 9 3 -1.</_>
                <_>
                  5 0 3 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.8599589131772518e-003</threshold>
            <left_val>0.1468378007411957</left_val>
            <right_val>-0.1646597981452942</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 6 3 7 -1.</_>
                <_>
                  14 6 1 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0100308498367667</threshold>
            <left_val>0.4957993924617767</left_val>
            <right_val>-0.0271883402019739</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 6 4 2 -1.</_>
                <_>
                  7 7 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.9560329429805279e-003</threshold>
            <left_val>0.2797777950763702</left_val>
            <right_val>-0.0779533311724663</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 18 6 1 -1.</_>
                <_>
                  8 18 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.8356808945536613e-003</threshold>
            <left_val>-0.5816398262977600</left_val>
            <right_val>0.0357399396598339</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 6 2 2 -1.</_>
                <_>
                  18 7 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.2647319603711367e-003</threshold>
            <left_val>-0.4994508028030396</left_val>
            <right_val>0.0469864904880524</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 4 7 3 -1.</_>
                <_>
                  6 5 7 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.8412350267171860e-003</threshold>
            <left_val>0.3453283011913300</left_val>
            <right_val>-0.0688104033470154</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 7 3 1 -1.</_>
                <_>
                  13 7 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.1718113506212831e-005</threshold>
            <left_val>0.1504171043634415</left_val>
            <right_val>-0.1414667963981628</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 1 2 10 -1.</_>
                <_>
                  15 1 1 5 2.</_>
                <_>
                  16 6 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.2448628917336464e-003</threshold>
            <left_val>0.2272451072931290</left_val>
            <right_val>-0.0928602069616318</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 18 2 2 -1.</_>
                <_>
                  0 19 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.8561151167377830e-004</threshold>
            <left_val>-0.4431901872158051</left_val>
            <right_val>0.0578124411404133</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 4 1 8 -1.</_>
                <_>
                  19 8 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.2474247533828020e-004</threshold>
            <left_val>0.1395238935947418</left_val>
            <right_val>-0.1466871947050095</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 17 1 3 -1.</_>
                <_>
                  1 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.2942948746494949e-004</threshold>
            <left_val>-0.2990157008171082</left_val>
            <right_val>0.0760667398571968</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 15 6 4 -1.</_>
                <_>
                  0 15 3 2 2.</_>
                <_>
                  3 17 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.2605739757418633e-003</threshold>
            <left_val>-0.1612560003995895</left_val>
            <right_val>0.1395380049943924</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 0 1 18 -1.</_>
                <_>
                  19 6 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0516670197248459</threshold>
            <left_val>-0.5314283967018127</left_val>
            <right_val>0.0407195203006268</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 2 6 2 -1.</_>
                <_>
                  12 2 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0152856195345521</threshold>
            <left_val>-0.7820637822151184</left_val>
            <right_val>0.0271837692707777</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 8 12 2 -1.</_>
                <_>
                  6 8 4 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0690298229455948</threshold>
            <left_val>-0.0364270210266113</left_val>
            <right_val>0.7110251784324646</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 0 4 1 -1.</_>
                <_>
                  18 0 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.4522749697789550e-003</threshold>
            <left_val>-0.0968905165791512</left_val>
            <right_val>0.2166842073202133</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 4 2 6 -1.</_>
                <_>
                  8 7 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.4765590205788612e-003</threshold>
            <left_val>0.1164531037211418</left_val>
            <right_val>-0.1822797954082489</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 5 2 10 -1.</_>
                <_>
                  15 5 1 10 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5134819550439715e-003</threshold>
            <left_val>0.1786397993564606</left_val>
            <right_val>-0.1221496984362602</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 4 2 2 -1.</_>
                <_>
                  13 5 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5099470037966967e-003</threshold>
            <left_val>0.1808623969554901</left_val>
            <right_val>-0.1144606992602348</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 1 3 6 -1.</_>
                <_>
                  11 3 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.7054620012640953e-003</threshold>
            <left_val>0.2510659992694855</left_val>
            <right_val>-0.0918714627623558</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 9 12 2 -1.</_>
                <_>
                  10 9 4 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0140752000734210</threshold>
            <left_val>0.1370750963687897</left_val>
            <right_val>-0.1733350008726120</right_val></_></_>
        <_>
          <!-- tree 36 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 16 4 2 -1.</_>
                <_>
                  9 17 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2400720044970512e-003</threshold>
            <left_val>0.4009298086166382</left_val>
            <right_val>-0.0475768782198429</right_val></_></_>
        <_>
          <!-- tree 37 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 14 15 4 -1.</_>
                <_>
                  5 16 15 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0197823699563742</threshold>
            <left_val>-0.1904035061597824</left_val>
            <right_val>0.1492341011762619</right_val></_></_>
        <_>
          <!-- tree 38 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 16 2 2 -1.</_>
                <_>
                  18 17 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.6002870872616768e-003</threshold>
            <left_val>0.0469717681407928</left_val>
            <right_val>-0.4330765902996063</right_val></_></_>
        <_>
          <!-- tree 39 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 18 2 2 -1.</_>
                <_>
                  16 18 1 1 2.</_>
                <_>
                  17 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.3445628145709634e-004</threshold>
            <left_val>-0.4374423027038574</left_val>
            <right_val>0.0415201894938946</right_val></_></_>
        <_>
          <!-- tree 40 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 4 3 8 -1.</_>
                <_>
                  7 4 1 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0174665097147226</threshold>
            <left_val>0.6581817269325256</left_val>
            <right_val>-0.0344474911689758</right_val></_></_>
        <_>
          <!-- tree 41 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 9 3 1 -1.</_>
                <_>
                  6 9 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.0425589755177498e-003</threshold>
            <left_val>0.3965792953968048</left_val>
            <right_val>-0.0440524294972420</right_val></_></_>
        <_>
          <!-- tree 42 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 8 1 6 -1.</_>
                <_>
                  0 10 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.6661779265850782e-003</threshold>
            <left_val>0.0587709583342075</left_val>
            <right_val>-0.3280636966228485</right_val></_></_>
        <_>
          <!-- tree 43 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 2 9 6 -1.</_>
                <_>
                  14 2 3 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0559823699295521</threshold>
            <left_val>-0.5173547267913818</left_val>
            <right_val>0.0357918404042721</right_val></_></_>
        <_>
          <!-- tree 44 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 2 6 4 -1.</_>
                <_>
                  14 2 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5066330088302493e-003</threshold>
            <left_val>0.1512386947870255</left_val>
            <right_val>-0.1252018064260483</right_val></_></_>
        <_>
          <!-- tree 45 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 7 2 4 -1.</_>
                <_>
                  1 9 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0114723695442081</threshold>
            <left_val>-0.6293053030967712</left_val>
            <right_val>0.0347043313086033</right_val></_></_>
        <_>
          <!-- tree 46 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 1 6 4 -1.</_>
                <_>
                  13 3 6 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0234096292406321</threshold>
            <left_val>-0.0580633506178856</left_val>
            <right_val>0.3866822123527527</right_val></_></_>
        <_>
          <!-- tree 47 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 10 2 10 -1.</_>
                <_>
                  4 10 1 5 2.</_>
                <_>
                  5 15 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.3243729956448078e-003</threshold>
            <left_val>0.1875409930944443</left_val>
            <right_val>-0.0983946695923805</right_val></_></_>
        <_>
          <!-- tree 48 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 16 9 3 -1.</_>
                <_>
                  5 16 3 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0290392991155386</threshold>
            <left_val>-0.5448690056800842</left_val>
            <right_val>0.0409263409674168</right_val></_></_>
        <_>
          <!-- tree 49 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 2 3 9 -1.</_>
                <_>
                  2 2 1 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0144746499136090</threshold>
            <left_val>-0.6724839210510254</left_val>
            <right_val>0.0231288503855467</right_val></_></_>
        <_>
          <!-- tree 50 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 7 1 4 -1.</_>
                <_>
                  19 9 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.2086091600358486e-003</threshold>
            <left_val>-0.4327144026756287</left_val>
            <right_val>0.0437806509435177</right_val></_></_>
        <_>
          <!-- tree 51 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 11 6 8 -1.</_>
                <_>
                  14 11 3 4 2.</_>
                <_>
                  17 15 3 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.9382899887859821e-003</threshold>
            <left_val>-0.1087862029671669</left_val>
            <right_val>0.1934258937835693</right_val></_></_>
        <_>
          <!-- tree 52 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 12 4 6 -1.</_>
                <_>
                  15 12 2 3 2.</_>
                <_>
                  17 15 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.3193930760025978e-003</threshold>
            <left_val>0.2408093065023422</left_val>
            <right_val>-0.1038080006837845</right_val></_></_>
        <_>
          <!-- tree 53 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 15 2 2 -1.</_>
                <_>
                  16 15 1 1 2.</_>
                <_>
                  17 16 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.3705669445917010e-004</threshold>
            <left_val>-0.0873490720987320</left_val>
            <right_val>0.2046623975038528</right_val></_></_>
        <_>
          <!-- tree 54 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 16 2 2 -1.</_>
                <_>
                  17 16 1 1 2.</_>
                <_>
                  18 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.7858079778961837e-004</threshold>
            <left_val>0.0456245802342892</left_val>
            <right_val>-0.3885467052459717</right_val></_></_>
        <_>
          <!-- tree 55 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 16 2 2 -1.</_>
                <_>
                  17 16 1 1 2.</_>
                <_>
                  18 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.5342838428914547e-004</threshold>
            <left_val>-0.5507794022560120</left_val>
            <right_val>0.0358258895576000</right_val></_></_>
        <_>
          <!-- tree 56 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 3 2 2 -1.</_>
                <_>
                  2 3 1 1 2.</_>
                <_>
                  3 4 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.4772121075075120e-005</threshold>
            <left_val>-0.1122523993253708</left_val>
            <right_val>0.1750351935625076</right_val></_></_>
        <_>
          <!-- tree 57 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 10 3 3 -1.</_>
                <_>
                  11 10 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.8445889949798584e-003</threshold>
            <left_val>0.2452670037746429</left_val>
            <right_val>-0.0811325684189796</right_val></_></_>
        <_>
          <!-- tree 58 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 9 7 8 -1.</_>
                <_>
                  5 13 7 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0401284582912922</threshold>
            <left_val>-0.6312270760536194</left_val>
            <right_val>0.0269726701080799</right_val></_></_>
        <_>
          <!-- tree 59 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 16 2 2 -1.</_>
                <_>
                  7 16 1 1 2.</_>
                <_>
                  8 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7886360001284629e-004</threshold>
            <left_val>0.1985509991645813</left_val>
            <right_val>-0.1033368036150932</right_val></_></_>
        <_>
          <!-- tree 60 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 16 2 2 -1.</_>
                <_>
                  7 16 1 1 2.</_>
                <_>
                  8 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.7668239888735116e-004</threshold>
            <left_val>-0.0913590118288994</left_val>
            <right_val>0.1984872072935104</right_val></_></_>
        <_>
          <!-- tree 61 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 8 10 3 -1.</_>
                <_>
                  14 8 5 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0727633833885193</threshold>
            <left_val>0.0500755794346333</left_val>
            <right_val>-0.3385263085365295</right_val></_></_>
        <_>
          <!-- tree 62 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 7 4 8 -1.</_>
                <_>
                  6 7 2 4 2.</_>
                <_>
                  8 11 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0101816300302744</threshold>
            <left_val>-0.0932299792766571</left_val>
            <right_val>0.2005959004163742</right_val></_></_>
        <_>
          <!-- tree 63 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 6 4 3 -1.</_>
                <_>
                  1 7 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.4409969337284565e-003</threshold>
            <left_val>0.0646366328001022</left_val>
            <right_val>-0.2692174017429352</right_val></_></_>
        <_>
          <!-- tree 64 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 10 6 10 -1.</_>
                <_>
                  8 10 2 10 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.6227488890290260e-003</threshold>
            <left_val>0.1316989064216614</left_val>
            <right_val>-0.1251484006643295</right_val></_></_>
        <_>
          <!-- tree 65 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 6 3 6 -1.</_>
                <_>
                  5 6 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3635610230267048e-003</threshold>
            <left_val>0.1635046005249023</left_val>
            <right_val>-0.1066593974828720</right_val></_></_></trees>
      <stage_threshold>-1.0888810157775879</stage_threshold>
      <parent>17</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 19 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 10 4 4 -1.</_>
                <_>
                  3 10 2 2 2.</_>
                <_>
                  5 12 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.6991164609789848e-003</threshold>
            <left_val>0.6112532019615173</left_val>
            <right_val>-0.0662253126502037</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 10 4 4 -1.</_>
                <_>
                  3 10 2 2 2.</_>
                <_>
                  5 12 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.6426531672477722e-003</threshold>
            <left_val>-1.</left_val>
            <right_val>2.7699959464371204e-003</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 10 4 4 -1.</_>
                <_>
                  3 10 2 2 2.</_>
                <_>
                  5 12 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.6381865441799164e-003</threshold>
            <left_val>1.</left_val>
            <right_val>-2.9904270195402205e-004</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 8 2 6 -1.</_>
                <_>
                  15 8 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.2553939856588840e-003</threshold>
            <left_val>0.2846438884735107</left_val>
            <right_val>-0.1554012000560761</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 10 4 4 -1.</_>
                <_>
                  3 10 2 2 2.</_>
                <_>
                  5 12 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.6223521977663040e-003</threshold>
            <left_val>-1.</left_val>
            <right_val>0.0439991801977158</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 10 4 4 -1.</_>
                <_>
                  3 10 2 2 2.</_>
                <_>
                  5 12 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.1231241822242737e-003</threshold>
            <left_val>0.8686934113502502</left_val>
            <right_val>-2.7267890982329845e-003</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 4 3 9 -1.</_>
                <_>
                  13 4 1 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.6240433156490326e-003</threshold>
            <left_val>0.4535248875617981</left_val>
            <right_val>-0.0860713794827461</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 3 1 12 -1.</_>
                <_>
                  12 7 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.9324144646525383e-003</threshold>
            <left_val>0.1337555944919586</left_val>
            <right_val>-0.2601251900196075</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 0 18 1 -1.</_>
                <_>
                  8 0 6 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0142078101634979</threshold>
            <left_val>0.3207764029502869</left_val>
            <right_val>-0.0972264111042023</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 0 10 6 -1.</_>
                <_>
                  10 0 5 3 2.</_>
                <_>
                  15 3 5 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0259110108017921</threshold>
            <left_val>-0.1296408027410507</left_val>
            <right_val>0.2621864974498749</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 16 2 2 -1.</_>
                <_>
                  18 17 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.0531509653665125e-004</threshold>
            <left_val>-0.1240428015589714</left_val>
            <right_val>0.2106295973062515</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 5 4 2 -1.</_>
                <_>
                  3 5 2 1 2.</_>
                <_>
                  5 6 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.4795680625829846e-005</threshold>
            <left_val>0.1197429969906807</left_val>
            <right_val>-0.2320127934217453</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 8 3 3 -1.</_>
                <_>
                  12 8 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.8555199541151524e-003</threshold>
            <left_val>-0.0632761269807816</left_val>
            <right_val>0.4104425013065338</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 7 3 5 -1.</_>
                <_>
                  12 7 1 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0122530404478312</threshold>
            <left_val>0.5488333106040955</left_val>
            <right_val>-0.0397311002016068</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 19 15 1 -1.</_>
                <_>
                  8 19 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.9058770053088665e-003</threshold>
            <left_val>0.2419098019599915</left_val>
            <right_val>-0.0970960110425949</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 13 3 2 -1.</_>
                <_>
                  8 14 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.7560980524867773e-003</threshold>
            <left_val>-0.1256967931985855</left_val>
            <right_val>0.1945665031671524</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 12 8 4 -1.</_>
                <_>
                  2 12 4 2 2.</_>
                <_>
                  6 14 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.7662160620093346e-003</threshold>
            <left_val>0.2976570129394531</left_val>
            <right_val>-0.0968181565403938</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 16 2 2 -1.</_>
                <_>
                  16 16 1 1 2.</_>
                <_>
                  17 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.8997188676148653e-004</threshold>
            <left_val>0.0621884018182755</left_val>
            <right_val>-0.4204089939594269</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 0 3 2 -1.</_>
                <_>
                  8 0 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.3579880837351084e-003</threshold>
            <left_val>0.0474981404840946</left_val>
            <right_val>-0.6321688294410706</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 7 2 5 -1.</_>
                <_>
                  7 7 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0167455393821001</threshold>
            <left_val>0.7109813094139099</left_val>
            <right_val>-0.0391573496162891</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 0 2 17 -1.</_>
                <_>
                  19 0 1 17 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.5409899689257145e-003</threshold>
            <left_val>-0.3504317104816437</left_val>
            <right_val>0.0706169530749321</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 16 1 3 -1.</_>
                <_>
                  16 17 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.0016340315341949e-004</threshold>
            <left_val>0.0919024571776390</left_val>
            <right_val>-0.2461867034435272</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 8 3 7 -1.</_>
                <_>
                  15 8 1 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0149189904332161</threshold>
            <left_val>-0.0519094504415989</left_val>
            <right_val>0.5663604140281677</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 17 2 2 -1.</_>
                <_>
                  10 17 1 1 2.</_>
                <_>
                  11 18 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.8153079114854336e-004</threshold>
            <left_val>0.0646595582365990</left_val>
            <right_val>-0.3659060895442963</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 9 1 3 -1.</_>
                <_>
                  4 10 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.0211321427486837e-004</threshold>
            <left_val>0.1792656928300858</left_val>
            <right_val>-0.1141066029667854</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 10 2 3 -1.</_>
                <_>
                  18 11 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.8521419628523290e-004</threshold>
            <left_val>0.1034561991691589</left_val>
            <right_val>-0.2007246017456055</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 1 3 10 -1.</_>
                <_>
                  13 1 1 10 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.0837132409214973e-003</threshold>
            <left_val>-0.0660734623670578</left_val>
            <right_val>0.3028424978256226</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 12 9 1 -1.</_>
                <_>
                  11 12 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0228049699217081</threshold>
            <left_val>0.5296235084533691</left_val>
            <right_val>-0.0401189997792244</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 18 2 2 -1.</_>
                <_>
                  5 18 1 1 2.</_>
                <_>
                  6 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.9440450705587864e-004</threshold>
            <left_val>0.0818548202514648</left_val>
            <right_val>-0.2466336041688919</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 6 1 9 -1.</_>
                <_>
                  19 9 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0128480903804302</threshold>
            <left_val>-0.3497331142425537</left_val>
            <right_val>0.0569162294268608</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 7 2 4 -1.</_>
                <_>
                  4 7 1 2 2.</_>
                <_>
                  5 9 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0937290498986840e-003</threshold>
            <left_val>0.2336868047714233</left_val>
            <right_val>-0.0916048064827919</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 4 6 14 -1.</_>
                <_>
                  3 4 2 14 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.0032650316134095e-003</threshold>
            <left_val>0.1185218021273613</left_val>
            <right_val>-0.1846919059753418</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 5 9 3 -1.</_>
                <_>
                  13 5 3 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0446884296834469</threshold>
            <left_val>-0.6436246037483215</left_val>
            <right_val>0.0303632691502571</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 7 2 6 -1.</_>
                <_>
                  18 9 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.1657543778419495e-003</threshold>
            <left_val>0.0436746589839458</left_val>
            <right_val>-0.4300208985805512</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 6 2 7 -1.</_>
                <_>
                  6 6 1 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0117178102955222</threshold>
            <left_val>0.4178147912025452</left_val>
            <right_val>-0.0482336990535259</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 4 6 8 -1.</_>
                <_>
                  13 4 3 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0842771306633949</threshold>
            <left_val>0.0534612797200680</left_val>
            <right_val>-0.3795219063758850</right_val></_></_>
        <_>
          <!-- tree 36 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 8 2 9 -1.</_>
                <_>
                  0 11 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0142118399962783</threshold>
            <left_val>0.0449009388685226</left_val>
            <right_val>-0.4298149943351746</right_val></_></_>
        <_>
          <!-- tree 37 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 7 5 3 -1.</_>
                <_>
                  0 8 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.5028340276330709e-003</threshold>
            <left_val>0.0822276398539543</left_val>
            <right_val>-0.2470639944076538</right_val></_></_>
        <_>
          <!-- tree 38 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 1 7 2 -1.</_>
                <_>
                  8 2 7 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0100035797804594</threshold>
            <left_val>-0.0572216697037220</left_val>
            <right_val>0.3460937142372131</right_val></_></_>
        <_>
          <!-- tree 39 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 5 3 5 -1.</_>
                <_>
                  8 5 1 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.0706320479512215e-003</threshold>
            <left_val>0.4505808949470520</left_val>
            <right_val>-0.0427953191101551</right_val></_></_>
        <_>
          <!-- tree 40 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 2 1 2 -1.</_>
                <_>
                  19 3 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.3141620224341750e-004</threshold>
            <left_val>0.1833691000938416</left_val>
            <right_val>-0.1075994968414307</right_val></_></_>
        <_>
          <!-- tree 41 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 7 10 11 -1.</_>
                <_>
                  11 7 5 11 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.1972327977418900</threshold>
            <left_val>-0.0303638298064470</left_val>
            <right_val>0.6642342805862427</right_val></_></_>
        <_>
          <!-- tree 42 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 19 6 1 -1.</_>
                <_>
                  11 19 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.1258801035583019e-003</threshold>
            <left_val>-0.8922504782676697</left_val>
            <right_val>0.0256699901074171</right_val></_></_>
        <_>
          <!-- tree 43 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 0 12 1 -1.</_>
                <_>
                  7 0 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.6921341717243195e-003</threshold>
            <left_val>-0.0707643702626228</left_val>
            <right_val>0.2821052968502045</right_val></_></_>
        <_>
          <!-- tree 44 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 1 6 5 -1.</_>
                <_>
                  6 1 2 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.9262127876281738e-003</threshold>
            <left_val>0.0710782334208488</left_val>
            <right_val>-0.3023256063461304</right_val></_></_>
        <_>
          <!-- tree 45 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 12 12 6 -1.</_>
                <_>
                  10 12 4 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0572860091924667</threshold>
            <left_val>0.0509741306304932</left_val>
            <right_val>-0.3919695019721985</right_val></_></_>
        <_>
          <!-- tree 46 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 13 2 3 -1.</_>
                <_>
                  16 14 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.7920880131423473e-003</threshold>
            <left_val>0.0338419415056705</left_val>
            <right_val>-0.5101628899574280</right_val></_></_>
        <_>
          <!-- tree 47 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 14 4 2 -1.</_>
                <_>
                  7 15 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.4508679741993546e-003</threshold>
            <left_val>0.3087914884090424</left_val>
            <right_val>-0.0638450831174850</right_val></_></_>
        <_>
          <!-- tree 48 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 14 2 2 -1.</_>
                <_>
                  7 15 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.8390132188796997e-004</threshold>
            <left_val>-0.1302956938743591</left_val>
            <right_val>0.1460441052913666</right_val></_></_>
        <_>
          <!-- tree 49 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 10 2 4 -1.</_>
                <_>
                  3 10 1 2 2.</_>
                <_>
                  4 12 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7221809830516577e-003</threshold>
            <left_val>0.2915700972080231</left_val>
            <right_val>-0.0685495585203171</right_val></_></_>
        <_>
          <!-- tree 50 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 3 2 6 -1.</_>
                <_>
                  0 5 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0109482500702143</threshold>
            <left_val>0.0343514084815979</left_val>
            <right_val>-0.4770225882530212</right_val></_></_>
        <_>
          <!-- tree 51 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 10 2 2 -1.</_>
                <_>
                  1 10 1 1 2.</_>
                <_>
                  2 11 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7176309484057128e-005</threshold>
            <left_val>0.1605526953935623</left_val>
            <right_val>-0.1169084012508392</right_val></_></_>
        <_>
          <!-- tree 52 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 4 4 3 -1.</_>
                <_>
                  16 5 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.4884208366274834e-003</threshold>
            <left_val>-0.4341588914394379</left_val>
            <right_val>0.0461062416434288</right_val></_></_>
        <_>
          <!-- tree 53 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 10 2 4 -1.</_>
                <_>
                  5 10 1 2 2.</_>
                <_>
                  6 12 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.0975250992923975e-003</threshold>
            <left_val>0.3794333934783936</left_val>
            <right_val>-0.0568605512380600</right_val></_></_>
        <_>
          <!-- tree 54 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 11 13 2 -1.</_>
                <_>
                  5 12 13 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.4182081259787083e-003</threshold>
            <left_val>-0.1585821062326431</left_val>
            <right_val>0.1233541965484619</right_val></_></_>
        <_>
          <!-- tree 55 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 2 3 11 -1.</_>
                <_>
                  11 2 1 11 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0118312397971749</threshold>
            <left_val>-0.0409292913973331</left_val>
            <right_val>0.4587895870208740</right_val></_></_>
        <_>
          <!-- tree 56 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 2 4 4 -1.</_>
                <_>
                  10 4 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0135404998436570</threshold>
            <left_val>-0.0537255592644215</left_val>
            <right_val>0.3505612015724182</right_val></_></_>
        <_>
          <!-- tree 57 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 8 6 2 -1.</_>
                <_>
                  10 8 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.5932150892913342e-003</threshold>
            <left_val>0.1101052016019821</left_val>
            <right_val>-0.1675221025943756</right_val></_></_>
        <_>
          <!-- tree 58 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 2 3 3 -1.</_>
                <_>
                  12 2 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.6856270376592875e-003</threshold>
            <left_val>0.0665743574500084</left_val>
            <right_val>-0.3083502054214478</right_val></_></_>
        <_>
          <!-- tree 59 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 18 14 2 -1.</_>
                <_>
                  6 18 7 1 2.</_>
                <_>
                  13 19 7 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.6524690911173820e-003</threshold>
            <left_val>0.0663184821605682</left_val>
            <right_val>-0.2786133885383606</right_val></_></_>
        <_>
          <!-- tree 60 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 7 1 12 -1.</_>
                <_>
                  17 11 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.7341729775071144e-003</threshold>
            <left_val>0.1971835941076279</left_val>
            <right_val>-0.1078291982412338</right_val></_></_>
        <_>
          <!-- tree 61 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 5 10 3 -1.</_>
                <_>
                  10 6 10 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.0944271497428417e-003</threshold>
            <left_val>0.0853374898433685</left_val>
            <right_val>-0.2484700977802277</right_val></_></_>
        <_>
          <!-- tree 62 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 1 3 3 -1.</_>
                <_>
                  7 1 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.9162371065467596e-003</threshold>
            <left_val>-0.4747635126113892</left_val>
            <right_val>0.0335664898157120</right_val></_></_>
        <_>
          <!-- tree 63 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 8 3 1 -1.</_>
                <_>
                  14 8 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.0121419113129377e-003</threshold>
            <left_val>-0.0475753806531429</left_val>
            <right_val>0.4258680045604706</right_val></_></_>
        <_>
          <!-- tree 64 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 14 2 6 -1.</_>
                <_>
                  10 16 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.1694869976490736e-003</threshold>
            <left_val>-0.1051945015788078</left_val>
            <right_val>0.1716345995664597</right_val></_></_>
        <_>
          <!-- tree 65 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 1 12 14 -1.</_>
                <_>
                  8 1 4 14 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.2232756018638611</threshold>
            <left_val>-0.0143702095374465</left_val>
            <right_val>0.9248365163803101</right_val></_></_>
        <_>
          <!-- tree 66 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 1 6 14 -1.</_>
                <_>
                  16 1 2 14 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0955850481987000</threshold>
            <left_val>-0.7420663833618164</left_val>
            <right_val>0.0278189703822136</right_val></_></_>
        <_>
          <!-- tree 67 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 16 2 2 -1.</_>
                <_>
                  3 16 1 1 2.</_>
                <_>
                  4 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.4773729566950351e-005</threshold>
            <left_val>-0.1276578009128571</left_val>
            <right_val>0.1292666941881180</right_val></_></_>
        <_>
          <!-- tree 68 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 16 2 2 -1.</_>
                <_>
                  0 17 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.2459770308341831e-005</threshold>
            <left_val>-0.1651857942342758</left_val>
            <right_val>0.1003680974245071</right_val></_></_></trees>
      <stage_threshold>-1.0408929586410522</stage_threshold>
      <parent>18</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 20 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 6 4 6 -1.</_>
                <_>
                  15 6 2 3 2.</_>
                <_>
                  17 9 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.5778270363807678e-003</threshold>
            <left_val>0.3381525874137878</left_val>
            <right_val>-0.1528190970420837</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 5 2 2 -1.</_>
                <_>
                  12 6 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0922809597104788e-003</threshold>
            <left_val>0.2228236943483353</left_val>
            <right_val>-0.1930849999189377</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 6 6 13 -1.</_>
                <_>
                  9 6 2 13 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0297595895826817</threshold>
            <left_val>0.2595987021923065</left_val>
            <right_val>-0.1540940999984741</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 9 6 5 -1.</_>
                <_>
                  3 9 2 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0131475403904915</threshold>
            <left_val>0.1903381049633026</left_val>
            <right_val>-0.1654399931430817</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 5 3 4 -1.</_>
                <_>
                  0 7 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.4396329643204808e-003</threshold>
            <left_val>0.2007171064615250</left_val>
            <right_val>-0.1233894005417824</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 1 16 2 -1.</_>
                <_>
                  4 1 8 1 2.</_>
                <_>
                  12 2 8 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.5928250290453434e-003</threshold>
            <left_val>0.2398552000522614</left_val>
            <right_val>-0.1292214989662170</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 18 4 2 -1.</_>
                <_>
                  1 18 2 1 2.</_>
                <_>
                  3 19 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5314699849113822e-003</threshold>
            <left_val>-0.4901489913463593</left_val>
            <right_val>0.1027503013610840</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 7 3 4 -1.</_>
                <_>
                  8 7 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.2372139655053616e-003</threshold>
            <left_val>0.3121463954448700</left_val>
            <right_val>-0.1140562966465950</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 4 9 3 -1.</_>
                <_>
                  6 4 3 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0333646498620510</threshold>
            <left_val>-0.4952087998390198</left_val>
            <right_val>0.0513284504413605</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 6 6 10 -1.</_>
                <_>
                  6 6 2 10 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0228276997804642</threshold>
            <left_val>0.3255882859230042</left_val>
            <right_val>-0.0650893077254295</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 0 8 10 -1.</_>
                <_>
                  13 0 4 10 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0861990973353386</threshold>
            <left_val>-0.6764633059501648</left_val>
            <right_val>0.0269856993108988</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 0 8 1 -1.</_>
                <_>
                  12 0 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.1065981127321720e-003</threshold>
            <left_val>0.2245243042707443</left_val>
            <right_val>-0.1261022984981537</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 2 8 16 -1.</_>
                <_>
                  6 2 4 8 2.</_>
                <_>
                  10 10 4 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0391201488673687</threshold>
            <left_val>0.1132939979434013</left_val>
            <right_val>-0.2686063051223755</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 10 2 10 -1.</_>
                <_>
                  14 10 1 5 2.</_>
                <_>
                  15 15 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.5082739777863026e-003</threshold>
            <left_val>-0.1135995984077454</left_val>
            <right_val>0.2564977109432221</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 11 1 2 -1.</_>
                <_>
                  12 12 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.9289898490533233e-004</threshold>
            <left_val>-0.1494296938180924</left_val>
            <right_val>0.1640983968973160</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 0 3 8 -1.</_>
                <_>
                  17 0 1 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.1766850305721164e-004</threshold>
            <left_val>0.0999056920409203</left_val>
            <right_val>-0.2196796983480454</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 0 6 10 -1.</_>
                <_>
                  17 0 3 10 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0218036007136106</threshold>
            <left_val>-0.3171172142028809</left_val>
            <right_val>0.0828895866870880</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 0 3 5 -1.</_>
                <_>
                  17 0 1 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.2962779514491558e-003</threshold>
            <left_val>-0.3804872930049896</left_val>
            <right_val>0.0608193799853325</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 5 11 2 -1.</_>
                <_>
                  4 6 11 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.4196270387619734e-003</threshold>
            <left_val>-0.0960130169987679</left_val>
            <right_val>0.2854058146476746</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 2 1 -1.</_>
                <_>
                  2 0 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.4187481398694217e-004</threshold>
            <left_val>0.2212793976068497</left_val>
            <right_val>-0.0974349081516266</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 2 3 -1.</_>
                <_>
                  0 1 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.4523929934948683e-003</threshold>
            <left_val>0.0375531204044819</left_val>
            <right_val>-0.5796905159950256</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 6 6 11 -1.</_>
                <_>
                  13 6 2 11 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0218346007168293</threshold>
            <left_val>0.2956213951110840</left_val>
            <right_val>-0.0800483003258705</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 0 3 1 -1.</_>
                <_>
                  15 0 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.1309500152710825e-004</threshold>
            <left_val>0.2281450927257538</left_val>
            <right_val>-0.1011418998241425</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 7 1 2 -1.</_>
                <_>
                  19 8 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.6166249988600612e-003</threshold>
            <left_val>-0.5054119825363159</left_val>
            <right_val>0.0447645410895348</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 0 3 9 -1.</_>
                <_>
                  18 0 1 9 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.5959609821438789e-003</threshold>
            <left_val>0.0459865406155586</left_val>
            <right_val>-0.4119768142700195</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 7 3 4 -1.</_>
                <_>
                  13 7 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.8601809646934271e-003</threshold>
            <left_val>-0.0865631699562073</left_val>
            <right_val>0.2480999976396561</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 1 14 2 -1.</_>
                <_>
                  0 1 7 1 2.</_>
                <_>
                  7 2 7 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.0622231103479862e-003</threshold>
            <left_val>-0.0755573734641075</left_val>
            <right_val>0.2843326032161713</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 1 3 2 -1.</_>
                <_>
                  4 1 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7097420059144497e-003</threshold>
            <left_val>-0.3529582023620606</left_val>
            <right_val>0.0584104992449284</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 0 15 2 -1.</_>
                <_>
                  9 0 5 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0165155790746212</threshold>
            <left_val>-0.0804869532585144</left_val>
            <right_val>0.2353743016719818</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 2 6 1 -1.</_>
                <_>
                  12 2 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.8465100117027760e-003</threshold>
            <left_val>0.0418952181935310</left_val>
            <right_val>-0.4844304919242859</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 4 6 11 -1.</_>
                <_>
                  11 4 2 11 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0311671700328588</threshold>
            <left_val>0.1919230967760086</left_val>
            <right_val>-0.1026815995573998</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 16 2 4 -1.</_>
                <_>
                  2 18 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.1892281519249082e-004</threshold>
            <left_val>-0.2108577042818070</left_val>
            <right_val>0.0938869267702103</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 17 6 3 -1.</_>
                <_>
                  8 17 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0119463102892041</threshold>
            <left_val>0.0390961691737175</left_val>
            <right_val>-0.6224862933158875</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 9 6 2 -1.</_>
                <_>
                  9 9 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.5677200220525265e-003</threshold>
            <left_val>0.1593683958053589</left_val>
            <right_val>-0.1225078031420708</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 8 9 2 -1.</_>
                <_>
                  9 8 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0537474118173122</threshold>
            <left_val>-0.5562217831611633</left_val>
            <right_val>0.0411900095641613</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 6 2 10 -1.</_>
                <_>
                  6 6 1 5 2.</_>
                <_>
                  7 11 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0155135300010443</threshold>
            <left_val>-0.0398268811404705</left_val>
            <right_val>0.6240072846412659</right_val></_></_>
        <_>
          <!-- tree 36 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 11 2 3 -1.</_>
                <_>
                  0 12 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.5246650436893106e-003</threshold>
            <left_val>0.0701386779546738</left_val>
            <right_val>-0.3078907132148743</right_val></_></_>
        <_>
          <!-- tree 37 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 15 4 1 -1.</_>
                <_>
                  13 15 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.8315100139006972e-004</threshold>
            <left_val>0.1788765937089920</left_val>
            <right_val>-0.1095862016081810</right_val></_></_>
        <_>
          <!-- tree 38 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 17 1 2 -1.</_>
                <_>
                  6 18 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.7374739293009043e-003</threshold>
            <left_val>0.0274785906076431</left_val>
            <right_val>-0.8848956823348999</right_val></_></_>
        <_>
          <!-- tree 39 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 6 20 -1.</_>
                <_>
                  2 0 2 20 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0657877177000046</threshold>
            <left_val>-0.4643214046955109</left_val>
            <right_val>0.0350371487438679</right_val></_></_>
        <_>
          <!-- tree 40 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 10 2 2 -1.</_>
                <_>
                  4 10 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.2409730115905404e-003</threshold>
            <left_val>-0.0964792370796204</left_val>
            <right_val>0.2877922058105469</right_val></_></_>
        <_>
          <!-- tree 41 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 7 3 5 -1.</_>
                <_>
                  5 7 1 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.1398809561505914e-004</threshold>
            <left_val>0.1151171997189522</left_val>
            <right_val>-0.1676616072654724</right_val></_></_>
        <_>
          <!-- tree 42 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 12 6 2 -1.</_>
                <_>
                  5 12 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0239018201828003</threshold>
            <left_val>-0.0326031893491745</left_val>
            <right_val>0.6001734733581543</right_val></_></_>
        <_>
          <!-- tree 43 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 15 7 4 -1.</_>
                <_>
                  6 17 7 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0275566000491381</threshold>
            <left_val>-0.0661373436450958</left_val>
            <right_val>0.2999447882175446</right_val></_></_>
        <_>
          <!-- tree 44 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 16 2 2 -1.</_>
                <_>
                  17 16 1 1 2.</_>
                <_>
                  18 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.8070970913395286e-004</threshold>
            <left_val>-0.3388118147850037</left_val>
            <right_val>0.0644507706165314</right_val></_></_>
        <_>
          <!-- tree 45 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 1 3 16 -1.</_>
                <_>
                  16 1 1 16 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3335429830476642e-003</threshold>
            <left_val>0.1458866000175476</left_val>
            <right_val>-0.1321762055158615</right_val></_></_>
        <_>
          <!-- tree 46 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 16 6 3 -1.</_>
                <_>
                  8 16 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.3507990241050720e-003</threshold>
            <left_val>-0.5117782950401306</left_val>
            <right_val>0.0349694713950157</right_val></_></_>
        <_>
          <!-- tree 47 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 14 3 2 -1.</_>
                <_>
                  15 15 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.6215229928493500e-003</threshold>
            <left_val>0.0232495293021202</left_val>
            <right_val>-0.6961941123008728</right_val></_></_>
        <_>
          <!-- tree 48 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 16 1 2 -1.</_>
                <_>
                  12 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.3407860832521692e-005</threshold>
            <left_val>0.2372737973928452</left_val>
            <right_val>-0.0869107097387314</right_val></_></_>
        <_>
          <!-- tree 49 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 2 4 4 -1.</_>
                <_>
                  0 2 2 2 2.</_>
                <_>
                  2 4 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5332329785451293e-003</threshold>
            <left_val>0.1922841072082520</left_val>
            <right_val>-0.1042239964008331</right_val></_></_>
        <_>
          <!-- tree 50 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 1 6 4 -1.</_>
                <_>
                  1 1 3 2 2.</_>
                <_>
                  4 3 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.3135890737175941e-003</threshold>
            <left_val>-0.0962195470929146</left_val>
            <right_val>0.2560121119022369</right_val></_></_>
        <_>
          <!-- tree 51 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 18 1 2 -1.</_>
                <_>
                  1 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.3042880638968199e-004</threshold>
            <left_val>-0.3156475126743317</left_val>
            <right_val>0.0588385984301567</right_val></_></_>
        <_>
          <!-- tree 52 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 7 2 3 -1.</_>
                <_>
                  4 8 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.8411828726530075e-003</threshold>
            <left_val>-0.6634092926979065</left_val>
            <right_val>0.0245009995996952</right_val></_></_>
        <_>
          <!-- tree 53 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 9 14 -1.</_>
                <_>
                  1 7 9 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.1710374057292938</threshold>
            <left_val>0.0338314995169640</left_val>
            <right_val>-0.4561594128608704</right_val></_></_>
        <_>
          <!-- tree 54 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 9 2 6 -1.</_>
                <_>
                  4 9 1 3 2.</_>
                <_>
                  5 12 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.6011140542104840e-003</threshold>
            <left_val>0.2157489061355591</left_val>
            <right_val>-0.0836225301027298</right_val></_></_>
        <_>
          <!-- tree 55 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 9 4 3 -1.</_>
                <_>
                  5 9 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0105357803404331</threshold>
            <left_val>0.2455231994390488</left_val>
            <right_val>-0.0823844894766808</right_val></_></_>
        <_>
          <!-- tree 56 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 9 2 4 -1.</_>
                <_>
                  0 11 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.8351638726890087e-003</threshold>
            <left_val>-0.4780732989311218</left_val>
            <right_val>0.0440862216055393</right_val></_></_>
        <_>
          <!-- tree 57 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 6 3 10 -1.</_>
                <_>
                  17 6 1 10 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0187061093747616</threshold>
            <left_val>-0.6002402901649475</left_val>
            <right_val>0.0214100405573845</right_val></_></_>
        <_>
          <!-- tree 58 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 11 2 1 -1.</_>
                <_>
                  17 11 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.3307439237833023e-004</threshold>
            <left_val>0.2432359009981155</left_val>
            <right_val>-0.0741657167673111</right_val></_></_></trees>
      <stage_threshold>-1.0566600561141968</stage_threshold>
      <parent>19</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 21 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 7 4 4 -1.</_>
                <_>
                  5 9 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0106462296098471</threshold>
            <left_val>-0.1386138945817947</left_val>
            <right_val>0.2649407088756561</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 11 9 2 -1.</_>
                <_>
                  13 11 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0352982692420483</threshold>
            <left_val>-0.0758217275142670</left_val>
            <right_val>0.3902106881141663</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 10 2 2 -1.</_>
                <_>
                  15 10 1 1 2.</_>
                <_>
                  16 11 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.5638387352228165e-004</threshold>
            <left_val>-0.0955214425921440</left_val>
            <right_val>0.2906199991703033</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 6 6 14 -1.</_>
                <_>
                  10 13 6 7 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0924977064132690</threshold>
            <left_val>-0.2770423889160156</left_val>
            <right_val>0.0794747024774551</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 7 3 5 -1.</_>
                <_>
                  15 7 1 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.9340879991650581e-003</threshold>
            <left_val>0.2298953980207443</left_val>
            <right_val>-0.0785500109195709</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 11 12 3 -1.</_>
                <_>
                  10 11 4 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0865358486771584</threshold>
            <left_val>0.4774481058120728</left_val>
            <right_val>-6.8231220357120037e-003</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 16 1 2 -1.</_>
                <_>
                  17 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.4699288739357144e-005</threshold>
            <left_val>-0.2264260947704315</left_val>
            <right_val>0.0881921127438545</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 5 5 4 -1.</_>
                <_>
                  8 7 5 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0365925207734108</threshold>
            <left_val>0.2735387086868286</left_val>
            <right_val>-0.0986067429184914</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 6 4 2 -1.</_>
                <_>
                  11 7 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.6469118893146515e-003</threshold>
            <left_val>-0.0440839789807796</left_val>
            <right_val>0.3144528865814209</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 4 8 2 -1.</_>
                <_>
                  3 4 4 1 2.</_>
                <_>
                  7 5 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.4271810911595821e-003</threshold>
            <left_val>0.2382272928953171</left_val>
            <right_val>-0.0867842733860016</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 8 6 6 -1.</_>
                <_>
                  2 8 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.1882481202483177e-003</threshold>
            <left_val>0.1504276990890503</left_val>
            <right_val>-0.1267210990190506</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 4 6 2 -1.</_>
                <_>
                  7 5 6 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.5530400238931179e-003</threshold>
            <left_val>-0.0559450201690197</left_val>
            <right_val>0.3650163114070892</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 3 6 3 -1.</_>
                <_>
                  9 3 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0145624103024602</threshold>
            <left_val>0.0363977700471878</left_val>
            <right_val>-0.5355919003486633</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 17 3 3 -1.</_>
                <_>
                  2 18 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.8677567469421774e-005</threshold>
            <left_val>-0.1747962981462479</left_val>
            <right_val>0.1106870993971825</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 10 6 1 -1.</_>
                <_>
                  5 10 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.9744901955127716e-003</threshold>
            <left_val>0.3107787072658539</left_val>
            <right_val>-0.0665302276611328</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 2 6 2 -1.</_>
                <_>
                  9 2 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.8691250160336494e-003</threshold>
            <left_val>-0.3190149068832398</left_val>
            <right_val>0.0639318302273750</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 11 9 1 -1.</_>
                <_>
                  7 11 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0111403102055192</threshold>
            <left_val>0.2436479032039642</left_val>
            <right_val>-0.0809351801872253</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 7 11 12 -1.</_>
                <_>
                  7 13 11 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0586435310542583</threshold>
            <left_val>-0.7608326077461243</left_val>
            <right_val>0.0308096297085285</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 2 3 4 -1.</_>
                <_>
                  4 2 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.6097282320261002e-003</threshold>
            <left_val>-0.4531502127647400</left_val>
            <right_val>0.0298790596425533</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 7 9 3 -1.</_>
                <_>
                  12 7 3 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.3032103031873703e-003</threshold>
            <left_val>0.1451337933540344</left_val>
            <right_val>-0.1103316992521286</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 11 2 6 -1.</_>
                <_>
                  15 11 1 3 2.</_>
                <_>
                  16 14 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.3253629440441728e-003</threshold>
            <left_val>-0.0976989567279816</left_val>
            <right_val>0.1964644044637680</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 5 5 3 -1.</_>
                <_>
                  0 6 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.9800761044025421e-003</threshold>
            <left_val>0.0336480811238289</left_val>
            <right_val>-0.3979220986366272</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 1 6 12 -1.</_>
                <_>
                  10 1 2 12 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.6542161405086517e-003</threshold>
            <left_val>0.0908419936895370</left_val>
            <right_val>-0.1596754938364029</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 7 15 13 -1.</_>
                <_>
                  8 7 5 13 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.3892059028148651</threshold>
            <left_val>-0.6657109260559082</left_val>
            <right_val>0.0190288294106722</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 9 9 9 -1.</_>
                <_>
                  0 12 9 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1001966968178749</threshold>
            <left_val>-0.5755926966667175</left_val>
            <right_val>0.0242827795445919</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 0 3 8 -1.</_>
                <_>
                  17 0 1 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.3541211895644665e-004</threshold>
            <left_val>0.0879198014736176</left_val>
            <right_val>-0.1619534045457840</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 2 4 2 -1.</_>
                <_>
                  18 2 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.4802639856934547e-003</threshold>
            <left_val>0.2606449127197266</left_val>
            <right_val>-0.0602008104324341</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 0 6 5 -1.</_>
                <_>
                  16 0 3 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.4000425413250923e-003</threshold>
            <left_val>-0.1097972989082336</left_val>
            <right_val>0.1570730954408646</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 1 3 2 -1.</_>
                <_>
                  16 1 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.3786011151969433e-003</threshold>
            <left_val>0.0360582396388054</left_val>
            <right_val>-0.4727719128131867</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 8 3 2 -1.</_>
                <_>
                  12 8 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.3831682093441486e-003</threshold>
            <left_val>-0.0357563607394695</left_val>
            <right_val>0.4949859082698822</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 8 2 12 -1.</_>
                <_>
                  1 8 1 6 2.</_>
                <_>
                  2 14 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.2115620560944080e-003</threshold>
            <left_val>-0.1012556031346321</left_val>
            <right_val>0.1574798971414566</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 1 6 12 -1.</_>
                <_>
                  2 1 2 12 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0782096683979034</threshold>
            <left_val>-0.7662708163261414</left_val>
            <right_val>0.0229658298194408</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 17 1 3 -1.</_>
                <_>
                  19 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.3303989261621609e-005</threshold>
            <left_val>-0.1341435015201569</left_val>
            <right_val>0.1111491993069649</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 3 3 10 -1.</_>
                <_>
                  12 3 1 10 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.6419155597686768e-003</threshold>
            <left_val>0.2506802976131439</left_val>
            <right_val>-0.0666081383824348</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 1 9 8 -1.</_>
                <_>
                  11 1 3 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0710926726460457</threshold>
            <left_val>-0.4005681872367859</left_val>
            <right_val>0.0402977913618088</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 16 2 2 -1.</_>
                <_>
                  18 16 1 1 2.</_>
                <_>
                  19 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.5171560011804104e-004</threshold>
            <left_val>0.0418611802160740</left_val>
            <right_val>-0.3296119868755341</right_val></_></_>
        <_>
          <!-- tree 36 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 16 2 2 -1.</_>
                <_>
                  18 16 1 1 2.</_>
                <_>
                  19 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.3458150574006140e-004</threshold>
            <left_val>-0.2602983117103577</left_val>
            <right_val>0.0678927376866341</right_val></_></_>
        <_>
          <!-- tree 37 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 13 2 6 -1.</_>
                <_>
                  6 15 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.1451421566307545e-003</threshold>
            <left_val>0.2396769970655441</left_val>
            <right_val>-0.0720933377742767</right_val></_></_>
        <_>
          <!-- tree 38 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 14 2 2 -1.</_>
                <_>
                  9 15 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.1754500232636929e-003</threshold>
            <left_val>-0.0712352693080902</left_val>
            <right_val>0.2412845045328140</right_val></_></_>
        <_>
          <!-- tree 39 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 10 2 4 -1.</_>
                <_>
                  14 10 1 2 2.</_>
                <_>
                  15 12 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.5184490047395229e-003</threshold>
            <left_val>0.5032023787498474</left_val>
            <right_val>-0.0296866800636053</right_val></_></_>
        <_>
          <!-- tree 40 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 15 2 2 -1.</_>
                <_>
                  0 15 1 1 2.</_>
                <_>
                  1 16 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.0242869979701936e-004</threshold>
            <left_val>0.2487905025482178</left_val>
            <right_val>-0.0567585788667202</right_val></_></_>
        <_>
          <!-- tree 41 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 7 2 2 -1.</_>
                <_>
                  6 7 1 1 2.</_>
                <_>
                  7 8 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3125919504091144e-003</threshold>
            <left_val>0.3174780011177063</left_val>
            <right_val>-0.0418458618223667</right_val></_></_>
        <_>
          <!-- tree 42 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 18 2 2 -1.</_>
                <_>
                  11 18 1 1 2.</_>
                <_>
                  12 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.7123570907860994e-004</threshold>
            <left_val>-0.2704207003116608</left_val>
            <right_val>0.0568289905786514</right_val></_></_>
        <_>
          <!-- tree 43 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 6 4 -1.</_>
                <_>
                  0 0 3 2 2.</_>
                <_>
                  3 2 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.3241777718067169e-003</threshold>
            <left_val>0.2755667865276337</left_val>
            <right_val>-0.0542529709637165</right_val></_></_>
        <_>
          <!-- tree 44 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 1 6 6 -1.</_>
                <_>
                  6 1 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0168517101556063</threshold>
            <left_val>-0.3485291004180908</left_val>
            <right_val>0.0453689992427826</right_val></_></_>
        <_>
          <!-- tree 45 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 13 5 4 -1.</_>
                <_>
                  15 15 5 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0299021005630493</threshold>
            <left_val>0.0316210798919201</left_val>
            <right_val>-0.4311437010765076</right_val></_></_>
        <_>
          <!-- tree 46 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 17 6 1 -1.</_>
                <_>
                  9 17 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.8902660124003887e-003</threshold>
            <left_val>0.0380299612879753</left_val>
            <right_val>-0.3702709972858429</right_val></_></_>
        <_>
          <!-- tree 47 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 19 4 1 -1.</_>
                <_>
                  18 19 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.9242949783802032e-003</threshold>
            <left_val>0.2480027973651886</left_val>
            <right_val>-0.0593332983553410</right_val></_></_>
        <_>
          <!-- tree 48 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 16 4 4 -1.</_>
                <_>
                  18 16 2 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.9354149959981441e-003</threshold>
            <left_val>-0.0830684006214142</left_val>
            <right_val>0.2204380929470062</right_val></_></_>
        <_>
          <!-- tree 49 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 8 9 4 -1.</_>
                <_>
                  10 8 3 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0820756033062935</threshold>
            <left_val>-0.0194134395569563</left_val>
            <right_val>0.6908928751945496</right_val></_></_>
        <_>
          <!-- tree 50 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 18 2 2 -1.</_>
                <_>
                  16 18 1 1 2.</_>
                <_>
                  17 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.4699489586055279e-004</threshold>
            <left_val>-0.2466056942939758</left_val>
            <right_val>0.0647764503955841</right_val></_></_>
        <_>
          <!-- tree 51 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 9 2 4 -1.</_>
                <_>
                  2 9 1 2 2.</_>
                <_>
                  3 11 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.8365769647061825e-003</threshold>
            <left_val>0.2883616089820862</left_val>
            <right_val>-0.0533904582262039</right_val></_></_>
        <_>
          <!-- tree 52 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 3 8 4 -1.</_>
                <_>
                  0 3 4 2 2.</_>
                <_>
                  4 5 4 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.9553811550140381e-003</threshold>
            <left_val>0.1274082958698273</left_val>
            <right_val>-0.1255941987037659</right_val></_></_>
        <_>
          <!-- tree 53 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 1 8 1 -1.</_>
                <_>
                  4 1 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.3086621016263962e-003</threshold>
            <left_val>0.2347811013460159</left_val>
            <right_val>-0.0716764926910400</right_val></_></_>
        <_>
          <!-- tree 54 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 5 8 9 -1.</_>
                <_>
                  4 5 4 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1087991967797279</threshold>
            <left_val>-0.2599223852157593</left_val>
            <right_val>0.0586897395551205</right_val></_></_>
        <_>
          <!-- tree 55 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 18 6 2 -1.</_>
                <_>
                  9 18 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.6786450594663620e-003</threshold>
            <left_val>-0.7072042822837830</left_val>
            <right_val>0.0187492594122887</right_val></_></_>
        <_>
          <!-- tree 56 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 4 1 12 -1.</_>
                <_>
                  0 8 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0271368306130171</threshold>
            <left_val>-0.5838422775268555</left_val>
            <right_val>0.0216841306537390</right_val></_></_>
        <_>
          <!-- tree 57 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 13 1 6 -1.</_>
                <_>
                  19 15 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.5389778465032578e-003</threshold>
            <left_val>-0.5974891185760498</left_val>
            <right_val>0.0214803107082844</right_val></_></_>
        <_>
          <!-- tree 58 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 8 6 8 -1.</_>
                <_>
                  4 8 2 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0120956301689148</threshold>
            <left_val>0.1326903998851776</left_val>
            <right_val>-0.0997227206826210</right_val></_></_>
        <_>
          <!-- tree 59 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 9 17 -1.</_>
                <_>
                  3 0 3 17 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1677609980106354</threshold>
            <left_val>-0.5665506720542908</left_val>
            <right_val>0.0321230888366699</right_val></_></_>
        <_>
          <!-- tree 60 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 9 6 8 -1.</_>
                <_>
                  9 9 2 8 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0132625503465533</threshold>
            <left_val>0.1149559020996094</left_val>
            <right_val>-0.1173838973045349</right_val></_></_>
        <_>
          <!-- tree 61 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 10 9 4 -1.</_>
                <_>
                  8 10 3 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0767445191740990</threshold>
            <left_val>-0.0314132310450077</left_val>
            <right_val>0.5993549227714539</right_val></_></_>
        <_>
          <!-- tree 62 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 0 8 3 -1.</_>
                <_>
                  5 1 8 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.0785229541361332e-003</threshold>
            <left_val>-0.0529119409620762</left_val>
            <right_val>0.2334239929914475</right_val></_></_>
        <_>
          <!-- tree 63 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 6 4 4 -1.</_>
                <_>
                  16 6 2 2 2.</_>
                <_>
                  18 8 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.1800279393792152e-003</threshold>
            <left_val>-0.0777343884110451</left_val>
            <right_val>0.1765290945768356</right_val></_></_>
        <_>
          <!-- tree 64 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 4 2 8 -1.</_>
                <_>
                  17 4 1 4 2.</_>
                <_>
                  18 8 1 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7729829996824265e-003</threshold>
            <left_val>0.1959162950515747</left_val>
            <right_val>-0.0797521993517876</right_val></_></_>
        <_>
          <!-- tree 65 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 16 1 3 -1.</_>
                <_>
                  2 17 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.8560940194875002e-004</threshold>
            <left_val>-0.2880037128925324</left_val>
            <right_val>0.0490471199154854</right_val></_></_>
        <_>
          <!-- tree 66 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 16 1 3 -1.</_>
                <_>
                  2 17 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.6554320831783116e-004</threshold>
            <left_val>0.0679228976368904</left_val>
            <right_val>-0.2249943017959595</right_val></_></_>
        <_>
          <!-- tree 67 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 0 1 3 -1.</_>
                <_>
                  11 1 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.6938671362586319e-004</threshold>
            <left_val>0.1658217012882233</left_val>
            <right_val>-0.0897440984845161</right_val></_></_>
        <_>
          <!-- tree 68 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 2 9 7 -1.</_>
                <_>
                  14 2 3 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0786842331290245</threshold>
            <left_val>0.0260816793888807</left_val>
            <right_val>-0.5569373965263367</right_val></_></_>
        <_>
          <!-- tree 69 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 2 3 6 -1.</_>
                <_>
                  11 2 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.3774810880422592e-004</threshold>
            <left_val>0.1403687000274658</left_val>
            <right_val>-0.1180030032992363</right_val></_></_>
        <_>
          <!-- tree 70 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 9 15 2 -1.</_>
                <_>
                  5 10 15 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0239578299224377</threshold>
            <left_val>0.0304707400500774</left_val>
            <right_val>-0.4615997970104218</right_val></_></_>
        <_>
          <!-- tree 71 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 16 6 2 -1.</_>
                <_>
                  8 17 6 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.6239080578088760e-003</threshold>
            <left_val>0.2632707953453064</left_val>
            <right_val>-0.0567653700709343</right_val></_></_>
        <_>
          <!-- tree 72 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 16 10 2 -1.</_>
                <_>
                  9 16 5 1 2.</_>
                <_>
                  14 17 5 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.0819748584181070e-004</threshold>
            <left_val>0.1546245962381363</left_val>
            <right_val>-0.1108706966042519</right_val></_></_>
        <_>
          <!-- tree 73 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 17 2 2 -1.</_>
                <_>
                  9 17 1 1 2.</_>
                <_>
                  10 18 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.9806248969398439e-004</threshold>
            <left_val>0.0556303709745407</left_val>
            <right_val>-0.2833195924758911</right_val></_></_>
        <_>
          <!-- tree 74 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 15 6 4 -1.</_>
                <_>
                  10 15 3 2 2.</_>
                <_>
                  13 17 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.0506449509412050e-003</threshold>
            <left_val>-0.0916048362851143</left_val>
            <right_val>0.1758553981781006</right_val></_></_>
        <_>
          <!-- tree 75 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 5 15 12 -1.</_>
                <_>
                  9 5 5 12 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0267425496131182</threshold>
            <left_val>0.0620030313730240</left_val>
            <right_val>-0.2448700070381165</right_val></_></_>
        <_>
          <!-- tree 76 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 13 2 3 -1.</_>
                <_>
                  11 14 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.1497008856385946e-003</threshold>
            <left_val>0.2944929897785187</left_val>
            <right_val>-0.0532181486487389</right_val></_></_>
        <_>
          <!-- tree 77 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 13 7 3 -1.</_>
                <_>
                  8 14 7 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.6671658530831337e-003</threshold>
            <left_val>-0.0642982423305511</left_val>
            <right_val>0.2490568011999130</right_val></_></_>
        <_>
          <!-- tree 78 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 12 1 2 -1.</_>
                <_>
                  1 13 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.8317902332637459e-005</threshold>
            <left_val>-0.1681963056325913</left_val>
            <right_val>0.0965485796332359</right_val></_></_>
        <_>
          <!-- tree 79 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 18 2 2 -1.</_>
                <_>
                  16 18 1 1 2.</_>
                <_>
                  17 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.7600439605303109e-004</threshold>
            <left_val>0.0653080120682716</left_val>
            <right_val>-0.2426788061857224</right_val></_></_>
        <_>
          <!-- tree 80 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 19 18 1 -1.</_>
                <_>
                  7 19 6 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.1861608624458313e-003</threshold>
            <left_val>-0.0979885831475258</left_val>
            <right_val>0.1805288940668106</right_val></_></_>
        <_>
          <!-- tree 81 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 17 6 1 -1.</_>
                <_>
                  4 17 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.1808340679854155e-003</threshold>
            <left_val>0.1923127025365830</left_val>
            <right_val>-0.0941239297389984</right_val></_></_>
        <_>
          <!-- tree 82 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 3 1 12 -1.</_>
                <_>
                  1 9 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0217304006218910</threshold>
            <left_val>0.0355785116553307</left_val>
            <right_val>-0.4508853852748871</right_val></_></_>
        <_>
          <!-- tree 83 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 9 3 6 -1.</_>
                <_>
                  0 11 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0147802699357271</threshold>
            <left_val>-0.4392701089382172</left_val>
            <right_val>0.0317355915904045</right_val></_></_>
        <_>
          <!-- tree 84 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 4 3 10 -1.</_>
                <_>
                  6 4 1 10 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.6145891062915325e-003</threshold>
            <left_val>0.1981147974729538</left_val>
            <right_val>-0.0777014195919037</right_val></_></_>
        <_>
          <!-- tree 85 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 17 2 1 -1.</_>
                <_>
                  7 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.8892709631472826e-003</threshold>
            <left_val>0.0199624393135309</left_val>
            <right_val>-0.7204172015190125</right_val></_></_>
        <_>
          <!-- tree 86 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 6 12 -1.</_>
                <_>
                  3 0 2 12 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.3822480104863644e-003</threshold>
            <left_val>0.0984669476747513</left_val>
            <right_val>-0.1488108038902283</right_val></_></_>
        <_>
          <!-- tree 87 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 7 9 2 -1.</_>
                <_>
                  7 7 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.9505911991000175e-003</threshold>
            <left_val>0.1159323006868362</left_val>
            <right_val>-0.1279197037220001</right_val></_></_></trees>
      <stage_threshold>-0.9769343137741089</stage_threshold>
      <parent>20</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 22 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 11 9 1 -1.</_>
                <_>
                  9 11 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0193955395370722</threshold>
            <left_val>0.4747475087642670</left_val>
            <right_val>-0.1172109022736549</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 10 2 10 -1.</_>
                <_>
                  17 15 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0131189199164510</threshold>
            <left_val>-0.2555212974548340</left_val>
            <right_val>0.1637880057096481</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 10 2 10 -1.</_>
                <_>
                  4 10 1 5 2.</_>
                <_>
                  5 15 1 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.1606801571324468e-004</threshold>
            <left_val>0.1945261955261231</left_val>
            <right_val>-0.1744889020919800</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 3 3 12 -1.</_>
                <_>
                  13 3 1 12 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0131841599941254</threshold>
            <left_val>0.4418145120143890</left_val>
            <right_val>-0.0900487527251244</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 3 4 6 -1.</_>
                <_>
                  15 3 2 3 2.</_>
                <_>
                  17 6 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.4657081123441458e-003</threshold>
            <left_val>-0.1347709000110626</left_val>
            <right_val>0.1805634051561356</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 8 3 3 -1.</_>
                <_>
                  13 8 1 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.2980200164020061e-003</threshold>
            <left_val>-0.0541649796068668</left_val>
            <right_val>0.3603338003158569</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 14 2 4 -1.</_>
                <_>
                  4 16 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.6879989998415112e-003</threshold>
            <left_val>-0.1999794989824295</left_val>
            <right_val>0.1202159970998764</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 16 1 3 -1.</_>
                <_>
                  6 17 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.6039709812030196e-004</threshold>
            <left_val>0.1052414029836655</left_val>
            <right_val>-0.2411606013774872</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 1 2 3 -1.</_>
                <_>
                  2 1 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5276849735528231e-003</threshold>
            <left_val>0.2813552916049957</left_val>
            <right_val>-0.0689648166298866</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 2 4 1 -1.</_>
                <_>
                  2 2 2 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.5033570602536201e-003</threshold>
            <left_val>-0.0825195834040642</left_val>
            <right_val>0.4071359038352966</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 17 12 3 -1.</_>
                <_>
                  12 17 4 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.7337161377072334e-003</threshold>
            <left_val>0.1972700953483582</left_val>
            <right_val>-0.1171014010906220</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 16 6 4 -1.</_>
                <_>
                  11 16 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0115571497008204</threshold>
            <left_val>-0.5606111288070679</left_val>
            <right_val>0.0681709572672844</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 6 3 6 -1.</_>
                <_>
                  4 9 3 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0274457205086946</threshold>
            <left_val>0.4971862137317658</left_val>
            <right_val>-0.0623801499605179</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 2 12 9 -1.</_>
                <_>
                  6 5 12 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0528257787227631</threshold>
            <left_val>0.1692122071981430</left_val>
            <right_val>-0.1309355050325394</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 0 14 20 -1.</_>
                <_>
                  6 0 7 10 2.</_>
                <_>
                  13 10 7 10 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.2984969913959503</threshold>
            <left_val>-0.6464967131614685</left_val>
            <right_val>0.0400768183171749</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 16 2 2 -1.</_>
                <_>
                  15 16 1 1 2.</_>
                <_>
                  16 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.6307269581593573e-004</threshold>
            <left_val>0.2512794137001038</left_val>
            <right_val>-0.0894948393106461</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 16 2 2 -1.</_>
                <_>
                  15 16 1 1 2.</_>
                <_>
                  16 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.3261709429789335e-004</threshold>
            <left_val>-0.0868439897894859</left_val>
            <right_val>0.2383197993040085</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 8 1 3 -1.</_>
                <_>
                  19 9 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.3631360090803355e-004</threshold>
            <left_val>0.1155446022748947</left_val>
            <right_val>-0.1893634945154190</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 4 1 2 -1.</_>
                <_>
                  13 5 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.0742209162563086e-003</threshold>
            <left_val>-0.0485948510468006</left_val>
            <right_val>0.5748599171638489</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 4 4 2 -1.</_>
                <_>
                  0 5 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.0308889262378216e-003</threshold>
            <left_val>-0.5412080883979797</left_val>
            <right_val>0.0487437509000301</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 5 1 6 -1.</_>
                <_>
                  19 7 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.2652270793914795e-003</threshold>
            <left_val>0.0264945197850466</left_val>
            <right_val>-0.6172845959663391</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 0 2 1 -1.</_>
                <_>
                  17 0 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.0042760297656059e-004</threshold>
            <left_val>-0.1176863014698029</left_val>
            <right_val>0.1633386015892029</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 1 1 3 -1.</_>
                <_>
                  13 2 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.6470040427520871e-003</threshold>
            <left_val>-0.0599549189209938</left_val>
            <right_val>0.3517970144748688</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 17 1 3 -1.</_>
                <_>
                  17 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.5642538568936288e-004</threshold>
            <left_val>-0.3442029953002930</left_val>
            <right_val>0.0649482533335686</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 4 8 8 -1.</_>
                <_>
                  5 4 4 4 2.</_>
                <_>
                  9 8 4 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0309358704835176</threshold>
            <left_val>0.1997970044612885</left_val>
            <right_val>-0.0976936966180801</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 2 2 2 -1.</_>
                <_>
                  1 2 1 1 2.</_>
                <_>
                  2 3 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.3578772824257612e-004</threshold>
            <left_val>-0.3148139119148254</left_val>
            <right_val>0.0594250410795212</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 0 8 6 -1.</_>
                <_>
                  0 0 4 3 2.</_>
                <_>
                  4 3 4 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0118621801957488</threshold>
            <left_val>0.2004369050264359</left_val>
            <right_val>-0.0894475430250168</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 3 4 2 -1.</_>
                <_>
                  6 4 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.1508930996060371e-003</threshold>
            <left_val>-0.0390060618519783</left_val>
            <right_val>0.5332716107368469</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 0 3 3 -1.</_>
                <_>
                  1 1 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.0059191156178713e-003</threshold>
            <left_val>-0.2846972048282623</left_val>
            <right_val>0.0707236081361771</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 1 7 2 -1.</_>
                <_>
                  6 2 7 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>3.6412389017641544e-003</threshold>
            <left_val>-0.1066031977534294</left_val>
            <right_val>0.2494480013847351</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 6 12 6 -1.</_>
                <_>
                  6 6 4 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1346742957830429</threshold>
            <left_val>0.4991008043289185</left_val>
            <right_val>-0.0403322204947472</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 16 9 2 -1.</_>
                <_>
                  4 16 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2547659464180470e-003</threshold>
            <left_val>0.1685169041156769</left_val>
            <right_val>-0.1111928001046181</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 15 6 4 -1.</_>
                <_>
                  9 15 2 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.3842289596796036e-003</threshold>
            <left_val>0.0861394926905632</left_val>
            <right_val>-0.2743177115917206</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 15 12 1 -1.</_>
                <_>
                  12 15 6 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.3361168615520000e-003</threshold>
            <left_val>0.2487521022558212</left_val>
            <right_val>-0.0959191620349884</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 17 1 3 -1.</_>
                <_>
                  17 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.4666912658140063e-004</threshold>
            <left_val>0.0674315765500069</left_val>
            <right_val>-0.3375408053398132</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 15 2 2 -1.</_>
                <_>
                  17 15 1 1 2.</_>
                <_>
                  18 16 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.2983769304119051e-004</threshold>
            <left_val>-0.0839030519127846</left_val>
            <right_val>0.2458409965038300</right_val></_></_>
        <_>
          <!-- tree 36 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 13 3 3 -1.</_>
                <_>
                  3 14 3 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.7039071582257748e-003</threshold>
            <left_val>0.0290793292224407</left_val>
            <right_val>-0.6905593872070313</right_val></_></_>
        <_>
          <!-- tree 37 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 17 1 3 -1.</_>
                <_>
                  10 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.0734888645820320e-005</threshold>
            <left_val>-0.1569671928882599</left_val>
            <right_val>0.1196542978286743</right_val></_></_>
        <_>
          <!-- tree 38 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 0 14 8 -1.</_>
                <_>
                  11 0 7 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.2033555954694748</threshold>
            <left_val>-0.6950634717941284</left_val>
            <right_val>0.0275075193494558</right_val></_></_>
        <_>
          <!-- tree 39 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 0 12 2 -1.</_>
                <_>
                  6 0 4 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.4939414411783218e-003</threshold>
            <left_val>-0.0874493718147278</left_val>
            <right_val>0.2396833002567291</right_val></_></_>
        <_>
          <!-- tree 40 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 0 4 3 -1.</_>
                <_>
                  4 0 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.4055240210145712e-003</threshold>
            <left_val>0.2115096002817154</left_val>
            <right_val>-0.1314893066883087</right_val></_></_>
        <_>
          <!-- tree 41 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 1 1 2 -1.</_>
                <_>
                  13 2 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.1342419747961685e-004</threshold>
            <left_val>0.1523378938436508</left_val>
            <right_val>-0.1272590011358261</right_val></_></_>
        <_>
          <!-- tree 42 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 5 3 6 -1.</_>
                <_>
                  8 5 1 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0149922100827098</threshold>
            <left_val>-0.0341279692947865</left_val>
            <right_val>0.5062407255172730</right_val></_></_>
        <_>
          <!-- tree 43 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 2 2 2 -1.</_>
                <_>
                  18 2 1 1 2.</_>
                <_>
                  19 3 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.4068200774490833e-004</threshold>
            <left_val>0.0487647503614426</left_val>
            <right_val>-0.4022532105445862</right_val></_></_>
        <_>
          <!-- tree 44 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 1 2 14 -1.</_>
                <_>
                  16 1 1 14 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.2459447868168354e-003</threshold>
            <left_val>0.2155476063489914</left_val>
            <right_val>-0.0871269926428795</right_val></_></_>
        <_>
          <!-- tree 45 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 6 2 2 -1.</_>
                <_>
                  15 6 1 1 2.</_>
                <_>
                  16 7 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>6.8655109498649836e-004</threshold>
            <left_val>-0.0754187181591988</left_val>
            <right_val>0.2640590965747833</right_val></_></_>
        <_>
          <!-- tree 46 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 1 6 3 -1.</_>
                <_>
                  5 1 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0167514607310295</threshold>
            <left_val>-0.6772903203964233</left_val>
            <right_val>0.0329187288880348</right_val></_></_>
        <_>
          <!-- tree 47 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 16 2 2 -1.</_>
                <_>
                  7 16 1 1 2.</_>
                <_>
                  8 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.6301678735762835e-004</threshold>
            <left_val>0.2272586971521378</left_val>
            <right_val>-0.0905348733067513</right_val></_></_>
        <_>
          <!-- tree 48 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 17 2 2 -1.</_>
                <_>
                  5 17 1 1 2.</_>
                <_>
                  6 18 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.3398610432632267e-004</threshold>
            <left_val>0.0558943785727024</left_val>
            <right_val>-0.3559266924858093</right_val></_></_>
        <_>
          <!-- tree 49 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 10 6 10 -1.</_>
                <_>
                  11 10 2 10 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0201501492410898</threshold>
            <left_val>0.1916276067495346</left_val>
            <right_val>-0.0949299708008766</right_val></_></_>
        <_>
          <!-- tree 50 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 17 6 3 -1.</_>
                <_>
                  12 17 2 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0144521296024323</threshold>
            <left_val>-0.6851034164428711</left_val>
            <right_val>0.0254221707582474</right_val></_></_>
        <_>
          <!-- tree 51 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 5 2 10 -1.</_>
                <_>
                  14 10 2 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0211497396230698</threshold>
            <left_val>0.3753319084644318</left_val>
            <right_val>-0.0514965802431107</right_val></_></_>
        <_>
          <!-- tree 52 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 12 6 2 -1.</_>
                <_>
                  11 13 6 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0211377702653408</threshold>
            <left_val>0.0290830805897713</left_val>
            <right_val>-0.8943036794662476</right_val></_></_>
        <_>
          <!-- tree 53 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 1 1 3 -1.</_>
                <_>
                  8 2 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.1524349683895707e-003</threshold>
            <left_val>-0.0696949362754822</left_val>
            <right_val>0.2729980051517487</right_val></_></_>
        <_>
          <!-- tree 54 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 15 2 2 -1.</_>
                <_>
                  12 15 1 1 2.</_>
                <_>
                  13 16 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.9070580310653895e-004</threshold>
            <left_val>0.1822811961174011</left_val>
            <right_val>-0.0983670726418495</right_val></_></_>
        <_>
          <!-- tree 55 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 8 6 4 -1.</_>
                <_>
                  6 8 3 2 2.</_>
                <_>
                  9 10 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0363496318459511</threshold>
            <left_val>-0.8369309902191162</left_val>
            <right_val>0.0250557605177164</right_val></_></_>
        <_>
          <!-- tree 56 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 5 3 5 -1.</_>
                <_>
                  8 5 1 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.0632075443863869e-003</threshold>
            <left_val>0.4146350026130676</left_val>
            <right_val>-0.0544134490191936</right_val></_></_>
        <_>
          <!-- tree 57 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 5 7 3 -1.</_>
                <_>
                  0 6 7 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.0535490475594997e-003</threshold>
            <left_val>-0.1975031048059464</left_val>
            <right_val>0.1050689965486527</right_val></_></_></trees>
      <stage_threshold>-1.0129359960556030</stage_threshold>
      <parent>21</parent>
      <next>-1</next></_>
    <_>
      <!-- stage 23 -->
      <trees>
        <_>
          <!-- tree 0 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 9 6 6 -1.</_>
                <_>
                  9 9 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0227170195430517</threshold>
            <left_val>0.2428855001926422</left_val>
            <right_val>-0.1474552005529404</right_val></_></_>
        <_>
          <!-- tree 1 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 7 8 8 -1.</_>
                <_>
                  5 11 8 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0255059506744146</threshold>
            <left_val>-0.2855173945426941</left_val>
            <right_val>0.1083720996975899</right_val></_></_>
        <_>
          <!-- tree 2 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 9 2 6 -1.</_>
                <_>
                  4 9 1 3 2.</_>
                <_>
                  5 12 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.6640091091394424e-003</threshold>
            <left_val>0.2927573025226593</left_val>
            <right_val>-0.1037271022796631</right_val></_></_>
        <_>
          <!-- tree 3 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 11 6 1 -1.</_>
                <_>
                  12 11 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.8115289062261581e-003</threshold>
            <left_val>0.2142689973115921</left_val>
            <right_val>-0.1381113976240158</right_val></_></_>
        <_>
          <!-- tree 4 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 6 6 11 -1.</_>
                <_>
                  15 6 2 11 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0167326908558607</threshold>
            <left_val>0.2655026018619537</left_val>
            <right_val>-0.0439113304018974</right_val></_></_>
        <_>
          <!-- tree 5 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 17 2 2 -1.</_>
                <_>
                  8 17 1 1 2.</_>
                <_>
                  9 18 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.9277010839432478e-004</threshold>
            <left_val>0.0211045593023300</left_val>
            <right_val>-0.4297136068344116</right_val></_></_>
        <_>
          <!-- tree 6 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 12 12 1 -1.</_>
                <_>
                  8 12 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0366911105811596</threshold>
            <left_val>0.5399242043495178</left_val>
            <right_val>-0.0436488017439842</right_val></_></_>
        <_>
          <!-- tree 7 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 17 3 2 -1.</_>
                <_>
                  11 18 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.2615970335900784e-003</threshold>
            <left_val>-0.1293386965990067</left_val>
            <right_val>0.1663877069950104</right_val></_></_>
        <_>
          <!-- tree 8 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 17 6 1 -1.</_>
                <_>
                  10 17 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.4106856957077980e-003</threshold>
            <left_val>-0.9469841122627258</left_val>
            <right_val>0.0214658491313457</right_val></_></_>
        <_>
          <!-- tree 9 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 1 14 6 -1.</_>
                <_>
                  4 3 14 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0649027228355408</threshold>
            <left_val>-0.0717277601361275</left_val>
            <right_val>0.2661347985267639</right_val></_></_>
        <_>
          <!-- tree 10 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 2 2 12 -1.</_>
                <_>
                  14 8 2 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0303050000220537</threshold>
            <left_val>-0.0827824920415878</left_val>
            <right_val>0.2769432067871094</right_val></_></_>
        <_>
          <!-- tree 11 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 13 3 2 -1.</_>
                <_>
                  12 14 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.5875340215861797e-003</threshold>
            <left_val>-0.1296616941690445</left_val>
            <right_val>0.1775663048028946</right_val></_></_>
        <_>
          <!-- tree 12 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 1 6 1 -1.</_>
                <_>
                  8 1 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.0240451022982597e-003</threshold>
            <left_val>-0.6424317955970764</left_val>
            <right_val>0.0399432107806206</right_val></_></_>
        <_>
          <!-- tree 13 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 6 6 1 -1.</_>
                <_>
                  12 6 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.0099769569933414e-003</threshold>
            <left_val>0.1417661011219025</left_val>
            <right_val>-0.1165997013449669</right_val></_></_>
        <_>
          <!-- tree 14 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 19 2 1 -1.</_>
                <_>
                  4 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.1179071558872238e-005</threshold>
            <left_val>0.1568766981363297</left_val>
            <right_val>-0.1112734004855156</right_val></_></_>
        <_>
          <!-- tree 15 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 16 2 2 -1.</_>
                <_>
                  18 16 1 1 2.</_>
                <_>
                  19 17 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.7293151146732271e-004</threshold>
            <left_val>-0.3355455994606018</left_val>
            <right_val>0.0459777303040028</right_val></_></_>
        <_>
          <!-- tree 16 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 11 3 7 -1.</_>
                <_>
                  17 11 1 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.7178079579025507e-003</threshold>
            <left_val>0.1695290952920914</left_val>
            <right_val>-0.1057806983590126</right_val></_></_>
        <_>
          <!-- tree 17 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 5 1 6 -1.</_>
                <_>
                  19 8 1 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0133331697434187</threshold>
            <left_val>-0.5825781226158142</left_val>
            <right_val>0.0309784300625324</right_val></_></_>
        <_>
          <!-- tree 18 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 8 4 3 -1.</_>
                <_>
                  9 9 4 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.8783430568873882e-003</threshold>
            <left_val>0.1426687985658646</left_val>
            <right_val>-0.1113125979900360</right_val></_></_>
        <_>
          <!-- tree 19 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 8 4 4 -1.</_>
                <_>
                  16 8 2 2 2.</_>
                <_>
                  18 10 2 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.5765981562435627e-003</threshold>
            <left_val>0.2756136059761047</left_val>
            <right_val>-0.0531003288924694</right_val></_></_>
        <_>
          <!-- tree 20 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 8 2 2 -1.</_>
                <_>
                  2 8 1 1 2.</_>
                <_>
                  3 9 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.7210381277836859e-005</threshold>
            <left_val>0.1324024051427841</left_val>
            <right_val>-0.1116779968142510</right_val></_></_>
        <_>
          <!-- tree 21 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 5 6 4 -1.</_>
                <_>
                  3 5 3 2 2.</_>
                <_>
                  6 7 3 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0219685398042202</threshold>
            <left_val>-0.0269681606441736</left_val>
            <right_val>0.5006716847419739</right_val></_></_>
        <_>
          <!-- tree 22 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 3 8 16 -1.</_>
                <_>
                  2 3 4 8 2.</_>
                <_>
                  6 11 4 8 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0274457503110170</threshold>
            <left_val>-0.2408674061298370</left_val>
            <right_val>0.0604782700538635</right_val></_></_>
        <_>
          <!-- tree 23 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 17 1 3 -1.</_>
                <_>
                  17 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.8305849456228316e-005</threshold>
            <left_val>-0.1333488970994949</left_val>
            <right_val>0.1012346968054771</right_val></_></_>
        <_>
          <!-- tree 24 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 2 8 11 -1.</_>
                <_>
                  11 2 4 11 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0701906830072403</threshold>
            <left_val>-0.0548637807369232</left_val>
            <right_val>0.2480994015932083</right_val></_></_>
        <_>
          <!-- tree 25 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 3 6 14 -1.</_>
                <_>
                  16 3 3 14 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0719021335244179</threshold>
            <left_val>-0.3784669041633606</left_val>
            <right_val>0.0422109998762608</right_val></_></_>
        <_>
          <!-- tree 26 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 9 18 2 -1.</_>
                <_>
                  6 9 6 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1078097969293594</threshold>
            <left_val>-0.3748658895492554</left_val>
            <right_val>0.0428334400057793</right_val></_></_>
        <_>
          <!-- tree 27 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 10 14 3 -1.</_>
                <_>
                  6 11 14 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.4364200178533792e-003</threshold>
            <left_val>0.0804763585329056</left_val>
            <right_val>-0.1726378947496414</right_val></_></_>
        <_>
          <!-- tree 28 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 9 9 3 -1.</_>
                <_>
                  13 9 3 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0682891905307770</threshold>
            <left_val>-0.0355957895517349</left_val>
            <right_val>0.4076131880283356</right_val></_></_>
        <_>
          <!-- tree 29 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 5 4 6 -1.</_>
                <_>
                  3 5 2 3 2.</_>
                <_>
                  5 8 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.8037179298698902e-003</threshold>
            <left_val>0.1923379004001617</left_val>
            <right_val>-0.0823680236935616</right_val></_></_>
        <_>
          <!-- tree 30 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 7 3 7 -1.</_>
                <_>
                  4 7 1 7 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.6193489581346512e-004</threshold>
            <left_val>0.1305712014436722</left_val>
            <right_val>-0.1435514986515045</right_val></_></_>
        <_>
          <!-- tree 31 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 8 11 6 -1.</_>
                <_>
                  2 10 11 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0582766495645046</threshold>
            <left_val>-0.3012543916702271</left_val>
            <right_val>0.0528196506202221</right_val></_></_>
        <_>
          <!-- tree 32 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 9 6 3 -1.</_>
                <_>
                  8 10 6 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.1205718666315079e-003</threshold>
            <left_val>0.2204390019178391</left_val>
            <right_val>-0.0756917521357536</right_val></_></_>
        <_>
          <!-- tree 33 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 3 3 11 -1.</_>
                <_>
                  4 3 1 11 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0135943097993732</threshold>
            <left_val>-0.3904936015605927</left_val>
            <right_val>0.0418571084737778</right_val></_></_>
        <_>
          <!-- tree 34 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 19 6 1 -1.</_>
                <_>
                  3 19 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.3626200379803777e-003</threshold>
            <left_val>-0.0953634232282639</left_val>
            <right_val>0.1497032046318054</right_val></_></_>
        <_>
          <!-- tree 35 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 18 1 2 -1.</_>
                <_>
                  18 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.5074219845701009e-004</threshold>
            <left_val>-0.2394558042287827</left_val>
            <right_val>0.0647983327507973</right_val></_></_>
        <_>
          <!-- tree 36 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 0 12 6 -1.</_>
                <_>
                  8 0 6 3 2.</_>
                <_>
                  14 3 6 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0774142593145370</threshold>
            <left_val>0.5594198107719421</left_val>
            <right_val>-0.0245168805122375</right_val></_></_>
        <_>
          <!-- tree 37 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 5 1 3 -1.</_>
                <_>
                  19 6 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.2117872554808855e-004</threshold>
            <left_val>0.0549288615584373</left_val>
            <right_val>-0.2793481051921845</right_val></_></_>
        <_>
          <!-- tree 38 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  5 8 2 1 -1.</_>
                <_>
                  6 8 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.0250780032947659e-003</threshold>
            <left_val>-0.0621673092246056</left_val>
            <right_val>0.2497636973857880</right_val></_></_>
        <_>
          <!-- tree 39 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 11 2 1 -1.</_>
                <_>
                  14 11 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-8.1174750812351704e-004</threshold>
            <left_val>0.2343793958425522</left_val>
            <right_val>-0.0657258108258247</right_val></_></_>
        <_>
          <!-- tree 40 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 6 15 13 -1.</_>
                <_>
                  8 6 5 13 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0834310203790665</threshold>
            <left_val>0.0509548000991344</left_val>
            <right_val>-0.3102098107337952</right_val></_></_>
        <_>
          <!-- tree 41 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 3 6 2 -1.</_>
                <_>
                  6 3 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-9.2014456167817116e-003</threshold>
            <left_val>-0.3924253880977631</left_val>
            <right_val>0.0329269506037235</right_val></_></_>
        <_>
          <!-- tree 42 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 18 1 2 -1.</_>
                <_>
                  0 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.9086650465615094e-004</threshold>
            <left_val>-0.3103975057601929</left_val>
            <right_val>0.0497118197381496</right_val></_></_>
        <_>
          <!-- tree 43 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 8 2 6 -1.</_>
                <_>
                  8 8 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.7576898038387299e-003</threshold>
            <left_val>-0.0440407507121563</left_val>
            <right_val>0.3643135130405426</right_val></_></_>
        <_>
          <!-- tree 44 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 0 6 19 -1.</_>
                <_>
                  5 0 2 19 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.1246609017252922</threshold>
            <left_val>-0.8195707798004150</left_val>
            <right_val>0.0191506408154964</right_val></_></_>
        <_>
          <!-- tree 45 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  3 1 6 5 -1.</_>
                <_>
                  5 1 2 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0132425501942635</threshold>
            <left_val>0.0389888398349285</left_val>
            <right_val>-0.3323068022727966</right_val></_></_>
        <_>
          <!-- tree 46 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 14 3 6 -1.</_>
                <_>
                  17 16 3 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.6770128905773163e-003</threshold>
            <left_val>-0.3579013943672180</left_val>
            <right_val>0.0404602102935314</right_val></_></_>
        <_>
          <!-- tree 47 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 13 2 6 -1.</_>
                <_>
                  18 13 1 6 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.7479929849505424e-003</threshold>
            <left_val>0.2525390088558197</left_val>
            <right_val>-0.0564278215169907</right_val></_></_>
        <_>
          <!-- tree 48 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 18 2 2 -1.</_>
                <_>
                  18 18 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>8.2659651525318623e-004</threshold>
            <left_val>-0.0719886571168900</left_val>
            <right_val>0.2278047949075699</right_val></_></_>
        <_>
          <!-- tree 49 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 14 9 4 -1.</_>
                <_>
                  14 14 3 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0501534007489681</threshold>
            <left_val>-0.6303647160530090</left_val>
            <right_val>0.0274620503187180</right_val></_></_>
        <_>
          <!-- tree 50 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 8 4 6 -1.</_>
                <_>
                  15 8 2 3 2.</_>
                <_>
                  17 11 2 3 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>7.4203149415552616e-003</threshold>
            <left_val>-0.0666107162833214</left_val>
            <right_val>0.2778733968734741</right_val></_></_>
        <_>
          <!-- tree 51 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 16 1 3 -1.</_>
                <_>
                  1 17 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.7951780511066318e-004</threshold>
            <left_val>-0.3632706105709076</left_val>
            <right_val>0.0427954308688641</right_val></_></_>
        <_>
          <!-- tree 52 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 0 3 14 -1.</_>
                <_>
                  8 0 1 14 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.9305750029161572e-003</threshold>
            <left_val>0.1419623047113419</left_val>
            <right_val>-0.1075998023152351</right_val></_></_>
        <_>
          <!-- tree 53 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 0 2 1 -1.</_>
                <_>
                  13 0 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.8132671033963561e-004</threshold>
            <left_val>0.2159176021814346</left_val>
            <right_val>-0.0702026635408401</right_val></_></_>
        <_>
          <!-- tree 54 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 9 6 5 -1.</_>
                <_>
                  10 9 3 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0709903463721275</threshold>
            <left_val>0.4526660144329071</left_val>
            <right_val>-0.0407504811882973</right_val></_></_>
        <_>
          <!-- tree 55 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 5 4 9 -1.</_>
                <_>
                  17 5 2 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0533680804073811</threshold>
            <left_val>-0.6767405867576599</left_val>
            <right_val>0.0192883405834436</right_val></_></_>
        <_>
          <!-- tree 56 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 0 6 6 -1.</_>
                <_>
                  13 0 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0200648494064808</threshold>
            <left_val>-0.4336543083190918</left_val>
            <right_val>0.0318532884120941</right_val></_></_>
        <_>
          <!-- tree 57 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 15 2 2 -1.</_>
                <_>
                  16 15 1 1 2.</_>
                <_>
                  17 16 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.1976360110566020e-003</threshold>
            <left_val>-0.0265598706901073</left_val>
            <right_val>0.5079718232154846</right_val></_></_>
        <_>
          <!-- tree 58 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 15 2 2 -1.</_>
                <_>
                  16 15 1 1 2.</_>
                <_>
                  17 16 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2697300300933421e-004</threshold>
            <left_val>0.1801259964704514</left_val>
            <right_val>-0.0836065486073494</right_val></_></_>
        <_>
          <!-- tree 59 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  13 2 2 18 -1.</_>
                <_>
                  13 11 2 9 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.0152626996859908</threshold>
            <left_val>-0.2023892998695374</left_val>
            <right_val>0.0674220174551010</right_val></_></_>
        <_>
          <!-- tree 60 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 4 8 10 -1.</_>
                <_>
                  8 9 8 5 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.2081176936626434</threshold>
            <left_val>0.6694386005401611</left_val>
            <right_val>-0.0224521104246378</right_val></_></_>
        <_>
          <!-- tree 61 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 3 2 3 -1.</_>
                <_>
                  8 4 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>1.5514369588345289e-003</threshold>
            <left_val>-0.0751218423247337</left_val>
            <right_val>0.1732691973447800</right_val></_></_>
        <_>
          <!-- tree 62 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 1 6 9 -1.</_>
                <_>
                  11 4 6 3 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0529240109026432</threshold>
            <left_val>0.2499251961708069</left_val>
            <right_val>-0.0628791674971581</right_val></_></_>
        <_>
          <!-- tree 63 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  15 4 5 6 -1.</_>
                <_>
                  15 6 5 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0216488502919674</threshold>
            <left_val>-0.2919428050518036</left_val>
            <right_val>0.0526144914329052</right_val></_></_>
        <_>
          <!-- tree 64 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 18 2 2 -1.</_>
                <_>
                  12 18 1 1 2.</_>
                <_>
                  13 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2905069636180997e-004</threshold>
            <left_val>-0.2211730033159256</left_val>
            <right_val>0.0631683394312859</right_val></_></_>
        <_>
          <!-- tree 65 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 17 1 3 -1.</_>
                <_>
                  1 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.0170070608146489e-005</threshold>
            <left_val>-0.1151070967316628</left_val>
            <right_val>0.1161144003272057</right_val></_></_>
        <_>
          <!-- tree 66 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 19 2 1 -1.</_>
                <_>
                  13 19 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-1.6416069411206990e-004</threshold>
            <left_val>0.1587152034044266</left_val>
            <right_val>-0.0826006010174751</right_val></_></_>
        <_>
          <!-- tree 67 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  8 10 6 6 -1.</_>
                <_>
                  10 10 2 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0120032895356417</threshold>
            <left_val>0.1221809014678001</left_val>
            <right_val>-0.1122969985008240</right_val></_></_>
        <_>
          <!-- tree 68 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 2 6 5 -1.</_>
                <_>
                  16 2 2 5 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0177841000258923</threshold>
            <left_val>-0.3507278859615326</left_val>
            <right_val>0.0313419215381145</right_val></_></_>
        <_>
          <!-- tree 69 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 5 2 6 -1.</_>
                <_>
                  9 7 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-6.3457582145929337e-003</threshold>
            <left_val>0.1307806968688965</left_val>
            <right_val>-0.1057441011071205</right_val></_></_>
        <_>
          <!-- tree 70 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 15 2 2 -1.</_>
                <_>
                  2 15 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.9523242311552167e-004</threshold>
            <left_val>0.1720467060804367</left_val>
            <right_val>-0.0860019922256470</right_val></_></_>
        <_>
          <!-- tree 71 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 17 1 3 -1.</_>
                <_>
                  18 18 1 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.1029590172693133e-004</threshold>
            <left_val>-0.2843317091464996</left_val>
            <right_val>0.0518171191215515</right_val></_></_>
        <_>
          <!-- tree 72 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  10 14 4 6 -1.</_>
                <_>
                  10 16 4 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0170537102967501</threshold>
            <left_val>0.3924242854118347</left_val>
            <right_val>-0.0401432700455189</right_val></_></_>
        <_>
          <!-- tree 73 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 7 3 2 -1.</_>
                <_>
                  10 7 1 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.6504959464073181e-003</threshold>
            <left_val>-0.0318375602364540</left_val>
            <right_val>0.4123769998550415</right_val></_></_>
        <_>
          <!-- tree 74 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 9 6 2 -1.</_>
                <_>
                  6 9 3 1 2.</_>
                <_>
                  9 10 3 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0103587601333857</threshold>
            <left_val>-0.5699319839477539</left_val>
            <right_val>0.0292483791708946</right_val></_></_>
        <_>
          <!-- tree 75 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 2 1 12 -1.</_>
                <_>
                  0 6 1 4 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0221962407231331</threshold>
            <left_val>-0.4560528993606567</left_val>
            <right_val>0.0262859892100096</right_val></_></_>
        <_>
          <!-- tree 76 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 0 15 1 -1.</_>
                <_>
                  9 0 5 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-7.0536029525101185e-003</threshold>
            <left_val>0.1599832028150559</left_val>
            <right_val>-0.0915948599576950</right_val></_></_>
        <_>
          <!-- tree 77 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  9 0 8 2 -1.</_>
                <_>
                  9 0 4 1 2.</_>
                <_>
                  13 1 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-5.7094299700111151e-004</threshold>
            <left_val>-0.1407632976770401</left_val>
            <right_val>0.1028741970658302</right_val></_></_>
        <_>
          <!-- tree 78 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  12 2 8 1 -1.</_>
                <_>
                  16 2 4 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.2152599412947893e-003</threshold>
            <left_val>0.1659359931945801</left_val>
            <right_val>-0.0852739885449409</right_val></_></_>
        <_>
          <!-- tree 79 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  7 1 10 6 -1.</_>
                <_>
                  7 3 10 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0280848909169436</threshold>
            <left_val>0.2702234089374542</left_val>
            <right_val>-0.0558738112449646</right_val></_></_>
        <_>
          <!-- tree 80 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  18 6 2 3 -1.</_>
                <_>
                  18 7 2 1 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.1515151020139456e-003</threshold>
            <left_val>0.0424728915095329</left_val>
            <right_val>-0.3200584948062897</right_val></_></_>
        <_>
          <!-- tree 81 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  4 12 2 2 -1.</_>
                <_>
                  4 12 1 1 2.</_>
                <_>
                  5 13 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-2.9733829433098435e-004</threshold>
            <left_val>0.1617716997861862</left_val>
            <right_val>-0.0851155892014503</right_val></_></_>
        <_>
          <!-- tree 82 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 6 6 2 -1.</_>
                <_>
                  8 6 2 2 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0166947804391384</threshold>
            <left_val>-0.4285877048969269</left_val>
            <right_val>0.0305416099727154</right_val></_></_>
        <_>
          <!-- tree 83 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  0 9 9 6 -1.</_>
                <_>
                  3 9 3 6 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.1198299005627632</threshold>
            <left_val>-0.0162772908806801</left_val>
            <right_val>0.7984678149223328</right_val></_></_>
        <_>
          <!-- tree 84 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  17 18 2 2 -1.</_>
                <_>
                  18 18 1 2 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-3.5499420482665300e-004</threshold>
            <left_val>0.1593593955039978</left_val>
            <right_val>-0.0832728818058968</right_val></_></_>
        <_>
          <!-- tree 85 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  11 2 6 16 -1.</_>
                <_>
                  13 2 2 16 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0182262696325779</threshold>
            <left_val>0.1952728033065796</left_val>
            <right_val>-0.0739398896694183</right_val></_></_>
        <_>
          <!-- tree 86 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 4 15 13 -1.</_>
                <_>
                  7 4 5 13 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-4.0238600922748446e-004</threshold>
            <left_val>0.0791018083691597</left_val>
            <right_val>-0.2080612927675247</right_val></_></_>
        <_>
          <!-- tree 87 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  16 2 3 10 -1.</_>
                <_>
                  17 2 1 10 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>4.0892060496844351e-004</threshold>
            <left_val>0.1003663018345833</left_val>
            <right_val>-0.1512821018695831</right_val></_></_>
        <_>
          <!-- tree 88 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  6 10 2 1 -1.</_>
                <_>
                  7 10 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>9.5368112670257688e-004</threshold>
            <left_val>-0.0730116665363312</left_val>
            <right_val>0.2175202071666718</right_val></_></_>
        <_>
          <!-- tree 89 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  1 1 18 16 -1.</_>
                <_>
                  10 1 9 16 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>0.4308179914951325</threshold>
            <left_val>-0.0274506993591785</left_val>
            <right_val>0.5706158280372620</right_val></_></_>
        <_>
          <!-- tree 90 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  14 4 3 15 -1.</_>
                <_>
                  15 4 1 15 3.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>5.3564831614494324e-004</threshold>
            <left_val>0.1158754006028175</left_val>
            <right_val>-0.1279056072235107</right_val></_></_>
        <_>
          <!-- tree 91 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  19 13 1 2 -1.</_>
                <_>
                  19 14 1 1 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>2.4430730263702571e-005</threshold>
            <left_val>-0.1681662946939468</left_val>
            <right_val>0.0804499834775925</right_val></_></_>
        <_>
          <!-- tree 92 -->
          <_>
            <!-- root node -->
            <feature>
              <rects>
                <_>
                  2 6 5 8 -1.</_>
                <_>
                  2 10 5 4 2.</_></rects>
              <tilted>0</tilted></feature>
            <threshold>-0.0553456507623196</threshold>
            <left_val>0.4533894956111908</left_val>
            <right_val>-0.0312227793037891</right_val></_></_></trees>
      <stage_threshold>-0.9774749279022217</stage_threshold>
      <parent>22</parent>
      <next>-1</next></_></stages></haarcascade_frontaleye>
</opencv_storage>
