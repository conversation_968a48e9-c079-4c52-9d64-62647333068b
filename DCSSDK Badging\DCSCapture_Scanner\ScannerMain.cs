using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;
using System.Data;

using DCSDEV.DCSFinisher;
using DCSDEV.DCSFinisherProperties;
using DCSDEV;

namespace DCSDEV.DCSScanner
{
	/// <summary>
	/// Summary description for ScannerMain.
	/// </summary>
	public class ScannerMain : System.Windows.Forms.Form
	{
		/// <summary>
		/// Required designer variable.
		/// </summary>

		private string m_PortrtaitPath = null;
		private string m_SignaturePath = null;
		private string m_FingerprintPath = null;
		private string m_ImageName;

		private int m_bCaptureStatus = 0;
		private DCSDEV.DCSScanner.ScannerProperties m_properties;
		private DCSDEV.ScannerIF m_scannerIF = null;

		private System.ComponentModel.Container components = null;
		private System.Windows.Forms.Button buttonAcquire;
		private System.Windows.Forms.Button buttonClose;
		private System.Windows.Forms.Button buttonAbout;
		private System.Windows.Forms.Button buttonConfigure;

		public ScannerMain(DCSDEV.ScannerIF scannerIF)
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			m_bCaptureStatus = 0;	//0= software did not finish; 1=user canceled; 2=OK
			this.m_ImageName = "_default_img.bmp";		// Application.StartupPath

			m_scannerIF = scannerIF;
			m_properties = new DCSDEV.DCSScanner.ScannerProperties(m_scannerIF);
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if (components != null) 
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ScannerMain));
            this.buttonAcquire = new System.Windows.Forms.Button();
            this.buttonClose = new System.Windows.Forms.Button();
            this.buttonAbout = new System.Windows.Forms.Button();
            this.buttonConfigure = new System.Windows.Forms.Button();
            this.SuspendLayout();
            // 
            // buttonAcquire
            // 
            this.buttonAcquire.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            resources.ApplyResources(this.buttonAcquire, "buttonAcquire");
            this.buttonAcquire.Name = "buttonAcquire";
            this.buttonAcquire.UseVisualStyleBackColor = false;
            this.buttonAcquire.Click += new System.EventHandler(this.buttonAcquire_Click);
            // 
            // buttonClose
            // 
            resources.ApplyResources(this.buttonClose, "buttonClose");
            this.buttonClose.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(255)))));
            this.buttonClose.Name = "buttonClose";
            this.buttonClose.UseVisualStyleBackColor = false;
            this.buttonClose.Click += new System.EventHandler(this.buttonClose_Click);
            // 
            // buttonAbout
            // 
            resources.ApplyResources(this.buttonAbout, "buttonAbout");
            this.buttonAbout.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(192)))), ((int)(((byte)(255)))));
            this.buttonAbout.Name = "buttonAbout";
            this.buttonAbout.UseVisualStyleBackColor = false;
            this.buttonAbout.Click += new System.EventHandler(this.buttonAbout_Click);
            // 
            // buttonConfigure
            // 
            this.buttonConfigure.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            resources.ApplyResources(this.buttonConfigure, "buttonConfigure");
            this.buttonConfigure.Name = "buttonConfigure";
            this.buttonConfigure.UseVisualStyleBackColor = false;
            this.buttonConfigure.Click += new System.EventHandler(this.buttonConfigure_Click);
            // 
            // ScannerMain
            // 
            resources.ApplyResources(this, "$this");
            this.Controls.Add(this.buttonConfigure);
            this.Controls.Add(this.buttonAbout);
            this.Controls.Add(this.buttonClose);
            this.Controls.Add(this.buttonAcquire);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ScannerMain";
            this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
            this.ResumeLayout(false);

		}
		#endregion

/********************************************************************************************
		/// <summary>
		/// The main entry point for the application.
		/// </summary>
		[STAThread]
		static void Main() 
		{
			Application.Run(new ScannerMain());
		}
*********************************************************************************************/

		#region Private Implementation Utilities
		private void GetScannerProperties()
		{
			// properties in controls
			//this.comboBoxSources.Text = m_ps.GetStringParameter("ScannerSource", this.comboBoxSources.Text);
		}

		private bool CallPhotoFinishing(string strFileNameIn, string strImageClass)
		{
			System.Drawing.Bitmap bitmapPhoto;
			bitmapPhoto = DCSDEV.DCSDatabaseIF.OpenImageFile(strFileNameIn);
			// file remains locked until bitmapPhoto is disposed.

			if (bitmapPhoto == null)
			{
				// This probably results from a corrupted image file.
				return false;
			}

			return CallPhotoFinishing(bitmapPhoto, strImageClass, Rectangle.Empty, Rectangle.Empty);
		}

		// also disposes of the image passed.
		private bool CallPhotoFinishing(Bitmap bitmapPhoto, string strImageClass, Rectangle rectInner, Rectangle rectOuter)
		{
			string strPath = null;
			bool bFaceFinding = false;
			DCSDEV.DCSDatabaseIF.ImageClass imageClass = DCSDEV.DCSDatabaseIF.StringToClass(strImageClass);
			int iSubClass = DCSDEV.DCSDatabaseIF.StringToSubClass(strImageClass);
			if (iSubClass < 0) iSubClass = 0;
			if (imageClass == DCSDEV.DCSDatabaseIF.ImageClass.Portrait)
			{
				strPath = this.m_PortrtaitPath;

				// set face finding flag if licensing allows it
				bFaceFinding = false;
				if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.DCSFaceFinder, true))
				{
					DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore(imageClass.ToString() + "Finisher");
					bFaceFinding = ps.GetBoolParameter("IfFaceFinding", false);
				}
			}
			else if (imageClass == DCSDEV.DCSDatabaseIF.ImageClass.Signature)
			{
				strPath = this.m_SignaturePath;
			}
			else if ( (imageClass == DCSDEV.DCSDatabaseIF.ImageClass.Fingerprint)
            || (imageClass == DCSDEV.DCSDatabaseIF.ImageClass.TenPrint) )
			{
				strPath = this.m_FingerprintPath;
			}
			if (iSubClass != 0) strPath = strPath + iSubClass;

			try
			{
				FinisherMain dlgFinish = new FinisherMain(imageClass);
				if (this.m_properties.m_bCropFineTune)
				{
					dlgFinish.FinisherImage = bitmapPhoto;
                    dlgFinish.ProcessFinisherImage();

					if (!bFaceFinding)
					{
						dlgFinish.CropLocation = rectInner.Location - (Size)rectOuter.Location;
						dlgFinish.CropSize = rectInner.Size;
					}

					dlgFinish.ShowDialog(this);
					// the original image from camera needs to be disposed and the file deleted
					bitmapPhoto.Dispose();
					bitmapPhoto = null;
					if (dlgFinish.IsCanceled) return false;
				}
				else
				{
					rectInner.Location = rectInner.Location - (Size)rectOuter.Location;
					Bitmap bitmap = this.CutOutImage(bitmapPhoto, rectInner);
					dlgFinish.FinisherImage = bitmap;
                    dlgFinish.ProcessFinisherImage();
				}
				// if ok the finisher's working image will become the current image
				bitmapPhoto = (System.Drawing.Bitmap)dlgFinish.FinisherImage;
				bool bRet;

				// Finisher process puts image into a temporary file and SetStoredImage puts it into the Image data storage according to FILES or OLE
				ParameterStore ps = new ParameterStore("DCSSDK_Mgt");
				string strTempFile = System.IO.Path.Combine(ps.m_strDCSInstallDirectory, DCSDatabaseIF.GetNameOfImage("_Temp",imageClass));
				if (System.IO.File.Exists(strTempFile)) System.IO.File.Delete(strTempFile);

				bRet = dlgFinish.SaveFinishedImage(strTempFile);
				
				if (strTempFile != null)
				{
					bRet = DCSDatabaseIF.SetStoredImage(strTempFile, m_ImageName, imageClass, iSubClass, false);
					System.IO.File.Delete(strTempFile);
				}
				else bRet = false;

				bitmapPhoto.Dispose();
				bitmapPhoto = null;
				return bRet;
			}
			catch(System.Exception ex)
			{
				DCSMsg.Show("ERROR in Photo Finishing.", ex);
				return false;
			}
		}

		private void CloseScanner()
		{
		}

		private bool SetAcquireSource()
		{
			return true;
		}
		
		private bool GetCapabilities()
		{
			return true;
		}

		private Rectangle InflateMargin(Rectangle rectIn)
		{
			Rectangle rect = rectIn;
			rect.Inflate(m_properties.m_OverScan, m_properties.m_OverScan);
			if (rect.X < 0)
			{
				rect.Width += rect.X;
				rect.X = 0;
			}
			if (rect.Y < 0)
			{
				rect.Height += rect.Y;
				rect.Y = 0;
			}
			return rect;
		}

		// syh NOTE: overlaps with DCSImageProcessing.Trim
		private Bitmap CutOutImage(Bitmap bitmapIn, Rectangle bounds)
		{
			Rectangle boundsOut = Rectangle.Empty;
			boundsOut.Size = bounds.Size;
			Bitmap bitmapOut = new Bitmap(bounds.Width, bounds.Height);
			Graphics gr = Graphics.FromImage(bitmapOut);
			gr.Clear((Color.Gray));
			gr.DrawImage(bitmapIn, boundsOut, bounds, System.Drawing.GraphicsUnit.Pixel);
			return bitmapOut;
		}

		public DialogResult DoAcquire()
		{
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.ScannerMgt, true)) return DialogResult.Cancel;

			m_bCaptureStatus = 0;	// 0= software did not finish; 1=user canceled; 2=OK

			//DCSMsg.Show("Acquire all images from scanner");

			if (!m_scannerIF.m_bScannerIsInitialized)
			{
				m_scannerIF.InitializeScanner();
			}
			if (!m_scannerIF.m_bScannerIsInitialized) return DialogResult.Cancel;

			Bitmap bitmapScan;
			Bitmap bitmap;
			Rectangle rectScan = Rectangle.Empty;
			Rectangle rectInner;
			Rectangle rectOuter;
			int iScanResolution = 100;

			bool  bDoPortrait = (m_PortrtaitPath != null);
			bool  bDoSignature = (m_SignaturePath != null);
			bool  bDoFingerprint = (m_FingerprintPath != null);
			bool bNotYetConfigured = false;

			// compute how many images are captured
			int iCaptured = 0;
			if (m_properties.m_bSinglePassScan)
			{
				// find size of rectangle to scan
				bool bFirst = true;
				foreach (DCSDEV.DCSScanner.DCSScannerObject obj in this.m_properties.m_Objects)
				{
					if (!bDoPortrait && DCSDEV.DCSDatabaseIF.StringToClass(obj.m_strImageClass) == DCSDEV.DCSDatabaseIF.ImageClass.Portrait)  continue;
					if (!bDoSignature && DCSDEV.DCSDatabaseIF.StringToClass(obj.m_strImageClass) == DCSDEV.DCSDatabaseIF.ImageClass.Signature)  continue;
					if (!bDoFingerprint && DCSDEV.DCSDatabaseIF.StringToClass(obj.m_strImageClass) == DCSDEV.DCSDatabaseIF.ImageClass.Fingerprint)  continue;
                    if (!bDoFingerprint && DCSDEV.DCSDatabaseIF.StringToClass(obj.m_strImageClass) == DCSDEV.DCSDatabaseIF.ImageClass.TenPrint) continue;
 
					if (obj.m_bounds ==  Rectangle.Empty)
					{
						bNotYetConfigured = true;
						continue;
					}
					if (bFirst) rectScan = obj.m_bounds;
					else rectScan = Rectangle.Union(rectScan, obj.m_bounds);
					bFirst = false;
					int iObjRes = (obj.m_sizePixels.Width * 100) / obj.m_bounds.Width;
					iScanResolution = Math.Max(iObjRes, iScanResolution);
				}
				if (bNotYetConfigured)
				{
					DCSMsg.Show("Not all images are positioned for scanning.");
					return DialogResult.Cancel;
				}
				if (rectScan == Rectangle.Empty)
				{
					DCSMsg.Show("There are no images that qualify to be scanned.");
					return DialogResult.Cancel;
				}
				// add margin all around
				rectScan = this.InflateMargin(rectScan);

				// scan at max resolution - adjusted to nearest multiple of 100 between 100 and 600
				int resolution;	// handle resolution
				resolution = ((iScanResolution + 85)/100) * 100;	// round up unless within 15%
				if (resolution < 100) resolution = 100;
				else if (resolution > 600) resolution = 600;

				bitmapScan = (Bitmap)m_scannerIF.ScanRect(rectScan, resolution);
				if (bitmapScan == null) return DialogResult.Cancel;

				// pull out each image
				foreach (DCSDEV.DCSScanner.DCSScannerObject obj in this.m_properties.m_Objects)
				{
					if (!bDoPortrait && DCSDEV.DCSDatabaseIF.StringToClass(obj.m_strImageClass) == DCSDEV.DCSDatabaseIF.ImageClass.Portrait)  continue;
					if (!bDoSignature && DCSDEV.DCSDatabaseIF.StringToClass(obj.m_strImageClass) == DCSDEV.DCSDatabaseIF.ImageClass.Signature)  continue;
					if (!bDoFingerprint && DCSDEV.DCSDatabaseIF.StringToClass(obj.m_strImageClass) == DCSDEV.DCSDatabaseIF.ImageClass.Fingerprint)  continue;
                    if (!bDoFingerprint && DCSDEV.DCSDatabaseIF.StringToClass(obj.m_strImageClass) == DCSDEV.DCSDatabaseIF.ImageClass.TenPrint) continue;

					rectInner = obj.m_bounds;
					rectInner.Offset(-rectScan.X, -rectScan.Y);

					rectOuter = this.InflateMargin(rectInner);

					// apply scale to object rectangles - to convert to pixels at scale
					rectInner = DCSMath.TimesDouble(rectInner, (double)resolution / 100.0);
					rectOuter = DCSMath.TimesDouble(rectOuter, (double)resolution / 100.0);
					
					bitmap = this.CutOutImage(bitmapScan, rectOuter);

					// apply rotation
					rectInner.Location = rectInner.Location - (Size)rectOuter.Location;
					rectOuter.Location = new Point(0,0);
					if (obj.m_rotateFlip == 1)
					{
						bitmap.RotateFlip(System.Drawing.RotateFlipType.Rotate270FlipNone);
						DCSMath.SwapWandH(ref rectInner);
						DCSMath.SwapWandH(ref rectOuter);
						int y = rectInner.Y;
						rectInner.Y = rectOuter.Right - rectInner.Right;
						rectInner.X = y;
					}
					else if (obj.m_rotateFlip == 2)
					{
						bitmap.RotateFlip(System.Drawing.RotateFlipType.Rotate180FlipNone);
						int x = rectOuter.Right - rectInner.Right;
						rectInner.Y = rectOuter.Bottom - rectInner.Bottom;
						rectInner.X = x;
					}
					else if (obj.m_rotateFlip == 3)
					{
						bitmap.RotateFlip(System.Drawing.RotateFlipType.Rotate90FlipNone);
						DCSMath.SwapWandH(ref rectInner);
						DCSMath.SwapWandH(ref rectOuter);
						int x = rectInner.X;
						rectInner.X = rectOuter.Bottom - rectInner.Bottom;
						rectInner.Y = x;
					}
					
					// syh - save image in temp file as a debugging aide
					//string strImageID = "Temp_" + obj.m_strImageClass + ".Jpg";
					//bmTemp.Save(strImageID, System.Drawing.Imaging.ImageFormat.Jpeg);

					bool bRet = CallPhotoFinishing(bitmap, obj.m_strImageClass, rectInner, rectOuter);
					// syh must get capture status here.
					if (bRet) iCaptured++;
				}
				bitmapScan.Dispose();
				bitmapScan = null;
			}
			else
			{
				bool bSomethingDone = false;
				foreach (DCSDEV.DCSScanner.DCSScannerObject obj in this.m_properties.m_Objects)
				{
					if (!bDoPortrait && DCSDEV.DCSDatabaseIF.StringToClass(obj.m_strImageClass) == DCSDEV.DCSDatabaseIF.ImageClass.Portrait)  continue;
					if (!bDoSignature && DCSDEV.DCSDatabaseIF.StringToClass(obj.m_strImageClass) == DCSDEV.DCSDatabaseIF.ImageClass.Signature)  continue;
					if (!bDoFingerprint && DCSDEV.DCSDatabaseIF.StringToClass(obj.m_strImageClass) == DCSDEV.DCSDatabaseIF.ImageClass.Fingerprint)  continue;
                    if (!bDoFingerprint && DCSDEV.DCSDatabaseIF.StringToClass(obj.m_strImageClass) == DCSDEV.DCSDatabaseIF.ImageClass.TenPrint) continue;
                    bSomethingDone = true;

					if (obj.m_bounds ==  Rectangle.Empty)
					{
						bNotYetConfigured = true;
						continue;
					}

					// scan at resolution - adjusted to nearest multiple of 100 between 100 and 600
					int iObjRes = (obj.m_sizePixels.Width * 100) / obj.m_bounds.Width;
					int resolution = ((iObjRes + 85)/100) * 100;	// round up unless within 15%
					if (resolution < 100) resolution = 100;
					else if (resolution > 600) resolution = 600;

					rectInner = obj.m_bounds;
					rectOuter = this.InflateMargin(rectInner);
					bitmapScan = (Bitmap)m_scannerIF.ScanRect(rectOuter, resolution);
					if (bitmapScan == null)
					{
						return DialogResult.Cancel;
					}

					// syh - save image in temp file as a debuggoing aide
					//string strImageID = "Temp_" + obj.m_strImageClass + ".Jpg";
					//bitmapScan.Save(strImageID, System.Drawing.Imaging.ImageFormat.Jpeg);

					// apply scale to object rectangles - to convert to pixels at scale
					rectInner = DCSMath.TimesDouble(rectInner, (double)resolution / 100.0);
					rectOuter = DCSMath.TimesDouble(rectOuter, (double)resolution / 100.0);

					// apply rotation
					rectInner.Location = rectInner.Location - (Size)rectOuter.Location;
					rectOuter.Location = new Point(0,0);
					if (obj.m_rotateFlip == 1)
					{
						bitmapScan.RotateFlip(System.Drawing.RotateFlipType.Rotate270FlipNone);
						DCSMath.SwapWandH(ref rectInner);
						DCSMath.SwapWandH(ref rectOuter);
						int y = rectInner.Y;
						rectInner.Y = rectOuter.Right - rectInner.Right;
						rectInner.X = y;
					}
					else if (obj.m_rotateFlip == 2)
					{
						bitmapScan.RotateFlip(System.Drawing.RotateFlipType.Rotate180FlipNone);
						int x = rectOuter.Right - rectInner.Right;
						rectInner.Y = rectOuter.Bottom - rectInner.Bottom;
						rectInner.X = x;
					}
					else if (obj.m_rotateFlip == 3)
					{
						bitmapScan.RotateFlip(System.Drawing.RotateFlipType.Rotate90FlipNone);
						DCSMath.SwapWandH(ref rectInner);
						DCSMath.SwapWandH(ref rectOuter);
						int x = rectInner.X;
						rectInner.X = rectOuter.Bottom - rectInner.Bottom;
						rectInner.Y = x;
					}

					bool bRet = CallPhotoFinishing(bitmapScan, obj.m_strImageClass, rectInner, rectOuter);
					// syh must get capture status here.
					if (bRet) iCaptured++;
				}
				if (!bSomethingDone)
				{
					DCSMsg.Show("There are no images that qualify to be scanned.");
					return DialogResult.Cancel;
				}
				if (bNotYetConfigured)
				{
					DCSMsg.Show("Not all images are positioned for scanning.");
					return DialogResult.Cancel;
				}
			}
			if (iCaptured > 0) m_bCaptureStatus = 2;	//0= software did not finish; 1=user canceled; 2=OK
			else m_bCaptureStatus = 1;
			return DialogResult.OK;
		}
		
		#endregion

		#region Public Interface

		public string PortrtaitPath
		{
			set { m_PortrtaitPath = value; }
		}
		public string SignaturePath
		{
			set { m_SignaturePath = value; }
		}
		public string FingerprintPath
		{
			set { m_FingerprintPath = value; }
		}
		public string ImageName
		{
			set { m_ImageName = value; }
		}

		public int CaptureStatus
		{
			get { return m_bCaptureStatus; }
		}
		#endregion

		#region Message Handlers
		private void buttonAbout_Click(object sender, System.EventArgs e)
		{
			DCSDEV.DCSScanner.AboutScanner aboutDlg = new DCSDEV.DCSScanner.AboutScanner();
			aboutDlg.ShowDialog(this);
		}
		private void buttonAcquire_Click(object sender, System.EventArgs e)
		{
			// disable buttons
			this.buttonAcquire.Enabled = false;	

			DoAcquire();

			// restore buttons
			this.buttonAcquire.Enabled = true;	
		}

		private void buttonClose_Click(object sender, System.EventArgs e)
		{
			CloseScanner();
			m_bCaptureStatus = 1;	//0= software did not finish; 1=user canceled; 2=OK
			this.Close();
		}

		#endregion

		private void buttonConfigure_Click(object sender, System.EventArgs e)
		{
			this.m_properties.ShowDialog(this);
		}
	}
}
