using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace DCSDEV.DCSDesigner
{
	public partial class DCSFormulaAddText : Form
	{
		public DCSFormulaAddText()
		{
			InitializeComponent();
		}

		private void buttonCancel_Click(object sender, EventArgs e)
		{
			this.Close();
		}

		private void buttonOK_Click(object sender, EventArgs e)
		{
			if (this.textBoxTextToAppend.Text == null || this.textBoxTextToAppend.Text == "")
			{
				DialogResult = DialogResult.Cancel;
				DCSDEV.DCSMsg.Show("A text string must be entered.");
				return;
			}
			DialogResult = DialogResult.OK;
			this.Close();
		}

		public string TextToAppend
		{
			get { return this.textBoxTextToAppend.Text; }
			set { this.textBoxTextToAppend.Text = value; }
		}
	}
}