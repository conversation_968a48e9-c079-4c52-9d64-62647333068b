using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Runtime.InteropServices;

namespace DCSDEV.DDEServer
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();

            // NOTE: requires a reference to DCSDDEServer must be a file reference 
            // rather than a project reference due to a documented MS bug when the 
            // reference is to an EXE project.
            DCSDEV.DDEServer.DDEServer server = new DDEServer();
            server.ShowDialog();
            this.timer1.Interval = 100;
            timer1.Start();
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}