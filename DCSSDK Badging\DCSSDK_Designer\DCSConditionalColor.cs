using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace DCSDEV.DCSDesigner
{
	public partial class DCSConditionalColor : Form
	{
		ArrayList m_AllDBFieldNames;

		public DCSConditionalColor()
		{
			InitializeComponent();
			m_AllDBFieldNames = new ArrayList();
		}

		private void buttonAccept_Click(object sender, EventArgs e)
		{
			this.Close();
		}
		private void buttonChooseColor1_Click(object sender, EventArgs e)
		{
			this.colorDialog1.Color = this.pictureBoxColor1.BackColor;
			this.colorDialog1.ShowDialog(this);
			this.pictureBoxColor1.BackColor = this.colorDialog1.Color;
		}
		private void buttonColor2_Click(object sender, EventArgs e)
		{
			this.colorDialog1.Color = this.pictureBoxColor2.BackColor;
			this.colorDialog1.ShowDialog(this);
			this.pictureBoxColor2.BackColor = this.colorDialog1.Color;
		}

		private void buttonColorCondition1_Click(object sender, EventArgs e)
		{
			string strCondition = this.textBox1.Text;
			DCSDEV.DCSDesigner.DCSFormulaDesigner.CallFormulaBuilder(m_AllDBFieldNames, ref strCondition, DCSFormulaDesigner.FormulaModeType.SQL_IF);
			if (strCondition != null) this.textBox1.Text = strCondition;
		}
		private void buttonColorCondition2_Click(object sender, EventArgs e)
		{
			string strCondition = this.textBox2.Text;
			DCSDEV.DCSDesigner.DCSFormulaDesigner.CallFormulaBuilder(m_AllDBFieldNames, ref strCondition, DCSFormulaDesigner.FormulaModeType.SQL_IF);
			if (strCondition != null) this.textBox2.Text = strCondition;
		}

		public Color Color1
		{
			get { return this.pictureBoxColor1.BackColor; }
			set { this.pictureBoxColor1.BackColor = value; }
		}
		public Color Color2
		{
			get { return this.pictureBoxColor2.BackColor; }
			set { this.pictureBoxColor2.BackColor = value; }
		}
		public Color Color3
		{
			get { return this.pictureBoxColor3.BackColor; }
			set { this.pictureBoxColor3.BackColor = value; }
		}
		public string ColorCondition1
		{
			get { return this.textBox1.Text; }
			set { this.textBox1.Text = value; }
		}
		public string ColorCondition2
		{
			get { return this.textBox2.Text; }
			set { this.textBox2.Text = value; }
		}
		public ArrayList AllDBFieldNames
		{
			set
			{
				foreach (string str in value) m_AllDBFieldNames.Add(str);
			}
		}

		private void buttonColor3_Click(object sender, EventArgs e)
		{
			this.colorDialog1.Color = this.pictureBoxColor3.BackColor;
			this.colorDialog1.ShowDialog(this);
			this.pictureBoxColor3.BackColor = this.colorDialog1.Color;
		}
	}
}