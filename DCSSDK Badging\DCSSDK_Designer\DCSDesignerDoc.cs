using System;
using System.IO ;
using System.Collections;
using System.Drawing;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Formatters.Binary ;
using System.Windows.Forms ;

using DCSDEV;
using DCSDEV.DCSDesign;

namespace DCSDEV.DCSDesigner
{
	/// <summary>
	///    Summary description for Doc.
	/// </summary>
    internal class DCSDesignerDoc : DCSDEV.DCSDesign.DCSDesign
	{
		DCSDesignerMain m_mainWin;
		public DCSDesignerView m_view = null;
		public UndoEventClass m_UndoEventClass;
		public UndoEventClass m_RedoEventClass;
	
		public DCSDesignerDoc(DCSDesignerMain mainWin, string strDesignName)
		{
			m_isDirty = false;
			m_mainWin = mainWin;
			if (strDesignName != null && strDesignName.Length == 0) strDesignName = null;
			m_strDesignName = strDesignName;
			string strViewName;

			m_RedoEventClass = new UndoEventClass(this.m_designSides, "Redo");
			m_UndoEventClass = new UndoEventClass(this.m_designSides, "Undo");
			m_RedoEventClass.DefineOtherUndoEventClass(m_UndoEventClass);
			m_UndoEventClass.DefineOtherUndoEventClass(m_RedoEventClass);
			
			if (strDesignName != null)
			{
				bool bRet = this.OpenDocument(strDesignName);
				if (!bRet) return;

				strViewName = strDesignName;
			}
			else
			{
				strViewName = this.FindUnusedName(); 	// find unused name

				// new doc retains all default settings
				this.Bounds.Size = new Size(300, 200);
			}
		
			//Create a view (Form) for this document
			m_view = new DCSDesignerView(this, mainWin);
			m_view.m_strViewName = strViewName;
			m_view.ViewScale = 1.5;
			this.m_view.ClientSize = DCSMath.TimesDouble(this.Bounds.Size, m_view.ViewScale);
			this.m_view.SetPictureBoxSize(this.m_view.ClientSize);
			this.m_view.Show();
		}

		// find unused name
		private string FindUnusedName()
		{
			int i = 1;
			bool bNumberIsUsed = true;
			while(bNumberIsUsed)
			{
				if (m_mainWin.MdiChildren.Length > 0)
				{
					bNumberIsUsed = false;
					foreach (DCSDesignerView view in m_mainWin.MdiChildren)
					{
						if (view.m_strViewName == "DCSDesign" + i)
						{
							i++;
							bNumberIsUsed = true;
							break;
						}
					}
				}
				else bNumberIsUsed = false;
				// unused by any view - check the stored badge names
				if (DCSDEV.DCSDesignDataAccess.ExpandDesignName("DCSDesign" + i, true) != null)
				{
					i++;
					bNumberIsUsed = true;
				}
			}
				
			string strNewName = "DCSDesign" + i;
			return strNewName;
		}

		//Save the document
		public void SaveDocument(string DesignName)
		{
			bool bRet;
			try 
			{
				bRet = this.WriteBadgeDesign(DesignName);
				if (bRet)
				{
					m_isDirty = false;
					//DCSDEV.DCSMsg.Show(String.Format("Design is saved to '{0}'.", DesignName));
				}
				else
				{
					DCSDEV.DCSMsg.Show("ERROR: WriteBadgeDesign() failed.");
				}
			}
			catch(Exception ex)
			{
				DCSDEV.DCSMsg.Show(ex);
			}
		}

		//Open the document
		public bool OpenDocument(string strDesignName)
		{
			bool bRet;
			try 
			{
				bRet = this.ReadBadgeDesign(strDesignName);

				// merge in sample images
				this.MergeWithSampleImages();

				// assign name
				this.m_strDesignName = strDesignName;
			}	
			catch(Exception ex)
			{
				DCSDEV.DCSMsg.Show(ex);
				return false;
			}
			return bRet;
		}	

		internal Size BadgeSize
		{
			get
			{
				return this.Bounds.Size;
			}
			set
			{
				this.Bounds.Size = value;
			}
		}
	}

	public enum UndoType {Insert, Delete, PreEdit, Edit};
    internal class UndoEventClass
	{
		string m_strUndoRedo = "Undo";
		ArrayList m_UndoEventsArray;
		ArrayList m_arrayDesignSides;
		DCSDesignObject m_designObjectBackup;
		int m_indexBackup;
		int m_LastSideUndone = 0;
		UndoEventClass m_OtherEventClass;

		public UndoEventClass(ArrayList arrayDesignSides, string strUndoRedo)
		{
			m_strUndoRedo = strUndoRedo;
			m_arrayDesignSides = arrayDesignSides;
			m_UndoEventsArray = new ArrayList();
		}

		public bool Add(
			int side, 
			UndoType eventType, 
			DCSDesignObject designObjectCurrent,
			bool bFirst)
		{
			return AddEx(side, eventType, designObjectCurrent, bFirst, true);	
		}
		// call this before the change is made
		private bool AddEx(
			int side, 
			UndoType eventType, 
			DCSDesignObject designObjectCurrent,
			bool bFirst,
			bool bPrimary)
		{
			DCSDesignObject designObjectClone;
			UndoEvent undoEvent;
			int index;
			ArrayList arrayDesignObjects = null;
			arrayDesignObjects = ((DCSDesignSide)m_arrayDesignSides[side]).m_DCSDesignObjects;
			if (bPrimary) m_OtherEventClass.m_UndoEventsArray.Clear();
			switch(eventType)
			{
				case UndoType.Insert:
					// must call after insert
					//undoEvent = new UndoEvent(side, eventType, designObjectCurrent, null);
					index = arrayDesignObjects.IndexOf(designObjectCurrent);
					undoEvent = new UndoEvent(side, eventType, designObjectCurrent, null, index, bFirst);
					break;
				case UndoType.Delete:
					// must call before deleting
					index = arrayDesignObjects.IndexOf(designObjectCurrent);
					designObjectClone = designObjectCurrent.CloneObject(false);
					undoEvent = new UndoEvent(side, eventType, designObjectClone, null, index, bFirst);
					break;
				case UndoType.PreEdit:
					// must be called before change
					m_indexBackup = arrayDesignObjects.IndexOf(designObjectCurrent);
					m_designObjectBackup = designObjectCurrent.CloneObject(false);
					return true;
				case UndoType.Edit:
					// must be called after change
					index = arrayDesignObjects.IndexOf(designObjectCurrent);
					if (m_indexBackup != index || m_designObjectBackup == null) 
						return false;
					undoEvent = new UndoEvent(side, eventType, designObjectCurrent, m_designObjectBackup, index, bFirst);
					break;
				default:
					return false;
			}
			m_UndoEventsArray.Add(undoEvent);
			if (undoEvent.m_index < 0)
			{
				undoEvent.m_index = 0;		// syh test
			}
			return true;
		}
		public void DefineOtherUndoEventClass(UndoEventClass redoEvents)
		{
			m_OtherEventClass = redoEvents;
		}
		public int GetUndoneSide()
		{
			return m_LastSideUndone;
		}
		public bool Undo(UndoEventClass redoEvents)
		{
			bool bFirst = true;
			int count;
UNDO_LOOP:
			count = m_UndoEventsArray.Count;
			if (count <= 0) return false;
			UndoEvent undoEvent = (UndoEvent)m_UndoEventsArray[count-1];
			m_UndoEventsArray.RemoveAt(count-1);

			ArrayList arrayDesignObjects = ((DCSDesignSide)m_arrayDesignSides[undoEvent.m_iSide]).m_DCSDesignObjects;
			switch(undoEvent.m_eEventType)
			{
				case UndoType.Insert:
					if (arrayDesignObjects.IndexOf(undoEvent.m_designObjectCurrent) != undoEvent.m_index)
					{
						undoEvent.m_designObjectCurrent = (DCSDesignObject)arrayDesignObjects[undoEvent.m_index];
					}
					redoEvents.AddEx(undoEvent.m_iSide, UndoType.Delete, undoEvent.m_designObjectCurrent, bFirst, false);
					bFirst = false;
					arrayDesignObjects.RemoveAt(undoEvent.m_index);
					undoEvent.m_designObjectCurrent.Dispose();
					break;
				case UndoType.Delete:
					if (undoEvent.m_index < 0)
					{
						undoEvent.m_index = 0;		// syh test
					}
					arrayDesignObjects.Insert(undoEvent.m_index, undoEvent.m_designObjectCurrent);
					redoEvents.AddEx(undoEvent.m_iSide, UndoType.Insert, undoEvent.m_designObjectCurrent, bFirst, false);
					bFirst = false;
					break;
				case UndoType.Edit:
					//redoEvents.AddEx(undoEvent.m_iSide, UndoType.Delete, undoEvent.m_designObjectCurrent, false, false);
					//DCSDesignObject fieldRedoBackup
					redoEvents.m_designObjectBackup = ((DCSDesignObject)arrayDesignObjects[undoEvent.m_index]).CloneObject(false);
					redoEvents.m_indexBackup = undoEvent.m_index;
					arrayDesignObjects.RemoveAt(undoEvent.m_index);
					undoEvent.m_designObjectCurrent.Dispose();
					arrayDesignObjects.Insert(undoEvent.m_index, undoEvent.m_designObjectBackup);
					redoEvents.AddEx(undoEvent.m_iSide, UndoType.Edit, undoEvent.m_designObjectBackup, bFirst, false);
					bFirst = false;
					break;
				default:
					break;
			}
			m_LastSideUndone = undoEvent.m_iSide;

			//see if next undo is part of same group
			if (!undoEvent.m_bFirst) goto UNDO_LOOP;
			else return true;
		}
		public int Count
		{
			get { return this.m_UndoEventsArray.Count; }
		}
	}
    internal class UndoEvent
	{
		public int		m_iSide;
		public UndoType m_eEventType;
		public DCSDesignObject	m_designObjectCurrent;
		public DCSDesignObject	m_designObjectBackup;  // old designObject or designObject after delete
		public int		m_index;		// index of delete or insert
		public bool	m_bFirst;

		public UndoEvent(
			int side, 
			UndoType eventType, 
			DCSDesignObject designObjectCurrent, 
			DCSDesignObject designObjectBackUp,
			int index,
			bool bFirst)
		{
			m_iSide = side;
			m_eEventType = eventType;
			m_designObjectCurrent = designObjectCurrent;
			m_designObjectBackup = designObjectBackUp;
			m_index = index;
			m_bFirst = bFirst;
		}
	}
}
