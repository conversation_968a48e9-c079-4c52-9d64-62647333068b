namespace DCSDEV.DCSDesigner
{
	partial class DCSFormulaAddText
	{
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		/// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
		protected override void Dispose(bool disposing)
		{
			if (disposing && (components != null))
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		#region Windows Form Designer generated code

		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			this.textBoxTextToAppend = new System.Windows.Forms.TextBox();
			this.label2 = new System.Windows.Forms.Label();
			this.buttonCancel = new System.Windows.Forms.Button();
			this.buttonOKay = new System.Windows.Forms.Button();
			this.SuspendLayout();
			// 
			// textBoxTextToAppend
			// 
			this.textBoxTextToAppend.Location = new System.Drawing.Point(84, 74);
			this.textBoxTextToAppend.Name = "textBoxTextToAppend";
			this.textBoxTextToAppend.Size = new System.Drawing.Size(128, 20);
			this.textBoxTextToAppend.TabIndex = 9;
			// 
			// label2
			// 
			this.label2.ImeMode = System.Windows.Forms.ImeMode.NoControl;
			this.label2.Location = new System.Drawing.Point(84, 24);
			this.label2.Name = "label2";
			this.label2.Size = new System.Drawing.Size(128, 16);
			this.label2.TabIndex = 10;
			this.label2.Text = "Text to add:";
			this.label2.TextAlign = System.Drawing.ContentAlignment.TopCenter;
			// 
			// buttonCancel
			// 
			this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.buttonCancel.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonCancel.ImeMode = System.Windows.Forms.ImeMode.NoControl;
			this.buttonCancel.Location = new System.Drawing.Point(168, 202);
			this.buttonCancel.Name = "buttonCancel";
			this.buttonCancel.Size = new System.Drawing.Size(96, 24);
			this.buttonCancel.TabIndex = 12;
			this.buttonCancel.Text = "&Cancel";
			this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
			// 
			// buttonOKay
			// 
			this.buttonOKay.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonOKay.ImeMode = System.Windows.Forms.ImeMode.NoControl;
			this.buttonOKay.Location = new System.Drawing.Point(40, 202);
			this.buttonOKay.Name = "buttonOKay";
			this.buttonOKay.Size = new System.Drawing.Size(96, 24);
			this.buttonOKay.TabIndex = 11;
			this.buttonOKay.Text = "&OK";
			this.buttonOKay.Click += new System.EventHandler(this.buttonOK_Click);
			// 
			// DCSFormulaAddText
			// 
			this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
			this.CancelButton = this.buttonCancel;
			this.ClientSize = new System.Drawing.Size(292, 266);
			this.Controls.Add(this.buttonCancel);
			this.Controls.Add(this.buttonOKay);
			this.Controls.Add(this.textBoxTextToAppend);
			this.Controls.Add(this.label2);
			this.MaximizeBox = false;
			this.MinimizeBox = false;
			this.Name = "DCSFormulaAddText";
			this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
			this.Text = "DCSFormulaAddText";
			this.ResumeLayout(false);
			this.PerformLayout();

		}

		#endregion

		private System.Windows.Forms.TextBox textBoxTextToAppend;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.Button buttonCancel;
		private System.Windows.Forms.Button buttonOKay;
	}
}