﻿  jcapimin.cpp
  jcapistd.cpp
  jccoefct.cpp
  jccolor.cpp
  jcdctmgr.cpp
  jchuff.cpp
  jcinit.cpp
  jcmainct.cpp
  jcmarker.cpp
  jcmaster.cpp
  jcomapi.cpp
  jcparam.cpp
  jcphuff.cpp
  jcprepct.cpp
  jcsample.cpp
  jctrans.cpp
  jdapimin.cpp
  jdapistd.cpp
  jdatadst.cpp
  jdatasrc.cpp
  Generating Code...
  Compiling...
  jdcoefct.cpp
  jdcolor.cpp
  jddctmgr.cpp
  jdhuff.cpp
  jdinput.cpp
  jdmainct.cpp
  jdmarker.cpp
  jdmaster.cpp
  jdmerge.cpp
  jdphuff.cpp
  jdpostct.cpp
  jdsample.cpp
  jdtrans.cpp
  jerror.cpp
  jfdctflt.cpp
D:\repos_D\SDS Collection\DCSSDK Capture\Canon_JpegLib\jerror.cpp(195,5): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
D:\repos_D\SDS Collection\DCSSDK Capture\Canon_JpegLib\jerror.cpp(197,5): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  jfdctfst.cpp
  jfdctint.cpp
  jidctflt.cpp
  jidctfst.cpp
  jidctint.cpp
  Generating Code...
  Compiling...
  jidctred.cpp
  jmemmgr.cpp
D:\repos_D\SDS Collection\DCSSDK Capture\Canon_JpegLib\jmemmgr.cpp(1108,19): warning C4996: 'getenv': This function or variable may be unsafe. Consider using _dupenv_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
D:\repos_D\SDS Collection\DCSSDK Capture\Canon_JpegLib\jmemmgr.cpp(1111,11): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  jmemnobs.cpp
  jquant1.cpp
  jquant2.cpp
  jutils.cpp
  Generating Code...
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v170\Microsoft.CppBuild.targets(1564,5): warning MSB8012: TargetPath(D:\repos_D\SDS Collection\DCSSDK Capture\Canon_JpegLib\Debug\Canon_JpegLib.lib) does not match the Library's OutputFile property value (D:\repos_D\SDS Collection\DCSSDK Capture\Canon_JpegLib\Debug\JpegLib.lib). This may cause your project to build incorrectly. To correct this, please make sure that $(OutDir), $(TargetName) and $(TargetExt) property values match the value specified in %(Lib.OutputFile).
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Microsoft\VC\v170\Microsoft.CppBuild.targets(1566,5): warning MSB8012: TargetName(Canon_JpegLib) does not match the Library's OutputFile property value (JpegLib). This may cause your project to build incorrectly. To correct this, please make sure that $(OutDir), $(TargetName) and $(TargetExt) property values match the value specified in %(Lib.OutputFile).
  JpegLib.vcxproj -> D:\repos_D\SDS Collection\DCSSDK Capture\Canon_JpegLib\Debug\Canon_JpegLib.lib
