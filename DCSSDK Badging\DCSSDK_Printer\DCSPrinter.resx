<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonStart.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonStart.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="buttonStart.Location" type="System.Drawing.Point, System.Drawing">
    <value>303, 108</value>
  </data>
  <data name="buttonStart.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 24</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonStart.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="buttonStart.Text" xml:space="preserve">
    <value>&amp;Start</value>
  </data>
  <data name="&gt;&gt;buttonStart.Name" xml:space="preserve">
    <value>buttonStart</value>
  </data>
  <data name="&gt;&gt;buttonStart.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonStart.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonStart.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="buttonPause.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonPause.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonPause.Location" type="System.Drawing.Point, System.Drawing">
    <value>199, 140</value>
  </data>
  <data name="buttonPause.Size" type="System.Drawing.Size, System.Drawing">
    <value>192, 24</value>
  </data>
  <data name="buttonPause.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="buttonPause.Text" xml:space="preserve">
    <value>Pau&amp;se</value>
  </data>
  <data name="&gt;&gt;buttonPause.Name" xml:space="preserve">
    <value>buttonPause</value>
  </data>
  <data name="&gt;&gt;buttonPause.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPause.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonPause.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="buttonProperties.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonProperties.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonProperties.Location" type="System.Drawing.Point, System.Drawing">
    <value>199, 48</value>
  </data>
  <data name="buttonProperties.Size" type="System.Drawing.Size, System.Drawing">
    <value>192, 24</value>
  </data>
  <data name="buttonProperties.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="buttonProperties.Text" xml:space="preserve">
    <value>Document Print &amp;Properties</value>
  </data>
  <data name="&gt;&gt;buttonProperties.Name" xml:space="preserve">
    <value>buttonProperties</value>
  </data>
  <data name="&gt;&gt;buttonProperties.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonProperties.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonProperties.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="tbPrintMode.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 12pt, style=Bold</value>
  </data>
  <data name="tbPrintMode.Location" type="System.Drawing.Point, System.Drawing">
    <value>199, 17</value>
  </data>
  <data name="tbPrintMode.Size" type="System.Drawing.Size, System.Drawing">
    <value>204, 19</value>
  </data>
  <data name="tbPrintMode.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="tbPrintMode.Text" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="&gt;&gt;tbPrintMode.Name" xml:space="preserve">
    <value>tbPrintMode</value>
  </data>
  <data name="&gt;&gt;tbPrintMode.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbPrintMode.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbPrintMode.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="tbQueueLength.Location" type="System.Drawing.Point, System.Drawing">
    <value>98, 100</value>
  </data>
  <data name="tbQueueLength.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 13</value>
  </data>
  <data name="tbQueueLength.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="tbQueueLength.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;tbQueueLength.Name" xml:space="preserve">
    <value>tbQueueLength</value>
  </data>
  <data name="&gt;&gt;tbQueueLength.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbQueueLength.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbQueueLength.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="label3.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 98</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>66, 13</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="label3.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <metadata name="timer1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="buttonRefreshQ.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonRefreshQ.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonRefreshQ.Location" type="System.Drawing.Point, System.Drawing">
    <value>199, 78</value>
  </data>
  <data name="buttonRefreshQ.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 24</value>
  </data>
  <data name="buttonRefreshQ.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="buttonRefreshQ.Text" xml:space="preserve">
    <value>&amp;Refresh queue</value>
  </data>
  <data name="&gt;&gt;buttonRefreshQ.Name" xml:space="preserve">
    <value>buttonRefreshQ</value>
  </data>
  <data name="&gt;&gt;buttonRefreshQ.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonRefreshQ.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonRefreshQ.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="buttonPrintNow.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonPrintNow.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonPrintNow.Location" type="System.Drawing.Point, System.Drawing">
    <value>303, 78</value>
  </data>
  <data name="buttonPrintNow.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 24</value>
  </data>
  <data name="buttonPrintNow.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="buttonPrintNow.Text" xml:space="preserve">
    <value>Print &amp;now</value>
  </data>
  <data name="&gt;&gt;buttonPrintNow.Name" xml:space="preserve">
    <value>buttonPrintNow</value>
  </data>
  <data name="&gt;&gt;buttonPrintNow.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPrintNow.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonPrintNow.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="tbPerSheet.Location" type="System.Drawing.Point, System.Drawing">
    <value>153, 74</value>
  </data>
  <data name="tbPerSheet.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 13</value>
  </data>
  <data name="tbPerSheet.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="tbPerSheet.Text" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;tbPerSheet.Name" xml:space="preserve">
    <value>tbPerSheet</value>
  </data>
  <data name="&gt;&gt;tbPerSheet.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbPerSheet.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbPerSheet.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="labelPerSheet.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelPerSheet.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 74</value>
  </data>
  <data name="labelPerSheet.Size" type="System.Drawing.Size, System.Drawing">
    <value>19, 13</value>
  </data>
  <data name="labelPerSheet.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="labelPerSheet.Text" xml:space="preserve">
    <value>of</value>
  </data>
  <data name="labelPerSheet.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="&gt;&gt;labelPerSheet.Name" xml:space="preserve">
    <value>labelPerSheet</value>
  </data>
  <data name="&gt;&gt;labelPerSheet.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelPerSheet.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelPerSheet.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="buttonEmptyQ.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonEmptyQ.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonEmptyQ.Location" type="System.Drawing.Point, System.Drawing">
    <value>199, 110</value>
  </data>
  <data name="buttonEmptyQ.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 24</value>
  </data>
  <data name="buttonEmptyQ.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="buttonEmptyQ.Text" xml:space="preserve">
    <value>&amp;Empty queue</value>
  </data>
  <data name="&gt;&gt;buttonEmptyQ.Name" xml:space="preserve">
    <value>buttonEmptyQ</value>
  </data>
  <data name="&gt;&gt;buttonEmptyQ.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonEmptyQ.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonEmptyQ.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="tbSheetQueue.Location" type="System.Drawing.Point, System.Drawing">
    <value>98, 74</value>
  </data>
  <data name="tbSheetQueue.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 13</value>
  </data>
  <data name="tbSheetQueue.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="tbSheetQueue.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;tbSheetQueue.Name" xml:space="preserve">
    <value>tbSheetQueue</value>
  </data>
  <data name="&gt;&gt;tbSheetQueue.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbSheetQueue.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbSheetQueue.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 74</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>66, 13</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>Multi-sheet</value>
  </data>
  <data name="label1.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="tbSingleQueue.Location" type="System.Drawing.Point, System.Drawing">
    <value>98, 48</value>
  </data>
  <data name="tbSingleQueue.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 13</value>
  </data>
  <data name="tbSingleQueue.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="tbSingleQueue.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;tbSingleQueue.Name" xml:space="preserve">
    <value>tbSingleQueue</value>
  </data>
  <data name="&gt;&gt;tbSingleQueue.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbSingleQueue.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbSingleQueue.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label4.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 48</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>66, 13</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>Single card</value>
  </data>
  <data name="label4.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="label5.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label5.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 9</value>
  </data>
  <data name="label5.Size" type="System.Drawing.Size, System.Drawing">
    <value>178, 20</value>
  </data>
  <data name="label5.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>Documents in queue:</value>
  </data>
  <data name="label5.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>BottomLeft</value>
  </data>
  <data name="&gt;&gt;label5.Name" xml:space="preserve">
    <value>label5</value>
  </data>
  <data name="&gt;&gt;label5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label5.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label5.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>43</value>
  </metadata>
  <data name="$this.AutoScaleBaseSize" type="System.Drawing.Size, System.Drawing">
    <value>5, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>408, 170</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAIAEBAQAAAABAAoAQAAJgAAACAgEAAAAAQA6AIAAE4BAAAoAAAAEAAAACAAAAABAAQAAAAAAMAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAgAAAAICAAIAAAACAAIAAgIAAAICAgADAwMAAAAD/AAD/
        AAAA//8A/wAAAP8A/wD//wAA////AAAAAAAAAAAAAH//j/j/jwAAf/+P+P+PAAB4iIiIiIgAAH93h3h3
        jwAAf3eHeHePAAB4iIiIiIgAAH93h3h3jwAAf3eHeHePAAB4iIiIiIgAAH93h3h3jwAAf3eHeHePAAB4
        iIiIgAAAAH//j/j39wAAf/+P+PdwAAB3d3d3dwAAwAEAAMABAADAAQAAwAEAAMABAADAAQAAwAEAAMAB
        AADAAQAAwAEAAMABAADAAQAAwAEAAMADAADABwAAwA8AACgAAAAgAAAAQAAAAAEABAAAAAAAAAIAAAAA
        AAAAAAAAEAAAABAAAAAAAAAAAACAAACAAAAAgIAAgAAAAIAAgACAgAAAgICAAMDAwAAAAP8AAP8AAAD/
        /wD/AAAA/wD/AP//AAD///8AAAAAAAAAAAAAAAAAAAAAAAAHiIiIiIiIiIiIiIiIAAAAB5mZmZmZn/j/
        //j/+AAAAAf/mZmZn//4///4//gAAAAH//mZmf//+P//+P/4AAAAB//5mZn///j///j/+AAAAAf/+ZmZ
        ///4///4//gAAAAHiImZmYiIiIiIiIiIAAAAB/95mZl4eHh4eHj/+AAAAAf/iZmZh4eIh4eI//gAAAAH
        /3mZmXh4eHh4eP/4AAAAB/+JmZmHh4iHh4j/+AAAAAf/eZmZmZmZeHh4//gAAAAHiImZmYiJmZmIiIiI
        AAAAB/95mZl4eJmZmHj/+AAAAAf/iZmZh4eJmZmI//gAAAAH/3mZmXh4eZmZeP/4AAAAB/+JmZmHh4mZ
        mYj/+AAAAAf/eZmZeHh5mZl4//gAAAAHiImZmYiIiZmZiIiIAAAAB/95mZl4eJmZmHj/+AAAAAf/mZmZ
        h4mZmYeI//gAAAAHmZmZmZmZmXh4eP/4AAAAB/+Hh4iHh4iHh4j/+AAAAAf/eHh4eHh4eHh4//gAAAAH
        iIiIiIiIiIiIcAAAAAAAB/////j///j//3/4cAAAAAf////4///4//9/hwAAAAAH////+P//+P//eHAA
        AAAAB/////j///j//3cAAAAAAAf////4///4//9wAAAAAAAHd3d3d3d3d3d3cAAAAADgAAAH4AAAB+AA
        AAfgAAAH4AAAB+AAAAfgAAAH4AAAB+AAAAfgAAAH4AAAB+AAAAfgAAAH4AAAB+AAAAfgAAAH4AAAB+AA
        AAfgAAAH4AAAB+AAAAfgAAAH4AAAB+AAAAfgAAAH4AAAB+AAAA/gAAAf4AAAP+AAAH/gAAD/4AAB/w==
</value>
  </data>
  <data name="$this.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>ID Services Printer</value>
  </data>
  <data name="&gt;&gt;timer1.Name" xml:space="preserve">
    <value>timer1</value>
  </data>
  <data name="&gt;&gt;timer1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Timer, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>DCSPrinter</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>