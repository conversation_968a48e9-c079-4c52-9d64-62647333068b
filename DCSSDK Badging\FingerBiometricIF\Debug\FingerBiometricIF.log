﻿  StdAfx.cpp
  _WIN32_WINNT not defined. Defaulting to _WIN32_WINNT_MAXVER (see WinSDKVer.h)
  AISFingerBiometric.cpp
D:\repos_D\SDS Collection\DCSSDK Badging\FingerBiometricIF\AISFingerBiometric.cpp(385,11): warning C4477: 'printf' : format string '%s' requires an argument of type 'char *', but variadic argument 1 has type 'const CString'
  (compiling source file '/AISFingerBiometric.cpp')
  
  FingerBiometricIF.cpp
  Generating Code...
AISFingerBiometric.obj : warning LNK4075: ignoring '/EDITANDCONTINUE' due to '/SAFESEH' specification
ais11.lib(ais11.dll) : error LNK2026: module unsafe for SAFESEH image.
ais11.lib(ais11.dll) : error LNK2026: module unsafe for SAFESEH image.
ais11.lib(ais11.dll) : error LNK2026: module unsafe for SAFESEH image.
ais11.lib(ais11.dll) : error LNK2026: module unsafe for SAFESEH image.
ais11.lib(ais11.dll) : error LNK2026: module unsafe for SAFESEH image.
ais11.lib(ais11.dll) : error LNK2026: module unsafe for SAFESEH image.
ais11.lib(ais11.dll) : error LNK2026: module unsafe for SAFESEH image.
ais11.lib(ais11.dll) : error LNK2026: module unsafe for SAFESEH image.
ais11.lib(ais11.dll) : error LNK2026: module unsafe for SAFESEH image.
ais11.lib(ais11.dll) : error LNK2026: module unsafe for SAFESEH image.
ais11.lib(ais11.dll) : error LNK2026: module unsafe for SAFESEH image.
ais11.lib(ais11.dll) : error LNK2026: module unsafe for SAFESEH image.
ais11.lib(ais11.dll) : error LNK2026: module unsafe for SAFESEH image.
ais11.lib(ais11.dll) : error LNK2026: module unsafe for SAFESEH image.
     Creating library .\Debug/FingerBiometricIF.lib and object .\Debug/FingerBiometricIF.exp
.\Debug\FingerBiometricIF.dll : fatal error LNK1281: Unable to generate SAFESEH image.
