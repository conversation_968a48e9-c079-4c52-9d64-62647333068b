using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;

namespace DCSDEV.DDEServer
{
	/// <summary>
	/// Summary description for ServerOptions.
	/// </summary>
	public class ServerOptions : System.Windows.Forms.Form
	{
		DCSDEV.ParameterStore m_ps;

		private System.Windows.Forms.CheckBox checkBoxShowTestControls;
		private System.Windows.Forms.Button buttonCancel;
		private System.Windows.Forms.Button buttonAccept;
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		public ServerOptions()
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			m_ps = new ParameterStore("DCSSDK_Mgt");
			this.GetData();
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ServerOptions));
			this.checkBoxShowTestControls = new System.Windows.Forms.CheckBox();
			this.buttonCancel = new System.Windows.Forms.Button();
			this.buttonAccept = new System.Windows.Forms.Button();
			this.SuspendLayout();
			// 
			// checkBoxShowTestControls
			// 
			this.checkBoxShowTestControls.Checked = true;
			this.checkBoxShowTestControls.CheckState = System.Windows.Forms.CheckState.Checked;
			resources.ApplyResources(this.checkBoxShowTestControls, "checkBoxShowTestControls");
			this.checkBoxShowTestControls.Name = "checkBoxShowTestControls";
			// 
			// buttonCancel
			// 
			this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			resources.ApplyResources(this.buttonCancel, "buttonCancel");
			this.buttonCancel.Name = "buttonCancel";
			this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
			// 
			// buttonAccept
			// 
			resources.ApplyResources(this.buttonAccept, "buttonAccept");
			this.buttonAccept.Name = "buttonAccept";
			this.buttonAccept.Click += new System.EventHandler(this.buttonAccept_Click);
			// 
			// ServerOptions
			// 
			resources.ApplyResources(this, "$this");
			this.Controls.Add(this.buttonCancel);
			this.Controls.Add(this.buttonAccept);
			this.Controls.Add(this.checkBoxShowTestControls);
			this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
			this.MinimizeBox = false;
			this.Name = "ServerOptions";
			this.ShowInTaskbar = false;
			this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
			this.ResumeLayout(false);

		}
		#endregion

		private void GetData()
		{
			this.checkBoxShowTestControls.Checked = m_ps.GetBoolParameter("ShowTestControls", true);
		}

		private void WriteData()
		{
			m_ps.WriteBoolParameter("ShowTestControls", this.checkBoxShowTestControls.Checked);
		}

		private void buttonCancel_Click(object sender, System.EventArgs e)
		{
			this.Close();		
		}

		private void buttonAccept_Click(object sender, System.EventArgs e)
		{
			this.WriteData();
			this.Close();
		}
	}
}
