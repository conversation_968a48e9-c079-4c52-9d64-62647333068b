namespace DCSDEV.OLEToFiles
{
    partial class OLEToFiles
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.buttonAccept = new System.Windows.Forms.Button();
            this.buttonCancel = new System.Windows.Forms.Button();
            this.labelInstructions = new System.Windows.Forms.Label();
            this.labelExistingIs = new System.Windows.Forms.Label();
            this.groupBoxExistingFiles = new System.Windows.Forms.GroupBox();
            this.radioButtonDontCopy = new System.Windows.Forms.RadioButton();
            this.radioButtonCopyFiles = new System.Windows.Forms.RadioButton();
            this.radioButtonMoveFiles = new System.Windows.Forms.RadioButton();
            this.labelDataRootDirectory = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.labelDBNameText = new System.Windows.Forms.Label();
            this.labelTableNameText = new System.Windows.Forms.Label();
            this.labelTableName = new System.Windows.Forms.Label();
            this.labelDBName = new System.Windows.Forms.Label();
            this.labelServerNameText = new System.Windows.Forms.Label();
            this.labelServerName = new System.Windows.Forms.Label();
            this.labelDataType = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.labelWait = new System.Windows.Forms.Label();
            this.labelUsersDBFields = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.groupBoxExistingFiles.SuspendLayout();
            this.SuspendLayout();
            // 
            // buttonAccept
            // 
            this.buttonAccept.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.buttonAccept.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.buttonAccept.Location = new System.Drawing.Point(399, 394);
            this.buttonAccept.Name = "buttonAccept";
            this.buttonAccept.Size = new System.Drawing.Size(84, 24);
            this.buttonAccept.TabIndex = 4;
            this.buttonAccept.Text = "&Accept";
            this.buttonAccept.Click += new System.EventHandler(this.buttonAccept_Click);
            // 
            // buttonCancel
            // 
            this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            this.buttonCancel.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.buttonCancel.ImeMode = System.Windows.Forms.ImeMode.NoControl;
            this.buttonCancel.Location = new System.Drawing.Point(511, 394);
            this.buttonCancel.Name = "buttonCancel";
            this.buttonCancel.Size = new System.Drawing.Size(84, 24);
            this.buttonCancel.TabIndex = 5;
            this.buttonCancel.Text = "&Cancel";
            this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
            // 
            // labelInstructions
            // 
            this.labelInstructions.AutoSize = true;
            this.labelInstructions.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelInstructions.ForeColor = System.Drawing.Color.Blue;
            this.labelInstructions.Location = new System.Drawing.Point(38, 344);
            this.labelInstructions.Name = "labelInstructions";
            this.labelInstructions.Size = new System.Drawing.Size(461, 20);
            this.labelInstructions.TabIndex = 8;
            this.labelInstructions.Text = "Click ACCEPT to convert image storage to files in subdirectories.";
            // 
            // labelExistingIs
            // 
            this.labelExistingIs.AutoSize = true;
            this.labelExistingIs.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelExistingIs.Location = new System.Drawing.Point(39, 27);
            this.labelExistingIs.Name = "labelExistingIs";
            this.labelExistingIs.Size = new System.Drawing.Size(197, 15);
            this.labelExistingIs.TabIndex = 6;
            this.labelExistingIs.Text = "The existing OLE image storage is:";
            // 
            // groupBoxExistingFiles
            // 
            this.groupBoxExistingFiles.Controls.Add(this.radioButtonDontCopy);
            this.groupBoxExistingFiles.Controls.Add(this.radioButtonCopyFiles);
            this.groupBoxExistingFiles.Controls.Add(this.radioButtonMoveFiles);
            this.groupBoxExistingFiles.Location = new System.Drawing.Point(42, 203);
            this.groupBoxExistingFiles.Name = "groupBoxExistingFiles";
            this.groupBoxExistingFiles.Size = new System.Drawing.Size(317, 107);
            this.groupBoxExistingFiles.TabIndex = 9;
            this.groupBoxExistingFiles.TabStop = false;
            this.groupBoxExistingFiles.Text = "What to do with existng images in the OLE database";
            // 
            // radioButtonDontCopy
            // 
            this.radioButtonDontCopy.AutoSize = true;
            this.radioButtonDontCopy.Location = new System.Drawing.Point(26, 68);
            this.radioButtonDontCopy.Name = "radioButtonDontCopy";
            this.radioButtonDontCopy.Size = new System.Drawing.Size(119, 17);
            this.radioButtonDontCopy.TabIndex = 2;
            this.radioButtonDontCopy.TabStop = true;
            this.radioButtonDontCopy.Text = "Do not copy images";
            this.radioButtonDontCopy.TextAlign = System.Drawing.ContentAlignment.TopRight;
            this.radioButtonDontCopy.UseVisualStyleBackColor = true;
            // 
            // radioButtonCopyFiles
            // 
            this.radioButtonCopyFiles.AutoSize = true;
            this.radioButtonCopyFiles.Location = new System.Drawing.Point(26, 48);
            this.radioButtonCopyFiles.Name = "radioButtonCopyFiles";
            this.radioButtonCopyFiles.Size = new System.Drawing.Size(122, 17);
            this.radioButtonCopyFiles.TabIndex = 1;
            this.radioButtonCopyFiles.TabStop = true;
            this.radioButtonCopyFiles.Text = "Copy Images to files.";
            this.radioButtonCopyFiles.UseVisualStyleBackColor = true;
            // 
            // radioButtonMoveFiles
            // 
            this.radioButtonMoveFiles.AutoSize = true;
            this.radioButtonMoveFiles.Location = new System.Drawing.Point(26, 25);
            this.radioButtonMoveFiles.Name = "radioButtonMoveFiles";
            this.radioButtonMoveFiles.Size = new System.Drawing.Size(260, 17);
            this.radioButtonMoveFiles.TabIndex = 0;
            this.radioButtonMoveFiles.TabStop = true;
            this.radioButtonMoveFiles.Text = "Move images to files.  Delete original OLE images.";
            this.radioButtonMoveFiles.UseVisualStyleBackColor = true;
            // 
            // labelDataRootDirectory
            // 
            this.labelDataRootDirectory.AutoSize = true;
            this.labelDataRootDirectory.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelDataRootDirectory.Location = new System.Drawing.Point(333, 79);
            this.labelDataRootDirectory.Name = "labelDataRootDirectory";
            this.labelDataRootDirectory.Size = new System.Drawing.Size(32, 15);
            this.labelDataRootDirectory.TabIndex = 17;
            this.labelDataRootDirectory.Text = "root";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label4.Location = new System.Drawing.Point(39, 79);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(118, 16);
            this.label4.TabIndex = 16;
            this.label4.Text = "Data root directory";
            // 
            // labelDBNameText
            // 
            this.labelDBNameText.AutoSize = true;
            this.labelDBNameText.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelDBNameText.Location = new System.Drawing.Point(333, 98);
            this.labelDBNameText.Name = "labelDBNameText";
            this.labelDBNameText.Size = new System.Drawing.Size(82, 15);
            this.labelDBNameText.TabIndex = 23;
            this.labelDBNameText.Text = "SDSImages";
            // 
            // labelTableNameText
            // 
            this.labelTableNameText.AutoSize = true;
            this.labelTableNameText.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelTableNameText.Location = new System.Drawing.Point(333, 116);
            this.labelTableNameText.Name = "labelTableNameText";
            this.labelTableNameText.Size = new System.Drawing.Size(152, 15);
            this.labelTableNameText.TabIndex = 22;
            this.labelTableNameText.Text = "DCSDAT_ImagesTable";
            // 
            // labelTableName
            // 
            this.labelTableName.AutoSize = true;
            this.labelTableName.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelTableName.Location = new System.Drawing.Point(39, 120);
            this.labelTableName.Name = "labelTableName";
            this.labelTableName.Size = new System.Drawing.Size(116, 16);
            this.labelTableName.TabIndex = 21;
            this.labelTableName.Text = "Image table name";
            // 
            // labelDBName
            // 
            this.labelDBName.AutoSize = true;
            this.labelDBName.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelDBName.Location = new System.Drawing.Point(39, 99);
            this.labelDBName.Name = "labelDBName";
            this.labelDBName.Size = new System.Drawing.Size(105, 16);
            this.labelDBName.TabIndex = 20;
            this.labelDBName.Text = "Database name";
            // 
            // labelServerNameText
            // 
            this.labelServerNameText.AutoSize = true;
            this.labelServerNameText.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelServerNameText.Location = new System.Drawing.Point(333, 136);
            this.labelServerNameText.Name = "labelServerNameText";
            this.labelServerNameText.Size = new System.Drawing.Size(46, 15);
            this.labelServerNameText.TabIndex = 25;
            this.labelServerNameText.Text = "server";
            // 
            // labelServerName
            // 
            this.labelServerName.AutoSize = true;
            this.labelServerName.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelServerName.Location = new System.Drawing.Point(39, 137);
            this.labelServerName.Name = "labelServerName";
            this.labelServerName.Size = new System.Drawing.Size(85, 16);
            this.labelServerName.TabIndex = 24;
            this.labelServerName.Text = "Server name";
            // 
            // labelDataType
            // 
            this.labelDataType.AutoSize = true;
            this.labelDataType.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelDataType.Location = new System.Drawing.Point(333, 60);
            this.labelDataType.Name = "labelDataType";
            this.labelDataType.Size = new System.Drawing.Size(26, 15);
            this.labelDataType.TabIndex = 27;
            this.labelDataType.Text = "sql";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label5.Location = new System.Drawing.Point(39, 60);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(97, 16);
            this.label5.TabIndex = 26;
            this.label5.Text = "Database type";
            // 
            // labelWait
            // 
            this.labelWait.AutoSize = true;
            this.labelWait.Font = new System.Drawing.Font("Microsoft Sans Serif", 15.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelWait.ForeColor = System.Drawing.Color.Blue;
            this.labelWait.Location = new System.Drawing.Point(37, 339);
            this.labelWait.Name = "labelWait";
            this.labelWait.Size = new System.Drawing.Size(157, 25);
            this.labelWait.TabIndex = 28;
            this.labelWait.Text = "Please Wait ....";
            this.labelWait.Visible = false;
            // 
            // labelUsersDBFields
            // 
            this.labelUsersDBFields.AutoSize = true;
            this.labelUsersDBFields.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.labelUsersDBFields.Location = new System.Drawing.Point(333, 156);
            this.labelUsersDBFields.Name = "labelUsersDBFields";
            this.labelUsersDBFields.Size = new System.Drawing.Size(39, 15);
            this.labelUsersDBFields.TabIndex = 30;
            this.labelUsersDBFields.Text = "none";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.label2.Location = new System.Drawing.Point(39, 156);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(192, 16);
            this.label2.TabIndex = 29;
            this.label2.Text = "Image types in users database";
            // 
            // OLEToFiles
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(624, 442);
            this.Controls.Add(this.labelUsersDBFields);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.labelWait);
            this.Controls.Add(this.labelDataType);
            this.Controls.Add(this.label5);
            this.Controls.Add(this.labelServerNameText);
            this.Controls.Add(this.labelServerName);
            this.Controls.Add(this.labelDBNameText);
            this.Controls.Add(this.labelTableNameText);
            this.Controls.Add(this.labelTableName);
            this.Controls.Add(this.labelDBName);
            this.Controls.Add(this.labelDataRootDirectory);
            this.Controls.Add(this.label4);
            this.Controls.Add(this.buttonAccept);
            this.Controls.Add(this.buttonCancel);
            this.Controls.Add(this.labelInstructions);
            this.Controls.Add(this.labelExistingIs);
            this.Controls.Add(this.groupBoxExistingFiles);
            this.Name = "OLEToFiles";
            this.Text = "OLEToFiles";
            this.Load += new System.EventHandler(this.OLEToFiles_Load);
            this.groupBoxExistingFiles.ResumeLayout(false);
            this.groupBoxExistingFiles.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Button buttonAccept;
        private System.Windows.Forms.Button buttonCancel;
        private System.Windows.Forms.Label labelInstructions;
        private System.Windows.Forms.Label labelExistingIs;
        private System.Windows.Forms.GroupBox groupBoxExistingFiles;
        private System.Windows.Forms.RadioButton radioButtonDontCopy;
        private System.Windows.Forms.RadioButton radioButtonCopyFiles;
        private System.Windows.Forms.RadioButton radioButtonMoveFiles;
        private System.Windows.Forms.Label labelDataRootDirectory;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label labelDBNameText;
        private System.Windows.Forms.Label labelTableNameText;
        private System.Windows.Forms.Label labelTableName;
        private System.Windows.Forms.Label labelDBName;
        private System.Windows.Forms.Label labelServerNameText;
        private System.Windows.Forms.Label labelServerName;
        private System.Windows.Forms.Label labelDataType;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label labelWait;
        private System.Windows.Forms.Label labelUsersDBFields;
        private System.Windows.Forms.Label label2;
    }
}