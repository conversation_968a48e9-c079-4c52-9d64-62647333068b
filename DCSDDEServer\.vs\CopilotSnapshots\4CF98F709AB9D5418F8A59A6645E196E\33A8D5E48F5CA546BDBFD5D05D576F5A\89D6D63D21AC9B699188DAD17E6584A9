﻿$projectFile = "D:\repos_D\SDS Collection\DCSDDEServer\DCSDDEServer.csproj"

# Make a backup first
Copy-Item $projectFile "$projectFile.original" -Force

# Load the project file as XML
[xml]$xml = Get-Content $projectFile

# Add new references
$itemGroups = $xml.Project.ItemGroup
$referencesItemGroup = $itemGroups | Where-Object { $_.Reference -ne $null } | Select-Object -First 1

# Create the new reference nodes for BadgingMgt
$badgingRef = $xml.CreateElement("Reference", $xml.DocumentElement.NamespaceURI)
$badgingRef.SetAttribute("Include", "DCSSDK_BadgingMgt")
$hintPath1 = $xml.CreateElement("HintPath", $xml.DocumentElement.NamespaceURI)
$hintPath1.InnerText = "lib\DCSSDK_BadgingMgt.dll"
$private1 = $xml.CreateElement("Private", $xml.DocumentElement.NamespaceURI)
$private1.InnerText = "True"
$badgingRef.AppendChild($hintPath1)
$badgingRef.AppendChild($private1)

# Create the new reference nodes for CaptureMgt
$captureRef = $xml.CreateElement("Reference", $xml.DocumentElement.NamespaceURI)
$captureRef.SetAttribute("Include", "DCSSDK_CaptureMgt")
$hintPath2 = $xml.CreateElement("HintPath", $xml.DocumentElement.NamespaceURI)
$hintPath2.InnerText = "lib\DCSSDK_CaptureMgt.dll"
$private2 = $xml.CreateElement("Private", $xml.DocumentElement.NamespaceURI)
$private2.InnerText = "True"
$captureRef.AppendChild($hintPath2)
$captureRef.AppendChild($private2)

# Create the new reference nodes for Utilities
$utilitiesRef = $xml.CreateElement("Reference", $xml.DocumentElement.NamespaceURI)
$utilitiesRef.SetAttribute("Include", "DCSSDK_Utilities")
$hintPath3 = $xml.CreateElement("HintPath", $xml.DocumentElement.NamespaceURI)
$hintPath3.InnerText = "lib\DCSSDK_Utilities.dll"
$private3 = $xml.CreateElement("Private", $xml.DocumentElement.NamespaceURI)
$private3.InnerText = "True"
$utilitiesRef.AppendChild($hintPath3)
$utilitiesRef.AppendChild($private3)

# Add new references to the References ItemGroup
$firstRef = $referencesItemGroup.Reference[0]
$referencesItemGroup.InsertAfter($badgingRef, $firstRef)
$referencesItemGroup.InsertAfter($captureRef, $badgingRef)
$referencesItemGroup.InsertAfter($utilitiesRef, $captureRef)

# Find ProjectReference ItemGroup
$projectRefsItemGroup = $itemGroups | Where-Object { $_.ProjectReference -ne $null } | Select-Object -First 1

# Find and remove the project references we don't need
$projectRefs = $projectRefsItemGroup.ProjectReference
$projectRefsToRemove = $projectRefs | Where-Object { 
    $_.Include -eq "..\DCSSDK Badging\DCSSDK_BadgingMgt\DCSSDK_BadgingMgt.csproj" -or 
    $_.Include -eq "..\DCSSDK Badging\DCSSDK_CaptureMgt\DCSSDK_CaptureMgt.csproj" -or
    $_.Include -eq "..\DCSSDK Capture\DCSSDK_Utilities\DCSSDK_Utilities.csproj"
}

foreach ($ref in $projectRefsToRemove) {
    $projectRefsItemGroup.RemoveChild($ref)
}

# Save the modified XML back to the project file
$xml.Save($projectFile)

Write-Output "Project file has been updated successfully!"