﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <ProjectType>Local</ProjectType>
    <ProductVersion>8.0.50727</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{89EC02EF-B16D-4884-A894-37F4CB62B24E}</ProjectGuid>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ApplicationIcon>App.ico</ApplicationIcon>
    <AssemblyKeyContainerName>
    </AssemblyKeyContainerName>
    <AssemblyName>DCSSDK_CaptureMgt</AssemblyName>
    <AssemblyOriginatorKeyFile>
    </AssemblyOriginatorKeyFile>
    <DefaultClientScript>JScript</DefaultClientScript>
    <DefaultHTMLPageLayout>Grid</DefaultHTMLPageLayout>
    <DefaultTargetSchema>IE50</DefaultTargetSchema>
    <DelaySign>false</DelaySign>
    <OutputType>Library</OutputType>
    <RootNamespace>DCSSDK_CaptureMgt</RootNamespace>
    <RunPostBuildEvent>OnBuildSuccess</RunPostBuildEvent>
    <StartupObject>
    </StartupObject>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <OldToolsVersion>2.0</OldToolsVersion>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <BaseAddress>285212672</BaseAddress>
    <CheckForOverflowUnderflow>false</CheckForOverflowUnderflow>
    <ConfigurationOverrideFile>
    </ConfigurationOverrideFile>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DocumentationFile>
    </DocumentationFile>
    <DebugSymbols>true</DebugSymbols>
    <FileAlignment>4096</FileAlignment>
    <NoStdLib>false</NoStdLib>
    <NoWarn>
    </NoWarn>
    <Optimize>false</Optimize>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RemoveIntegerChecks>false</RemoveIntegerChecks>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningLevel>4</WarningLevel>
    <DebugType>full</DebugType>
    <ErrorReport>prompt</ErrorReport>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <BaseAddress>285212672</BaseAddress>
    <CheckForOverflowUnderflow>false</CheckForOverflowUnderflow>
    <ConfigurationOverrideFile>
    </ConfigurationOverrideFile>
    <DefineConstants>TRACE</DefineConstants>
    <DocumentationFile>
    </DocumentationFile>
    <DebugSymbols>false</DebugSymbols>
    <FileAlignment>4096</FileAlignment>
    <NoStdLib>false</NoStdLib>
    <NoWarn>
    </NoWarn>
    <Optimize>true</Optimize>
    <RegisterForComInterop>false</RegisterForComInterop>
    <RemoveIntegerChecks>false</RemoveIntegerChecks>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningLevel>4</WarningLevel>
    <DebugType>none</DebugType>
    <ErrorReport>prompt</ErrorReport>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AgrFeature, Version=1.0.2902.20261, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Referenced Files\AgoraDlls\AgrFeature.DLL</HintPath>
    </Reference>
    <Reference Include="Antheus.Display.FingerView, Version=1.3.0.1, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Referenced Files\AgoraDlls\Antheus.Display.FingerView.dll</HintPath>
    </Reference>
    <Reference Include="Innovatrics.IEngine, Version=2.0.3463.39291, Culture=neutral, PublicKeyToken=0bfe3bd71f8c6cda, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Referenced Files\Innovatrics IDKit\Innovatrics.IEngine.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <Name>System</Name>
    </Reference>
    <Reference Include="System.Data">
      <Name>System.Data</Name>
    </Reference>
    <Reference Include="System.Drawing">
      <Name>System.Drawing</Name>
    </Reference>
    <Reference Include="System.Windows.Forms">
      <Name>System.Windows.Forms</Name>
    </Reference>
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
    </Reference>
    <ProjectReference Include="..\..\DCSSDK Capture\DCSCapture_Canon\DCSCapture_Canon.csproj">
      <Project>{A4D80E36-F7F2-45A1-AA1A-257DBFEC9E7B}</Project>
      <Name>DCSCapture_Canon</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\DCSSDK Capture\DCSCapture_CrossMatch\DCSCapture_CrossMatch.csproj">
      <Project>{99475490-40C3-481E-8BE5-523316DDBBFD}</Project>
      <Name>DCSCapture_CrossMatch</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\DCSSDK Capture\DCSCapture_DCS8000\DCSCapture_DCS8000.csproj">
      <Project>{15235730-18A9-40CA-BB9B-897FA0D87441}</Project>
      <Name>DCSCapture_DCS8000</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\DCSSDK Capture\DCSCapture_FDU04\DCSCapture_FDU04.csproj">
      <Project>{B4226568-84CF-4F48-A303-3A5FEFF57467}</Project>
      <Name>DCSCapture_FDU04</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\DCSSDK Capture\DCSCapture_FromFile\DCSCapture_FromFile.csproj">
      <Project>{459DE427-2EB2-4C0C-AAB3-1B2BF5156336}</Project>
      <Name>DCSCapture_FromFile</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\DCSSDK Capture\DCSCapture_Topaz\DCSCapture_Topaz.csproj">
      <Project>{C67A9CC1-4604-4FC4-BB60-7CB6AC753CE9}</Project>
      <Name>DCSCapture_Topaz</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\DCSSDK Capture\DCSCapture_Twain\DCSCapture_Twain.csproj">
      <Project>{762C71EE-F85E-4635-8F87-B1ABE994FC54}</Project>
      <Name>DCSCapture_Twain</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\DCSSDK Capture\DCSSDK_Finisher\DCSSDK_Finisher.csproj">
      <Project>{4D22800C-2DB5-450B-A8A0-A130A3D50BD6}</Project>
      <Name>DCSSDK_Finisher</Name>
    </ProjectReference>
    <ProjectReference Include="..\DCSCapture_Scanner\DCSCapture_Scanner.csproj">
      <Name>DCSCapture_Scanner</Name>
      <Project>{B8FCC081-8249-45B7-A33A-1E2D66D04B25}</Project>
      <Package>{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</Package>
    </ProjectReference>
    <ProjectReference Include="..\..\DCSSDK Capture\DCSSDK_FinisherProperties\DCSSDK_FinisherProperties.csproj">
      <Name>DCSSDK_FinisherProperties</Name>
      <Project>{65E7D81F-BD45-423C-9DE9-A9D66849B163}</Project>
      <Package>{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</Package>
    </ProjectReference>
    <ProjectReference Include="..\..\DCSSDK Capture\DCSSDK_Utilities\DCSSDK_Utilities.csproj">
      <Name>DCSSDK_Utilities</Name>
      <Project>{6EF6073A-143A-497A-9FFA-21DD39EBC9ED}</Project>
      <Package>{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</Package>
    </ProjectReference>
    <ProjectReference Include="..\DCSInnovatricsIF\DCSInnovatricsIF.csproj">
      <Project>{7C89FA84-1E98-4830-9DAC-9503FE5F0DAD}</Project>
      <Name>DCSInnovatricsIF</Name>
    </ProjectReference>
    <ProjectReference Include="..\DCSSDK_ChipIF\DCSSDK_ChipIF.csproj">
      <Project>{C5F4E046-FD68-4042-AA10-19DADD20191B}</Project>
      <Name>DCSSDK_ChipIF</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AboutCaptureMgt.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="AgoraIF.cs" />
    <Compile Include="AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="CaptureMgt.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DCSFingerMapping.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FilesToOLE.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FilesToOLE.Designer.cs">
      <DependentUpon>FilesToOLE.cs</DependentUpon>
    </Compile>
    <Compile Include="FIPS_Tools.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="ImageDisplay.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="OLEToFiles.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="OLEToFiles.Designer.cs">
      <DependentUpon>OLEToFiles.cs</DependentUpon>
    </Compile>
    <Compile Include="ScanBarcode.cs">
      <SubType>Form</SubType>
    </Compile>
    <EmbeddedResource Include="AboutCaptureMgt.resx">
      <DependentUpon>AboutCaptureMgt.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="CaptureMgt.en-US.resx">
      <SubType>Designer</SubType>
      <DependentUpon>CaptureMgt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CaptureMgt.fr-FR.resx">
      <SubType>Designer</SubType>
      <DependentUpon>CaptureMgt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CaptureMgt.fr.resx">
      <SubType>Designer</SubType>
      <DependentUpon>CaptureMgt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CaptureMgt.resx">
      <DependentUpon>CaptureMgt.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="DCSFingerMapping.resx">
      <DependentUpon>DCSFingerMapping.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FilesToOLE.resx">
      <SubType>Designer</SubType>
      <DependentUpon>FilesToOLE.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ImageDisplay.resx">
      <DependentUpon>ImageDisplay.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="OLEToFiles.resx">
      <SubType>Designer</SubType>
      <DependentUpon>OLEToFiles.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ScanBarcode.resx">
      <DependentUpon>ScanBarcode.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Content Include="App.ico" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.3.1">
      <Visible>False</Visible>
      <ProductName>Windows Installer 3.1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PreBuildEvent>
    </PreBuildEvent>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
</Project>