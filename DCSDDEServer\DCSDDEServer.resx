<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonExit.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="buttonExit.Location" type="System.Drawing.Point, System.Drawing">
    <value>219, 29</value>
  </data>
  <data name="buttonExit.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 32</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonExit.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="buttonExit.Text" xml:space="preserve">
    <value>E&amp;xit</value>
  </data>
  <data name="&gt;&gt;buttonExit.Name" xml:space="preserve">
    <value>buttonExit</value>
  </data>
  <data name="&gt;&gt;buttonExit.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonExit.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonExit.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <metadata name="timer1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="checkBoxRetake.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBoxRetake.Location" type="System.Drawing.Point, System.Drawing">
    <value>216, 158</value>
  </data>
  <data name="checkBoxRetake.Size" type="System.Drawing.Size, System.Drawing">
    <value>201, 17</value>
  </data>
  <data name="checkBoxRetake.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="checkBoxRetake.Text" xml:space="preserve">
    <value>Recapture - suppress historic archive</value>
  </data>
  <data name="&gt;&gt;checkBoxRetake.Name" xml:space="preserve">
    <value>checkBoxRetake</value>
  </data>
  <data name="&gt;&gt;checkBoxRetake.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBoxRetake.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;checkBoxRetake.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label8.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label8.Location" type="System.Drawing.Point, System.Drawing">
    <value>427, 141</value>
  </data>
  <data name="label8.Size" type="System.Drawing.Size, System.Drawing">
    <value>149, 16</value>
  </data>
  <data name="label8.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="label8.Text" xml:space="preserve">
    <value>FIPS portrait I/O</value>
  </data>
  <data name="label8.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>TopCenter</value>
  </data>
  <data name="&gt;&gt;label8.Name" xml:space="preserve">
    <value>label8</value>
  </data>
  <data name="&gt;&gt;label8.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label8.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;label8.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="buttonReadFips.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonReadFips.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonReadFips.Location" type="System.Drawing.Point, System.Drawing">
    <value>510, 117</value>
  </data>
  <data name="buttonReadFips.Size" type="System.Drawing.Size, System.Drawing">
    <value>66, 24</value>
  </data>
  <data name="buttonReadFips.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="buttonReadFips.Text" xml:space="preserve">
    <value>FIPS Import</value>
  </data>
  <data name="&gt;&gt;buttonReadFips.Name" xml:space="preserve">
    <value>buttonReadFips</value>
  </data>
  <data name="&gt;&gt;buttonReadFips.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonReadFips.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonReadFips.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="buttonGenFips.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonGenFips.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonGenFips.Location" type="System.Drawing.Point, System.Drawing">
    <value>427, 117</value>
  </data>
  <data name="buttonGenFips.Size" type="System.Drawing.Size, System.Drawing">
    <value>66, 24</value>
  </data>
  <data name="buttonGenFips.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="buttonGenFips.Text" xml:space="preserve">
    <value>FIPS Export</value>
  </data>
  <data name="&gt;&gt;buttonGenFips.Name" xml:space="preserve">
    <value>buttonGenFips</value>
  </data>
  <data name="&gt;&gt;buttonGenFips.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonGenFips.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonGenFips.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="tbImageTitle.Location" type="System.Drawing.Point, System.Drawing">
    <value>427, 182</value>
  </data>
  <data name="tbImageTitle.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 20</value>
  </data>
  <data name="tbImageTitle.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;tbImageTitle.Name" xml:space="preserve">
    <value>tbImageTitle</value>
  </data>
  <data name="&gt;&gt;tbImageTitle.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbImageTitle.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;tbImageTitle.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="label7.Location" type="System.Drawing.Point, System.Drawing">
    <value>427, 206</value>
  </data>
  <data name="label7.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 16</value>
  </data>
  <data name="label7.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="label7.Text" xml:space="preserve">
    <value>Optional Image Title</value>
  </data>
  <data name="&gt;&gt;label7.Name" xml:space="preserve">
    <value>label7</value>
  </data>
  <data name="&gt;&gt;label7.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label7.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;label7.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="cbBadgeDat.Items" xml:space="preserve">
    <value>Badge.Dat</value>
  </data>
  <data name="cbBadgeDat.Items1" xml:space="preserve">
    <value>Test1.Dat</value>
  </data>
  <data name="cbBadgeDat.Items2" xml:space="preserve">
    <value>Test2.Dat</value>
  </data>
  <data name="cbBadgeDat.Items3" xml:space="preserve">
    <value>Test3.Dat</value>
  </data>
  <data name="cbBadgeDat.Items4" xml:space="preserve">
    <value>Test4.Dat</value>
  </data>
  <data name="cbBadgeDat.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 286</value>
  </data>
  <data name="cbBadgeDat.Size" type="System.Drawing.Size, System.Drawing">
    <value>136, 21</value>
  </data>
  <data name="cbBadgeDat.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="cbBadgeDat.Text" xml:space="preserve">
    <value>Badge.Dat</value>
  </data>
  <data name="&gt;&gt;cbBadgeDat.Name" xml:space="preserve">
    <value>cbBadgeDat</value>
  </data>
  <data name="&gt;&gt;cbBadgeDat.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbBadgeDat.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;cbBadgeDat.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="label6.Location" type="System.Drawing.Point, System.Drawing">
    <value>363, 270</value>
  </data>
  <data name="label6.Size" type="System.Drawing.Size, System.Drawing">
    <value>192, 40</value>
  </data>
  <data name="label6.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>test1.dat ... reference badge designs named test1.Bdg ...</value>
  </data>
  <data name="&gt;&gt;label6.Name" xml:space="preserve">
    <value>label6</value>
  </data>
  <data name="&gt;&gt;label6.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label6.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;label6.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="buttonClose.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonClose.Location" type="System.Drawing.Point, System.Drawing">
    <value>151, 182</value>
  </data>
  <data name="buttonClose.Size" type="System.Drawing.Size, System.Drawing">
    <value>44, 32</value>
  </data>
  <data name="buttonClose.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="buttonClose.Text" xml:space="preserve">
    <value>Clos&amp;e</value>
  </data>
  <data name="&gt;&gt;buttonClose.Name" xml:space="preserve">
    <value>buttonClose</value>
  </data>
  <data name="&gt;&gt;buttonClose.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonClose.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonClose.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="tbImageIDDisplay.Location" type="System.Drawing.Point, System.Drawing">
    <value>347, 182</value>
  </data>
  <data name="tbImageIDDisplay.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 20</value>
  </data>
  <data name="tbImageIDDisplay.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="tbImageIDDisplay.Text" xml:space="preserve">
    <value>test</value>
  </data>
  <data name="&gt;&gt;tbImageIDDisplay.Name" xml:space="preserve">
    <value>tbImageIDDisplay</value>
  </data>
  <data name="&gt;&gt;tbImageIDDisplay.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbImageIDDisplay.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;tbImageIDDisplay.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="tbImageID.Location" type="System.Drawing.Point, System.Drawing">
    <value>347, 117</value>
  </data>
  <data name="tbImageID.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 20</value>
  </data>
  <data name="tbImageID.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="tbImageID.Text" xml:space="preserve">
    <value>test</value>
  </data>
  <data name="&gt;&gt;tbImageID.Name" xml:space="preserve">
    <value>tbImageID</value>
  </data>
  <data name="&gt;&gt;tbImageID.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbImageID.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;tbImageID.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 206</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>136, 16</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>Image classes to display</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="cbImageClassDisplay.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="cbImageClassDisplay.Items1" xml:space="preserve">
    <value>Portrait</value>
  </data>
  <data name="cbImageClassDisplay.Items2" xml:space="preserve">
    <value>Signature</value>
  </data>
  <data name="cbImageClassDisplay.Items3" xml:space="preserve">
    <value>Fingerprint</value>
  </data>
  <data name="cbImageClassDisplay.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 182</value>
  </data>
  <data name="cbImageClassDisplay.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 21</value>
  </data>
  <data name="cbImageClassDisplay.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="&gt;&gt;cbImageClassDisplay.Name" xml:space="preserve">
    <value>cbImageClassDisplay</value>
  </data>
  <data name="&gt;&gt;cbImageClassDisplay.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbImageClassDisplay.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;cbImageClassDisplay.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="buttonDisplay.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonDisplay.Location" type="System.Drawing.Point, System.Drawing">
    <value>27, 182</value>
  </data>
  <data name="buttonDisplay.Size" type="System.Drawing.Size, System.Drawing">
    <value>118, 32</value>
  </data>
  <data name="buttonDisplay.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="buttonDisplay.Text" xml:space="preserve">
    <value>&amp;Display</value>
  </data>
  <data name="&gt;&gt;buttonDisplay.Name" xml:space="preserve">
    <value>buttonDisplay</value>
  </data>
  <data name="&gt;&gt;buttonDisplay.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonDisplay.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonDisplay.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="label5.Location" type="System.Drawing.Point, System.Drawing">
    <value>347, 206</value>
  </data>
  <data name="label5.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 16</value>
  </data>
  <data name="label5.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>Image ID</value>
  </data>
  <data name="&gt;&gt;label5.Name" xml:space="preserve">
    <value>label5</value>
  </data>
  <data name="&gt;&gt;label5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label5.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;label5.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 262</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>240, 16</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>Badge DAT file name</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="buttonPreview.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonPreview.Location" type="System.Drawing.Point, System.Drawing">
    <value>27, 262</value>
  </data>
  <data name="buttonPreview.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 32</value>
  </data>
  <data name="buttonPreview.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="buttonPreview.Text" xml:space="preserve">
    <value>Pre&amp;view</value>
  </data>
  <data name="&gt;&gt;buttonPreview.Name" xml:space="preserve">
    <value>buttonPreview</value>
  </data>
  <data name="&gt;&gt;buttonPreview.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPreview.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonPreview.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="buttonPrint.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonPrint.Location" type="System.Drawing.Point, System.Drawing">
    <value>27, 302</value>
  </data>
  <data name="buttonPrint.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 32</value>
  </data>
  <data name="buttonPrint.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="buttonPrint.Text" xml:space="preserve">
    <value>Prin&amp;t</value>
  </data>
  <data name="&gt;&gt;buttonPrint.Name" xml:space="preserve">
    <value>buttonPrint</value>
  </data>
  <data name="&gt;&gt;buttonPrint.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPrint.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonPrint.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="buttonLayout.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonLayout.Location" type="System.Drawing.Point, System.Drawing">
    <value>27, 69</value>
  </data>
  <data name="buttonLayout.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 32</value>
  </data>
  <data name="buttonLayout.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="buttonLayout.Text" xml:space="preserve">
    <value>Design &amp;Layouts</value>
  </data>
  <data name="&gt;&gt;buttonLayout.Name" xml:space="preserve">
    <value>buttonLayout</value>
  </data>
  <data name="&gt;&gt;buttonLayout.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonLayout.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonLayout.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 141</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>136, 16</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>Image classes to capture</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="cbImageClass.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="cbImageClass.Items1" xml:space="preserve">
    <value>Portrait</value>
  </data>
  <data name="cbImageClass.Items2" xml:space="preserve">
    <value>Signature</value>
  </data>
  <data name="cbImageClass.Items3" xml:space="preserve">
    <value>Fingerprint</value>
  </data>
  <data name="cbImageClass.Items4" xml:space="preserve">
    <value>TenPrint</value>
  </data>
  <data name="cbImageClass.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 117</value>
  </data>
  <data name="cbImageClass.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 21</value>
  </data>
  <data name="cbImageClass.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;cbImageClass.Name" xml:space="preserve">
    <value>cbImageClass</value>
  </data>
  <data name="&gt;&gt;cbImageClass.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbImageClass.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;cbImageClass.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="buttonAllCapture.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonAllCapture.Location" type="System.Drawing.Point, System.Drawing">
    <value>27, 117</value>
  </data>
  <data name="buttonAllCapture.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 32</value>
  </data>
  <data name="buttonAllCapture.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="buttonAllCapture.Text" xml:space="preserve">
    <value>&amp;Capture</value>
  </data>
  <data name="&gt;&gt;buttonAllCapture.Name" xml:space="preserve">
    <value>buttonAllCapture</value>
  </data>
  <data name="&gt;&gt;buttonAllCapture.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAllCapture.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonAllCapture.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>347, 141</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 16</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>Image ID</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="buttonSetup.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonSetup.Location" type="System.Drawing.Point, System.Drawing">
    <value>27, 31</value>
  </data>
  <data name="buttonSetup.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 32</value>
  </data>
  <data name="buttonSetup.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="buttonSetup.Text" xml:space="preserve">
    <value>&amp;Setup</value>
  </data>
  <data name="&gt;&gt;buttonSetup.Name" xml:space="preserve">
    <value>buttonSetup</value>
  </data>
  <data name="&gt;&gt;buttonSetup.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonSetup.Parent" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;buttonSetup.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="groupBoxTestControls.Location" type="System.Drawing.Point, System.Drawing">
    <value>18, 86</value>
  </data>
  <data name="groupBoxTestControls.Size" type="System.Drawing.Size, System.Drawing">
    <value>600, 346</value>
  </data>
  <data name="groupBoxTestControls.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="groupBoxTestControls.Text" xml:space="preserve">
    <value>Test controls</value>
  </data>
  <data name="&gt;&gt;groupBoxTestControls.Name" xml:space="preserve">
    <value>groupBoxTestControls</value>
  </data>
  <data name="&gt;&gt;groupBoxTestControls.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBoxTestControls.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBoxTestControls.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="buttonStartPrinter.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonStartPrinter.Location" type="System.Drawing.Point, System.Drawing">
    <value>85, 29</value>
  </data>
  <data name="buttonStartPrinter.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 32</value>
  </data>
  <data name="buttonStartPrinter.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="buttonStartPrinter.Text" xml:space="preserve">
    <value>Show &amp;printer</value>
  </data>
  <data name="&gt;&gt;buttonStartPrinter.Name" xml:space="preserve">
    <value>buttonStartPrinter</value>
  </data>
  <data name="&gt;&gt;buttonStartPrinter.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonStartPrinter.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonStartPrinter.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="checkBoxShowTest.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="checkBoxShowTest.Location" type="System.Drawing.Point, System.Drawing">
    <value>18, 7</value>
  </data>
  <data name="checkBoxShowTest.Size" type="System.Drawing.Size, System.Drawing">
    <value>176, 16</value>
  </data>
  <data name="checkBoxShowTest.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="checkBoxShowTest.Text" xml:space="preserve">
    <value>Show test controls</value>
  </data>
  <data name="&gt;&gt;checkBoxShowTest.Name" xml:space="preserve">
    <value>checkBoxShowTest</value>
  </data>
  <data name="&gt;&gt;checkBoxShowTest.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBoxShowTest.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;checkBoxShowTest.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="buttonAbout.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonAbout.Location" type="System.Drawing.Point, System.Drawing">
    <value>18, 29</value>
  </data>
  <data name="buttonAbout.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 32</value>
  </data>
  <data name="buttonAbout.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="buttonAbout.Text" xml:space="preserve">
    <value>&amp;About</value>
  </data>
  <data name="&gt;&gt;buttonAbout.Name" xml:space="preserve">
    <value>buttonAbout</value>
  </data>
  <data name="&gt;&gt;buttonAbout.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAbout.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAbout.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="buttonMinimize.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonMinimize.Location" type="System.Drawing.Point, System.Drawing">
    <value>152, 29</value>
  </data>
  <data name="buttonMinimize.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 32</value>
  </data>
  <data name="buttonMinimize.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="buttonMinimize.Text" xml:space="preserve">
    <value>&amp;Minimize</value>
  </data>
  <data name="&gt;&gt;buttonMinimize.Name" xml:space="preserve">
    <value>buttonMinimize</value>
  </data>
  <data name="&gt;&gt;buttonMinimize.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonMinimize.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonMinimize.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>36</value>
  </metadata>
  <data name="$this.AutoScaleBaseSize" type="System.Drawing.Size, System.Drawing">
    <value>5, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>630, 444</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>ID Services</value>
  </data>
  <data name="&gt;&gt;timer1.Name" xml:space="preserve">
    <value>timer1</value>
  </data>
  <data name="&gt;&gt;timer1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Timer, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>DDEServer</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>