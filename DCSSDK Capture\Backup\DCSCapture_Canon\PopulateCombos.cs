using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;
using System.Runtime.InteropServices;

namespace DCSDEV.CanonCamera
{
	internal struct ComboVals
	{
		internal int dVal;
		internal string strVal;
		internal ComboVals(int i, string str)
		{
			dVal=i;
			strVal=str;
		}
	};

	/// <summary>
	/// Summary description for PopulateCombos.
	/// </summary>
	internal class PopulateCombos
	{
		// Get-Set-Enumerate Exposure Compensation
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_SetExpoComp(uint dValue);
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_EnumExpoComp(uint MaxLen, out int ActualLen, int[] dValues, ref int CurrValue, out int DefaultValue);

		// Get-Set-Enumerate Flash
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_SetFlash(uint dValue);
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_EnumFlash(uint MaxLen, out int ActualLen, int[] dValues, ref int CurrValue, out int DefaultValue);

		// Get-Set-Enumerate image format
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_SetImageFormat(uint dValue);
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_EnumImageFormat(uint MaxLen, out int ActualLen, int[] dValues, ref int CurrValue, out int DefaultValue);

		// Get-Set-Enumerate ISO Speed
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_SetISOSpeed(uint dValue);
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_EnumISOSpeed(uint MaxLen, out int ActualLen, int[] dValues, ref int CurrValue, out int DefaultValue);

		// Get-Set-Enumerate photo effect
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_SetPhotoEffect(uint dValue);
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_EnumPhotoEffect(uint MaxLen, out int ActualLen, int[] dValues, ref int CurrValue, out int DefaultValue);

		// Get-Set-Enumerate shooting mode
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_SetShootingMode(uint dValue);
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_EnumShootingMode(uint MaxLen, out int ActualLen, int[] dValues, ref int CurrValue, out int DefaultValue);

		// Get-Set-Enumerate white balance
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_SetWhiteBalance(uint dValue);
		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_EnumWhiteBalance(uint MaxLen, out int ActualLen, int[] dValues, ref int CurrValue, out int DefaultValue);

		[DllImport("CanonSDKIF.Dll")]
		internal static extern bool CanonSDKCall_EnumImageSizes(uint MaxLen, out int ActualLen, int[] dSizeWidths, int[] dSizeHeights, int[] dSizeIndexes);
		
		/* 	Image data quality. */
		const int cdCOMP_QUALITY_UNKNOWN	= 0xffff;
		const int cdCOMP_QUALITY_ECONOMY	= 0x0001;
		const int cdCOMP_QUALITY_NORMAL		= 0x0002;
		const int cdCOMP_QUALITY_FINE		= 0x0003;
		const int cdCOMP_QUALITY_LOSSLESS	= 0x0004;
		const int cdCOMP_QUALITY_RAW		= 0x0004;
		const int cdCOMP_QUALITY_SUPERFINE	= 0x0005;

		/* Image size */
		const int cdIMAGE_SIZE_UNKNOWN		= 0xFFFF;
		const int cdIMAGE_SIZE_LARGE		= 0x0000;
		const int cdIMAGE_SIZE_MEDIUM		= 0x0001;
		const int cdIMAGE_SIZE_SMALL		= 0x0002;
		const int cdIMAGE_SIZE_MEDIUM1		= 0x0005;
		const int cdIMAGE_SIZE_MEDIUM2		= 0x0006;
		const int cdIMAGE_SIZE_MEDIUM3		= 0x0007;

		/* Shooting Mode	*/
		const int cdSHOOTING_MODE_INVALID		= 0xFFFF;
		const int cdSHOOTING_MODE_AUTO			= 0x0000;
		const int cdSHOOTING_MODE_PROGRAM		= 0x0001;
		const int cdSHOOTING_MODE_TV			= 0x0002;
		const int cdSHOOTING_MODE_AV			= 0x0003;
		const int cdSHOOTING_MODE_MANUAL		= 0x0004;
		const int cdSHOOTING_MODE_A_DEP			= 0x0005;
		const int cdSHOOTING_MODE_M_DEP			= 0x0006;
		const int cdSHOOTING_MODE_BULB			= 0x0007;
		const int cdSHOOTING_MODE_MANUAL_2		= 0x0065;
		const int cdSHOOTING_MODE_FAR_SCENE		= 0x0066;
		const int cdSHOOTING_MODE_FAST_SHUTTER	= 0x0067;
		const int cdSHOOTING_MODE_SLOW_SHUTTER	= 0x0068;
		const int cdSHOOTING_MODE_NIGHT_SCENE	= 0x0069;
		const int cdSHOOTING_MODE_GRAY_SCALE	= 0x006a;
		const int cdSHOOTING_MODE_SEPIA			= 0x006b;
		const int cdSHOOTING_MODE_PORTRAIT		= 0x006c;
		const int cdSHOOTING_MODE_SPOT			= 0x006d;
		const int cdSHOOTING_MODE_MACRO			= 0x006e;
		const int cdSHOOTING_MODE_BW			= 0x006f;
		const int cdSHOOTING_MODE_PANFOCUS		= 0x0070;
		const int cdSHOOTING_MODE_VIVID			= 0x0071;
		const int cdSHOOTING_MODE_NEUTRAL		= 0x0072;
		const int cdSHOOTING_MODE_FLASH_OFF		= 0x0073;
		const int cdSHOOTING_MODE_LONG_SHUTTER	= 0x0074;
		const int cdSHOOTING_MODE_SUPER_MACRO	= 0x0075;
		const int cdSHOOTING_MODE_FOLIAGE		= 0x0076;
		const int cdSHOOTING_MODE_INDOOR		= 0x0077;
		const int cdSHOOTING_MODE_FIREWORKS		= 0x0078;
		const int cdSHOOTING_MODE_BEACH			= 0x0079;
		const int cdSHOOTING_MODE_UNDERWATER	= 0x007a;
		const int cdSHOOTING_MODE_SNOW			= 0x007b;

		/* 	Photo effect setting */
		const int cdPHOTO_EFFECT_OFF			= 0x0000;	/* Off				*/
		const int cdPHOTO_EFFECT_VIVID			= 0x0001;	/* Vivid			*/
		const int cdPHOTO_EFFECT_NEUTRAL		= 0x0002;	/* Neutral			*/
		const int cdPHOTO_EFFECT_LOW_SHARPENING	= 0x0003;	/* Low sharpening	*/
		const int cdPHOTO_EFFECT_SEPIA			= 0x0004;	/* Sepia			*/
		const int cdPHOTO_EFFECT_BW				= 0x0005;	/* Black & white	*/
		const int cdPHOTO_EFFECT_CUSTOM			= 0x0006;	/* Custom			*/
		const int cdPHOTO_EFFECT_MY_COLOR		= 0x0064;	/* My color data    */
		const int cdPHOTO_EFFECT_UNKNOWN		= 0xffff;	/* Unknown			*/

		/* ISO speed setting	*/
		const int cdREL_VAL_ISO_AUTO	= (0x0000);	/* Auto		*/
		const int cdREL_VAL_ISO_6		= (0x0028);	/* ISO 6	*/
		const int cdREL_VAL_ISO_8		= (0x002b);	/* ISO 8	*/
		const int cdREL_VAL_ISO_10		= (0x002d);	/* ISO 10	*/
		const int cdREL_VAL_ISO_12		= (0x0030);	/* ISO 12	*/
		const int cdREL_VAL_ISO_16		= (0x0033);	/* ISO 16	*/
		const int cdREL_VAL_ISO_20		= (0x0035);	/* ISO 20	*/
		const int cdREL_VAL_ISO_25		= (0x0038);	/* ISO 25	*/
		const int cdREL_VAL_ISO_32		= (0x003b);	/* ISO 32	*/
		const int cdREL_VAL_ISO_40		= (0x003d);	/* ISO 40	*/
		const int cdREL_VAL_ISO_50		= (0x0040);	/* ISO 50	*/
		const int cdREL_VAL_ISO_64		= (0x0043);	/* ISO 64	*/
		const int cdREL_VAL_ISO_80		= (0x0045);	/* ISO 80	*/
		const int cdREL_VAL_ISO_100		= (0x0048);	/* ISO 100	*/
		const int cdREL_VAL_ISO_125		= (0x004b);	/* ISO 125	*/
		const int cdREL_VAL_ISO_160		= (0x004d);	/* ISO 160	*/
		const int cdREL_VAL_ISO_200		= (0x0050);	/* ISO 200	*/
		const int cdREL_VAL_ISO_250		= (0x0053);	/* ISO 250	*/
		const int cdREL_VAL_ISO_320		= (0x0055);	/* ISO 320	*/
		const int cdREL_VAL_ISO_400		= (0x0058);	/* ISO 400	*/
		const int cdREL_VAL_ISO_500		= (0x005b);	/* ISO 500	*/
		const int cdREL_VAL_ISO_640		= (0x005d);	/* ISO 640	*/
		const int cdREL_VAL_ISO_800		= (0x0060);	/* ISO 800	*/
		const int cdREL_VAL_ISO_1000	= (0x0063);	/* ISO 1000	*/
		const int cdREL_VAL_ISO_1250	= (0x0065);	/* ISO 1250	*/
		const int cdREL_VAL_ISO_1600	= (0x0068);	/* ISO 1600	*/
		const int cdREL_VAL_ISO_2000	= (0x006b);	/* ISO 2000	*/
		const int cdREL_VAL_ISO_2500	= (0x006d);	/* ISO 2500	*/
		const int cdREL_VAL_ISO_3200	= (0x0070);	/* ISO 3200	*/
		const int cdREL_VAL_ISO_4000	= (0x0073);	/* ISO 4000	*/
		const int cdREL_VAL_ISO_5000	= (0x0075);	/* ISO 5000	*/
		const int cdREL_VAL_ISO_6400	= (0x0078);	/* ISO 6400	*/
		const int cdREL_VAL_ISO_NA		= (0xffff);	/* Invalid	*/

		/* FlashMode */
		const int cdFLASH_MODE_OFF				= (0x0000);
		const int cdFLASH_MODE_AUTO				= (0x0001);
		const int cdFLASH_MODE_ON				= (0x0002);
		const int cdFLASH_MODE_RED_EYE			= (0x0003);
		const int cdFLASH_MODE_SLOW_SYNC		= (0x0004);
		const int cdFLASH_MODE_AUTO_PLUS_RED_EYE= (0x0005);
		const int cdFLASH_MODE_ON_PLUS_RED_EYE	= (0x0006);
		const int cdFLASH_MODE_NA				= (0x00ff);

		/* Amount of compensation. */
		const int cdCOMP_300_PLUS		= (0x0000);
		const int cdCOMP_266_PLUS 		= (0x0003);
		const int cdCOMP_250_PLUS		= (0x0004);
		const int cdCOMP_233_PLUS		= (0x0005);
		const int cdCOMP_200_PLUS		= (0x0008);
		const int cdCOMP_166_PLUS		= (0x000b);
		const int cdCOMP_150_PLUS		= (0x000c);
		const int cdCOMP_133_PLUS		= (0x000d);
		const int cdCOMP_100_PLUS		= (0x0010);
		const int cdCOMP_066_PLUS		= (0x0013);
		const int cdCOMP_050_PLUS		= (0x0014);
		const int cdCOMP_033_PLUS		= (0x0015);
		const int cdCOMP_000_PLUS		= (0x0018);
		const int cdCOMP_033_MINUS		= (0x001b);
		const int cdCOMP_050_MINUS		= (0x001c);
		const int cdCOMP_066_MINUS		= (0x001d);
		const int cdCOMP_100_MINUS		= (0x0020);
		const int cdCOMP_133_MINUS		= (0x0023);
		const int cdCOMP_150_MINUS		= (0x0024);
		const int cdCOMP_166_MINUS		= (0x0025);
		const int cdCOMP_200_MINUS		= (0x0028);
		const int cdCOMP_233_MINUS		= (0x002b);
		const int cdCOMP_250_MINUS		= (0x002c);
		const int cdCOMP_266_MINUS		= (0x002d);
		const int cdCOMP_300_MINUS		= (0x0030);
		const int cdCOMP_NA				= (0x00ff);

		/* Light source of the white balance. */
		const int cdWB_AUTO				= (0x0000);  /* Auto */
		const int cdWB_DAY_LIGHT		= (0x0001);  /* Daylight */
		const int cdWB_CLOUDY			= (0x0002);  /* Cloudy */
		const int cdWB_TUNGSTEN			= (0x0003);  /* Tungsten */
		const int cdWB_FLUORESCENT		= (0x0004);  /* Fluorescent */
		const int cdWB_FLASH			= (0x0005);  /* Flash */
		const int cdWB_CUSTOM			= (0x0006);  /* Custom */
		const int cdWB_BW				= (0x0007);    
		const int cdWB_SHADE			= (0x0008);  /* Shade for Type B  (and Type C)		*/
		const int cdWB_KELVIN			= (0x0009);  /* Kelvin for Type B  (and Type C) 	*/
		const int cdWB_PCSET1			= (0x000a);  /* PC Set1 for Type B 				*/
		const int cdWB_PCSET2			= (0x000b);  /* PC Set2 for Type B 				*/
		const int cdWB_PCSET3			= (0x000c);  /* PC Set3 for Type B 				*/
		const int cdWB_FLUORESCENT_H	= (0x000e);  /* Fluorescent H 						*/
		const int cdWB_CUSTOM_1			= (0x000f);  /* Custom 1							*/
		const int cdWB_CUSTOM_2			= (0x0010);  /* Custom 2							*/

		const string UNKNOW_OPT			= "(unknown)";
		ComboVals[] expoCompVals;
		ComboVals[] flashModeVals;
		ComboVals[] imageFormatValsQ;
		ComboVals[] imageFormatValsS;
		ComboVals[] ISOSpeedVals;
		ComboVals[] photoEffectVals;
		ComboVals[] shootingModeVals;
		ComboVals[] whiteBalanceVals;

		int[] dSizeWidths;
		int[] dSizeHeights;
		int[] dSizeIndexes;
		int dSizeLen;

		internal PopulateCombos()
		{
			//
			// TODO: Add constructor logic here
			//
			expoCompVals = new ComboVals[]
				{
					new ComboVals(cdCOMP_300_PLUS, "+ 3"),
					new ComboVals(cdCOMP_266_PLUS, "+ 2 2/3"),
					new ComboVals(cdCOMP_250_PLUS, "+ 2 1/2"),
					new ComboVals(cdCOMP_233_PLUS, "+ 2 1/3"),
					new ComboVals(cdCOMP_200_PLUS, "+ 2"),
					new ComboVals(cdCOMP_166_PLUS, "+ 1 2/3"),
					new ComboVals(cdCOMP_150_PLUS, "+ 1 1/2"),
					new ComboVals(cdCOMP_133_PLUS, "+ 1 1/3"),
					new ComboVals(cdCOMP_100_PLUS, "+ 1"),
					new ComboVals(cdCOMP_066_PLUS, "+ 2/3"),
					new ComboVals(cdCOMP_050_PLUS, "+ 1/2"),
					new ComboVals(cdCOMP_033_PLUS, "+ 1/3"),
					new ComboVals(cdCOMP_000_PLUS, "0"),
					new ComboVals(cdCOMP_033_MINUS, "- 1/3"),
					new ComboVals(cdCOMP_050_MINUS, "- 1/2"),
					new ComboVals(cdCOMP_066_MINUS, "- 2/3"),
					new ComboVals(cdCOMP_100_MINUS, "- 1"	),
					new ComboVals(cdCOMP_133_MINUS, "- 1 1/3"),
					new ComboVals(cdCOMP_150_MINUS, "- 1 1/2"),
					new ComboVals(cdCOMP_166_MINUS, "- 1 2/3"),
					new ComboVals(cdCOMP_200_MINUS, "- 2"),
					new ComboVals(cdCOMP_233_MINUS, "- 2 1/3"),
					new ComboVals(cdCOMP_250_MINUS, "- 2 1/2"),
					new ComboVals(cdCOMP_266_MINUS, "- 2 2/3"),
					new ComboVals(cdCOMP_300_MINUS, "- 3"),
					new ComboVals(cdCOMP_NA,		"(NA)")
				};
			flashModeVals = new ComboVals[] 
				{
					new ComboVals(cdFLASH_MODE_ON,				"on"),
					new ComboVals(cdFLASH_MODE_AUTO,			"auto"),
					new ComboVals(cdFLASH_MODE_OFF,				"off"),
					new ComboVals(cdFLASH_MODE_RED_EYE,			"Red Eye"),
					new ComboVals(cdFLASH_MODE_SLOW_SYNC,		"Slow Sync."),
					new ComboVals(cdFLASH_MODE_AUTO_PLUS_RED_EYE,"Auto (Red Eye)"),
					new ComboVals(cdFLASH_MODE_ON_PLUS_RED_EYE,	"On (Red Eye)"),
					new ComboVals(cdFLASH_MODE_NA,				"(NA)")
				};
			imageFormatValsQ = new ComboVals[]
				{
					new ComboVals(cdCOMP_QUALITY_ECONOMY,	"Economy"),
					new ComboVals(cdCOMP_QUALITY_NORMAL,	"Normal"),
					new ComboVals(cdCOMP_QUALITY_FINE,		"Fine"),
					new ComboVals(cdCOMP_QUALITY_RAW,		"Raw/Lossless"),
					new ComboVals(cdCOMP_QUALITY_SUPERFINE,	"SuperFine"),
				};
			imageFormatValsS = new ComboVals[]
				{
					new ComboVals(cdIMAGE_SIZE_LARGE,		"Large"),
					new ComboVals(cdIMAGE_SIZE_MEDIUM,		"Medium"),
					new ComboVals(cdIMAGE_SIZE_SMALL,		"Small"),
					new ComboVals(cdIMAGE_SIZE_MEDIUM1,		"Medium1"),
					new ComboVals(cdIMAGE_SIZE_MEDIUM2,		"Medium2"),
					new ComboVals(cdIMAGE_SIZE_MEDIUM3,		"Medium3"),
				};
			ISOSpeedVals = new ComboVals[]
				{
					new ComboVals(cdREL_VAL_ISO_AUTO,	"AUTO"),
					new ComboVals(cdREL_VAL_ISO_6,		"6"),
					new ComboVals(cdREL_VAL_ISO_8,		"8"),
					new ComboVals(cdREL_VAL_ISO_10,		"10"),
					new ComboVals(cdREL_VAL_ISO_12,		"12"),
					new ComboVals(cdREL_VAL_ISO_16,		"16"),
					new ComboVals(cdREL_VAL_ISO_20,		"20"),
					new ComboVals(cdREL_VAL_ISO_25,		"25"),
					new ComboVals(cdREL_VAL_ISO_32,		"32"),
					new ComboVals(cdREL_VAL_ISO_40,		"40"),
					new ComboVals(cdREL_VAL_ISO_50,		"50"),
					new ComboVals(cdREL_VAL_ISO_64,		"64"),
					new ComboVals(cdREL_VAL_ISO_80,		"80"),
					new ComboVals(cdREL_VAL_ISO_100,	"100"),
					new ComboVals(cdREL_VAL_ISO_125,	"125"),
					new ComboVals(cdREL_VAL_ISO_160,	"160"),
					new ComboVals(cdREL_VAL_ISO_200,	"200"),
					new ComboVals(cdREL_VAL_ISO_250,	"250"),
					new ComboVals(cdREL_VAL_ISO_320,	"320"),
					new ComboVals(cdREL_VAL_ISO_400,	"400"),
					new ComboVals(cdREL_VAL_ISO_500,	"500"),
					new ComboVals(cdREL_VAL_ISO_640,	"640"),
					new ComboVals(cdREL_VAL_ISO_800,	"800"),
					new ComboVals(cdREL_VAL_ISO_1000,	"1000"),
					new ComboVals(cdREL_VAL_ISO_1250,	"1250"),
					new ComboVals(cdREL_VAL_ISO_1600,	"1600"),
					new ComboVals(cdREL_VAL_ISO_2000,	"2000"),
					new ComboVals(cdREL_VAL_ISO_2500,	"2500"),
					new ComboVals(cdREL_VAL_ISO_3200,	"3200"),
					new ComboVals(cdREL_VAL_ISO_4000,	"4000"),
					new ComboVals(cdREL_VAL_ISO_5000,	"5000"),
					new ComboVals(cdREL_VAL_ISO_6400,	"6400"),
				};
			photoEffectVals = new ComboVals[]
				{
					new ComboVals(cdPHOTO_EFFECT_OFF,			"Off"),
					new ComboVals(cdPHOTO_EFFECT_VIVID,			"Vivid"),
					new ComboVals(cdPHOTO_EFFECT_NEUTRAL,		"Neutral"),
					new ComboVals(cdPHOTO_EFFECT_LOW_SHARPENING,"Low Sharpening"),
					new ComboVals(cdPHOTO_EFFECT_SEPIA,			"Sepia"),
					new ComboVals(cdPHOTO_EFFECT_BW,			"BW"),
					new ComboVals(cdPHOTO_EFFECT_CUSTOM,		"Custom"),
					new ComboVals(cdPHOTO_EFFECT_UNKNOWN,		"(Unknown)"),
				};
			shootingModeVals = new ComboVals[]
				{
					new ComboVals(cdSHOOTING_MODE_INVALID,		"(Invalid)"),
					new ComboVals(cdSHOOTING_MODE_AUTO,			"Auto"),
					new ComboVals(cdSHOOTING_MODE_PROGRAM,		"Program"),
					new ComboVals(cdSHOOTING_MODE_TV,			"Tv"),
					new ComboVals(cdSHOOTING_MODE_AV,			"Av"),
					new ComboVals(cdSHOOTING_MODE_MANUAL,		"Manual"),
					new ComboVals(cdSHOOTING_MODE_A_DEP,		"A_DEP"),
					new ComboVals(cdSHOOTING_MODE_M_DEP,		"M_DEP"),
					new ComboVals(cdSHOOTING_MODE_BULB,			"Bulb"),
					new ComboVals(cdSHOOTING_MODE_MANUAL_2,		"Manual"),
					new ComboVals(cdSHOOTING_MODE_FAR_SCENE,	"Far Scene"),
					new ComboVals(cdSHOOTING_MODE_FAST_SHUTTER,	"Fast Shutter"),
					new ComboVals(cdSHOOTING_MODE_SLOW_SHUTTER,	"Slow Shutter"),
					new ComboVals(cdSHOOTING_MODE_NIGHT_SCENE,	"Night Scene"),
					new ComboVals(cdSHOOTING_MODE_GRAY_SCALE,	"Gray Scale"),
					new ComboVals(cdSHOOTING_MODE_SEPIA,		"Sepia"),
					new ComboVals(cdSHOOTING_MODE_PORTRAIT,		"Portrait"),
					new ComboVals(cdSHOOTING_MODE_SPOT,			"Spot"),
					new ComboVals(cdSHOOTING_MODE_MACRO,		"Macro"),
					new ComboVals(cdSHOOTING_MODE_BW,			"BW"),
					new ComboVals(cdSHOOTING_MODE_PANFOCUS,		"Panfocus"),
					new ComboVals(cdSHOOTING_MODE_VIVID,		"Vivid"),
					new ComboVals(cdSHOOTING_MODE_NEUTRAL,		"Neutral"),
				};
			whiteBalanceVals = new ComboVals[]
				{
					new ComboVals(cdWB_AUTO,		"Auto"),
					new ComboVals(cdWB_DAY_LIGHT,	"Daylight"),
					new ComboVals(cdWB_CLOUDY,		"Cloudy"),
					new ComboVals(cdWB_TUNGSTEN,	"Tungsten"),
					new ComboVals(cdWB_FLUORESCENT,	"Fluorescent"),
					new ComboVals(cdWB_FLASH,		"Flash"),
					new ComboVals(cdWB_BW,			"WB_BW"),
					new ComboVals(cdWB_SHADE,		"Shade"),
					new ComboVals(cdWB_KELVIN,		"Kelvin"),
					new ComboVals(cdWB_FLUORESCENT_H,"Fluorescent H"),
				};
				dSizeWidths = new int[20];
				dSizeHeights = new int[20];
				dSizeIndexes = new int[20];
				dSizeLen=0;
		}

		private string GetText(ComboVals[] Vals, int d)
		{
			int count = Vals.Length;
			while (--count >= 0)
			{
				if (Vals[count].dVal == d)
					return Vals[count].strVal;
			}
			return UNKNOW_OPT;
		}
		private string GetImageFormatText(int param)
		{
			string strQ = GetText(imageFormatValsQ, param/1000);
			string strV = GetSizeText(param%1000);
			string strS = GetText(imageFormatValsS, param%1000);
			if (strQ == UNKNOW_OPT || strS == UNKNOW_OPT) 
				return UNKNOW_OPT;
			string str = strQ + "/" + strS + strV;
			return str; 
		}
		private string GetSizeText(int dIndex)
		{
			int i;
			int index = -1;
			for (i=0; i<dSizeLen; i++)
			{
				if (dSizeIndexes[i] == dIndex)
				{
					index = i;
					break;
				}
			}
			if (index == -1) return "";
			return "-" + dSizeWidths[i].ToString() + "x" + dSizeHeights[i].ToString();
		}
		private int GetVal(ComboVals[] Vals, string str)
		{
			int count = Vals.Length;
			while (--count >= 0)
			{
				if (Vals[count].strVal == str)
					return Vals[count].dVal;
			}
			return -1;
		}
		//===============================================================================================//
		/// <summary>
		/// InstallWhatever sets camera to use the "whatever" property whose Canon id number is passed in "param"
		/// If there is a combo box specified, it also sets "param" in the combo box.
		/// Arg1 = combobox or null; 
		/// Arg2 = param.
		/// There are InstallWhatever functions for each camera property selection:
		/// InstallExpoComp, InstallFlashMode, InstallImageFormat, InstallISOSpeed, InstallPhotoEffect,
		/// InstallShootingMode, InstallWhiteBalance.
		/// </summary>
		/// <param name="comboBoxExpoComp"></param>
		/// <param name="param"></param>
		internal void InstallExpoComp(ComboBox comboBox, int param)
		{
			if (comboBox != null) comboBox.SelectedItem = GetText(expoCompVals, param);
			// changing SelectedItem cases CanonSDKCall_Set...
			else CanonSDKCall_SetExpoComp((uint)param);
		}
		internal void InstallFlashMode(ComboBox comboBox, int param)
		{
			if (comboBox != null) comboBox.SelectedItem = GetText(flashModeVals, param);
			// changing SelectedItem cases CanonSDKCall_Set...
			else CanonSDKCall_SetFlash((uint)param);
		}
		internal void InstallImageFormat(ComboBox comboBox, ref int param)
		{
			if (comboBox != null) 
			{
				string str = GetImageFormatText(param);
				// see if text is one of the combo box choices
				bool IsIn = false;
				foreach(string s in comboBox.Items)
					if (str == s) IsIn = true;
				if (IsIn)
					comboBox.SelectedItem = str;
				else
					if (comboBox.Items.Count > 0) comboBox.SelectedIndex = comboBox.Items.Count-1;
			}
			// changing SelectedItem cases CanonSDKCall_Set...
			else CanonSDKCall_SetImageFormat((uint)param);
		}
		internal void InstallISOSpeed(ComboBox comboBox, int param)
		{
			if (comboBox != null) comboBox.SelectedItem = GetText(ISOSpeedVals, param);
			// changing SelectedItem cases CanonSDKCall_Set...
			else CanonSDKCall_SetISOSpeed((uint)param);
		}
		internal void InstallPhotoEffect(ComboBox comboBox, int param)
		{
			if (comboBox != null) comboBox.SelectedItem = GetText(photoEffectVals, param);
			// changing SelectedItem cases CanonSDKCall_Set...
			else CanonSDKCall_SetPhotoEffect((uint)param);
		}
		internal void InstallShootingMode(ComboBox comboBox, int param)
		{
			if (comboBox != null) comboBox.SelectedItem = GetText(shootingModeVals, param);
			// changing SelectedItem cases CanonSDKCall_Set...
			else CanonSDKCall_SetShootingMode((uint)param);
		}
		internal void InstallWhiteBalance(ComboBox comboBox, int param)
		{
			if (comboBox != null) comboBox.SelectedItem = GetText(whiteBalanceVals, param);
			// changing SelectedItem cases CanonSDKCall_Set...
			else CanonSDKCall_SetWhiteBalance((uint)param);
			// syh this is failing when using sepia or b&w
		}
		//========================================================================================//
		/// <summary>
		/// Utility for PopulateWhatever routines - fills in combobox items
		/// </summary>
		/// <param name="comboBox"></param>
		/// <param name="Len"></param>
		/// <param name="whateverVals"></param>
		/// <param name="dValues"></param>
		/// <param name="dCurrValue"></param>
		private void Populator(ComboBox comboBox, int Len, ComboVals[] whateverVals, int[] dValues)
		{
			int i;
			if (comboBox != null)
			{
                string str;
				comboBox.Items.Clear();
				for (i=0; i<Len; i++)
				{
                    str = GetText(whateverVals, dValues[i]);
					if (str != UNKNOW_OPT)
						comboBox.Items.Add(str);
				}
			}
		}
		/// <summary>
		/// PopulateWhatever fills in the choices for the combobox and returns the current camera setting 
		/// and the default value that the DCS program likes.
		/// If no camera is connected, then the default value only is returned
		/// Arg1 = combobox or null
		/// Arg2 = current camera setting returned - or -1 if no camera is connected
		/// Arg3 = programs default value
		/// If combobox is null, Populator() does nothng - just return current value and default value.
		/// There are PopulateWhatever functions for each camera property selection:
		/// PopulateExpoComp, PopulateFlashMode, PopulateImageFormat, PopulateISOSpeed, PopulatePhotoEffect,
		/// PopulateShootingMode, PopulateWhiteBalance.
		/// The CanonSDKCall_EnumWhatever( routne) in the C++ DLL will handle the case if no camera is attached
		/// </summary>
		internal bool PopulateExpoComp(ComboBox comboBox, ref int dCurrValue, out int dDefaultValue)
		{
			int Len=0;
			int[] dValues = new int[30];

			bool bRet1 = CanonSDKCall_EnumExpoComp(30, out Len, dValues, ref dCurrValue, out dDefaultValue);
			if (!bRet1) return false;
			Populator(comboBox, Len, expoCompVals, dValues); // does nothing if comboBox is null
			return true;
		}
		internal bool PopulateFlashMode(ComboBox comboBox, ref int dCurrValue, out int dDefaultValue)
		{
			int Len=0;
			int[] dValues = new int[20];

			bool bRet1 = CanonSDKCall_EnumFlash(20, out Len, dValues, ref dCurrValue, out dDefaultValue);
			if (!bRet1) return false;
			Populator(comboBox, Len, flashModeVals, dValues); // does nothing if comboBox is null
			return true;
		}
		internal bool PopulateImageFormat(ComboBox comboBox, ref int dCurrValue, out int dDefaultValue)
		{
			int i, Len=0; //, dDefaultValue, dCurrValue;
			int[] dValues = new int[20];
			string str;

			bool bRet1 = CanonSDKCall_EnumImageFormat(20, out Len, dValues, ref dCurrValue, out dDefaultValue);
			if (!bRet1) return false;

			dSizeLen=0;
			bRet1 = CanonSDKCall_EnumImageSizes(20, out dSizeLen, dSizeWidths, dSizeHeights, dSizeIndexes);
			if (!bRet1) return false;

			// Populator()only works for simple mapping between dValues and strings 
			if (comboBox != null)
			{
				comboBox.Items.Clear();
				for (i=0; i<Len; i++)
				{
					str = GetImageFormatText(dValues[i]);
					if (str != UNKNOW_OPT) comboBox.Items.Add(str);
				}
			}
			return true;
		}
		internal bool PopulateISOSpeed(ComboBox comboBox, ref int dCurrValue, out int dDefaultValue)
		{
			int Len=0;
			int[] dValues = new int[20];

			bool bRet1 = CanonSDKCall_EnumISOSpeed(20, out Len, dValues, ref dCurrValue, out dDefaultValue);
			if (!bRet1) return false;
			Populator(comboBox, Len, ISOSpeedVals, dValues); // does nothing if comboBox is null
			return true;
		}
		internal bool PopulatePhotoEffect(ComboBox comboBox, ref int dCurrValue, out int dDefaultValue)
		{
			int Len=0;
			int[] dValues = new int[20];

			bool bRet1 = CanonSDKCall_EnumPhotoEffect(20, out Len, dValues, ref dCurrValue, out dDefaultValue);
			if (!bRet1) return false;
			Populator(comboBox, Len, photoEffectVals, dValues); // does nothing if comboBox is null
			return true;
		}
		internal bool PopulateShootingMode(ComboBox comboBox, ref int dCurrValue, out int dDefaultValue)
		{
			int Len=0;
			int[] dValues = new int[20];

			bool bRet1 = CanonSDKCall_EnumShootingMode(20, out Len, dValues, ref dCurrValue, out dDefaultValue);
			if (!bRet1) return false;
			Populator(comboBox, Len, shootingModeVals, dValues); // does nothing if comboBox is null
			return true;
		}
		internal bool PopulateWhiteBalance(ComboBox comboBox, ref int dCurrValue, out int dDefaultValue)
		{
			int Len=0;
			int[] dValues = new int[20];

			bool bRet1 = CanonSDKCall_EnumWhiteBalance(20, out Len, dValues, ref dCurrValue, out dDefaultValue);
			if (!bRet1) return false;
			Populator(comboBox, Len, whiteBalanceVals, dValues); // does nothing if comboBox is null
			return true;
		}

		//==============================================================================//
		/// <summary>
		/// ExtractWhatever gets the text from the specified control and returns the associate Canon int number. 
		/// There are ExtractWhatever functions for each camera property selection:
		/// ExtractExpoComp, ExtractFlashMode, ExtractImageFormat, ExtractISOSpeed, ExtractPhotoEffect,
		/// ExtractShootingMode, ExtractWhiteBalance.
		/// </summary>
		/// <param name="comboBoxExpoComp"></param>
		/// <param name="param"></param>
		internal void ExtractExpoComp(System.Windows.Forms.ComboBox comboBox, out int param)
		{
			param = GetVal(expoCompVals, comboBox.Text);
		}
		internal void ExtractFlashMode(System.Windows.Forms.ComboBox comboBox, out int param)
		{
			param = GetVal(flashModeVals, comboBox.Text);
		}
		internal void ExtractImageFormat(string strComboBoxText, out int param)
		{	// parse format Quality/Size-WidthxHeight
			string strQ;
			string strS;
			int pos1, pos2;
			pos1 = strComboBoxText.IndexOf("/");
			pos2 = strComboBoxText.IndexOf("-");
			strQ = strComboBoxText.Substring(0,pos1);
			if (pos2 > pos1)
				strS = strComboBoxText.Substring(pos1+1, pos2-pos1-1);
			else
				strS = strComboBoxText.Substring(pos1+1);
			param = GetVal(imageFormatValsQ, strQ)*1000 + GetVal(imageFormatValsS, strS);
		}
		internal void ExtractISOSpeed(System.Windows.Forms.ComboBox comboBox, out int param)
		{
			param = GetVal(ISOSpeedVals, comboBox.Text);
		}
		internal void ExtractPhotoEffect(System.Windows.Forms.ComboBox comboBox, out int param)
		{
			param = GetVal(photoEffectVals, comboBox.Text);
		}
		internal void ExtractShootingMode(System.Windows.Forms.ComboBox comboBox, out int param)
		{
			param = GetVal(shootingModeVals, comboBox.Text);
		}
		internal void ExtractWhiteBalance(System.Windows.Forms.ComboBox comboBox, out int param)
		{
			param = GetVal(whiteBalanceVals, comboBox.Text);
		}
	}
}
