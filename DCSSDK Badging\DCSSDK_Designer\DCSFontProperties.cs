using System;
using System.Collections;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Windows.Forms;

namespace DCSDEV.DCSDesigner
{
	/// <summary>
	/// Summary description for DCSFontProperties.
	/// </summary>
    public class DCSFontProperties : System.Windows.Forms.UserControl
	{
		private char m_cSimple = 'T';	// I=Icao MRZ mode; T=text; B=Barcode - each case has diff font/appearance options visible
		private Color m_color = Color.Gray;
		private Font m_font = null;
		private int m_iUnits = 0;
		private int m_iLineSpacing = 0;

		private System.Windows.Forms.Button buttonFont;
		private System.Windows.Forms.Label labelSample;
		private System.Windows.Forms.Button buttonChoose;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.PictureBox pictureBox1;
		private System.Windows.Forms.ColorDialog colorDialog1;
		private System.Windows.Forms.Label labelFont;
		private System.Windows.Forms.TextBox textBoxFontSize;
		private System.Windows.Forms.TextBox tbLineSpacing;
		private System.Windows.Forms.Label labelLineSpacing;
		private CheckBox checkSizeToFit;
		private CheckBox checkWordWrap;
		private CheckBox checkBoxShadow;
		private PictureBox pictureBoxShadow;
		private Button buttonChooseShadow;
		private Label labelShadowColor;
		/// <summary> 
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		public DCSFontProperties()
		{
			// This call is required by the Windows.Forms Form Designer.
			InitializeComponent();

			// TODO: Add any initialization after the InitializeComponent call
			m_font = null;	//new Font("Microsoft Sans Serif", 8.0F, System.Drawing.FontStyle.Regular);
			this.AdjustViz();
		}

		private void AdjustViz()
		{
			this.checkSizeToFit.Visible = (m_cSimple == 'T');
			this.checkBoxShadow.Visible = this.checkWordWrap.Visible = (m_cSimple == 'T');
			this.tbLineSpacing.Visible = this.labelLineSpacing.Visible = (m_cSimple == 'I' || m_cSimple == 'T');

			if (!this.checkBoxShadow.Visible) this.checkBoxShadow.Checked = false;
			bool bViz = (this.checkBoxShadow.Checked);
			this.labelShadowColor.Visible = bViz;
			this.buttonChooseShadow.Visible = bViz;
			this.pictureBoxShadow.Visible = bViz;
		}

		/// <summary> 
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Component Designer generated code
		/// <summary> 
		/// Required method for Designer support - do not modify 
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			this.buttonFont = new System.Windows.Forms.Button();
			this.labelSample = new System.Windows.Forms.Label();
			this.buttonChoose = new System.Windows.Forms.Button();
			this.label2 = new System.Windows.Forms.Label();
			this.pictureBox1 = new System.Windows.Forms.PictureBox();
			this.colorDialog1 = new System.Windows.Forms.ColorDialog();
			this.labelFont = new System.Windows.Forms.Label();
			this.textBoxFontSize = new System.Windows.Forms.TextBox();
			this.tbLineSpacing = new System.Windows.Forms.TextBox();
			this.labelLineSpacing = new System.Windows.Forms.Label();
			this.checkSizeToFit = new System.Windows.Forms.CheckBox();
			this.checkWordWrap = new System.Windows.Forms.CheckBox();
			this.checkBoxShadow = new System.Windows.Forms.CheckBox();
			this.pictureBoxShadow = new System.Windows.Forms.PictureBox();
			this.buttonChooseShadow = new System.Windows.Forms.Button();
			this.labelShadowColor = new System.Windows.Forms.Label();
			((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
			((System.ComponentModel.ISupportInitialize)(this.pictureBoxShadow)).BeginInit();
			this.SuspendLayout();
			// 
			// buttonFont
			// 
			this.buttonFont.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
						| System.Windows.Forms.AnchorStyles.Right)));
			this.buttonFont.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonFont.Location = new System.Drawing.Point(8, 4);
			this.buttonFont.Name = "buttonFont";
			this.buttonFont.Size = new System.Drawing.Size(74, 23);
			this.buttonFont.TabIndex = 0;
			this.buttonFont.Text = "Font";
			this.buttonFont.Click += new System.EventHandler(this.buttonFont_Click);
			// 
			// labelSample
			// 
			this.labelSample.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
						| System.Windows.Forms.AnchorStyles.Left)
						| System.Windows.Forms.AnchorStyles.Right)));
			this.labelSample.Location = new System.Drawing.Point(8, 49);
			this.labelSample.Name = "labelSample";
			this.labelSample.Size = new System.Drawing.Size(146, 57);
			this.labelSample.TabIndex = 3;
			this.labelSample.Text = "ABC abc";
			this.labelSample.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
			// 
			// buttonChoose
			// 
			this.buttonChoose.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.buttonChoose.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonChoose.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.buttonChoose.Location = new System.Drawing.Point(52, 109);
			this.buttonChoose.Name = "buttonChoose";
			this.buttonChoose.Size = new System.Drawing.Size(24, 20);
			this.buttonChoose.TabIndex = 5;
			this.buttonChoose.Text = ">";
			this.buttonChoose.Click += new System.EventHandler(this.buttonChoose_Click);
			// 
			// label2
			// 
			this.label2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.label2.Location = new System.Drawing.Point(10, 112);
			this.label2.Name = "label2";
			this.label2.Size = new System.Drawing.Size(48, 16);
			this.label2.TabIndex = 4;
			this.label2.Text = "Color";
			// 
			// pictureBox1
			// 
			this.pictureBox1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)
						| System.Windows.Forms.AnchorStyles.Right)));
			this.pictureBox1.BackColor = System.Drawing.Color.Black;
			this.pictureBox1.Location = new System.Drawing.Point(110, 105);
			this.pictureBox1.Name = "pictureBox1";
			this.pictureBox1.Size = new System.Drawing.Size(44, 25);
			this.pictureBox1.TabIndex = 58;
			this.pictureBox1.TabStop = false;
			// 
			// labelFont
			// 
			this.labelFont.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
						| System.Windows.Forms.AnchorStyles.Right)));
			this.labelFont.Location = new System.Drawing.Point(8, 29);
			this.labelFont.Name = "labelFont";
			this.labelFont.Size = new System.Drawing.Size(146, 15);
			this.labelFont.TabIndex = 2;
			this.labelFont.Text = "font name";
			this.labelFont.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
			// 
			// textBoxFontSize
			// 
			this.textBoxFontSize.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.textBoxFontSize.Location = new System.Drawing.Point(114, 4);
			this.textBoxFontSize.Name = "textBoxFontSize";
			this.textBoxFontSize.Size = new System.Drawing.Size(40, 20);
			this.textBoxFontSize.TabIndex = 1;
			// 
			// tbLineSpacing
			// 
			this.tbLineSpacing.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
			this.tbLineSpacing.Location = new System.Drawing.Point(112, 200);
			this.tbLineSpacing.Name = "tbLineSpacing";
			this.tbLineSpacing.Size = new System.Drawing.Size(42, 20);
			this.tbLineSpacing.TabIndex = 12;
			this.tbLineSpacing.Text = "0.000";
			// 
			// labelLineSpacing
			// 
			this.labelLineSpacing.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.labelLineSpacing.Location = new System.Drawing.Point(8, 202);
			this.labelLineSpacing.Name = "labelLineSpacing";
			this.labelLineSpacing.Size = new System.Drawing.Size(88, 16);
			this.labelLineSpacing.TabIndex = 11;
			this.labelLineSpacing.Text = "Line spacing";
			// 
			// checkSizeToFit
			// 
			this.checkSizeToFit.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.checkSizeToFit.ImeMode = System.Windows.Forms.ImeMode.NoControl;
			this.checkSizeToFit.Location = new System.Drawing.Point(11, 161);
			this.checkSizeToFit.Name = "checkSizeToFit";
			this.checkSizeToFit.Size = new System.Drawing.Size(133, 16);
			this.checkSizeToFit.TabIndex = 9;
			this.checkSizeToFit.Text = "Reduce size to fit";
			// 
			// checkWordWrap
			// 
			this.checkWordWrap.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.checkWordWrap.ImeMode = System.Windows.Forms.ImeMode.NoControl;
			this.checkWordWrap.Location = new System.Drawing.Point(11, 182);
			this.checkWordWrap.Name = "checkWordWrap";
			this.checkWordWrap.Size = new System.Drawing.Size(133, 17);
			this.checkWordWrap.TabIndex = 10;
			this.checkWordWrap.Text = "Word wrap";
			// 
			// checkBoxShadow
			// 
			this.checkBoxShadow.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.checkBoxShadow.ImeMode = System.Windows.Forms.ImeMode.NoControl;
			this.checkBoxShadow.Location = new System.Drawing.Point(11, 139);
			this.checkBoxShadow.Name = "checkBoxShadow";
			this.checkBoxShadow.Size = new System.Drawing.Size(104, 16);
			this.checkBoxShadow.TabIndex = 6;
			this.checkBoxShadow.Text = "Shadow";
			this.checkBoxShadow.Click += new System.EventHandler(this.checkBoxShadow_Click);
			// 
			// pictureBoxShadow
			// 
			this.pictureBoxShadow.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)
						| System.Windows.Forms.AnchorStyles.Right)));
			this.pictureBoxShadow.BackColor = System.Drawing.Color.Gray;
			this.pictureBoxShadow.Location = new System.Drawing.Point(110, 134);
			this.pictureBoxShadow.Name = "pictureBoxShadow";
			this.pictureBoxShadow.Size = new System.Drawing.Size(44, 25);
			this.pictureBoxShadow.TabIndex = 66;
			this.pictureBoxShadow.TabStop = false;
			// 
			// buttonChooseShadow
			// 
			this.buttonChooseShadow.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.buttonChooseShadow.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonChooseShadow.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.buttonChooseShadow.Location = new System.Drawing.Point(110, 139);
			this.buttonChooseShadow.Name = "buttonChooseShadow";
			this.buttonChooseShadow.Size = new System.Drawing.Size(24, 20);
			this.buttonChooseShadow.TabIndex = 8;
			this.buttonChooseShadow.Text = ">";
			this.buttonChooseShadow.Click += new System.EventHandler(this.buttonChooseShadow_Click);
			// 
			// labelShadowColor
			// 
			this.labelShadowColor.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.labelShadowColor.Location = new System.Drawing.Point(75, 141);
			this.labelShadowColor.Name = "labelShadowColor";
			this.labelShadowColor.Size = new System.Drawing.Size(42, 15);
			this.labelShadowColor.TabIndex = 7;
			this.labelShadowColor.Text = "Color";
			// 
			// DCSFontProperties
			// 
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.Controls.Add(this.buttonChooseShadow);
			this.Controls.Add(this.labelShadowColor);
			this.Controls.Add(this.checkBoxShadow);
			this.Controls.Add(this.checkSizeToFit);
			this.Controls.Add(this.checkWordWrap);
			this.Controls.Add(this.tbLineSpacing);
			this.Controls.Add(this.labelLineSpacing);
			this.Controls.Add(this.textBoxFontSize);
			this.Controls.Add(this.labelFont);
			this.Controls.Add(this.pictureBox1);
			this.Controls.Add(this.buttonChoose);
			this.Controls.Add(this.label2);
			this.Controls.Add(this.labelSample);
			this.Controls.Add(this.buttonFont);
			this.Controls.Add(this.pictureBoxShadow);
			this.Name = "DCSFontProperties";
			this.Size = new System.Drawing.Size(162, 220);
			((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
			((System.ComponentModel.ISupportInitialize)(this.pictureBoxShadow)).EndInit();
			this.ResumeLayout(false);
			this.PerformLayout();

		}
		#endregion

		private void buttonFont_Click(object sender, System.EventArgs e)
		{
			FontDialog fontDialog1 = new FontDialog();
			fontDialog1.Font = this.m_font;
			DialogResult result = fontDialog1.ShowDialog(this);
			if (result == DialogResult.Cancel) return;
			this.m_font = fontDialog1.Font;
			this.labelSample.Font = this.m_font;
			this.labelFont.Text = m_font.Name;
			this.textBoxFontSize.Text = m_font.Size.ToString();
		}

		private void buttonChoose_Click(object sender, System.EventArgs e)
		{
			this.colorDialog1.Color = m_color;
			System.Windows.Forms.DialogResult result = this.colorDialog1.ShowDialog(this);
			if (result != DialogResult.OK) return;
			m_color = this.colorDialog1.Color;
			this.pictureBox1.BackColor = m_color;
		}
		// If true the font properties include only those attribute appropriate for barcodes and ICAO MRZ
		public char DCSSimple
		{
			set
			{
				if (this.m_cSimple == value) return;
				this.m_cSimple = value;
				this.AdjustViz();
			}
			get { return this.m_cSimple; }
		}

		public Color DCSColor
		{
			set 
			{ 
				m_color = value;
				this.pictureBox1.BackColor = m_color;
			}
			get { return m_color; }
		}

		public Font DCSFont
		{
			set 
			{
				m_font = value;
				if (m_font != null)
				{
					this.labelSample.Font = m_font;
					this.labelFont.Text = m_font.Name;
					this.textBoxFontSize.Text = m_font.Size.ToString();
				}
			}
			get 
			{
				//syh m_font = new Font(m_font.FontFamily, (float)Convert.ToDouble(this.textBoxFontSize.Text), m_font.Style);
				return m_font; 
			}
		}

		public int Units
		{
			// 0 = inches; 1 = MM
			get 
			{ 
				return m_iUnits; 
			}
			set 
			{
				if (value < 0) value = 0;
				if (value > 1) value = 1;
				if (m_iUnits != value)
				{
					int spacing = this.LineSpacing;	// extract and repopulate will be required to convert units
					m_iUnits = value;
					this.LineSpacing = spacing;
				}
			}
		}

		public int LineSpacing
		{
			set 
			{
				m_iLineSpacing = value; 
				if (m_iUnits == 0)	// inches are displayed
				{
					this.tbLineSpacing.Text = ((double)m_iLineSpacing / 100.0).ToString("0.000");
				}
				else
				{
					this.tbLineSpacing.Text = ((double)m_iLineSpacing * 0.254).ToString("0.000");
				}
			}
			get 
			{
				if (m_iUnits == 0)	// inches are displayed
				{
					m_iLineSpacing = (int)(Convert.ToDouble(this.tbLineSpacing.Text) * 100.0 );
				}
				else
				{
					m_iLineSpacing = (int)(Convert.ToDouble(this.tbLineSpacing.Text) / .254 );
				}
				return m_iLineSpacing;
			}
		}
		public bool DCSWordWrap
		{
			set { this.checkWordWrap.Checked = value; }
			get { return this.checkWordWrap.Checked;  }
		}
		public bool DCSSizeToFit
		{
			set { this.checkSizeToFit.Checked = value; }
			get { return this.checkSizeToFit.Checked; }
		}
		public bool DCSShadow
		{
			set 
			{
				if (this.checkBoxShadow.Checked == value) return;
				if (this.m_cSimple == 'T')
				{
					this.checkBoxShadow.Checked = value;
					this.AdjustViz();
				}
			}
			get { return this.checkBoxShadow.Checked; }
		}

		private void buttonChooseShadow_Click(object sender, EventArgs e)
		{
			this.colorDialog1.Color = this.pictureBoxShadow.BackColor;
			System.Windows.Forms.DialogResult result = this.colorDialog1.ShowDialog(this);
			if (result != DialogResult.OK) return;
			this.pictureBoxShadow.BackColor = this.colorDialog1.Color;
		}

		public Color DCSShadowColor
		{
			set
			{
				this.pictureBoxShadow.BackColor = value;
			}
			get { return this.pictureBoxShadow.BackColor; }
		}

		private void checkBoxShadow_Click(object sender, EventArgs e)
		{
			//checkBoxShadow.Checked = !checkBoxShadow.Checked;
			AdjustViz();
		}
	}
}
