using System;
using System.Collections;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Windows.Forms;
using System.Drawing.Drawing2D;

namespace DCSDEV.DCSDesigner
{
	/// <summary>
	/// Summary description for DCSFrameProperties1.
	/// </summary>
    internal class DCSFrameProperties1 : System.Windows.Forms.UserControl
	{
		private System.Windows.Forms.ColorDialog colorDialog1;
		private System.Windows.Forms.NumericUpDown numericUpDownThickness;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.Button buttonChoose;
		private System.Windows.Forms.PictureBox pictureBox2;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.NumericUpDown numericUpDownRadius;
		/// <summary> 
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		public DCSFrameProperties1()
		{
			// This call is required by the Windows.Forms Form Designer.
			InitializeComponent();

			// TODO: Add any initialization after the InitializeComponent call
			this.AdjustVisibilities();
		}

		/// <summary> 
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Component Designer generated code
		/// <summary> 
		/// Required method for Designer support - do not modify 
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			this.colorDialog1 = new System.Windows.Forms.ColorDialog();
			this.numericUpDownThickness = new System.Windows.Forms.NumericUpDown();
			this.label1 = new System.Windows.Forms.Label();
			this.label2 = new System.Windows.Forms.Label();
			this.buttonChoose = new System.Windows.Forms.Button();
			this.pictureBox2 = new System.Windows.Forms.PictureBox();
			this.label3 = new System.Windows.Forms.Label();
			this.numericUpDownRadius = new System.Windows.Forms.NumericUpDown();
			((System.ComponentModel.ISupportInitialize)(this.numericUpDownThickness)).BeginInit();
			((System.ComponentModel.ISupportInitialize)(this.numericUpDownRadius)).BeginInit();
			this.SuspendLayout();
			// 
			// numericUpDownThickness
			// 
			this.numericUpDownThickness.Location = new System.Drawing.Point(120, 0);
			this.numericUpDownThickness.Name = "numericUpDownThickness";
			this.numericUpDownThickness.Size = new System.Drawing.Size(48, 20);
			this.numericUpDownThickness.TabIndex = 18;
			// 
			// label1
			// 
			this.label1.Location = new System.Drawing.Point(8, 0);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(104, 24);
			this.label1.TabIndex = 1;
			this.label1.Text = "Frame thickness";
			this.label1.TextAlign = System.Drawing.ContentAlignment.TopRight;
			// 
			// label2
			// 
			this.label2.Location = new System.Drawing.Point(8, 56);
			this.label2.Name = "label2";
			this.label2.Size = new System.Drawing.Size(88, 23);
			this.label2.TabIndex = 2;
			this.label2.Text = "Color";
			this.label2.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
			// 
			// buttonChoose
			// 
			this.buttonChoose.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonChoose.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((System.Byte)(0)));
			this.buttonChoose.Location = new System.Drawing.Point(120, 56);
			this.buttonChoose.Name = "buttonChoose";
			this.buttonChoose.Size = new System.Drawing.Size(24, 24);
			this.buttonChoose.TabIndex = 14;
			this.buttonChoose.Text = ">";
			this.buttonChoose.Click += new System.EventHandler(this.buttonChoose_Click);
			// 
			// pictureBox2
			// 
			this.pictureBox2.BackColor = System.Drawing.SystemColors.ControlDark;
			this.pictureBox2.Location = new System.Drawing.Point(8, 88);
			this.pictureBox2.Name = "pictureBox2";
			this.pictureBox2.Size = new System.Drawing.Size(160, 16);
			this.pictureBox2.TabIndex = 17;
			this.pictureBox2.TabStop = false;
			// 
			// label3
			// 
			this.label3.Location = new System.Drawing.Point(8, 24);
			this.label3.Name = "label3";
			this.label3.Size = new System.Drawing.Size(104, 24);
			this.label3.TabIndex = 19;
			this.label3.Text = "radius percent";
			this.label3.TextAlign = System.Drawing.ContentAlignment.TopRight;
			// 
			// numericUpDownRadius
			// 
			this.numericUpDownRadius.Increment = new System.Decimal(new int[] {
																				  10,
																				  0,
																				  0,
																				  0});
			this.numericUpDownRadius.Location = new System.Drawing.Point(120, 24);
			this.numericUpDownRadius.Name = "numericUpDownRadius";
			this.numericUpDownRadius.Size = new System.Drawing.Size(48, 20);
			this.numericUpDownRadius.TabIndex = 20;
			// 
			// DCSFrameProperties1
			// 
			this.Controls.Add(this.label3);
			this.Controls.Add(this.numericUpDownRadius);
			this.Controls.Add(this.pictureBox2);
			this.Controls.Add(this.buttonChoose);
			this.Controls.Add(this.label2);
			this.Controls.Add(this.label1);
			this.Controls.Add(this.numericUpDownThickness);
			this.Name = "DCSFrameProperties1";
			this.Size = new System.Drawing.Size(176, 112);
			((System.ComponentModel.ISupportInitialize)(this.numericUpDownThickness)).EndInit();
			((System.ComponentModel.ISupportInitialize)(this.numericUpDownRadius)).EndInit();
			this.ResumeLayout(false);

		}
		#endregion

		private void AdjustVisibilities()
		{
			if (this.numericUpDownThickness.Value == 0) this.pictureBox2.Visible = false;
			else this.pictureBox2.Visible = true;
			this.pictureBox2.Height = Math.Min((int)this.numericUpDownThickness.Value, 16);
		}

		private void buttonChoose_Click(object sender, System.EventArgs e)
		{
			this.colorDialog1.Color = this.pictureBox2.BackColor;
			System.Windows.Forms.DialogResult result = this.colorDialog1.ShowDialog(this);
			if (result != DialogResult.OK) return;
			this.pictureBox2.BackColor = this.colorDialog1.Color;
		}

		private void numericUpDownThickness_ValueChanged(object sender, System.EventArgs e)
		{
			this.AdjustVisibilities();
		}

		public Color Color
		{
			get {return this.pictureBox2.BackColor;}
			set{this.pictureBox2.BackColor = value;}
		}

		public int Radius
		{
			get {return (int)this.numericUpDownRadius.Value;}
			set
			{
				this.numericUpDownRadius.Value = value;
				this.AdjustVisibilities();
			}
		}

		public int Thickness
		{
			get {return (int)this.numericUpDownThickness.Value;}
			set
			{
				this.numericUpDownThickness.Value = value;
				this.AdjustVisibilities();
			}
		}
	}
}
