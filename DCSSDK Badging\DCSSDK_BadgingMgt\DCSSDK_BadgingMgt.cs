/************************************************************************************** 
 * syh notes
 * I would like to change internal units to 1000 inch
 * 
 * background image of an object should fill the obj frame if the source image does not (!= IDServ)
 * i am not happy about loading all images when the layout is loaded
 * 
 * need to provide way to report print enqueing problems
 * thinking about not having ini read all over the place
 * two ways to parse dde message is DIRTY
 * Sax/CommStudio barcode licensing is a problem
 * with my epson 0,0 is not at 0,0; causing verso mis-alignment
 * use ResourceManager.GetString("token") for internationalization
 ****************************************************************************************/

using System;
using System.Collections;
using System.Windows.Forms;
using System.Drawing;
using System.Drawing.Printing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.IO;

using DCSDEV;
using DCSDEV.DCSDesign;

namespace DCSSDK.BadgingMgt
{
	/// <summary>
	/// Summary description for DCSSDK_BadgingMgt.
	/// THE DCSSDK_BadgingMgt CLASS HAS public SUPPORT FOR:
	///   void ClosePreview()
	///   void DesignBadge(string DesignName) or null
	///   void EditBadgingProperties()
	///   int EncodeChip(string strBadgeDotDatName, out string strChipID)
    ///   int EnqueueBadge(string strBadgeDotDatName) returns ref no (is planned to be used to make status requests about a specific badge)
	///   int GetBadgePrintStatus(int iRefNo) - not yet implemented
	///   void PreviewBadge(string strBadgeDotDatName, string strLabel, int iSide)
	///   void PrintQueuePause()
	///   void PrintQueueStart()
    ///   void StartDCSPrinter() start DCSPrinter if its not already running; then call PrintQueueStart() to put it in active printing mode - or not.
	///  
	/// </summary>
	public class DCSSDK_BadgingMgt : System.ComponentModel.Component
	{
        /*****************************************
        [ThreadStatic]
        private static bool m_bDoRefresh = false;
        [ThreadStatic]
        private static bool m_bDoInit = false;
        [ThreadStatic]
        private static bool m_bDoActivate = false;
        [ThreadStatic]
        private static bool m_bDoPause = false;
        [ThreadStatic]
        private static bool m_bDoShowMinimize = false;
        [ThreadStatic]
        private static bool m_bDoShowNormal = false;
        **********************************************/

        private static bool m_bDoRefresh = false;
        private static bool m_bDoInit = false;
        private static bool m_bDoActivate = false;
        private static bool m_bDoPause = false;
        private static bool m_bDoPrintNow = false;
        private static bool m_bDoShowMinimize = false;
        private static bool m_bDoShowNormal = false;
        private static bool m_bDoStop = false;

        private static System.Threading.Thread m_thread = null;
        
        private static int m_iLastFileNumber = 1;


		//		private const double dBadgeDPI = 360;
		private const double dMMPerInch = 25.4;
		private const double dMMPerInch100 = .254;

		//private int m_offsetX;
		//private int m_offsetY;

		private ArrayList m_sideBitmaps = null;

		private DCSSDK.BadgingMgt.BadgePreviewDisplay m_previewDisplay = null;

		public DCSSDK_BadgingMgt()
		{
			//
			// TODO: Add constructor logic here
			//
			m_sideBitmaps = new ArrayList();
		}

		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
                if (m_thread != null)
                {
                    this.PrintStop();
                    m_thread.Abort();
                    m_thread.Join();
                    m_thread = null;
                }
                if (m_previewDisplay != null)
				{
					m_previewDisplay.Close();
					m_previewDisplay.Dispose();
				}
				// m_sideBitmaps were set in m_previewDisplay and are Disposed from there
				this.m_sideBitmaps.Clear();
			}
			base.Dispose( disposing );
		}

		public void ClosePreview()
		{
			if (m_previewDisplay != null)
			{
				m_previewDisplay.Close();
				if (this.m_sideBitmaps != null)
				{
					foreach(Bitmap bm in this.m_sideBitmaps)
						bm.Dispose();
					this.m_sideBitmaps.Clear();
				}
				m_previewDisplay.Dispose();
				m_previewDisplay = null;
			}
		}
		
		public void DesignBadge(string DesignName)
		{
			if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.SystemMgt)) return;
			this.ClosePreview();
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.BadgeLayout, true)) return;

			try
			{
				using (DCSDEV.DCSDesigner.DCSDesignerMain dlgLayout = new DCSDEV.DCSDesigner.DCSDesignerMain())
				{
					dlgLayout.ShowDialog();
				}
			}
			catch (Exception ex)
			{
				DCSDEV.DCSMsg.Show(ex);
			}
		}
		
		public void EditBadgingProperties()
		{
			this.ClosePreview();

            using (DCSDEV.PrintProperties.BadgingMgtForm printProp = new DCSDEV.PrintProperties.BadgingMgtForm())
            {
                printProp.ShowDialog();
            }
            m_bDoInit = true;   // signal call to dlgPrinter.DoInit();
		}

        /// <summary>
        /// Encode chip with the data in the Badge Data File according to the specified formula.
        /// </summary>
        /// strSourceDatName = name of data source file
		/// strChipID set to chip's unique ID if encode is ok, otherwise null
        /// <returns 0 if cancel; pos if OK; neg if error</returns>
		public int EncodeChip(string strSourceDatName, out string strChipID)
		{
			strChipID = null;
			bool bRet = true;

			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.EncodeChips, true)) return -1;

            if (!System.IO.File.Exists(strSourceDatName))
            {
				DCSMsg.Show(String.Format("Cannot find chip encoding data source file '{0}'.", strSourceDatName), MessageBoxIcon.Error);
                return -1;
            }

            DCSDEV.ParameterStore ps = new ParameterStore("DCSSDK_Mgt");
			DCSDEV.DCSChipIF.DCSDEV_ChipIF.ChipIFType eChipIFType = (DCSDEV.DCSChipIF.DCSDEV_ChipIF.ChipIFType)ps.GetIntParameter("ChipIFType", 0);

			string strFinalSource = null;
			string strFormula = null;
			string strDocBIN = null;
			if (eChipIFType == DCSDEV.DCSChipIF.DCSDEV_ChipIF.ChipIFType.MIFARE_RFID_CHIP_FORMULA)
			{
				DCSDEV.DCSDesign.DCSBadgeDataset badgeData = null;
				try
				{
					/////////////////////////////////////////////////////////
					// initialize m_lFieldNames from AllField.txt
					/////////////////////////////////////////////////////////
					string allfield;
					allfield = System.IO.Path.Combine(ps.m_strDCSInstallDirectory, "AllField.txt");
					if (!File.Exists(allfield))
					{
						DCSMsg.Show("The table of field names " + allfield + " is missing.", MessageBoxIcon.Error);
						return -1;
					}

					badgeData = new DCSDEV.DCSDesign.DCSBadgeDataset();
					bRet = badgeData.LoadAll(allfield, strSourceDatName);
					if (!bRet) return -1;

					using (DCSDEV.DCSDesign.DCSDesign design = new DCSDesign())
					{
						bRet = design.ReadBadgeDesign(badgeData.m_strDesignName);
						if (bRet)
						{
							strFormula = design.FormulaChip;
							if (strFormula == null || strFormula == "")
								DCSMsg.Show(String.Format("Chip encoding formula is not specified in document design '{0}'", badgeData.m_strDesignName), MessageBoxIcon.Error);
						}
						else DCSMsg.Show(String.Format("Cannot read document design '{0}'.", badgeData.m_strDesignName), MessageBoxIcon.Error);
					}
				}
				catch (Exception ex)
				{
					DCSMsg.Show(String.Format("Cannot obtain chip encoding formula from document design '{0}' referenced in badge data file '{1}'", badgeData.m_strDesignName, strSourceDatName), ex);
				}
				if (strFormula == null || strFormula == "") return -1;

				strDocBIN = DCSDEV.DCSDesign.DCSFormula.FormulaEval(strFormula, badgeData);
				if (strDocBIN != null)
				{
					// write the data to encode on the chip temporarily to a file
					string strTempFile = System.IO.Path.Combine(ps.m_strDCSInstallDirectory, "_ChipWrite.dat");
					using (System.IO.StreamWriter stream = new System.IO.StreamWriter(strTempFile))
					{
						stream.Write(strDocBIN);
						stream.Close();
					}
					strFinalSource = strTempFile;
				}
				else return -1;
			}
			else if ((eChipIFType == DCSDEV.DCSChipIF.DCSDEV_ChipIF.ChipIFType.MIFARE_RFID_CHIP_FILE)
				|| (eChipIFType == DCSDEV.DCSChipIF.DCSDEV_ChipIF.ChipIFType.ICAO_CONTACT_CHIP)
				|| (eChipIFType == DCSDEV.DCSChipIF.DCSDEV_ChipIF.ChipIFType.MULTIFILE_CONTACT_CHIP)
				|| (eChipIFType == DCSDEV.DCSChipIF.DCSDEV_ChipIF.ChipIFType.EDL_RFID_CHIP_FILE))
			{
				strFinalSource = strSourceDatName;
			}
			// the chip interface writes from temporary files to the chip

			DCSDEV.SmartChipIO dlg = new DCSDEV.SmartChipIO(SmartChipIO.ChipIOMode.ENCODE, strFinalSource, true);	// true=stand-alone device; false = in printer
			dlg.ShowDialog(null);
			int iRet;
			if (dlg.GetStatus == SmartChipIO.ChipStatus.OK)
			{
				iRet = 1;
				strChipID = dlg.GetChipID;
			}
			else if (dlg.GetStatus == SmartChipIO.ChipStatus.CANCEL) iRet = 0;
			else iRet = -1;
			dlg.Dispose();
			return iRet;
		}

        public string GetBadgeDataPath()
		{
			return DCSDEV.DCSDesignDataAccess.GetDataPath();
		}

		public bool IsReturnDataType(string strBadgeDatName)
		{
			return DCSDEV.DCSPrinter.DCSPrinter.IsScanChipIDType(strBadgeDatName);
		}

		public int PrintBadge(string strBadgeDatName, out string strChipID)
		{
			if (DCSDEV.DCSPrinter.DCSPrinter.IsScanChipIDType(strBadgeDatName))
				return PrintBadgeDirect(strBadgeDatName, out strChipID);
			else
			{
				strChipID = null;
				return EnqueueBadge(strBadgeDatName);
			}
		}

		// return -1=error 0=cancel 1=OK
		public int PrintBadgeDirect(string strBadgeDatName, out string strChipID)
		{
			int iRet;
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.Print, true))
			{
				strChipID = null;
				return -1;
			}
			DCSDEV.DCSPrinter.PrintThread printThread = new DCSDEV.DCSPrinter.PrintThread();
			printThread.m_strBadgeDataFile = strBadgeDatName;
			// use default printThread.m_iBadgesPerSheet = 1;
			// use default printThread.m_arrayQueueSheet = null;
			printThread.Thread_PrintOneCard();
			// return false if canceled
			if (printThread.m_bCardPrintOK)
			{
				strChipID = printThread.m_strChipID;
				iRet = 1;
			}
			else
			{
				strChipID = null;
				iRet = -1;
			}
			/***************************************************************/

			return iRet;
		}

		/// <summary>
		/// Add badge dot dat to printing queue - 
		/// RETURN:-1 if error; 0 if cancel.  Queue reference number if OK - same as file name in queue
		/// 
		/// </summary>
		public int EnqueueBadge(string strBadgeDatName)
		{
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.Print, true)) return -1;

			// get queue path
			DCSDEV.ParameterStore ps = new ParameterStore("DCSSDK_Mgt");
			string strQueueDir = ps.GetStringParameter("PrinterQueueDir", "");
			if (strQueueDir == "")
			{
				strQueueDir = System.IO.Path.Combine(ps.m_strDCSInstallDirectory, "Queue");
				ps.WriteStringParameter("PrinterQueueDir", strQueueDir);
			}
			if (!System.IO.Directory.Exists(strQueueDir)) System.IO.Directory.CreateDirectory(strQueueDir);
			string strQueueFilename = null;

			string strBadgeDatFullName;
			// start with assumption that strBadgeDatName contains a full path, then look in local programs dir
			strBadgeDatFullName = strBadgeDatName;
			if (!File.Exists(strBadgeDatFullName))
			{
				strBadgeDatFullName = System.IO.Path.Combine(ps.m_strDCSInstallDirectory, strBadgeDatName);
				if (!File.Exists(strBadgeDatFullName))
				{
					DCSMsg.Show("The badge data file " + strBadgeDatName + " is missing.", MessageBoxIcon.Error);
					return -1;
				}
			}

			// find next available enqueued file name
			while (true)
			{
				m_iLastFileNumber++;
				strQueueFilename = System.IO.Path.Combine(strQueueDir, m_iLastFileNumber.ToString("00000000") + ".ENQ");
				if (!System.IO.File.Exists(strQueueFilename)) break;
			}

			// copy badge.dat file into queue. (from, to)
			System.IO.File.Copy(strBadgeDatFullName, strQueueFilename);
			DateTime tt = System.IO.File.GetLastAccessTime(strQueueFilename);

			if (m_thread != null) 
			{
                m_bDoRefresh = true;   // signal call to dlgPrinter.DoRefresh();
			}
			return m_iLastFileNumber;
		}

		// returns Badge print status
		public int GetBadgePrintStatus(int iRefNo)
		{
			DCSDEV.DCSMsg.Show("ERROR GetBadgePrintStatus() is not available");
			return 0;
		}

		public void PreviewBadge(string strBadgeDotDatName, string strLabel)
		{
			bool bRet;
			this.ClosePreview();
			
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.Preview, true)) return;

			// Load the badge design and the badge data and merge badge data into badge design
            using (DCSDesign design = new DCSDesign())
            {
                bRet = design.LoadBadgeDesignAndData(strBadgeDotDatName);
                if (!bRet) return;

                // prepare window: get size
                Rectangle rectBadge = design.Bounds;
                Rectangle rectWindow = GetBadgePreviewRectangle();

                // scale preserves sum of width and height so landscape and portrait badges can intermingle
                double dScale = (double)(rectWindow.Width + rectWindow.Height) / (double)(rectBadge.Width + rectBadge.Height);
                rectWindow.Width = DCSMath.TimesDouble(rectBadge.Width, dScale);
                rectWindow.Height = DCSMath.TimesDouble(rectBadge.Height, dScale);

                // if the scale will make very few pixels, use a larger scale
                double dRipScale = dScale;
                if (dRipScale < 1.0 && rectBadge.Width < 400) dRipScale = 1.0;

                // rip to bitmaps
                bRet = this.RipToBitmaps(design, dRipScale);
                if (!bRet) return;

			// put bitmaps to display
			this.SendToDisplay(strLabel, rectWindow);
            }
        }

        // badge preview utility function
		private void SendToDisplay(string strLabel, Rectangle rectWindow)
		{
			m_previewDisplay = new DCSSDK.BadgingMgt.BadgePreviewDisplay();

			//window is filled with image - preserving original ratio
			m_previewDisplay.ClientSize = rectWindow.Size;
			m_previewDisplay.DisplayImageSize = m_previewDisplay.ClientSize;

			m_previewDisplay.SideBitmaps = this.m_sideBitmaps;
			m_previewDisplay.Side = 0;

			// label
			if (strLabel != null && strLabel.Length > 0)
				m_previewDisplay.Label = strLabel;
				
			m_previewDisplay.Location = rectWindow.Location;
			m_previewDisplay.Show();
			m_previewDisplay.Location = rectWindow.Location;
		}

		public void PrintQueuePause()
		{
			if (m_thread == null) return;
			// DCSMsg.Show("PrintPause");
            m_bDoPause = true;      // signal call to dlgPrinter.DoPause();
		}

        /// <summary>
        /// Put DCSPrinter in active printing mode.
        /// </summary>
		public void PrintQueueStart()
		{
			if (m_thread == null) return;
			// DCSMsg.Show("PrintStart");
            m_bDoActivate = true;       // signal call to dlgPrinter.DoActivate();
		}

        public void PrintQueuePrintNow()
        {
            if (m_thread == null) return;
            m_bDoPrintNow = true;
        }

        public void PrintShowNormal()
        {
            m_bDoShowNormal = true;
        }

        public void PrintShowMinimized()
        {
            if (m_thread == null) return;
            m_bDoShowMinimize = true;
        }

        private void PrintStop()
        {
            if (m_thread == null) return;
            m_bDoStop = true;
        }

        /// <summary>
        /// Start DCSPrinter if its not already running; then call PrintQueueStart() to put it in active printing mode.
        /// </summary>
		public void StartDCSPrinter()
		{
			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.Print, true)) return;

            m_bDoActivate = false;  // signal call to dlgPrinter.DoActivate();
            m_bDoPause = false;     // signal call to dlgPrinter.DoPause();
            m_bDoPrintNow = false;  // signal call to dlgPrinter.DoPrintNow();

            if (m_thread != null && (!m_thread.IsAlive))
            {
                m_thread.Abort();
                m_thread.Join();
                m_thread = null;
            }
            if (m_thread == null)
            {
                System.Threading.ThreadStart threadStart;
                threadStart = new System.Threading.ThreadStart(Thread_DCSPrinter);
                m_thread = new System.Threading.Thread(threadStart);
                m_thread.Start();

                m_bDoActivate = true;  // signal call to dlgPrinter.DoActivate();
            }
		}
        private static void Thread_DCSPrinter()
        {
            using (DCSDEV.DCSPrinter.DCSPrinter dlgPrinter = new DCSDEV.DCSPrinter.DCSPrinter())
            {
                dlgPrinter.Show();
                while (true)
                {
                    if (m_bDoRefresh)
                    {
                        m_bDoRefresh = false;
                        dlgPrinter.DoRefresh();
                    }
                    else if (m_bDoPause)
                    {
                        m_bDoPause = false;
                        dlgPrinter.DoPause();
                    }
                    else if (m_bDoActivate)
                    {
                        m_bDoActivate = false;
                        dlgPrinter.DoActivate();
                    }
                    else if (m_bDoPrintNow)
                    {
                        m_bDoPrintNow = false;
                        dlgPrinter.DoPrintNow();
                    }
                    else if (m_bDoInit)
                    {
                        m_bDoInit = false;
                        dlgPrinter.DoInit();
                    }
                    else if (m_bDoShowMinimize)
                    {
                        m_bDoShowMinimize = false;
                        dlgPrinter.DoSetWindowState(FormWindowState.Minimized);
                    }
                    else if (m_bDoShowNormal)
                    {
                        m_bDoShowNormal = false;
                        dlgPrinter.DoSetWindowState(FormWindowState.Normal);
                    }
                    else if (m_bDoStop)
                    {
                        m_bDoStop = false;
                        dlgPrinter.Close();
                        dlgPrinter.Dispose();
                        break;
                    }
                    else
                    {
                        if (dlgPrinter.IsDisposed)
                        {
                            // MessageBox.Show("IsDisposed");
                            break;
                        }
                        System.Threading.Thread.Sleep(1000);    //syh 2.22.2008
                        Application.DoEvents();
                    }
                }
            }
        }

		private Rectangle GetBadgePreviewRectangle()
		{
			DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("BadgePreview");
			Rectangle rect = ps.GetRectParameter("PreviewRect", new Rectangle(0, 0, 400, 400));
			if (rect.Width <= 100) rect.Width = 400;	// just in case the size got trashed
			if (rect.Height <= 100) rect.Height = 400;
			if (rect.X < 0) rect.X = 0;
			if (rect.Y < 0) rect.Y = 0;
			return rect;
		}

		private bool RipToBitmaps(DCSDesign design, double dScale)
		{
			int width, height;
			// get badge size scaled
			width = DCSMath.IntTimesDouble(design.Bounds.Width, dScale);
			height = DCSMath.IntTimesDouble(design.Bounds.Height, dScale);

			////////////////////////////////////////
			// start with empty array of bitmaps
			////////////////////////////////////////
			foreach(Bitmap bm in m_sideBitmaps)
				if (bm != null) bm.Dispose();
			m_sideBitmaps.Clear();

			try
			{
				bool bRet;
				Bitmap bitmap;
				for (int iSide=0; iSide<design.m_designSides.Count; iSide++)
				{
					// if back side orientation is not same as front swap dimensions
					bool bBackOrientationDiffers = false;
					if (iSide != 0)
					{
						DCSDesignSide designSide0 = (DCSDesignSide)design.m_designSides[0];
						DCSDesignSide designSide1 = (DCSDesignSide)design.m_designSides[1];
						if (designSide0.SideIsLandscape != designSide1.SideIsLandscape) bBackOrientationDiffers = true;
					}
					if (!bBackOrientationDiffers) bitmap = new Bitmap(width, height);
					else bitmap = new Bitmap(height, width);

					m_sideBitmaps.Add(bitmap);

                    using (Graphics gr = Graphics.FromImage(bitmap))
                    {
                        /***********************************************************************************
                        // syh
                        if ((System.Windows.Forms.Control.ModifierKeys & Keys.Shift) == Keys.Shift)
                        {
                            bRet = design.RipSideToGDI(iSide, gr, dScale, DCSDesign.RipMode.RIPMODE_PRINT_NONK, true, true, true);   // back fore text
                            //bRet = design.RipSideToGDI(iSide, gr, dScale, DCSDesign.RipMode.RIPMODE_PRINT_KTEXT, true, true, true);
                        }
                        else if ((System.Windows.Forms.Control.ModifierKeys & Keys.Control) == Keys.Control)
                        {
                            //bRet = design.RipSideToGDI(iSide, gr, dScale, DCSDesign.RipMode.RIPMODE_PRINT_NONK, true, true, true);
                            bRet = design.RipSideToGDI(iSide, gr, dScale, DCSDesign.RipMode.RIPMODE_PRINT_KTEXT, true, true, true);
                        }
                        else
                        ***************************************************************************************/

                        bRet = design.RipSideToGDI(iSide, gr, dScale, DCSDesign.RipMode.RIPMODE_PREVIEW, true, true, true /* back fore text*/);
                        if (!bRet) return false;
                    }
					if (bBackOrientationDiffers)
						bitmap.RotateFlip(System.Drawing.RotateFlipType.Rotate90FlipNone);
				}
				return true;
			}
			catch (Exception ex)
			{
				DCSDEV.DCSMsg.Show(ex);
				return false;
			}
		}
		private void SaveBMP(string filename)
		{
			int iSide = 0;
			DCSDEV.ParameterStore ps = new ParameterStore("DCSSDK_Mgt");
			foreach(Bitmap bm in m_sideBitmaps)
			{
				bm.Save(System.IO.Path.Combine(ps.m_strDCSInstallDirectory, filename + iSide.ToString() + ".BMP"));
				iSide++;
			}
		}

	}
}
