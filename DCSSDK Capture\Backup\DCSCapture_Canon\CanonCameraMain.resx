<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonFromFile.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonFromFile.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="buttonFromFile.Location" type="System.Drawing.Point, System.Drawing">
    <value>496, 312</value>
  </data>
  <data name="buttonFromFile.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonFromFile.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="buttonFromFile.Text" xml:space="preserve">
    <value>Get from &amp;File</value>
  </data>
  <data name="&gt;&gt;buttonFromFile.Name" xml:space="preserve">
    <value>buttonFromFile</value>
  </data>
  <data name="&gt;&gt;buttonFromFile.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonFromFile.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonFromFile.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="buttonAcquire.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="buttonAcquire.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonAcquire.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonAcquire.Location" type="System.Drawing.Point, System.Drawing">
    <value>496, 376</value>
  </data>
  <data name="buttonAcquire.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <data name="buttonAcquire.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="buttonAcquire.Text" xml:space="preserve">
    <value>&amp;Capture</value>
  </data>
  <data name="&gt;&gt;buttonAcquire.Name" xml:space="preserve">
    <value>buttonAcquire</value>
  </data>
  <data name="&gt;&gt;buttonAcquire.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAcquire.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAcquire.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="buttonCancel.AccessibleDescription" xml:space="preserve">
    <value>shut down camera and exit</value>
  </data>
  <data name="buttonCancel.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonCancel.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>496, 408</value>
  </data>
  <data name="buttonCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <data name="buttonCancel.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="buttonCancel.Text" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Name" xml:space="preserve">
    <value>buttonCancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonCancel.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="tbCameraStatus.Location" type="System.Drawing.Point, System.Drawing">
    <value>416, 48</value>
  </data>
  <data name="tbCameraStatus.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 13</value>
  </data>
  <data name="tbCameraStatus.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="tbCameraStatus.Text" xml:space="preserve">
    <value>Uninitialized</value>
  </data>
  <data name="&gt;&gt;tbCameraStatus.Name" xml:space="preserve">
    <value>tbCameraStatus</value>
  </data>
  <data name="&gt;&gt;tbCameraStatus.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbCameraStatus.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbCameraStatus.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="buttonAbout.AccessibleDescription" xml:space="preserve">
    <value>shut down camera and exit</value>
  </data>
  <data name="buttonAbout.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonAbout.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonAbout.Location" type="System.Drawing.Point, System.Drawing">
    <value>496, 344</value>
  </data>
  <data name="buttonAbout.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <data name="buttonAbout.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="buttonAbout.Text" xml:space="preserve">
    <value>&amp;About</value>
  </data>
  <data name="&gt;&gt;buttonAbout.Name" xml:space="preserve">
    <value>buttonAbout</value>
  </data>
  <data name="&gt;&gt;buttonAbout.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAbout.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAbout.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="tbCameraModel.Location" type="System.Drawing.Point, System.Drawing">
    <value>416, 24</value>
  </data>
  <data name="tbCameraModel.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 13</value>
  </data>
  <data name="tbCameraModel.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="tbCameraModel.Text" xml:space="preserve">
    <value>Unknown model</value>
  </data>
  <data name="&gt;&gt;tbCameraModel.Name" xml:space="preserve">
    <value>tbCameraModel</value>
  </data>
  <data name="&gt;&gt;tbCameraModel.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbCameraModel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbCameraModel.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="pictureBoxPreview.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="pictureBoxPreview.Location" type="System.Drawing.Point, System.Drawing">
    <value>32, 40</value>
  </data>
  <data name="pictureBoxPreview.Size" type="System.Drawing.Size, System.Drawing">
    <value>320, 240</value>
  </data>
  <data name="pictureBoxPreview.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="&gt;&gt;pictureBoxPreview.Name" xml:space="preserve">
    <value>pictureBoxPreview</value>
  </data>
  <data name="&gt;&gt;pictureBoxPreview.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pictureBoxPreview.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pictureBoxPreview.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="trackBarZoom.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="trackBarZoom.Location" type="System.Drawing.Point, System.Drawing">
    <value>88, 288</value>
  </data>
  <data name="trackBarZoom.Size" type="System.Drawing.Size, System.Drawing">
    <value>240, 45</value>
  </data>
  <data name="trackBarZoom.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;trackBarZoom.Name" xml:space="preserve">
    <value>trackBarZoom</value>
  </data>
  <data name="&gt;&gt;trackBarZoom.Type" xml:space="preserve">
    <value>System.Windows.Forms.TrackBar, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;trackBarZoom.Parent" xml:space="preserve">
    <value>groupBoxPreview</value>
  </data>
  <data name="&gt;&gt;trackBarZoom.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelZoom.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelZoom.Location" type="System.Drawing.Point, System.Drawing">
    <value>32, 296</value>
  </data>
  <data name="labelZoom.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 16</value>
  </data>
  <data name="labelZoom.TabIndex" type="System.Int32, mscorlib">
    <value>34</value>
  </data>
  <data name="labelZoom.Text" xml:space="preserve">
    <value>Zoom</value>
  </data>
  <data name="&gt;&gt;labelZoom.Name" xml:space="preserve">
    <value>labelZoom</value>
  </data>
  <data name="&gt;&gt;labelZoom.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelZoom.Parent" xml:space="preserve">
    <value>groupBoxPreview</value>
  </data>
  <data name="&gt;&gt;labelZoom.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="buttonReset.AccessibleDescription" xml:space="preserve">
    <value />
  </data>
  <data name="buttonReset.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonReset.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonReset.Location" type="System.Drawing.Point, System.Drawing">
    <value>496, 280</value>
  </data>
  <data name="buttonReset.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <data name="buttonReset.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="buttonReset.Text" xml:space="preserve">
    <value>&amp;Reset</value>
  </data>
  <data name="&gt;&gt;buttonReset.Name" xml:space="preserve">
    <value>buttonReset</value>
  </data>
  <data name="&gt;&gt;buttonReset.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonReset.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonReset.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="comboBoxFlashMode.ItemHeight" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="comboBoxFlashMode.Location" type="System.Drawing.Point, System.Drawing">
    <value>496, 104</value>
  </data>
  <data name="comboBoxFlashMode.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 21</value>
  </data>
  <data name="comboBoxFlashMode.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;comboBoxFlashMode.Name" xml:space="preserve">
    <value>comboBoxFlashMode</value>
  </data>
  <data name="&gt;&gt;comboBoxFlashMode.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxFlashMode.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;comboBoxFlashMode.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="labelFlash.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelFlash.Location" type="System.Drawing.Point, System.Drawing">
    <value>400, 104</value>
  </data>
  <data name="labelFlash.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 16</value>
  </data>
  <data name="labelFlash.TabIndex" type="System.Int32, mscorlib">
    <value>29</value>
  </data>
  <data name="labelFlash.Text" xml:space="preserve">
    <value>Flash mode</value>
  </data>
  <data name="&gt;&gt;labelFlash.Name" xml:space="preserve">
    <value>labelFlash</value>
  </data>
  <data name="&gt;&gt;labelFlash.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelFlash.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelFlash.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="labelExpoComp.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelExpoComp.Location" type="System.Drawing.Point, System.Drawing">
    <value>400, 152</value>
  </data>
  <data name="labelExpoComp.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 32</value>
  </data>
  <data name="labelExpoComp.TabIndex" type="System.Int32, mscorlib">
    <value>31</value>
  </data>
  <data name="labelExpoComp.Text" xml:space="preserve">
    <value>Exposure compensation</value>
  </data>
  <data name="&gt;&gt;labelExpoComp.Name" xml:space="preserve">
    <value>labelExpoComp</value>
  </data>
  <data name="&gt;&gt;labelExpoComp.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelExpoComp.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelExpoComp.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="comboBoxExpoComp.ItemHeight" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="comboBoxExpoComp.Location" type="System.Drawing.Point, System.Drawing">
    <value>496, 152</value>
  </data>
  <data name="comboBoxExpoComp.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 21</value>
  </data>
  <data name="comboBoxExpoComp.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;comboBoxExpoComp.Name" xml:space="preserve">
    <value>comboBoxExpoComp</value>
  </data>
  <data name="&gt;&gt;comboBoxExpoComp.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxExpoComp.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;comboBoxExpoComp.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="labelWhiteBalance.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelWhiteBalance.Location" type="System.Drawing.Point, System.Drawing">
    <value>400, 200</value>
  </data>
  <data name="labelWhiteBalance.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 24</value>
  </data>
  <data name="labelWhiteBalance.TabIndex" type="System.Int32, mscorlib">
    <value>33</value>
  </data>
  <data name="labelWhiteBalance.Text" xml:space="preserve">
    <value>White balance</value>
  </data>
  <data name="&gt;&gt;labelWhiteBalance.Name" xml:space="preserve">
    <value>labelWhiteBalance</value>
  </data>
  <data name="&gt;&gt;labelWhiteBalance.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelWhiteBalance.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelWhiteBalance.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="comboBoxWhiteBalance.ItemHeight" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="comboBoxWhiteBalance.Location" type="System.Drawing.Point, System.Drawing">
    <value>496, 200</value>
  </data>
  <data name="comboBoxWhiteBalance.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 21</value>
  </data>
  <data name="comboBoxWhiteBalance.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;comboBoxWhiteBalance.Name" xml:space="preserve">
    <value>comboBoxWhiteBalance</value>
  </data>
  <data name="&gt;&gt;comboBoxWhiteBalance.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxWhiteBalance.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;comboBoxWhiteBalance.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 72pt, style=Bold</value>
  </data>
  <data name="label1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 32</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>320, 240</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>35</value>
  </data>
  <data name="label1.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>groupBoxPreview</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="groupBoxPreview.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 16</value>
  </data>
  <data name="groupBoxPreview.Size" type="System.Drawing.Size, System.Drawing">
    <value>360, 344</value>
  </data>
  <data name="groupBoxPreview.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="groupBoxPreview.Text" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="&gt;&gt;groupBoxPreview.Name" xml:space="preserve">
    <value>groupBoxPreview</value>
  </data>
  <data name="&gt;&gt;groupBoxPreview.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBoxPreview.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBoxPreview.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="buttonProperties.AccessibleDescription" xml:space="preserve">
    <value />
  </data>
  <data name="buttonProperties.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonProperties.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonProperties.Location" type="System.Drawing.Point, System.Drawing">
    <value>496, 248</value>
  </data>
  <data name="buttonProperties.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <data name="buttonProperties.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="buttonProperties.Text" xml:space="preserve">
    <value>Device &amp;Properties</value>
  </data>
  <data name="&gt;&gt;buttonProperties.Name" xml:space="preserve">
    <value>buttonProperties</value>
  </data>
  <data name="&gt;&gt;buttonProperties.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonProperties.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonProperties.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pictureBox1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="pictureBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>32, 32</value>
  </data>
  <data name="pictureBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>328, 256</value>
  </data>
  <data name="pictureBox1.TabIndex" type="System.Int32, mscorlib">
    <value>35</value>
  </data>
  <data name="&gt;&gt;pictureBox1.Name" xml:space="preserve">
    <value>pictureBox1</value>
  </data>
  <data name="&gt;&gt;pictureBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pictureBox1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pictureBox1.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleBaseSize" type="System.Drawing.Size, System.Drawing">
    <value>5, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>632, 446</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAIAICAQAAAAAADoAgAAJgAAABAQEAAAAAAAKAEAAA4DAAAoAAAAIAAAAEAAAAABAAQAAAAAAAAC
        AAAAAAAAAAAAABAAAAAQAAAAAAAAAAAAgAAAgAAAAICAAIAAAACAAIAAgIAAAICAgADAwMAAAAD/AAD/
        AAAA//8A/wAAAP8A/wD//wAA////AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AA7u7u7u4ADg7gAAAAAAAAAA7gDuAO4A7g7gAAAAAAAAAO4A7gAO4AAO4AAAAAAAAADuAO4ADuAO7uAA
        AAAAAAAA7gDuAA7g7u4AAAAAAAAAAO4A7gAO4O4AAAAAAAAAAADuAO4A7gDuDuAAAAAAAAAO7u7u7uAA
        DuDgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AHAHAAAAAAAAAAAAAAAHAAAAAAAAcAAAAAAAAAAAAHcAd3cAdwAAAAAAAAAAAAB3B3d3cHcAAAAAAAAA
        AAAAdwcHd3B3AAAAAAAAAAAAAHcH4HdwdwAAAAAAAAAAAAAAAHd3AAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAAcAAAAAAAAAAAAAAAAABwBwAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA////////
        /////////////////////////gB0//8zMn//M55//zOYf/8zkP//M5P//zMyf/4AeX//////////////
        /////D///4AB//+AAf//gAH//4IB//+BAf//sA3//7gd//+AAf//yZP///w/////////////////////
        //8oAAAAEAAAACAAAAABAAQAAAAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAgAAAAICAAIAA
        AACAAIAAgIAAAICAgADAwMAAAAD/AAD/AAAA//8A/wAAAP8A/wD//wAA////AAAAAAAAAAAAB3d3d3d3
        d3dERERERERER0////////hHT///////+EdP///////4R0////////hHT///////+EdP///////4R0//
        //////hHT///////+EdIiIiIiIiIR0zMzMzMzMxHxERERERERMAAAAAAAAAAAAAAAAAAAAAA//8AAIAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAD//wAA//8AAA==
</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Canon Portrait Capture Dialog</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>CanonCameraMain</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>