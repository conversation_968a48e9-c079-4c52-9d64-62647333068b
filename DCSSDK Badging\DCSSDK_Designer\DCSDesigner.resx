<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="openButton.ImageIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="openButton.ToolTipText" xml:space="preserve">
    <value>Open design</value>
  </data>
  <metadata name="printDoc.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="menuItemOpen.Text" xml:space="preserve">
    <value>&amp;Open...</value>
  </data>
  <data name="menuItemClose.Text" xml:space="preserve">
    <value>&amp;Close</value>
  </data>
  <data name="menuItemToolbar.Text" xml:space="preserve">
    <value>&amp;Toolbar</value>
  </data>
  <data name="menuItemStatusbar.Text" xml:space="preserve">
    <value>&amp;Status Bar</value>
  </data>
  <data name="menuItemFlipSide.Text" xml:space="preserve">
    <value>&amp;Flip Side</value>
  </data>
  <data name="menuItemZoom50.Text" xml:space="preserve">
    <value>50%</value>
  </data>
  <data name="menuItemZoom75.Text" xml:space="preserve">
    <value>75%</value>
  </data>
  <data name="menuItemZoom100.Text" xml:space="preserve">
    <value>100%</value>
  </data>
  <data name="menuItemZoom150.Text" xml:space="preserve">
    <value>150%</value>
  </data>
  <data name="menuItemZoom200.Text" xml:space="preserve">
    <value>200%</value>
  </data>
  <data name="menuItemZoom300.Text" xml:space="preserve">
    <value>300%</value>
  </data>
  <data name="menuItemZoom400.Text" xml:space="preserve">
    <value>400%</value>
  </data>
  <data name="menuItemViewZoom.Text" xml:space="preserve">
    <value>&amp;Zoom</value>
  </data>
  <data name="menuItemView.Text" xml:space="preserve">
    <value>&amp;View</value>
  </data>
  <data name="menuItemSetGrid.Text" xml:space="preserve">
    <value>&amp;Set Alignment Parameters ...</value>
  </data>
  <data name="buttonSave.ImageIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="buttonSave.ToolTipText" xml:space="preserve">
    <value>Save design</value>
  </data>
  <data name="menuItemHelpTopics.Text" xml:space="preserve">
    <value>&amp;Help Topics</value>
  </data>
  <data name="menuItem37.Text" xml:space="preserve">
    <value>-</value>
  </data>
  <data name="menuItemAbout.Text" xml:space="preserve">
    <value>&amp;About DCSDesigner</value>
  </data>
  <data name="menuItemHelp.Text" xml:space="preserve">
    <value>&amp;Help</value>
  </data>
  <data name="menuItemNew.Text" xml:space="preserve">
    <value>&amp;New</value>
  </data>
  <data name="menuItemSave.Text" xml:space="preserve">
    <value>&amp;Save</value>
  </data>
  <data name="menuItemSaveAs.Text" xml:space="preserve">
    <value>Save &amp;As...</value>
  </data>
  <data name="menuItem12.Text" xml:space="preserve">
    <value>-</value>
  </data>
  <data name="menuItemEditPrinterTypes.Text" xml:space="preserve">
    <value>&amp;Document Print Properties</value>
  </data>
  <data name="menuItemPrint.Text" xml:space="preserve">
    <value>&amp;Print...</value>
  </data>
  <data name="menuItemPreview.Text" xml:space="preserve">
    <value>Print Pre&amp;view</value>
  </data>
  <data name="menuItem3.Text" xml:space="preserve">
    <value>-</value>
  </data>
  <data name="menuItemExit.Text" xml:space="preserve">
    <value>E&amp;xit Designer</value>
  </data>
  <data name="menuItemFile.Text" xml:space="preserve">
    <value>&amp;File</value>
  </data>
  <data name="menuItem16.Text" xml:space="preserve">
    <value>-</value>
  </data>
  <data name="menuItemEditBadgeDesign.Text" xml:space="preserve">
    <value>&amp;Document Properties ...</value>
  </data>
  <data name="menuItemEditSides.Text" xml:space="preserve">
    <value>&amp;Eliminate Empty Sides</value>
  </data>
  <data name="menuItemEditObjProperties.Text" xml:space="preserve">
    <value>&amp;Object Properties ...</value>
  </data>
  <data name="menuItemDefaultFonts.Text" xml:space="preserve">
    <value>Edit document fonts ...</value>
  </data>
  <data name="menuItem1.Text" xml:space="preserve">
    <value>-</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="menuItemEditDelete.Shortcut" type="System.Windows.Forms.Shortcut, System.Windows.Forms">
    <value>Del</value>
  </data>
  <data name="menuItemEditDelete.Text" xml:space="preserve">
    <value>&amp;Delete</value>
  </data>
  <data name="menuItemEditFront.Text" xml:space="preserve">
    <value>Bring to &amp;Front</value>
  </data>
  <data name="menuItemEditBack.Text" xml:space="preserve">
    <value>Push to Bac&amp;k</value>
  </data>
  <data name="menuItemEditCut.Shortcut" type="System.Windows.Forms.Shortcut, System.Windows.Forms">
    <value>CtrlX</value>
  </data>
  <data name="menuItemEditCut.Text" xml:space="preserve">
    <value>Cu&amp;t</value>
  </data>
  <data name="menuItemEditCopy.Shortcut" type="System.Windows.Forms.Shortcut, System.Windows.Forms">
    <value>CtrlC</value>
  </data>
  <data name="menuItemEditCopy.Text" xml:space="preserve">
    <value>&amp;Copy</value>
  </data>
  <data name="menuItemEditPaste.Shortcut" type="System.Windows.Forms.Shortcut, System.Windows.Forms">
    <value>CtrlV</value>
  </data>
  <data name="menuItemEditPaste.Text" xml:space="preserve">
    <value>&amp;Paste</value>
  </data>
  <data name="menuItem2.Text" xml:space="preserve">
    <value>-</value>
  </data>
  <data name="menuItemEditUndo.Shortcut" type="System.Windows.Forms.Shortcut, System.Windows.Forms">
    <value>CtrlZ</value>
  </data>
  <data name="menuItemEditUndo.Text" xml:space="preserve">
    <value>&amp;Undo</value>
  </data>
  <data name="menuItemEditRedo.Text" xml:space="preserve">
    <value>&amp;Redo</value>
  </data>
  <data name="menuItemEdit.Text" xml:space="preserve">
    <value>&amp;Edit</value>
  </data>
  <data name="buttonPrint.ImageIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="buttonPrint.ToolTipText" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="newButton.ImageIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="newButton.ToolTipText" xml:space="preserve">
    <value>New design</value>
  </data>
  <data name="buttonPreview.ImageIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="buttonPreview.ToolTipText" xml:space="preserve">
    <value>Print Preview</value>
  </data>
  <data name="buttonFlip.ImageIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="buttonFlip.ToolTipText" xml:space="preserve">
    <value>Flip side</value>
  </data>
  <data name="buttonUndo.ImageIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="buttonUndo.ToolTipText" xml:space="preserve">
    <value>Undo</value>
  </data>
  <data name="buttonRedo.ImageIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="buttonRedo.ToolTipText" xml:space="preserve">
    <value>Redo</value>
  </data>
  <data name="buttonCopy.ImageIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="buttonCopy.ToolTipText" xml:space="preserve">
    <value>Copy to clipboard</value>
  </data>
  <data name="buttonCut.ImageIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="buttonCut.ToolTipText" xml:space="preserve">
    <value>Delete and copy to clipboard</value>
  </data>
  <data name="buttonPaste.ImageIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="buttonPaste.ToolTipText" xml:space="preserve">
    <value>Paste from clipboard</value>
  </data>
  <data name="buttonDelete.ImageIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="buttonDelete.ToolTipText" xml:space="preserve">
    <value>Delete object</value>
  </data>
  <data name="buttonInsertText.ImageIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="buttonInsertText.ToolTipText" xml:space="preserve">
    <value>Insert text</value>
  </data>
  <data name="buttonInsertPortrait.ImageIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="buttonInsertPortrait.ToolTipText" xml:space="preserve">
    <value>Insert portrait</value>
  </data>
  <data name="buttonInsertImage.ImageIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="buttonInsertImage.ToolTipText" xml:space="preserve">
    <value>Insert image</value>
  </data>
  <data name="buttonInsertBarcode.ImageIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="buttonInsertBarcode.ToolTipText" xml:space="preserve">
    <value>Insert barcode</value>
  </data>
  <data name="buttonInsertICAO.ImageIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="buttonInsertICAO.ToolTipText" xml:space="preserve">
    <value>Insert ICAO OCRB text</value>
  </data>
  <data name="buttonEditObjProp.ImageIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="buttonEditObjProp.ToolTipText" xml:space="preserve">
    <value>Edit object properties</value>
  </data>
  <data name="buttonHelp.ImageIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="buttonHelp.ToolTipText" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="toolBar1.DropDownArrows" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <metadata name="imageList1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>110, 17</value>
  </metadata>
  <data name="imageList1.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        ********************************************************************************
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAB2
        FAAAAk1TRnQBSQFMAgEBEwEAARgBAAEEAQABEAEAARABAAT/AQkBAAj/AUIBTQE2AQQGAAE2AQQCAAEo
        AwABQAMAAVADAAEBAQABCAYAARQYAAGAAgABgAMAAoABAAGAAwABgAEAAYABAAKAAgADwAEAAcAB3AHA
        AQAB8AHKAaYBAAEzBQABMwEAATMBAAEzAQACMwIAAxYBAAMcAQADIgEAAykBAANVAQADTQEAA0IBAAM5
        AQABgAF8Af8BAAJQAf8BAAGTAQAB1gEAAf8B7AHMAQABxgHWAe8BAAHWAucBAAGQAakBrQIAAf8BMwMA
        AWYDAAGZAwABzAIAATMDAAIzAgABMwFmAgABMwGZAgABMwHMAgABMwH/AgABZgMAAWYBMwIAAmYCAAFm
        AZkCAAFmAcwCAAFmAf8CAAGZAwABmQEzAgABmQFmAgACmQIAAZkBzAIAAZkB/wIAAcwDAAHMATMCAAHM
        AWYCAAHMAZkCAALMAgABzAH/AgAB/wFmAgAB/wGZAgAB/wHMAQABMwH/AgAB/wEAATMBAAEzAQABZgEA
        ATMBAAGZAQABMwEAAcwBAAEzAQAB/wEAAf8BMwIAAzMBAAIzAWYBAAIzAZkBAAIzAcwBAAIzAf8BAAEz
        AWYCAAEzAWYBMwEAATMCZgEAATMBZgGZAQABMwFmAcwBAAEzAWYB/wEAATMBmQIAATMBmQEzAQABMwGZ
        AWYBAAEzApkBAAEzAZkBzAEAATMBmQH/AQABMwHMAgABMwHMATMBAAEzAcwBZgEAATMBzAGZAQABMwLM
        AQABMwHMAf8BAAEzAf8BMwEAATMB/wFmAQABMwH/AZkBAAEzAf8BzAEAATMC/wEAAWYDAAFmAQABMwEA
        AWYBAAFmAQABZgEAAZkBAAFmAQABzAEAAWYBAAH/AQABZgEzAgABZgIzAQABZgEzAWYBAAFmATMBmQEA
        AWYBMwHMAQABZgEzAf8BAAJmAgACZgEzAQADZgEAAmYBmQEAAmYBzAEAAWYBmQIAAWYBmQEzAQABZgGZ
        AWYBAAFmApkBAAFmAZkBzAEAAWYBmQH/AQABZgHMAgABZgHMATMBAAFmAcwBmQEAAWYCzAEAAWYBzAH/
        AQABZgH/AgABZgH/ATMBAAFmAf8BmQEAAWYB/wHMAQABzAEAAf8BAAH/AQABzAEAApkCAAGZATMBmQEA
        AZkBAAGZAQABmQEAAcwBAAGZAwABmQIzAQABmQEAAWYBAAGZATMBzAEAAZkBAAH/AQABmQFmAgABmQFm
        ATMBAAGZATMBZgEAAZkBZgGZAQABmQFmAcwBAAGZATMB/wEAApkBMwEAApkBZgEAA5kBAAKZAcwBAAKZ
        Af8BAAGZAcwCAAGZAcwBMwEAAWYBzAFmAQABmQHMAZkBAAGZAswBAAGZAcwB/wEAAZkB/wIAAZkB/wEz
        AQABmQHMAWYBAAGZAf8BmQEAAZkB/wHMAQABmQL/AQABzAMAAZkBAAEzAQABzAEAAWYBAAHMAQABmQEA
        AcwBAAHMAQABmQEzAgABzAIzAQABzAEzAWYBAAHMATMBmQEAAcwBMwHMAQABzAEzAf8BAAHMAWYCAAHM
        AWYBMwEAAZkCZgEAAcwBZgGZAQABzAFmAcwBAAGZAWYB/wEAAcwBmQIAAcwBmQEzAQABzAGZAWYBAAHM
        ApkBAAHMAZkBzAEAAcwBmQH/AQACzAIAAswBMwEAAswBZgEAAswBmQEAA8wBAALMAf8BAAHMAf8CAAHM
        Af8BMwEAAZkB/wFmAQABzAH/AZkBAAHMAf8BzAEAAcwC/wEAAcwBAAEzAQAB/wEAAWYBAAH/AQABmQEA
        AcwBMwIAAf8CMwEAAf8BMwFmAQAB/wEzAZkBAAH/ATMBzAEAAf8BMwH/AQAB/wFmAgAB/wFmATMBAAHM
        AmYBAAH/AWYBmQEAAf8BZgHMAQABzAFmAf8BAAH/AZkCAAH/AZkBMwEAAf8BmQFmAQAB/wKZAQAB/wGZ
        AcwBAAH/AZkB/wEAAf8BzAIAAf8BzAEzAQAB/wHMAWYBAAH/AcwBmQEAAf8CzAEAAf8BzAH/AQAC/wEz
        AQABzAH/AWYBAAL/AZkBAAL/AcwBAAJmAf8BAAFmAf8BZgEAAWYC/wEAAf8CZgEAAf8BZgH/AQAC/wFm
        AQABIQEAAaUBAANfAQADdwEAA4YBAAOWAQADywEAA7IBAAPXAQAD3QEAA+MBAAPqAQAD8QEAA/gBAAHw
        AfsB/wEAAaQCoAEAA4ADAAH/AgAB/wMAAv8BAAH/AwAB/wEAAf8BAAL/AgAD/wEABgcJbQHtBQcCbQ4H
        Ae0KbRAABgcBAAjxAW0EBwEPAu8BbQEHAg8GBwFtAw8BDgnxAesQAAYHAQAI/wFtBAcBAAIHAW0BAAIH
        AW0EBwHsBQAB/wHwBu8B/wHsEAAGBwEAAf8FFAH3Af8BbQQHAQACBwFtAQACBwFtBAcGAAH/AfEGBwH/
        AewRAAVDAQAI/wFtBAcBEAETAZIBbQEAAQcBkgFtBAcGAAH/AZIGQwH/AewRAAX/AQAB/wVtAe8B/wFt
        BQcC7AFtAQABEwEAAZIEBwYAAf8B7wZtAf8B7BEAAf8EDgEACP8BbQcHAeoBAAcHBgAB/wH0A/MB9AH/
        AvMB7BEABf8BAAH/A+wBBwP/AW0HBwFDAW0HBwYAAf8BBwPsAQcB/wEUAewBQxEAAf8EDgEAAf8D8wHs
        Aw4BbQcHAW0IBwYAB/8B7AEOAQcRAAX/AQAB/wKSAf8B7AH/AbwBEwcHAewBEAEAAZIGBwYAB5IBEAIH
        EQAB/wNDAZIBAAH/ArwB/wHsAf8BbQgHAQABbQEAAW0GBw0AAW0CBxEABP8B7AEABAcBbQFDCAcB7wEA
        AgcBEwYHBAAFQwEPAwABbQIHEQAB/wISAf8B7AHvAeoE7AkHAW0BAAIHAQAGBwQAAfMB7QIVAfMB6wMA
        AW0CBxEABP8B7AHxAW0NBwFtAwcBbQcHBA8B6wLxAQAEDwMHEQAE6wEVAW0OBwFtAwcBbQsHBG0HBxAA
        MAcQACDyEP8w8hD/EfIB7wzrEvIQ/xHyAesL/wHsBfIBFAkAA/IQ/xHyAesD/wH2AcMCmgHDA/8B7AXy
        AVEBNgFPAQMBLgEGATABIQFXAQAD8gL/AbwB9wHxAfMB9wHvAf8BvAH3AfEB8wH3Ae8B/wHyAe8B6wHy
        AesB7wHrAe8B6wHyAesB7wHrAe8B8gHvAfIB6wP/ApoBSgEAAUoBmgL/AewF8gG5AQABNgH+ATYBAwEh
        AfsBBgEAA/IC/wEVAbwB/wHvAewC/wHrAfMB/wH3AZIC/wHyAesBAAHyAQAB6wEAAesBAAHyAQAB6wEA
        AesB8gHrAfIB6wP/ApoBdAOaAv8B7AXyAbsB4QFPAZ4BKAHsASgBBgHhAQAD8gP/Ae8B8AH/AfEB7wH/
        AfQB9wHxAf8BvAHvAf8B8gHrAQAB8gEAAesBAAHrAQAB8gEAAesBAAHrAfIB6wHyAesC/weaAcMB/wHs
        BfIBBwH+AeEBAAHsAf4B4QH/AeEBAAPyEP8B8gHrAQAB8gEAAesBAAHrAQAB8gEAAesBAAHrAfIB6wHy
        AesC/wKaAkoBmgFDAZoBwwH/AewF8gG7BeEBuwEGAeEBAAPyEP8B8gHrAQAB8gEAAesBAAHrAQAB8gEA
        AesBAAHrAfIB6wHyAesC/wEkBpoBkwH/AewF8gG5Af8B4QH+AeEB/wHsAfsBBgEAA/IC/wHrAW0BBwH/
        AZIB7AH/AbwB/wHwAf8B9wL/AfIB6wEAAfIBAAHrAQAB6wEAAfIBAAHrAQAB6wHyAesB8gHrAv8BJAFL
        BJoBJAGTAf8B7AXyAbsF4QFPAewBuwEAA/IC/wH0AZIB/wEHAfcB9AH/AewB6wEHAbwB7QHrAf8B8gHr
        AQAB8gEAAesBAAHrAQAB8gEAAesBAAHrAfIB6wHyAesC/wckAZMB/wHsBfIBBwH+AeEB/wHhAf4B4QH/
        AeEBAAPyA/8B9wH/Ae8B8wL/AfcBEwH/Ae0BvAEUAf8B8gHrAQAB8gEAAesBAAHrAQAB8gEAAesBAAHr
        AfIB6wHyAesD/wFLBCQBkwL/AewF8grvA/IC/wHvAewB8AHzAewB7wH/AfAB7AL/AewBvAH/AfIB6wEA
        AfIBAAHrAQAB6wEAAfIBAAHrAQAB6wHyAesB8gHrBf8CJAGTA/8B7BLyEP8R8gHvDOsS8hD/MPIQ/xDy
        MAcQ8jAHEPIwBxDyIwcBbQcAAW0EBxDyCwcBrgH3BgcB7wFfDgcBbQH/AQcB/wMHAf8BbQQHBPIB6wEV
        A/IB7wIAAesD8gMHBIYB7QMHAZIBCgHtBQcBZgHsBAcEhgHtBQcBbQf/AW0EBwXyAesD8gHrAQAB6wTy
        AwcEBAYHAQAEBwEAAW0FBwGuAwQBrgUHAW0B/wEHAf8DBwH/AW0EBwXyAe8D6wEVAQAB7wTyAwcCBAFf
        BwcBrgQHAQQHBwEKAgQBrgUHAW0F/wEHAf8BbQQHBvIBAALyAgAF8gMHAgQBrgEOBgcBrgQHAQQGBwFt
        AQQBrgEEAa4FBwFtAf8B7QEHAfIC9wEHAesBBwHtAgcG8gHrAe8B6wEAAesF8gMHAYYCBwGGARQFBwEK
        BAcBXwFtBAcB7AFmAgcBhgGuBQcBbQH/AgcB9wHsAfcCBwLsAgcH8gHrAgAG8gMHAe8DBwKGAesBBwHr
        AYYBEgQHAZIBXwEQAgcBZgFfAe8DBwHvBQcB7QNtAvcB7wMHAewCBwfyAesBAAHrBvIJBwOGAUMGBwHs
        A4YBrg8HAfcEBwLsAgcH8gHrAQAB6wbyKAcE9wEHAfcCBxDyMAcQ8jAHEPIwBxDyAgcLbQkHAm0pBwFt
        Cu8BAAFtCAcCACgHAewBFArsAQABbQHsBgcBbQEAAW0CBwPtBgcBbwH5BwcB+QFvBQcBDgHvBQcCkwQH
        AQAG7QFRAjcD7QEPAe0B7AEHAUMDBwEPAQAB7wIHAwQGBwGTAfkBbwUHAUcB+QGTBQcB7QEAAe0DBwGT
        AfkBRwQHAQAGBwF5AjcDBwFtARABbQEHAgACkgEAARADBwOGBwcC+QMHAe8BkwH5CAcBAAFtBAcBkwH5
        BAcBAAYHAZIC7AMHAW0BBwFtAQcFAAQHARMBhgETBwcBbwH5ARcCBwFHAfkBkwkHAQ8B7QIHAW8BkwEX
        BAcBAAsOAQABbQEHAW0BBwgAAQcCBAEACAcBbwH5AW8C+QsHAZIBAAHsAQcBkwH5AUcEBwFtAe0KBwEA
        Ae0CbQEHBwABbQEHAa4CBAHtBwcBkwL5AW8OBwEABQcBbQMHAW0KDgEAAW0B7wEAAQcGAAHqAwcBrgEE
        AQoJBwL5DgcBbQETAQcB7AEVAQABFQUHAewH/wTsARUBBwUAARMFBwGGAQQBCgGSBQcBkwH5AUcB+QGT
        CQcCRwFvAQcB7wIHAe0BEwIABQcBbQH/AZIEQwH/AfIBAAEQAQABEwEHBAABEwIHAoYDBwEKAQQBCgUH
        AfkBkwHvAvkJBwFvAfkGBwFtAQAGBwHsAQcH/wEUAW0DBwMAAUMCBwGuAQQBCgMHAa4CBAMHAZMB+QFv
        AwcBRwH5CAcBbwH5AwcBbQIHAUMBAAcHAewB7wUSAe8B/wFtAwcCAAEUAwcBrgEEAQoDBwGuAgQDBwFv
        AfkBkwMHAW8B+QFvBwcC+QIHAW0B7wFtAQ8BAAHvCAcB7Af/AewDBwEAAQ8EBwFtAgQBAALvAQoBBAEK
        CQcBbwH5AW8HBwFvAUcCBwMAAUMB7AkHAW0H6wEVAwcBbQYHAW0BXwEEAgoBBAEKAe0KBwFvCwcBEwIA
        Aew9BwFtAQABbQkHC20CBwttBwcNbQEHAQAK6wEVAgcBbQEAAwcB6wnxAesCBwEAASIJAwFtBQcBbQID
        BgAC7wEAAQMBAAEHAQAK/wHsAQcBbQIAAwcB7An/AewCBwEAARwBKQgDASkB7AQHAW0CAwYAAgcBAAED
        AQABBwEABv8B8AEAAQ8BFQEPAwAB7AMHAewJ/wHsAgcBAAGgAQcBKQgDASkB7AMHAW0CAwYAAgcBAAED
        AQABBwEABv8BEAHtAe8BBwFtAgAB7QQHAewJ/wHsAgcBAAGgAV4B7QEpCAMBIgGSAgcBbQIDBgACBwEA
        AQMBAAEHAQAG/wEOAgcB/wEGARABDgUHAewJ/wHsAgcBAAGgAeUBoAGSASkIAwEpAewBBwFtAgMGAAIT
        AQABAwEAAQcBAAb/AW0B8gEHAfEBbQEHAW0FBwHsCf8B7AIHAQABoAH2AaAB+wHsCQMBIgEHAW0MAwEA
        AQcBAAb/AeoB/gHyAQcBbQHvAeoFBwHsCf8B7AIHAQAFoAHsAVEBAwFRAewBQwRtAQcBbQIDCCICAwEA
        AQcBAAb/AQABugHhAfIBbQIABQcB7An/AewCBwEAAaAB+wGgAfYBoAH7AaAB9gGgAfsBbQUHAW0BAwEi
        CO8BAAEDAQABBwEABv8B7AEOAW0BBwFtAQAB6gUHAewJ/wHsAgcBAAOgATABcwGSAXMBMAFzAZIBbQUH
        AW0BAwEiCAcBAAEDAQABBwEAB/8DbQFDARMGBwHsBv8DQwEPAgcBAAGgAcMBoAEABpIB7wMQAesBBwFt
        AQMBIggHAQABAwEAAQcBAAr/AewHBwHsBv8BAAH/AQcB7AMHA0MJBwEPAQABbQEHAW0BAwEiCAcBAAED
        AQABBwEAB/8DBwFtBwcB7Ab/AQAB7wHsCwcBFQQHAW0BAAFtAQcBbQEDASIIBwEAAQMBAAEHAQAH/wEA
        AuwBFAcHAesG8QEAAW0NBwFtAu8BDwIHAW0BBwFtAQMBIggHAQABDwEAAQcBAAf/AQAB7wEPCAcIbQ4H
        Ae0CbQUHAW0BIgEOCBIBAAESAQABBwEAB/8BAAFtNgcJAAcHAUIBTQE+BwABPgMAASgDAAFAAwABUAMA
        AQEBAAEBBQABgAECFgAD//8A/wCDAAs=
</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="toolBar1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="toolBar1.ShowToolTips" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="toolBar1.Size" type="System.Drawing.Size, System.Drawing">
    <value>588, 28</value>
  </data>
  <data name="toolBar1.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;toolBar1.Name" xml:space="preserve">
    <value>toolBar1</value>
  </data>
  <data name="&gt;&gt;toolBar1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolBar, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolBar1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;toolBar1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="helpProvider1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>215, 17</value>
  </metadata>
  <data name="helpProvider1.HelpNamespace" xml:space="preserve">
    <value>..\..\Help\DCSDesigner.chm</value>
  </data>
  <metadata name="mainMenu1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>335, 17</value>
  </metadata>
  <data name="menuItemInsertText.Text" xml:space="preserve">
    <value>&amp;Text</value>
  </data>
  <data name="menuItemInsertImage.Text" xml:space="preserve">
    <value>&amp;Image</value>
  </data>
  <data name="menuItemInsertPortrait.Text" xml:space="preserve">
    <value>&amp;Portrait</value>
  </data>
  <data name="menuItemInsertSig.Text" xml:space="preserve">
    <value>&amp;Signature</value>
  </data>
  <data name="menuItemInsertFinger.Text" xml:space="preserve">
    <value>&amp;Fingerprint</value>
  </data>
  <data name="menuItemInsertBarcode.Text" xml:space="preserve">
    <value>&amp;Barcode</value>
  </data>
  <data name="menuItemInsertBarcode2D.Text" xml:space="preserve">
    <value>2D Barcode</value>
  </data>
  <data name="menuItemInsertICAO.Text" xml:space="preserve">
    <value>&amp;ICAO MRZ</value>
  </data>
  <data name="menuItemInsertGraphicBlock.Text" xml:space="preserve">
    <value>Graphic Block</value>
  </data>
  <data name="menuItem4.Text" xml:space="preserve">
    <value>Graphic</value>
  </data>
  <data name="menuItemInsert.Text" xml:space="preserve">
    <value>&amp;Insert</value>
  </data>
  <data name="menuItemAlignLeft.Text" xml:space="preserve">
    <value>&amp;Left</value>
  </data>
  <data name="menuItemAlignRight.Text" xml:space="preserve">
    <value>&amp;Right</value>
  </data>
  <data name="menuItemAlignTop.Text" xml:space="preserve">
    <value>&amp;Top</value>
  </data>
  <data name="menuItemAlignBottom.Text" xml:space="preserve">
    <value>&amp;Bottom</value>
  </data>
  <data name="menuItem7.Text" xml:space="preserve">
    <value>-</value>
  </data>
  <data name="menuItemAlignToMax.Text" xml:space="preserve">
    <value>Align to ma&amp;x</value>
  </data>
  <data name="menuItemAlignToMin.Text" xml:space="preserve">
    <value>Align to mi&amp;n</value>
  </data>
  <data name="menuItemAlignObj.Text" xml:space="preserve">
    <value>&amp;Align selected</value>
  </data>
  <data name="menuItemSizeHeight.Text" xml:space="preserve">
    <value>&amp;Height</value>
  </data>
  <data name="menuItemSizeWidth.Text" xml:space="preserve">
    <value>&amp;Width</value>
  </data>
  <data name="menuItemSizeBoth.Text" xml:space="preserve">
    <value>&amp;Both</value>
  </data>
  <data name="menuItem8.Text" xml:space="preserve">
    <value>-</value>
  </data>
  <data name="menuItemSizeToMax.Text" xml:space="preserve">
    <value>Size to ma&amp;x</value>
  </data>
  <data name="menuItemSizeToMin.Text" xml:space="preserve">
    <value>Size to mi&amp;n</value>
  </data>
  <data name="menuItemSize.Text" xml:space="preserve">
    <value>&amp;Resize selected</value>
  </data>
  <data name="menuItem5.Text" xml:space="preserve">
    <value>-</value>
  </data>
  <data name="menuItemAutoAlign.Text" xml:space="preserve">
    <value>&amp;Dynamic align</value>
  </data>
  <data name="menuItemSnapToGrid.Text" xml:space="preserve">
    <value>&amp;Grid align</value>
  </data>
  <data name="menuItemSnapToGridLines.Text" xml:space="preserve">
    <value>&amp;Tabular align</value>
  </data>
  <data name="menuItem6.Text" xml:space="preserve">
    <value>-</value>
  </data>
  <data name="menuItemAlignment.Text" xml:space="preserve">
    <value>&amp;Alignment</value>
  </data>
  <data name="menuItemCascade.Text" xml:space="preserve">
    <value>&amp;Cascade</value>
  </data>
  <data name="menuItemTile.Text" xml:space="preserve">
    <value>&amp;Tile</value>
  </data>
  <data name="menuItemWindow.Text" xml:space="preserve">
    <value>&amp;Window</value>
  </data>
  <data name="statusBar1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 8pt</value>
  </data>
  <data name="statusBar1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 384</value>
  </data>
  <data name="statusBar1.Size" type="System.Drawing.Size, System.Drawing">
    <value>588, 16</value>
  </data>
  <data name="statusBar1.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;statusBar1.Name" xml:space="preserve">
    <value>statusBar1</value>
  </data>
  <data name="&gt;&gt;statusBar1.Type" xml:space="preserve">
    <value>System.Windows.Forms.StatusBar, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;statusBar1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;statusBar1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="mdiClient1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="mdiClient1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 28</value>
  </data>
  <data name="mdiClient1.Size" type="System.Drawing.Size, System.Drawing">
    <value>588, 356</value>
  </data>
  <data name="mdiClient1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;mdiClient1.Name" xml:space="preserve">
    <value>mdiClient1</value>
  </data>
  <data name="&gt;&gt;mdiClient1.Type" xml:space="preserve">
    <value>System.Windows.Forms.MdiClient, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mdiClient1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;mdiClient1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>49</value>
  </metadata>
  <data name="$this.AutoScaleBaseSize" type="System.Drawing.Size, System.Drawing">
    <value>5, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>588, 400</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAIAICAQAAAAAADoAgAAJgAAACAgEAAAAAAA6AIAAA4DAAAoAAAAIAAAAEAAAAABAAQAAAAAAAAC
        AAAAAAAAAAAAABAAAAAQAAAAAAAAAAAAgAAAgAAAAICAAIAAAACAAIAAgIAAAICAgADAwMAAAAD/AAD/
        AAAA//8A/wAAAP8A/wD//wAA////AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADu7u
        7u7gAODuAAAAAAAAAADuAO4A7gDuDuAAAAAAAAAA7gDuAA7gAA7gAAAAAAAAAO4A7gAO4A7u4AAAAAAA
        AADuAO4ADuDu7gAAAAAAAAAA7gDuAA7g7gAAAAAAAAAAAO4A7gDuAO4O4AAAAAAAAA7u7u7u4AAO4OAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADu
        7u7u4AAAAAAAAAAAAAAA4AAA7gAAAAAAAAAAAAAAAOAA7uAAAAAAAAAAAAAAAADgAO7gAAAAAAAAAAAA
        AAAA4A7uAAAAAAAAAAAAAAAAAODuAAAAAAAAAAAAAAAAAADu4AAAC3AAAAAAAAAAAAAA7gAAAAsAAAAA
        AAAAAAAAAOAAAAAAtwAAAAAAAAAAAAAAAAAAALAAAAAAAAAAAAAAAAAAAACwAAAAAAAAAAAAAAAAAAAA
        ARAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA////////
        ///+AHT//zMyf/8znn//M5h//zOQ//8zk///MzJ//gB5f/////////////////8AAH//f/5//1Sqf/8A
        AH//gA///4Af//+IH///iB///4BP//+Ax///g8P//4fL//+P4f//n+H//5/h//+/8H////H/////////
        //8oAAAAIAAAAEAAAAABAAQAAAAAAAACAAAAAAAAAAAAABAAAAAQAAAAAAAAAAAAgAAAgAAAAICAAIAA
        AACAAIAAgIAAAICAgADAwMAAAAD/AAD/AAAA//8A/wAAAP8A/wD//wAA////AAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA7u7u7uAAAAAAAAAA
        AAAAAOAAAO4AAAAAAAAAAAAAAADgAO7gAAAAAAAAAAAAAAAA4ADu4AAAAAAAAAAAAAAAAOAO7gAAAAAA
        AAAAAAAAAADg7gAAAAAAAAAAAAAAAAAA7uAAAAtwAAAAAAAAAAAAAO4AAAALAAAAAAAAAAAAAADgAAAA
        ALcAAAAAAAAAAAAAAAAAAACwAAAAAAAAAAAAAAAAAAAAsAAAAAAAAAAAAAAAAAAAAAEQAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAA///////////////////////////////////////AAB//3/+f/9Uqn//A
        AB//4AP//+AH///iB///4gf//+AT///gMf//4PD//+Hy///j+H//5/h//+f4f//v/B////x/////////
        //////////////////////////////////8=
</value>
  </data>
  <data name="$this.ShowHelp" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>ID Services Document Designer</value>
  </data>
  <data name="&gt;&gt;openButton.Name" xml:space="preserve">
    <value>openButton</value>
  </data>
  <data name="&gt;&gt;openButton.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolBarButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;printDoc.Name" xml:space="preserve">
    <value>printDoc</value>
  </data>
  <data name="&gt;&gt;printDoc.Type" xml:space="preserve">
    <value>System.Drawing.Printing.PrintDocument, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="&gt;&gt;menuItemOpen.Name" xml:space="preserve">
    <value>menuItemOpen</value>
  </data>
  <data name="&gt;&gt;menuItemOpen.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemClose.Name" xml:space="preserve">
    <value>menuItemClose</value>
  </data>
  <data name="&gt;&gt;menuItemClose.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemView.Name" xml:space="preserve">
    <value>menuItemView</value>
  </data>
  <data name="&gt;&gt;menuItemView.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemToolbar.Name" xml:space="preserve">
    <value>menuItemToolbar</value>
  </data>
  <data name="&gt;&gt;menuItemToolbar.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemStatusbar.Name" xml:space="preserve">
    <value>menuItemStatusbar</value>
  </data>
  <data name="&gt;&gt;menuItemStatusbar.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemFlipSide.Name" xml:space="preserve">
    <value>menuItemFlipSide</value>
  </data>
  <data name="&gt;&gt;menuItemFlipSide.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemViewZoom.Name" xml:space="preserve">
    <value>menuItemViewZoom</value>
  </data>
  <data name="&gt;&gt;menuItemViewZoom.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemZoom50.Name" xml:space="preserve">
    <value>menuItemZoom50</value>
  </data>
  <data name="&gt;&gt;menuItemZoom50.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemZoom75.Name" xml:space="preserve">
    <value>menuItemZoom75</value>
  </data>
  <data name="&gt;&gt;menuItemZoom75.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemZoom100.Name" xml:space="preserve">
    <value>menuItemZoom100</value>
  </data>
  <data name="&gt;&gt;menuItemZoom100.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemZoom150.Name" xml:space="preserve">
    <value>menuItemZoom150</value>
  </data>
  <data name="&gt;&gt;menuItemZoom150.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemZoom200.Name" xml:space="preserve">
    <value>menuItemZoom200</value>
  </data>
  <data name="&gt;&gt;menuItemZoom200.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemZoom300.Name" xml:space="preserve">
    <value>menuItemZoom300</value>
  </data>
  <data name="&gt;&gt;menuItemZoom300.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemZoom400.Name" xml:space="preserve">
    <value>menuItemZoom400</value>
  </data>
  <data name="&gt;&gt;menuItemZoom400.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemSetGrid.Name" xml:space="preserve">
    <value>menuItemSetGrid</value>
  </data>
  <data name="&gt;&gt;menuItemSetGrid.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonSave.Name" xml:space="preserve">
    <value>buttonSave</value>
  </data>
  <data name="&gt;&gt;buttonSave.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolBarButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemHelp.Name" xml:space="preserve">
    <value>menuItemHelp</value>
  </data>
  <data name="&gt;&gt;menuItemHelp.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemHelpTopics.Name" xml:space="preserve">
    <value>menuItemHelpTopics</value>
  </data>
  <data name="&gt;&gt;menuItemHelpTopics.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItem37.Name" xml:space="preserve">
    <value>menuItem37</value>
  </data>
  <data name="&gt;&gt;menuItem37.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemAbout.Name" xml:space="preserve">
    <value>menuItemAbout</value>
  </data>
  <data name="&gt;&gt;menuItemAbout.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemNew.Name" xml:space="preserve">
    <value>menuItemNew</value>
  </data>
  <data name="&gt;&gt;menuItemNew.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemFile.Name" xml:space="preserve">
    <value>menuItemFile</value>
  </data>
  <data name="&gt;&gt;menuItemFile.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemSave.Name" xml:space="preserve">
    <value>menuItemSave</value>
  </data>
  <data name="&gt;&gt;menuItemSave.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemSaveAs.Name" xml:space="preserve">
    <value>menuItemSaveAs</value>
  </data>
  <data name="&gt;&gt;menuItemSaveAs.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItem12.Name" xml:space="preserve">
    <value>menuItem12</value>
  </data>
  <data name="&gt;&gt;menuItem12.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemEditPrinterTypes.Name" xml:space="preserve">
    <value>menuItemEditPrinterTypes</value>
  </data>
  <data name="&gt;&gt;menuItemEditPrinterTypes.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemPrint.Name" xml:space="preserve">
    <value>menuItemPrint</value>
  </data>
  <data name="&gt;&gt;menuItemPrint.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemPreview.Name" xml:space="preserve">
    <value>menuItemPreview</value>
  </data>
  <data name="&gt;&gt;menuItemPreview.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItem3.Name" xml:space="preserve">
    <value>menuItem3</value>
  </data>
  <data name="&gt;&gt;menuItem3.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemExit.Name" xml:space="preserve">
    <value>menuItemExit</value>
  </data>
  <data name="&gt;&gt;menuItemExit.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItem16.Name" xml:space="preserve">
    <value>menuItem16</value>
  </data>
  <data name="&gt;&gt;menuItem16.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemEdit.Name" xml:space="preserve">
    <value>menuItemEdit</value>
  </data>
  <data name="&gt;&gt;menuItemEdit.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemEditBadgeDesign.Name" xml:space="preserve">
    <value>menuItemEditBadgeDesign</value>
  </data>
  <data name="&gt;&gt;menuItemEditBadgeDesign.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemEditSides.Name" xml:space="preserve">
    <value>menuItemEditSides</value>
  </data>
  <data name="&gt;&gt;menuItemEditSides.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemEditObjProperties.Name" xml:space="preserve">
    <value>menuItemEditObjProperties</value>
  </data>
  <data name="&gt;&gt;menuItemEditObjProperties.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemDefaultFonts.Name" xml:space="preserve">
    <value>menuItemDefaultFonts</value>
  </data>
  <data name="&gt;&gt;menuItemDefaultFonts.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItem1.Name" xml:space="preserve">
    <value>menuItem1</value>
  </data>
  <data name="&gt;&gt;menuItem1.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemEditDelete.Name" xml:space="preserve">
    <value>menuItemEditDelete</value>
  </data>
  <data name="&gt;&gt;menuItemEditDelete.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemEditFront.Name" xml:space="preserve">
    <value>menuItemEditFront</value>
  </data>
  <data name="&gt;&gt;menuItemEditFront.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemEditBack.Name" xml:space="preserve">
    <value>menuItemEditBack</value>
  </data>
  <data name="&gt;&gt;menuItemEditBack.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemEditCut.Name" xml:space="preserve">
    <value>menuItemEditCut</value>
  </data>
  <data name="&gt;&gt;menuItemEditCut.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemEditCopy.Name" xml:space="preserve">
    <value>menuItemEditCopy</value>
  </data>
  <data name="&gt;&gt;menuItemEditCopy.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemEditPaste.Name" xml:space="preserve">
    <value>menuItemEditPaste</value>
  </data>
  <data name="&gt;&gt;menuItemEditPaste.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItem2.Name" xml:space="preserve">
    <value>menuItem2</value>
  </data>
  <data name="&gt;&gt;menuItem2.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemEditUndo.Name" xml:space="preserve">
    <value>menuItemEditUndo</value>
  </data>
  <data name="&gt;&gt;menuItemEditUndo.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemEditRedo.Name" xml:space="preserve">
    <value>menuItemEditRedo</value>
  </data>
  <data name="&gt;&gt;menuItemEditRedo.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPrint.Name" xml:space="preserve">
    <value>buttonPrint</value>
  </data>
  <data name="&gt;&gt;buttonPrint.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolBarButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;newButton.Name" xml:space="preserve">
    <value>newButton</value>
  </data>
  <data name="&gt;&gt;newButton.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolBarButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPreview.Name" xml:space="preserve">
    <value>buttonPreview</value>
  </data>
  <data name="&gt;&gt;buttonPreview.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolBarButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonFlip.Name" xml:space="preserve">
    <value>buttonFlip</value>
  </data>
  <data name="&gt;&gt;buttonFlip.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolBarButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonUndo.Name" xml:space="preserve">
    <value>buttonUndo</value>
  </data>
  <data name="&gt;&gt;buttonUndo.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolBarButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonRedo.Name" xml:space="preserve">
    <value>buttonRedo</value>
  </data>
  <data name="&gt;&gt;buttonRedo.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolBarButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonCopy.Name" xml:space="preserve">
    <value>buttonCopy</value>
  </data>
  <data name="&gt;&gt;buttonCopy.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolBarButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonCut.Name" xml:space="preserve">
    <value>buttonCut</value>
  </data>
  <data name="&gt;&gt;buttonCut.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolBarButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPaste.Name" xml:space="preserve">
    <value>buttonPaste</value>
  </data>
  <data name="&gt;&gt;buttonPaste.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolBarButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonDelete.Name" xml:space="preserve">
    <value>buttonDelete</value>
  </data>
  <data name="&gt;&gt;buttonDelete.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolBarButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonInsertText.Name" xml:space="preserve">
    <value>buttonInsertText</value>
  </data>
  <data name="&gt;&gt;buttonInsertText.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolBarButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonInsertPortrait.Name" xml:space="preserve">
    <value>buttonInsertPortrait</value>
  </data>
  <data name="&gt;&gt;buttonInsertPortrait.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolBarButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonInsertImage.Name" xml:space="preserve">
    <value>buttonInsertImage</value>
  </data>
  <data name="&gt;&gt;buttonInsertImage.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolBarButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonInsertBarcode.Name" xml:space="preserve">
    <value>buttonInsertBarcode</value>
  </data>
  <data name="&gt;&gt;buttonInsertBarcode.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolBarButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonInsertICAO.Name" xml:space="preserve">
    <value>buttonInsertICAO</value>
  </data>
  <data name="&gt;&gt;buttonInsertICAO.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolBarButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonEditObjProp.Name" xml:space="preserve">
    <value>buttonEditObjProp</value>
  </data>
  <data name="&gt;&gt;buttonEditObjProp.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolBarButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonHelp.Name" xml:space="preserve">
    <value>buttonHelp</value>
  </data>
  <data name="&gt;&gt;buttonHelp.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolBarButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;imageList1.Name" xml:space="preserve">
    <value>imageList1</value>
  </data>
  <data name="&gt;&gt;imageList1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ImageList, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;helpProvider1.Name" xml:space="preserve">
    <value>helpProvider1</value>
  </data>
  <data name="&gt;&gt;helpProvider1.Type" xml:space="preserve">
    <value>System.Windows.Forms.HelpProvider, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mainMenu1.Name" xml:space="preserve">
    <value>mainMenu1</value>
  </data>
  <data name="&gt;&gt;mainMenu1.Type" xml:space="preserve">
    <value>System.Windows.Forms.MainMenu, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemInsert.Name" xml:space="preserve">
    <value>menuItemInsert</value>
  </data>
  <data name="&gt;&gt;menuItemInsert.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemInsertText.Name" xml:space="preserve">
    <value>menuItemInsertText</value>
  </data>
  <data name="&gt;&gt;menuItemInsertText.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemInsertImage.Name" xml:space="preserve">
    <value>menuItemInsertImage</value>
  </data>
  <data name="&gt;&gt;menuItemInsertImage.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemInsertPortrait.Name" xml:space="preserve">
    <value>menuItemInsertPortrait</value>
  </data>
  <data name="&gt;&gt;menuItemInsertPortrait.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemInsertSig.Name" xml:space="preserve">
    <value>menuItemInsertSig</value>
  </data>
  <data name="&gt;&gt;menuItemInsertSig.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemInsertFinger.Name" xml:space="preserve">
    <value>menuItemInsertFinger</value>
  </data>
  <data name="&gt;&gt;menuItemInsertFinger.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemInsertBarcode.Name" xml:space="preserve">
    <value>menuItemInsertBarcode</value>
  </data>
  <data name="&gt;&gt;menuItemInsertBarcode.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemInsertBarcode2D.Name" xml:space="preserve">
    <value>menuItemInsertBarcode2D</value>
  </data>
  <data name="&gt;&gt;menuItemInsertBarcode2D.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemInsertICAO.Name" xml:space="preserve">
    <value>menuItemInsertICAO</value>
  </data>
  <data name="&gt;&gt;menuItemInsertICAO.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItem4.Name" xml:space="preserve">
    <value>menuItem4</value>
  </data>
  <data name="&gt;&gt;menuItem4.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemInsertGraphicBlock.Name" xml:space="preserve">
    <value>menuItemInsertGraphicBlock</value>
  </data>
  <data name="&gt;&gt;menuItemInsertGraphicBlock.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemAlignment.Name" xml:space="preserve">
    <value>menuItemAlignment</value>
  </data>
  <data name="&gt;&gt;menuItemAlignment.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemAlignObj.Name" xml:space="preserve">
    <value>menuItemAlignObj</value>
  </data>
  <data name="&gt;&gt;menuItemAlignObj.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemAlignLeft.Name" xml:space="preserve">
    <value>menuItemAlignLeft</value>
  </data>
  <data name="&gt;&gt;menuItemAlignLeft.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemAlignRight.Name" xml:space="preserve">
    <value>menuItemAlignRight</value>
  </data>
  <data name="&gt;&gt;menuItemAlignRight.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemAlignTop.Name" xml:space="preserve">
    <value>menuItemAlignTop</value>
  </data>
  <data name="&gt;&gt;menuItemAlignTop.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemAlignBottom.Name" xml:space="preserve">
    <value>menuItemAlignBottom</value>
  </data>
  <data name="&gt;&gt;menuItemAlignBottom.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItem7.Name" xml:space="preserve">
    <value>menuItem7</value>
  </data>
  <data name="&gt;&gt;menuItem7.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemAlignToMax.Name" xml:space="preserve">
    <value>menuItemAlignToMax</value>
  </data>
  <data name="&gt;&gt;menuItemAlignToMax.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemAlignToMin.Name" xml:space="preserve">
    <value>menuItemAlignToMin</value>
  </data>
  <data name="&gt;&gt;menuItemAlignToMin.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemSize.Name" xml:space="preserve">
    <value>menuItemSize</value>
  </data>
  <data name="&gt;&gt;menuItemSize.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemSizeHeight.Name" xml:space="preserve">
    <value>menuItemSizeHeight</value>
  </data>
  <data name="&gt;&gt;menuItemSizeHeight.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemSizeWidth.Name" xml:space="preserve">
    <value>menuItemSizeWidth</value>
  </data>
  <data name="&gt;&gt;menuItemSizeWidth.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemSizeBoth.Name" xml:space="preserve">
    <value>menuItemSizeBoth</value>
  </data>
  <data name="&gt;&gt;menuItemSizeBoth.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItem8.Name" xml:space="preserve">
    <value>menuItem8</value>
  </data>
  <data name="&gt;&gt;menuItem8.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemSizeToMax.Name" xml:space="preserve">
    <value>menuItemSizeToMax</value>
  </data>
  <data name="&gt;&gt;menuItemSizeToMax.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemSizeToMin.Name" xml:space="preserve">
    <value>menuItemSizeToMin</value>
  </data>
  <data name="&gt;&gt;menuItemSizeToMin.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItem5.Name" xml:space="preserve">
    <value>menuItem5</value>
  </data>
  <data name="&gt;&gt;menuItem5.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemAutoAlign.Name" xml:space="preserve">
    <value>menuItemAutoAlign</value>
  </data>
  <data name="&gt;&gt;menuItemAutoAlign.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemSnapToGrid.Name" xml:space="preserve">
    <value>menuItemSnapToGrid</value>
  </data>
  <data name="&gt;&gt;menuItemSnapToGrid.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemSnapToGridLines.Name" xml:space="preserve">
    <value>menuItemSnapToGridLines</value>
  </data>
  <data name="&gt;&gt;menuItemSnapToGridLines.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItem6.Name" xml:space="preserve">
    <value>menuItem6</value>
  </data>
  <data name="&gt;&gt;menuItem6.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemWindow.Name" xml:space="preserve">
    <value>menuItemWindow</value>
  </data>
  <data name="&gt;&gt;menuItemWindow.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemCascade.Name" xml:space="preserve">
    <value>menuItemCascade</value>
  </data>
  <data name="&gt;&gt;menuItemCascade.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;menuItemTile.Name" xml:space="preserve">
    <value>menuItemTile</value>
  </data>
  <data name="&gt;&gt;menuItemTile.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>DCSDesignerMain</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>