using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;
using DCSDEV;

namespace DCSSDK.BadgingMgt
{
	/// <summary>
	/// Summary description for BadgePreviewDisplay.
	/// </summary>
	internal class BadgePreviewDisplay : System.Windows.Forms.Form
	{
		private string m_strLabelBase = null;
		private int m_iSide = 0;
		private ArrayList m_sideBitmaps = null;

		private System.Windows.Forms.PictureBox pbDisplayImage;
		private System.Windows.Forms.MainMenu mainMenu1;
		private System.Windows.Forms.MenuItem menuItemFlip;
        private System.Windows.Forms.MenuItem menuItemClose;
        private IContainer components;

		internal BadgePreviewDisplay()
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
				if (m_sideBitmaps != null)
				{
					foreach (Image img in m_sideBitmaps)
						if (img != null) img.Dispose();
					m_sideBitmaps.Clear();
					m_sideBitmaps = null;
				}
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(BadgePreviewDisplay));
            this.pbDisplayImage = new System.Windows.Forms.PictureBox();
            this.mainMenu1 = new System.Windows.Forms.MainMenu(this.components);
            this.menuItemClose = new System.Windows.Forms.MenuItem();
            this.menuItemFlip = new System.Windows.Forms.MenuItem();
            ((System.ComponentModel.ISupportInitialize)(this.pbDisplayImage)).BeginInit();
            this.SuspendLayout();
            // 
            // pbDisplayImage
            // 
            resources.ApplyResources(this.pbDisplayImage, "pbDisplayImage");
            this.pbDisplayImage.Name = "pbDisplayImage";
            this.pbDisplayImage.TabStop = false;
            this.pbDisplayImage.DoubleClick += new System.EventHandler(this.pbDisplayImage_DoubleClick);
            // 
            // mainMenu1
            // 
            this.mainMenu1.MenuItems.AddRange(new System.Windows.Forms.MenuItem[] {
            this.menuItemClose,
            this.menuItemFlip});
            // 
            // menuItemClose
            // 
            this.menuItemClose.Index = 0;
            resources.ApplyResources(this.menuItemClose, "menuItemClose");
            this.menuItemClose.Click += new System.EventHandler(this.menuItemClose_Click);
            // 
            // menuItemFlip
            // 
            this.menuItemFlip.Index = 1;
            resources.ApplyResources(this.menuItemFlip, "menuItemFlip");
            this.menuItemFlip.Click += new System.EventHandler(this.menuItemFlip_Click);
            // 
            // BadgePreviewDisplay
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            resources.ApplyResources(this, "$this");
            this.Controls.Add(this.pbDisplayImage);
            this.MaximizeBox = false;
            this.Menu = this.mainMenu1;
            this.MinimizeBox = false;
            this.Name = "BadgePreviewDisplay";
            this.ShowInTaskbar = false;
            this.TopMost = true;
            this.Closing += new System.ComponentModel.CancelEventHandler(this.PreviewDisplay_Closing);
            ((System.ComponentModel.ISupportInitialize)(this.pbDisplayImage)).EndInit();
            this.ResumeLayout(false);

		}
		#endregion

		private void PreviewDisplay_Closing(object sender, System.ComponentModel.CancelEventArgs e)
		{
			DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("BadgePreview");
			ps.WriteRectParameter("PreviewRect", new Rectangle(Location, this.ClientRectangle.Size));
			
			this.pbDisplayImage.Image = null;
			if (m_sideBitmaps != null)
			{
				foreach (Image img in m_sideBitmaps)
					if (img != null) img.Dispose();
				m_sideBitmaps.Clear();
				m_sideBitmaps = null;
			}
		}

		protected override void OnResize(EventArgs e)
		{
			if (this.pbDisplayImage.Image != null)
			{
				double dImageRatio = (double)this.pbDisplayImage.Image.Width / (double)pbDisplayImage.Image.Height;
				Size sizeThisClient = this.ClientSize;
				
				double dWindowRatio = (double)sizeThisClient.Width / (double)sizeThisClient.Height;
				if (dWindowRatio < dImageRatio)
					sizeThisClient.Height = DCSMath.IntDivDouble(sizeThisClient.Width, dImageRatio);
				else
					sizeThisClient.Width = DCSMath.IntTimesDouble(sizeThisClient.Height, dImageRatio);
				this.ClientSize = sizeThisClient;
				this.pbDisplayImage.Size = sizeThisClient;
			}
			base.OnResize (e);
		}

		private void SetBitmapForSide()
		{
			if (m_strLabelBase == null || m_strLabelBase == "")
				m_strLabelBase = "Document Preview Display";
			if (m_sideBitmaps != null)
			{
				if (m_iSide >= m_sideBitmaps.Count) m_iSide = 0;

				this.pbDisplayImage.Image = (Image)m_sideBitmaps[m_iSide];
				if (m_sideBitmaps.Count > 1)
				{
					this.Text = m_strLabelBase + " Side " + (m_iSide+1).ToString() + " of " + m_sideBitmaps.Count;
					this.menuItemFlip.Visible = true;
				}
				else
				{
					this.Text = m_strLabelBase;
					this.menuItemFlip.Visible = false; 
				}
			}
		}

		private void pbDisplayImage_DoubleClick(object sender, System.EventArgs e)
		{
			m_iSide++;
			SetBitmapForSide();
		}

		private void menuItemFlip_Click(object sender, System.EventArgs e)
		{
			m_iSide++;
			SetBitmapForSide();
		}

		private void menuItemClose_Click(object sender, System.EventArgs e)
		{
			Close();
		}

		internal string Label
		{
			set 
			{ 
				m_strLabelBase = value; 
				SetBitmapForSide();
			}
			get { return m_strLabelBase; }
		}
		internal ArrayList SideBitmaps
		{
			set 
			{ 
				m_sideBitmaps = value;
				SetBitmapForSide();
			}
			get 
			{ 
				return m_sideBitmaps; 
			}
		}
		internal int Side
		{
			set 
			{ 
				m_iSide = value;
				SetBitmapForSide();
			}
			get { return m_iSide; }
		}
		internal Size DisplayImageSize
		{
			set { pbDisplayImage.Size = value; }
			get { return pbDisplayImage.Size; }
		}
	}
}
