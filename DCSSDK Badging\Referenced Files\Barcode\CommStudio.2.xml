<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CommStudio.2</name>
    </assembly>
    <members>
        <member name="T:CommStudio.Barcodes.AuxEval">
            <summary>
            Summary description for AuxEval.
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.BarcodeLayoutMode">
            <summary>
            Describes how the ModuleWidth and ModuleRatio of the bar code are handled: calculated automatically trying
            to fit the Rectangle supplied in Render or following the user's settings.  
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeLayoutMode.Auto">
            <summary>
            ModuleWidth and ModuleRatio are calculated automatically from the information supplied in the Render method.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeLayoutMode.Manual">
            <summary>
            ModuleWidth and ModuleRatio have to be set by the user. 
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.BarcodeAlignment">
            <summary>
            Possible alignment values of the bar code symbol relative to its layout rectangle.
            <seealso cref="T:CommStudio.Barcodes.BarcodeOrientation"/>
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeAlignment.Near">
            <summary>
            Specifies the symbol be aligned at the near end of  the layout rectangle.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeAlignment.Center">
            <summary>
            Specifies the symbol be aligned at the center the layout.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeAlignment.Far">
            <summary>
            Specifies the symbol be aligned at the far end of the layout rectangle. 
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.BarcodeOrientation">
            <summary>
            Possible orientation values for the bar code symbol. Specifies in what direction the bar code symbol is drawn.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeOrientation.Right">
            <summary>The symbol is drawn from Left to Right.</summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeOrientation.Left">
            <summary>The symbol is drawn from Right to Left.</summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeOrientation.Up">
            <summary>The symbol is drawn from Bottom to Top.</summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeOrientation.Down">
            <summary>The symbol is drawn from Top to Bottom.</summary>
        </member>
        <member name="T:CommStudio.Barcodes.BarcodeStyle">
            <summary>
            Different styles or bar code symbologies to use. The style determines the supported character set.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeStyle.MsiPlessey">
            <summary>
            MSI/Plessey Symbology. Variable length numeric symbology. A modulo 10 check digit is always appended to the encoded string.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeStyle.Interleaved2of5">
            <summary>
            Interleaved 2 of 5 Symbology. Variable length numeric symbology. A leading zero is appended to make the total number of characters even if necessary.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeStyle.Interleaved2of5Chk">
            <summary>
            Interleaved 2 of 5 with Check Digit. The same as the Interleaved 2 of 5 with an appended modulo 10 check digit.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeStyle.Codabar">
            <summary>
            Codabar Symbology. Variable length symbology. Supports digits 0-9 and six special alphanumeric characters, capital letters A through D. 
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeStyle.Code39">
            <summary>
            Code 39 or Code 3 of 9. Variable length alphanumeric symbology. It supports 26 uppercase letters, 10 digits and 7 special characters (space, -, ., $, %, / and +). The asterisk * is used as start/stop character.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeStyle.Code39Chk">
            <summary>
            The same as Code 39 with a modulo 43 Check Digit.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeStyle.Code39Ext">
            <summary>
            Code 39 Extended. Supports the full 128 ASCII character set. 
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeStyle.Code39ExtChk">
            <summary>
            The same as Code 39 Extended with a module 43 Check Digit appended. 
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeStyle.Code93">
            <summary>
            Code 93. Encodes digits 0–9, capital letters A through Z, and characters -, ., $, %, /, + and ' ' (space) . Two weighted modulo 47 check digits are appended to the encoded string.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeStyle.Code93Ext">
            <summary>
            Extended Code 93. Encodes the full 128 ASCII character set. Two weighted modulo 47 check digits are appended. 
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeStyle.Code128A">
            <summary>
            Code 128-A. Variable length alphanumeric symbology. Character supported include capital letters A-Z, digits 0-9,  punctuation marks, signs and special control characters. A modulo 103 check digit is always appended. 
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeStyle.Code128B">
            <summary>
            Code 128 B. Similar to Code 128 A but with the inclusion of lower case latters replacing some of the signs.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeStyle.Code128C">
            <summary>
            Code 128 C. High density numeric symbology.. The total number of digits must be even. A leading zero is appended if necessary. A modulo 103 check digit is appended.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeStyle.UpcA">
            <summary>
            UPC A. Fixed length numeric symbology. Only 11 digits can be supplied and an check digit will be always appended to make the total number of digits 12.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeStyle.UpcE">
            <summary>
            UPC E. Fixed length seven digit numeric bar code. Six digits must be supplied and a check digit will be appended. If an 11 digit UPC A string is supplied the translation will be performed if possible.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeStyle.Ean13">
            <summary>
            EAN 13. Fixed length 13 digits (including the check digit) numeric code similar to UPC A. 
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeStyle.Ean8">
            <summary>
            EAN 8. Fixed length 8 digits (including the check digit) numeric code.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.BarcodeStyle.Postnet">
            <summary>
            Postnet. Numeric symbology. As used by the U.S. Postal Service has a fixed length (not enforced by CommStudio.Barcodes) and an modulo 10 check digit that is appended automatically.
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.Barcode">
            <summary>
            Represents a bar code generator.
            </summary>
            <remarks>Implements IDisposable. The user should call Dispose() when the object instance in no more necessary.</remarks>
        </member>
        <member name="M:CommStudio.Barcodes.Barcode.#ctor">
            <summary>
            Default constructor. 
            </summary>
            <remarks>The default constructor creates a bar code with a <see cref="F:CommStudio.Barcodes.BarcodeStyle.Code39"/> style.</remarks> 
        </member>
        <member name="M:CommStudio.Barcodes.Barcode.#ctor(CommStudio.Barcodes.BarcodeStyle)">
            <summary>
            Creates a bar code with a given style. 
            </summary>
            <param name="style">Style of the bar code.</param>
        </member>
        <member name="M:CommStudio.Barcodes.Barcode.Finalize">
            <summary>
            Destructor. Called by the GC only if the user does not call Dispose()
            </summary>
        </member>
        <member name="M:CommStudio.Barcodes.Barcode.Dispose">
            <summary>
            Frees all resources associated with the <see cref="T:CommStudio.Barcodes.Barcode"/>
            </summary>
        </member>
        <member name="M:CommStudio.Barcodes.Barcode.Render(System.String,System.Drawing.Graphics,System.Drawing.Rectangle)">
             <summary>
              Renders the bar code in the supplied layout rectangle.
             </summary>
             <param name="text">Text to encode.</param>
             <param name="graphics"><see cref="T:System.Drawing.Graphics"/> object where the bar code will be rendered.</param>
             <param name="rect">Layout rectangle for the bar code.</param>
             <exception cref="T:System.ArgumentException">Thrown when one or more characters are not supported with the selected style.</exception>
             <remarks>
             Note that the Rectangle argumet is passed by reference. When returning, the variable will hold the exact rectangle where the
             bar code symbol was rendered. This is usefull if some other information has to be displayed in a position relative to the symbol. 
             </remarks>    
             <example>
             <code> 
             //Consider a Form with a PictureBox pictureBox1 on it.
             
             //Create the Barcode object (Style defaults to Code39).
             Barcode bc = new Barcode();
             
             //Get a Graphic object from the PictureBox (The image has to be created first!).
             Graphics g = Graphics.FromImage(pictureBox1.Image);
             
             //Render the bar code.
             bc.Render("123456", g, pictureBox1.ClientRectangle); 
             
             //Dispose resources.
             g.Dispose();
             bc.Dispose();
             </code>  
            </example> 
        </member>
        <member name="M:CommStudio.Barcodes.Barcode.Validate(System.String,System.Boolean)">
            <summary>
            Validates the passed text against the selected bar code style. <see cref="T:CommStudio.Barcodes.Barcode"/>.
            </summary>
            <param name="text">true if a <see cref="T:System.String"/>Text to validate.</param>
            <param name="allowExceptions ">true if a <see cref="T:System.ArgumentException"/> should be thrown if there are invalid characters.</param>
            <returns>True if the supplied text is compatible with the selected bar code stype or false otherwise.</returns>
            <exception cref="T:System.ArgumentException">Thrown if there are characters not compatible with the selected bar code style.</exception>
        </member>
        <member name="P:CommStudio.Barcodes.Barcode.LayoutMode">
            <summary>
            Get/Set the value of the LayoutMode.
            </summary>
        </member>
        <member name="P:CommStudio.Barcodes.Barcode.ModuleWidth">
            <summary>
            Get/Set the witdh of the bar code module.
            </summary>
            <exception cref="T:System.ArgumentException">Thrown if the users attempts to set a value less or equal zero.</exception>
        </member>
        <member name="P:CommStudio.Barcodes.Barcode.ModuleRatio">
            <summary>
            Get/Set the ratio of the wide to the narrow module in bar code styles with two module widths. 
            </summary>
            <exception cref="T:System.ArgumentException">Thrown if the users attempts to set a value less than 1. </exception>
        </member>
        <member name="P:CommStudio.Barcodes.Barcode.CaptionFont">
            <summary>
            Is the <see cref="T:System.Drawing.Font"/> used to display the Caption if enabled (see <see cref="P:CommStudio.Barcodes.Barcode.DisplayCaption"/>). 
            </summary>
            <remarks>If no <see cref="T:System.Drawing.Font"/> is supplied a default "Arial" 8pt is used.</remarks> 
        </member>
        <member name="P:CommStudio.Barcodes.Barcode.DisplayCaption">
            <summary>
            Enable/Disable the display of the Caption in the bar code symbol.
            </summary>
            <remarks>The Caption shows the original string without any additional character, like check digits, that might be appended to the final encoded string.</remarks> 
        </member>
        <member name="P:CommStudio.Barcodes.Barcode.ForeColor">
            <summary>
            Color of the bar code.
            </summary>
            <value> A <see cref="T:System.Drawing.Color"/> object</value> 
            <remarks>Usually Black</remarks> 
        </member>
        <member name="P:CommStudio.Barcodes.Barcode.BackColor">
            <summary>
            Color of the background.
            </summary>
            <value> A <see cref="T:System.Drawing.Color"/> object</value> 
            <remarks>Usually White</remarks> 
        </member>
        <member name="P:CommStudio.Barcodes.Barcode.Bounds">
            <summary>
            After a call to <see cref="M:CommStudio.Barcodes.Barcode.Render(System.String,System.Drawing.Graphics,System.Drawing.Rectangle)"/> contains the <see cref="T:System.Drawing.Rectangle"/>that bounds the
            rendered bar code.
            </summary>
        </member>
        <member name="P:CommStudio.Barcodes.Barcode.EncodedText">
            <summary>
            After a call to <see cref="M:CommStudio.Barcodes.Barcode.Render(System.String,System.Drawing.Graphics,System.Drawing.Rectangle)"/> contains the string finally encoded including the check digits if they exist.
            </summary>
        </member>
        <member name="P:CommStudio.Barcodes.Barcode.Alignment">
            <summary>
            Specifies the <see cref="T:CommStudio.Barcodes.BarcodeAlignment"/> of the bar code symbol in the layout rectangle. Defaults to Center. 
            </summary>
        </member>
        <member name="P:CommStudio.Barcodes.Barcode.Orientation">
            <summary>
            Specifies the <see cref="T:CommStudio.Barcodes.BarcodeOrientation"/> of the bar code. 
            </summary>
        </member>
        <member name="P:CommStudio.Barcodes.Barcode.Style">
            <summary>
            Specifies the <see cref="T:CommStudio.Barcodes.BarcodeStyle"/> or Symbology for the bar code. This determines the supported character set.
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.Barcode128">
            <summary>
               Summary description for Barcode128.
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.ColorCodedBarcode">
            <summary>
            Base class for Bar Codes Coded by Color
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.BarcodeBase">
            <summary>
            Common for all Bar Codes
            </summary>
        </member>
        <member name="M:CommStudio.Barcodes.Barcode128.Validate(CommStudio.Barcodes.BarcodeStyle,System.String)">
            <summary>
            Validates the passed string.
            </summary>
            <param name="style">Which style are using</param>
            <param name="text">Test to validate</param>
            <returns>true if the text is OK, false otherwise</returns>
        </member>
        <member name="M:CommStudio.Barcodes.Barcode39.Validate(CommStudio.Barcodes.BarcodeStyle,System.String)">
            <summary>
            Validates the passed string
            </summary>
            <param name="style">The style of the bar code.</param>
            <param name="text">Text to Validate</param>
            <returns>true if the text is OK, false otherwise</returns>
        </member>
        <member name="T:CommStudio.Barcodes.Codabar">
            <summary>
            Summary description for Codabar.
            </summary>
        </member>
        <member name="M:CommStudio.Barcodes.Codabar.Validate(CommStudio.Barcodes.BarcodeStyle,System.String)">
            <summary>
            Validates the passed string.
            </summary>
            <param name="style">The style of the bar code</param>
            <param name="text">Test to validate</param>
            <returns>true if the text is OK, false otherwise.</returns>
        </member>
        <member name="T:CommStudio.Barcodes.Code93">
            <summary>
            Summary description for Code93.
            </summary>
        </member>
        <member name="M:CommStudio.Barcodes.Code93.Validate(CommStudio.Barcodes.BarcodeStyle,System.String)">
            <summary>
            Validates the passed string.
            </summary>
            <param name="style">The style of the bar code.</param>
            <param name="text">Test to validate.</param>
            <returns>true if the text is OK, flase otherwise.</returns>
        </member>
        <member name="T:CommStudio.Barcodes.DataMatrix">
            <summary>
            Summary description for Class1.
            </summary>
        </member>
        <member name="M:CommStudio.Barcodes.DataMatrix.Render(System.Byte[],System.Drawing.Graphics,System.Drawing.Rectangle)">
            <summary>
            Renders a sequence of bytes as a DataMatrix symbol. <see cref="F:CommStudio.Barcodes.DataMatrix.SymbolEncoding.Base256"/>
            is used.
            </summary>
            <param name="buffer">The stream of bytes to be encoded in the DataMatrix symbol.</param>
            <param name="g">The <see cref="T:System.Drawing.Graphics"/> object where the symbol will be rendered.</param>
            <param name="rect">The <see cref="T:System.Drawing.Rectangle"/> where the DataMatrix symbol will be
            centered.</param>
            <remarks>The <see cref="P:CommStudio.Barcodes.DataMatrix.Encoding"/> setting is ignored when calling this method.</remarks>
        </member>
        <member name="M:CommStudio.Barcodes.DataMatrix.Render(System.String,System.Drawing.Graphics,System.Drawing.Rectangle)">
            <summary>
            Renders a string of characters as a DataMatrix symbol. The characters are
            encoded according to the selected <see cref="T:CommStudio.Barcodes.DataMatrix.SymbolEncoding"/>.
            </summary>
            <param name="text">The string of characters to be encoded in the DataMatrix symbol.</param>
            <param name="g">The <see cref="T:System.Drawing.Graphics"/> object where the symbol will be rendered.</param>
            <param name="rect">The <see cref="T:System.Drawing.Rectangle"/> where the DataMatrix symbol will be
            centered.
            </param>
        </member>
        <member name="M:CommStudio.Barcodes.DataMatrix.Dispose">
            <summary>
            Disposes the instance.
            </summary>
        </member>
        <member name="M:CommStudio.Barcodes.DataMatrix.Finalize">
            <summary>
            Instance Finalizer.
            </summary>
        </member>
        <member name="M:CommStudio.Barcodes.DataMatrix.#ctor">
            <summary>
            Instance Constructor.
            </summary>
        </member>
        <member name="P:CommStudio.Barcodes.DataMatrix.Bounds">
            <summary>
            After a call to <see cref="M:CommStudio.Barcodes.DataMatrix.Render(System.Byte[],System.Drawing.Graphics,System.Drawing.Rectangle)"/> returns the exact position of the
            bidimensional bar code.
            </summary>
        </member>
        <member name="P:CommStudio.Barcodes.DataMatrix.Orientation">
            <summary>
            Gets, set the Orientation of the DataMatrix symbol.
            </summary>
        </member>
        <member name="P:CommStudio.Barcodes.DataMatrix.ForeColor">
            <summary>
            DataMatrix supports both dark-on-light and light-on-dark symbols. ForeColor
            defaults to black.  
            </summary>
        </member>
        <member name="P:CommStudio.Barcodes.DataMatrix.BackColor">
            <summary>
            DataMatrix supports both dark-on-light and light-on-dark symbols. BackColor
            defaults to white.  
            </summary>
        </member>
        <member name="P:CommStudio.Barcodes.DataMatrix.Encoding">
            <summary>
            Gets and Set the Encoding scheme to use. Defaults to
            <see cref="F:CommStudio.Barcodes.DataMatrix.SymbolEncoding.Auto"/>.  
            </summary>
            <remarks>If in the data supplied in <see cref="M:CommStudio.Barcodes.DataMatrix.Render(System.Byte[],System.Drawing.Graphics,System.Drawing.Rectangle)"/> are characters incompatible
            with the selected encoding mode an Exception is thrown.</remarks> 
        </member>
        <member name="P:CommStudio.Barcodes.DataMatrix.Rows">
            <summary>
            After a call to <see cref="M:CommStudio.Barcodes.DataMatrix.Render(System.Byte[],System.Drawing.Graphics,System.Drawing.Rectangle)"/> returns how many Rows has the resulting symbol. 
            </summary>
            <remarks>Read only.</remarks>
        </member>
        <member name="P:CommStudio.Barcodes.DataMatrix.Columns">
            <summary>
            After a call to <see cref="M:CommStudio.Barcodes.DataMatrix.Render(System.Byte[],System.Drawing.Graphics,System.Drawing.Rectangle)"/> returns how many Columns has the resulting symbol. 
            </summary>
            <remarks>Read only.</remarks>
        </member>
        <member name="P:CommStudio.Barcodes.DataMatrix.Size">
            <summary>
            Gets or set the <see cref="T:CommStudio.Barcodes.DataMatrix.SymbolSize"/> to use. Defaults to
            <see cref="F:CommStudio.Barcodes.DataMatrix.SymbolSize.Auto"/> and the final size will be
            the smaller possible for the supplied data.
            </summary>
        </member>
        <member name="P:CommStudio.Barcodes.DataMatrix.ModuleSize">
            <summary>
            Gets or set the size of the smalles square element in the symbol. 
            Defaults to 20 mils. 
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.DataMatrix.SymbolSize">
            <summary>
            DataMatrix supports square symbols with sizes from
            10x10 up to 144x144 as well as six different rectangular
            symbols from 8x18 to 16x48. If SymbolSize.Auto is selected then
            the smaller symbol fr the given amount of data is automatically selected.  
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.Auto">
            <summary>
            The size is determined by the length of the data supplied.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S010x010">
            <summary>
            10 rows x 10 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S012x012">
            <summary>
            12 rows x 12 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S014x014">
            <summary>
            14 rows x 14 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S016x016">
            <summary>
            16 rows x 16 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S018x018">
            <summary>
            18 rows x 18 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S020x020">
            <summary>
            20 rows x 20 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S022x022">
            <summary>
            22 rows x 22 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S024x024">
            <summary>
            24 rows x 24 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S026x026">
            <summary>
            26 rows x 26 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S032x032">
            <summary>
            32 rows x 32 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S036x036">
            <summary>
            36 rows x 36 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S040x040">
            <summary>
            40 rows x 40 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S044x044">
            <summary>
            44 rows x 44 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S048x048">
            <summary>
            48 rows x 48 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S052x052">
            <summary>
            52 rows x 52 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S064x064">
            <summary>
            64 rows x 64 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S072x072">
            <summary>
            72 rows x 72 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S080x080">
            <summary>
            80 rows x 80 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S088x088">
            <summary>
            88 rows x 88 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S096x096">
            <summary>
            96 rows x 96 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S104x104">
            <summary>
            104 rows x 104 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S120x120">
            <summary>
            120 rows x 120 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S132x132">
            <summary>
            132 rows x 132 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.S144x144">
            <summary>
            144 rows x 144 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.R008x018">
            <summary>
            8 rows x 18 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.R008x032">
            <summary>
            8 rows x 32 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.R012x026">
            <summary>
            12 rows x 26 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.R012x036">
            <summary>
            12 rows x 36 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.R016x036">
            <summary>
            16 rows x 36 columns.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolSize.R016x048">
            <summary>
            16 rows x 48 columns.
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.DataMatrix.SymbolEncoding">
            <summary>
            DataMatrix supports six different encoding schemes or modes. Each scheme has
            its native character set for which achieves maximum efficiency.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolEncoding.Auto">
            <summary>
            The data is encoded in the best possible way using all the available
            encodation modes. Auto is the default mode.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolEncoding.ASCII">
            <summary>
            Encodes all 127 standard ASCII characters as well as the extended ASCII
            character set 128-255.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolEncoding.C40">
            <summary>
            Encodes all characters from the standard and extended ASCII sets. 
            Is more efficient than ASCII encodation for upper-case alphabetic 
            and numeric characters. 
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolEncoding.Text">
            <summary>
            Encodes all characters from the standard and extended ASCII sets. 
            Is more efficient than ASCII encodation for lower-case alphabetic 
            and numeric characters. 
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolEncoding.X12">
            <summary>
            Encodes only A-Z, 0-9, space and X12 control characters with ASCII
            values 13, 42 and 62. 
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolEncoding.EDIFACT">
            <summary>
            Encodes characters from the standard ASCII set in the range 32 
            through 94 which includes all upper and lower case alphabetic letters,
            numbers 0-9, the space as well as many symbols and punctuation characters. 
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolEncoding.Base256">
            <summary>
            Encodes characters in the range 0-255. 
            Adecuate for the encodation of binary data.
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.DataMatrix.SymbolOrientation">
            <summary>
            Possible orientations for the DataMatrix bar code symbol.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolOrientation.Horizontal">
            <summary>
            The DataMatrix bar code will be rendered horizontally.
            </summary>
            <remarks>Any Graphic Transformation previously applied to the Graphics object
            will be respected.</remarks>
        </member>
        <member name="F:CommStudio.Barcodes.DataMatrix.SymbolOrientation.Vertical">
            <summary>
            The DataMatrix will be rendered vertically.
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.DM_BaseEncoder">
            <summary>
            Summary description for BaseEncoder.
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.DM_ByteEncoder">
            <summary>
            Summary description for ByteEncoder.
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.DM_Constants">
            <summary>
            Summary description for DM_Constants.
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.DM_Encoder">
            <summary>
            Summary description for TextEncoder.
            </summary>
        </member>
        <member name="M:CommStudio.Barcodes.DM_Encoder.encodeC40Codewords(System.Int32[],System.Int32)">
            <summary>
            This procedure is used to encode C40, Text and X12 
            </summary>
            <param name="codewords">int Array of codewords</param>
            <param name="n">How many of the elements of the array should be used</param>
        </member>
        <member name="M:CommStudio.Barcodes.DM_Encoder.encodeCharC40(System.Char)">
            <summary>
            Returns an array (from 1 to 4 elements) of int codewords 
            corresponding to the character passed.
            </summary>
            <param name="c">Character to encode.</param>
            <returns>Array if int codewords.</returns>
        </member>
        <member name="T:CommStudio.Barcodes.DM_GF256">
            <summary>
            Implementation of Galois Field 2^8.
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.DM_ReedSolomon">
            <summary>
            Summary description for ReedSolomon.
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.DM_Symbol">
            <summary>
            Summary description for Symbol.
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.Interleaved2of5">
            <summary>
               Summary description for Interleaved2of5.
            </summary>
        </member>
        <member name="M:CommStudio.Barcodes.Interleaved2of5.Validate(CommStudio.Barcodes.BarcodeStyle,System.String)">
            <summary>
            Validates the passed string.
            </summary>
            <param name="style">The style of the bar code.</param>
            <param name="text">Test to validate.</param>
            <returns>true if the text is OK, false otherwise.</returns>
        </member>
        <member name="T:CommStudio.Barcodes.MSIPlessey">
            <summary>
            Summary description for MSIPlessey.
            </summary>
        </member>
        <member name="M:CommStudio.Barcodes.MSIPlessey.Validate(CommStudio.Barcodes.BarcodeStyle,System.String)">
            <summary>
            Validates the passed string.
            </summary>
            <param name="style">The style of the bar code.</param>
            <param name="text">Test to validate.</param>
            <returns>true if the text is OK, false otherwise.</returns>
        </member>
        <member name="T:CommStudio.Barcodes.Pdf417">
            <summary>
            PDF417 bar code generator.
            </summary>
        </member>
        <member name="M:CommStudio.Barcodes.Pdf417.Render(System.Byte[],System.Drawing.Graphics,System.Drawing.Rectangle)">
            <summary>
            Creates the PDF417 bar code. 
            </summary>
            <param name="bytes">Array of bytes to encode. </param>
            <param name="g">Graphic object where the bar code will be rendered.</param>
            <param name="rect">Layout rectangle in the graphic object where the bar code symbol will be centered.</param>
        </member>
        <member name="M:CommStudio.Barcodes.Pdf417.Render(System.String,System.Drawing.Graphics,System.Drawing.Rectangle)">
            <summary>
            Creates the PDF417 bar code. 
            </summary>
            <param name="text">The characters to encode. If text consists only of digits then
            Numeric Encoding is used, otherwise Text Encoding is used.</param>
            <param name="g">Graphic object where the bar code will be rendered.</param>
            <param name="rect">Layout rectangle in the graphic object where the bar code symbol will be centered.</param>
        </member>
        <member name="M:CommStudio.Barcodes.Pdf417.Dispose">
            <summary>
            Implementation of IDisposable
            </summary>
        </member>
        <member name="M:CommStudio.Barcodes.Pdf417.Finalize">
            <summary>
            Destructor. Should never be called.
            </summary>
        </member>
        <member name="M:CommStudio.Barcodes.Pdf417.#ctor">
            <summary>
            Default constructor.      
            </summary>
        </member>
        <member name="P:CommStudio.Barcodes.Pdf417.Bounds">
            <summary>
            After a call to <see cref="M:CommStudio.Barcodes.Pdf417.Render(System.Byte[],System.Drawing.Graphics,System.Drawing.Rectangle)"/>, returns the <see cref="T:System.Drawing.Rectangle"/> bounding the PDF417 Symbol.
            </summary>
        </member>
        <member name="P:CommStudio.Barcodes.Pdf417.Layout">
            <summary>
            Get/Set the value of the <see cref="P:CommStudio.Barcodes.Pdf417.Layout"/>.
            </summary>
        </member>
        <member name="P:CommStudio.Barcodes.Pdf417.Orientation">
            <summary>
            Get/Set the value of the bar code Orientation"/>.
            </summary>
        </member>
        <member name="P:CommStudio.Barcodes.Pdf417.ErrorCorrectionLevel">
             <summary>
             Error Correction Level. Determines how many extra Error Correction Codewords (ECC)
             will be encoded in the final symbol to allow the posibility of recovering from 
             damage in the symbol. 
             </summary>
             <remarks>Only values in the range 0-8 are valid. Depending on the value of the Error
             Correction Level different number of Error Correction Codewords (ECC) are appended.
             In any <see cref="P:CommStudio.Barcodes.Pdf417.Layout"/> if the User selects a valid Error Correction Level 
             that value is respected.  
             <list type="table">
             <listheader><term>Error Correction Level</term><description>Codewords</description></listheader> 
             <item><term>0</term><description>2</description></item>  
             <item><term>1</term><description>4</description></item>  
             <item><term>2</term><description>8</description></item>  
             <item><term>3</term><description>16</description></item>  
             <item><term>4</term><description>32</description></item>  
             <item><term>5</term><description>64</description></item>  
             <item><term>6</term><description>128</description></item>  
             <item><term>7</term><description>256</description></item>  
             <item><term>8</term><description>512</description></item>  
             </list> 
             
             AIM (Automatic Identification Manufacturers) Standard for PDF417 recommends the following EC Levels depending on the amount of Data:
             <list type="table">
             <listheader><term>Data Codewords</term><description>Recommended EC Level</description></listheader>   
             <item><term>1-40</term><description>2</description></item>
             <item><term>41-160</term><description>3</description></item>
             <item><term>161-320</term><description>4</description></item>
             <item><term>321-863</term><description>5</description></item>
             </list>
            </remarks> 
        </member>
        <member name="P:CommStudio.Barcodes.Pdf417.Truncated">
            <summary>
            Indicates that the Stop pattern and Right Row Indicator at the end of each row can 
            be omitted resulting in a smaller symbol.
            </summary>
        </member>
        <member name="P:CommStudio.Barcodes.Pdf417.Rows">
            <summary>
            Number of rows in the symbol. Acceptable values are in the range 3-90. 
            The product Rows*Columns cannot exceed 925.   
            </summary>
        </member>
        <member name="P:CommStudio.Barcodes.Pdf417.Columns">
            <summary>
            Number of data columns in the symbol. Note that appart from data columns a symbol has start/stop
            columns and left and right row indicators. That is why a symbol printed with Columns = 1 will 
            appear to have 5 columsn when printed.
            Acceptable values are in the range 1-30. The product Rows*Columns cannot exceed 925.  
            </summary>
        </member>
        <member name="P:CommStudio.Barcodes.Pdf417.ModuleWidth">
            <summary>
            The width of module, the smallest element in the symbol. The units will be determined by the
            <see cref="P:System.Drawing.Graphics.PageUnit"/> of the <see cref="T:System.Drawing.Graphics"/> in <see cref="M:CommStudio.Barcodes.Pdf417.Render(System.Byte[],System.Drawing.Graphics,System.Drawing.Rectangle)"/>. By
            default for printing the units are <see cref="F:System.Drawing.GraphicsUnit.Document"/> which corresponds to 10 mils per unit.
            </summary>
            <remarks>
            Using <see cref="M:System.Drawing.Graphics.ScaleTransform(System.Single,System.Single)"/> <see cref="P:System.Drawing.Graphics.PageScale"/> any desired
            ModuleWidth value can be obtained.
            </remarks> 
        </member>
        <member name="P:CommStudio.Barcodes.Pdf417.ModuleHeight">
            <summary>
            The height of module, equivalent to the height of a row. The units will be determined by the
            <see cref="P:System.Drawing.Graphics.PageUnit"/> of the <see cref="T:System.Drawing.Graphics"/> in <see cref="M:CommStudio.Barcodes.Pdf417.Render(System.Byte[],System.Drawing.Graphics,System.Drawing.Rectangle)"/>. By
            default for printing the units are <see cref="F:System.Drawing.GraphicsUnit.Document"/> which corresponds to 10 mils per unit.
            </summary>
            <remarks>
            Using <see cref="M:System.Drawing.Graphics.ScaleTransform(System.Single,System.Single)"/> <see cref="P:System.Drawing.Graphics.PageScale"/> any desired
            ModuleWidth value can be obtained.
            </remarks> 
        </member>
        <member name="T:CommStudio.Barcodes.Pdf417.SymbolOrientation">
            <summary>
            Possible orientations for the PDF417 bar code symbol.
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.Pdf417.SymbolOrientation.Horizontal">
            <summary>
            The PDF417 bar code will be rendered horizontally.
            </summary>
            <remarks>Any Graphic Transformation previously applied to the Graphics object
            will be respected.</remarks>
        </member>
        <member name="F:CommStudio.Barcodes.Pdf417.SymbolOrientation.Vertical">
            <summary>
            The PDF417 will be rendered vertically.
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.Pdf417.SymbolLayout">
            <summary>
            Describes how the layout of the PDF417 bar code is calculated: how many
            rows and columns the symbol will have, the ModuleWidth and ModuleHeight values, 
            the Orientation and the Error Correction Level.  
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.Pdf417.SymbolLayout.Auto">
            <summary>
            All bar code parameters (rows, columns, orientation, module dimensions and Error Correction Level
            are calculated automatically from the information supplied in the Render method. Nevertheless, if
            the User sets the values for <see cref="P:CommStudio.Barcodes.Pdf417.ErrorCorrectionLevel"/>, <see cref="P:CommStudio.Barcodes.Pdf417.ModuleWidth"/> or <see cref="P:CommStudio.Barcodes.Pdf417.ModuleHeight"/>
            they are not changed. 
            </summary>
        </member>
        <member name="F:CommStudio.Barcodes.Pdf417.SymbolLayout.Manual">
            <summary>
            All bar code parameters have to be set by the user. In case the values are incompatible
            with the data supplied in the method Render an ArgumentException will ne thrown.
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.PDF417_ByteEncoder">
            <summary>
            Encodes (Translates to codewords) a sequence of bytes.
            </summary>
        </member>
        <member name="M:CommStudio.Barcodes.PDF417_Clusters.getCodeword(System.Int32,System.Int32)">
            <summary>
            Summary description for Clusters.
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.PDF417_ECC">
            <summary>
            Summary description for ErrorCorrection.
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.PDF417_NumericEncoder">
            <summary>
            Summary description for NumericEncoder.
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.PDF417_TextEncoder">
            <summary>
            Summary description for TextEncoder.
            </summary>
        </member>
        <member name="M:CommStudio.Barcodes.PDF417_TextEncoder.getValMode(System.Char)">
            <summary>
            
            </summary>
            <param name="c"></param>
            <returns></returns>
        </member>
        <member name="M:CommStudio.Barcodes.PDF417_TextEncoder.MyArray.pushInt(System.Int32)">
            <summary>
            Used only in Compacting a byte 
            </summary>
            <param name="val"></param>
        </member>
        <member name="T:CommStudio.Barcodes.PDF417_TextEncoder.Transitions">
            <summary>
            All possible Transitions between modes.
            </summary>
        </member>
        <member name="T:CommStudio.Barcodes.Postnet">
            <summary>
               Summary description for Postnet.
            </summary>
        </member>
        <member name="M:CommStudio.Barcodes.Postnet.Validate(CommStudio.Barcodes.BarcodeStyle,System.String)">
            <summary>
            Validates the passed string.
            </summary>
            <param name="style">The style of the bar code.</param>
            <param name="text">The text to validate.</param>
            <returns></returns>
        </member>
        <member name="T:CommStudio.Barcodes.UPC">
            <summary>
            Summary description for UPC.
            </summary>
        </member>
        <member name="M:CommStudio.Barcodes.UPC.Validate(CommStudio.Barcodes.BarcodeStyle,System.String)">
            <summary>
            Validates the passed string.
            </summary>
            <param name="style"></param>
            <param name="text"></param>
            <returns></returns>
        </member>
        <member name="T:CommStudio.Connections.WaitMethod">
            <summary>
            Defines the method used when waiting for data.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.WaitMethod.Automatic">
            <summary>
            Automatically detects if the current thread has user interface.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.WaitMethod.DoEvents">
            <summary>
            Check the Windows message loop while waiting to allow the user to interact with the application.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.WaitMethod.Sleep">
            <summary>
            Put the processor in a sleep state or allow it to perform other tasks.
            </summary>
        </member>
        <member name="T:CommStudio.Connections.ConnectionKey">
            <summary>
            Defines the key used to temporarily lock out all other functions from transmitting
            through a device.
            </summary>
            <remarks>
            The primary purpose of this class is to create a unique key to be used by file transfer
            protocol management. During the execution of a specific transfer, it is important that all
            other processes and functions be prevented from either reading or writing to the device.
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.ConnectionKey.GetHashCode">
            <summary>
            Returns the hash code for this instance.
            </summary>
            <returns>Hash code for this object.</returns>
        </member>
        <member name="M:CommStudio.Connections.ConnectionKey.#ctor">
            <summary>
            Default constructor for the ConnectionKey object.
            </summary>
        </member>
        <member name="M:CommStudio.Connections.ConnectionKey.Equals(System.Object)">
            <summary>
            Determines whether the specified Object is equal to the current <see cref="T:CommStudio.Connections.ConnectionKey"/> object.
            </summary>
            <param name="other">The Object to compare with the current <see cref="T:CommStudio.Connections.ConnectionKey"/>.</param>
            <returns>true if the specified Object is equal to the current Object; otherwise, false.</returns>
        </member>
        <member name="M:CommStudio.Connections.ConnectionKey.Compare(CommStudio.Connections.ConnectionKey,CommStudio.Connections.ConnectionKey)">
            <summary>
            Compares the two instances of the <see cref="T:CommStudio.Connections.ConnectionKey"/> objects.
            </summary>
            <param name="a">First key structure to compare</param>
            <param name="b">Second key structure to compare</param>
            <returns>'true' if they are the same.</returns>
        </member>
        <member name="T:CommStudio.Connections.Connection">
            <summary>
            Implements the base functionality for a connection.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.Connection.synchronizingObject">
            <summary>
            Gets or sets the object used to marshal the event handler calls issued from different threads
            </summary>
        </member>
        <member name="F:CommStudio.Connections.Connection.m_Encoding">
            <summary>
            Encoding used when converting between text strings and byte arrays.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.Connection.m_WaitMethod">
            <summary>
            Defines the method used when waiting for data.
            </summary>
        </member>
        <member name="M:CommStudio.Connections.Connection.#ctor(System.ComponentModel.IContainer)">
            <summary>
            Creates a new instance of the <see cref="T:CommStudio.Connections.Connection"/> class in the specified container
            </summary>
            <param name="container">Container to place this new instance of the Connection class.</param>
        </member>
        <member name="M:CommStudio.Connections.Connection.#ctor">
            <summary>
            Initializes a new instances of the <see cref="T:CommStudio.Connections.Connection"/> class
            </summary>
        </member>
        <member name="M:CommStudio.Connections.Connection.InitializeMe">
            <summary>
            Common initializations for all constructors within the Connection class.
            </summary>
        </member>
        <member name="M:CommStudio.Connections.Connection.Dispose(System.Boolean)">
            <summary>
            Disposes of the resources used by the object
            </summary>
            <param name="disposing">Determines if the code was called directly by the user's code</param>
        </member>
        <member name="M:CommStudio.Connections.Connection.Read(System.Byte[],System.Int32,System.Int32,CommStudio.Connections.ConnectionKey)">
            <summary>
            Reads bytes from the serial port into a byte array, not checking for locked condition.
            </summary>
            <param name="buffer">The buffer to read data into.</param>
            <param name="offset">The starting point in the buffer at which to begin reading
            into the buffer.</param>
            <param name="count">The number of characters to read.</param>
            <param name="key">Communication locking key.</param>
            <returns>The number of characters read into buffer. This might be less than the
            number of bytes requested if that many bytes are not available.</returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.Read(CommStudio.Connections.ConnectionKey)">
            <summary>
            Reads a byte from the connection or -1 if no bytes are available.
            </summary>
            <param name="key">Communication locking key.</param>
            <returns>The byte that was read, or -1 if no bytes are available.</returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.Write(System.Byte[],System.Int32,System.Int32,CommStudio.Connections.ConnectionKey)">
            <summary>
            Writes bytes from a byte array to the serial port, not checking for locked condition.
            </summary>
            <param name="buffer">The data to write to the stream. </param>
            <param name="offset">The location in the buffer to start writing data from.</param>
            <param name="count">The number of bytes to write to the stream.</param>
            <param name="key">Communication locking key.</param>
            <returns>Returns the number of bytes written.</returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.Write(System.Byte,CommStudio.Connections.ConnectionKey)">
            <summary>
            Writes a byte to the connection.
            </summary>
            <param name="b">The byte that should be written.</param>
            <param name="key">Communication locking key.</param>
        </member>
        <member name="F:CommStudio.Connections.Connection.m_Key">
            <summary>
            Stored key for locking the file transfer
            </summary>
        </member>
        <member name="M:CommStudio.Connections.Connection.BeginLock(CommStudio.Connections.ConnectionKey)">
            <summary>
            Locks the current connection from use except by methods which know the correct key.
            </summary>
            <param name="key">Generic object to use as the key.</param>
            <remarks>
            The primary purpose of locking is for a protocol to shield unwanted calls from modifying
            the required data stream. This method should be called when starting your protocol.
            </remarks>
            <exception cref="T:CommStudio.Connections.CommunicationsLockedPortException">Attempt to lock a connection which is already locked.</exception>
        </member>
        <member name="M:CommStudio.Connections.Connection.EndLock(CommStudio.Connections.ConnectionKey)">
            <summary>
            Unlocks the connection so that any method may access the read/write abilities.
            </summary>
            <param name="key">Generic object currently used as the key.</param>
            <remarks>
            The primary purpose of locking is for a protocol to shield unwanted calls from modifying
            the required data stream. This method should be called when your protocol as completed.
            </remarks>
            <exception cref="T:CommStudio.Connections.CommunicationsLockedPortException">Attempt to lock a connection which is already locked.</exception>
        </member>
        <member name="M:CommStudio.Connections.Connection.ValidKey(CommStudio.Connections.ConnectionKey)">
            <summary>
            Lets you verify if the product can be used with the supplied key.
            </summary>
            <param name="key">Key as defined by the user (may be null for no key).</param>
            <returns>If it is OK to use the product given the key.</returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads bytes from the serial port into a byte array.
            </summary>
            <param name="buffer">The buffer to read data into.</param>
            <param name="offset">The starting point in the buffer at which to begin reading
            into the buffer.</param>
            <param name="count">The number of characters to read.</param>
            <returns>The number of characters read into buffer. This might be less than the
            number of bytes requested if that many bytes are not available.</returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes bytes from a byte array to the serial port.
            </summary>
            <param name="buffer">The data to write to the stream. </param>
            <param name="offset">The location in the buffer to start writing data from.</param>
            <param name="count">The number of bytes to write to the stream.</param>
            <returns>Returns the number of bytes written.</returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.Peek">
            <summary>
            Returns the next available character but does not consume it.
            </summary>
            <returns>The next character to be read, or -1 if no characters are available.</returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.Peek(CommStudio.Connections.ConnectionKey)">
            <summary>
            Returns the next available character but does not consume it (key protected version).
            </summary>
            <param name="key">Key as defined by the user (may be null for no key).</param>
            <returns>The next character to be read, or -1 if no characters are available.</returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.WaitForInput(System.Int32)">
            <summary>
            Wait for a certain number of bytes to be input using current Read timeout value.
            </summary>
            <param name="count">Number of bytes to wait for.</param>
            <returns>If the requested number of bytes are available for reading.</returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.WaitForInput(System.Int32,System.Int32,System.Int32)">
            <summary>
            Wait for a certain number of bytes to be input, allows complete definition of timeout.
            </summary>
            <param name="count">Number of bytes to wait for.</param>
            <param name="waitConst">Minimum amount of time to wait for input bytes.</param>
            <param name="waitMult">Milliseconds to wait for each byte to appear in the queue (default=0).</param>
            <returns><c>true</c> if the requested number of bytes are available for reading.</returns>
            <exception cref="T:System.IO.IOException">The specified port is not open.</exception>
            <remarks>
            The total time waiting for the specified bytes will be the constant value + the
            multiplier*[Total bytes waiting for].
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.Connection.ReadShort(CommStudio.Connections.ConnectionKey)">
            <summary>
            Reads in a single short value from the current device.
            </summary>
            <returns>Two-byte short value.</returns>
            <exception cref="T:CommStudio.Connections.CommunicationsTimeoutException">Unable to read value within current time limit.</exception>
        </member>
        <member name="M:CommStudio.Connections.Connection.Read(System.Int32)">
            <summary>
            Reads the specified number of bytes from the connection and returns them as a string.
            </summary>
            <param name="count">The number of bytes to read.</param>
            <returns>The bytes read, converted to a string using the current <see cref="P:CommStudio.Connections.Connection.Encoding"/>.</returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.Open">
            <summary>
            Opens a connection to the device
            </summary>
        </member>
        <member name="M:CommStudio.Connections.Connection.Close">
            <summary>
            Closes the connection to the current device
            </summary>
        </member>
        <member name="M:CommStudio.Connections.Connection.Read">
            <summary>
            Reads a byte from the connection or -1 if no bytes are available.
            </summary>
            <returns>The byte that was read, or -1 if no bytes are available.</returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.ReadLine">
            <summary>
            Reads a line of text from the serial port.
            </summary>
            <returns>The line of text, not including the carriage return or line feed.</returns>
            <remarks>The maximum length of the line is 1024 characters.</remarks>
        </member>
        <member name="M:CommStudio.Connections.Connection.ReadLine(System.Int32)">
            <summary>
            Reads a line of text from the serial port.
            </summary>
            <param name="maxCount">The maximum length of the line, not including the carriage return or line feed</param>
            <returns>The line of text, not including the carriage return or line feed.</returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.ReadUntil(System.Int32,System.String,System.Boolean@)">
            <summary>
            Reads until a specified string is found.
            </summary>
            <param name="maxCount">The maximum number of bytes to read.</param>
            <param name="match">The string to be found.</param>
            <param name="found"><c>true</c> if the string was found.</param>
            <returns>The data which has been read.</returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.ReadUntil(System.Int32,System.String[],System.Int32@)">
            <summary>
            Reads until one of the specified strings is found.
            </summary>
            <param name="maxCount">The maximum number of bytes to read.</param>
            <param name="matches">An array of strings to be found.</param>
            <param name="matchIndex">Value assigned upon return. A zero based index into the
            'matches' array to which the item was found. '-1' when the item was not found.
            </param>
            <returns>Data which was read (ends with match from supplied list).</returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.ReadUntil(System.Int32,System.Byte[],System.Boolean@)">
            <summary>
            Reads until a specified string is found.
            </summary>
            <param name="maxCount">The maximum number of bytes to read.</param>
            <param name="match">The array of bytes to be found.</param>
            <param name="found"><c>true</c> if the string was found.</param>
            <returns>The data which has been read.</returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.OldReadUntil(System.Int32,System.String[],System.Int32@)">
            <summary>
            Reads until one of the specified strings is found.
            </summary>
            <param name="maxCount">The maximum number of bytes to read.</param>
            <param name="matches">An array of strings to be found.</param>
            <param name="matchIndex">Value assigned upon return. A zero based index into the
            'matches' array to which the item was found. '-1' when the item was not found.
            </param>
            <returns>Data which was read (ends with match from supplied list).</returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.Write(System.String)">
            <summary>
            Writes the specified string to the connection.
            </summary>
            <param name="outputText">The text that should be written, which is converted using
            the current <see cref="P:CommStudio.Connections.Connection.Encoding"/>.</param>
        </member>
        <member name="M:CommStudio.Connections.Connection.Write(System.Byte)">
            <summary>
            Writes a byte to the connection.
            </summary>
            <param name="b">The byte that should be written.</param>
        </member>
        <member name="M:CommStudio.Connections.Connection.Flush">
            <summary>
            Clears all buffers for this connection and causes any buffered data to be discarded
            </summary>
            <remarks>
            The output buffer will be emptied while ensuring that the contents are transmitted
            (a synchronous operation). This is subject to flow control but not to write
            time-outs, and it will not return until all pending write operations have been
            transmitted. Also, this will not affect the contents of the input buffer.
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.Connection.Flush(System.Boolean,System.Int32)">
            <summary>
            Allows timeout to clear all buffers for this connection and causes any buffered
            data to either be discarded or written to the underlying device.
            </summary>
            <param name="discard">
            Indicates whether any buffered data should be discarded.
            </param>
            <param name="timeout">Number of milliseconds to wait before aborting the flush.</param>
            <remarks>
            Setting 'discard' to 'true' will flush the input and output buffers, the deleted
            characters are not transmitted. Setting 'discard' to 'false' will empty the output
            buffer while ensuring that the contents are transmitted (a synchronous operation).
            This last option is subject to flow control but not to write time-outs, and it
            will not return until all pending write operations have been transmitted. Also, a
            setting of 'false' will not affect the input buffer.
            </remarks>
            <exception cref="T:System.Exception">Timeout expired prior to completion of non-discard flush.</exception>
        </member>
        <member name="M:CommStudio.Connections.Connection.Flush(System.Boolean)">
            <summary>
            Clears all buffers for this connection and causes any buffered data to either be discarded or written to the underlying device.
            </summary>
            <param name="discard">Indicates whether any buffered data should be discarded.</param>
        </member>
        <member name="M:CommStudio.Connections.Connection.ShowPropertiesDialog(System.Windows.Forms.IWin32Window)">
            <summary>
            Shows a properties dialog to change the settings of this connection with the specified owner
            </summary>
            <param name="owner">Any object that implements IWin32Window that represents the top-level window that will own the modal dialog box. </param>
            <returns>DialogResult.OK if the user clicks OK in the dialog box; otherwise, DialogResult.Cancel.</returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.ShowPropertiesDialog">
            <summary>
            Shows a properties dialog to change the settings of this connection with a default owner
            </summary>
            <returns>DialogResult.OK if the user clicks OK in the dialog box; otherwise, DialogResult.Cancel.</returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:CommStudio.Connections.Connection.SupportsEightBitProtocol">
            <summary>
            Makes sure the current connection is valid for 8-bit protocols (X and Y Modem).
            </summary>
            <returns>If this connection will support the protocol.</returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.EmptyReceiveQueue(System.Int32,CommStudio.Connections.ConnectionKey)">
            <summary>
            Empties the input queue of all data, waiting a specified time for it to empty.
            </summary>
            <param name="timeout">Maximum time to wait for the queue to remain empty.</param>
            <param name="key">Key as defined by the user (may be null for no key).</param>
            <returns>If the queue is empty upon return.</returns>
            <remarks>
            The function will return when the earliest of: a read timeout or the specified timeout
            are reached. This method only affects the input (read) queue.
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.Connection.FireOpened">
            <summary>
            Method to call when an attempt to establish a new connection has been completed.
            </summary>
            <returns>'true' if the event was handled, else 'false' if no handlers are defined.</returns>
            <remarks>
            There should be no need to further derive from this method.
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.Connection.FireClosed">
            <summary>
            Method to call an attempt to close a connection was completed.
            </summary>
            <returns>'true' if the event was handled, else 'false' if no handlers are defined.</returns>
            <remarks>
            There should be no need to further derive from this method.
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.Connection.FireDeviceError(System.String)">
            <summary>
            Method to call when an error is encountered.
            </summary>
            <returns>'true' if the event was handled, else 'false' if no handlers are defined.</returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.FireDataAvailable">
            <summary>
            Method to call when a DataAvailable event is encountered.
            </summary>
            <returns>'true' if the event was handled, else 'false' if no handlers are defined.</returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.FireBreakReceived">
            <summary>
            Method to call when a break was encountered.
            </summary>
            <returns>'true' if the event was handled, else 'false' if no handlers are defined.</returns>
            <remarks>
            This is the base version which does nothing, you should derive a functional version from this
            method.
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.Connection.FireBreakReceivedBase">
            <summary>
            Single internal entry to call when a Break has been received.
            </summary>
            <returns></returns>
        </member>
        <member name="M:CommStudio.Connections.Connection.FireCtsChanged">
            <summary>
            Method to call when the CTS signal changed.
            </summary>
            <returns>'true' if the event was handled, else 'false' if no handlers are defined.</returns>
            <remarks>
            This is the base version which does nothing, you should derive a functional version from this
            method.
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.Connection.FireDsrChanged">
            <summary>
            Method to call when the DSR signal changed.
            </summary>
            <returns>'true' if the event was handled, else 'false' if no handlers are defined.</returns>
            <remarks>
            This is the base version which does nothing, you should derive a functional version from this
            method.
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.Connection.FireRingDetected">
            <summary>
            Method to call when the CTS signal changed.
            </summary>
            <returns>'true' if the event was handled, else 'false' if no handlers are defined.</returns>
            <remarks>
            This is the base version which does nothing, you should derive a functional version from this
            method.
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.Connection.FireCarrierChanged">
            <summary>
            Method to call when the carrier signal changed.
            </summary>
            <returns>'true' if the event was handled, else 'false' if no handlers are defined.</returns>
            <remarks>
            This is the base version which does nothing, you should derive a functional version from this
            method.
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.Connection.FireWriteBufferEmpty">
            <summary>
            Method to call when last character in write buffer was sent.
            </summary>
            <returns>'true' if the event was handled, else 'false' if no handlers are defined.</returns>
            <remarks>
            This is the base version which does nothing, you should derive a functional version from this
            method.
            </remarks>
        </member>
        <member name="P:CommStudio.Connections.Connection.DataAvailableThreshold">
            <summary>
            Indicates the number of bytes that need to be in the receive buffer before the DataAvailable event is fired.
            <exception cref="T:System.ArgumentException">The argument cannot be negative.</exception>		
            </summary>
        </member>
        <member name="P:CommStudio.Connections.Connection.Handle">
            <summary>
            Returns the operating system-level handle of the connection.
            </summary>
            <value>Handle of the communications port.</value>
        </member>
        <member name="P:CommStudio.Connections.Connection.SynchronizingObject">
            <summary>
            Gets or sets the object used to marshal the event handler calls issued from other threads
            </summary>
            <remarks>
            When SynchronizingObject is a null reference (Nothing in Visual Basic), event methods are
            called on a thread from the system thread pool. For more information on system thread pools,
            see ThreadPool.
            
            When the component is used in a Windows Forms environment, accessing the form or any of its
            controls through the system thread pool might not work, or may result in an exception. Avoid
            this by setting SynchronizingObject to a Windows Forms component, which causes the methods
            that handle the events to be called on the same thread on which the component was created.
            
            If the Connection component is used inside Visual Studio .NET in a Windows Forms designer,
            SynchronizingObject automatically sets to the control that contains the Connection. For
            example, if you place a Connection on a designer for Form1 (which inherits from Form) the
            SynchronizingObject property of Connection is set to the instance of Form1.
            </remarks>
        </member>
        <member name="P:CommStudio.Connections.Connection.Available">
            <summary>
            Returns the number of bytes available in the input buffer.
            </summary>
            <value>Number of bytes in the input buffer.</value>
        </member>
        <member name="P:CommStudio.Connections.Connection.ReadTimeOut">
            <summary>
            Defines the number of milliseconds the control should wait for data from the port.
            </summary>
            <value>Maximum number of milliseconds to wait for data in the port.</value>
        </member>
        <member name="P:CommStudio.Connections.Connection.WriteTimeOut">
            <summary>
            Defines the number of milliseconds the control should wait for data write into the port.
            </summary>
            <value>Maximum number of milliseconds to wait for the write to complete.</value>
        </member>
        <member name="P:CommStudio.Connections.Connection.Encoding">
            <summary>
            Defines the type of encoding used when converting between text strings and
            byte arrays.
            </summary>
            <value>Type of encoding to use.</value>
            <remarks>The default is <see cref="T:System.Text.ASCIIEncoding"/>.</remarks>
        </member>
        <member name="P:CommStudio.Connections.Connection.WaitMethod">
            <summary>
            Defines the method used when waiting for data.
            </summary>
            <value>Method to use while waiting for data.</value>
        </member>
        <member name="P:CommStudio.Connections.Connection.IsOpen">
            <summary>
             Gets a value indicating whether the current connection is open
            </summary>
            <value><c>true</c> if the connection is currently open (else it is closed)</value>
        </member>
        <member name="P:CommStudio.Connections.Connection.AutoShowStatusDialog">
            <summary>
            Determines if a status dialog should be shown when a new connection is established.
            </summary>
        </member>
        <member name="E:CommStudio.Connections.Connection.DeviceError">
            <summary>
            Event fired when an error happens while waiting on a device event.
            </summary>
        </member>
        <member name="E:CommStudio.Connections.Connection.Opened">
            <summary>
            Event fired when an attempt to establish a new connection was completed.
            </summary>
        </member>
        <member name="E:CommStudio.Connections.Connection.Closed">
            <summary>
            Event fired when an attempt to establish a new connection was completed.
            </summary>
        </member>
        <member name="E:CommStudio.Connections.Connection.DataAvailable">
            <summary>
            Event fired when new data is waiting in the input buffer.
            </summary>
            <remarks>
            This will fire an event whenever a new set of byte(s) arrives in 
            the input queue.
            </remarks>
        </member>
        <member name="T:CommStudio.Connections.Connection.CommErrorEventArgs">
            <summary>
            Data to be used for the communication error event handler.
            </summary>
        </member>
        <member name="M:CommStudio.Connections.Connection.CommErrorEventArgs.#ctor(System.String)">
            <summary>
            Default constructor for defining the data passed when an error has occurred
            in the communication driver.
            </summary>
            <param name="errorMessage">Text describing what error just occurred.</param>
        </member>
        <member name="P:CommStudio.Connections.Connection.CommErrorEventArgs.Message">
            <summary>
            Returns the description of the error which triggered this event.
            </summary>
            <value>Textual description of the error.</value>
        </member>
        <member name="T:CommStudio.Connections.Connection.DeviceErrorEventHandler">
            <summary>
            Event fired when an error happens while waiting on a device event.
            </summary>
            <param name="sender">Class sending the event.</param>
            <param name="e">Object containing information to describe the event.</param>
        </member>
        <member name="T:CommStudio.Connections.Connection.DataAvailableEventHandler">
            <summary>
            Event fired when new data is waiting in the input buffer.
            </summary>
            <param name="sender">Class sending the event.</param>
            <param name="e">Object containing information to describe the event.</param>
            <remarks>
            This event will fire an event whenever a new set of byte(s) arrives in 
            the input queue.
            </remarks>
        </member>
        <member name="T:CommStudio.Connections.Parity">
            <summary>
            Defines the type of parity checking that should be performed.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.Parity.None">
            <summary>
            No parity checking
            </summary>
        </member>
        <member name="F:CommStudio.Connections.Parity.Odd">
            <summary>
            The parity bit is set when the number of data bits that are set is odd.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.Parity.Even">
            <summary>
            The parity bit is set when the number of data bits that are set is even.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.Parity.Mark">
            <summary>
            The parity bit is always on.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.Parity.Space">
            <summary>
            The parity bit is always off.
            </summary>
        </member>
        <member name="T:CommStudio.Connections.CommStopBits">
            <summary>
            Defines the valid number of stop bits which may be used
            </summary>
        </member>
        <member name="F:CommStudio.Connections.CommStopBits.One">
            <summary>
            1 Stop bit.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.CommStopBits.OnePointFive">
            <summary>
            1.5 Stop bits.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.CommStopBits.Two">
            <summary>
            2 stop bits.
            </summary>
        </member>
        <member name="M:CommStudio.Connections.CommunicationsTimeoutException.#ctor">
            <summary>
            Default exception to throw when the request for data times out.
            </summary>
        </member>
        <member name="M:CommStudio.Connections.CommunicationsTimeoutException.#ctor(System.String)">
            <summary>
            Exception to throw when the request for data times out and description is supplied.
            </summary>
            <param name="message">A message that describes the error.</param>
        </member>
        <member name="M:CommStudio.Connections.CommunicationsTimeoutException.#ctor(System.String,System.Exception)">
            <summary>
            Exception to throw when the request for data times out and description and inner
            exception are supplied.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception. If the
            innerException parameter is not a null reference, the current exception is raised in a
            catch block that handles the inner exception. </param>
        </member>
        <member name="M:CommStudio.Connections.CommunicationsTimeoutException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Exception to throw when the request for data times out and serialization is required.
            </summary>
            <param name="info">The object that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:CommStudio.Connections.CommunicationsLockedPortException">
            <summary>
            Thrown when the port is locked from public usage by an active file transfer.
            </summary>
        </member>
        <member name="M:CommStudio.Connections.CommunicationsLockedPortException.#ctor">
            <summary>
            Exception is thrown when the port is locked from public usage by an active file transfer.
            </summary>
        </member>
        <member name="M:CommStudio.Connections.CommunicationsLockedPortException.#ctor(System.String)">
            <summary>
            Exception is thrown when the port is locked from public usage by an active file transfer.
            </summary>
            <param name="message">A message that describes the error.</param>
        </member>
        <member name="M:CommStudio.Connections.CommunicationsLockedPortException.#ctor(System.String,System.Exception)">
            <summary>
            Exception is thrown when the port is locked from public usage by an active file transfer.
            </summary>
            <param name="message">The error message that explains the reason for the exception.</param>
            <param name="innerException">The exception that is the cause of the current exception. If the
            innerException parameter is not a null reference, the current exception is raised in a
            catch block that handles the inner exception. </param>
        </member>
        <member name="M:CommStudio.Connections.CommunicationsLockedPortException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Exception is thrown when the port is locked from public usage by an active file transfer.
            </summary>
            <param name="info">The object that holds the serialized object data.</param>
            <param name="context">The contextual information about the source or destination.</param>
        </member>
        <member name="T:CommStudio.Connections.LookUpRing">
            <summary>
            Internal class used for ReadUntil functions.  It creates a ring buffer that should be just large enough
            for the largest string you will look for.  You can write to the ring buffer. When you call match it checks
            to see if the last characters written match the string.
            </summary>
        </member>
        <member name="T:CommStudio.Connections.SerialConnection">
            <summary>
            Lets you connect directly to a serial port.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialConnection.DefaultInputQueueSize">
            <summary>
            Default size for the input Queue.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialConnection.DefaultOutputQueueSize">
            <summary>
            Default size for the output Queue.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialConnection.serial">
            <summary>
            Provides all the core serial communications functionality
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialConnection.options">
            <summary>
            Currently active license for this component.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialConnection.m_InputQueueSize">
            <summary>
            Current size for the input queue (as set, not validated after opening).
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialConnection.m_OutputQueueSize">
            <summary>
            Current size for the output queue (as set, not validated after opening).
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialConnection.license">
            <summary>
            Used to store the license.
            </summary>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.#ctor(System.ComponentModel.IContainer)">
            <summary>
            Creates a new instance of the SerialConnection class inside a Windows Forms designer.
            </summary>
            <param name="container">Container to place this new instance of the SerialConnection class.</param>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.#ctor">
            <summary>
            Creates a new instance of the SerialConnection class.
            </summary>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.InitializeMe">
            <summary>
            Common initialization for all constructors of this class.
            </summary>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">
            If disposing equals true, the method has been called directly or indirectly by a user's code.
            Managed and unmanaged resources can be disposed.
            If disposing equals false, the method has been called by the runtime from inside the
            finalizer and you should not reference other objects.
            Only unmanaged resources can be disposed.
            </param>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.WaitForInput(System.Int32)">
            <summary>
            Wait for a certain number of bytes to be input using current Read timeout value.
            </summary>
            <param name="count">Number of bytes to wait for.</param>
            <returns>If the requested number of bytes are available for reading.</returns>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.WaitForInput(System.Int32,System.Int32,System.Int32)">
            <summary>
            Wait for a certain number of bytes to be input, allows complete definition of timeout.
            </summary>
            <param name="count">Number of bytes to wait for.</param>
            <param name="waitConst">Minimum amount of time to wait for input bytes.</param>
            <param name="waitMult">Milliseconds to wait for each byte to appear in the queue (default=0).</param>
            <returns>If the requested number of bytes are available for reading.</returns>
            <exception cref="T:System.IO.IOException">The specified port is not open.</exception>
            <remarks>
            The total time waiting for the specified bytes will be the constant value + the
            multiplier*[Total bytes waiting for].
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.ModemStatus">
            <summary>
            Gets the status of the modem control input signals.
            </summary>
            <returns>Modem status object</returns>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.ApplyOptions">
            <summary>
            Applies the currently set options to the current device. If the device is open, then
            the settings will happen immediately, else they will be delayed until Open() is
            called.
            </summary>
            <exception cref="T:System.Exception">General error trying to set state of device.</exception>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.Read(System.Byte[],System.Int32,System.Int32,CommStudio.Connections.ConnectionKey)">
            <summary>
            Reads bytes from the serial port into a byte array, not checking for locked condition.
            </summary>
            <param name="buffer">The buffer to read data into.</param>
            <param name="offset">The starting point in the buffer at which to begin reading
            into the buffer.</param>
            <param name="count">The number of characters to read.</param>
            <param name="key">Communication locking key.</param>
            <returns>The number of characters read into buffer. This might be less than the
            number of bytes requested if that many bytes are not available.</returns>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.Read(CommStudio.Connections.ConnectionKey)">
            <summary>
            Reads a byte from the connection or -1 if no bytes are available.
            </summary>
            <param name="key">Communication locking key.</param>
            <returns>The byte that was read, or -1 if no bytes are available.</returns>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.Write(System.Byte[],System.Int32,System.Int32,CommStudio.Connections.ConnectionKey)">
            <summary>
            Writes bytes from a byte array to the serial port, not checking for locked condition.
            </summary>
            <param name="buffer">The data to write to the stream. </param>
            <param name="offset">The location in the buffer to start writing data from.</param>
            <param name="count">The number of bytes to write to the stream.</param>
            <param name="key">Communication locking key.</param>
            <returns>Returns the number of bytes written.</returns>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.Write(System.Byte,CommStudio.Connections.ConnectionKey)">
            <summary>
            Writes a byte to the connection.
            </summary>
            <param name="b">The byte that should be written.</param>
            <param name="key">Communication locking key.</param>
        </member>
        <member name="F:CommStudio.Connections.SerialConnection.breakState">
            <summary>
            Private data to cache if the status of the Break State is true.
            </summary>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.Read">
            <summary>
            Reads a byte from the connection or -1 if no bytes are available.
            </summary>
            <returns>The byte that was read, or -1 if no bytes are available.</returns>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.ReadUnlocked">
            <summary>
            Reads a byte from the connection or -1 if no bytes are available.
            </summary>
            <returns>The byte that was read, or -1 if no bytes are available.</returns>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads bytes from the serial port into a byte array.
            </summary>
            <param name="buffer">The buffer to read data into. </param>
            <param name="offset">The starting point in the buffer at which to begin reading into the buffer. </param>
            <param name="count">The number of characters to read.</param>
            <returns>The number of characters read into buffer. This might be less than the number of bytes requested if that many bytes are not available.</returns>
            <exception cref="T:System.ArgumentException">The buffer length minus index is less than count.</exception>
            <exception cref="T:System.ArgumentNullException">buffer is a null reference (Nothing in Visual Basic).</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">offset or count is negative</exception>
            <exception cref="T:System.IO.IOException">The specified port is not open.</exception>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.ReadUnlocked(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Internal implementation of reads bytes from the serial port into a byte array (no lock check).
            </summary>
            <param name="buffer">The buffer to read data into. </param>
            <param name="offset">The starting point in the buffer at which to begin reading into the buffer. </param>
            <param name="count">The number of characters to read.</param>
            <returns>The number of characters read into buffer. This might be less than the number of bytes requested if that many bytes are not available.</returns>
            <exception cref="T:System.ArgumentException">The buffer length minus index is less than count.</exception>
            <exception cref="T:System.ArgumentNullException">buffer is a null reference (Nothing in Visual Basic).</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">offset or count is negative</exception>
            <exception cref="T:System.IO.IOException">The specified port is not open.</exception>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.Write(System.Byte)">
            <summary>
            Writes a byte to the connection.
            </summary>
            <param name="b">The byte that should be written.</param>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.WriteUnlocked(System.Byte)">
            <summary>
            Writes a byte to the connection (w/out lock checking).
            </summary>
            <param name="b">The byte that should be written.</param>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes bytes from a byte array to the serial port.
            </summary>
            <param name="buffer">The data to write to the stream. </param>
            <param name="offset">The location in the buffer to start writing data from.</param>
            <param name="count">The number of bytes to write to the stream.</param>
            <returns>Returns the number of bytes written to the port.</returns>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.WriteUnlocked(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes bytes from a byte array to the serial port.
            </summary>
            <param name="buffer">The data to write to the stream. </param>
            <param name="offset">The location in the buffer to start writing data from.</param>
            <param name="count">The number of bytes to write to the stream.</param>
            <returns>Returns the number of bytes written to the port.</returns>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.Peek">
            <summary>
            Returns the next available character but does not consume it.
            </summary>
            <returns>The next character to be read, or -1 if no characters are available.</returns>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.Peek(CommStudio.Connections.ConnectionKey)">
            <summary>
            Returns the next available character but does not consume it (key protected version).
            </summary>
            <param name="key">Key as defined by the user (may be null for no key).</param>
            <returns>The next character to be read, or -1 if no characters are available.</returns>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.Flush(System.Boolean)">
            <summary>
            Clears all buffers for this connection and causes any buffered data to either be
            discarded or written to the underlying device.
            </summary>
            <param name="discard">
            Indicates whether any buffered data should be discarded.
            </param>
            <remarks>
            Setting 'discard' to 'true' will flush the input and output buffers, the deleted
            characters are not transmitted. Setting 'discard' to 'false' will empty the output
            buffer while ensuring that the contents are transmitted (a synchronous operation).
            This last option is subject to flow control but not to write time-outs, and it
            will not return until all pending write operations have been transmitted. Also, a
            setting of 'false' will not affect the input buffer.
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.Flush(System.Boolean,System.Int32)">
            <summary>
            Allows timeout to clear all buffers for this connection and causes any buffered
            data to either be discarded or written to the underlying device.
            </summary>
            <param name="discard">
            Indicates whether any buffered data should be discarded.
            </param>
            <param name="timeout">Number of milliseconds to wait before aborting the flush.</param>
            <remarks>
            Setting 'discard' to 'true' will flush the input and output buffers, the deleted
            characters are not transmitted. Setting 'discard' to 'false' will empty the output
            buffer while ensuring that the contents are transmitted (a synchronous operation).
            This last option is subject to flow control but not to write time-outs, and it
            will not return until all pending write operations have been transmitted. Also, a
            setting of 'false' will not affect the input buffer.
            </remarks>
            <exception cref="T:System.Exception">Timeout expired prior to completion of non-discard flush.</exception>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.EmptyReceiveQueue(System.Int32,CommStudio.Connections.ConnectionKey)">
            <summary>
            Empties the input queue of all data, waiting a specified time for it to empty.
            </summary>
            <param name="timeout">Maximum time to wait for the queue to remain empty.</param>
            <param name="key">Key as defined by the user (may be null for no key).</param>
            <returns>If the queue is empty upon return.</returns>
            <remarks>
            The function will return when the earliest of: a read timeout or the specified timeout
            are reached. This method only affects the input (read) queue.
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.Open">
            <summary>
            Opens a connection to the device.
            </summary>
            <exception cref="T:System.IO.IOException">Error encountered opening the device.</exception>
            <exception cref="T:System.ArgumentException">General error occurred, check message for details.</exception>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.Open(System.Int64,System.Int64)">
             <summary>
             Opens a connection to the device.
             </summary>
             <param name="inputQueueSize">Specifies the recommended size, in bytes, of the device's
             internal input buffer.</param>
             <param name="outputQueueSize">Specifies the recommended size, in bytes, of the device's
             internal output buffer.</param>
             <exception cref="T:System.IO.IOException">Error encountered opening the device - check error message for details.</exception>
             <exception cref="T:System.ArgumentException">General error occurred, check message for details.</exception>
             <remarks>
              If either Queue size is invalid for a device, the device uses the default parameters
              when the first call to another communications function occurs. 
            
              The inputQueueSize and outputQueueSize parameters specify the recommended sizes for the internal
              buffers used by the driver for the specified device.
            
              The device driver receives the recommended buffer sizes, but is free to use any input and
              output (I/O) buffering scheme, as long as it provides reasonable performance and data is
              not lost due to overrun (except under extreme circumstances). For example, the function 
              can succeed even though the driver does not allocate a buffer, as long as some other 
              portion of the system provides equivalent functionality.
             </remarks>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.Close">
            <summary>
            Closes the connection to the current device
            </summary>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.SetOptions(CommStudio.Connections.SerialOptions)">
            <summary>
            Assigns the supplied options to the current device. If the device is open, then
            the settings will happen immediately, else they will be delayed until Open() is
            called.
            </summary>
            <param name="newOptions">Set of options to use for the current device.</param>
            <exception cref="T:System.Exception">General error trying to set state of device.</exception>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.ShowPropertiesDialog(System.Windows.Forms.IWin32Window)">
            <summary>
            Shows a properties dialog to change the settings of this connection with the specified owner
            </summary>
            <param name="owner">Any object that implements IWin32Window that represents the top-level window that will own the modal dialog box. </param>
            <returns>DialogResult.OK if the user clicks OK in the dialog box; otherwise, DialogResult.Cancel.</returns>
            <remarks>Please call the Application.EnableVisualStyles method at the start of your application to make sure the properties window has modern look and feel.</remarks>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.SupportsEightBitProtocol">
            <summary>
            Makes sure the current connection is valid for 8-bit protocols (X and Y Modem)
            </summary>
            <returns>If this connection will support the protocol.</returns>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.FireBreakReceived">
            <summary>
            Method to call when a break was encountered.
            </summary>
            <returns>'true' if the event was handled, else 'false' if no handlers are defined.</returns>
            <remarks>
            There should be no need to further derive from this method.
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.FireCtsChanged">
            <summary>
            Method to call when the CTS signal changed.
            </summary>
            <returns>'true' if the event was handled, else 'false' if no handlers are defined.</returns>
            <remarks>
            There should be no need to further derive from this method.
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.FireDsrChanged">
            <summary>
            Method to call when the DSR signal changed.
            </summary>
            <returns>'true' if the event was handled, else 'false' if no handlers are defined.</returns>
            <remarks>
            There should be no need to further derive from this method.
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.FireRingDetected">
            <summary>
            Method to call when the ring indicator is detected.
            </summary>
            <returns>'true' if the event was handled, else 'false' if no handlers are defined.</returns>
            <remarks>
            There should be no need to further derive from this method.
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.FireCarrierChanged">
            <summary>
            Method to call when the carrier signal changed.
            </summary>
            <returns>'true' if the event was handled, else 'false' if no handlers are defined.</returns>
            <remarks>
            There should be no need to further derive from this method.
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.SerialConnection.FireWriteBufferEmpty">
            <summary>
            Method to call when last character in write buffer was sent.
            </summary>
            <returns>'true' if the event was handled, else 'false' if no handlers are defined.</returns>
        </member>
        <member name="P:CommStudio.Connections.SerialConnection.Cts">
            <summary>
            Gets the status of the Clear-To-Send (CTS) signal.
            </summary>
            <value>Current status of the CTS signal - <c>true</c>=on, else off</value>
            <remarks>The function returns <c>false</c> if the hardware does not support
            the CTS signal.</remarks>
        </member>
        <member name="P:CommStudio.Connections.SerialConnection.Rts">
            <summary>
            Sets the status (clear/send) of the RTS (request-to-send) signal.
            </summary>
            <value>Status of the RTS signal - <c>true</c>=send, <c>false</c>=clear</value>
            <remarks>The current status of the RTS (get) is not available.</remarks>
        </member>
        <member name="P:CommStudio.Connections.SerialConnection.Dsr">
            <summary>
            Gets the status of the DSR (data-set-ready) signal.
            </summary>
            <value>Status of the DSR indicator signal - <c>true</c>=signal is on, <c>false</c>=off.</value>
            <remarks>The set accessor is not available for this property.</remarks>
        </member>
        <member name="P:CommStudio.Connections.SerialConnection.Dtr">
            <summary>
            Sets the status of the DTR (data-terminal-ready) signal.
            </summary>
            <value>Status of the DTR signal - <c>true</c>=send, <c>false</c>=clear</value>
            <remarks>The current status of the DTR is not available.</remarks>
        </member>
        <member name="P:CommStudio.Connections.SerialConnection.Ring">
            <summary>
            Gets the status of the Ring indicator signal.
            </summary>
            <value>Status of the Ring indicator signal - <c>true</c>=signal is on, <c>false</c>=off.</value>
            <remarks>The set accessor is not available for this property.</remarks>
        </member>
        <member name="P:CommStudio.Connections.SerialConnection.Carrier">
            <summary>
            Gets the status of the Carrier/Receive Line Signal Detect (RLSD) signal.
            </summary>
            <value>Status of the RLSD signal - <c>true</c>=signal is on, <c>false</c>=off.</value>
            <remarks>The set accessor is not available for this property.</remarks>
        </member>
        <member name="P:CommStudio.Connections.SerialConnection.Xon">
            <summary>
            Causes the control to act as if a Xon (true) or Xoff (false) character was received.
            </summary>
            <value>Transmission will act as if a Xon character has been received - <c>true</c>=Xon,
            <c>false</c>=Xoff.</value>
            <remarks>The get accessor is not available for this property.</remarks>
        </member>
        <member name="P:CommStudio.Connections.SerialConnection.Break">
            <summary>
            Suspends or restores character transmission for the device, placing the status of the Break
            state as <c>set</c> (suspended transmission) or <c>Cleared</c> (non-break) state.
            </summary>
            <value>If character transmission is suspended and line is in break state - <c>true</c>=break,
            <c>false</c>=non-break.</value>
            <remarks>
            A communications device is placed in a break state by the setting this property to <c>true</c>.
            Character transmission is then suspended until the break state is cleared by setting this value
            to <c>false</c> (non-break state).
            Note: This does <c>not</c> flush data that has not been transmitted. 
            </remarks>
        </member>
        <member name="P:CommStudio.Connections.SerialConnection.Handle">
            <summary>
            Returns the operating-system level handle of the communications port.
            </summary>
            <value>Handle of the communications port.</value>
        </member>
        <member name="P:CommStudio.Connections.SerialConnection.Options">
            <summary>
            Determines the current serial port and its settings.
            </summary>
            <value>Settings for current serial port.</value>
        </member>
        <member name="P:CommStudio.Connections.SerialConnection.Available">
            <summary>
            Returns the number of bytes available in the input buffer.
            </summary>
            <value>Number of bytes in the input buffer.</value>
            <exception cref="T:System.IO.IOException">The specified port is not open.</exception>
            <remarks>
            This property reports how many bytes are waiting to be read from the receive buffer.
            <seealso cref="P:CommStudio.Connections.SerialConnection.Unwritten"/>
            </remarks>
        </member>
        <member name="P:CommStudio.Connections.SerialConnection.ReadTimeOut">
            <summary>
            Defines the number of milliseconds the control should wait for input data from the port.
            </summary>
            <value>Number of milliseconds to wait for data in the input port before failing on the read.</value>
            <remarks>
            <list type="bullet">
            <item>
            Allowable values are between zero and 4294967295.
            </item>
            <item>
            This action will be performed immediately on an open port.
            </item>
            <item>
            A value of zero specifies that the read operation is to return immediately with the
            bytes that have already been received, even if no bytes have been received. 
            </item>
            </list>
            </remarks>
        </member>
        <member name="P:CommStudio.Connections.SerialConnection.WriteTimeOut">
            <summary>
            Defines the number of milliseconds the control should wait for data to travel through the output port.
            </summary>
            <value>Number of milliseconds to wait for data in the input port before failing on the read.</value>
            <remarks>
            <list type="bullet">
            <item>
            Allowable values are between zero and 4294967295.
            </item>
            <item>
            This action will be performed immediately on an open port.
            </item>
            <item>
            A value of zero specifies that the write operation is to wait indefinitely for the
            bytes to be sent through the device.
            </item>
            </list>
            </remarks>
        </member>
        <member name="P:CommStudio.Connections.SerialConnection.Unwritten">
            <summary>
            Returns the number of bytes which remain in the output buffer and are waiting to be sent.
            </summary>
            <value>Number of waiting bytes in the output buffer.</value>
            <exception cref="T:System.IO.IOException">The specified port is not open.</exception>
            <remarks>
            This function reports how many bytes are waiting to be sent, not how much room is left
            within the output buffer. <seealso cref="P:CommStudio.Connections.SerialConnection.Available"/>
            </remarks>
        </member>
        <member name="P:CommStudio.Connections.SerialConnection.IsOpen">
            <summary>
             Gets a value indicating whether the current connection is open.
            </summary>
            <value>If the current connection is open.</value>
        </member>
        <member name="E:CommStudio.Connections.SerialConnection.BreakReceived">
            <summary>
            Event fired when a 'break' is detected on input.
            </summary>
            <paramref name="sender"/>Class sending the event.
            <paramref name="e"/>Object containing information to describe the event.
        </member>
        <member name="E:CommStudio.Connections.SerialConnection.CtsChanged">
            <summary>
            Event fired when CTS signal changed.
            </summary>
        </member>
        <member name="E:CommStudio.Connections.SerialConnection.DsrChanged">
            <summary>
            Event fired when the DSR signal has changed on input.
            </summary>
        </member>
        <member name="E:CommStudio.Connections.SerialConnection.RingDetected">
            <summary>
            Event fired to report the detection of Ring indicator.
            </summary>
        </member>
        <member name="E:CommStudio.Connections.SerialConnection.CarrierChanged">
            <summary>
            Event Fires whenever the Carrier signal changes.
            </summary>
        </member>
        <member name="E:CommStudio.Connections.SerialConnection.WriteComplete">
            <summary>
            Event fires to report when the last character in write buffer was sent.
            </summary>
        </member>
        <member name="T:CommStudio.Connections.SerialConnection.BreakEventHandler">
            <summary>
            Event fired when a 'break' is detected on input.
            </summary>
            <param name="sender">Class sending the event.</param>
            <param name="e">Object containing information to describe the event.</param>
        </member>
        <member name="T:CommStudio.Connections.SerialConnection.CtsEventHandler">
            <summary>
            Event fired when CTS signal changed.
            </summary>
            <param name="sender">Class sending the event.</param>
            <param name="e">Object containing information to describe the event.</param>
        </member>
        <member name="T:CommStudio.Connections.SerialConnection.DsrEventHandler">
            <summary>
            Event fired when the DSR signal has changed on input.
            </summary>
            <param name="sender">Class sending the event.</param>
            <param name="e">Object containing information to describe the event.</param>
        </member>
        <member name="T:CommStudio.Connections.SerialConnection.RingEventHandler">
            <summary>
            Fired when a ring indicator is detected.
            </summary>
            <param name="sender">Class sending the event.</param>
            <param name="e">Object containing information to describe the event.</param>
        </member>
        <member name="T:CommStudio.Connections.SerialConnection.CarrierEventHandler">
            <summary>
            Event fired when the Carrier signal changes.
            </summary>
            <param name="sender">Class sending the event.</param>
            <param name="e">Object containing information to describe the event.</param>
        </member>
        <member name="T:CommStudio.Connections.SerialConnection.WriteCompleteEventHandler">
            <summary>
            Event fires when the last character in the output buffer was sent.
            </summary>
            <param name="sender">Class sending the event.</param>
            <param name="e">Object containing information to describe the event.</param>
        </member>
        <member name="T:CommStudio.Connections.SerialHelper">
            <summary>
            SerialHelper contains all the functionality in common by classes that use a type of
            serial connection, such as the SerialConnection and ModemConnection classes.  In the
            future it can also be used by InfraredConnection and (perhaps)  BluetoothConnection.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialHelper.NoByte">
            <summary>
            Flag denoting no bytes are available
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialHelper.connection">
            <summary>
            The object that this helper class is used for
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialHelper.portHandle">
            <summary>
            Handle to the port (set to Win32.INVALID_HANDLE_VALUE when not opened)
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialHelper.peek">
            <summary>
            The stored 'peek' character
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialHelper.m_readTimeOut">
            <summary>
            Milliseconds before the read methods will timeout.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialHelper.m_writeTimeOut">
            <summary>
            Milliseconds before the write methods will timeout.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialHelper.m_trackIncomingByteSize">
            <summary>
            Flag to tell thread to optimize how many hardware hits we need to monitor available bytes.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialHelper.m_totalAvailableBytes">
            <summary>
            Optimize count of many bytes are available in the input queue (only valid while m_trackIncomingByteSize is true).
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialHelper.rxThread">
            <summary>
            Thread used for receiving data.
            </summary>
        </member>
        <member name="M:CommStudio.Connections.SerialHelper.#ctor(CommStudio.Connections.Connection)">
            <summary>
            Constructs a new instance of the SerialHelper class
            </summary>
            <param name="parent">The connection that helper class is used for</param>
        </member>
        <member name="M:CommStudio.Connections.SerialHelper.ClearCommError(System.IntPtr,CommStudio.Win32.COMSTAT@)">
            <summary>
            Clears the current error reported and returns a descriptive string about the problem.
            </summary>
            <param name="handle">Handle to port which failed.</param>
            <param name="comStat">Status flag reported by the hardware.</param>
            <returns>String to report user describing the problem.</returns>
        </member>
        <member name="M:CommStudio.Connections.SerialHelper.BuildUartErrorString(System.UInt32)">
            <summary>
            generates a general purpose error string to report what kind of read error occurred.
            </summary>
            <param name="errorCode">The error code returned by the hardware port (MSComm).</param>
            <returns></returns>
        </member>
        <member name="M:CommStudio.Connections.SerialHelper.CheckPortOpen">
            <summary>
            Throw exception if port is not open
            </summary>
            <exception cref="T:System.IO.IOException">The specified port is not open.</exception>
        </member>
        <member name="M:CommStudio.Connections.SerialHelper.WaitForInput(System.Int32)">
            <summary>
            Wait for a certain number of bytes to be input using current Read timeout value.
            </summary>
            <param name="count">Number of bytes to wait for.</param>
            <returns>If the requested number of bytes are available for reading.</returns>
        </member>
        <member name="M:CommStudio.Connections.SerialHelper.WaitForInput(System.Int32,System.Int32,System.Int32)">
            <summary>
            Wait for a certain number of bytes to be input, allows complete definition of timeout.
            </summary>
            <param name="count">Number of bytes to wait for.</param>
            <param name="waitConst">Minimum amount of time to wait for input bytes.</param>
            <param name="waitMult">Milliseconds to wait for each byte to appear in the queue (default=0).</param>
            <returns>If the requested number of bytes are available for reading.</returns>
            <exception cref="T:System.IO.IOException">The specified port is not open.</exception>
            <remarks>
            The total time waiting for the specified bytes will be the constant value + the
            multiplier*[Total bytes waiting for].
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.SerialHelper.SetTimeouts">
            <summary>
            Sets the timeout values according to current option settings.
            </summary>
            <remarks>
            This is currently set so that a timeout is fired if more than the specified timeout occurs
            per character (e.g. you have #bytes * timeout to get all characters through).
            There is no timeout associated with the transfer (output), you can flush that buffer if you
            want it cleared.
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.SerialHelper.ReadCore(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads data directly from the device without using the Peek buffer.
            </summary>
            <param name="buffer">Byte array to place the input bytes.</param>
            <param name="offset">First byte in array to place the results (zero based).</param>
            <param name="count">Number of bytes to read.</param>
            <returns>Number of bytes which were actually read from the input device.</returns>
            <remarks>
            Be careful not to expose this directly to the public API, the Peek buffering system
            may have another byte cached off. The Readxxx() members all correctly deal with this
            byte.
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.SerialHelper.WriteCore(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes data directly to the device.
            </summary>
            <param name="buffer">Byte array to get the bytes from.</param>
            <param name="offset">First byte in array to write (zero based).</param>
            <param name="count">Number of bytes to write.</param>
            <returns>Number of bytes correctly written to the device.</returns>
            <exception cref="T:System.IO.IOException">The specified port is not open.</exception>
            <remarks>
            Be careful not to expose this directly to the public API, the Peek buffering system
            may have another byte cached off. The Readxxx() members all correctly deal with this
            byte.
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.SerialHelper.EmptyReceiveQueueUnlocked(System.Int32)">
            <summary>
            Empties the input queue of all data, waiting a specified time for it to empty.
            </summary>
            <param name="timeout">Maximum time to wait for the queue to remain empty.</param>
            <returns>If the queue is empty upon return.</returns>
            <remarks>
            The function will return when the earliest of: a read timeout or the specified timeout
            are reached. This method only affects the input (read) queue.
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.SerialHelper.Flush(System.Boolean,System.Int32)">
            <summary>
            Allows timeout to clear all buffers for this connection and causes any buffered
            data to either be discarded or written to the underlying device.
            </summary>
            <param name="discard">
            Indicates whether any buffered data should be discarded.
            </param>
            <param name="timeout">Number of milliseconds to wait before aborting the flush.</param>
            <remarks>
            Setting 'discard' to 'true' will flush the input and output buffers, the deleted
            characters are not transmitted. Setting 'discard' to 'false' will empty the output
            buffer while ensuring that the contents are transmitted (a synchronous operation).
            This last option is subject to flow control but not to write time-outs, and it
            will not return until all pending write operations have been transmitted. Also, a
            setting of 'false' will not affect the input buffer.
            </remarks>
            <exception cref="T:System.Exception">Timeout expired prior to completion of non-discard flush.</exception>
        </member>
        <member name="M:CommStudio.Connections.SerialHelper.ReadUnlocked">
            <summary>
            Reads a byte from the connection or -1 if no bytes are available.
            </summary>
            <returns>The byte that was read, or -1 if no bytes are available.</returns>
        </member>
        <member name="M:CommStudio.Connections.SerialHelper.ReadUnlocked(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Internal implementation of reads bytes from the serial port into a byte array (no lock check).
            </summary>
            <param name="buffer">The buffer to read data into.</param>
            <param name="offset">The starting point in the buffer at which to begin writing into the buffer.</param>
            <param name="count">The number of characters to read.</param>
            <returns>The number of characters read and placed in the buffer. This might be less than the
            number of bytes requested if that many bytes are not available.</returns>
            <exception cref="T:System.ArgumentException">The buffer length minus index is less than count.</exception>
            <exception cref="T:System.ArgumentNullException">buffer is a null reference (Nothing in Visual Basic).</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">offset or count is negative</exception>
            <exception cref="T:System.IO.IOException">The specified port is not open.</exception>
        </member>
        <member name="M:CommStudio.Connections.SerialHelper.WriteUnlocked(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes bytes from a byte array to the serial port.
            </summary>
            <param name="buffer">The data to write to the stream. </param>
            <param name="offset">The location in the buffer to start writing data from.</param>
            <param name="count">The number of bytes to write to the stream.</param>
            <returns>Returns the number of bytes written to the port.</returns>
        </member>
        <member name="M:CommStudio.Connections.SerialHelper.WriteUnlocked(System.Byte)">
            <summary>
            Writes a byte to the connection (w/out lock checking).
            </summary>
            <param name="b">The byte that should be written.</param>
        </member>
        <member name="M:CommStudio.Connections.SerialHelper.SetCommMask">
            <summary>
            Sets up the communication mask- returns false if bad hardware.
            </summary>
            <returns>'true' of mask set OK, false if it failed (hardware problem).</returns>
        </member>
        <member name="M:CommStudio.Connections.SerialHelper.Close">
            <summary>
            Close the port.
            </summary>
        </member>
        <member name="M:CommStudio.Connections.SerialHelper.ReceiveDataListener">
            <summary>
            Independent thread to connection. Fire an event whenever some new piece of data is found in the
            input buffer.
            </summary>
            <remarks>
            This function will run forever until you kill it, thus it is expected to be
            run within a thread that you can kill when you are through with it!
            </remarks>
        </member>
        <member name="P:CommStudio.Connections.SerialHelper.TotalAvailableBytes">
            <summary>
            Optimize count of how many bytes are available in the input queue.
            </summary>
            <remarks>
            This function is always valid, it will take advantage of the fastest means to report
            the number of bytes.
            </remarks>
        </member>
        <member name="P:CommStudio.Connections.SerialHelper.Handle">
            <summary>
            The handle of the serial connection as used by the Windows API
            </summary>
            <value>Handle of the communications port.</value>
        </member>
        <member name="P:CommStudio.Connections.SerialHelper.Available">
            <summary>
            Returns the number of bytes available in the input buffer.
            </summary>
            <value>Number of bytes in the input buffer.</value>
            <exception cref="T:System.IO.IOException">The specified port is not open.</exception>
            <remarks>
            This property reports how many bytes are waiting to be read from the receive buffer.
            </remarks>
        </member>
        <member name="P:CommStudio.Connections.SerialHelper.ReadTimeOut">
            <summary>
            Timeout for reading milliseconds (default=1000)
            </summary>
        </member>
        <member name="P:CommStudio.Connections.SerialHelper.WriteTimeOut">
            <summary>
            Access to the timeout for writing (in milliseconds)
            </summary>
        </member>
        <member name="P:CommStudio.Connections.SerialHelper.Unwritten">
            <summary>
            Returns the number of bytes which remain in the output buffer and are waiting to be sent.
            </summary>
            <value>Number of waiting bytes in the output buffer.</value>
            <exception cref="T:System.IO.IOException">The specified port is not open.</exception>
            <remarks>
            This function reports how many bytes are waiting to be sent, not how much room is left
            within the output buffer. <seealso cref="P:CommStudio.Connections.SerialHelper.Available"/>
            </remarks>
        </member>
        <member name="P:CommStudio.Connections.SerialHelper.IsOpen">
            <summary>
             Gets a value indicating whether the current connection is open.
            </summary>
            <value>If the current connection is open.</value>
        </member>
        <member name="T:CommStudio.Connections.SerialOptions">
            <summary>
            Defines the settings for a serial port connection.
            </summary>
        </member>
        <member name="M:CommStudio.Connections.SerialOptions.#ctor(System.String,System.Int32,CommStudio.Connections.Parity,System.Int32,CommStudio.Connections.CommStopBits,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Creates a new instance of the <see cref="T:CommStudio.Connections.SerialOptions"/> object.
            </summary>
            <param name="portName">Name of port to connect to.</param>
            <param name="baudRate">Defines the communications speed of the connection.</param>
            <param name="parity">The parity checking scheme to use.</param>
            <param name="dataBits">Defines the number of bits in each byte that is transmitted
            or received (1-255). </param>
            <param name="stopBits">Number of stop bits.</param>
            <param name="flowControlDsrDtr">Defines whether the DSR and DTR signals should be
            used for handshaking.</param>
            <param name="flowControlXonXoff">Defines whether Xon and Xoff characters should be
            used for handshaking.</param>
            <param name="flowControlCtsRts">Defines whether the CTS and RTS signals should be
            used for handshaking.</param>
            <param name="flowControlRs485">Defines whether this is an RS485 or RS232
            connection.</param>
            <param name="enableDtr">Defines whether the DTR signal should be enabled.</param>
            <param name="enableRts">Defines whether the RTS signal should be enabled.</param>
            <remarks>
             In order to access any COM port, append the port number to the end of the word
             "COM" (e.g. "COM7" for port 7).
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.SerialOptions.#ctor(CommStudio.Connections.SerialOptions)">
            <summary>
            Copy constructor to initialize a new instance of a
            <see cref="T:CommStudio.Connections.SerialOptions"/> object using an existing
            SerialOptions as a template for initial settings.
            </summary>
            <param name="copy">Existing SerialOptions to copy settings from.</param>
        </member>
        <member name="M:CommStudio.Connections.SerialOptions.#ctor(System.String,System.Int32,CommStudio.Connections.Parity)">
            <summary>
            Creates a new instance of the <see cref="T:CommStudio.Connections.SerialOptions"/> object.
            </summary>
            <param name="portName">Name of port to connect to.</param>
            <param name="baudRate">Defines the communications speed of the connection.</param>
            <param name="parity">The parity checking scheme to use.</param>
            <remarks>Handshaking will be set to use CTS and RTS (Hardware). The number of databits will be set to 8 of no Parity None is specified, or 7 otherwise.</remarks>
        </member>
        <member name="F:CommStudio.Connections.SerialOptions.m_portName">
            <summary>
            The name of the communications port used to make the connection.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialOptions.m_baudRate">
            <summary>
            Current BAUD rate.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialOptions.m_parity">
            <summary>
            Parity for the port.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialOptions.m_dataBits">
            <summary>
            The number of bits in each byte that is transmitted or received (5,6,7, or 8).
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialOptions.m_stopBits">
            <summary>
            How many stop bits.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialOptions.m_flowControlXonXoff">
            <summary>
            Should we use X-On/X-Off flow control?
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialOptions.m_flowControlDsrDtr">
            <summary>
            Should we use DSR/DTR flow control?
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialOptions.m_flowControlCtsRts">
            <summary>
            Should we use CTS/RTS flow control?
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialOptions.m_flowControlRs485">
            <summary>
            Whether this is a RS485 connection (else it is an RS232).
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialOptions.m_enableDtr">
            <summary>
            Whether the DTR signal should be enabled.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialOptions.m_enableRts">
            <summary>
            Whether the RTS signal should be enabled.
            </summary>
        </member>
        <member name="M:CommStudio.Connections.SerialOptions.FromShortParity(System.String)">
            <summary>
            Determine the Parity enum value from the text string.
            </summary>
            <param name="s">Text code defining what they want the parity to be.</param>
            <returns>The Parity enum value for the short text string.</returns>
        </member>
        <member name="M:CommStudio.Connections.SerialOptions.ToShortParity(CommStudio.Connections.Parity)">
            <summary>
            Determine the parity character (string) given the enum value.
            </summary>
            <param name="value">Parity enum of requested parity.</param>
            <returns>The short character string for this parity.</returns>
        </member>
        <member name="M:CommStudio.Connections.SerialOptions.CheckValid">
            <summary>
            Throws an exception if any of the properties are invalid.
            </summary>
            <exception cref="T:System.ArgumentException">When an invalid set of options are defined.
            The exception will contain a description of which arguments are invalid.
            <par>Applies to: Parity/DataBits.</par></exception>
            <exception cref="T:System.ArgumentOutOfRangeException">When an individual option item is set to
            an invalid value.<par>Applies to: DataBits</par></exception>
            <remarks>This member performs a very basic validation, only invalid conditions common
            to all port devices are checked. Additional conditions which generally cause an invalid
            connection are:
            1) Data Bits set to 5 and Stop Bits set to 2
            2) Data Bits set to 6 and Stop Bits set to 1.5
            3) Data Bits set to 7 and Stop Bits set to 1.5
            </remarks>
        </member>
        <member name="M:CommStudio.Connections.SerialOptions.ToString">
            <summary>
            Returns the options in a string.
            <seealso cref="M:CommStudio.Connections.SerialOptions.FromString(System.String)"/>
            </summary>
            <returns>String containing the options.</returns>
            <remarks>
            <para>The string has the following format: 
            PortName:BaudRate,Parity,DataBits,StopBits[,DTR,RTS,CTSRTS,DSRDTR,RS485,XONXOFF]</para>
            <list type="bullet">
            <item>
            Parity will be one of the following:
            <list type="bullet">
            <item>
            Even: e"
            </item>
            <item>
            Mark: "m"
            </item>
            <item>
            None: "n"
            </item>
            <item>
            Odd: "o"
            </item>
            <item>
            Space: "s"
            </item>
            </list>
            </item>
            <item>
            Data Bits must be 5, 6, 7, or 8
            </item>
            <item>
            The stop bits must be 1, 1.5, or 2. The closest valid value to the supplied
            value will be used
            </item>
            </list>
            <para>
            The following optional values are used to turn the property on (true)-
            </para>
            <list type="bullet">
            <item>
            EnableDtr: "DTR"
            </item>
            <item>
            EnableRts: "RTS"
            </item>
            <item>
            FlowControlCtsRts: "CTSRTS"
            </item>
            <item>
            FlowControlDsrDtr: "DSRDTR"
            </item>
            <item>
            FlowControlRs485: "RS485"
            </item>
            <item>
            FlowControlXonXoff: "XONXOFF"
            </item>
            </list>
            </remarks>
            <example>
            "COM3:9600,n,7,1.5,DTR,RTS,CTSRTS,DSRDTR,RS485,XONXOFF"
            </example>
        </member>
        <member name="M:CommStudio.Connections.SerialOptions.FromString(System.String)">
            <summary>
            Creates a new instance of the <see cref="T:CommStudio.Connections.SerialOptions"/> from a string.
            <seealso cref="M:CommStudio.Connections.SerialOptions.ToString"/>
            </summary>
            <param name="value">A string that describes the options</param>
            <returns>A new instance of the <see cref="T:CommStudio.Connections.SerialOptions"/>.</returns>
            <remarks>
            <para>The string has the following format: 
            PortName:BaudRate,Parity,DataBits,StopBits[,DTR,RTS,CTSRTS,DSRDTR,RS485,XONXOFF]</para>
            <list type="bullet">
            <item>
            Parity will be one of the following:
            <list type="bullet">
            <item>
            Even: e"
            </item>
            <item>
            Mark: "m"
            </item>
            <item>
            None: "n"
            </item>
            <item>
            Odd: "o"
            </item>
            <item>
            Space: "s"
            </item>
            </list>
            </item>
            <item>
            Data Bits must be 5, 6, 7, or 8
            </item>
            <item>
            The stop bits must be 1, 1.5, or 2. The closest valid value to the supplied
            value will be used
            </item>
            </list>
            <para>
            The following optional values are used to turn the property on (true)-
            </para>
            <list type="bullet">
            <item>
            EnableDtr: "DTR"
            </item>
            <item>
            EnableRts: "RTS"
            </item>
            <item>
            FlowControlCtsRts: "CTSRTS"
            </item>
            <item>
            FlowControlDsrDtr: "DSRDTR"
            </item>
            <item>
            FlowControlRs485: "RS485"
            </item>
            <item>
            FlowControlXonXoff: "XONXOFF"
            </item>
            </list>
            </remarks>
            <example>
            "COM3:9600,n,7,1.5,DTR,RTS,CTSRTS,DSRDTR,RS485,XONXOFF"
            </example>
        </member>
        <member name="P:CommStudio.Connections.SerialOptions.PortName">
            <summary>
            Defines the communications port used to make the connection.
            </summary>
            <value>Name of port used for current connection.</value>
        </member>
        <member name="P:CommStudio.Connections.SerialOptions.BaudRate">
            <summary>
            Defines the communications speed of the connection.
            </summary>
            <value>Baud rate at which the communication device operates.</value>
        </member>
        <member name="P:CommStudio.Connections.SerialOptions.Parity">
            <summary>
            Defines the type of parity checking that is performed.
            </summary>
            <value>Type of parity checking in effect.</value>
        </member>
        <member name="P:CommStudio.Connections.SerialOptions.DataBits">
            <summary>
            Defines the number of bits in each byte that is transmitted or received.
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">The value was less than 5 or greater than 8.
            </exception>
            <value>Number of bits to use in each byte.</value>
        </member>
        <member name="P:CommStudio.Connections.SerialOptions.StopBits">
            <summary>
            Defines the number of stopbits to be used.
            </summary>
            <value>Number of stopbits.</value>
        </member>
        <member name="P:CommStudio.Connections.SerialOptions.FlowControlXonXoff">
            <summary>
            Defines whether Xon and Xoff characters should be used for handshaking.
            </summary>
            <value>If Xon/Xoff handshaking should be used.</value>
        </member>
        <member name="P:CommStudio.Connections.SerialOptions.FlowControlDsrDtr">
            <summary>
            Defines whether the DSR and DTR signals should be used for handshaking.
            </summary>
            <value>If DSR/DTR signals are used for handshaking.</value>
        </member>
        <member name="P:CommStudio.Connections.SerialOptions.FlowControlCtsRts">
            <summary>
            Defines whether the CTS and RTS signals should be used for handshaking.
            </summary>
            <value>If CTS/RTS signals are used for handshaking.</value>
        </member>
        <member name="P:CommStudio.Connections.SerialOptions.FlowControlRs485">
            <summary>
            Defines whether this is an RS485 connection (else it is an RS232).
            </summary>
            <value>If this is an RS485 connection.</value>
        </member>
        <member name="P:CommStudio.Connections.SerialOptions.EnableDtr">
            <summary>
            Defines whether the DTR signal should be enabled.
            </summary>
            <value>If DTR signal is enabled.</value>
        </member>
        <member name="P:CommStudio.Connections.SerialOptions.EnableRts">
            <summary>
            Defines whether the RTS signal should be enabled.
            </summary>
            <value>If RTS signal is enabled.</value>
        </member>
        <member name="T:CommStudio.Connections.SerialPortInfo">
            <summary>
            Describes a serial port that is installed on the system.
            </summary>
        </member>
        <member name="M:CommStudio.Connections.SerialPortInfo.CompareTo(System.Object)">
            <summary>
            Compares this instance to a specified object and returns an indication of their relative values.
            </summary>
            <param name="obj">An object to compare to this instance, or a null reference (Nothing in Visual Basic).</param>
            <returns>A signed integer that indicates the relative order of this instance and obj.</returns>
        </member>
        <member name="M:CommStudio.Connections.SerialPortInfo.ToString">
            <summary>
            Returns the port name
            </summary>
            <returns>The Name property</returns>
        </member>
        <member name="P:CommStudio.Connections.SerialPortInfo.Ports">
            <summary>
            Returns an array that of SerialPortInfo objects
            describing the serial ports that are available on the system
            </summary>
        </member>
        <member name="P:CommStudio.Connections.SerialPortInfo.Name">
            <summary>
            The name of the port that is used by the system to identify the port, such as COM1.
            </summary>
        </member>
        <member name="P:CommStudio.Connections.SerialPortInfo.Description">
            <summary>
            A description of the port
            </summary>
        </member>
        <member name="T:CommStudio.Connections.SerialPropertiesWindow">
            <summary>
            Summary description for SerialPropertiesWindow.
            </summary>
        </member>
        <member name="F:CommStudio.Connections.SerialPropertiesWindow.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:CommStudio.Connections.SerialPropertiesWindow.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
        </member>
        <member name="M:CommStudio.Connections.SerialPropertiesWindow.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:CommStudio.Win32__0.#ctor">
            <summary>
            All contents are static or PInvoke, this prevents automatic constructor.
            </summary>
        </member>
        <member name="T:Sax.Communications.Dates">
            <summary>
            Defined hard times for when this assembly was compiled and expires
            </summary>
        </member>
        <member name="M:CommStudio.LicenseProvider.GetLicense(System.ComponentModel.LicenseContext,System.Type,System.Object,System.Boolean)">
            <summary>
            Gets a license for an instance or type of component, when given a context and whether
            the denial of a license throws an exception.
            </summary>
            <param name="context">A LicenseContext that specifies where you can use the licensed object.</param>
            <param name="type">A Type that represents the component requesting the license.</param>
            <param name="instance">An object that is requesting the license.</param>
            <param name="allowExceptions">true if a LicenseException should be thrown when the component
            cannot be granted a license; otherwise, false.</param>
            <returns>A valid License.</returns>
        </member>
        <member name="T:CommStudio.Transfers.newCrc16">
            <summary>
            Class to calculate a 16-bit CRC.
            </summary>
            <remarks>
            This is a general purpose class to calculate the 16-bit CRC value from a sequence of bytes.
            </remarks>
        </member>
        <member name="M:CommStudio.Transfers.newCrc16.#ctor">
            <summary>
            Initializes the 16-bit CRC calculator.
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.newCrc16.Crc(System.Byte[],System.Int32,System.Int32,System.Int16)">
            <summary>
            Returns the 16-bit CRC for the supplied array of bytes.
            </summary>
            <param name="ba">Array of bytes to calculate CRC from.</param>
            <param name="offset">Index of first byte to use in calculation.</param>
            <param name="count">Total number of bytes to include in calculation.</param>
            <param name="oldCrc">The crc code that is updated</param>
            <returns>Calculated CRC for this array.</returns>
        </member>
        <member name="M:CommStudio.Transfers.newCrc16.Crc(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Returns the 16-bit CRC for the supplied array of bytes.
            </summary>
            <param name="ba">Array of bytes to calculate CRC from.</param>
            <param name="offset">Index of first byte to use in calculation.</param>
            <param name="count">Total number of bytes to include in calculation.</param>
            <returns>Calculated CRC for this array.</returns>
        </member>
        <member name="M:CommStudio.Transfers.Crc32.Reset">
            <summary>
            Resets as if the checksum class was just created.
            </summary>
        </member>
        <member name="P:CommStudio.Transfers.Crc32.Value">
            <summary>
            Gets or sets the CRC32 value to this point
            </summary>
        </member>
        <member name="T:CommStudio.Transfers.TransferProtocol">
            <summary>
            Defines all the possible protocols for file transfer. (Professional Edition only)
            </summary>
            <remarks>
            This enumeration is only available in the Professional Edition.
            </remarks>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocol.XmodemChecksum">
            <summary>
            Original Xmodem protocol using simple checksum for error detection.
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocol.XmodemCrc">
            <summary>
            Xmodem using Cyclic Redundancy Check (CRC) for error detection.
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocol.Xmodem1K">
            <summary>
            Xmodem protocol using CRC for error detection and a 1024 byte buffer for transfer.
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocol.Ymodem">
            <summary>
            Ymodem protocol (uses CRC for error detection).
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocol.Zmodem">
            <summary>
            Zmodem protocol (uses CRC for error detection, a sliding window, and
            may restart from a disconnected file transfer).
            </summary>
        </member>
        <member name="T:CommStudio.Transfers.TransferStatus">
            <summary>
            The status of the latest or current file transfer session. (Professional Edition only)
            </summary>
            <remarks>
            This enumeration is only available in the Professional Edition.
            </remarks>
        </member>
        <member name="F:CommStudio.Transfers.TransferStatus.NotStarted">
            <summary>
            No file transfer has been started
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferStatus.Completed">
            <summary>
            The last file transfer session was completed successfully
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferStatus.CanceledByRemote">
            <summary>
            The last file transfer session was canceled by the remote computer
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferStatus.CanceledByLocal">
            <summary>
            The last file transfer session was canceled by the local computer
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferStatus.Failed">
            <summary>
            The last file transfer session failed unexepectedly
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferStatus.Uploading">
            <summary>
            A current file transfer session to upload files is in progress
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferStatus.Downloading">
            <summary>
            A current file transfer session to download files is in progress
            </summary>
        </member>
        <member name="T:CommStudio.Transfers.TransferFileStatus">
            <summary>
            The status of the latest file that was transferred (Professional Edition only)
            </summary>
            <remarks>
            This enumeration is only available in the Professional Edition.
            </remarks>
        </member>
        <member name="F:CommStudio.Transfers.TransferFileStatus.Completed">
            <summary>
            The file was transfered successfully
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferFileStatus.Failed">
            <summary>
            The file transfer failed 
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferFileStatus.Skipped">
            <summary>
            The file was skipped and it was not transferred
            </summary>
        </member>
        <member name="T:CommStudio.Transfers.Transfer">
            <summary>
            This component is used with the Connection class to perform a file transfer. (Professional Edition only)
            </summary>
            <remarks>
            This class is only available in the Professional Edition.
            </remarks>
        </member>
        <member name="F:CommStudio.Transfers.Transfer.path">
            <summary>
            base path to read/write files into
            </summary>		
        </member>
        <member name="F:CommStudio.Transfers.Transfer.protocol">
            <summary>
            What protocol is being used?
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.Transfer.synchronizingObject">
            <summary>
            Gets or sets the object used to marshal the event handler calls issued from different threads
            </summary>		
        </member>
        <member name="F:CommStudio.Transfers.Transfer.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.Transfer.transferEngine">
            <summary>
            Does all the core work...
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.Transfer.localFilename">
            <summary>
            The full name and path of the file on the local system (can be null if streams are used)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.Transfer.remoteFilename">
            <summary>
            The file as indiciated from/to the remote system (can be null for certain transfer protocols)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.Transfer.position">
            <summary>
            The size in bytes of the file or stream that is being transferred
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.Transfer.length">
            <summary>
            The size in bytes of the data that has already been transferrred.
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.Transfer.connection">
            <summary>
            A reference to the connection object used to send/receive data
            </summary>		
        </member>
        <member name="F:CommStudio.Transfers.Transfer.status">
            <summary>
            The status of the current transfer session
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.Transfer.insideBeginFileTransfer">
            <summary>
            Are inside a begin file transfer event, and can the LocalFilename and/or RemoteFilename property be changed?
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.Transfer.timeStarted">
            <summary>
            The moment the transfer was started, reset for each file
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.Transfer.license">
            <summary>
            Used to store the license.
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.Transfer.autoShowStatusDialog">
            <summary>
            Determines if a new status dialog should be shown when a new transfer is started
            </summary> 
        </member>
        <member name="M:CommStudio.Transfers.Transfer.#ctor(System.ComponentModel.IContainer)">
            <summary>
            Creates a new instance of the Transfer class inside a Windows Forms designer.
            </summary>
            <param name="container">Container to place this new instance of the Transfer class.</param>
        </member>
        <member name="M:CommStudio.Transfers.Transfer.#ctor">
            <summary>
            Creates a new instance of the Transfer class
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.Transfer.Dispose(System.Boolean)">
            <summary> 
            Clean up any resources being used.
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.Transfer.Upload(System.IO.Stream,System.String)">
            <summary>
            Uploads one file from a Stream with specific naming of the file for the
            receiving system.
            </summary>
            <param name="inputStream">Source data stream to send.</param>
            <param name="destinationFilename">Name to assign to the resulting file on the
            receiving system.</param>
            <returns>Completion status of the transfer.</returns>
            <remarks>
            <list type="bullet">
            <item>
            This is a synchronous process- your process will be wait until the transfer of
            this file has completed.
            </item>
            <item>
            Not all protocols assign a name to the resulting file, so the resulting name may differ
            from the input name.
            </item>
            </list>
            </remarks>
        </member>
        <member name="M:CommStudio.Transfers.Transfer.Upload(System.IO.Stream)">
            <summary>
            Uploads one file from a Stream with the destination name
            automatically generated.
            </summary>
            <param name="inputStream">Source data stream to send.</param>
            <returns>Completion status of the transfer.</returns>
            <remarks>
            <list>
            <item>
            Some protocols require that a name be assigned to the resulting file. For this case,
            a name will be generated according to automatic name generation rules.
            </item>
            <item>
            The stream must support seeking in order to determine the length in bytes of the stream.
            Without this ability, the transfer protocols would be unable to operate properly.
            </item>
            </list>
            </remarks>
            <exception cref="T:System.NotSupportedException">
            Thrown when the supplied stream does not support read or seek.
            </exception>
        </member>
        <member name="M:CommStudio.Transfers.Transfer.Upload(System.IO.Stream[])">
            <summary>
            Synchronously uploads a list of files.
            </summary>
            <param name="inputStreams">List of Streams to upload to another system.</param>
            <returns>
            Completion status - OK signifies that an attempt was made to upload each
            file in the list.
            </returns>
            <remarks>
            The return code does not tell you if all files were correctly Uploaded, only that
            an attempt was made to upload each file. A failure return on any file will store
            that value and return it.
            <list type="bullet">
            <item>
            This is a synchronous process- your process will be wait until the transfer of
            all files have completed.
            </item>
            <item>
            Not all protocols assign a name to the resulting file, so the resulting name may differ
            from the input name.
            </item>
            <item>
            Attempting to transfer more than one file using Xmodem may cause unexpected results 
            on the receiving side since no file separators are sent.
            </item>
            <item>
            The stream must support seeking in order to determine the length in bytes of the stream.
            Without this ability, the transfer protocols would be unable to operate properly.
            </item>
            </list>
            </remarks>
            <exception cref="T:System.NotSupportedException">
            Thrown when the supplied stream does not support the read and seek properties.
            </exception>
        </member>
        <member name="M:CommStudio.Transfers.Transfer.Upload(System.IO.Stream[],System.String[])">
            <summary>
            Synchronously uploads a list of Stream classes with the ability to produce new names on
            the receiving system.
            </summary>
            <param name="inputStreams">List of Streams to upload to another system.</param>
            <param name="remoteFilenames">List of names to assign to the resulting files on the
            receiving system.</param>
            <returns>Completion status of the transfer.</returns>
            <remarks>
            <list type="bullet">
            <item>
            This is a synchronous process- your process will be wait until the transfer of
            this file has completed.
            </item>
            <item>
            Attempting to transfer more than one file using Xmodem may cause unexpected results 
            on the receiving side since no file separators sent.
            </item>
            <item>
            The stream must support seeking in order to determine the length in bytes of the stream.
            Without this ability, the transfer protocols would be unable to operate properly.
            </item>
            </list>
            </remarks>
            <exception cref="T:System.NotSupportedException">
            Thrown when the supplied stream does not support the read and seek properties.
            </exception>
        </member>
        <member name="M:CommStudio.Transfers.Transfer.Download(System.IO.Stream)">
            <summary>
            Starts a file download which uses the supplied stream for the local output.
            </summary>
            <param name="outputStream">Source data stream to send.</param>
            <returns>Completion status of the transfer.</returns>
            <remarks>
            <list type="bullet">
            <item>
            This method will place the active thread in a wait state for a new file to begin a
            transfer using the active protocol on the current port.
            </item>
            <item>
            The output filename will be ignored from any protocol providing one a name.
            </item>
            <item>
            The restart option will not be used, even when the protocol supports it.
            </item>
            </list>
            </remarks>
        </member>
        <member name="M:CommStudio.Transfers.Transfer.Download(System.IO.Stream[])">
            <summary>
            Starts a transfer for a set of files, the resulting files use the default filename.
            </summary>
            <param name="outputStreams">List of Streams to use for output of the next set of
            arriving files.
            </param>
            <returns>Completion status of the transfer.</returns>
            <remarks>
            This method will place the active thread in a wait state until all files to finish transfer
            using the active protocol on the current port. Also, the return code tells you the worst
            case return code from all of the files in the supplied list.
            <list type="bullet">
            <item>
            If the current protocol is not Xmodem, the new filename will use that which is supplied.
            </item>
            <item>
            Other protocol types will be named according to the supplied argument (replacing the name
            sent via the transmission).
            </item>
            <item>
            For Xmodem, any existing file by this name will always be replaced.
            </item>
            <item>
            For protocols which support it and when the file already exists, the system will attempt
            to restart where it last stopped.
            </item>
            <item>
            Incoming requested filenames will be ignored.
            </item>
            <item>
            Any incoming transfers in excess of the available list will be assigned a default filename.
            </item>
            </list>
            </remarks>
        </member>
        <member name="M:CommStudio.Transfers.Transfer.Upload(System.String,System.String)">
            <summary>
            Uploads one file with specific naming of the file for the receiving system.
            </summary>
            <param name="localFilename">Name of the file to upload.</param>
            <param name="destinationFilename">Name to assign to the resulting file on the
            receiving system.</param>
            <returns>Completion status of the transfer.</returns>
            <remarks>
            <list type="bullet">
            <item>
            This is a synchronous process- your process will be wait until the transfer of
            this file has completed.
            </item>
            <item>
            Not all protocols assign a name to the resulting file, so the resulting name may differ
            from the input name.
            </item>
            </list>
            </remarks>
            <exception cref="T:System.IO.IOException">The process cannot access the file because it does
            not exist or is being used by another process.</exception>
            <exception cref="T:System.UnauthorizedAccessException">Access to the path or file is denied.</exception>
            <exception cref="T:System.IO.DirectoryNotFoundException">Could not find a part of the supplied path.</exception>
            <exception cref="T:System.IO.FileNotFoundException">Could not find the supplied file.</exception>
        </member>
        <member name="M:CommStudio.Transfers.Transfer.Upload(System.String)">
            <summary>
            Uploads one file with the destination name the same as the base input filename.
            </summary>
            <param name="localFilename">Name of the file to upload.</param>
            <returns>Completion status of the transfer.</returns>
            <remarks>
            Not all protocols assign a name to the resulting file, so the resulting name may differ
            from the input name.
            </remarks>
        </member>
        <member name="M:CommStudio.Transfers.Transfer.Upload(System.String[])">
            <summary>
            Synchronously uploads a list of files.
            </summary>
            <param name="filenames">List of files to upload to another system.</param>
            <returns>Completion status of the transfer.</returns>
            <remarks>
            <list type="bullet">
            <item>
            This is a synchronous process- your process will wait until the transfer of
            all files have completed.
            </item>
            <item>
            Not all protocols assign a name to the resulting file, so the resulting name may differ
            from the input name.
            </item>
            <item>
            Attempting to transfer more than one file using Xmodem may cause unexpected results 
            on the receiving side since no file separators are sent.
            </item>
            </list>
            </remarks>
        </member>
        <member name="M:CommStudio.Transfers.Transfer.Upload(System.String[],System.String[])">
            <summary>
            Synchronously uploads a list of files with the ability to produce new names on
            the receiving system.
            </summary>
            <param name="localFilenames">List of files to upload to another system.</param>
            <param name="remoteFilenames">List of names to assign to the resulting files on the
            receiving system.</param>
            <returns>Completion status of the transfer.</returns>
            <remarks>
            <list type="bullet">
            <item>
            This is a synchronous process- your process will wait until the transfer of
            all files have completed.
            </item>
            <item>
            Attempting to transfer more than one file using Xmodem may cause unexpected results 
            on the receiving side since no file separators sent.
            </item>
            </list>
            </remarks>
        </member>
        <member name="M:CommStudio.Transfers.Transfer.Download">
            <summary>
            Starts a new file download using the default filename.
            </summary>
            <returns>Completion status of the transfer.</returns>
            <remarks>
            <list type="bullet">
            <item>
            This method will place the active thread in a wait state until the file transfer
            has completed using the active protocol on the current port.
            </item>
            <item>
            If the current protocol is Xmodem, a new filename called Untitled.xxx is automatically
            generated, where xxx is 000, 001, 002, etc. For other protocols, the filename supplied
            by the other computer is used.
            </item>
            <item>
            Attempting to transfer more than one file using Xmodem may cause unexpected results 
            on the receiving side since no file separators sent.
            </item>
            </list>
            </remarks>
        </member>
        <member name="M:CommStudio.Transfers.Transfer.Download(System.String)">
            <summary>
            Starts a file download which uses the supplied name for the local file.
            </summary>
            <param name="localFilename">Name to assign to the next arriving file (will use
            directly for Xmodem or rename the file for other protocols).</param>
            <returns>Completion status of the transfer.</returns>
            <remarks>
            <list type="bullet">
            <item>
            This method will place the active thread in a wait state until the transfer has
            completed using the active protocol on the current port.
            </item>
            <item>
            If the current protocol is not Xmodem, the new filename will use that which is supplied.
            </item>
            <item>
            Other protocol types will be named according to the supplied argument (replacing the
            name sent via the transmission).
            </item>
            <item>
            For Xmodem, any existing file by this name will always be replaced.
            </item>
            <item>
            For protocols which support it, and when the file already exists, the system will attempt
            to restart where it last stopped.
            </item>
            </list>
            </remarks>
            <returns>Completion status of the transfer.</returns>
            <exception cref="T:System.IO.IOException">The process cannot access the file because it does
            not exist or is being used by another process.</exception>
            <exception cref="T:System.UnauthorizedAccessException">Access to the path or file is denied.</exception>
            <exception cref="T:System.IO.DirectoryNotFoundException">Could not find a part of the supplied path.</exception>
            <exception cref="T:System.IO.FileNotFoundException">Could not find the supplied file.</exception>
        </member>
        <member name="M:CommStudio.Transfers.Transfer.Download(System.String[])">
            <summary>
            Starts a transfer for a set of files, the resulting files use the default filename.
            </summary>
            <param name="localFilenames">Names to assign to the next set of arriving files
            (will use directly for Xmodem or rename the file for other protocols).</param>
            <returns>Completion status of the transfer.</returns>
            <remarks>
            This method will place the active thread in a wait state until the transfer has
            completed for all files using the active protocol on the current port.
            <list type="bullet">
            <item>
            If the current protocol is not Xmodem, the new filename will use that which is supplied.
            </item>
            <item>
            Other protocol types will be named according to the supplied argument (replacing the name
            sent via the transmission).
            </item>
            <item>
            For Xmodem, any existing file by this name will always be replaced.
            </item>
            <item>
            For protocols which support it and when the file already exists, the system will attempt
            to restart where it last stopped.
            </item>
            </list>
            </remarks>
            <returns>Completion status of the transfer.</returns>
        </member>
        <member name="M:CommStudio.Transfers.Transfer.Cancel">
            <summary>
            Signals the current file transfer to cancel processing as soon as possible.
            </summary>
            <returns>
            State of cancel before this member was called ('true' signals the transfer
            was already trying to abort when this function was called).
            </returns>
        </member>
        <member name="M:CommStudio.Transfers.Transfer.SetLocalFilename(System.String)">
            <summary>
            Internal only way to set LocalFilename without firing an exception
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.Transfer.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="P:CommStudio.Transfers.Transfer.AutoShowStatusDialog">
            <summary>
            Determines if the transfer status dialog should be shown when a new transfer is started.
            </summary>
        </member>
        <member name="P:CommStudio.Transfers.Transfer.Status">
            <summary>
            Returns the status of the current file transfer session.
            </summary>		
        </member>
        <member name="P:CommStudio.Transfers.Transfer.Path">
            <summary>
            Defines the default path to use for reading and writing files on the local machine.
            </summary>
            <value>Default path for files on local machine.</value>
        </member>
        <member name="P:CommStudio.Transfers.Transfer.Connection">
            <summary>
            The connection that will be used to perform the file transfer
            </summary>
        </member>
        <member name="P:CommStudio.Transfers.Transfer.Protocol">
            <summary>
            Type of protocol to be used by the next file transfer.
            </summary>
            <value>File protocol used on current or next transfer.</value>
        </member>
        <member name="P:CommStudio.Transfers.Transfer.SynchronizingObject">
            <summary>
            Gets or sets the object used to marshal the event handler calls issued from other threads
            </summary>
            <remarks>
            When SynchronizingObject is a null reference (Nothing in Visual Basic), event methods are
            called on a thread from the system thread pool. For more information on system thread pools,
            see ThreadPool.
            
            When the component is used in a Windows Forms environment, accessing the form or any of its
            controls through the system thread pool might not work, or may result in an exception. Avoid
            this by setting SynchronizingObject to a Windows Forms component, which causes the methods
            that handle the events to be called on the same thread on which the component was created.
            
            If the Connection component is used inside Visual Studio .NET in a Windows Forms designer,
            SynchronizingObject automatically sets to the control that contains the Connection. For
            example, if you place a Connection on a designer for Form1 (which inherits from Form) the
            SynchronizingObject property of Connection is set to the instance of Form1.
            </remarks>
        </member>
        <member name="P:CommStudio.Transfers.Transfer.LocalFilename">
            <summary>
            Gets or sets the full name and path of the file being transferred on the local computer.
            </summary>
            <remarks>
            Except during a BegingFileTransfer event, this property is read-only. It can be set during a 
            BeginFileTransfer event to override the local file. It is null if no transfer is in progress, 
            or of the Upload or Download variations that only use streams were used to initiate the transfer.
            </remarks>
        </member>
        <member name="P:CommStudio.Transfers.Transfer.RemoteFilename">
            <summary>
            Gets or sets the name of the current file as indicated to or from the remote system.
            </summary>
            <remarks>
            Except during a BeginFileTransfer event, this property is read-only.  During an upload, this property
            can be set inside a BeginFileTransfer event to override the filename that is sent to the remote system.
            It is null if no transfer is in progress, or if a protocol is used that doesn't transfer the filename,
            such as XModem.
            </remarks>
        </member>
        <member name="P:CommStudio.Transfers.Transfer.Position">
            <summary>
            Indicates the number of bytes that have been transferred
            </summary>
        </member>
        <member name="P:CommStudio.Transfers.Transfer.TimeElapsed">
            <summary>
            Indicates the time that has elapsed since the transfer of the current file was started.
            </summary>
        </member>
        <member name="P:CommStudio.Transfers.Transfer.Length">
            <summary>
            Returns the Length in bytes of the current file or stream being transferred, or -1 if unkown.
            </summary>
        </member>
        <member name="E:CommStudio.Transfers.Transfer.BeginSession">
            <summary>
            Event fired when a new file transfer session was started.
            </summary>
        </member>
        <member name="E:CommStudio.Transfers.Transfer.EndSession">
            <summary>
            Event fired when a file transfer session has terminated.
            </summary>
        </member>
        <member name="E:CommStudio.Transfers.Transfer.BeginFile">
            <summary>
            Event fired just before a new file is transferred
            </summary>
        </member>
        <member name="E:CommStudio.Transfers.Transfer.EndFile">
            <summary>
            Event fired when a file transfer session has terminated.
            </summary>
        </member>
        <member name="E:CommStudio.Transfers.Transfer.PositionChanged">
            <summary>
            Event fired when the status of the transfer has changed.
            </summary>
        </member>
        <member name="T:CommStudio.Transfers.Transfer.EndFileEventArgs">
            <summary>
            Fired when the transfer of a single file has been completed. The FileStatus property indicates success or failure.
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.Transfer.EndFileEventArgs.#ctor(CommStudio.Transfers.TransferFileStatus)">
            <summary>
            Creates a new instance o fthe EndFileEventArgs class
            </summary>
            <param name="fileStatus">Indicates whether the file was transferred successfully</param>
        </member>
        <member name="P:CommStudio.Transfers.Transfer.EndFileEventArgs.FileStatus">
            <summary>
            Indicates whether the file was transferred successfully
            </summary>
        </member>
        <member name="T:CommStudio.Transfers.Transfer.EndFileEventHandler">
            <summary>
            Event fired after each file during the transfer session
            </summary>
            <param name="sender">Class sending the event.</param>
            <param name="e">Object containing information to describe the event.</param>
            <remarks>
            The FileStatus property in the event arguments indicates if the file transfer was completed 
            successfully, failed, or skipped.
            </remarks>
        </member>
        <member name="T:CommStudio.Transfers.TransferFileInfo">
            <summary>
            Used to store information about a remote file.. essentially a subset of the regular FileInfo class
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.TransferFileInfo.#ctor(System.String,System.DateTime,System.Int64)">
            <summary>
            Creates a new instance of the TransferFileInfo class
            </summary>
            <param name="filename">The filename without a path. Can be null if it unkown.</param>
            <param name="lastWriteTime">The time the file was last modified or DateTime.MinValue if unknown</param>
            <param name="length">The size of the file, or -1 if the size is unkown</param>
        </member>
        <member name="P:CommStudio.Transfers.TransferFileInfo.Filename">
            <summary>
            Returns the name of the file, or null if it is unknown.
            </summary>
        </member>
        <member name="P:CommStudio.Transfers.TransferFileInfo.LastWriteTime">
            <summary>
            Returns the date the file was last modified or null if it is unknown.
            </summary>
        </member>
        <member name="P:CommStudio.Transfers.TransferFileInfo.Length">
            <summary>
            Returns the length of the file or null if it is unkown.
            </summary>
        </member>
        <member name="T:CommStudio.Transfers.TransferException">
            <summary>
            The exception that is thrown when a transfer error occurs.
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.TransferException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the TransferException class.
            </summary>
        </member>
        <member name="T:CommStudio.Transfers.TransferRemoteCanceledException">
            <summary>
            The exception that is thrown when the remote system cancels a transfer.
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.TransferRemoteCanceledException.#ctor">
            <summary>
            Initializes a new instance of the TransferRemoteCanceledException class.
            </summary>
        </member>
        <member name="T:CommStudio.Transfers.TransferLocalCanceledException">
            <summary>
            The exception that is thrown when the local user or application cancels a transfer.
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.TransferLocalCanceledException.#ctor">
            <summary>
            Initializes a new instance of the TransferLocalCanceledException class.
            </summary>
        </member>
        <member name="T:CommStudio.Transfers.TransferTimeOutException">
            <summary>
            The exception that is thrown when timeout occurs during transfers.
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.TransferTimeOutException.#ctor">
            <summary>
            Initializes a new instance of the TransferTimeOutException class.
            </summary>
        </member>
        <member name="T:CommStudio.Transfers.TransferTooManyFilesException">
            <summary>
            The exception that is thrown when more files are attempted to be transferred than expected.
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.TransferTooManyFilesException.#ctor">
            <summary>
            Initializes a new instance of the TransferTooManyFilesException class.
            </summary>
        </member>
        <member name="T:CommStudio.Transfers.TransferTooFewFilesException">
            <summary>
            The exception that is thrown when fewer than expected files are transferred.
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.TransferTooFewFilesException.#ctor">
            <summary>
            Initializes a new instance of the TransferTooFewFilesException class.
            </summary>
        </member>
        <member name="T:CommStudio.Transfers.TransferProtocolBase">
            <summary>
            The base class for all file transfer protocol implementations.
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolBase.#ctor(CommStudio.Transfers.Transfer)">
            <summary>
            Creates a new instance of the TransferProtolBase class
            </summary>
            <param name="Transfer">The Transfer component that will be used to perform the file transfer</param>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolBase.SaveConnectionState">
            <summary>
            Saves the properties that may be altered during the file transfer
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolBase.RestoreConnectionState">
            <summary>
            Restores the properties that may have been altered during the file transfer
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolBase.BeginSession">
            <summary>
            Starts a download session up to the point where the first file will be downloaded.
            </summary>
            <returns>True if the session was initiated successfully</returns>
            <remarks>If you override this method it's important to call the base class at the start of your new method.</remarks>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolBase.EndSession(System.Boolean)">
            <summary>
            Terminates a download session successfully after BeginDownloadSession was called.
            </summary>
            <returns>True if the transfer was completed successfully</returns>
            <remarks>If you override this method it's important to call the base class at the end of your new method.</remarks>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolBase.DownloadFile(System.IO.Stream,CommStudio.Transfers.TransferFileInfo)">
            <summary>
            Downloads the next file after PeekNextFile has been called
            </summary>
            <param name="outputStream">The stream that the file contents should be sent to. Should be seekable and writeable.</param>
            <param name="remoteFileInfo">A TransferFileInfo that describes the info that should be sent to the remote system</param>
            <returns></returns>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolBase.PeekNextFile">
            <summary>
            Returns the next filename being downloaded, or
                "" if the filename is unkown (for example, in XModem)
                null if there is no file to be downloaded
            </summary>
            <returns>A TransferFileInfo object with any information that is known filled out, or null if no 
            more files are available</returns>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolBase.UploadFile(System.IO.Stream,CommStudio.Transfers.TransferFileInfo)">
            <summary>
            Uploads a file to the remote computer
            </summary>
            <param name="inputStream">The stream that is used to read the file from.  Should be seekable and readable.</param>
            <param name="remoteFileInfo">A TransferFileInfo object with all the fields set which will be sent to the remote computer if the protocol supports it.</param>
            <returns></returns>
        </member>
        <member name="P:CommStudio.Transfers.TransferProtocolBase.Cancel">
            <summary>
            Indicates whether the transfer should be canceled ASAP or not.
            </summary>
        </member>
        <member name="T:CommStudio.Transfers.TransferProtocolXYModemBase">
            <summary>
            Summary description for TransferProtocolXYModemBase.
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolXYModemBase.NUL">
            <summary>
            NULL character (marks no more files in Y-Modem)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolXYModemBase.SOH">
            <summary>
            Start Of Header (First byte in Xmodem packet)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolXYModemBase.EOT">
            <summary>
             End Of Transmission (End of Xmodem file)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolXYModemBase.ACK">
            <summary>
            Acknowledge (positive - Last packet was OK)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolXYModemBase.BACK">
            <summary>
            Backspace (go left one column and erase the space)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolXYModemBase.DLE">
            <summary>
            Data Link Escape
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolXYModemBase.NAK">
            <summary>
            Negative Acknowledge (Receiver sends to start OR bad packet received)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolXYModemBase.SUB">
            <summary>
            Padding for last incomplete X-Modem Sector
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolXYModemBase.STX">
            <summary>
            Start of Xmodem-K
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolXYModemBase.CAN">
            <summary>
            Cancel (transfer)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolXYModemBase.XON">
            <summary>
            (DC1) Transmit On
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolXYModemBase.XOF">
            <summary>
            (DC3) Transmit Off
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolXYModemBase.SYN">
            <summary>
            Synchronous idle
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolXYModemBase.protocol">
            <summary>
            The current protocol (should be an XModem or YModem variation)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolXYModemBase.hasFileInfoSector">
            <summary>
            Indicates if the first sector contains file information - true for YModem
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolXYModemBase.preferLargeSectors">
            <summary>
            Indicates whether sectors are 128 or 1024 bytes - true for YModem and XModem1k
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolXYModemBase.preferCrc">
            <summary>
            Indicates whether CRC should be used instead of checksum - true for all variations except XModem Checksum
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolXYModemBase.receivedEmptyFilename">
            <summary>
            For YModem downloads only, indicates if an emtpy filename sector was received
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolXYModemBase.WaitForInput(System.Int32)">
            <summary>
            Waits up to 10 seconds
            </summary>
            <param name="count">Number of bytes that should be in the input buffer</param>
            <returns></returns>
        </member>
        <member name="T:CommStudio.Transfers.TransferProtocolXYModemDownload">
            <summary>
            Summary description for TransferProtocolXYModemDownload.
            </summary>
        </member>
        <member name="T:CommStudio.Transfers.TransferProtocolXYModemUpload">
            <summary>
            Summary description for TransferProtocolXYModemUpload.
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolXYModemUpload.BeginSession">
            <summary>
            Initiates an XModem or YModem upload
            </summary>
            <returns>True if successfull</returns>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolXYModemUpload.EndSession(System.Boolean)">
            <summary>
            Completes a successful XModem or YModem upload
            </summary>
            <returns>True if successfull</returns>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolXYModemUpload.UploadFile(System.IO.Stream,CommStudio.Transfers.TransferFileInfo)">
            <summary>
            Uploads a file using XModem or YModem
            </summary>
            <param name="inputStream">The stream that contains the data to be sent</param>
            <param name="remoteFileInfo">A TransferFileInfo object that describes the file information that should be sent</param>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolXYModemUpload.SendSectorWithoutConfirmation(System.Byte,System.Byte[])">
            <summary>
            Sends an XModem or YModem formatted sector without waiting for confirmation from the remote computer
            </summary>
            <param name="sectorIndex">The index of the sector (0-255)</param>
            <param name="data">An array of either 128 or 1024 bytes</param>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolXYModemUpload.DoubleToOctalString(System.Double)">
            <summary>
            Converts a whole number double to octal
            </summary>
            <param name="n">A whole number (can be negative)</param>
            <returns>The number represented in octal</returns>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolXYModemUpload.WaitForNAKorC">
            <summary>
            Wait up to ten times for one second for a NAK or a C
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolXYModemUpload.SendEOT">
            <summary>
            Sends an EOT and wait for confirmation
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolXYModemUpload.SendSector(System.Byte,System.Byte[])">
            <summary>
            Sends a sector and waits for an acknowledgement
            </summary>
            <param name="sectorIndex">The index of the sector (0-255)</param>
            <param name="data">An array of either 128 or 1024 bytes</param>
            <returns></returns>
        </member>
        <member name="T:CommStudio.Transfers.TransferProtocolZModemBase">
            <summary>
            Summary description for TransferProtocolZModemBase.
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.maximumGarbageCount">
            <summary>
            The largest number of garbage characters allowed
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ESCAPEDLF">
            <summary>
            An escaped version of line feed (10)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZPAD">
            <summary>
            ZModem: Padding character begins every frame header
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZDLE">
            <summary>
            ZModem: General purpose escape code
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZDLEE">
            <summary>
            ZModem: Escaped ZDLE as transmitted
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZBIN">
            <summary>
            ZModem: Start Binary CRC16 frame
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZHEX">
            <summary>
            ZModem: Start HEX CRC16 frame
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZBIN32">
            <summary>
            ZModem: Start Binary CRC32 frame
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.LF">
            <summary>
            Line feed
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.CR">
            <summary>
            Carriage return
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.BACK">
            <summary>
            Backspace (go left one column and erase the space)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.XON">
            <summary>
            (DC1) Transmit On
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.XOFF">
            <summary>
            (DC3) Transmit Off
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ERROR">
            <summary>
            ZModem unexpected hex char or incorrect encoding
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.CAN">
            <summary>
            Cancel (transfer)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZCRCE">
            <summary>
            ZModem: CRC next, frame ends, header packet follows
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZCRCG">
            <summary>
            ZModem: CRC next, frame continues nonstop
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZCRCQ">
            <summary>
            ZModem: CRC next, frame continues, ZACK expected
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZCRCW">
            <summary>
            ZModem: CRC next, ZACK expected, end of frame
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZRUB0">
            <summary>
            ZModem: Translate to rubout (0177 == 0x7f)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZRUB1">
            <summary>
            ZModem: Translate to rubout (0xFF == 0xff)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.GOTOR">
            <summary>
            ZModem: hi byte offset
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.GOTCRCE">
            <summary>
            ZModem: ZDLE-ZCRCE received
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.GOTCRCG">
            <summary>
            ZModem: ZDLE-ZCRCG received
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.GOTCRCQ">
            <summary>
            ZModem: ZDLE-ZCRCQ received
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.GOTCRCW">
            <summary>
            ZModem: ZDLE-ZCRCW received
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.GOTCAN">
            <summary>
            ZModem: CAN*5 seen
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZCBIN">
            <summary>
            ZModem: Binary transfer - inhibit conversion
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZCNL">
            <summary>
            ZModem: Convert NL to local end of line convention
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZCRESUM">
            <summary>
            ZModem: Resume interrupted file transfer
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZMNEW">
            <summary>
            ZModem: Transfer if source newer or longer
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZMCRC">
            <summary>
            ZModem: Transfer if different file CRC or length
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZMAPND">
            <summary>
            ZModem: Append contents to existing file (if any)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZMCLOB">
            <summary>
            ZModem: Replace existing file
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZMSPARS">
            <summary>
            ZModem: Encoding for sparse file
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZMDIFF">
            <summary>
            ZModem: Transfer if dates or lengths different
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZMPROT">
            <summary>
            ZModem: Protect destination file
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZTLZW">
            <summary>
            ZModem: Lempel-Ziv compression
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZTCRYPT">
            <summary>
            ZModem: Encryption
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZTRLE">
            <summary>
            ZModem: Run Length encoding
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.TransferProtocolZModemBase.ZCACK1">
            <summary>
            ZModem: Acknowledge, then do command
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolZModemBase.ReadHex">
            <summary>
            Reads two hex characters and returns a byte, or ERROR if no valid hex characters were read
            </summary>
            <returns></returns>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolZModemBase.ReadBinaryHeader16">
            <summary>
            Called after ReadHeader has read a ZPAD, ZDLE, and ZBIN
            </summary>
            <returns></returns>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolZModemBase.ReadBinaryHeader32">
            <summary>
            Called after ReadHeader has read a ZPAD, ZDLE, and ZBIN32
            </summary>
            <returns></returns>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolZModemBase.ReadHexHeader">
            <summary>
            Called after ReadHeader has read a ZPAD, ZDLE, and ZHEX
            </summary>
            <returns></returns>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZRQINIT">
            <summary>
            ZModem: Request receive init frame
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZRINIT">
            <summary>
            ZModem: Receiver init frame
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZSINIT">
            <summary>
            ZModem: Sender init frame (optional)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZACK">
            <summary>
            ZModem: General purpose acknowledge frame (ACK to above)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZFILE">
            <summary>
            ZModem: File name and data from sender
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZSKIP">
            <summary>
            ZModem: Tell sender: skip this file
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZNAK">
            <summary>
            ZModem: General purpose Not Acknowledge (Last packet was garbled)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZABORT">
            <summary>
            ZModem: Abort batch transfers (entire session)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZFIN">
            <summary>
            ZModem: Finish session
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZRPOS">
            <summary>
            ZModem: Resume data transmission at this position
            (request to start sending from a specific address)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZDATA">
            <summary>
            ZModem: Start of a data frame (Data packet(s) follow)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZEOF">
            <summary>
            ZModem: End of file
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZFERR">
            <summary>
            ZModem: Fatal Read or Write error Detected
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZCRC">
            <summary>
            ZModem: Request for file CRC and response
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZCHALLENGE">
            <summary>
            ZModem: Receiver's Challenge for the frame
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZCOMPL">
            <summary>
            ZModem: Request is complete
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZCAN">
            <summary>
            ZModem: Remote end cancelled (Other end canned session with CAN*5)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZFREECNT">
            <summary>
            ZModem: Request for free bytes on target file system
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZCOMMAND">
            <summary>
            ZModem: Command from sending program
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZSTDERR">
            <summary>
            ZModem: Output to standard error, data follows
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ESCAPEDLF">
            <summary>
            An escaped version of line feed (10)
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZPAD">
            <summary>
            ZModem: Padding character begins every frame header
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZDLEE">
            <summary>
            ZModem: Escaped ZDLE as transmitted
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZBIN">
            <summary>
            ZModem: Start Binary CRC16 frame
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZHEX">
            <summary>
            ZModem: Start HEX CRC16 frame
            </summary>
        </member>
        <member name="F:CommStudio.Transfers.ZModemHeaderType.ZBIN32">
            <summary>
            ZModem: Start Binary CRC32 frame
            </summary>
        </member>
        <member name="T:CommStudio.Transfers.TransferProtocolZModemDownload">
            <summary>
            Summary description for TransferProtocolZModemDownload.
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolZModemDownload.GetInSync">
            <summary>
            Gets in sync with the remote computer
            </summary>
            <returns>Either ZNAK (error), ZFILE, ZCOMPL, ZCAN or throws a TransferTimeOutException</returns>
        </member>
        <member name="T:CommStudio.Transfers.TransferProtocolZModemUpload">
            <summary>
            Summary description for TransferProtocolZModemUpload.
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.TransferProtocolZModemUpload.GetInSync(System.Boolean)">
            <summary>
            Gets in sync with the remote computer
            </summary>
            <param name="ignorePosition">Indicates if the Position parameter in an ACK sector can be ignored</param>
            <returns>Either ZRPOS, ZACK, ZSKIP, ZRINIT or throws a TransferTimeOutException</returns>
        </member>
        <member name="T:CommStudio.Transfers.TransferStatusWindow">
            <summary>
            Summary description for TransferStatusWindow.
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.TransferStatusWindow.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
        </member>
        <member name="M:CommStudio.Transfers.TransferStatusWindow.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
        <member name="M:CommStudio.Win32__1.#ctor">
            <summary>
            All contents are static or PInvoke, this prevents automatic constructor.
            </summary>
        </member>
        <member name="M:CommStudio.Win32.#ctor">
            <summary>
            All contents are static or PInvoke, this prevents automatic constructor.
            </summary>
        </member>
    </members>
</doc>
