using System;
using System.Runtime.InteropServices;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;
using System.Data;
using System.Threading;

using DCSSDK.DCSFinisher;
using DCSSDK.DCSFinisherProperties;
using DCSSDK;

namespace DCSSDK.CrossMatch
{
	/// <summary>
	/// Summary description for CrossMatchMain.
	/// </summary>
	public class CrossMatchMain : System.Windows.Forms.Form
	{
		/// <summary>
		/// Required designer variable.
		/// </summary>

		//bool m_bLiveMode;
		DCSSDK.ParameterStore m_ps = null;
		//private bool m_bEnableGetFile = true;
		private string m_FingerFileName;
		private string m_FingerTempName;
		private int m_CaptureStatus = 0;
		private System.ComponentModel.IContainer components = null;

		public CrossMatchMain()
		{
			//if (!DCSSDK.DCSLicensing.IsLicensedOK(DCSSDK.LicensedFeatures.CrossMatchCapture)) return;
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			
			m_CaptureStatus = 0;	//0= software did not finish; 1=user canceled; 2=OK
			m_ps = new DCSSDK.ParameterStore("CrossMatch Camera");
			m_FingerFileName = m_ps.m_strDCSInstallDirectory + "\\_default_img.bmp";		// Application.StartupPath
			m_FingerTempName = m_ps.m_strDCSInstallDirectory + "\\_temp_img.bmp";
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if (components != null) 
				{
					components.Dispose();
				}
			}
			CrossMatchUSB.USB_Uninitialize();
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			// 
			// CrossMatchMain
			// 
			this.AutoScaleBaseSize = new System.Drawing.Size(5, 13);
			this.ClientSize = new System.Drawing.Size(632, 446);
			this.MaximizeBox = false;
			this.MinimizeBox = false;
			this.Name = "CrossMatchMain";
			this.SizeGripStyle = System.Windows.Forms.SizeGripStyle.Hide;
			this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
			this.Text = "CrossMatch Capture Dialog";

		}
		#endregion

/********************************************************************************************
		/// <summary>
		/// The main entry point for the application.
		/// </summary>
		[STAThread]
		static void Main() 
		{
			Application.Run(new CrossMatchMain());
		}
*********************************************************************************************/

		#region Private Implementation Utilities
		// return  true if success - false If canceled

		private void GetCrossMatchProperties()
		{
			// properties in controls
			//MessageBox.Show("GetCrossMatchProperties");
		}

		private void WriteCrossMatchProperties()
		{
			// properties in controls
			//MessageBox.Show("WriteCrossMatchProperties");
		}

		// Call finishing in Fingerprint mode
		// read and process m_FingerTempName.  Write out to m_FingerFileName
		private bool CallFingerFinishing()
		{
			System.Drawing.Image bitmapFinger;
			string strError = String.Empty;
			bitmapFinger = DCSSDK.DCSServerStuff.OpenImageFile(m_FingerTempName, out strError);
			// file remains locked until bitmapFinger is disposed.
			if (bitmapFinger == null)
			{
				DCSMsg.Show(strError);
				// This probably results from a corrupted image file.
				return false;
			}

			bool bCenterFinger = true;
			Point pointFingerCenter = Point.Empty;
			
			//syh
			int minx=0;
			int maxx=0;
			int miny=0;
			int maxy=0;
			if (bCenterFinger)
			{
				//===============================//
				// find finger center of gravity //
				//===============================//
				int w,h;
				int x,y;
				w = bitmapFinger.Width; h = bitmapFinger.Height;
			
				// find x center of mass: xc = sigma(x * v(x)) / sigma( v(x))
				long sigx = 0;
				long sigy = 0;
				long sigv = 0;
				int v;
				Color color;

				for (y=0; y<h; y++)
				{
					for (x=0; x<w; x++)
					{
						color = ((Bitmap)bitmapFinger).GetPixel(x,y);
						v = 255-color.G;
						sigx += x*v;
						sigy += y*v;
						sigv += v;
					}
				}
				if (sigv <= 0)
					bCenterFinger = false;
				else
				{
					pointFingerCenter = new Point((int)(sigx / sigv), (int)(sigy / sigv));
					if (pointFingerCenter.X < 0 || pointFingerCenter.Y < 0)
						bCenterFinger = false;
				}

				//===========================//
				// Find extents of the print //
				//===========================//
				minx = w;
				maxx = 0;
				miny = h;
				maxy = 0;
				for (y=0; y<h; y++)
				{
					sigx = 0;
					for (x=0; x<w; x++)
					{
						color = ((Bitmap)bitmapFinger).GetPixel(x,y);
						v = 255-color.G;
						sigx += v;
					}
					if (sigx > 2048)
					{
						if (y<miny) miny = y;
						else if (y>maxy) maxy = y;
					}
				}
				for (x=0; x<w; x++)
				{
					sigy = 0;
					for (y=0; y<h; y++)
					{
						color = ((Bitmap)bitmapFinger).GetPixel(x,y);
						v = 255-color.G;
						sigy += v;
					}
					if (sigy > 2048)
					{
						if (x<minx) minx = x;
						else if (x>maxx) maxx = x;
					}
				}
			}

			try
			{
				FinisherMain dlgFinish = new FinisherMain("Fingerprint");
				dlgFinish.Image = bitmapFinger;

				// do not allow a size larger than the device supports
				Size sizeCrop = new Size(Math.Min(CrossMatchUSB.MAX_WIDTH, dlgFinish.CropSize.Width), Math.Min(CrossMatchUSB.MAX_HEIGHT, dlgFinish.CropSize.Height));

				if (bCenterFinger)
				{
					// Apply center of gravity of the print //
					Rectangle rect = new Rectangle(pointFingerCenter - DCSMath.Half(sizeCrop), sizeCrop);

					// Apply extents of the print //
					if (rect.Width > maxx-minx)	rect.X = (maxx+minx - sizeCrop.Width)/2;
					if (rect.Height > maxy-miny)rect.Y = (maxy+miny - sizeCrop.Height)/2;

					DCSMath.ApplyBounds(ref rect, bitmapFinger.Size);
					dlgFinish.CropLocation = rect.Location;
				}
				dlgFinish.ShowDialog();

				// the original image from camera needs to be disposed and the file deleted
				bitmapFinger.Dispose();
				bitmapFinger = null;

				if (dlgFinish.IsCanceled)
				{
					return false;
				}
				else
				{
					// if ok the finisher's working image will become the current image
					bitmapFinger = (System.Drawing.Image)dlgFinish.Image;
					
					bool bRet = dlgFinish.SaveFinishedImage(m_FingerFileName);

					bitmapFinger.Dispose();
					bitmapFinger = null;
					return bRet;
				}
			}
			catch(System.Exception ex)
			{
				DCSMsg.Show("ERROR in Finger Finishing.", ex);
				return false;
			}
		}

		private void DoCrossMatch()
		{
			string strCmd = System.IO.Path.Combine(m_ps.m_strDCSInstallDirectory, "DCSCapture_CrossMatchExe.Exe");
			string[] strArgs = {m_FingerTempName};
			int iRet;

			while (true)
			{
				try
				{
					//Exec crossmatch in its own exe to better handle device init and uninit 
					System.AppDomain d = System.AppDomain.CreateDomain("CMT");
					iRet = d.ExecuteAssembly(strCmd, null, strArgs);
					System.AppDomain.Unload(d);
				}
				catch (Exception ex)
				{
					DCSMsg.Show("ERROR: ExecuteAssembly(" + strCmd + ")", ex);
					break;
				}
				if (!System.IO.File.Exists(m_FingerTempName))
				{
					m_CaptureStatus = 1;
					break;
				}

				bool bOK = CallFingerFinishing();
				if (bOK)
				{
					m_CaptureStatus = 2;
					break;
				}
				else continue;
			}
		}
		#endregion

		#region Public Interface
		// also public ShowDialog()

		public string FingerFileName
		{
			set { m_FingerFileName = value; }
		}
		public int CaptureStatus
		{
			get { return m_CaptureStatus; }
		}
		#endregion

		#region Message Handlers
		protected override void OnVisibleChanged(EventArgs e)
		{
			if (this.Visible)
			{
				m_CaptureStatus = 0;
				this.DoCrossMatch();
				this.Close();
			}
			else
			{
			}
			base.OnVisibleChanged (e);
		}

		#endregion
	}

	public class CrossMatchUSB
	{
		[DllImport("Usb4xx.dll")]
		public static extern CrossMatchUSB.USB_STATUS USB_AutoDraw(IntPtr hWnd);	

		[DllImport("Usb4xx.dll")]
		public static extern CrossMatchUSB.USB_STATUS USB_DisplayErrorMessage(CrossMatchUSB.USB_STATUS status);	

		[DllImport("Usb4xx.dll")]
		public static extern unsafe CrossMatchUSB.USB_STATUS USB_GetLiveMode(bool* pbLiveMode);

		[DllImport("Usb4xx.dll")]
		public static extern unsafe CrossMatchUSB.USB_STATUS USB_GetImageSize(out short pnWidth, out short pnHeight);

		[DllImport("Usb4xx.dll")]
		public static extern CrossMatchUSB.USB_STATUS USB_Initialize(short nWidth, short nHeight, IntPtr hWnd);	

		[DllImport("Usb4xx.dll")]
		public static extern CrossMatchUSB.USB_STATUS USB_Reset();

		[DllImport("Usb4xx.dll")]
		public static extern unsafe CrossMatchUSB.USB_STATUS USB_SaveImage(string strFilename);

		[DllImport("Usb4xx.dll")]
		public static extern unsafe CrossMatchUSB.USB_STATUS USB_SetImageSize(short nWidth, short nHeight);

		[DllImport("Usb4xx.dll")]
		public static extern CrossMatchUSB.USB_STATUS USB_SetLiveMode(bool bLiveMode);

		[DllImport("Usb4xx.dll")]
		public static extern CrossMatchUSB.USB_STATUS USB_Uninitialize();	

		[DllImport("Usb4xx.dll")]
		public static extern CrossMatchUSB.USB_STATUS USB_Enable_V300LC_First_Init_Detect();

		[DllImport("Usb4xx.dll")]
		public static extern CrossMatchUSB.USB_STATUS USB_GetLiveImage(out IntPtr /*USB_IMAGE* */ pImage);

		[DllImport("Usb4xx.dll")]
		public static extern CrossMatchUSB.USB_STATUS USB_RegisterCallbacks(IntPtr a, IntPtr b);

		public enum USB_STATUS
		{
			USB_OK = 0,						  // The function successfully completed its operation(s)
			USB_FAIL = 1,					  // The function did not successfully complete its operation(s)
			USB_NOT_READY = 2,				  // The USB device is not responding
			USB_OUT_OF_RANGE = 3,			  // A parameter passed to the function is out of range
			USB_ROT_OUT_OF_RANGE = 4,		  // the rotation is out of range
			USB_LEN_OUT_OF_RANGE = 5,		  // length is out of range
			USB_INVALID_DATA = 6,			  // no data pointer provided
			USB_IO_FAILED = 7,				  // The IOCtl failed
			USB_SETTINGS_ERR = 8,			  // A scanner setting failed to get set
			USB_MV5_NO_CAPTURED_IMAGE = 9,	  // there is not a captured image in MV5 memory
			USB_MV5_CAPTURED_IMAGE = 10,	  // there is a captured image in MV5 memory
			USB_MV5_FORCED_CAPTURED = 11,     // there is a forced captured image in MV5 memory
			USB_LIVE_MODE = 12,				  // Can not perform function in Live Mode
			USB_INVALID_SERIAL_NUM = 13,      // Invalid serial number
			USB_OLD_EE_VERSION = 14,          // the eeprom is not the latest version
			USB_THREAD_FAILED = 15,           // failed to create usb connect/disconnect thread
			USB_MV5_ONLY = 16,                // function only valid for MV5 scanners
			USB_V500_ONLY = 17,               // function only valid for V500 scanners
			USB_MV5_INVALID_IMAGE_NUM  = 18,  // invalid image capacity number passed to MV5_2X
			USB_MV5_2X_ONLY            = 19,  // function only valid for MV5_2X, MV5_2XA, MV5_2XBC scanners
			USB_MV5_CAPTURED_BARCODE   = 20,  // There is a barcode captured in the MV5 2XBC
			USB_MV5_BC_ONLY            = 21,  // function only valid for MV5_2XBC scanner
			USB_ALLOCATE_MEMORY_FAILED = 22,  // Unable to allocate required memory
			USB_INCORRECT_IMAGE_WIDTH  = 23,  // Width must be divisible by 4 in still mode, by 8 in live mode
			USB_NO_INVERSE_IMAGE       = 24,  // Image inverse not provided by this scanner
			USB_AUTOCAPTURE_ONLY       = 25,  // function only valid for autocapture enabled scanner
			USB_AC_BLACK_LARGER_WHITE  = 26,  // Autocapture black threshold > white threshold
			USB_AC_WHITE_LARGER_255    = 27,  // Autocapture white threshold > 255
			USB_AC_BLACK_SMALLER_0     = 28,  // Autocapture black threshold < 0
			USB_V300LC_INIT_FAILED     = 29,  // V300 LC failed to properly initialize
			USB_INIT_FAIL_FINGER_PRESENT = 30,  // Failure during system call attempting to detect USB devices
			USB_DEVICE_PARSE_FAILED    = 31,  // Failure during system call attempting to detect USB devices
			USB_USB2_HUB_REQUIRED      = 32,  // USB 2.0 hub required but not detected
			USB_MINIMUM_OS_NOT_MET     = 33,  // Minimum OS Requirement Not Met
			USB_USB2_SCANNER_ONLY      = 34,  // Function call only valid for USB 2.0 scanners
			USB_FUNCTION_UNSUPPORTED   = 35   // Function call not supported for connected scanner
		};
		public const int MAX_WIDTH = 640;
		public const int MAX_HEIGHT = 512;
	}
}
