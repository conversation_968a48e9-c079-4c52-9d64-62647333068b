#pragma once

struct readerConfig
{
  long baudRate;  
  char protocol;  
  unsigned char stationID;
};

struct presetSettings 
{
  long baudRate;  
  char protocol;  
};

// Opens and initializes a specified communication device.
void* __cdecl RDR_OpenComm(char* commDevice, char autodetect, struct presetSettings* settings);

// Closes and releases the communication device.
void  __cdecl RDR_CloseComm(void* hComm);

// Empties the receive buffer of the communication.
void  __cdecl RDR_EmptyCommRcvBuffer(void* hComm);

// Aborts the continuous read command.
void  __cdecl RDR_AbortContinuousRead(void* hComm);

// Sets and applies the transfer protocol of the communication device.
void  __cdecl RDR_SetCommProtocol(void* hComm, char protocol);

// Returns the active transfer protocol of the communication device.
char  __cdecl RDR_GetCommProtocol(void* hComm);

// Sets and applies the baud rate of the communication device.
void  __cdecl RDR_SetCommBaudRate(void* hComm, long bdRate);

// Gets the current baud rate of the communication device.
long  __cdecl RDR_GetCommBaudRate(void* hComm);

// Sets and applies the continuous receive mode of the commnication device.
void  __cdecl RDR_SetCommContRcv(void* hComm, char contReceiveMode);

// Returns the state of continuous receive mode.
char  __cdecl RDR_GetCommContRcv(void* hComm);

// Sets and applies the timeout of the communication device.
void  __cdecl RDR_SetCommTimeout(void* hComm, long timeout);

// Returns the timeout of the communication device.
long  __cdecl RDR_GetCommTimeout(void* hComm);

// Detects all available readers and devices on a specified communication device.
char* __cdecl RDR_DetectReader(void* hComm, char* buffer);

// Opens a reader on specified communication device with specified ID.
void* __cdecl RDR_OpenReader(void* hComm, unsigned char id, short knownReader);

// Closes a reader specified with its handle.
void  __cdecl RDR_CloseReader(void* hReader);

// Resets a reader specified with its handle.
void  __cdecl RDR_ResetReader(void* hReader);

// Sends a command to a reader specified with its handle.
long  __cdecl RDR_SendCommand(void* hReader, char* command, char* data);

// Receives data from a reader specified with its handle.
char* __cdecl RDR_GetData(void* hReader, char* buffer);

// Sends a command to a reader specified with its handle and receives data.
char* __cdecl RDR_SendCommandGetData(void* hReader, char* command, char* data, char* buffer);

// Receives data from a reader specified with its handle with timeout.
char* __cdecl RDR_GetDataTimeout(void* hReader, char* buffer, long timeout);

// Sends a command to a reader specified with its handle and receives data with timeout.
char* __cdecl RDR_SendCommandGetDataTimeout(void* hReader, char* command, char* data, char* buffer, long timeout);

// Sets and applies the reader with specified configuration. The communication device even adapted to this new settings.
void  __cdecl RDR_SetReaderConfig(void* hReader, struct readerConfig* config);

// Returns current reader configuration.
struct readerConfig __cdecl RDR_GetReaderConfig(void* hReader);

// Returns the version string of a reader specified with its handle.
char* __cdecl RDR_GetReaderType(void* hReader, char* buffer);

// Returns the station ID of a reader specified with its handle.
unsigned char __cdecl RDR_GetStationID(void* hReader);

// Check if a script command is available for a reader specified with its handle.
char  __cdecl RDR_IsCommandAvailable(void* hReader, char* command);

// Returns the device ID of a reader specified with its handle.
char* __cdecl RDR_GetDeviceID(void* hReader, char* buffer);

// Activates/deactivates broadcast mode of binary protocol using a reader specified with its handle.
void  __cdecl RDR_SetBroadcast(void* hReader, char broadcast);

// Returns the state of broadcast mode of a reader specified with its handle.
char  __cdecl RDR_GetBroadcast(void* hReader);

// Returns the version of the DLL. (This function is obsolete, use GetDLLVersionStr)
long  __cdecl RDR_GetDLLVersion(void);

// Returns the version of the DLL as string.
char* __cdecl RDR_GetDLLVersionStr(char* buffer);

// Activates/deactivates debug output for a reader specified with its handle.
void  __cdecl RDR_SetDebugOutputState(void* hReader, char state);

// Returns the state of debug output for a reader specified with its handle.
char  __cdecl RDR_GetDebugOutputState(void* hReader);

// Returns the debug output for a reader specified with its handle.
char* __cdecl RDR_GetDebugOutput(void* hReader, char* buffer);

// Starts the timer for a reader specified with its handle.
void  __cdecl RDR_StartTimer(void* hReader);

// Returns the response time for a reader specified with its handle.
float __cdecl RDR_GetTiming(void* hReader);

// Returns the flag of the binary protocol v2 for a reader specified with its handle.
char  __cdecl RDR_GetBinFlag(void* hReader);

// Returns the used binary protocol type for a reader specified with its handle.
char  __cdecl RDR_GetBinProtocol(void* hReader);

// Prepares the firmware upload and checks the password.
long  __cdecl RDR_LoadFirmware(void* hReader, char* data, long length, char* password, char option);

// Initialize the firmware update.
void  __cdecl RDR_InitUpdateFirmware();

// Update one line of the firmware. Call this function until the upload is finished.
char  __cdecl RDR_UpdateFirmware(void* hReader);

// Encrypts data using DES algorithm.
char* __cdecl RDR_DESEncrypt(char options, char* key, char* data, long length, char* output);

// Decrypts data using DES algorithm.
char* __cdecl RDR_DESDecrypt(char options, char* key, char* data, long length, char* output);

// DESFire command set.
char* __cdecl RDR_DESFire(void* hReader, char command, char* data, char* output);
