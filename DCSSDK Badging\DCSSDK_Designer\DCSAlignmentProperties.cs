using System;
using System.Collections;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Windows.Forms;

namespace DCSDEV.DCSDesigner
{
	/// <summary>
	/// Summary description for DCSAlignmentProperties.
	/// </summary>
    internal class DCSAlignmentProperties : System.Windows.Forms.UserControl
	{
		private Color m_color;
		private Color m_colorPick;
		private System.Windows.Forms.Button m_buttonCurrent = null;

		private System.Windows.Forms.Button button1;
		private System.Windows.Forms.Button button2;
		private System.Windows.Forms.Button button3;
		private System.Windows.Forms.Button button4;
		private System.Windows.Forms.Button button5;
		private System.Windows.Forms.Button button6;
		private System.Windows.Forms.Button button7;
		private System.Windows.Forms.Button button8;
		private System.Windows.Forms.Button button9;
		private System.Windows.Forms.Label label5;
		/// <summary> 
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		public DCSAlignmentProperties()
		{
			// This call is required by the Windows.Forms Form Designer.
			InitializeComponent();

			// TODO: Add any initialization after the InitializeComponent call
			m_buttonCurrent = button1;
			m_color = this.button1.BackColor;
			m_colorPick = Color.Gray;
		}

		/// <summary> 
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Component Designer generated code
		/// <summary> 
		/// Required method for Designer support - do not modify 
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			this.button1 = new System.Windows.Forms.Button();
			this.button2 = new System.Windows.Forms.Button();
			this.button3 = new System.Windows.Forms.Button();
			this.button4 = new System.Windows.Forms.Button();
			this.button5 = new System.Windows.Forms.Button();
			this.button6 = new System.Windows.Forms.Button();
			this.button7 = new System.Windows.Forms.Button();
			this.button8 = new System.Windows.Forms.Button();
			this.button9 = new System.Windows.Forms.Button();
			this.label5 = new System.Windows.Forms.Label();
			this.SuspendLayout();
			// 
			// button1
			// 
			this.button1.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.button1.Location = new System.Drawing.Point(7, 18);
			this.button1.Name = "button1";
			this.button1.Size = new System.Drawing.Size(24, 24);
			this.button1.TabIndex = 0;
			this.button1.Click += new System.EventHandler(this.button_Click);
			// 
			// button2
			// 
			this.button2.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.button2.Location = new System.Drawing.Point(31, 18);
			this.button2.Name = "button2";
			this.button2.Size = new System.Drawing.Size(48, 24);
			this.button2.TabIndex = 1;
			this.button2.Click += new System.EventHandler(this.button_Click);
			// 
			// button3
			// 
			this.button3.BackColor = System.Drawing.SystemColors.Control;
			this.button3.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.button3.Location = new System.Drawing.Point(79, 18);
			this.button3.Name = "button3";
			this.button3.Size = new System.Drawing.Size(24, 24);
			this.button3.TabIndex = 2;
			this.button3.UseVisualStyleBackColor = false;
			this.button3.Click += new System.EventHandler(this.button_Click);
			// 
			// button4
			// 
			this.button4.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.button4.Location = new System.Drawing.Point(7, 42);
			this.button4.Name = "button4";
			this.button4.Size = new System.Drawing.Size(24, 24);
			this.button4.TabIndex = 3;
			this.button4.Click += new System.EventHandler(this.button_Click);
			// 
			// button5
			// 
			this.button5.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.button5.Location = new System.Drawing.Point(31, 42);
			this.button5.Name = "button5";
			this.button5.Size = new System.Drawing.Size(48, 24);
			this.button5.TabIndex = 4;
			this.button5.Click += new System.EventHandler(this.button_Click);
			// 
			// button6
			// 
			this.button6.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.button6.Location = new System.Drawing.Point(79, 42);
			this.button6.Name = "button6";
			this.button6.Size = new System.Drawing.Size(24, 24);
			this.button6.TabIndex = 5;
			this.button6.Click += new System.EventHandler(this.button_Click);
			// 
			// button7
			// 
			this.button7.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.button7.Location = new System.Drawing.Point(7, 66);
			this.button7.Name = "button7";
			this.button7.Size = new System.Drawing.Size(24, 24);
			this.button7.TabIndex = 6;
			this.button7.Click += new System.EventHandler(this.button_Click);
			// 
			// button8
			// 
			this.button8.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.button8.Location = new System.Drawing.Point(31, 66);
			this.button8.Name = "button8";
			this.button8.Size = new System.Drawing.Size(48, 24);
			this.button8.TabIndex = 7;
			this.button8.Click += new System.EventHandler(this.button_Click);
			// 
			// button9
			// 
			this.button9.Font = new System.Drawing.Font("Microsoft Sans Serif", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.button9.Location = new System.Drawing.Point(79, 66);
			this.button9.Name = "button9";
			this.button9.Size = new System.Drawing.Size(24, 24);
			this.button9.TabIndex = 8;
			this.button9.Click += new System.EventHandler(this.button_Click);
			// 
			// label5
			// 
			this.label5.Location = new System.Drawing.Point(0, 0);
			this.label5.Name = "label5";
			this.label5.Size = new System.Drawing.Size(112, 15);
			this.label5.TabIndex = 69;
			this.label5.Text = "Alignment positioning:";
			this.label5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
			// 
			// DCSAlignmentProperties
			// 
			this.Controls.Add(this.label5);
			this.Controls.Add(this.button9);
			this.Controls.Add(this.button8);
			this.Controls.Add(this.button7);
			this.Controls.Add(this.button6);
			this.Controls.Add(this.button5);
			this.Controls.Add(this.button4);
			this.Controls.Add(this.button3);
			this.Controls.Add(this.button2);
			this.Controls.Add(this.button1);
			this.Name = "DCSAlignmentProperties";
			this.Size = new System.Drawing.Size(112, 94);
			this.ResumeLayout(false);

		}
		#endregion

		private void button_Click(object sender, System.EventArgs e)
		{
			if (sender == this.m_buttonCurrent) return;
			else 
			{
				this.m_buttonCurrent.Text = "";
				this.m_buttonCurrent.BackColor = this.m_color;
			}
			this.m_buttonCurrent = (Button)sender;
			this.m_buttonCurrent.Text = "*";
			this.m_buttonCurrent.BackColor = this.m_colorPick;
		}

		public void SetAlignment(DCSDEV.DCSDatatypes.Justifications iHoriz, DCSDEV.DCSDatatypes.Alignments iVert)
		{ 
			this.m_buttonCurrent.Text = "";
			this.m_buttonCurrent.BackColor = this.m_color;
			int index = (int)iHoriz + (int)iVert*3;
			if (index >= 9) index = 0;
			switch(index)
			{
				case 0: m_buttonCurrent = button1; break;
				case 1: m_buttonCurrent = button2; break;
				case 2: m_buttonCurrent = button3; break;
				case 3: m_buttonCurrent = button4; break;
				case 4: m_buttonCurrent = button5; break;
				case 5: m_buttonCurrent = button6; break;
				case 6: m_buttonCurrent = button7; break;
				case 7: m_buttonCurrent = button8; break;
				case 8: m_buttonCurrent = button9; break;
			}
			this.m_buttonCurrent.Text = "*";
			this.m_buttonCurrent.BackColor = this.m_colorPick;
		}
		public void GetAlignment(out int iHoriz, out int iVert)
		{
			int index;
			if      (m_buttonCurrent == button1) index = 0;
			else if (m_buttonCurrent == button2) index = 1;
			else if (m_buttonCurrent == button3) index = 2;
			else if (m_buttonCurrent == button4) index = 3;
			else if (m_buttonCurrent == button5) index = 4;
			else if (m_buttonCurrent == button6) index = 5;
			else if (m_buttonCurrent == button7) index = 6;
			else if (m_buttonCurrent == button8) index = 7;
			else if (m_buttonCurrent == button9) index = 8;
			else index = 0;
			iHoriz = index / 3;
			iVert = index % 3;
		}
	}
}
