<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="label4.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>59, 112</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 16</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>Grid Y spacing</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="label5.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label5.Location" type="System.Drawing.Point, System.Drawing">
    <value>59, 86</value>
  </data>
  <data name="label5.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 16</value>
  </data>
  <data name="label5.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>Grid X spacing</value>
  </data>
  <data name="&gt;&gt;label5.Name" xml:space="preserve">
    <value>label5</value>
  </data>
  <data name="&gt;&gt;label5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label5.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label5.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="numericUpDownGridX.Location" type="System.Drawing.Point, System.Drawing">
    <value>153, 86</value>
  </data>
  <data name="numericUpDownGridX.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="numericUpDownGridX.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;numericUpDownGridX.Name" xml:space="preserve">
    <value>numericUpDownGridX</value>
  </data>
  <data name="&gt;&gt;numericUpDownGridX.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numericUpDownGridX.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;numericUpDownGridX.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="numericUpDownGridY.Location" type="System.Drawing.Point, System.Drawing">
    <value>153, 112</value>
  </data>
  <data name="numericUpDownGridY.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="numericUpDownGridY.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;numericUpDownGridY.Name" xml:space="preserve">
    <value>numericUpDownGridY</value>
  </data>
  <data name="&gt;&gt;numericUpDownGridY.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numericUpDownGridY.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;numericUpDownGridY.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="buttonCancel.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>464, 384</value>
  </data>
  <data name="buttonCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 32</value>
  </data>
  <data name="buttonCancel.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="buttonCancel.Text" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Name" xml:space="preserve">
    <value>buttonCancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonCancel.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="buttonAccept.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonAccept.Location" type="System.Drawing.Point, System.Drawing">
    <value>312, 384</value>
  </data>
  <data name="buttonAccept.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 32</value>
  </data>
  <data name="buttonAccept.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="buttonAccept.Text" xml:space="preserve">
    <value>&amp;OK</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Name" xml:space="preserve">
    <value>buttonAccept</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAccept.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="label1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>59, 24</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>150, 19</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>Grid values  for document:</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="labelDocumentName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 9pt, style=Bold</value>
  </data>
  <data name="labelDocumentName.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelDocumentName.Location" type="System.Drawing.Point, System.Drawing">
    <value>218, 24</value>
  </data>
  <data name="labelDocumentName.Size" type="System.Drawing.Size, System.Drawing">
    <value>222, 19</value>
  </data>
  <data name="labelDocumentName.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="labelDocumentName.Text" xml:space="preserve">
    <value>name</value>
  </data>
  <data name="&gt;&gt;labelDocumentName.Name" xml:space="preserve">
    <value>labelDocumentName</value>
  </data>
  <data name="&gt;&gt;labelDocumentName.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelDocumentName.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelDocumentName.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="label2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>59, 43</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>169, 19</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>Units = inches</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="label3.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>59, 165</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>206, 49</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>These values are also used for keyboard Tab increment and Auto Align sensitivity.</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="listBoxTabStopsH.Location" type="System.Drawing.Point, System.Drawing">
    <value>388, 154</value>
  </data>
  <data name="listBoxTabStopsH.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 95</value>
  </data>
  <data name="listBoxTabStopsH.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="&gt;&gt;listBoxTabStopsH.Name" xml:space="preserve">
    <value>listBoxTabStopsH</value>
  </data>
  <data name="&gt;&gt;listBoxTabStopsH.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;listBoxTabStopsH.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;listBoxTabStopsH.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="listBoxTabStopsV.Location" type="System.Drawing.Point, System.Drawing">
    <value>507, 154</value>
  </data>
  <data name="listBoxTabStopsV.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 95</value>
  </data>
  <data name="listBoxTabStopsV.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="&gt;&gt;listBoxTabStopsV.Name" xml:space="preserve">
    <value>listBoxTabStopsV</value>
  </data>
  <data name="&gt;&gt;listBoxTabStopsV.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;listBoxTabStopsV.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;listBoxTabStopsV.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="numericUpDowntoAdd.Location" type="System.Drawing.Point, System.Drawing">
    <value>312, 270</value>
  </data>
  <data name="numericUpDowntoAdd.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="numericUpDowntoAdd.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="&gt;&gt;numericUpDowntoAdd.Name" xml:space="preserve">
    <value>numericUpDowntoAdd</value>
  </data>
  <data name="&gt;&gt;numericUpDowntoAdd.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numericUpDowntoAdd.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;numericUpDowntoAdd.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="buttonAddToH.Location" type="System.Drawing.Point, System.Drawing">
    <value>388, 267</value>
  </data>
  <data name="buttonAddToH.Size" type="System.Drawing.Size, System.Drawing">
    <value>71, 22</value>
  </data>
  <data name="buttonAddToH.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="buttonAddToH.Text" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="&gt;&gt;buttonAddToH.Name" xml:space="preserve">
    <value>buttonAddToH</value>
  </data>
  <data name="&gt;&gt;buttonAddToH.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAddToH.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAddToH.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="buttonAddToV.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonAddToV.Location" type="System.Drawing.Point, System.Drawing">
    <value>508, 267</value>
  </data>
  <data name="buttonAddToV.Size" type="System.Drawing.Size, System.Drawing">
    <value>71, 22</value>
  </data>
  <data name="buttonAddToV.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="buttonAddToV.Text" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="&gt;&gt;buttonAddToV.Name" xml:space="preserve">
    <value>buttonAddToV</value>
  </data>
  <data name="&gt;&gt;buttonAddToV.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAddToV.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAddToV.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="buttonRemoveFromH.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonRemoveFromH.Location" type="System.Drawing.Point, System.Drawing">
    <value>388, 293</value>
  </data>
  <data name="buttonRemoveFromH.Size" type="System.Drawing.Size, System.Drawing">
    <value>71, 22</value>
  </data>
  <data name="buttonRemoveFromH.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="buttonRemoveFromH.Text" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="&gt;&gt;buttonRemoveFromH.Name" xml:space="preserve">
    <value>buttonRemoveFromH</value>
  </data>
  <data name="&gt;&gt;buttonRemoveFromH.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonRemoveFromH.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonRemoveFromH.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="buttonRemoveFromV.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonRemoveFromV.Location" type="System.Drawing.Point, System.Drawing">
    <value>508, 293</value>
  </data>
  <data name="buttonRemoveFromV.Size" type="System.Drawing.Size, System.Drawing">
    <value>71, 22</value>
  </data>
  <data name="buttonRemoveFromV.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="buttonRemoveFromV.Text" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="&gt;&gt;buttonRemoveFromV.Name" xml:space="preserve">
    <value>buttonRemoveFromV</value>
  </data>
  <data name="&gt;&gt;buttonRemoveFromV.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonRemoveFromV.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonRemoveFromV.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="label6.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label6.Location" type="System.Drawing.Point, System.Drawing">
    <value>287, 248</value>
  </data>
  <data name="label6.Size" type="System.Drawing.Size, System.Drawing">
    <value>81, 19</value>
  </data>
  <data name="label6.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>Value to add:</value>
  </data>
  <data name="label6.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>TopRight</value>
  </data>
  <data name="&gt;&gt;label6.Name" xml:space="preserve">
    <value>label6</value>
  </data>
  <data name="&gt;&gt;label6.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label6.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label6.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="numericUpDownTabRange.Location" type="System.Drawing.Point, System.Drawing">
    <value>507, 106</value>
  </data>
  <data name="numericUpDownTabRange.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="numericUpDownTabRange.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="&gt;&gt;numericUpDownTabRange.Name" xml:space="preserve">
    <value>numericUpDownTabRange</value>
  </data>
  <data name="&gt;&gt;numericUpDownTabRange.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numericUpDownTabRange.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;numericUpDownTabRange.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label8.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label8.Location" type="System.Drawing.Point, System.Drawing">
    <value>388, 108</value>
  </data>
  <data name="label8.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 20</value>
  </data>
  <data name="label8.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="label8.Text" xml:space="preserve">
    <value>Active range</value>
  </data>
  <data name="&gt;&gt;label8.Name" xml:space="preserve">
    <value>label8</value>
  </data>
  <data name="&gt;&gt;label8.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label8.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label8.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="label9.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label9.Location" type="System.Drawing.Point, System.Drawing">
    <value>388, 86</value>
  </data>
  <data name="label9.Size" type="System.Drawing.Size, System.Drawing">
    <value>206, 19</value>
  </data>
  <data name="label9.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="label9.Text" xml:space="preserve">
    <value>Tabular alignment values</value>
  </data>
  <data name="&gt;&gt;label9.Name" xml:space="preserve">
    <value>label9</value>
  </data>
  <data name="&gt;&gt;label9.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label9.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label9.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="label10.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label10.Location" type="System.Drawing.Point, System.Drawing">
    <value>388, 135</value>
  </data>
  <data name="label10.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 16</value>
  </data>
  <data name="label10.TabIndex" type="System.Int32, mscorlib">
    <value>29</value>
  </data>
  <data name="label10.Text" xml:space="preserve">
    <value>Column positions</value>
  </data>
  <data name="&gt;&gt;label10.Name" xml:space="preserve">
    <value>label10</value>
  </data>
  <data name="&gt;&gt;label10.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label10.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label10.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label11.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label11.Location" type="System.Drawing.Point, System.Drawing">
    <value>508, 135</value>
  </data>
  <data name="label11.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 16</value>
  </data>
  <data name="label11.TabIndex" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="label11.Text" xml:space="preserve">
    <value>Row positions</value>
  </data>
  <data name="&gt;&gt;label11.Name" xml:space="preserve">
    <value>label11</value>
  </data>
  <data name="&gt;&gt;label11.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label11.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label11.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleBaseSize" type="System.Drawing.Size, System.Drawing">
    <value>5, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>632, 446</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>SetGridProperties</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>SetGridProperties</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>