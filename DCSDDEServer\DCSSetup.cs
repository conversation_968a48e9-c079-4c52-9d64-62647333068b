using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;

using DCSSDK.CaptureMgt;
using DCSSDK.BadgingMgt;

namespace DCSDEV.DDEServer
{
	/// <summary>
	/// Summary description for DCSSetup.
	/// </summary>
	internal class DCSSetup : System.Windows.Forms.Form
	{
        private DCSSDK.CaptureMgt.DCSSDK_CaptureMgt m_dlgCaptureMgt;
		private DCSSDK.BadgingMgt.DCSSDK_BadgingMgt m_dlgBadgingMgt;

		private System.Windows.Forms.Button buttonSetupCapture;
		private System.Windows.Forms.Button buttonSetupBadges;
		private System.Windows.Forms.Button buttonClose;
		private System.Windows.Forms.Button buttonDDEOptions;
		private Label label1;
		private Label label2;
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		internal DCSSetup(DCSSDK.CaptureMgt.DCSSDK_CaptureMgt dlgCaptureMgt, DCSSDK.BadgingMgt.DCSSDK_BadgingMgt dlgBadgingMgt)
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			m_dlgCaptureMgt = dlgCaptureMgt;
			m_dlgBadgingMgt = dlgBadgingMgt;
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(DCSSetup));
			this.buttonSetupCapture = new System.Windows.Forms.Button();
			this.buttonSetupBadges = new System.Windows.Forms.Button();
			this.buttonClose = new System.Windows.Forms.Button();
			this.buttonDDEOptions = new System.Windows.Forms.Button();
			this.label1 = new System.Windows.Forms.Label();
			this.label2 = new System.Windows.Forms.Label();
			this.SuspendLayout();
			// 
			// buttonSetupCapture
			// 
			this.buttonSetupCapture.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			resources.ApplyResources(this.buttonSetupCapture, "buttonSetupCapture");
			this.buttonSetupCapture.Name = "buttonSetupCapture";
			this.buttonSetupCapture.Click += new System.EventHandler(this.buttonSetupCapture_Click);
			// 
			// buttonSetupBadges
			// 
			this.buttonSetupBadges.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			resources.ApplyResources(this.buttonSetupBadges, "buttonSetupBadges");
			this.buttonSetupBadges.Name = "buttonSetupBadges";
			this.buttonSetupBadges.Click += new System.EventHandler(this.buttonSetupBadges_Click);
			// 
			// buttonClose
			// 
			this.buttonClose.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			resources.ApplyResources(this.buttonClose, "buttonClose");
			this.buttonClose.Name = "buttonClose";
			this.buttonClose.Click += new System.EventHandler(this.buttonClose_Click);
			// 
			// buttonDDEOptions
			// 
			this.buttonDDEOptions.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			resources.ApplyResources(this.buttonDDEOptions, "buttonDDEOptions");
			this.buttonDDEOptions.Name = "buttonDDEOptions";
			this.buttonDDEOptions.Click += new System.EventHandler(this.buttonDDEOptions_Click);
			// 
			// label1
			// 
			resources.ApplyResources(this.label1, "label1");
			this.label1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(192)))));
			this.label1.Name = "label1";
			// 
			// label2
			// 
			resources.ApplyResources(this.label2, "label2");
			this.label2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(0)))), ((int)(((byte)(192)))));
			this.label2.Name = "label2";
			// 
			// DCSSetup
			// 
			resources.ApplyResources(this, "$this");
			this.CancelButton = this.buttonClose;
			this.Controls.Add(this.label2);
			this.Controls.Add(this.label1);
			this.Controls.Add(this.buttonDDEOptions);
			this.Controls.Add(this.buttonClose);
			this.Controls.Add(this.buttonSetupBadges);
			this.Controls.Add(this.buttonSetupCapture);
			this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
			this.MaximizeBox = false;
			this.MinimizeBox = false;
			this.Name = "DCSSetup";
			this.ResumeLayout(false);

		}
		#endregion

		private void buttonSetupCapture_Click(object sender, System.EventArgs e)
		{
			try
			{
				m_dlgCaptureMgt.ShowDialog(this);
			}
			catch(Exception ex)
			{
				DCSMsg.Show(ex);
			}
		}

		private void buttonSetupBadges_Click(object sender, System.EventArgs e)
		{
			try
			{
				m_dlgBadgingMgt.EditBadgingProperties();

				// data root dir may have been changed
				// get Badge Data Directory and reset it and the paths dependent on it
				DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("DCSSDK_Mgt");
				m_dlgCaptureMgt.DataRootDir = ps.GetStringParameter("DataRootDir", ps.m_strDCSInstallDirectory);
			}
			catch(Exception ex)
			{
				DCSMsg.Show(ex);
			}
		}

		private void buttonClose_Click(object sender, System.EventArgs e)
		{
			this.Close();
		}

		private void buttonDDEOptions_Click(object sender, System.EventArgs e)
		{
			DCSDEV.DDEServer.ServerOptions dlg = new DCSDEV.DDEServer.ServerOptions();
			dlg.ShowDialog(this);
		}
	}
}
