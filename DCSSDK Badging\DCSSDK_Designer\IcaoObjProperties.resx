<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonApply.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonApply.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="buttonApply.Location" type="System.Drawing.Point, System.Drawing">
    <value>504, 328</value>
  </data>
  <data name="buttonApply.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 24</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonApply.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="buttonApply.Text" xml:space="preserve">
    <value>&amp;Apply</value>
  </data>
  <data name="&gt;&gt;buttonApply.Name" xml:space="preserve">
    <value>buttonApply</value>
  </data>
  <data name="&gt;&gt;buttonApply.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonApply.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonApply.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="buttonCancel.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonCancel.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>504, 392</value>
  </data>
  <data name="buttonCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 24</value>
  </data>
  <data name="buttonCancel.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="buttonCancel.Text" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Name" xml:space="preserve">
    <value>buttonCancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonCancel.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="buttonAccept.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonAccept.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonAccept.Location" type="System.Drawing.Point, System.Drawing">
    <value>504, 360</value>
  </data>
  <data name="buttonAccept.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 24</value>
  </data>
  <data name="buttonAccept.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="buttonAccept.Text" xml:space="preserve">
    <value>&amp;OK - apply and close</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Name" xml:space="preserve">
    <value>buttonAccept</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAccept.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="rb_3lines.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="rb_3lines.Location" type="System.Drawing.Point, System.Drawing">
    <value>152, 16</value>
  </data>
  <data name="rb_3lines.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 16</value>
  </data>
  <data name="rb_3lines.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="rb_3lines.Text" xml:space="preserve">
    <value>TD-1 - 3 Lines</value>
  </data>
  <data name="&gt;&gt;rb_3lines.Name" xml:space="preserve">
    <value>rb_3lines</value>
  </data>
  <data name="&gt;&gt;rb_3lines.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rb_3lines.Parent" xml:space="preserve">
    <value>gb_type</value>
  </data>
  <data name="&gt;&gt;rb_3lines.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="rb_2linesp.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="rb_2linesp.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 16</value>
  </data>
  <data name="rb_2linesp.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 16</value>
  </data>
  <data name="rb_2linesp.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="rb_2linesp.Text" xml:space="preserve">
    <value>Passport - 2 lines</value>
  </data>
  <data name="&gt;&gt;rb_2linesp.Name" xml:space="preserve">
    <value>rb_2linesp</value>
  </data>
  <data name="&gt;&gt;rb_2linesp.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rb_2linesp.Parent" xml:space="preserve">
    <value>gb_type</value>
  </data>
  <data name="&gt;&gt;rb_2linesp.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="rb_2lines.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="rb_2lines.Location" type="System.Drawing.Point, System.Drawing">
    <value>288, 16</value>
  </data>
  <data name="rb_2lines.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 16</value>
  </data>
  <data name="rb_2lines.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="rb_2lines.Text" xml:space="preserve">
    <value>TD-2 - 2 Lines</value>
  </data>
  <data name="&gt;&gt;rb_2lines.Name" xml:space="preserve">
    <value>rb_2lines</value>
  </data>
  <data name="&gt;&gt;rb_2lines.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rb_2lines.Parent" xml:space="preserve">
    <value>gb_type</value>
  </data>
  <data name="&gt;&gt;rb_2lines.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="gb_type.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 264</value>
  </data>
  <data name="gb_type.Size" type="System.Drawing.Size, System.Drawing">
    <value>440, 48</value>
  </data>
  <data name="gb_type.TabIndex" type="System.Int32, mscorlib">
    <value>73</value>
  </data>
  <data name="gb_type.Text" xml:space="preserve">
    <value>ICAO MRZ type</value>
  </data>
  <data name="&gt;&gt;gb_type.Name" xml:space="preserve">
    <value>gb_type</value>
  </data>
  <data name="&gt;&gt;gb_type.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;gb_type.Parent" xml:space="preserve">
    <value>tabPage2</value>
  </data>
  <data name="&gt;&gt;gb_type.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="dcsAlignmentProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>208, 40</value>
  </data>
  <data name="dcsAlignmentProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 112</value>
  </data>
  <data name="dcsAlignmentProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>72</value>
  </data>
  <data name="&gt;&gt;dcsAlignmentProperties1.Name" xml:space="preserve">
    <value>dcsAlignmentProperties1</value>
  </data>
  <data name="&gt;&gt;dcsAlignmentProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSAlignmentProperties, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsAlignmentProperties1.Parent" xml:space="preserve">
    <value>tabPage2</value>
  </data>
  <data name="&gt;&gt;dcsAlignmentProperties1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="dcsRotationProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>352, 40</value>
  </data>
  <data name="dcsRotationProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 96</value>
  </data>
  <data name="dcsRotationProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>66</value>
  </data>
  <data name="dcsRotationProperties1.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;dcsRotationProperties1.Name" xml:space="preserve">
    <value>dcsRotationProperties1</value>
  </data>
  <data name="&gt;&gt;dcsRotationProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSRotationProperties, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsRotationProperties1.Parent" xml:space="preserve">
    <value>tabPage2</value>
  </data>
  <data name="&gt;&gt;dcsRotationProperties1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="dcsFontProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 56</value>
  </data>
  <data name="dcsFontProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>151, 202</value>
  </data>
  <data name="dcsFontProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>65</value>
  </data>
  <data name="&gt;&gt;dcsFontProperties1.Name" xml:space="preserve">
    <value>dcsFontProperties1</value>
  </data>
  <data name="&gt;&gt;dcsFontProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSFontProperties, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsFontProperties1.Parent" xml:space="preserve">
    <value>tabPage2</value>
  </data>
  <data name="&gt;&gt;dcsFontProperties1.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="tabPage2.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tabPage2.Size" type="System.Drawing.Size, System.Drawing">
    <value>456, 342</value>
  </data>
  <data name="tabPage2.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="tabPage2.Text" xml:space="preserve">
    <value>Appearance</value>
  </data>
  <data name="&gt;&gt;tabPage2.Name" xml:space="preserve">
    <value>tabPage2</value>
  </data>
  <data name="&gt;&gt;tabPage2.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabPage2.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabPage2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="em_sample_doctype.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 144</value>
  </data>
  <data name="em_sample_doctype.Size" type="System.Drawing.Size, System.Drawing">
    <value>160, 20</value>
  </data>
  <data name="em_sample_doctype.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="&gt;&gt;em_sample_doctype.Name" xml:space="preserve">
    <value>em_sample_doctype</value>
  </data>
  <data name="&gt;&gt;em_sample_doctype.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;em_sample_doctype.Parent" xml:space="preserve">
    <value>gb_samplevalues</value>
  </data>
  <data name="&gt;&gt;em_sample_doctype.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="em_sample_surnames.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 24</value>
  </data>
  <data name="em_sample_surnames.Size" type="System.Drawing.Size, System.Drawing">
    <value>160, 20</value>
  </data>
  <data name="em_sample_surnames.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;em_sample_surnames.Name" xml:space="preserve">
    <value>em_sample_surnames</value>
  </data>
  <data name="&gt;&gt;em_sample_surnames.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;em_sample_surnames.Parent" xml:space="preserve">
    <value>gb_samplevalues</value>
  </data>
  <data name="&gt;&gt;em_sample_surnames.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="em_sample_opt2.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 264</value>
  </data>
  <data name="em_sample_opt2.Size" type="System.Drawing.Size, System.Drawing">
    <value>160, 20</value>
  </data>
  <data name="em_sample_opt2.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="&gt;&gt;em_sample_opt2.Name" xml:space="preserve">
    <value>em_sample_opt2</value>
  </data>
  <data name="&gt;&gt;em_sample_opt2.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;em_sample_opt2.Parent" xml:space="preserve">
    <value>gb_samplevalues</value>
  </data>
  <data name="&gt;&gt;em_sample_opt2.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="em_sample_docno.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 168</value>
  </data>
  <data name="em_sample_docno.Size" type="System.Drawing.Size, System.Drawing">
    <value>160, 20</value>
  </data>
  <data name="em_sample_docno.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="&gt;&gt;em_sample_docno.Name" xml:space="preserve">
    <value>em_sample_docno</value>
  </data>
  <data name="&gt;&gt;em_sample_docno.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;em_sample_docno.Parent" xml:space="preserve">
    <value>gb_samplevalues</value>
  </data>
  <data name="&gt;&gt;em_sample_docno.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="em_sample_names.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 48</value>
  </data>
  <data name="em_sample_names.Size" type="System.Drawing.Size, System.Drawing">
    <value>160, 20</value>
  </data>
  <data name="em_sample_names.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="&gt;&gt;em_sample_names.Name" xml:space="preserve">
    <value>em_sample_names</value>
  </data>
  <data name="&gt;&gt;em_sample_names.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;em_sample_names.Parent" xml:space="preserve">
    <value>gb_samplevalues</value>
  </data>
  <data name="&gt;&gt;em_sample_names.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="em_sample_opt1.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 240</value>
  </data>
  <data name="em_sample_opt1.Size" type="System.Drawing.Size, System.Drawing">
    <value>160, 20</value>
  </data>
  <data name="em_sample_opt1.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="&gt;&gt;em_sample_opt1.Name" xml:space="preserve">
    <value>em_sample_opt1</value>
  </data>
  <data name="&gt;&gt;em_sample_opt1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;em_sample_opt1.Parent" xml:space="preserve">
    <value>gb_samplevalues</value>
  </data>
  <data name="&gt;&gt;em_sample_opt1.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="em_sample_dateofbirth.CustomFormat" xml:space="preserve">
    <value>MM/dd/yyyy</value>
  </data>
  <data name="em_sample_dateofbirth.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 72</value>
  </data>
  <data name="em_sample_dateofbirth.Size" type="System.Drawing.Size, System.Drawing">
    <value>160, 20</value>
  </data>
  <data name="em_sample_dateofbirth.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="&gt;&gt;em_sample_dateofbirth.Name" xml:space="preserve">
    <value>em_sample_dateofbirth</value>
  </data>
  <data name="&gt;&gt;em_sample_dateofbirth.Type" xml:space="preserve">
    <value>System.Windows.Forms.DateTimePicker, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;em_sample_dateofbirth.Parent" xml:space="preserve">
    <value>gb_samplevalues</value>
  </data>
  <data name="&gt;&gt;em_sample_dateofbirth.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="em_sample_dateofexpiry.CustomFormat" xml:space="preserve">
    <value>MM/dd/yyyy</value>
  </data>
  <data name="em_sample_dateofexpiry.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 192</value>
  </data>
  <data name="em_sample_dateofexpiry.Size" type="System.Drawing.Size, System.Drawing">
    <value>160, 20</value>
  </data>
  <data name="em_sample_dateofexpiry.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="&gt;&gt;em_sample_dateofexpiry.Name" xml:space="preserve">
    <value>em_sample_dateofexpiry</value>
  </data>
  <data name="&gt;&gt;em_sample_dateofexpiry.Type" xml:space="preserve">
    <value>System.Windows.Forms.DateTimePicker, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;em_sample_dateofexpiry.Parent" xml:space="preserve">
    <value>gb_samplevalues</value>
  </data>
  <data name="&gt;&gt;em_sample_dateofexpiry.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="em_sample_nationality.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 96</value>
  </data>
  <data name="em_sample_nationality.Size" type="System.Drawing.Size, System.Drawing">
    <value>160, 20</value>
  </data>
  <data name="em_sample_nationality.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="&gt;&gt;em_sample_nationality.Name" xml:space="preserve">
    <value>em_sample_nationality</value>
  </data>
  <data name="&gt;&gt;em_sample_nationality.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;em_sample_nationality.Parent" xml:space="preserve">
    <value>gb_samplevalues</value>
  </data>
  <data name="&gt;&gt;em_sample_nationality.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="em_sample_issuingstate.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 216</value>
  </data>
  <data name="em_sample_issuingstate.Size" type="System.Drawing.Size, System.Drawing">
    <value>160, 20</value>
  </data>
  <data name="em_sample_issuingstate.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;em_sample_issuingstate.Name" xml:space="preserve">
    <value>em_sample_issuingstate</value>
  </data>
  <data name="&gt;&gt;em_sample_issuingstate.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;em_sample_issuingstate.Parent" xml:space="preserve">
    <value>gb_samplevalues</value>
  </data>
  <data name="&gt;&gt;em_sample_issuingstate.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="ddlb_sample_sex.Items" xml:space="preserve">
    <value>Male</value>
  </data>
  <data name="ddlb_sample_sex.Items1" xml:space="preserve">
    <value>Female</value>
  </data>
  <data name="ddlb_sample_sex.Items2" xml:space="preserve">
    <value>Unknown</value>
  </data>
  <data name="ddlb_sample_sex.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 120</value>
  </data>
  <data name="ddlb_sample_sex.Size" type="System.Drawing.Size, System.Drawing">
    <value>160, 21</value>
  </data>
  <data name="ddlb_sample_sex.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="&gt;&gt;ddlb_sample_sex.Name" xml:space="preserve">
    <value>ddlb_sample_sex</value>
  </data>
  <data name="&gt;&gt;ddlb_sample_sex.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;ddlb_sample_sex.Parent" xml:space="preserve">
    <value>gb_samplevalues</value>
  </data>
  <data name="&gt;&gt;ddlb_sample_sex.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="gb_samplevalues.Location" type="System.Drawing.Point, System.Drawing">
    <value>264, 8</value>
  </data>
  <data name="gb_samplevalues.Size" type="System.Drawing.Size, System.Drawing">
    <value>184, 296</value>
  </data>
  <data name="gb_samplevalues.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="gb_samplevalues.Text" xml:space="preserve">
    <value>Sample/Default Values</value>
  </data>
  <data name="&gt;&gt;gb_samplevalues.Name" xml:space="preserve">
    <value>gb_samplevalues</value>
  </data>
  <data name="&gt;&gt;gb_samplevalues.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;gb_samplevalues.Parent" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;gb_samplevalues.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 24</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 24</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>Primary Identifier</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 48</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 24</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>Secondary Identifier</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label3.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 96</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 24</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>Nationality</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="label4.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 72</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 24</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>29</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>Date of Birth</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label5.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label5.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 264</value>
  </data>
  <data name="label5.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 24</value>
  </data>
  <data name="label5.TabIndex" type="System.Int32, mscorlib">
    <value>32</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>Optional 2</value>
  </data>
  <data name="&gt;&gt;label5.Name" xml:space="preserve">
    <value>label5</value>
  </data>
  <data name="&gt;&gt;label5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label5.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;label5.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="label6.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label6.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 120</value>
  </data>
  <data name="label6.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 24</value>
  </data>
  <data name="label6.TabIndex" type="System.Int32, mscorlib">
    <value>33</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>Sex</value>
  </data>
  <data name="&gt;&gt;label6.Name" xml:space="preserve">
    <value>label6</value>
  </data>
  <data name="&gt;&gt;label6.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label6.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;label6.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="label7.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label7.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 168</value>
  </data>
  <data name="label7.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 24</value>
  </data>
  <data name="label7.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="label7.Text" xml:space="preserve">
    <value>Document Number</value>
  </data>
  <data name="&gt;&gt;label7.Name" xml:space="preserve">
    <value>label7</value>
  </data>
  <data name="&gt;&gt;label7.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label7.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;label7.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="label8.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label8.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 240</value>
  </data>
  <data name="label8.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 24</value>
  </data>
  <data name="label8.TabIndex" type="System.Int32, mscorlib">
    <value>34</value>
  </data>
  <data name="label8.Text" xml:space="preserve">
    <value>Optional 1</value>
  </data>
  <data name="&gt;&gt;label8.Name" xml:space="preserve">
    <value>label8</value>
  </data>
  <data name="&gt;&gt;label8.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label8.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;label8.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="label9.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label9.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 144</value>
  </data>
  <data name="label9.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 24</value>
  </data>
  <data name="label9.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="label9.Text" xml:space="preserve">
    <value>Document Type</value>
  </data>
  <data name="&gt;&gt;label9.Name" xml:space="preserve">
    <value>label9</value>
  </data>
  <data name="&gt;&gt;label9.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label9.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;label9.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="label10.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label10.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 216</value>
  </data>
  <data name="label10.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 24</value>
  </data>
  <data name="label10.TabIndex" type="System.Int32, mscorlib">
    <value>31</value>
  </data>
  <data name="label10.Text" xml:space="preserve">
    <value>Issuing Country</value>
  </data>
  <data name="&gt;&gt;label10.Name" xml:space="preserve">
    <value>label10</value>
  </data>
  <data name="&gt;&gt;label10.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label10.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;label10.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="label11.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label11.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 192</value>
  </data>
  <data name="label11.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 24</value>
  </data>
  <data name="label11.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="label11.Text" xml:space="preserve">
    <value>Date of Expiry</value>
  </data>
  <data name="&gt;&gt;label11.Name" xml:space="preserve">
    <value>label11</value>
  </data>
  <data name="&gt;&gt;label11.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label11.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;label11.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="ddlb_icaodatasource_surnames.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 24</value>
  </data>
  <data name="ddlb_icaodatasource_surnames.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 21</value>
  </data>
  <data name="ddlb_icaodatasource_surnames.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_surnames.Name" xml:space="preserve">
    <value>ddlb_icaodatasource_surnames</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_surnames.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_surnames.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_surnames.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="ddlb_icaodatasource_names.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 48</value>
  </data>
  <data name="ddlb_icaodatasource_names.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 21</value>
  </data>
  <data name="ddlb_icaodatasource_names.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_names.Name" xml:space="preserve">
    <value>ddlb_icaodatasource_names</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_names.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_names.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_names.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="ddlb_icaodatasource_nationality.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 96</value>
  </data>
  <data name="ddlb_icaodatasource_nationality.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 21</value>
  </data>
  <data name="ddlb_icaodatasource_nationality.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_nationality.Name" xml:space="preserve">
    <value>ddlb_icaodatasource_nationality</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_nationality.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_nationality.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_nationality.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="ddlb_icaodatasource_dateofbirth.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 72</value>
  </data>
  <data name="ddlb_icaodatasource_dateofbirth.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 21</value>
  </data>
  <data name="ddlb_icaodatasource_dateofbirth.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_dateofbirth.Name" xml:space="preserve">
    <value>ddlb_icaodatasource_dateofbirth</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_dateofbirth.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_dateofbirth.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_dateofbirth.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="ddlb_icaodatasource_opt2.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 264</value>
  </data>
  <data name="ddlb_icaodatasource_opt2.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 21</value>
  </data>
  <data name="ddlb_icaodatasource_opt2.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_opt2.Name" xml:space="preserve">
    <value>ddlb_icaodatasource_opt2</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_opt2.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_opt2.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_opt2.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="ddlb_icaodatasource_sex.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 120</value>
  </data>
  <data name="ddlb_icaodatasource_sex.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 21</value>
  </data>
  <data name="ddlb_icaodatasource_sex.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_sex.Name" xml:space="preserve">
    <value>ddlb_icaodatasource_sex</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_sex.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_sex.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_sex.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="ddlb_icaodatasource_dateofexpiry.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 192</value>
  </data>
  <data name="ddlb_icaodatasource_dateofexpiry.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 21</value>
  </data>
  <data name="ddlb_icaodatasource_dateofexpiry.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_dateofexpiry.Name" xml:space="preserve">
    <value>ddlb_icaodatasource_dateofexpiry</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_dateofexpiry.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_dateofexpiry.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_dateofexpiry.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="ddlb_icaodatasource_doctype.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 144</value>
  </data>
  <data name="ddlb_icaodatasource_doctype.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 21</value>
  </data>
  <data name="ddlb_icaodatasource_doctype.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_doctype.Name" xml:space="preserve">
    <value>ddlb_icaodatasource_doctype</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_doctype.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_doctype.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_doctype.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="ddlb_icaodatasource_docno.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 168</value>
  </data>
  <data name="ddlb_icaodatasource_docno.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 21</value>
  </data>
  <data name="ddlb_icaodatasource_docno.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_docno.Name" xml:space="preserve">
    <value>ddlb_icaodatasource_docno</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_docno.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_docno.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_docno.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="ddlb_icaodatasource_opt1.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 240</value>
  </data>
  <data name="ddlb_icaodatasource_opt1.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 21</value>
  </data>
  <data name="ddlb_icaodatasource_opt1.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_opt1.Name" xml:space="preserve">
    <value>ddlb_icaodatasource_opt1</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_opt1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_opt1.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_opt1.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="ddlb_icaodatasource_issuingstate.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 216</value>
  </data>
  <data name="ddlb_icaodatasource_issuingstate.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 21</value>
  </data>
  <data name="ddlb_icaodatasource_issuingstate.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_issuingstate.Name" xml:space="preserve">
    <value>ddlb_icaodatasource_issuingstate</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_issuingstate.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_issuingstate.Parent" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;ddlb_icaodatasource_issuingstate.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="gb_icao_db.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 8</value>
  </data>
  <data name="gb_icao_db.Size" type="System.Drawing.Size, System.Drawing">
    <value>256, 296</value>
  </data>
  <data name="gb_icao_db.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gb_icao_db.Text" xml:space="preserve">
    <value>Database designObject</value>
  </data>
  <data name="&gt;&gt;gb_icao_db.Name" xml:space="preserve">
    <value>gb_icao_db</value>
  </data>
  <data name="&gt;&gt;gb_icao_db.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;gb_icao_db.Parent" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;gb_icao_db.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="tabPage1.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 22</value>
  </data>
  <data name="tabPage1.Size" type="System.Drawing.Size, System.Drawing">
    <value>456, 342</value>
  </data>
  <data name="tabPage1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="tabPage1.Text" xml:space="preserve">
    <value>Source database tokens</value>
  </data>
  <data name="&gt;&gt;tabPage1.Name" xml:space="preserve">
    <value>tabPage1</value>
  </data>
  <data name="&gt;&gt;tabPage1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabPage, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabPage1.Parent" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabPage1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="tabControl1.ItemSize" type="System.Drawing.Size, System.Drawing">
    <value>0, 18</value>
  </data>
  <data name="tabControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 72</value>
  </data>
  <data name="tabControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>464, 368</value>
  </data>
  <data name="tabControl1.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;tabControl1.Name" xml:space="preserve">
    <value>tabControl1</value>
  </data>
  <data name="&gt;&gt;tabControl1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TabControl, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tabControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tabControl1.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="buttonRotateCW.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonRotateCW.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonRotateCW.Location" type="System.Drawing.Point, System.Drawing">
    <value>576, 296</value>
  </data>
  <data name="buttonRotateCW.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 24</value>
  </data>
  <data name="buttonRotateCW.TabIndex" type="System.Int32, mscorlib">
    <value>76</value>
  </data>
  <data name="buttonRotateCW.Text" xml:space="preserve">
    <value>+90</value>
  </data>
  <data name="buttonRotateCW.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;buttonRotateCW.Name" xml:space="preserve">
    <value>buttonRotateCW</value>
  </data>
  <data name="&gt;&gt;buttonRotateCW.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonRotateCW.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonRotateCW.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="buttonRotateCCW.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonRotateCCW.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonRotateCCW.Location" type="System.Drawing.Point, System.Drawing">
    <value>504, 296</value>
  </data>
  <data name="buttonRotateCCW.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 24</value>
  </data>
  <data name="buttonRotateCCW.TabIndex" type="System.Int32, mscorlib">
    <value>75</value>
  </data>
  <data name="buttonRotateCCW.Text" xml:space="preserve">
    <value>-90</value>
  </data>
  <data name="buttonRotateCCW.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;buttonRotateCCW.Name" xml:space="preserve">
    <value>buttonRotateCCW</value>
  </data>
  <data name="&gt;&gt;buttonRotateCCW.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonRotateCCW.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonRotateCCW.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <metadata name="toolTip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="labelPanel.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelPanel.Location" type="System.Drawing.Point, System.Drawing">
    <value>486, 196</value>
  </data>
  <data name="labelPanel.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 16</value>
  </data>
  <data name="labelPanel.TabIndex" type="System.Int32, mscorlib">
    <value>78</value>
  </data>
  <data name="labelPanel.Text" xml:space="preserve">
    <value>panel</value>
  </data>
  <data name="&gt;&gt;labelPanel.Name" xml:space="preserve">
    <value>labelPanel</value>
  </data>
  <data name="&gt;&gt;labelPanel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelPanel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelPanel.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="comboBoxPanel.Items" xml:space="preserve">
    <value>default</value>
  </data>
  <data name="comboBoxPanel.Items1" xml:space="preserve">
    <value>K Panel</value>
  </data>
  <data name="comboBoxPanel.Location" type="System.Drawing.Point, System.Drawing">
    <value>558, 193</value>
  </data>
  <data name="comboBoxPanel.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 21</value>
  </data>
  <data name="comboBoxPanel.TabIndex" type="System.Int32, mscorlib">
    <value>77</value>
  </data>
  <data name="comboBoxPanel.Text" xml:space="preserve">
    <value>default</value>
  </data>
  <data name="&gt;&gt;comboBoxPanel.Name" xml:space="preserve">
    <value>comboBoxPanel</value>
  </data>
  <data name="&gt;&gt;comboBoxPanel.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxPanel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;comboBoxPanel.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="dcsPositionSizeProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>486, 59</value>
  </data>
  <data name="dcsPositionSizeProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 128</value>
  </data>
  <data name="dcsPositionSizeProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>74</value>
  </data>
  <data name="&gt;&gt;dcsPositionSizeProperties1.Name" xml:space="preserve">
    <value>dcsPositionSizeProperties1</value>
  </data>
  <data name="&gt;&gt;dcsPositionSizeProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSPositionSizeProperties, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsPositionSizeProperties1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;dcsPositionSizeProperties1.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>35</value>
  </metadata>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>647, 447</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterParent</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Icao Object Properties</value>
  </data>
  <data name="&gt;&gt;toolTip1.Name" xml:space="preserve">
    <value>toolTip1</value>
  </data>
  <data name="&gt;&gt;toolTip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolTip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>IcaoObjProperties</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>