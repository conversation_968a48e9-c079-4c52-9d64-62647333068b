using System;
using System.Collections;
using System.Windows.Forms;
//using System.Drawing;
//using System.ComponentModel;
//using System.Data;

using System.Reflection;	// for enumeration

namespace DCSDEV
{
	/// <summary>
	/// Summary description for DynamicLoadMgt.
	/// This class supports dynamic loading of a class that supports the indicated interface.  
	///	Methods
	///	  object GetSelectedClass()
	///   void ShowList()
	/// Properties
	///   ArrayList CaptureClassNames
	///   string SelectedCaptureClass
	/// </summary>
	/// 
	public class DynamicLoadMgt
	{
		private string m_strSelectedType = null;
		private Type m_typeLoaded = null;
		private ArrayList m_listTypeNames = new ArrayList();
		private ArrayList m_listFileNames = new ArrayList();

		/// <summary>
		/// Upon creation a list of qualifying classes is built in m_listFileNames and m_listTypeNames.
		/// </summary>
		/// <param name="strBaseDir">Searches for Assemblys in this directory</param>
		/// <param name="strFileMask">Examines only files that conform to this mask</param>
		/// <param name="tInterface">Only Assemblys that implement this interface qualify</param>
		public DynamicLoadMgt(string strBaseDir, string strFileMask, Type tInterface)
		{
			//
			// TODO: Add constructor logic here
			//

			//////////////////////////////////////////////////////////////////////////////
			// Build a list of all the methods that must be in a qualifying assembly    //
			//////////////////////////////////////////////////////////////////////////////
			//		BindingFlags flags = (BindingFlags.NonPublic | BindingFlags.Public | 
			//		BindingFlags.Static | BindingFlags.Instance | BindingFlags.DeclaredOnly);
			BindingFlags flags = BindingFlags.Public | BindingFlags.Instance | BindingFlags.DeclaredOnly;

			MethodInfo[] miInterface = tInterface.GetMethods(flags);
			ArrayList listInterfaceMethods = new ArrayList();
			foreach(MethodInfo m in miInterface)
			{
				listInterfaceMethods.Add(m.Name);
			}
			listInterfaceMethods.Sort();

			////////////////////////////////////////////
			// scan all DLLs for qualifying assemblys //
			////////////////////////////////////////////
			string[] files = System.IO.Directory.GetFiles(strBaseDir, strFileMask);
			foreach (string file in files) 
			{
				try
				{
					Assembly a;
					Type[] mytypes = null;
					try
					{
						a = Assembly.LoadFrom(file);
						mytypes = a.GetTypes();
					}
					catch(Exception ex)
					{
						DCSMsg.Log("ERROR in DynamicLoadMgt: " + Environment.NewLine + "Unable to load file " + file, ex);
						continue;
					}
					foreach(Type t in mytypes)
					{
						if (t.Name == tInterface.Name) continue;
						MethodInfo[] mi = t.GetMethods(flags);

						ArrayList listMethods = new ArrayList();
						foreach(MethodInfo m in mi)
						{
							listMethods.Add(m.Name);
						}
						listMethods.Sort();
						bool bSame = true;
						/***********************************************************************/
						// the following code requires the interfaces to be identical 
						if (listInterfaceMethods.Count != listMethods.Count) bSame = false;
						if (bSame)
						{
							for (int i=0; i<listMethods.Count; i++)
							{
								if (listInterfaceMethods[i].ToString() != listMethods[i].ToString())
								{
									bSame = false;
									break;
								}
							}
						}
						/**********************************************************************
						// the following code requires that tInterface be included as a subset
						if (listInterfaceMethods.Count > listMethods.Count) bSame = false;
						if (bSame)
						{
							int i, j, cnt;
							for (i=0,j=0,cnt=0; i<listMethods.Count && j<listInterfaceMethods.Count; i++)
							{
								if (listMethods[i].ToString() == listInterfaceMethods[j].ToString())
								{
									j++;
									cnt++;
									continue;
								}
							}
							if (cnt != listInterfaceMethods.Count) bSame = false;
						}
						**********************************************************************/
						if (bSame)
						{
							this.m_listFileNames.Add(file);
							this.m_listTypeNames.Add(t.Name);
						}
					}
				}
				catch(Exception ex)
				{
					DCSMsg.Show("ERROR in DynamicLoadMgt: " + Environment.NewLine + "Unable to load and check interface of file " + file, ex);
				}
			}
		}

		/// <summary>
		/// Get list of class type names generated upon creation.
		/// </summary>
		public ArrayList CaptureClassNames
		{
			get
			{
				return this.m_listTypeNames;
			}
		}

		/// <summary>
		/// Get class object that matches the selected type.
		/// </summary>
		/// <returns>
		/// object that supports the required interface type.  
		/// The returned object can be cast to the required interface type.
		/// </returns>
		public object GetSelectedClass()
		{
			if (m_strSelectedType == null || m_strSelectedType == "") 
			{
				DCSMsg.Show("ERROR in DLMCreateInstance: m_strSelectedType is empty.");
				return null;
			}
			try
			{
				if (m_typeLoaded == null || m_typeLoaded.Name != this.m_strSelectedType)
				{
					// find the selected capture type in the array lists
					int index;
					string strFilename = null;
					for (index=0; index<this.m_listTypeNames.Count; index++)
					{
						if ((string)this.m_listTypeNames[index] == this.m_strSelectedType)
						{
							strFilename = (string)this.m_listFileNames[index];
							break;
						}
					}
					if (strFilename == null) 
					{
						DCSMsg.Show("ERROR in DynamicLoadMgt: m_strSelectedType=" + m_strSelectedType + " is not supported.");
						return null;
					}

					Assembly a = Assembly.LoadFrom(strFilename);
					Type[] types = a.GetTypes();
					foreach(Type t in types)
					{
						if (t.Name == m_strSelectedType)
						{
							m_typeLoaded = t;
							break;
						}
					}
				}
				if (m_typeLoaded == null)
					return null;

				return (Activator.CreateInstance(m_typeLoaded));
			}
			catch(Exception ex)
			{
				DCSMsg.Show("ERROR in DLMCreateInstance: ", ex);
				return null;
			}
		}

		public void ShowList()
		{
			for (int i=0; i<this.m_listTypeNames.Count; i++)
			{
				DCSMsg.Show(String.Format("Class {0} in {1}", this.m_listTypeNames[i], this.m_listFileNames[i]));
			}
		}

		/// <summary>
		/// Gets or Sets a class name from the list of class names that conform to the required interface.
		/// </summary>
		public string SelectedCaptureClass
		{
			get { return m_strSelectedType; }
			set 
			{ 
				m_strSelectedType = value;
				foreach(string classname in m_listTypeNames)
					if (classname == m_strSelectedType) return;
				DCSMsg.Show("ERROR in DLMCreateInstance: SelectedCaptureClass " +  m_strSelectedType + " is not listed as a class of the required interface type.");
				m_strSelectedType = null;
			}
		}
	}
}
