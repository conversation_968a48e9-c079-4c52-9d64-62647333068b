using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;

//using DCSDEV.DCSFinisher;

namespace DCSDEV.DCSScanner
{
	/// <summary>
	/// Summary description for ScannerProperties.
	/// </summary>
	public class ScannerProperties : System.Windows.Forms.Form
	{
		private enum ClickedHandle {None, TopLeft, TopRight, BottomRight, BottomLeft, Left, Top, Right, Bottom, Interior};
		private string m_strDCSInstallDirectory;

		private Point m_pointDragStart = new Point(0,0);
		private Rectangle m_rectDragStart = new Rectangle(0,0,0,0);
		private bool m_bMouseIsDown = false;
		private ClickedHandle m_clickedHandle = ClickedHandle.None;
		private Rectangle m_rectDragCurrent;
		private Cursor m_cursorSave;
		private DCSDEV.DCSScanner.DCSScannerObject m_objDragged = null;
	
		private Rectangle m_rectPictureBoxStart;
		private Rectangle m_rectPictureBoxCurrent;
		private Rectangle m_rectThisStart;
		private double m_dCurrentScale;
		private int m_indexCurrentSelectedObject = -1;
		private Rectangle m_rectPreviewScan = new Rectangle(0,0,850,1100);

		private int m_iUnits;
		private bool m_bEnableGetFile = false;
		public ArrayList m_Objects;
		public bool m_bSinglePassScan = true;
		public bool m_bCropFineTune = true;
		public int m_OverScan = 30;

		private bool m_bPortrait = true;
		private bool m_bFingerprint = false;
		private bool m_bSignature = false;
		private bool m_bPortraitScannerCapture = true;
		private bool m_bFingerprintScannerCapture = false;
		private bool m_bSignatureScannerCapture = false;

		private int m_nPortraitInstances = 1;
		private int m_nSignatureInstances = 1;
		private int m_nFingerprintInstances = 1;

		private ScannerIF m_scannerIF = null;

		private System.Windows.Forms.Button buttonOK;
		private System.Windows.Forms.Button buttonCancel;
		private System.Windows.Forms.PictureBox pictureBox1;
		private System.Windows.Forms.GroupBox groupBox2;
		private System.Windows.Forms.Button buttonScanPreview;
		private System.Windows.Forms.GroupBox groupBox3;
		private System.Windows.Forms.ComboBox comboBoxObjects;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.TextBox textBoxResolution;
		private System.Windows.Forms.CheckBox checkBoxSinglePass;
		private System.Windows.Forms.Label label3;
		private System.Windows.Forms.ComboBox comboBoxUnits;
		private DCSSDK_Utilities.UnitizedNumberBox nbX;
		private System.Windows.Forms.Label label4;
		private System.Windows.Forms.Label label5;
		private System.Windows.Forms.Label label6;
		private System.Windows.Forms.Label label7;
		private DCSSDK_Utilities.UnitizedNumberBox nbY;
		private DCSSDK_Utilities.UnitizedNumberBox nbW;
		private DCSSDK_Utilities.UnitizedNumberBox nbH;
		private DCSSDK_Utilities.UnitizedNumberBox nbObjH;
		private DCSSDK_Utilities.UnitizedNumberBox nbObjW;
		private DCSSDK_Utilities.UnitizedNumberBox nbObjY;
		private DCSSDK_Utilities.UnitizedNumberBox nbObjX;
		private System.Windows.Forms.Label label8;
		private System.Windows.Forms.Label label9;
		private System.Windows.Forms.Label label10;
		private System.Windows.Forms.Label label11;
		private DCSSDK_Utilities.UnitizedNumberBox nbOverScan;
		private System.Windows.Forms.Label label12;
		private System.Windows.Forms.TextBox textBoxPixelWidth;
		private System.Windows.Forms.Label label13;
		private System.Windows.Forms.TextBox textBoxPixelHeight;
		private System.Windows.Forms.Label label14;
		private System.Windows.Forms.Label label15;
		private System.Windows.Forms.Label label16;
		private System.Windows.Forms.CheckBox checkBoxCropFineTuning;
		private System.Windows.Forms.Label label17;
		private System.Windows.Forms.PictureBox pictureBoxRotation;
		private System.Windows.Forms.ImageList imageList1;
		private System.ComponentModel.IContainer components;
		private System.Windows.Forms.Button buttonNext;
		private System.Windows.Forms.Button buttonSelectDevice;
		private System.Windows.Forms.TextBox deviceNameTextBox;
		private System.Windows.Forms.CheckBox checkBoxEnableGetFile;

		public ScannerProperties(DCSDEV.ScannerIF scannerIF)
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			m_scannerIF = scannerIF;

			m_rectThisStart = this.Bounds;
			m_rectPictureBoxCurrent = m_rectPictureBoxStart = this.pictureBox1.Bounds;

			m_Objects = new ArrayList();

			m_cursorSave = this.Cursor;

			m_bEnableGetFile = true;

			DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("DCSSDK_Mgt"); 
			m_strDCSInstallDirectory = ps.m_strDCSInstallDirectory;

			m_iUnits = ps.GetIntParameter("DisplayUnits", m_iUnits);
			this.comboBoxUnits.SelectedIndex = m_iUnits;
			this.SetControlUnits(m_iUnits);
			this.GetScannerProperties();
			this.InstallScannerProperties();

			
			// display scanned image
			string strName = System.IO.Path.Combine(m_strDCSInstallDirectory, "_ScannerPreview.Bmp");
			if (System.IO.File.Exists(strName))
			{
				this.pictureBox1.Image = new Bitmap(strName);
				this.pictureBox1.Bounds = DCSDEV.DCSMath.GetBiggestInnerRect(this.pictureBox1.Image.Size, this.m_rectPictureBoxCurrent);
			}
			else
			{
				this.pictureBox1.Bounds = DCSDEV.DCSMath.GetBiggestInnerRect(m_rectPreviewScan.Size, this.m_rectPictureBoxCurrent);
			}
			m_dCurrentScale = (double)this.pictureBox1.Height / (double)m_rectPreviewScan.Height;
			this.pictureBox1.Invalidate();
		}
		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			this.components = new System.ComponentModel.Container();
			System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ScannerProperties));
			this.buttonOK = new System.Windows.Forms.Button();
			this.checkBoxEnableGetFile = new System.Windows.Forms.CheckBox();
			this.buttonCancel = new System.Windows.Forms.Button();
			this.pictureBox1 = new System.Windows.Forms.PictureBox();
			this.groupBox2 = new System.Windows.Forms.GroupBox();
			this.nbH = new DCSSDK_Utilities.UnitizedNumberBox();
			this.nbW = new DCSSDK_Utilities.UnitizedNumberBox();
			this.nbY = new DCSSDK_Utilities.UnitizedNumberBox();
			this.nbX = new DCSSDK_Utilities.UnitizedNumberBox();
			this.label4 = new System.Windows.Forms.Label();
			this.label5 = new System.Windows.Forms.Label();
			this.label6 = new System.Windows.Forms.Label();
			this.label7 = new System.Windows.Forms.Label();
			this.buttonScanPreview = new System.Windows.Forms.Button();
			this.groupBox3 = new System.Windows.Forms.GroupBox();
			this.buttonNext = new System.Windows.Forms.Button();
			this.label17 = new System.Windows.Forms.Label();
			this.pictureBoxRotation = new System.Windows.Forms.PictureBox();
			this.label16 = new System.Windows.Forms.Label();
			this.label15 = new System.Windows.Forms.Label();
			this.textBoxPixelHeight = new System.Windows.Forms.TextBox();
			this.label14 = new System.Windows.Forms.Label();
			this.textBoxPixelWidth = new System.Windows.Forms.TextBox();
			this.label13 = new System.Windows.Forms.Label();
			this.nbObjH = new DCSSDK_Utilities.UnitizedNumberBox();
			this.nbObjW = new DCSSDK_Utilities.UnitizedNumberBox();
			this.nbObjY = new DCSSDK_Utilities.UnitizedNumberBox();
			this.nbObjX = new DCSSDK_Utilities.UnitizedNumberBox();
			this.label8 = new System.Windows.Forms.Label();
			this.label9 = new System.Windows.Forms.Label();
			this.label10 = new System.Windows.Forms.Label();
			this.label11 = new System.Windows.Forms.Label();
			this.textBoxResolution = new System.Windows.Forms.TextBox();
			this.label2 = new System.Windows.Forms.Label();
			this.comboBoxObjects = new System.Windows.Forms.ComboBox();
			this.checkBoxSinglePass = new System.Windows.Forms.CheckBox();
			this.label3 = new System.Windows.Forms.Label();
			this.comboBoxUnits = new System.Windows.Forms.ComboBox();
			this.nbOverScan = new DCSSDK_Utilities.UnitizedNumberBox();
			this.label12 = new System.Windows.Forms.Label();
			this.checkBoxCropFineTuning = new System.Windows.Forms.CheckBox();
			this.imageList1 = new System.Windows.Forms.ImageList(this.components);
			this.buttonSelectDevice = new System.Windows.Forms.Button();
			this.deviceNameTextBox = new System.Windows.Forms.TextBox();
			((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
			this.groupBox2.SuspendLayout();
			this.groupBox3.SuspendLayout();
			((System.ComponentModel.ISupportInitialize)(this.pictureBoxRotation)).BeginInit();
			this.SuspendLayout();
			// 
			// buttonOK
			// 
			resources.ApplyResources(this.buttonOK, "buttonOK");
			this.buttonOK.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
			this.buttonOK.Name = "buttonOK";
			this.buttonOK.UseVisualStyleBackColor = false;
			this.buttonOK.Click += new System.EventHandler(this.buttonOK_Click);
			// 
			// checkBoxEnableGetFile
			// 
			resources.ApplyResources(this.checkBoxEnableGetFile, "checkBoxEnableGetFile");
			this.checkBoxEnableGetFile.Name = "checkBoxEnableGetFile";
			// 
			// buttonCancel
			// 
			resources.ApplyResources(this.buttonCancel, "buttonCancel");
			this.buttonCancel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
			this.buttonCancel.Name = "buttonCancel";
			this.buttonCancel.UseVisualStyleBackColor = false;
			this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
			// 
			// pictureBox1
			// 
			resources.ApplyResources(this.pictureBox1, "pictureBox1");
			this.pictureBox1.BackColor = System.Drawing.SystemColors.ControlLightLight;
			this.pictureBox1.Name = "pictureBox1";
			this.pictureBox1.TabStop = false;
			this.pictureBox1.MouseMove += new System.Windows.Forms.MouseEventHandler(this.pictureBox1_MouseMove);
			this.pictureBox1.MouseDown += new System.Windows.Forms.MouseEventHandler(this.pictureBox1_MouseDown);
			this.pictureBox1.Paint += new System.Windows.Forms.PaintEventHandler(this.pictureBox1_Paint);
			this.pictureBox1.MouseUp += new System.Windows.Forms.MouseEventHandler(this.pictureBox1_MouseUp);
			// 
			// groupBox2
			// 
			this.groupBox2.Controls.Add(this.nbH);
			this.groupBox2.Controls.Add(this.nbW);
			this.groupBox2.Controls.Add(this.nbY);
			this.groupBox2.Controls.Add(this.nbX);
			this.groupBox2.Controls.Add(this.label4);
			this.groupBox2.Controls.Add(this.label5);
			this.groupBox2.Controls.Add(this.label6);
			this.groupBox2.Controls.Add(this.label7);
			this.groupBox2.Controls.Add(this.buttonScanPreview);
			resources.ApplyResources(this.groupBox2, "groupBox2");
			this.groupBox2.Name = "groupBox2";
			this.groupBox2.TabStop = false;
			// 
			// nbH
			// 
			resources.ApplyResources(this.nbH, "nbH");
			this.nbH.Name = "nbH";
			this.nbH.ReadOnly = false;
			this.nbH.UnitizedValue = 0;
			this.nbH.Units = 0;
			// 
			// nbW
			// 
			resources.ApplyResources(this.nbW, "nbW");
			this.nbW.Name = "nbW";
			this.nbW.ReadOnly = false;
			this.nbW.UnitizedValue = 0;
			this.nbW.Units = 0;
			// 
			// nbY
			// 
			resources.ApplyResources(this.nbY, "nbY");
			this.nbY.Name = "nbY";
			this.nbY.ReadOnly = false;
			this.nbY.UnitizedValue = 0;
			this.nbY.Units = 0;
			// 
			// nbX
			// 
			resources.ApplyResources(this.nbX, "nbX");
			this.nbX.Name = "nbX";
			this.nbX.ReadOnly = false;
			this.nbX.UnitizedValue = 0;
			this.nbX.Units = 0;
			// 
			// label4
			// 
			resources.ApplyResources(this.label4, "label4");
			this.label4.Name = "label4";
			// 
			// label5
			// 
			resources.ApplyResources(this.label5, "label5");
			this.label5.Name = "label5";
			// 
			// label6
			// 
			resources.ApplyResources(this.label6, "label6");
			this.label6.Name = "label6";
			// 
			// label7
			// 
			resources.ApplyResources(this.label7, "label7");
			this.label7.Name = "label7";
			// 
			// buttonScanPreview
			// 
			resources.ApplyResources(this.buttonScanPreview, "buttonScanPreview");
			this.buttonScanPreview.Name = "buttonScanPreview";
			this.buttonScanPreview.Click += new System.EventHandler(this.buttonScanPreview_Click);
			// 
			// groupBox3
			// 
			this.groupBox3.Controls.Add(this.buttonNext);
			this.groupBox3.Controls.Add(this.label17);
			this.groupBox3.Controls.Add(this.pictureBoxRotation);
			this.groupBox3.Controls.Add(this.label16);
			this.groupBox3.Controls.Add(this.label15);
			this.groupBox3.Controls.Add(this.textBoxPixelHeight);
			this.groupBox3.Controls.Add(this.label14);
			this.groupBox3.Controls.Add(this.textBoxPixelWidth);
			this.groupBox3.Controls.Add(this.label13);
			this.groupBox3.Controls.Add(this.nbObjH);
			this.groupBox3.Controls.Add(this.nbObjW);
			this.groupBox3.Controls.Add(this.nbObjY);
			this.groupBox3.Controls.Add(this.nbObjX);
			this.groupBox3.Controls.Add(this.label8);
			this.groupBox3.Controls.Add(this.label9);
			this.groupBox3.Controls.Add(this.label10);
			this.groupBox3.Controls.Add(this.label11);
			this.groupBox3.Controls.Add(this.textBoxResolution);
			this.groupBox3.Controls.Add(this.label2);
			this.groupBox3.Controls.Add(this.comboBoxObjects);
			resources.ApplyResources(this.groupBox3, "groupBox3");
			this.groupBox3.Name = "groupBox3";
			this.groupBox3.TabStop = false;
			// 
			// buttonNext
			// 
			resources.ApplyResources(this.buttonNext, "buttonNext");
			this.buttonNext.Name = "buttonNext";
			this.buttonNext.Click += new System.EventHandler(this.buttonNext_Click);
			// 
			// label17
			// 
			resources.ApplyResources(this.label17, "label17");
			this.label17.Name = "label17";
			// 
			// pictureBoxRotation
			// 
			resources.ApplyResources(this.pictureBoxRotation, "pictureBoxRotation");
			this.pictureBoxRotation.Name = "pictureBoxRotation";
			this.pictureBoxRotation.TabStop = false;
			this.pictureBoxRotation.MouseMove += new System.Windows.Forms.MouseEventHandler(this.pictureBoxRotation_MouseMove);
			this.pictureBoxRotation.MouseUp += new System.Windows.Forms.MouseEventHandler(this.pictureBoxRotation_MouseUp);
			// 
			// label16
			// 
			resources.ApplyResources(this.label16, "label16");
			this.label16.Name = "label16";
			// 
			// label15
			// 
			resources.ApplyResources(this.label15, "label15");
			this.label15.Name = "label15";
			// 
			// textBoxPixelHeight
			// 
			resources.ApplyResources(this.textBoxPixelHeight, "textBoxPixelHeight");
			this.textBoxPixelHeight.Name = "textBoxPixelHeight";
			this.textBoxPixelHeight.ReadOnly = true;
			// 
			// label14
			// 
			resources.ApplyResources(this.label14, "label14");
			this.label14.Name = "label14";
			// 
			// textBoxPixelWidth
			// 
			resources.ApplyResources(this.textBoxPixelWidth, "textBoxPixelWidth");
			this.textBoxPixelWidth.Name = "textBoxPixelWidth";
			this.textBoxPixelWidth.ReadOnly = true;
			// 
			// label13
			// 
			resources.ApplyResources(this.label13, "label13");
			this.label13.Name = "label13";
			// 
			// nbObjH
			// 
			resources.ApplyResources(this.nbObjH, "nbObjH");
			this.nbObjH.Name = "nbObjH";
			this.nbObjH.ReadOnly = false;
			this.nbObjH.UnitizedValue = 0;
			this.nbObjH.Units = 0;
			// 
			// nbObjW
			// 
			resources.ApplyResources(this.nbObjW, "nbObjW");
			this.nbObjW.Name = "nbObjW";
			this.nbObjW.ReadOnly = false;
			this.nbObjW.UnitizedValue = 0;
			this.nbObjW.Units = 0;
			// 
			// nbObjY
			// 
			resources.ApplyResources(this.nbObjY, "nbObjY");
			this.nbObjY.Name = "nbObjY";
			this.nbObjY.ReadOnly = false;
			this.nbObjY.UnitizedValue = 0;
			this.nbObjY.Units = 0;
			// 
			// nbObjX
			// 
			resources.ApplyResources(this.nbObjX, "nbObjX");
			this.nbObjX.Name = "nbObjX";
			this.nbObjX.ReadOnly = false;
			this.nbObjX.UnitizedValue = 0;
			this.nbObjX.Units = 0;
			// 
			// label8
			// 
			resources.ApplyResources(this.label8, "label8");
			this.label8.Name = "label8";
			// 
			// label9
			// 
			resources.ApplyResources(this.label9, "label9");
			this.label9.Name = "label9";
			// 
			// label10
			// 
			resources.ApplyResources(this.label10, "label10");
			this.label10.Name = "label10";
			// 
			// label11
			// 
			resources.ApplyResources(this.label11, "label11");
			this.label11.Name = "label11";
			// 
			// textBoxResolution
			// 
			resources.ApplyResources(this.textBoxResolution, "textBoxResolution");
			this.textBoxResolution.Name = "textBoxResolution";
			this.textBoxResolution.ReadOnly = true;
			// 
			// label2
			// 
			resources.ApplyResources(this.label2, "label2");
			this.label2.Name = "label2";
			// 
			// comboBoxObjects
			// 
			resources.ApplyResources(this.comboBoxObjects, "comboBoxObjects");
			this.comboBoxObjects.Name = "comboBoxObjects";
			this.comboBoxObjects.SelectedIndexChanged += new System.EventHandler(this.comboBoxObjects_SelectedIndexChanged);
			// 
			// checkBoxSinglePass
			// 
			this.checkBoxSinglePass.Checked = true;
			this.checkBoxSinglePass.CheckState = System.Windows.Forms.CheckState.Checked;
			resources.ApplyResources(this.checkBoxSinglePass, "checkBoxSinglePass");
			this.checkBoxSinglePass.Name = "checkBoxSinglePass";
			// 
			// label3
			// 
			resources.ApplyResources(this.label3, "label3");
			this.label3.Name = "label3";
			// 
			// comboBoxUnits
			// 
			this.comboBoxUnits.BackColor = System.Drawing.SystemColors.Window;
			this.comboBoxUnits.Items.AddRange(new object[] {
            resources.GetString("comboBoxUnits.Items"),
            resources.GetString("comboBoxUnits.Items1")});
			resources.ApplyResources(this.comboBoxUnits, "comboBoxUnits");
			this.comboBoxUnits.Name = "comboBoxUnits";
			this.comboBoxUnits.SelectedIndexChanged += new System.EventHandler(this.comboBoxUnits_SelectedIndexChanged);
			// 
			// nbOverScan
			// 
			resources.ApplyResources(this.nbOverScan, "nbOverScan");
			this.nbOverScan.Name = "nbOverScan";
			this.nbOverScan.ReadOnly = false;
			this.nbOverScan.UnitizedValue = 0;
			this.nbOverScan.Units = 0;
			// 
			// label12
			// 
			resources.ApplyResources(this.label12, "label12");
			this.label12.Name = "label12";
			// 
			// checkBoxCropFineTuning
			// 
			this.checkBoxCropFineTuning.Checked = true;
			this.checkBoxCropFineTuning.CheckState = System.Windows.Forms.CheckState.Checked;
			resources.ApplyResources(this.checkBoxCropFineTuning, "checkBoxCropFineTuning");
			this.checkBoxCropFineTuning.Name = "checkBoxCropFineTuning";
			// 
			// imageList1
			// 
			this.imageList1.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList1.ImageStream")));
			this.imageList1.TransparentColor = System.Drawing.Color.White;
			this.imageList1.Images.SetKeyName(0, "");
			this.imageList1.Images.SetKeyName(1, "");
			this.imageList1.Images.SetKeyName(2, "");
			this.imageList1.Images.SetKeyName(3, "");
			// 
			// buttonSelectDevice
			// 
			resources.ApplyResources(this.buttonSelectDevice, "buttonSelectDevice");
			this.buttonSelectDevice.Name = "buttonSelectDevice";
			this.buttonSelectDevice.Click += new System.EventHandler(this.buttonSelectDevice_Click);
			// 
			// deviceNameTextBox
			// 
			resources.ApplyResources(this.deviceNameTextBox, "deviceNameTextBox");
			this.deviceNameTextBox.Name = "deviceNameTextBox";
			this.deviceNameTextBox.ReadOnly = true;
			this.deviceNameTextBox.TextChanged += new System.EventHandler(this.deviceNameTextBox_TextChanged);
			// 
			// ScannerProperties
			// 
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			resources.ApplyResources(this, "$this");
			this.Controls.Add(this.deviceNameTextBox);
			this.Controls.Add(this.checkBoxCropFineTuning);
			this.Controls.Add(this.checkBoxSinglePass);
			this.Controls.Add(this.buttonSelectDevice);
			this.Controls.Add(this.label12);
			this.Controls.Add(this.nbOverScan);
			this.Controls.Add(this.comboBoxUnits);
			this.Controls.Add(this.label3);
			this.Controls.Add(this.groupBox3);
			this.Controls.Add(this.groupBox2);
			this.Controls.Add(this.pictureBox1);
			this.Controls.Add(this.buttonCancel);
			this.Controls.Add(this.checkBoxEnableGetFile);
			this.Controls.Add(this.buttonOK);
			this.Name = "ScannerProperties";
			this.ShowInTaskbar = false;
			this.SizeChanged += new System.EventHandler(this.ScannerProperties_SizeChanged);
			((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
			this.groupBox2.ResumeLayout(false);
			this.groupBox3.ResumeLayout(false);
			this.groupBox3.PerformLayout();
			((System.ComponentModel.ISupportInitialize)(this.pictureBoxRotation)).EndInit();
			this.ResumeLayout(false);
			this.PerformLayout();

		}
		#endregion

		private void GetScannerProperties()
		{
            this.ParameterIO(true);

            DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("DCSSDK_Mgt");
			m_strDCSInstallDirectory = ps.m_strDCSInstallDirectory;

			m_bPortrait = ps.GetBoolParameter("PortraitEnabled", true);
			m_bSignature = ps.GetBoolParameter("SignatureEnabled", false);
			m_bFingerprint = ps.GetBoolParameter("FingerprintEnabled", false);
			m_bPortraitScannerCapture = ps.GetBoolParameter("PortraitScannerEnabled", true);
			m_bSignatureScannerCapture = ps.GetBoolParameter("SignatureScannerEnabled", false);
			m_bFingerprintScannerCapture = ps.GetBoolParameter("FingerprintScannerEnabled", false);

			m_nPortraitInstances = ps.GetIntParameter("PortraitInstances", 1);
			m_nSignatureInstances = ps.GetIntParameter("SignatureInstances", 1);
			m_nFingerprintInstances = ps.GetIntParameter("FingerprintInstances", 1);
            
            // make the object list
			DCSDEV.DCSFinisherProperties.FinisherProperties finisherProperties;
			m_Objects.Clear();
			int i;
			DCSDEV.DCSScanner.DCSScannerObject obj;
			if (m_bPortrait && m_bPortraitScannerCapture)
			{
				finisherProperties = new DCSDEV.DCSFinisherProperties.FinisherProperties(DCSDatabaseIF.ImageClass.Portrait);
				for (i=0; i<m_nPortraitInstances; i++)
				{
					if (i == 0)
						obj = new DCSScannerObject("Portrait");
					else
						obj = new DCSScannerObject("Portrait" + i);
					obj.ParameterIO(true);
					obj.m_sizePixels = finisherProperties.CropSpecSize;
                    //if (obj.m_sizePixels == Size.Empty) obj.m_sizePixels = DCSMath.Half(m_rectPreviewScan.Size);
					obj.m_bLockAspect = finisherProperties.IfPreserveAspect;

					this.ValidatePortraitObjectBounds(obj);
					m_Objects.Add(obj);
				}
				finisherProperties.Dispose();
			}
			if (m_bSignature && m_bSignatureScannerCapture)
			{
				finisherProperties = new DCSDEV.DCSFinisherProperties.FinisherProperties(DCSDatabaseIF.ImageClass.Signature);
				for (i=0; i<m_nSignatureInstances; i++)
				{
					if (i == 0)
						obj = new DCSScannerObject("Signature");
					else
						obj = new DCSScannerObject("Signature" + i);
					obj.ParameterIO(true);
					obj.m_sizePixels = finisherProperties.CropSpecSize;
					obj.m_bLockAspect = finisherProperties.IfPreserveAspect;

					this.ValidateSignatureObjectBounds(obj);
					m_Objects.Add(obj);
				}
				finisherProperties.Dispose();
			}
			if (m_bFingerprint && m_bFingerprintScannerCapture)
			{
				finisherProperties = new DCSDEV.DCSFinisherProperties.FinisherProperties(DCSDatabaseIF.ImageClass.Fingerprint);
				for (i=0; i<m_nFingerprintInstances; i++)
				{
					if (i == 0)
						obj = new DCSScannerObject("Fingerprint");
					else
						obj = new DCSScannerObject("Fingerprint" + i);
					obj.ParameterIO(true);
					obj.m_sizePixels = finisherProperties.CropSpecSize;
					obj.m_bLockAspect = finisherProperties.IfPreserveAspect;

					this.ValidateFingerprintObjectBounds(obj);
					m_Objects.Add(obj);
				}
				finisherProperties.Dispose();
			}
		}
		// if no bounds are specified use pixel size at 300 dpi. 
		// adjust object bounds to adhere to aspect ratio
		private void ValidatePortraitObjectBounds(DCSDEV.DCSScanner.DCSScannerObject obj)
		{
			// assume 300 dpi 
            if (obj.m_bounds.Size == Size.Empty) obj.m_bounds.Size = this.m_rectPreviewScan.Size; // DCSMath.DivDouble(obj.m_sizePixels, 3.0);
            if (obj.m_sizePixels == Size.Empty) obj.m_sizePixels = DCSMath.TimesDouble(obj.m_bounds.Size, 3.0);
			if (obj.m_bLockAspect)
			{
				if (obj.m_rotateFlip == 0 || obj.m_rotateFlip == 2)
				{
					double ratioScan = (double)obj.m_bounds.Width / (double)obj.m_bounds.Height;
					double ratioSpec = (double)obj.m_sizePixels.Width / (double)obj.m_sizePixels.Height;
					double ratioOfratios = ratioScan / ratioSpec;
					if (ratioOfratios > 1.10 || ratioOfratios < 0.90)
					{
						Rectangle rect = DCSMath.GetBiggestInnerRect(obj.m_sizePixels, obj.m_bounds);
						obj.m_bounds = rect;
					}
				}
				else
				{
					double ratioScan = (double)obj.m_bounds.Height / (double)obj.m_bounds.Width;
					double ratioSpec = (double)obj.m_sizePixels.Width / (double)obj.m_sizePixels.Height;
					double ratioOfratios = ratioScan / ratioSpec;
					if (ratioOfratios > 1.10 || ratioOfratios < 0.90)
					{
						Size size = new Size(obj.m_sizePixels.Height, obj.m_sizePixels.Width);
						Rectangle rect = DCSMath.GetBiggestInnerRect(size, obj.m_bounds);
						obj.m_bounds = rect;
					}
				}
			}
		}
		// if no bounds are specified use pixel size at 600 dpi. 
		// adjust object bounds to adhere to aspect ratio
		private void ValidateSignatureObjectBounds(DCSDEV.DCSScanner.DCSScannerObject obj)
		{
			// assume 600 dpi 
			if (obj.m_bounds.Size == Size.Empty) obj.m_bounds.Size = DCSMath.DivDouble(obj.m_sizePixels, 6.0);
			if (obj.m_bLockAspect)
			{
				double ratioScan = (double)obj.m_bounds.Width / (double)obj.m_bounds.Height;
				double ratioSpec = (double)obj.m_sizePixels.Width / (double)obj.m_sizePixels.Height;
				double ratioOfratios = ratioScan / ratioSpec;
				if (ratioOfratios > 1.10 || ratioOfratios < 0.90)
				{
					Rectangle rect = DCSMath.GetBiggestInnerRect(obj.m_sizePixels, obj.m_bounds);
					obj.m_bounds = rect;
				}
			}
		}
		// if no bounds are specified use pixel size at 400 dpi. 
		// adjust object bounds to adhere to aspect ratio
		private void ValidateFingerprintObjectBounds(DCSDEV.DCSScanner.DCSScannerObject obj)
		{
			// assume 500 dpi 
			if (obj.m_bounds.Size == Size.Empty) obj.m_bounds.Size = DCSMath.DivDouble(obj.m_sizePixels, 4.0);
			if (obj.m_bLockAspect)
			{
				double ratioScan = (double)obj.m_bounds.Width / (double)obj.m_bounds.Height;
				double ratioSpec = (double)obj.m_sizePixels.Width / (double)obj.m_sizePixels.Height;
				double ratioOfratios = ratioScan / ratioSpec;
				if (ratioOfratios > 1.10 || ratioOfratios < 0.90)
				{
					Rectangle rect = DCSMath.GetBiggestInnerRect(obj.m_sizePixels, obj.m_bounds);
					obj.m_bounds = rect;
				}
			}
		}

		private void WriteScannerProperties()
		{
			foreach (DCSDEV.DCSScanner.DCSScannerObject obj in m_Objects)
			{
				obj.ParameterIO(false);
			}
			this.ParameterIO(false);
		}

		private void ParameterIO(bool bGet)
		{
			DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("DCSScanner");
			ps.StringParameterIO("DCSScannerDeviceID", ref m_scannerIF.m_strSelectedID, bGet);
			ps.StringParameterIO("DCSScannerDeviceName", ref m_scannerIF.m_strSelectedName, bGet);
			ps.BoolParameterIO("EnableGetFile", ref m_bEnableGetFile, bGet);
			ps.BoolParameterIO("SinglePassScan", ref m_bSinglePassScan, bGet);
			ps.BoolParameterIO("CropFineTune", ref m_bCropFineTune, bGet);
			ps.RectParameterIO ("PreviewScanBounds", ref m_rectPreviewScan, bGet);
			ps.IntParameterIO("OverScan", ref m_OverScan, bGet);
		}
		private void InstallScannerProperties()
		{
			this.comboBoxObjects.Items.Clear();
			foreach (DCSDEV.DCSScanner.DCSScannerObject obj in m_Objects)
			{
				this.comboBoxObjects.Items.Add(obj.m_strImageClass);
			}

			this.deviceNameTextBox.Text = m_scannerIF.m_strSelectedName;
			this.checkBoxEnableGetFile.Checked = m_bEnableGetFile;
			this.checkBoxSinglePass.Checked = this.m_bSinglePassScan;
			this.checkBoxCropFineTuning.Checked = m_bCropFineTune;
			this.nbOverScan.UnitizedValue = this.m_OverScan;
			this.SetPreviewScanBoundsControls(m_rectPreviewScan);
			m_indexCurrentSelectedObject = -1;

			if (m_Objects.Count <= 0) return;	// should not be empty

			DCSDEV.DCSScanner.DCSScannerObject objDisplayed = (DCSDEV.DCSScanner.DCSScannerObject)m_Objects[0];
			this.comboBoxObjects.Text = objDisplayed.m_strImageClass;
			this.SetObjBoundsControls(objDisplayed.m_bounds);
			this.textBoxResolution.Text = ((objDisplayed.m_sizePixels.Width * 100) / objDisplayed.m_bounds.Width).ToString();
			this.textBoxPixelWidth.Text = objDisplayed.m_sizePixels.Width.ToString();
			this.textBoxPixelHeight.Text = objDisplayed.m_sizePixels.Height.ToString();
			this.pictureBoxRotation.Image = this.imageList1.Images[objDisplayed.m_rotateFlip];
		}
		private Rectangle GetObjBoundsControls()
		{
			Rectangle rect = Rectangle.Empty;
			rect.X = this.nbObjX.UnitizedValue;
			rect.Y = this.nbObjY.UnitizedValue;
			rect.Width = this.nbObjW.UnitizedValue;
			rect.Height = this.nbObjH.UnitizedValue;
			return rect;
		}
		private Rectangle GetPreviewScanBoundsControls()
		{
			Rectangle rect = Rectangle.Empty;
			rect.X = this.nbX.UnitizedValue;
			rect.Y = this.nbY.UnitizedValue;
			rect.Width = this.nbW.UnitizedValue;
			rect.Height = this.nbH.UnitizedValue;
			return rect;
		}
		private void SetObjBoundsControls(Rectangle bounds)
		{
			this.nbObjX.UnitizedValue = bounds.X;
			this.nbObjY.UnitizedValue = bounds.Y;
			this.nbObjW.UnitizedValue = bounds.Width;
			this.nbObjH.UnitizedValue = bounds.Height;
		}
		private void SetPreviewScanBoundsControls(Rectangle bounds)
		{
			this.nbX.UnitizedValue = bounds.X;
			this.nbY.UnitizedValue = bounds.Y;
			this.nbW.UnitizedValue = bounds.Width;
			this.nbH.UnitizedValue = bounds.Height;
		}

		private void ExtractScannerProperties()
		{
			m_bEnableGetFile = this.checkBoxEnableGetFile.Checked;
			this.m_bSinglePassScan = this.checkBoxSinglePass.Checked;
			this.m_bCropFineTune = this.checkBoxCropFineTuning.Checked;
			this.m_OverScan = this.nbOverScan.UnitizedValue;
			m_rectPreviewScan = this.GetPreviewScanBoundsControls();
			int index = this.comboBoxObjects.SelectedIndex;
			if (m_Objects.Count > 0)
			{
				if (index < 0 || index >= m_Objects.Count) index = 0;	// this second check shouldn't be necessary
				DCSDEV.DCSScanner.DCSScannerObject objDisplayed = (DCSDEV.DCSScanner.DCSScannerObject)m_Objects[index];
				if (this.comboBoxObjects.Text != objDisplayed.m_strImageClass) MessageBox.Show("oops");
				objDisplayed.m_bounds = this.GetObjBoundsControls();
			}
		}

		private Rectangle TranslateScaleRect(Rectangle rectIn)
		{
			Rectangle rect = rectIn;
			m_rectPreviewScan = this.GetPreviewScanBoundsControls();
			rect.Offset(-m_rectPreviewScan.X, -m_rectPreviewScan.Y);
			return DCSDEV.DCSMath.TimesDouble(rect, m_dCurrentScale);
		}
		private Rectangle UnTranslateScaleRect(Rectangle rectIn)
		{
			Rectangle rect = DCSDEV.DCSMath.DivDouble(rectIn, m_dCurrentScale);
			rect.Offset(m_rectPreviewScan.X, m_rectPreviewScan.Y);
			return rect;
		}

		private void GetClickedHandle(Point point, ref ClickedHandle clickedHandle, ref DCSDEV.DCSScanner.DCSScannerObject objClicked)
		{
			if (m_Objects.Count <= 0)
			{
				clickedHandle = ClickedHandle.None;
				return;
			}

			Rectangle rectNet;
			Rectangle rectBox = new Rectangle(-5,-5,10,10);
			Rectangle rect;

			foreach(DCSDEV.DCSScanner.DCSScannerObject obj in m_Objects)
			{
				rectNet = this.TranslateScaleRect(obj.m_bounds);
				rect = rectBox;
				rect.Offset(rectNet.Left, rectNet.Top);
				if (rect.Contains(point)) 
				{
					clickedHandle = ClickedHandle.TopLeft;
					objClicked = obj;
					return;
				}
				rect = rectBox;
				rect.Offset(rectNet.Right, rectNet.Top);
				if (rect.Contains(point))
				{
					clickedHandle = ClickedHandle.TopRight;
					objClicked = obj;
					return;
				}
				rect = rectBox;
				rect.Offset(rectNet.Left, rectNet.Bottom);
				if (rect.Contains(point))
				{
					clickedHandle = ClickedHandle.BottomLeft;
					objClicked = obj;
					return;
				}
				rect = rectBox;
				rect.Offset(rectNet.Right, rectNet.Bottom);
				if (rect.Contains(point))
				{
					clickedHandle = ClickedHandle.BottomRight;
					objClicked = obj;
					return;
				}
				if (rectNet.Contains(point))
				{
					clickedHandle = ClickedHandle.Interior;
					objClicked = obj;
					return;
				}
			}
			clickedHandle = ClickedHandle.None;
			objClicked = null;
			return;
		}

		private void DrawDragRect()
		{
			Rectangle rect = this.RectangleToScreen(m_rectDragCurrent);
			rect.Offset(this.pictureBox1.Location);
			System.Windows.Forms.ControlPaint.DrawReversibleFrame(rect, Color.Black, System.Windows.Forms.FrameStyle.Thick);
		}

		private void buttonOK_Click(object sender, System.EventArgs e)
		{
			ExtractScannerProperties();
			WriteScannerProperties();
			this.Close();
		}
		private void SetControlUnits(int units)
		{
			this.nbObjX.Units = units;
			this.nbObjY.Units = units;
			this.nbObjW.Units = units;
			this.nbObjH.Units = units;
			this.nbX.Units = units;
			this.nbY.Units = units;
			this.nbW.Units = units;
			this.nbH.Units = units;
			this.nbOverScan.Units = units;
		}

		private void buttonCancel_Click(object sender, System.EventArgs e)
		{
			this.Close();
		}

		private void buttonScanPreview_Click(object sender, System.EventArgs e)
		{
			if (this.pictureBox1.Image != null)
			{
				this.pictureBox1.Image.Dispose();
				this.pictureBox1.Image = null;
			}

			//acquire image from scanner
			this.ExtractScannerProperties();

			if (!m_scannerIF.m_bScannerIsInitialized)
			{
				m_scannerIF.InitializeScanner();
			}
			if (!m_scannerIF.m_bScannerIsInitialized) return;

			Image bitmap = m_scannerIF.ScanRect(m_rectPreviewScan, 50);
			if (bitmap == null) return;

			// put scanner name into control
			this.deviceNameTextBox.Text = m_scannerIF.m_strSelectedName;

			// display scanned image
			this.pictureBox1.Bounds = DCSDEV.DCSMath.GetBiggestInnerRect(bitmap.Size, this.m_rectPictureBoxCurrent);
			this.pictureBox1.Image = bitmap;
			m_dCurrentScale = (double)this.pictureBox1.Height / (double)m_rectPreviewScan.Height;
			this.pictureBox1.Invalidate();

			// save scanned image for redisplay next time setup is run
			string strName = System.IO.Path.Combine(m_strDCSInstallDirectory, "_ScannerPreview.Bmp");
			try
			{
				bitmap.Save(strName, System.Drawing.Imaging.ImageFormat.Bmp);
			}
#if DEBUG
			catch (Exception ex)
			{
				DCSMsg.Show(ex);
			}
#else
			catch
			{
				// ignore a saving error - the old image may be stuck in use
			}
#endif
		}

		private void ScannerProperties_SizeChanged(object sender, System.EventArgs e)
		{
			Size delta = this.Bounds.Size - m_rectThisStart.Size;
			m_rectPictureBoxCurrent.Size = m_rectPictureBoxStart.Size + delta;
			if (this.pictureBox1.Image != null)
				this.pictureBox1.Bounds = DCSDEV.DCSMath.GetBiggestInnerRect(this.pictureBox1.Image.Size, this.m_rectPictureBoxCurrent);
			m_dCurrentScale = (double)this.pictureBox1.Height / (double)m_rectPreviewScan.Height;
		}

		private void pictureBox1_Paint(object sender, System.Windows.Forms.PaintEventArgs e)
		{
			System.Drawing.Pen penHighlight = new Pen(System.Drawing.Color.Red, 2);
			int index = -1;
			foreach (DCSDEV.DCSScanner.DCSScannerObject obj in m_Objects)
			{
				index++;
				if (obj.m_bounds != Rectangle.Empty)
				{
					Rectangle rect;
					Rectangle rectNet = Rectangle.Empty;

					rectNet = this.TranslateScaleRect(obj.m_bounds);
					e.Graphics.DrawRectangle(penHighlight, rectNet);

					if (index != m_indexCurrentSelectedObject) continue;

					// draw drag handles at corners of rect net
					Rectangle rectBox = new Rectangle(-3,-3,6,6);
					rect = rectBox;
					rect.Offset(rectNet.Left, rectNet.Top);
					e.Graphics.DrawRectangle(penHighlight, rect);
					rect = rectBox;
					rect.Offset(rectNet.Right, rectNet.Top);
					e.Graphics.DrawRectangle(penHighlight, rect);
					rect = rectBox;
					rect.Offset(rectNet.Left, rectNet.Bottom);
					e.Graphics.DrawRectangle(penHighlight, rect);
					rect = rectBox;
					rect.Offset(rectNet.Right, rectNet.Bottom);
					e.Graphics.DrawRectangle(penHighlight, rect);

					/*****
					if (!designObject.IfLockAspect)
					{
						rect = rectBox;
						rect.Offset(rectNet.Right, (rectNet.Bottom + rectNet.Top)/2);
						e.Graphics.DrawRectangle(penHighlight, rect);
						rect = rectBox;
						rect.Offset((rectNet.Left + rectNet.Right)/2, rectNet.Top);
						e.Graphics.DrawRectangle(penHighlight, rect);
						rect = rectBox;
						rect.Offset(rectNet.Left, (rectNet.Bottom + rectNet.Top)/2);
						e.Graphics.DrawRectangle(penHighlight, rect);
						rect = rectBox;
						rect.Offset((rectNet.Left + rectNet.Right)/2, rectNet.Bottom);
						e.Graphics.DrawRectangle(penHighlight, rect);
					}
					*************/
				}
			}
		}

		private void pictureBox1_MouseMove(object sender, System.Windows.Forms.MouseEventArgs e)
		{
			Point point = new Point(e.X,e.Y);
			if (m_bMouseIsDown)
			{
				if (m_clickedHandle == ClickedHandle.None)
				{
					return;
				}
				else if (m_clickedHandle == ClickedHandle.Interior)
				{
					this.DrawDragRect();	// erase old draw rectangle
					Point pointDelta = point;
					pointDelta.Offset(-m_pointDragStart.X, -m_pointDragStart.Y);
					m_rectDragCurrent = m_rectDragStart;
					m_rectDragCurrent.Offset(pointDelta);

					// make the bounds stay inside the badge bounds
					DCSMath.ApplyBounds(ref m_rectDragCurrent, new Rectangle(new Point(0,0), this.pictureBox1.Size));

					this.DrawDragRect();
				}
				else
				{
					// drag by a handle
					this.DrawDragRect();	// erase old draw rectangle
					Point pointDelta = point;
					pointDelta.Offset(-m_pointDragStart.X, -m_pointDragStart.Y);
					if (!pointDelta.IsEmpty)
					{
						string strEdges = "";
						switch(m_clickedHandle)
						{
							case ClickedHandle.TopLeft:
								strEdges = "TL";
								m_rectDragCurrent.X = m_rectDragStart.Left + pointDelta.X;
								m_rectDragCurrent.Width = m_rectDragStart.Width - pointDelta.X;
								m_rectDragCurrent.Y = m_rectDragStart.Top + pointDelta.Y;
								m_rectDragCurrent.Height = m_rectDragStart.Height - pointDelta.Y;
								break;
							case ClickedHandle.TopRight:
								strEdges = "TR";
								m_rectDragCurrent.Width = m_rectDragStart.Width + pointDelta.X;
								m_rectDragCurrent.Y = m_rectDragStart.Top + pointDelta.Y;
								m_rectDragCurrent.Height = m_rectDragStart.Height - pointDelta.Y;
								break;
							case ClickedHandle.BottomLeft:
								strEdges = "BL";
								m_rectDragCurrent.X = m_rectDragStart.Left + pointDelta.X;
								m_rectDragCurrent.Width = m_rectDragStart.Width - pointDelta.X;
								m_rectDragCurrent.Height = m_rectDragStart.Height + pointDelta.Y;
								break;
							case ClickedHandle.BottomRight:
								strEdges = "BR";
								m_rectDragCurrent.Height = m_rectDragStart.Height + pointDelta.Y;
								m_rectDragCurrent.Width = m_rectDragStart.Width + pointDelta.X;
								break;
							case ClickedHandle.Left:
								strEdges = "L";
								m_rectDragCurrent.X = m_rectDragStart.Left + pointDelta.X;
								m_rectDragCurrent.Width = m_rectDragStart.Width - pointDelta.X;
								break;
							case ClickedHandle.Top:
								strEdges = "T";
								m_rectDragCurrent.Y = m_rectDragStart.Top + pointDelta.Y;
								m_rectDragCurrent.Height = m_rectDragStart.Height - pointDelta.Y;
								break;
							case ClickedHandle.Right:
								strEdges = "R";
								m_rectDragCurrent.Width = m_rectDragStart.Width + pointDelta.X;
								break;
							case ClickedHandle.Bottom:
								strEdges = "B";
								m_rectDragCurrent.Height = m_rectDragStart.Height + pointDelta.Y;
								break;
						}

						// make the bounds stay inside the bounds
						DCSMath.ApplyBounds(ref m_rectDragCurrent, new Rectangle(new Point(0,0), this.pictureBox1.Size), strEdges);
						
						// adjust to lock aspect ratio
						if (m_objDragged.m_bLockAspect)
						{
							int dw, dh;
							double ratioObj = (double)m_objDragged.m_bounds.Width / (double)m_objDragged.m_bounds.Height;
							double ratioDrag = (double)m_rectDragCurrent.Width / (double)m_rectDragCurrent.Height;
							if (ratioDrag > ratioObj)
							{
								dw = m_rectDragCurrent.Width - DCSMath.IntTimesDouble(m_rectDragCurrent.Height, ratioObj);
								dh = 0;
							}
							else
							{
								dw = 0;
								dh = m_rectDragCurrent.Height - DCSMath.IntDivDouble(m_rectDragCurrent.Width, ratioObj);
							}
							switch(m_clickedHandle)
							{
								case ClickedHandle.TopLeft:
									m_rectDragCurrent.X += dw;
									m_rectDragCurrent.Width -= dw;
									m_rectDragCurrent.Y += dh;
									m_rectDragCurrent.Height -= dh;
									break;
								case ClickedHandle.TopRight:
									//m_rectDragCurrent.X += dw;
									m_rectDragCurrent.Width -= dw;
									m_rectDragCurrent.Y += dh;
									m_rectDragCurrent.Height -= dh;
									break;
								case ClickedHandle.BottomLeft:
									m_rectDragCurrent.X += dw;
									m_rectDragCurrent.Width -= dw;
									//m_rectDragCurrent.Y += dh;
									m_rectDragCurrent.Height -= dh;
									break;
								case ClickedHandle.BottomRight:
									//m_rectDragCurrent.X;
									m_rectDragCurrent.Width -= dw;
									//m_rectDragCurrent.Y;
									m_rectDragCurrent.Height -= dh;
									break;
							}
						}
					}
					this.DrawDragRect();
				}
			}
			else
			{
				// mouse is up - show cursor that indicates where the mouse is
				if (this.m_Objects.Count != 0)
				{
					ClickedHandle enumClicked = ClickedHandle.None;
					GetClickedHandle(point, ref enumClicked, ref m_objDragged);

					if (enumClicked == ClickedHandle.None)
					{
						this.Cursor = m_cursorSave;
						return;
					}
					this.comboBoxObjects.Text = m_objDragged.m_strImageClass;
					this.SetObjBoundsControls(m_objDragged.m_bounds);
					this.textBoxResolution.Text = ((m_objDragged.m_sizePixels.Width * 100) / m_objDragged.m_bounds.Width).ToString();
					switch(enumClicked)
					{
						case ClickedHandle.TopLeft:
							this.Cursor = Cursors.SizeNWSE;
							break;
						case ClickedHandle.TopRight:
							this.Cursor = Cursors.SizeNESW;
							break;
						case ClickedHandle.BottomLeft:
							this.Cursor = Cursors.SizeNESW;
							break;
						case ClickedHandle.BottomRight:
							this.Cursor = Cursors.SizeNWSE;
							break;
						default:	// Interior Edge or none
						switch(enumClicked)
						{
							case ClickedHandle.Right:
								this.Cursor = Cursors.SizeWE;
								break;
							case ClickedHandle.Top:
								this.Cursor = Cursors.SizeNS;
								break;
							case ClickedHandle.Left:
								this.Cursor = Cursors.SizeWE;
								break;
							case ClickedHandle.Bottom:
								this.Cursor = Cursors.SizeNS;
								break;
							default:	// interior
							case ClickedHandle.Interior:
								this.Cursor = Cursors.SizeAll;
								break;
						}
							break;
					}
				}
			}
		}

		private void pictureBox1_MouseDown(object sender, System.Windows.Forms.MouseEventArgs e)
		{
			m_bMouseIsDown = true;
			if (this.m_Objects.Count <= 0) return;

			Point point = new Point(e.X, e.Y);
			m_pointDragStart = point;	// remember mouse down position

			// find selected object
			GetClickedHandle(point, ref m_clickedHandle, ref m_objDragged);
			if (m_clickedHandle != ClickedHandle.None)
			{ 
				if (m_objDragged.m_strImageClass != this.comboBoxObjects.Text)
				{
					// this shouldn't happen - mouse move w/o click does it
					this.comboBoxObjects.Text = m_objDragged.m_strImageClass;
					this.SetObjBoundsControls(m_objDragged.m_bounds);
					this.textBoxResolution.Text = ((m_objDragged.m_sizePixels.Width * 100) / m_objDragged.m_bounds.Width).ToString();
				}
				m_rectDragCurrent = this.TranslateScaleRect(m_objDragged.m_bounds);
				m_rectDragStart = m_rectDragCurrent; 
				this.DrawDragRect();
				return;
			}
		}

		private void pictureBox1_MouseUp(object sender, System.Windows.Forms.MouseEventArgs e)
		{
			m_bMouseIsDown = false;
			Point point = new Point(e.X,e.Y);
			if (m_clickedHandle == ClickedHandle.Interior)
			{
				//this.DrawDragRect();	// erase old draw rectangle
				Point pointDelta = point;
				pointDelta.Offset(-m_pointDragStart.X, -m_pointDragStart.Y);

				// make the bounds stay inside the badge bounds
				Rectangle rect = m_rectDragStart;
				rect.Offset(pointDelta);
				Rectangle rectAdj = rect;
				DCSMath.ApplyBounds(ref rectAdj, new Rectangle(new Point(0,0), this.pictureBox1.Size));
				pointDelta = pointDelta + (Size)rectAdj.Location - (Size)rect.Location;

				if (!pointDelta.IsEmpty)
				{
					// There is some actual movement of the selected objects
					pointDelta = DCSMath.DivDouble(pointDelta, this.m_dCurrentScale);
					m_objDragged.m_bounds.Offset(pointDelta);
					this.SetObjBoundsControls(m_objDragged.m_bounds);
					this.textBoxResolution.Text = ((m_objDragged.m_sizePixels.Width * 100) / m_objDragged.m_bounds.Width).ToString();
					this.pictureBox1.Invalidate();
				}
			}
			else if (m_clickedHandle != ClickedHandle.None)
			{
				// drag by a handle
				//this.DrawDragRect();	// erase old draw rectangle
				Point pointDelta = point;
				pointDelta.Offset(-m_pointDragStart.X, -m_pointDragStart.Y);
				if (!pointDelta.IsEmpty)
				{
					// select and move - otherwise select only
					DCSDEV.DCSMath.Normalize(ref m_rectDragCurrent);
					pointDelta = DCSMath.DivDouble(pointDelta, this.m_dCurrentScale);
					m_objDragged.m_bounds = UnTranslateScaleRect(m_rectDragCurrent);
					this.SetObjBoundsControls(m_objDragged.m_bounds);
					this.textBoxResolution.Text = ((m_objDragged.m_sizePixels.Width * 100) / m_objDragged.m_bounds.Width).ToString();
					this.pictureBox1.Invalidate();
				}
			}
			this.DrawDragRect();	// erase old draw rectangle
			m_clickedHandle = ClickedHandle.None;
		}

		private void comboBoxObjects_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			DCSDEV.DCSScanner.DCSScannerObject objDisplayed;
			// extract current obj position/size
			if (m_indexCurrentSelectedObject != -1)
			{
				objDisplayed = (DCSDEV.DCSScanner.DCSScannerObject)m_Objects[m_indexCurrentSelectedObject];
				objDisplayed.m_bounds = this.GetObjBoundsControls();
			}

			// set new obj position/size
			m_indexCurrentSelectedObject = this.comboBoxObjects.SelectedIndex;
			objDisplayed = (DCSDEV.DCSScanner.DCSScannerObject)m_Objects[m_indexCurrentSelectedObject];
			if (this.comboBoxObjects.Text != objDisplayed.m_strImageClass) MessageBox.Show("oops");
			this.SetObjBoundsControls(objDisplayed.m_bounds);
			this.textBoxResolution.Text = ((objDisplayed.m_sizePixels.Width * 100) / objDisplayed.m_bounds.Width).ToString();
			this.textBoxPixelWidth.Text = objDisplayed.m_sizePixels.Width.ToString();
			this.textBoxPixelHeight.Text = objDisplayed.m_sizePixels.Height.ToString();
			this.pictureBoxRotation.Image = this.imageList1.Images[objDisplayed.m_rotateFlip];
			this.pictureBox1.Invalidate();
		}

		private void comboBoxUnits_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if (m_iUnits != this.comboBoxUnits.SelectedIndex)
			{
				this.ExtractScannerProperties();	// read controls with old units
				m_iUnits = this.comboBoxUnits.SelectedIndex;
				this.SetControlUnits(m_iUnits);
				this.InstallScannerProperties();	// reset controls with new units

				DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("DCSSDK_Mgt"); 
				ps.WriteIntParameter("DisplayUnits", m_iUnits);
			}
		}

		private int GetRotationPoint(Point point)
		{
			// Control is 48x48, centers are at 12,24 36,24, 24,12 24,36, size should be 12x12
			Point[] points = {	new Point(24-6,12-6), 
								 new Point(36-6,24-6), 
								 new Point(24-6,36-6),
								 new Point(12-6,24-6)}; 

			Rectangle rectBase = new Rectangle(0,0,12,12);
			Rectangle rect;
			for (int i=0; i<4; i++)
			{
				rect = rectBase;
				rect.Offset(points[i]);
				if (rect.Contains(point)) return i ;
			}
			return -1;
		}

		private void pictureBoxRotation_MouseMove(object sender, System.Windows.Forms.MouseEventArgs e)
		{
			Point point = new Point(e.X,e.Y);
			int i = GetRotationPoint(point);
			if (i != -1) this.Cursor = Cursors.SizeAll;
			else this.Cursor = Cursors.Default;
		}

		private void pictureBoxRotation_MouseUp(object sender, System.Windows.Forms.MouseEventArgs e)
		{
			Point point = new Point(e.X,e.Y);
			if (m_Objects.Count <= 0) return;

			int i = GetRotationPoint(point);
			if (i == -1) return;

			DCSDEV.DCSScanner.DCSScannerObject objDisplayed;
			objDisplayed = (DCSDEV.DCSScanner.DCSScannerObject)m_Objects[m_indexCurrentSelectedObject];
			//see if need to swap height and width
			if (i%2 != objDisplayed.m_rotateFlip%2)
			{
				int w = objDisplayed.m_bounds.Width;
				objDisplayed.m_bounds.Width = objDisplayed.m_bounds.Height;
				objDisplayed.m_bounds.Height = w;
				this.nbObjW.UnitizedValue = objDisplayed.m_bounds.Width;
				this.nbObjH.UnitizedValue = objDisplayed.m_bounds.Height;
			}
			objDisplayed.m_rotateFlip = i;
			// adjust the display
			this.pictureBoxRotation.Image = this.imageList1.Images[i];
			this.pictureBox1.Invalidate();
		}

		private void buttonNext_Click(object sender, System.EventArgs e)
		{
			int count = this.comboBoxObjects.Items.Count;
			if (count <= 0) return;
			int idx = this.comboBoxObjects.SelectedIndex;
			if (idx == count - 1) idx = 0;
			else idx++;
			this.comboBoxObjects.SelectedIndex = idx;
		}

		private void buttonSelectDevice_Click(object sender, System.EventArgs e)
		{
			string strDeviceName = String.Empty;
			string strDeviceID = String.Empty;
			bool bRet = m_scannerIF.SelectDevice();
			if (bRet) this.deviceNameTextBox.Text = m_scannerIF.m_strSelectedName;
		}

		private void deviceNameTextBox_TextChanged(object sender, EventArgs e)
		{

		}
	}
	internal class DCSScannerObject
	{
		internal string m_strImageClass;		// = "Portrait";
		internal Rectangle m_bounds;
		internal Size m_sizePixels;
		internal bool m_bLockAspect;
		internal int m_rotateFlip;	// 1=90; 2=180; 3=270

		internal DCSScannerObject(string strImageClass)
		{
			m_strImageClass = strImageClass;
			m_bounds = Rectangle.Empty;
			// this is the requested pixel target - taken from finisher properties
			m_sizePixels = new Size(100, 100);
			m_bLockAspect = false;
			m_rotateFlip = 0;
		}
		internal void ParameterIO(bool bGet)
		{
			DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("ScannerObject_" + m_strImageClass);
			ps.RectParameterIO("Bounds", ref m_bounds, bGet);
			// m_sizePixels is not written - it is taken from finisher properties
			ps.IntParameterIO("Orientation", ref m_rotateFlip, bGet);
		}
	}
}
