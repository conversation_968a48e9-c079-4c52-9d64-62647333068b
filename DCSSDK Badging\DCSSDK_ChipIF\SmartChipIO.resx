<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="labelMain.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="labelMain.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 12pt, style=Bold</value>
  </data>
  <data name="labelMain.Location" type="System.Drawing.Point, System.Drawing">
    <value>239, 127</value>
  </data>
  <data name="labelMain.Size" type="System.Drawing.Size, System.Drawing">
    <value>384, 44</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="labelMain.TabIndex" type="System.Int32, mscorlib">
    <value>40</value>
  </data>
  <data name="labelMain.Text" xml:space="preserve">
    <value>Smart Chip Reader Interface</value>
  </data>
  <data name="labelMain.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="&gt;&gt;labelMain.Name" xml:space="preserve">
    <value>labelMain</value>
  </data>
  <data name="&gt;&gt;labelMain.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelMain.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelMain.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="textBoxTextInChip.Location" type="System.Drawing.Point, System.Drawing">
    <value>25, 174</value>
  </data>
  <data name="textBoxTextInChip.Multiline" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textBoxTextInChip.Size" type="System.Drawing.Size, System.Drawing">
    <value>584, 227</value>
  </data>
  <data name="textBoxTextInChip.TabIndex" type="System.Int32, mscorlib">
    <value>36</value>
  </data>
  <data name="&gt;&gt;textBoxTextInChip.Name" xml:space="preserve">
    <value>textBoxTextInChip</value>
  </data>
  <data name="&gt;&gt;textBoxTextInChip.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxTextInChip.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBoxTextInChip.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="buttonClear.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="buttonClear.Location" type="System.Drawing.Point, System.Drawing">
    <value>132, 411</value>
  </data>
  <data name="buttonClear.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <data name="buttonClear.TabIndex" type="System.Int32, mscorlib">
    <value>37</value>
  </data>
  <data name="buttonClear.Text" xml:space="preserve">
    <value>&amp;Reset</value>
  </data>
  <data name="&gt;&gt;buttonClear.Name" xml:space="preserve">
    <value>buttonClear</value>
  </data>
  <data name="&gt;&gt;buttonClear.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonClear.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonClear.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="buttonCancel.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="buttonCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>404, 411</value>
  </data>
  <data name="buttonCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <data name="buttonCancel.TabIndex" type="System.Int32, mscorlib">
    <value>39</value>
  </data>
  <data name="buttonCancel.Text" xml:space="preserve">
    <value>&amp;Cancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Name" xml:space="preserve">
    <value>buttonCancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonCancel.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="buttonOK.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="buttonOK.Location" type="System.Drawing.Point, System.Drawing">
    <value>268, 411</value>
  </data>
  <data name="buttonOK.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <data name="buttonOK.TabIndex" type="System.Int32, mscorlib">
    <value>38</value>
  </data>
  <data name="buttonOK.Text" xml:space="preserve">
    <value>&amp;OK</value>
  </data>
  <data name="&gt;&gt;buttonOK.Name" xml:space="preserve">
    <value>buttonOK</value>
  </data>
  <data name="&gt;&gt;buttonOK.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonOK.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonOK.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="textBoxChipID.Location" type="System.Drawing.Point, System.Drawing">
    <value>383, 91</value>
  </data>
  <data name="textBoxChipID.Size" type="System.Drawing.Size, System.Drawing">
    <value>199, 13</value>
  </data>
  <data name="textBoxChipID.TabIndex" type="System.Int32, mscorlib">
    <value>42</value>
  </data>
  <data name="&gt;&gt;textBoxChipID.Name" xml:space="preserve">
    <value>textBoxChipID</value>
  </data>
  <data name="&gt;&gt;textBoxChipID.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxChipID.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBoxChipID.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="labelCardID.Location" type="System.Drawing.Point, System.Drawing">
    <value>248, 91</value>
  </data>
  <data name="labelCardID.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 20</value>
  </data>
  <data name="labelCardID.TabIndex" type="System.Int32, mscorlib">
    <value>43</value>
  </data>
  <data name="labelCardID.Text" xml:space="preserve">
    <value>Chip card ID:</value>
  </data>
  <data name="&gt;&gt;labelCardID.Name" xml:space="preserve">
    <value>labelCardID</value>
  </data>
  <data name="&gt;&gt;labelCardID.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelCardID.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelCardID.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="labelDataFile.Location" type="System.Drawing.Point, System.Drawing">
    <value>248, 33</value>
  </data>
  <data name="labelDataFile.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 20</value>
  </data>
  <data name="labelDataFile.TabIndex" type="System.Int32, mscorlib">
    <value>44</value>
  </data>
  <data name="labelDataFile.Text" xml:space="preserve">
    <value>Data file:</value>
  </data>
  <data name="&gt;&gt;labelDataFile.Name" xml:space="preserve">
    <value>labelDataFile</value>
  </data>
  <data name="&gt;&gt;labelDataFile.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelDataFile.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelDataFile.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="textBoxDataFile.Location" type="System.Drawing.Point, System.Drawing">
    <value>383, 33</value>
  </data>
  <data name="textBoxDataFile.Size" type="System.Drawing.Size, System.Drawing">
    <value>241, 13</value>
  </data>
  <data name="textBoxDataFile.TabIndex" type="System.Int32, mscorlib">
    <value>45</value>
  </data>
  <data name="textBoxDataFile.Text" xml:space="preserve">
    <value>data file</value>
  </data>
  <data name="&gt;&gt;textBoxDataFile.Name" xml:space="preserve">
    <value>textBoxDataFile</value>
  </data>
  <data name="&gt;&gt;textBoxDataFile.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxDataFile.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBoxDataFile.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="textBoxConfigFile.Location" type="System.Drawing.Point, System.Drawing">
    <value>383, 14</value>
  </data>
  <data name="textBoxConfigFile.Size" type="System.Drawing.Size, System.Drawing">
    <value>241, 13</value>
  </data>
  <data name="textBoxConfigFile.TabIndex" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="textBoxConfigFile.Text" xml:space="preserve">
    <value>config</value>
  </data>
  <data name="&gt;&gt;textBoxConfigFile.Name" xml:space="preserve">
    <value>textBoxConfigFile</value>
  </data>
  <data name="&gt;&gt;textBoxConfigFile.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxConfigFile.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBoxConfigFile.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="label3.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>248, 14</value>
  </data>
  <data name="label3.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 20</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>46</value>
  </data>
  <data name="label3.Text" xml:space="preserve">
    <value>Config file:</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="tbChipIOMode.Location" type="System.Drawing.Point, System.Drawing">
    <value>382, 52</value>
  </data>
  <data name="tbChipIOMode.Size" type="System.Drawing.Size, System.Drawing">
    <value>241, 13</value>
  </data>
  <data name="tbChipIOMode.TabIndex" type="System.Int32, mscorlib">
    <value>49</value>
  </data>
  <data name="tbChipIOMode.Text" xml:space="preserve">
    <value>chipIOmode</value>
  </data>
  <data name="&gt;&gt;tbChipIOMode.Name" xml:space="preserve">
    <value>tbChipIOMode</value>
  </data>
  <data name="&gt;&gt;tbChipIOMode.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbChipIOMode.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbChipIOMode.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>248, 52</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 20</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>48</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>Chip IO mode:</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="tbChipIFType.Location" type="System.Drawing.Point, System.Drawing">
    <value>382, 72</value>
  </data>
  <data name="tbChipIFType.Size" type="System.Drawing.Size, System.Drawing">
    <value>241, 13</value>
  </data>
  <data name="tbChipIFType.TabIndex" type="System.Int32, mscorlib">
    <value>51</value>
  </data>
  <data name="tbChipIFType.Text" xml:space="preserve">
    <value>chipInterfaceType</value>
  </data>
  <data name="&gt;&gt;tbChipIFType.Name" xml:space="preserve">
    <value>tbChipIFType</value>
  </data>
  <data name="&gt;&gt;tbChipIFType.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbChipIFType.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbChipIFType.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label4.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>248, 72</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 20</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>50</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>Chip interface type:</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="pictureBoxPortrait.Location" type="System.Drawing.Point, System.Drawing">
    <value>37, 232</value>
  </data>
  <data name="pictureBoxPortrait.Size" type="System.Drawing.Size, System.Drawing">
    <value>560, 158</value>
  </data>
  <data name="pictureBoxPortrait.SizeMode" type="System.Windows.Forms.PictureBoxSizeMode, System.Windows.Forms">
    <value>Zoom</value>
  </data>
  <data name="pictureBoxPortrait.TabIndex" type="System.Int32, mscorlib">
    <value>55</value>
  </data>
  <data name="&gt;&gt;pictureBoxPortrait.Name" xml:space="preserve">
    <value>pictureBoxPortrait</value>
  </data>
  <data name="&gt;&gt;pictureBoxPortrait.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pictureBoxPortrait.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pictureBoxPortrait.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>46</value>
  </metadata>
  <data name="$this.AutoScaleBaseSize" type="System.Drawing.Size, System.Drawing">
    <value>5, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>632, 446</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Smart Chip Reader Interface</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>SmartChipIO</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>