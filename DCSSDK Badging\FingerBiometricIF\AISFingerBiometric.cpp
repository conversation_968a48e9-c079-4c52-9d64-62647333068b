// FingerBiometricIF.cpp : Defines the initialization routines for the DLL.
//

#include "stdafx.h"
#include "AISFingerBiometric.h"
#include <io.h>
//#include <commdlg.h> 
//#include <sys\stat.h>
#include <fcntl.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif


#define A_Threshold 30
#define B_Threshold 15

bool DoExtractFeatures(char* szFingerImage, char* szFingerFeaturesFile, long instance)
{
	//AfxMessageBox(CString("DoExtractFeatures ") + CString(szFingerImage) + CString(" '") + CString(szFingerFeaturesFile));

	unsigned long rc = 0l;
	HGLOBAL hFingerDIB = ImageFromDisk(szFingerImage);
	if (hFingerDIB == (HGLOBAL)-1) return FALSE;
	char *lpFingerDIB = (char *)GlobalLock(hFingerDIB);
	//BITMAPFILEHEADER*	pBMFH = (BITMAPFILEHEADER*)lpFingerDIB;
	//BITMAPINFOHEADER*	pBMIH = (BITMAPINFOHEADER*)(lpFingerDIB + sizeof(BITMAPFILEHEADER));

	//AfxMessageBox("calling AisCreateVerifyTemplate\n");
	HAIS hTemplate = AisCreateVerifyTemplate(lpFingerDIB + sizeof (BITMAPFILEHEADER), AIS_IMG_WINDOWS_BMP, &rc);
	if (rc)
	{
		AfxMessageBox(CString(AisRCToString(rc)) + " - AisCreateVerifyTemplate");
		// free the image memory
		GlobalUnlock(hFingerDIB);
		hFingerDIB = GlobalFree(hFingerDIB);
		return false;
	}

	unsigned long PackSize = 0l;
	struct ibm_minData_def ibm_minutia;

	//***********************************************************
	//* create the quality information 
	//**********************************************************
	long lMinCount = AisTemplateGetNumMinutiae(hTemplate, &rc);
	long lQuality;
	CString strQuality;

	HAIS hQualityInfo = NULL;
	hQualityInfo = AisCreateQualityInfo(lpFingerDIB + sizeof (BITMAPFILEHEADER), AIS_IMG_WINDOWS_BMP, &rc);
	if (hQualityInfo) 
	{
		lQuality = AisQualityGetGeneralValue(hQualityInfo, &rc);
		// Overall = General * ((2 * Dryness) + Smudgeness) + 300
		///*
		//DrynessValue = AisQualityGetDrynessValue(hQualityInfo, &rc);
		//SmudgenessValue = AisQualityGetSmudgenessValue(hQualityInfo, &rc);
		//OverallValue = AisQualityGetOverallValue(hQualityInfo, &rc);
		//OrientationValue = AisQualityGetOrientationValue(hQualityInfo, &rc);
		//TranslateValue = AisQualityGetTranslateValue(hQualityInfo, &rc);
		//TranslateDirection = AisQualityGetTranslateDirection(hQualityInfo, &rc);
		//RollValue = AisQualityGetRollValue(hQualityInfo, &rc);
		//RollDirection = AisQualityGetRollDirection(hQualityInfo, &rc);
		//
	}

	if (lQuality >= A_Threshold) 
		strQuality = "A";
	else if (lQuality >= B_Threshold) 
		strQuality = "B";
	else 
		strQuality = "C";
	
	// free the image memory
	GlobalUnlock(hFingerDIB);
	hFingerDIB = GlobalFree(hFingerDIB);

	if (rc)
	{
		AfxMessageBox(CString(AisRCToString(rc)) + " - AisCreateVerifyTemplate");
		return FALSE;
	}

	//***********************************************************
	//* get the size of the packed verify template
	//**********************************************************
	//AfxMessageBox("calling AisGetPackedSize\n");
	PackSize = AisGetPackedSize(hTemplate, &rc);
	if (rc)
	{
		AfxMessageBox(CString(AisRCToString(rc)) + " - AisGetPackedSize");
		return FALSE;
	}

	///***********************************************************
	//* pack the template into buffer
	//***********************************************************
	//AfxMessageBox("calling AisPack\n");
	AisPack(hTemplate, PackSize, ibm_minutia.PackedData, &rc); 
	if (rc)
	{
		AfxMessageBox(CString(AisRCToString(rc)) + " - AisPack");
		return FALSE;
	}
	ibm_minutia.ibm_minHead.nHeadSize = sizeof(struct ibm_minHead_def);
	strcpy(ibm_minutia.ibm_minHead.szQuality, (const char*)strQuality);
	ibm_minutia.ibm_minHead.lNumMinutia = (short)lMinCount;
	ibm_minutia.ibm_minHead.lQuality = (short)lQuality;
	ibm_minutia.ibm_minHead.lSize = (short)PackSize;

	///***********************************************************
	//* save the packed template to file with header
	//***********************************************************
	if (!WriteMinutiaFile((char*)(&ibm_minutia), PackSize + sizeof(struct ibm_minHead_def), szFingerFeaturesFile, instance))
	{
		AfxMessageBox(CString("Cannot write to path ") + szFingerFeaturesFile);
		return FALSE;
	}

	///***********************************************************
	//* free any memory supplied by the AIS library
	//***********************************************************
	//AfxMessageBox("freeing AIS template structures\n");
	AisFree(hTemplate, &rc);
	if (rc)
	{
		AfxMessageBox(CString(AisRCToString(rc)) + " - AisFree");
	}

	///***********************************************************
	//* free memory allocated by the application
	//***********************************************************
	// free the image memory
	GlobalUnlock(hFingerDIB);
	hFingerDIB = GlobalFree(hFingerDIB);

	return true;
}

// return score 0-100 or -1 if there is an error
long DoVerifyFeatures(char* szFingerFeaturesFile1, char* szFingerFeaturesFile2)
{
	//AfxMessageBox(CString("DoVerifyFeatures ") + CString(szFingerFeaturesFile1) + CString(" '") + CString(szFingerFeaturesFile2));

	HANDLE hFeatures1;
	HANDLE hFeatures2;
	int m_MinutiaSize = sizeof(struct ibm_minData_def);
	hFeatures1 = GlobalAlloc(GMEM_MOVEABLE | GMEM_ZEROINIT, m_MinutiaSize);
	hFeatures2 = GlobalAlloc(GMEM_MOVEABLE | GMEM_ZEROINIT, m_MinutiaSize);

	//retrieve the minutia from disk
    char* pFeatures1 = (char*)GlobalLock(hFeatures1);
    char* pFeatures2 = (char*)GlobalLock(hFeatures2);
	
	long lScore = 0;
	if (!ReadMinutiaFile(pFeatures1, m_MinutiaSize, szFingerFeaturesFile1)) return FALSE;
	if (!ReadMinutiaFile(pFeatures2, m_MinutiaSize, szFingerFeaturesFile2)) return FALSE;
	//////////////////////////

	//---------------------------------------------------------//
	// Call the 1-to-1 matching function for each finger to
	// be matched.  The result is a score for each finger.
	//---------------------------------------------------------//
	lScore = DoMinutiaMatch(pFeatures1, pFeatures2);

	GlobalUnlock(hFeatures1);
	GlobalUnlock(hFeatures2);
	GlobalFree(hFeatures1);
	GlobalFree(hFeatures2);

	return lScore;
}

// return score 0-100 or -1 if there is an error
long DoMinutiaMatch(char* pFeatures1, char* pFeatures2)
{
	long lScore = 0; 
	unsigned long rc = 0l;
    /* unpack the packed templates */
    HAIS hTemplate1 = AisUnpack(pFeatures1 + sizeof(struct ibm_minHead_def), &rc);
    if (rc)
    {
		AfxMessageBox(CString(AisRCToString(rc)) + " - AisUnpack");
		return -1;
    }
    HAIS hTemplate2 = AisUnpack(pFeatures2 + sizeof(struct ibm_minHead_def), &rc);
    if (rc)
    {
		AfxMessageBox(CString(AisRCToString(rc)) + " - AisUnpack");
		AisFree(hTemplate1, &rc);
		return -1;
    }
	/***********************************************************
	* match the verify templates
	***********************************************************/
	//AfxMesageBox("calling AisMatchTemplate \n");
	lScore = AisMatchTemplates(hTemplate1, hTemplate2, &rc);
	if (rc)
	{
		AfxMessageBox(CString(AisRCToString(rc)) + " - AisMatchTemplates");
		return -1;
	}

	/***********************************************************
	* free any memory supplied by the AIS library
	***********************************************************/
	AisFree(hTemplate1, &rc);
	if (rc)
	{
		AfxMessageBox(CString(AisRCToString(rc)) + " - AisFree");
	}

	AisFree(hTemplate2, &rc);
	if (rc)
	{
		AfxMessageBox(CString(AisRCToString(rc)) + " - AisFree");
	}
	return lScore;
}

long DoVerifyFinger(char* szFingerImage, char* szFingerFeaturesFile, long instance)
{
	//AfxMessageBox(CString("DoVerifyFinger ") + CString(szFingerImage) + CString(" '") + CString(szFingerFeaturesFile));

	int len = strlen(szFingerImage);
	static char szTemp[200];
	strncpy(szTemp, szFingerImage, len-3);
	strcat(szTemp, "BIN2");

	DoExtractFeatures(szFingerImage, szTemp, instance);

	return DoVerifyFeatures(szFingerFeaturesFile, szTemp);
}

bool DoMeasureQuality(char* szFingerImage, long* lDryness, long* lSmudginess, long* lOrientation, long* lRoll, long* lQuality)
{
	unsigned long rc = 0l;
	HGLOBAL hFingerDIB = ImageFromDisk(szFingerImage);
	if (hFingerDIB == (HGLOBAL)-1) return false;
	char *lpFingerDIB = (char *)GlobalLock(hFingerDIB);

	HAIS hTemplate = AisCreateVerifyTemplate(lpFingerDIB + sizeof (BITMAPFILEHEADER),
				  AIS_IMG_WINDOWS_BMP, &rc);
	if (rc)
	{
		AfxMessageBox(CString(AisRCToString(rc)) + " - AisCreateVerifyTemplate");
		// free the image memory
		GlobalUnlock(hFingerDIB);
		hFingerDIB = GlobalFree(hFingerDIB);
		return false;
	}

	HAIS hQualityInfo = NULL;
	hQualityInfo = AisCreateQualityInfo(lpFingerDIB + sizeof (BITMAPFILEHEADER), AIS_IMG_WINDOWS_BMP, &rc);
	// free the image memory
	GlobalUnlock(hFingerDIB);
	hFingerDIB = GlobalFree(hFingerDIB);

	if (rc)
	{
		AfxMessageBox(CString(AisRCToString(rc)) + " - AisCreateQualityInfo");
		return false;
	}

	if (hQualityInfo != NULL)
	{
		*lQuality = AisQualityGetGeneralValue(hQualityInfo, &rc);

		*lDryness = AisQualityGetDrynessValue(hQualityInfo, &rc);
		*lSmudginess = AisQualityGetSmudgenessValue(hQualityInfo, &rc);
		*lOrientation = AisQualityGetOrientationValue(hQualityInfo, &rc);
		*lRoll = AisQualityGetRollValue(hQualityInfo, &rc);
		//OverallValue = AisQualityGetOverallValue(hQualityInfo, &rc);
		//TranslateValue = AisQualityGetTranslateValue(hQualityInfo, &rc);
		//TranslateDirection = AisQualityGetTranslateDirection(hQualityInfo, &rc);
		//RollDirection = AisQualityGetRollDirection(hQualityInfo, &rc);

		// Overall = General * ((2 * Dryness) + Smudgeness) + 300

		return true;
	}
	else
	{
		return false;
	}
}

HGLOBAL ImageFromDisk(const CString imagefile)
{
	// this also should work
	// pFingImage = new CImageProc;
	// pFingImage->Open(imagefile);
	// if (!pFingImage->IsValid()) return FALSE;
	// hDIB = (HGLOBAL)pFingImage->ImageToHDIB();

	/*****************************************************************************
	 *  
	 *  synopsis:   reads the specified bitmap image
	 *
	 *  params:     imagefile - the full pathname of the file
	 *
	 *  returns:    a pointer to the bitmap image
	 */

	int fh = 0;
	long ImageSize = 0l;
	int BytesRead = 0;
	char* pImage;

	/* check that we have a bitmap file */
	if (strstr(imagefile, ".bmp") || strstr(imagefile, ".BMP"))
	{
		/* attempt to open the image file */
		fh = _open(imagefile, _O_BINARY | _O_RDONLY);
		if (fh != -1)
		{
			/* determine the file length */
			ImageSize = _lseek(fh, 0, SEEK_END);

			/* reset the file pointer to the start of the file */
			_lseek(fh, 0, SEEK_SET);

			HANDLE hBuffer = GlobalAlloc( GMEM_MOVEABLE | GMEM_DDESHARE, ImageSize);
			if (hBuffer != NULL) pImage = (char*)GlobalLock(hBuffer);
			if (pImage != NULL)
			{
				/* read the image */
				BytesRead = _read(fh, pImage, ImageSize);
				/* close the file */
				_close(fh);

				if (BytesRead == ImageSize)
				{
					/* all bitmap files contain a bitmap identifier */
					if (pImage[0] == 'B' && pImage[1] == 'M')
					{
						/* success, we have a bitmap file */
						/* Make the Pixels density is a reasonable value - revert to
						   500 dpi if the file has less than 300 dpi */
						BITMAPFILEHEADER*	pBMFH = (BITMAPFILEHEADER*)pImage;
						BITMAPINFOHEADER*	pBMIH = (BITMAPINFOHEADER*)(pImage + sizeof(BITMAPFILEHEADER));
						if (pBMIH->biXPelsPerMeter < 7000)
						{
							pBMIH->biXPelsPerMeter = 19685;
							pBMIH->biYPelsPerMeter = 19685;
						}
// ais doesn't handle the zero default for colour table size correctly, so put in an 
// explicit value. PB
						if(pBMIH->biClrUsed == 0)
						{
							pBMIH->biClrUsed = 1L << pBMIH->biBitCount;
						}
						if(pBMIH->biClrImportant == 0)
						{
							pBMIH->biClrImportant = pBMIH->biClrUsed;
						}
						GlobalUnlock(hBuffer);
						return hBuffer;
					}
					else
					{
						printf("*** ERROR ***  bad or corrupt bitmap file\n");
						/* lose the memory */
						GlobalFree(hBuffer);
					}
				}
				else
				{
					printf("*** ERROR ***  file read error - unexpected EOF\n");
					/* lose the memory */
					GlobalFree(hBuffer);
				}
			}
			else
			{
				printf("*** ERROR ***  memory allocation failure\n");
			}
		}
		else
		{
			printf("*** ERROR ***  unable to open file %s\n", imagefile);
		}
	}
	else
	{
		printf("*** ERROR ***  invalid bitmap file extension - needs 'bmp' or 'BMP'\n");
	}
	return (HGLOBAL)-1;
}

// return TRUE if read is OK
BOOL ReadMinutiaFile(char*& pBuffer, int iFeaturesSize, const CString minutiafile)
{
	int iSize;
	CFile file;
	CFileException e;
	if (!file.Open (minutiafile, CFile::modeRead, &e)) return FALSE;

	iSize = (int)file.GetLength();
	unsigned char* pText = new unsigned char[iSize];
	file.Read (pText, iSize);
	file.Close();

	// strip BIN header and convert hex to binary
	//pBuffer = new char[iFeaturesSize];
	int i;
	unsigned int num1, num2;
	unsigned char* pIn = pText+6;
	unsigned char* pOut = (unsigned char*)pBuffer;
	for (i=6; i<iSize-1; i+=2)
	{
		num1 = *pIn++;
		if (num1 >= '0' && num1 <= '9') num1 = num1 - '0';
		else num1 = num1 - 'a' + 10;
		num2 = *pIn++;
		if (num2 >= '0' && num2 <= '9') num2 = num2 - '0';
		else num2 = num2 - 'a' + 10;
		*pOut++ = (num1 * 16) + num2; 
	}
	return TRUE;
}

// return TRUE if write is OK
BOOL WriteMinutiaFile(char* pFeatures, int iFeaturesSize, const CString minutiafile, long instance)
{
	//AfxMessageBox("converting to hex");
	char* pBuffer = new char[iFeaturesSize*2 + 7];

	char s[4];
	int i;
	strcpy(pBuffer, "AS01X0");
	pBuffer[5] = (unsigned char)(instance + '0');

	for (i=0; i < iFeaturesSize; i++)
	{
		sprintf(s, "%02x", (unsigned char)pFeatures[i]);
		strcat(pBuffer, s);
	}
	strcat(pBuffer, ";");

	//AfxMessageBox(strName + CString("=converted: ") + pBuffer);
	CFile file;
	if (!file.Open (minutiafile, CFile::modeCreate | CFile::modeWrite | CFile::typeBinary)) return FALSE;
	file.Write (pBuffer, strlen(pBuffer));
	file.Close();

	return TRUE;
}
