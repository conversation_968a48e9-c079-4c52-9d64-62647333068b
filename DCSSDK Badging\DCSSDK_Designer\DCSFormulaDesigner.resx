<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="textBoxFormula.Location" type="System.Drawing.Point, System.Drawing">
    <value>182, 80</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="textBoxFormula.Multiline" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textBoxFormula.Size" type="System.Drawing.Size, System.Drawing">
    <value>419, 38</value>
  </data>
  <data name="textBoxFormula.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;textBoxFormula.Name" xml:space="preserve">
    <value>textBoxFormula</value>
  </data>
  <data name="&gt;&gt;textBoxFormula.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxFormula.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBoxFormula.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonAppendDatabaseField.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonAppendDatabaseField.Location" type="System.Drawing.Point, System.Drawing">
    <value>505, 142</value>
  </data>
  <data name="buttonAppendDatabaseField.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 40</value>
  </data>
  <data name="buttonAppendDatabaseField.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="buttonAppendDatabaseField.Text" xml:space="preserve">
    <value>Append Field</value>
  </data>
  <data name="&gt;&gt;buttonAppendDatabaseField.Name" xml:space="preserve">
    <value>buttonAppendDatabaseField</value>
  </data>
  <data name="&gt;&gt;buttonAppendDatabaseField.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAppendDatabaseField.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAppendDatabaseField.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="buttonAppendText.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonAppendText.Location" type="System.Drawing.Point, System.Drawing">
    <value>505, 188</value>
  </data>
  <data name="buttonAppendText.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 40</value>
  </data>
  <data name="buttonAppendText.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="buttonAppendText.Text" xml:space="preserve">
    <value>Append Text</value>
  </data>
  <data name="&gt;&gt;buttonAppendText.Name" xml:space="preserve">
    <value>buttonAppendText</value>
  </data>
  <data name="&gt;&gt;buttonAppendText.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAppendText.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAppendText.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="comboBoxBINToAppend.Location" type="System.Drawing.Point, System.Drawing">
    <value>184, 142</value>
  </data>
  <data name="comboBoxBINToAppend.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 21</value>
  </data>
  <data name="comboBoxBINToAppend.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;comboBoxBINToAppend.Name" xml:space="preserve">
    <value>comboBoxBINToAppend</value>
  </data>
  <data name="&gt;&gt;comboBoxBINToAppend.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxBINToAppend.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;comboBoxBINToAppend.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="buttonClear.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonClear.Location" type="System.Drawing.Point, System.Drawing">
    <value>216, 373</value>
  </data>
  <data name="buttonClear.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 24</value>
  </data>
  <data name="buttonClear.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="buttonClear.Text" xml:space="preserve">
    <value>C&amp;lear</value>
  </data>
  <data name="&gt;&gt;buttonClear.Name" xml:space="preserve">
    <value>buttonClear</value>
  </data>
  <data name="&gt;&gt;buttonClear.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonClear.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonClear.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="buttonOK.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonOK.Location" type="System.Drawing.Point, System.Drawing">
    <value>344, 373</value>
  </data>
  <data name="buttonOK.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 24</value>
  </data>
  <data name="buttonOK.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="buttonOK.Text" xml:space="preserve">
    <value>&amp;OK</value>
  </data>
  <data name="&gt;&gt;buttonOK.Name" xml:space="preserve">
    <value>buttonOK</value>
  </data>
  <data name="&gt;&gt;buttonOK.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonOK.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonOK.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="buttonCancel.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>472, 373</value>
  </data>
  <data name="buttonCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 24</value>
  </data>
  <data name="buttonCancel.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="buttonCancel.Text" xml:space="preserve">
    <value>&amp;Cancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Name" xml:space="preserve">
    <value>buttonCancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonCancel.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="buttonUndo.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonUndo.Location" type="System.Drawing.Point, System.Drawing">
    <value>88, 373</value>
  </data>
  <data name="buttonUndo.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 24</value>
  </data>
  <data name="buttonUndo.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="buttonUndo.Text" xml:space="preserve">
    <value>&amp;Undo</value>
  </data>
  <data name="&gt;&gt;buttonUndo.Name" xml:space="preserve">
    <value>buttonUndo</value>
  </data>
  <data name="&gt;&gt;buttonUndo.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonUndo.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonUndo.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="textBoxBINDescription.Location" type="System.Drawing.Point, System.Drawing">
    <value>153, 178</value>
  </data>
  <data name="textBoxBINDescription.Multiline" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textBoxBINDescription.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 64</value>
  </data>
  <data name="textBoxBINDescription.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="textBoxBINDescription.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;textBoxBINDescription.Name" xml:space="preserve">
    <value>textBoxBINDescription</value>
  </data>
  <data name="&gt;&gt;textBoxBINDescription.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxBINDescription.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBoxBINDescription.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="radioButtonBiometricExpression.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButtonBiometricExpression.Location" type="System.Drawing.Point, System.Drawing">
    <value>46, 146</value>
  </data>
  <data name="radioButtonBiometricExpression.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 17</value>
  </data>
  <data name="radioButtonBiometricExpression.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="radioButtonBiometricExpression.Text" xml:space="preserve">
    <value>Biometric Function</value>
  </data>
  <data name="&gt;&gt;radioButtonBiometricExpression.Name" xml:space="preserve">
    <value>radioButtonBiometricExpression</value>
  </data>
  <data name="&gt;&gt;radioButtonBiometricExpression.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButtonBiometricExpression.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;radioButtonBiometricExpression.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="radioButtonFormula.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButtonFormula.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="radioButtonFormula.Location" type="System.Drawing.Point, System.Drawing">
    <value>46, 83</value>
  </data>
  <data name="radioButtonFormula.Size" type="System.Drawing.Size, System.Drawing">
    <value>116, 17</value>
  </data>
  <data name="radioButtonFormula.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="radioButtonFormula.Text" xml:space="preserve">
    <value>Formula Expression</value>
  </data>
  <data name="&gt;&gt;radioButtonFormula.Name" xml:space="preserve">
    <value>radioButtonFormula</value>
  </data>
  <data name="&gt;&gt;radioButtonFormula.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButtonFormula.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;radioButtonFormula.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="buttonAppendFunction.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonAppendFunction.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonAppendFunction.Location" type="System.Drawing.Point, System.Drawing">
    <value>505, 234</value>
  </data>
  <data name="buttonAppendFunction.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 40</value>
  </data>
  <data name="buttonAppendFunction.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="buttonAppendFunction.Text" xml:space="preserve">
    <value>Append Function</value>
  </data>
  <data name="&gt;&gt;buttonAppendFunction.Name" xml:space="preserve">
    <value>buttonAppendFunction</value>
  </data>
  <data name="&gt;&gt;buttonAppendFunction.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAppendFunction.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAppendFunction.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="buttonInsertFunction.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonInsertFunction.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonInsertFunction.Location" type="System.Drawing.Point, System.Drawing">
    <value>386, 234</value>
  </data>
  <data name="buttonInsertFunction.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 40</value>
  </data>
  <data name="buttonInsertFunction.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="buttonInsertFunction.Text" xml:space="preserve">
    <value>Insert Function</value>
  </data>
  <data name="&gt;&gt;buttonInsertFunction.Name" xml:space="preserve">
    <value>buttonInsertFunction</value>
  </data>
  <data name="&gt;&gt;buttonInsertFunction.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonInsertFunction.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonInsertFunction.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="buttonInsertText.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonInsertText.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonInsertText.Location" type="System.Drawing.Point, System.Drawing">
    <value>386, 188</value>
  </data>
  <data name="buttonInsertText.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 40</value>
  </data>
  <data name="buttonInsertText.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="buttonInsertText.Text" xml:space="preserve">
    <value>Insert Text</value>
  </data>
  <data name="&gt;&gt;buttonInsertText.Name" xml:space="preserve">
    <value>buttonInsertText</value>
  </data>
  <data name="&gt;&gt;buttonInsertText.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonInsertText.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonInsertText.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="buttonInsertDatabaseField.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonInsertDatabaseField.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonInsertDatabaseField.Location" type="System.Drawing.Point, System.Drawing">
    <value>386, 142</value>
  </data>
  <data name="buttonInsertDatabaseField.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 40</value>
  </data>
  <data name="buttonInsertDatabaseField.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="buttonInsertDatabaseField.Text" xml:space="preserve">
    <value>Insert Field</value>
  </data>
  <data name="&gt;&gt;buttonInsertDatabaseField.Name" xml:space="preserve">
    <value>buttonInsertDatabaseField</value>
  </data>
  <data name="&gt;&gt;buttonInsertDatabaseField.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonInsertDatabaseField.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonInsertDatabaseField.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleBaseSize" type="System.Drawing.Size, System.Drawing">
    <value>5, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>634, 448</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterParent</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>DCSFormulaDesigner</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>DCSFormulaDesigner</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>