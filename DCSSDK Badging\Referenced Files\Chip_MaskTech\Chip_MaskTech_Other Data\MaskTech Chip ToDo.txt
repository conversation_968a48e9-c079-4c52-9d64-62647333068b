
icao chip encode type takes badge.dat
    make icao mrz lines - put them into file
    locate photo file
    locate 2 available finger files

problems:
    need a way to indicate which finger
    test phase will only have 16K chip - target is 64K
    need to ask MaskTech to put the file system on the chip
    super high compression of finger wsq might have problems
    arabic text is not available - DF11
    the program responds very badly to errors
        messages rarely tell you whats wrong - eg no directory
        get no status file if there is no device
    Chip encoder cannot accepts JPG files and creates JPG files that many pgms cannot read.
    Chip encoder cannot accept wsq unless size is already OK
    My code cannot display the images read from the chip if they are jp2 and wsq
    IAS JP2 viewer form [http://hirise.lpl.arizona.edu/jp2.php] reports the jp2 file generated has improper header

ToDo:
    build MRZ - done
    put image file names in directory file - done
    read chip - done
    test write with some files missing from directory file
    ** get and report errors vs ok
    test 3 modes
    test finger choices
    add command to display ICAO results scanned - needs refinement
    ** add application calls to scan chip in verify form and in document functions

Paul To<PERSON>o
    change names of config and status.txt - done
    make contents of status.txt a simple OK if it works - done

    make it encode jpg
    better error messages
    When I run encode chip program in a batch file (as I do) and send output to a file, some remains.

WSQ support
http://www.cognaxon.com/index.php?page=wsqlibrary

Price of WSQ image library
WSQ image library (for Windows) costs 253.00 U.S. dollars for the first licence (single developer licence) and 19.00 U.S. dollars per every additional licence (client computer), which is the lowest price on the market.
WSQ image library (for Windows) runtime free / royalty free license costs 4000.00 U.S. dollars.



