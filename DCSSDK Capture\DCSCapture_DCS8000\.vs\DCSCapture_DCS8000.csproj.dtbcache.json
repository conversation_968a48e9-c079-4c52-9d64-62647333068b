{"RootPath": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSCapture_DCS8000", "ProjectFileName": "DCSCapture_DCS8000.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "AboutDCS8000.cs"}, {"SourceFile": "AssemblyInfo.cs"}, {"SourceFile": "DCS8000Main.cs"}, {"SourceFile": "DCS8000Properties.cs"}, {"SourceFile": "ValCamInterface.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSSDK_Finisher\\bin\\Debug\\DCSSDK_Finisher.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSSDK_Finisher\\bin\\Debug\\DCSSDK_Finisher.dll"}, {"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSSDK_FinisherProperties\\bin\\Debug\\DCSSDK_FinisherProperties.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSSDK_FinisherProperties\\bin\\Debug\\DCSSDK_FinisherProperties.dll"}, {"Reference": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSSDK_Utilities\\bin\\Debug\\DCSSDK_Utilities.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSSDK_Utilities\\bin\\Debug\\DCSSDK_Utilities.dll"}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "D:\\repos_D\\SDS Collection\\DCSSDK Capture\\DCSCapture_DCS8000\\bin\\Debug\\DCSCapture_DCS8000.dll", "OutputItemRelativePath": "DCSCapture_DCS8000.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}