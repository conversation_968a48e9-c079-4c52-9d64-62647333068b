using System;
using System.Collections;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Windows.Forms;

using DCSDEV;
namespace DCSDEV.DCSDesigner
{
	/// <summary>
	/// Summary description for DCSSourceProperties.
	/// </summary>
    public class DCSSourceProperties : System.Windows.Forms.UserControl
	{
		ArrayList m_AllDBFieldNames;

		private DCSDEV.DCSDatatypes.DCSDesignObjectTypes m_designObjectType;
		private bool m_bTextType;
		private bool m_bImageType;
		private bool m_bCaptureType;
		private string m_strText = "text";
		private string m_strFilename;
		private Rectangle m_rectOriginalPicture;
        private int m_iLastSourceType;

		private System.Windows.Forms.TextBox tbText;
		private System.Windows.Forms.ComboBox cbDBField;
		private System.Windows.Forms.Label labelDBField;
		private System.Windows.Forms.ComboBox cbSourceType;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.Label labelStatic;
		private System.Windows.Forms.Button buttonChooseFile;
		private System.Windows.Forms.Label labelFormula;
		private System.Windows.Forms.Button buttonEditFormula;
		private System.Windows.Forms.TextBox tbObjectType;
		private System.Windows.Forms.TextBox tbFormula;
		private System.Windows.Forms.PictureBox pictureBox1;
		private System.Windows.Forms.Label labelSourceType;
		private System.Windows.Forms.NumericUpDown numericUpDownInstance;
		private CheckBox checkBoxVisibleIf;
		private Button buttonEditVisibleIf;
		private TextBox textBoxVisibleIf;
		/// <summary> 
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		public DCSSourceProperties()
		{
			// This call is required by the Windows.Forms Form Designer.
			InitializeComponent();

			// TODO: Add any initialization after the InitializeComponent call
			m_AllDBFieldNames = new ArrayList();

			m_rectOriginalPicture = this.pictureBox1.Bounds;
            this.checkBoxVisibleIf.Enabled = DCSLicensing.IsLicensedOK(LicensedFeatures.ConditionalsOrFormulas, false);
			this.AdjustVisibilities();
		}

		/// <summary> 
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if (pictureBox1.Image != null)
				{
					pictureBox1.Image.Dispose();
					pictureBox1.Image = null;
				}
				if (components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Component Designer generated code
		/// <summary> 
		/// Required method for Designer support - do not modify 
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			this.tbText = new System.Windows.Forms.TextBox();
			this.cbDBField = new System.Windows.Forms.ComboBox();
			this.labelDBField = new System.Windows.Forms.Label();
			this.labelStatic = new System.Windows.Forms.Label();
			this.cbSourceType = new System.Windows.Forms.ComboBox();
			this.labelSourceType = new System.Windows.Forms.Label();
			this.tbObjectType = new System.Windows.Forms.TextBox();
			this.label2 = new System.Windows.Forms.Label();
			this.buttonChooseFile = new System.Windows.Forms.Button();
			this.labelFormula = new System.Windows.Forms.Label();
			this.tbFormula = new System.Windows.Forms.TextBox();
			this.buttonEditFormula = new System.Windows.Forms.Button();
			this.pictureBox1 = new System.Windows.Forms.PictureBox();
			this.numericUpDownInstance = new System.Windows.Forms.NumericUpDown();
			this.checkBoxVisibleIf = new System.Windows.Forms.CheckBox();
			this.buttonEditVisibleIf = new System.Windows.Forms.Button();
			this.textBoxVisibleIf = new System.Windows.Forms.TextBox();
			((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
			((System.ComponentModel.ISupportInitialize)(this.numericUpDownInstance)).BeginInit();
			this.SuspendLayout();
			// 
			// tbText
			// 
			this.tbText.Location = new System.Drawing.Point(96, 58);
			this.tbText.Name = "tbText";
			this.tbText.Size = new System.Drawing.Size(200, 20);
			this.tbText.TabIndex = 3;
			this.tbText.Text = "text";
			this.tbText.TextChanged += new System.EventHandler(this.tbText_TextChanged);
			// 
			// cbDBField
			// 
			this.cbDBField.Items.AddRange(new object[] {
            "LastName",
            "FirstName"});
			this.cbDBField.Location = new System.Drawing.Point(96, 82);
			this.cbDBField.Name = "cbDBField";
			this.cbDBField.Size = new System.Drawing.Size(136, 21);
			this.cbDBField.TabIndex = 5;
			this.cbDBField.Text = "select database field";
			// 
			// labelDBField
			// 
			this.labelDBField.Location = new System.Drawing.Point(8, 84);
			this.labelDBField.Name = "labelDBField";
			this.labelDBField.Size = new System.Drawing.Size(80, 16);
			this.labelDBField.TabIndex = 45;
			this.labelDBField.Text = "Database Field";
			// 
			// labelStatic
			// 
			this.labelStatic.Location = new System.Drawing.Point(8, 58);
			this.labelStatic.Name = "labelStatic";
			this.labelStatic.Size = new System.Drawing.Size(80, 16);
			this.labelStatic.TabIndex = 44;
			this.labelStatic.Text = "Value";
			// 
			// cbSourceType
			// 
			this.cbSourceType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.cbSourceType.Items.AddRange(new object[] {
            "Static value",
            "Database",
            "Formula"});
			this.cbSourceType.Location = new System.Drawing.Point(96, 34);
			this.cbSourceType.Name = "cbSourceType";
			this.cbSourceType.Size = new System.Drawing.Size(104, 21);
			this.cbSourceType.TabIndex = 2;
			this.cbSourceType.SelectedIndexChanged += new System.EventHandler(this.cbSourceType_SelectedIndexChanged);
			// 
			// labelSourceType
			// 
			this.labelSourceType.Location = new System.Drawing.Point(8, 34);
			this.labelSourceType.Name = "labelSourceType";
			this.labelSourceType.Size = new System.Drawing.Size(80, 16);
			this.labelSourceType.TabIndex = 41;
			this.labelSourceType.Text = "Source Type";
			// 
			// tbObjectType
			// 
			this.tbObjectType.Location = new System.Drawing.Point(96, 8);
			this.tbObjectType.Name = "tbObjectType";
			this.tbObjectType.ReadOnly = true;
			this.tbObjectType.Size = new System.Drawing.Size(80, 20);
			this.tbObjectType.TabIndex = 0;
			this.tbObjectType.Text = "unknown";
			// 
			// label2
			// 
			this.label2.Location = new System.Drawing.Point(8, 8);
			this.label2.Name = "label2";
			this.label2.Size = new System.Drawing.Size(80, 16);
			this.label2.TabIndex = 51;
			this.label2.Text = "Object type";
			// 
			// buttonChooseFile
			// 
			this.buttonChooseFile.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonChooseFile.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.buttonChooseFile.Location = new System.Drawing.Point(304, 58);
			this.buttonChooseFile.Name = "buttonChooseFile";
			this.buttonChooseFile.Size = new System.Drawing.Size(20, 20);
			this.buttonChooseFile.TabIndex = 4;
			this.buttonChooseFile.Text = ">";
			this.buttonChooseFile.Click += new System.EventHandler(this.buttonChooseFile_Click);
			// 
			// labelFormula
			// 
			this.labelFormula.Location = new System.Drawing.Point(8, 93);
			this.labelFormula.Name = "labelFormula";
			this.labelFormula.Size = new System.Drawing.Size(80, 16);
			this.labelFormula.TabIndex = 53;
			this.labelFormula.Text = "Formula";
			// 
			// tbFormula
			// 
			this.tbFormula.Location = new System.Drawing.Point(96, 93);
			this.tbFormula.Name = "tbFormula";
			this.tbFormula.Size = new System.Drawing.Size(200, 20);
			this.tbFormula.TabIndex = 6;
			this.tbFormula.Text = "specify formula";
			// 
			// buttonEditFormula
			// 
			this.buttonEditFormula.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonEditFormula.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.buttonEditFormula.Location = new System.Drawing.Point(304, 93);
			this.buttonEditFormula.Name = "buttonEditFormula";
			this.buttonEditFormula.Size = new System.Drawing.Size(20, 20);
			this.buttonEditFormula.TabIndex = 7;
			this.buttonEditFormula.Text = ">";
			this.buttonEditFormula.Click += new System.EventHandler(this.buttonEditFormula_Click);
			// 
			// pictureBox1
			// 
			this.pictureBox1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
						| System.Windows.Forms.AnchorStyles.Right)));
			this.pictureBox1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
			this.pictureBox1.Location = new System.Drawing.Point(224, 0);
			this.pictureBox1.Name = "pictureBox1";
			this.pictureBox1.Size = new System.Drawing.Size(96, 55);
			this.pictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
			this.pictureBox1.TabIndex = 56;
			this.pictureBox1.TabStop = false;
			// 
			// numericUpDownInstance
			// 
			this.numericUpDownInstance.Location = new System.Drawing.Point(184, 8);
			this.numericUpDownInstance.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
			this.numericUpDownInstance.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
			this.numericUpDownInstance.Name = "numericUpDownInstance";
			this.numericUpDownInstance.Size = new System.Drawing.Size(32, 20);
			this.numericUpDownInstance.TabIndex = 1;
			this.numericUpDownInstance.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
			this.numericUpDownInstance.Visible = false;
			// 
			// checkBoxVisibleIf
			// 
			this.checkBoxVisibleIf.AutoSize = true;
			this.checkBoxVisibleIf.Location = new System.Drawing.Point(8, 119);
			this.checkBoxVisibleIf.Name = "checkBoxVisibleIf";
			this.checkBoxVisibleIf.Size = new System.Drawing.Size(77, 17);
			this.checkBoxVisibleIf.TabIndex = 57;
			this.checkBoxVisibleIf.Text = "Visible If ...";
			this.checkBoxVisibleIf.UseVisualStyleBackColor = true;
			this.checkBoxVisibleIf.CheckedChanged += new System.EventHandler(this.checkBoxVisibleIf_CheckedChanged);
			// 
			// buttonEditVisibleIf
			// 
			this.buttonEditVisibleIf.FlatStyle = System.Windows.Forms.FlatStyle.System;
			this.buttonEditVisibleIf.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.buttonEditVisibleIf.Location = new System.Drawing.Point(304, 119);
			this.buttonEditVisibleIf.Name = "buttonEditVisibleIf";
			this.buttonEditVisibleIf.Size = new System.Drawing.Size(20, 20);
			this.buttonEditVisibleIf.TabIndex = 59;
			this.buttonEditVisibleIf.Text = ">";
			this.buttonEditVisibleIf.Click += new System.EventHandler(this.buttonEditVisibleIf_Click);
			// 
			// textBoxVisibleIf
			// 
			this.textBoxVisibleIf.Location = new System.Drawing.Point(96, 119);
			this.textBoxVisibleIf.Name = "textBoxVisibleIf";
			this.textBoxVisibleIf.Size = new System.Drawing.Size(200, 20);
			this.textBoxVisibleIf.TabIndex = 58;
			this.textBoxVisibleIf.Text = "specify condition";
			// 
			// DCSSourceProperties
			// 
			this.Controls.Add(this.buttonEditVisibleIf);
			this.Controls.Add(this.textBoxVisibleIf);
			this.Controls.Add(this.checkBoxVisibleIf);
			this.Controls.Add(this.numericUpDownInstance);
			this.Controls.Add(this.pictureBox1);
			this.Controls.Add(this.buttonEditFormula);
			this.Controls.Add(this.tbFormula);
			this.Controls.Add(this.labelFormula);
			this.Controls.Add(this.buttonChooseFile);
			this.Controls.Add(this.label2);
			this.Controls.Add(this.tbObjectType);
			this.Controls.Add(this.tbText);
			this.Controls.Add(this.cbDBField);
			this.Controls.Add(this.labelDBField);
			this.Controls.Add(this.labelStatic);
			this.Controls.Add(this.cbSourceType);
			this.Controls.Add(this.labelSourceType);
			this.Name = "DCSSourceProperties";
			this.Size = new System.Drawing.Size(328, 144);
			this.VisibleChanged += new System.EventHandler(this.DCSSourceProperties_VisibleChanged);
			((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
			((System.ComponentModel.ISupportInitialize)(this.numericUpDownInstance)).EndInit();
			this.ResumeLayout(false);
			this.PerformLayout();

		}
		#endregion

		private void AdjustVisibilities()
		{
			bool visible;
			if (m_bCaptureType) 
			{
				this.labelSourceType.Visible = false;
				this.cbSourceType.Visible = false;
				this.cbSourceType.SelectedIndex = m_iLastSourceType = (int)DCSDEV.DCSDatatypes.SourceTypes.Database;

			}

			// static text box or filename
			if (this.cbSourceType.SelectedIndex == (int)DCSDEV.DCSDatatypes.SourceTypes.StaticValue)
				this.labelStatic.Text = "Value";
			else 
				this.labelStatic.Text = "Sample";

			this.tbText.Visible = true;
			if (m_bImageType || m_bCaptureType)
			{
				this.tbText.Text = m_strFilename;
			}
			else if (m_bTextType)
			{
				this.tbText.Text = m_strText;
			}
			this.buttonChooseFile.Visible = m_bImageType || m_bCaptureType;

			// image
			if (m_bImageType || m_bCaptureType)
			{
				this.pictureBox1.Visible = true;		// && pictureBox1.Image != null;
				this.pictureBox1.Image = DCSDEV.DCSDesignDataAccess.GetImage(m_strFilename, true);
				if (this.pictureBox1.Image != null)
					this.pictureBox1.Bounds = DCSMath.GetBiggestInnerRect(this.pictureBox1.Image.Size, m_rectOriginalPicture);
			}
			else this.pictureBox1.Visible = false;

			// DB Field
			visible = (!m_bCaptureType && (this.cbSourceType.SelectedIndex == (int)DCSDEV.DCSDatatypes.SourceTypes.Database || this.cbSourceType.SelectedIndex == -1));
			this.labelDBField.Visible = visible;
			this.cbDBField.Visible = visible;

			// formula
			visible = (this.cbSourceType.SelectedIndex == (int)DCSDEV.DCSDatatypes.SourceTypes.Formula || this.cbSourceType.SelectedIndex == -1);
			this.labelFormula.Visible = visible;
			this.tbFormula.Visible = visible;
			this.buttonEditFormula.Visible = visible;

			// VisibleIf
			visible = this.checkBoxVisibleIf.Checked;
			this.textBoxVisibleIf.Visible = visible;
			this.buttonEditVisibleIf.Visible = visible;
		}

		private void cbSourceType_SelectedIndexChanged(object sender, System.EventArgs e)
		{
            if (cbSourceType.SelectedIndex == (int)DCSDEV.DCSDatatypes.SourceTypes.Formula)
            {
                if (!DCSLicensing.IsLicensedOK(LicensedFeatures.ConditionalsOrFormulas, true))
                {
                    cbSourceType.SelectedIndex = m_iLastSourceType;
                    return;
                }
            }
            this.tbText.Text = "";		// you lose your sample text if you change SourceType
			this.AdjustVisibilities();
		}

		private void buttonChooseFile_Click(object sender, System.EventArgs e)
		{
			string strName = DCSDEV.DCSDesignDataAccess.SelectOpenImageName("");
			if (strName == null) return;

			if (m_strFilename != strName && pictureBox1.Image != null) 
			{
				pictureBox1.Image.Dispose();
				pictureBox1.Image = null;
			}
			m_strFilename = strName;
			this.pictureBox1.Image = DCSDEV.DCSDesignDataAccess.GetImage(strName, false);
			if (this.pictureBox1.Image != null)
				this.pictureBox1.Bounds = DCSMath.GetBiggestInnerRect(this.pictureBox1.Image.Size, m_rectOriginalPicture);
			this.AdjustVisibilities();
		}

		private void buttonEditFormula_Click(object sender, System.EventArgs e)
		{
			string strFormula = this.tbFormula.Text;
			DCSDEV.DCSDesigner.DCSFormulaDesigner.CallFormulaBuilder(m_AllDBFieldNames, ref strFormula, DCSFormulaDesigner.FormulaModeType.ALL_MODES);
			if (strFormula != null) this.tbFormula.Text = strFormula;
		}

		private void buttonEditVisibleIf_Click(object sender, EventArgs e)
		{
			string strCondition = this.textBoxVisibleIf.Text;
			DCSDEV.DCSDesigner.DCSFormulaDesigner.CallFormulaBuilder(m_AllDBFieldNames, ref strCondition, DCSFormulaDesigner.FormulaModeType.SQL_IF);
			if (strCondition != null) this.textBoxVisibleIf.Text = strCondition;
		}

		private void DCSSourceProperties_VisibleChanged(object sender, System.EventArgs e)
		{
			this.AdjustVisibilities();
		}

		private void tbText_TextChanged(object sender, System.EventArgs e)
		{
			if (m_bImageType || m_bCaptureType)
			{
				m_strFilename = this.tbText.Text;
			}
			else
			{
				m_strText = this.tbText.Text;
			}
		}

		public DCSDEV.DCSDatatypes.DCSDesignObjectTypes DesignObjectType
		{
			get
			{
				return m_designObjectType;
			}
			set
			{
				m_designObjectType = value;
				this.tbObjectType.Text = value.ToString();
				if (m_designObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.TextObj
					|| m_designObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode2D
					|| m_designObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode) 
					m_bTextType = true;
				else m_bTextType = false;

				if (m_designObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Portrait
					|| m_designObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Signature
					|| m_designObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Fingerprint) m_bCaptureType = true;
				else m_bCaptureType = false;

				if (m_designObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ImageObj) m_bImageType = true;
				else m_bImageType = false;

				this.numericUpDownInstance.Visible = m_bCaptureType;
			}
		}
		public int SourceType
		{
			get { return this.cbSourceType.SelectedIndex; }
			set
			{
                if (value == (int)DCSDEV.DCSDatatypes.SourceTypes.Formula && !DCSLicensing.IsLicensedOK(LicensedFeatures.ConditionalsOrFormulas, false)) return;
                this.cbSourceType.SelectedIndex = m_iLastSourceType = value;
			}
		}
		public string StaticText
		{
			get { return m_strText; }
			set { m_strText = value; }
		}
		public string ForegroundImageName
		{
			get { return m_strFilename; }
			set { m_strFilename = value; }
		}
		public string DBField
		{
			get { return this.cbDBField.Text; }
			set { this.cbDBField.Text = value; }
		}
		public string Formula
		{
			get { return this.tbFormula.Text; }
			set { this.tbFormula.Text = value; }
		}
		public bool VisibleIf
		{
			get { return this.checkBoxVisibleIf.Checked; }
			set { if (this.checkBoxVisibleIf.Enabled) this.checkBoxVisibleIf.Checked = value; }
		}
		public string VisibleIfCondition
		{
			get 
			{
				if (this.checkBoxVisibleIf.Checked)
					return this.textBoxVisibleIf.Text;
				else
					return null;
			}
			set { this.textBoxVisibleIf.Text = value; }
		}
		public ArrayList AllDBFieldNames
		{
			set
			{
				this.cbDBField.Items.Clear();
				foreach(string str in value)
					this.cbDBField.Items.Add(str);

				foreach(string str in value) m_AllDBFieldNames.Add(str);
			}
		}
		public int Instance
		{
			// Work with zero based value - display 1 based.
			get { return (int)this.numericUpDownInstance.Value - 1; }
			set { this.numericUpDownInstance.Value = value + 1; }
		}
		public int MaxInstances
		{
			get { return (int)this.numericUpDownInstance.Maximum; }
			set 
			{
				this.numericUpDownInstance.Visible = (value > 1); 
				this.numericUpDownInstance.Maximum = value; 
			}
		}

		private void checkBoxVisibleIf_CheckedChanged(object sender, EventArgs e)
		{
			this.AdjustVisibilities();
		}
	}
}
