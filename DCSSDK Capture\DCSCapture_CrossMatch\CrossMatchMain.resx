<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonAcquire.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="buttonAcquire.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="buttonAcquire.Location" type="System.Drawing.Point, System.Drawing">
    <value>496, 376</value>
  </data>
  <data name="buttonAcquire.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonAcquire.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="buttonAcquire.Text" xml:space="preserve">
    <value>O&amp;K</value>
  </data>
  <data name="&gt;&gt;buttonAcquire.Name" xml:space="preserve">
    <value>buttonAcquire</value>
  </data>
  <data name="&gt;&gt;buttonAcquire.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAcquire.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAcquire.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="buttonClose.AccessibleDescription" xml:space="preserve">
    <value>shut down camera and exit</value>
  </data>
  <data name="buttonClose.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="buttonClose.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonClose.Location" type="System.Drawing.Point, System.Drawing">
    <value>496, 408</value>
  </data>
  <data name="buttonClose.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <data name="buttonClose.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="buttonClose.Text" xml:space="preserve">
    <value>&amp;Cancel</value>
  </data>
  <data name="&gt;&gt;buttonClose.Name" xml:space="preserve">
    <value>buttonClose</value>
  </data>
  <data name="&gt;&gt;buttonClose.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonClose.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonClose.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="tbCameraStatus.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 12pt, style=Bold</value>
  </data>
  <data name="tbCameraStatus.Location" type="System.Drawing.Point, System.Drawing">
    <value>160, 8</value>
  </data>
  <data name="tbCameraStatus.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 19</value>
  </data>
  <data name="tbCameraStatus.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="tbCameraStatus.Text" xml:space="preserve">
    <value>Uninitialized</value>
  </data>
  <data name="&gt;&gt;tbCameraStatus.Name" xml:space="preserve">
    <value>tbCameraStatus</value>
  </data>
  <data name="&gt;&gt;tbCameraStatus.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbCameraStatus.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbCameraStatus.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="label4.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 12pt</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 8</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 16</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>Device status</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="buttonAbout.AccessibleDescription" xml:space="preserve">
    <value>shut down camera and exit</value>
  </data>
  <data name="buttonAbout.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="buttonAbout.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonAbout.Location" type="System.Drawing.Point, System.Drawing">
    <value>496, 344</value>
  </data>
  <data name="buttonAbout.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <data name="buttonAbout.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="buttonAbout.Text" xml:space="preserve">
    <value>&amp;About</value>
  </data>
  <data name="&gt;&gt;buttonAbout.Name" xml:space="preserve">
    <value>buttonAbout</value>
  </data>
  <data name="&gt;&gt;buttonAbout.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAbout.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAbout.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="pictureBox1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="pictureBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>24, 48</value>
  </data>
  <data name="pictureBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>456, 368</value>
  </data>
  <data name="pictureBox1.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="&gt;&gt;pictureBox1.Name" xml:space="preserve">
    <value>pictureBox1</value>
  </data>
  <data name="&gt;&gt;pictureBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pictureBox1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pictureBox1.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="buttonReset.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="buttonReset.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonReset.Location" type="System.Drawing.Point, System.Drawing">
    <value>496, 312</value>
  </data>
  <data name="buttonReset.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <data name="buttonReset.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="buttonReset.Text" xml:space="preserve">
    <value>&amp;Reset</value>
  </data>
  <data name="&gt;&gt;buttonReset.Name" xml:space="preserve">
    <value>buttonReset</value>
  </data>
  <data name="&gt;&gt;buttonReset.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonReset.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonReset.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="labelFing8.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 12pt, style=Bold</value>
  </data>
  <data name="labelFing8.Location" type="System.Drawing.Point, System.Drawing">
    <value>504, 176</value>
  </data>
  <data name="labelFing8.Size" type="System.Drawing.Size, System.Drawing">
    <value>16, 16</value>
  </data>
  <data name="labelFing8.TabIndex" type="System.Int32, mscorlib">
    <value>36</value>
  </data>
  <data name="labelFing8.Text" xml:space="preserve">
    <value>x</value>
  </data>
  <data name="labelFing8.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="labelFing8.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelFing8.Name" xml:space="preserve">
    <value>labelFing8</value>
  </data>
  <data name="&gt;&gt;labelFing8.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelFing8.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelFing8.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="labelFing1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 12pt, style=Bold</value>
  </data>
  <data name="labelFing1.Location" type="System.Drawing.Point, System.Drawing">
    <value>560, 40</value>
  </data>
  <data name="labelFing1.Size" type="System.Drawing.Size, System.Drawing">
    <value>16, 16</value>
  </data>
  <data name="labelFing1.TabIndex" type="System.Int32, mscorlib">
    <value>39</value>
  </data>
  <data name="labelFing1.Text" xml:space="preserve">
    <value>x</value>
  </data>
  <data name="labelFing1.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="labelFing1.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelFing1.Name" xml:space="preserve">
    <value>labelFing1</value>
  </data>
  <data name="&gt;&gt;labelFing1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelFing1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelFing1.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="labelFing5.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 12pt, style=Bold</value>
  </data>
  <data name="labelFing5.Location" type="System.Drawing.Point, System.Drawing">
    <value>560, 160</value>
  </data>
  <data name="labelFing5.Size" type="System.Drawing.Size, System.Drawing">
    <value>16, 16</value>
  </data>
  <data name="labelFing5.TabIndex" type="System.Int32, mscorlib">
    <value>33</value>
  </data>
  <data name="labelFing5.Text" xml:space="preserve">
    <value>x</value>
  </data>
  <data name="labelFing5.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="labelFing5.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelFing5.Name" xml:space="preserve">
    <value>labelFing5</value>
  </data>
  <data name="&gt;&gt;labelFing5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelFing5.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelFing5.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="labelFing0.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 12pt, style=Bold</value>
  </data>
  <data name="labelFing0.Location" type="System.Drawing.Point, System.Drawing">
    <value>536, 48</value>
  </data>
  <data name="labelFing0.Size" type="System.Drawing.Size, System.Drawing">
    <value>16, 16</value>
  </data>
  <data name="labelFing0.TabIndex" type="System.Int32, mscorlib">
    <value>38</value>
  </data>
  <data name="labelFing0.Text" xml:space="preserve">
    <value>x</value>
  </data>
  <data name="labelFing0.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="labelFing0.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelFing0.Name" xml:space="preserve">
    <value>labelFing0</value>
  </data>
  <data name="&gt;&gt;labelFing0.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelFing0.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelFing0.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="labelFing4.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 12pt, style=Bold</value>
  </data>
  <data name="labelFing4.Location" type="System.Drawing.Point, System.Drawing">
    <value>496, 96</value>
  </data>
  <data name="labelFing4.Size" type="System.Drawing.Size, System.Drawing">
    <value>16, 16</value>
  </data>
  <data name="labelFing4.TabIndex" type="System.Int32, mscorlib">
    <value>42</value>
  </data>
  <data name="labelFing4.Text" xml:space="preserve">
    <value>x</value>
  </data>
  <data name="labelFing4.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="labelFing4.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelFing4.Name" xml:space="preserve">
    <value>labelFing4</value>
  </data>
  <data name="&gt;&gt;labelFing4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelFing4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelFing4.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelFing9.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 12pt, style=Bold</value>
  </data>
  <data name="labelFing9.Location" type="System.Drawing.Point, System.Drawing">
    <value>600, 208</value>
  </data>
  <data name="labelFing9.Size" type="System.Drawing.Size, System.Drawing">
    <value>16, 16</value>
  </data>
  <data name="labelFing9.TabIndex" type="System.Int32, mscorlib">
    <value>37</value>
  </data>
  <data name="labelFing9.Text" xml:space="preserve">
    <value>x</value>
  </data>
  <data name="labelFing9.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="labelFing9.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelFing9.Name" xml:space="preserve">
    <value>labelFing9</value>
  </data>
  <data name="&gt;&gt;labelFing9.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelFing9.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelFing9.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="pbLeftHand.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        /9j/4AAQSkZJRgABAQEAUQBRAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYa
        HSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgo
        KCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAFGAa8DASIAAhEBAxEB/8QA
        HwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIh
        MUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVW
        V1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXG
        x8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQF
        BgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAV
        YnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOE
        hYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq
        8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD6oFFFFABRRRQAUUUUAFFFFABRRRQAtFJSigAooooAKKKKACii
        igAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKAA0YoooAKTFLSGgAoooo
        AKKKKACiiigAooooAKKKKACkpaKACiiigAooooAKKKKACiilxQAlFFFAC0UCigAooooAKKKKACiiigAo
        rndX8Srbzta6bA15dDhtv3E+pHWsKfV/ETHezLAp7RxqR+uaAO/orgoPEuq2ZU3axzxk85UKfzHH6V1u
        j6tb6pDvgJDj7yHqv/**************************************************************
        lpKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKAFopKWgAooooAKKKpX2q2Ni226uY4
        3xnZnLfkOaALtFYaeKtIZ9v2pgf9qJwP5VsW88VzEJIJEkjPRkOQaAJK5zxlqz2NtHbQMVmuM5cdVUdc
        e/P866OuJ8e2zvqFnIp4aNh19CP/AIqgCppto0sW1comfug8f/XroLPT41A7/wAqwdMvlSBf1rb0u9Vp
        CpPPWgCDVrFIQWCAoR8wx1rHs1eyvI7u1bG0/Mp/iXPIrotcuVWMr1rltkssj7Gwg5A/WgD0SzuY7u3S
        aFsow/L2qauI8G6mYr17KU4SU5X2bH9QP0rt6ACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoooo
        AKKMUUAJRRRQAUUUUAFFFFABRRRQAUUUlAC0UmaWgAooooAKKKKACiiigAooooAKWkpaACiimuwRGZjh
        QCSfagDnPE2tNAz2do5SQY82VRkpnsPfHPsKwLWxhDsxtDOxPLyyEkn1NUreZ7m7eWU5eV2kIPqT/hgV
        1lkF2JjGaAMm9s7cQgm0EYP905xVKzuZdDukniJMDEeYueGX/H0rsLxFNqcgdO9clexB0dSRsX1PY+lA
        HocUiyxJJGwZGAYEdwawvGkbDTEuU+9byBicdFPB/mD+FV/A1/5trNYSMC9sfk90PT8jkflXRXcCXVrN
        byjMcqFGHsRigDyhPldwrjB5x/hWzpMc/nJKFAUHkk9a5kpJHcT28h/ewuY29yDj+lXra7uFAQHCjvig
        DtdT8hokw67gME5rCZxHJ8g+UHkdM0y2dCm6UCUHqxYgr/SpZ0QZMDZAHR+SP6GgDKu2MNx5kTFZFO5c
        dq9L0m8W/wBOguU/5aKCR6HuPzrzW+UOu7OG6HFb/wAPr8q0+nyN/wBNIwf1H8j+dAHbUUUUAFZmt6qm
        nIiIokupeI489fc+1adeea9cu/iiZmPyxkRJ7AKM/qTQBpT/AGi7bFzqrBz/AMs4RhR7e9RT6LdoA+n3
        jluuAxU/nVu2RDGpOBxWvZhBtwaAMDTtcvrCYQ6mGkiHBYj5l9/euwhlSaJZImDowyGHQiuV8RqruigA
        sTgZqv4c1M6fe/ZJ2/0aZsIc8I3p9DQB2tFFFABRRRQAUUUUAFFFFABRQaSgAooooAKKKKACiiigAooo
        oAKQ0tJQAAUd6KWgAFFFFABRRRQAUUUUAFFFFABS0lLQAlVdWyNKvSOvkPj/AL5NW6ZNGJYZI2+66lT9
        DQB5dKnl+XcKcDO0jPX0P6Vv6ZdDC55xXONI0cf2aXhoyVYHsQcH9RWppk9tEo82QA47UAdPqM/mWQ2n
        BHWuMug7ToOSFbc30rpW1OF4DFaxmV8ZOBx+dc/dCaSRi42Z7UARaNdnT/Flq5bEczGFvo3T9QK9Qrxr
        U1fZvjJEsZDKfccivWtJvEv9NtrqP7ssav8ATI6UAed+L7P7J4vdwMR3cYlU/wC0PlI/QH8aghjQMdxP
        0rp/iRa77CzvVHzW020/7r8H9dtcorqQGGTxx9aAL6klwFzirmYliOXIYcjPBrPgbepPJ/QVNNMiQgLG
        CT6d6AKlwAchXDA96p2N2dO1S3ulJHlv8w/2eh/TNXHjPlnhRx6VnXCb4yT97tQB7HG4kjV1OVYZBp1c
        /wCBr77b4fhDHMkP7pvw6fpiugoAK841wD+0dRO5VZJ93J5PH+Fej15b4x3QeJ7xf+euyQfTbj+YNAF2
        G8kMChOT7VsaTNcGI5Q7h6iuV07VPsq7TGGrbg8QZiKpEeeDQA/VneWQKc5DY4rJ1qIiEBAV64J/nWjD
        dSTOx2hVPPAqDUQHiwSc44zQB1/ha/Oo6LBLIczKNkh9WHGfx61rVw/gG5Md1cWrH5XXePqOD/Su4oAK
        KKKACiiigAoopKACiiigAooooAKKKKACiiigAooooADSUUUAFLSUtABRRRQAUUUUAFFFFABRRRQAUUUU
        ALRSUtAHl3ie3Nr4kvE6LIRKv0Yc/rmoUthIuRng1ufEKHbqdjPjh4mQ/wDASCP/AEI1lWXyRnHcUAam
        khYYx9/JBX5ePwqzfKJFAIbGOCRg1ThYALzz1AJrSZibfLqRigDk9TjYbtw5HFdV8NL0TaPNaE/NbSkA
        f7Lcj9cj8Kw9UQSsRkbiufrVbwLdmx8VCBjiO6Qpj/aHI/r+dAHoXiO0+3aHewAbnaMlB/tDkfqBXmFq
        EaFZASARnivYa8iv4m07Xr60wPLjlJQeit8wH5EUATQOkbld7Y9hVmIb2xHGTu/iYfyFOjZNm5Y8n3FT
        xvPlcKF6d6ABoNke2R8yenYVm3kBhB2ncv0rdUZXLuNx7KKo6hGPJIxz0z6UAT/De5EepX9mTw6iVR9O
        D/MV6DXj+iXZ07xJZzsfk8zyn/3W4/wP4V7AOlABXnHxLg2a7p8//PWIof8AgJz/AOzV6PXDfFKI+Tpc
        /ZZmj/76XP8A7LQBzkduCqtwDnFaEUIjBKgZznnvWbETKijnI6AVoWisygBQcdyaALwYFiSu3jIxzVO4
        k3Iyt17GrrxiNMtgk+nase7kHmNxj6UAP0W6FlrdtJkhS+G+h4r1QHIzXjFwTvVge9et6Nc/a9Mtps8s
        gz9e9AFyiiigAooooADRQaSgAooooAKKKKACiiigAooooAKKKKAAUhpTSUAFAooFAC0UCigAooooAKKK
        KACiiigApR0pKWgAooFFAHLfEGDdplvOB/qZgCfZgR/PbXKqwxHtPbkZru/F0Xm+HL4Dqkfmf98kN/Sv
        PbDJYcjPbPSgDXtG6KPl569zVqeUorgsefU1myQyMQS5A/2eKtQWqdwzOfXpQBFc5aEsxO71I4rmr+U2
        Oo2t4nPlSrJ9cEH+VdRcRnayptC46CuY1dA9oR12mgD2iJxJGrqcqwBBrzrx7AIvEkEvQTQj8SpIP6EV
        1HgS7N54XsmY5eNTE3/ATj+QFZfxIiGzTLjHKytHn/eXP/stAGRbEeUNoz7VKS5cZwuDUNqSEXH41ZZQ
        x+Yd6ALlrhlO45Puaq36kKQcGrMEmwoFxnPUmkvVBySee1AHF63EQpK5ye/pXrnh68/tDRbO67yRgn69
        /wBa811WDfE2RXUfDC636JLaMfmt5SAP9k8/zzQB2Vcv8Ro1bw7vYZMU8bD2Odv9TXUVmeJrP7foF/bA
        ZZ4mK/7w5X9QKAPMrKVcfNkHtnvWrFPGisY5QhPVWrG0dorq3j3dSMjNaht7WM8Lk98c0ASztHKqkyqc
        +jc1SuUAyBncO+c1digdh+7jQD1I5qG6hCoxL7moAx5FwpB7DIruPhtqBu7K9gbP7iYAfQqD/jXDTbuR
        mut+FUJii1Rz1eZSD7bQP8aAO9ooooAKKKSgAoNFFABRRRQAUUUUAFFFFABRRRQAUdKKDQAlFFFABRQK
        DQAtFIKWgAooooAKiuriK1geadwkajJJqWuO8Qah9o1QwIfkgbZjr82ASfrzj2wfWgCW68T3DsfsNquw
        dGkyc/gOn51Ha+LpY5Quo2wCH+OLPH4GrlvButgScsB3Oayb+2ik3KQAw9f50AdtbTxXMCTQOrxuMhh3
        qWvO/DWpto+orbTv/oc7YOekbHo30Pf869E7UAFFFFAFfUYPtOn3UB/5axMn5givLNKfdGvuM162eleT
        W6LDf3UJyBFM8f5MR/SgDUAkIGOB6mpY1JcZLNjsBgUIxICqP8alWJ8jJJx+FAD5F+U5XH41zOrxhJHU
        fdcflXYJGpj6LnHrXOa7F1OOR3FAGr8KbrNnfWZPMcgkA9mH+IrW+Ike7w40neKaNh+Lbf8A2auJ+H10
        bTxd5LE7bmNk/Ecj+R/OvQfGqeZ4W1AH+FA//fLA/wBKAONs2xEuD2q1vOAccepPWsywYNGpJ4x0rSVt
        oDNhfc0AWYJAjBgpY+mKluJS4+4R6ciqBnPRSfqTgU7ejHgBjjnAoAiu13RkEY+tVfB95/ZnihImOILs
        eUf97qp/mPxq7Mu1SBurntYRkAkibbKhDo3oRyP1oA9moPNU9Gvl1LS7W7TpNGGx6EjkVcoA8d0pPKnm
        hT5THI8YHphiK3EtnZWzKQAPzrJZSviTVEXjF3IR+LE1qhQ0YVmIHXJNABHCgkJLM/HQmor9BzhMDtUk
        SoAQGAPTAokUunTJ9S1AGHdKcYPpW98PLwxatPak/JMm4D3H/wBase+T5ifwIqHRLn7HrlnMThVcAn2P
        BoA9kooByBRQAUlLSUAFFFFABRRRQAUUUUAFFFFABRRRQAUUUUAJRR3ooAKKb0pwNAC0UUgoAWiiigAr
        zC4lZNTnkcHEs0jA/wDAj/SvT68juSx1B42fCRO6lcdwxFAHZaZdLIgQtgEYzVDVyEchhyvBqhYypC5Z
        5wNvHHep7q6jmdjGCw9WoAztTgSePcjKSRyK7LwVqp1LSAkzZubY+VJnqR2P4j9Qa5F2DkjH1xSeF7z+
        y/E6K5xDdDym9M/wn8+PxoA9PpaQUUALXl+tRfZ/E2ooB1l3j/gQDf1r1CvOfGSBPFT9vMhR/wBSP/Za
        AFgzgEdauwAryx3exPSs+0U/KelaayADAANAEyys38KKPTNZuqQ7o2Yjp7Vd805GSPwqO4ywIJzn1FAH
        CmX+zdesbscCOZSfpnn9K9e8SgS+GdTxyDayH/x015V4osy0DkdR0Ir0jSLg6p4HjfO55bMofrtIP60A
        cNaFREuTjipUlZmIBCj1PWqsH/HgsnH3c/pVrTLYzr5sgJTsO1AFi3jhyS8m89eOaukoifKDx3200YhO
        2EKp9hnFQzm42El2KY7CgBVuF3YYmsvxAm2BmUhx1p32jY4L+YFBzyOKk1CWK5sW2nHXAIoA6D4UXn2j
        w/JCT/qJmUfQ8j+ddrXl/wAHJttzqtsT0KuP1H9K9QoA8gnk3+ItVcY5upAOfRiP6VoiVUAJK7u3FYtr
        j7Vc3En3pJ5HA+rE1ofacBgu0e4XJoAsi4z93eB6haU3BwVaTPf5lqpFcSFSDIEX3XrTnkkKhhKX/KgB
        LlS4JPT1FY1wp34HBrchYupBQk+o4rJv1IkOc5oA9d0S4+16Vaz55eME/Wr1cp8Or0XOitCT80EhU/jz
        /WuqoAXNFJSmgBKKKKACiiigAooooAKKKKACiiigApDS0hoAKQmlpDQAGiiigApR1pMU4UAFFFFABXk3
        iFTa+I9ShAGDL5g/4EA39a9Zrzn4j2vk6va3YHyzx7G+qn/Bv0oAybcb8bsjNbVoF2sODjnBrLt1xCvH
        PbNatghlfaCQSPrQBHJDghwvynrjtWJrUBVfMjJ45U+ldNcf6LMRL86H7wz2rL1aBUjZYgWhYEqT+ooA
        73w5qI1TRrW6BG51w+OzDg/rWlXCfDC6IjvrFz/q3Ei/Q/8A1xXd0AFcF48Tbr1pJ/eg2/kx/wDiq72u
        L+IMf+maZL2xIpP/AHzj+tAGTDLsHTOOoqVJ0zzkk84FUm4+6cA9/WpbRkB4JZ80AaIYhQSAoz071PEV
        ZPuH8ah25TLMsf480yHyvMHzrIfTNAFTV7dHiYY7VrfC+4D6HPZt9+2mZcf7J5H8z+VVdQtBJAzwZ4GT
        zWb4FvBbeKmiztW8iKsP+miHI/QmgDMuU8u5fT+QInZG+inFa8OoQxWYhBVGXp/jVb4g2rab4jju1X/R
        7wZJ7BxgEfyP4mo7KNHiLrhm9MUAWoryPBG8n2A61PFNcvtMcSpngGQ/0qgs8kRIES59utSi6klcEwSP
        gdGNAEWoZiUiWRHY8fKOBTLZ0bT3ifoRkZ9auXMMroG+zRxgjOSc1l3LGK1ZXUcHgigB3wtlEfi25jz/
        AKyJsZ9m/wDr16tqc5ttOup16xRM4/AE14p4CnMXjSwLHBkLofxBr2nV4zLpN7GoyXgdR+KmgDxlFbdB
        ET6Zz/M10rrbLbRIrRhs4JB56VzUA87UYMYIZOh/z71tXdvDCSzAZC8LjHP+TQBWknVZigOVb0p0kRVA
        EbBI6iolsoo1SaYbmfkAHAFTujfZwIyie27JNAFe2upraXDjdjpVfUZkl3OODT97hyzFTj1rKvJfMk46
        UAdn8KLn/iYalb56oj/zFel15V8LRt8SXq4P/Huv/oRr1WgAoooNABSUUUAFFFFABRRRQAUUUUAFFFFA
        BSGlpKACk70tJQAd6UUUtABRRSUALRRRQAVzHxEszc+HXmQfPauJvw6N+hJ/CunqK7gS6tZreUZjlQow
        9iMGgDybT5xLGqu2DgAGt3S5PJuljkH3ulcppitbXE1rPgywuY2HupxW9FFI8kZkfaF9DzQBvXQEkb7g
        oBOB64FYNz8sojPCv29DW5GiqAACSTyWOTWdq1uxl3hSAOlAGf4bnOm+LrcscR3AMLemTyP1H616p2rx
        vUhI6B4iRNEQ6EdiDkV6tod+mp6TbXkfAlQEj0PcfnQBerl/iDFnSIJv+eM4J+hBX+ZFdRWV4qhE/h++
        UjO2PzMf7p3f0oA89um8uzD+o/X0otElZN6ME71T1Fz5QyPl4xVu0mm8lfKRQMcc0AStBdOxYhZG6jLc
        VeiUeQBKEjkHUqeBVVTcOuJGhTHUnk1LFZ2rlfMmaVs8joBQAtlqcUFwVnctEwIJ5rmbu5TTtctryFzs
        juFl/wCAk4P6E10uo20UdszW4Bxx2rgtZkaYTs/GVAA/GgD2Lx7aR33hS5kYAtABOhPYjr+mR+NcFpyb
        Ywyu34V21ldnU/hq8x+d2sHB92CEfzFcZYKhiVicGgDR84MpBt959SKaWnChAjCMnOKlgySNgB4xk1Z8
        u5MiGJwuR0FAEEaTtbhdrOxHVm6VlXsWyNt+GY9BW+sZiVkmV2k6g5yKxb0AE7gQecCgDmdDJtvGGls3
        H78DP14r3/qOelfP1x+613Tpec/aEP8A48K+gE5QH2oA8RjQWWvtEeWgd4wCeu1sf0rWv5pLgFmhJPY5
        6VT8Y2xh8XXwj4IdZQfZgCf1zV61nea0VFyGAweAc0AQNLIUVZbRtwXapDf0qKIS4P7hiD15xUspmypL
        c4x6UgmITy2z6fLzQBU1BxsCRoA3oGzWdHb4kJcVrPCI03FcE9z1qlNlUZzwcYAoA2fhoQPFMx3cmEr9
        eQa9ZrxHwXcG38S2T5wGl2H6FSP54r24dKAA0UUUABpKWkoAKKM0UAFFFFABRRRQAUUUUAFFFFACUlLR
        QAClFIBSA80AOo6iiigAooooAKKKKAPJvHdsdO8WmZVxHcqJQR0z0b+Wfxq5ps+YgRgn3rb+J2mfa9FS
        8jGZbNtx/wBw8N/Q/hXJaJLvRQTyKAOtsn3SbmJYjp7VYvYg6dMk+vNVLJ8KBx+dXTLvG3H4UAcve25R
        ye1bHw6vPKlu9Nc4XPnxD2P3h+eD+NLfWjMeQRmsGTz9Jvob6EEtC2dufvDuPyzQB6sKR1DoysAQRgg9
        6hsbqK9s4bm3bdFKodT7Gp6APKL63EOoXmmN1t3wme6nBX9CKIFVImV8KR056+1XviDbPB4ihuoiVM0I
        H1Knn9CtU7RxIB5iDd9aAI+Hb5Ekb6dKtRWErSZkO0dcCrcMsUeVZenpU6zSEhoUIBGNzdKAItQhENqF
        GAD1HTNcLrlqPIkZB24ru75lIXJZ2x95v6Vz2pwh4zx160Ab/wALXOoeBruyJ5VpIvwYf/Xrn9G+a3Q5
        52jIIrU+Db+VPrFoeNrK4H1zVCJfJ1S/iUfKl1Kg9gHYCgDZgUFBkEe44q0IkCA5K4PJLe3pUFnhl6ZP
        vzVswKqb9qBfZefxoAru7D5lAAI/g9KytQZCTsJBbjmr858vcd5UdsHOazbiTbGwwuccH0NAHK6x+7ub
        aU/wSqfpg177bNvt42HdQa8G15CYDhTuU8cele2+HrgXeiWUynIeJT+lAHnHjnCeOnP8LW0e765anWkS
        4DKWB9qh8Y/vfHF7n+BY0H/fAP8AWrWnnYgRsAdRQBFKSThvMyD3XGKVI5ZnHkoQnr0z+NX1jVm5G761
        YIVYEDHkEgDtQBhXMTgkMOM4HNZN4AI2z1rodRjwHbexzgnIxWDeDc3HAHWgDM02ZoNRtWXgrcRn/wAf
        H+NfQCHKA+1fPLN5dzH6mWP/ANDWvoSA5hT6CgB9FFFABikxSmkoAKKKKACiiigAooooAKKKKACiiigA
        NJQaKAAEUtIKWgAooooAKKKKACiiigCK6gS5tpYJl3RyKUYeoIwa8WsVeyvpraQnfDI0bfVTivbq8o8d
        WhsvFjyqMJcoso9M42t/LP40AaFpIXA+YAAetaEDleAce/c1zunzl32qeP5Vso2VypIHc9zQBpTEMuCx
        yfU4rOurdZVKnn3onfYmVIDD8zUNvdGQFGUgqSfwoA0PA96bS9m0mVvkbM0Ge395f6/nXbV5bqUr281v
        fW6nz7dxIB646j8RkfjXptpOl1axTxHKSKGU+xoA5z4hWnm6KLpVzJauH4/uHhv6H8K42xKsA2cZr1W7
        gS5tZoJRmOVCjD2Iwa8iso3tJpbWb/WwOYm9yDjP49aAN6BYypyAOcZPerK5UgpGH9CRgVRtyWGcj1yR
        V2zjdchSpwMFjxQBFdNkBpc7x1Uc4/Csmcbg2Sc+la90GQElhkf3ax7nIY5bBPb1oAPh85tPGUsbcLdQ
        kD6qc/yJplyPL17U8Hj7VKf/AB8mqE80mn3ltqFuuZLZw4GfvDoR+IyKnj1CLULy5vIo2SKeVpAGHIz1
        B/HNAHQWj7ceXzg9D/n2q8rh1ILfhgVj2rx71JYYrQBLlfKkIXHPHSgAuwpXEwXGMAcA1iXKAgkc4GBi
        tG9GFwx3Ec5qjI7hSoPHX1waAMa/iLq4K5b0x0r0T4a3S3HheCPPzwExMPQg8fpiuIu41bCod2ep96Xw
        3r0nhq8kBh8+3uSAV3bdrDuOPw/KgC54oRH8Y6iR28sN9fLWpYYwYlDbuOA3b2rOlvl1LWL29WIxrM4O
        084wAP6VfWRUC89eOtAF6F9qkP8AeHIx6UJKGkIwMkHG7jmqTFuCpcDsTimTsccBiT/Fg5oALtvNkwWL
        EgHg9qybvb5bN1JJ4+lX967iG+V+i9efxqrOF+zIMEyNzkHpQBzc3zXUOenmp/6EK+hLcfuE/wB0V893
        Hy3EeTz5qf8AoQr6Et/9Qn+6KAJKKKKACiiigAoopDQAUUUUAFFFFABRRRQAUUUUAJRRQaAEGc9OKXNJ
        nmloAWiikoAWiiigAooooAK4z4oWBn0aK+jGXtHy3+43B/XbXZ1De20d5ZzW8wzFKhRh7EYoA8Y0aYsQ
        QfrXQPOyptQZyMYrkoWl0jVLmzuBl4WMZ98Hr/WuhtWkni+UqoPrzQBft7ny5suweQYwcdKdMB9vypBD
        DP0rLO6Bgc72PHvmt7Rrf/RJJ5+ZmOMelAFO+BELFhk4wa2vhpqP2nSJbN2zLaSFMf7B5X/D8KztRt3M
        L5HJFYHhC9bSfGUSucQ3Y8l/r1U/nx+NAHsNeXeNI1tfF0xQYE8SSn3blf8A2UV6hXnnxNiK6rpc3GGj
        kT8ip/rQBStJQQAT1q9C8oOBgHrzWNaSBcE9RWhbh5iCsm0jse9AF2RY5EDMZDKeTznPtWXcRsHJVMrn
        qTzWzgxpulI2AcbfX29ayL2QD5XEm8nIA4oAz7mHKNnHSqelAJEUI2qrEfhmr8vIJ6YHTOcVSsJVaaaM
        gYJOCe9AGtb4JHIHuOtaAlCKAhDcY61Qhi8tflY/lVmOfYhB3c+ooAjuXkySxQLjtVRXyHIZ9pPLYqWd
        nlYnHA654FLHku7NhgB2HGKAKcg+T5AcHnk1nahaiS2YORnHGK2GYKu6Uqc9AKqTRvPuQAKg4NAGfoDs
        bTPAx1rchZfMXG0HHWsFgbK4DRL8jcFa0bW5E5JZjk87eKANabLwsPkIXHIGDUEgxEBA7Ej+9xTTK0R4
        GT/dZcGmpP5iNuUo3oRn8aAKhZwWEn3gKZK/7ltq5A6NmnAAzbZTnvkelOkbcCCvXJzQBz9xGWnQn+8D
        +RzXv1owa2iI6FQa8IvoyBkHBr2XwndC88P2cgOT5YU/UcUAa9FFFABS0lFABRRVDU9UttPX9626XGRG
        vX/6woAv1n3msWdrkNLvYfwpz/8AWrk9T124vMqrbIz/AAJ0/E96yZZJUXdJ8qdjQB1Nx4rKkeVa/L6u
        39BVb/hMpUP72zQj2kI/pWFbuJl+R2c/TNFwimPaY5C+P7hoA67TvFmnXcixylrd26eZ90n6/wCNdD1H
        FeKXMTIfmR1U9CVIruvh3rDXdlLZTuWltz8hJ5KHoPwP6YoA7Gg9KKQ0AFFFFADaUUg60vegBaKSloAW
        ijNIDQAtFFFABRRRQB5V8VdO+zavbajGPkuF2P8A769PzH8qy9Nn3ooDbSPSvSvHGlnVfDlzEi5njHnR
        eu5e34jI/GvKNLkDKCpAzzyKAOogVRhlXc394nNaljIIzuPzP0AFYEUjBBlsKeDitjS1zukOWY/dA4xz
        QBfvA3l/Nxnng9K4zxDZuW86HKyxsHQjsRyK7a4MbgYzjuSeKyr2DzIyMD8utAHbaDfrqWj2l2v/AC1j
        DEeh7j865D4pOPO0heP+WrH1/hH9azPDviOTw7K1lcwGaydiylSAyMeoGeCD1qHxJqR1y/WdYfLiiXYg
        JyeuST6dqAKUJyMcn0NX7YgglmOR7VQSNjjaePeplkdTsTBHXOKANNZnBMjHai/d3dqqzuCrFAxJ6tjG
        aSIgyfvBvKngZ6mnXkn3g55Xqc559BQBmTnKiOPgtxTltlCqFByMVPFAV/eyjaegB7Cno6kng8kcCgCe
        OMwgBnBXqN1Sxzpggk7vQAkVFsNzKB6DA/2R/jWh5ez5Y8IBwTjNAGXczBiR2GMnBzTnnwnyEtnoAOv4
        Uy+jO/5pxg+lPtwkUSEEAj5i3t6UAI8RABchpjwMDhaftEKmMHHHB9ahMpUZzyWyFPb0JqOR98wReSRj
        jr/+ugCN4FmcYBY+wqdLAY4+VxyPeraWzQqJHKYOQAp5ptxMYyocqWCZJH8Q9/egCp9rk3hLpBtHGRSX
        LRlMqWJHQkYxV63SKdQZByOGz65qG+h8hA1sN0LclDzj3oAzSQ0yjru45pVVmdiflUZAz6VGI90nyYGO
        eaeGk4GMgccUAVr9dyhdoXb2rtfhfen7Pc2Mh5RvMQex6/r/ADrjZh95n7c8nGKk8Gag0Hiu3OcJIfLN
        AHtNFAooAKKK4zxJ4gNxI9lp7HyxxJKp+97D296AL+v+IhblrfTyrzdGk6hPp6muZhtJblzJMzMzHJLH
        JNPsrXAywx7etaqMFXtkDoKAK5s4o4SVAJFZF3by3dwka5ES8tz71rXVzlMYwo9OM1nXN2rMIjKVwOT6
        0AWZrgQ2y28TBFHBIHWsW5lLNgOT7mquoO4Y7HytZ4uWB+bgUAWroEDOc+9TeDL023i2xVDhZt0bD8M/
        4Vm3N3mHkgDuTU/w2tn1XxjHcLn7PaKXz6k8f5+lAHuOeKKDRQAhNIOgJGD6U6koAaKUn0ppzjikGQKA
        Hg5FANMyOlIDzQBNRTVanUAKKTNFFAC0UZooAD0rxTxLZHRvE13bqNsLt50Q/wBlucD6HI/Cva68/wDi
        zppe0tdUjHzQN5UnurdD+B/nQBzltJ5gXkZPAHpW5aSYKLG+0DqR1Nchp04AXIrchmVVGWxz2oA6PHQk
        jb/dzkmoZm+Xk1XtTuxw3P3VQcn8abPIC3lqQ/svQfjQBl6vbpPGwxleuR61Do1u0Np+85LZNTalJhBG
        CAzcADtVuFSsSgA/KvWgCpMjBTsGf6VHzFHknDHnAq5JEY5A3G3NV7pTI+UXHHJ/rQBDHcBAGOXboBV2
        GMn551/ecFFB+VR6n3rOsWVbh2f5gnStdmHl56HHU+vrQBCwIEmRx/ESe9RxwsqqXUhW+63b609BukBJ
        +QcAf1NSzTFlY/Mdxz9KAHRYh+8PmPQD0pqzSNIEY5yc4FQxlwJHz8x5IH8VNhYrIoA+cjLMe1ADNQVu
        cqq9qrRSbQGyX9Aegq1PtfO4sce1QnPl4wBxkE0AV5JHGCQSQRjNWrXCTbskynoq9j/9as6acYAPIU/m
        auWpEcTMxzK/UZ6UAWpS+8IJMLngk96gRvMuF3sdoHH1qO4uG2EcYPA6VHbOrS/NwDigDYZMIgyRJjsK
        gdyEIZSpAxwMg1JIWjhG142XPRTlqrXMoZfmWROONxzmgCiWKksDxnGKtW395842kg+tZ8n+t4Y/iatR
        EOkakhcZ4oArak5Y/N39KyQ7QahC8Z+ZPm49jWnqbiBDk7scAGsiz5M00vXGM0Ae/wCl3AutPt5lOQ6B
        v0q1XKfDO7N34UtSTnZlPyNdRPKkEMksrBY0UszHsAMmgDl/HOtmyt1sbZ8XM4+YjqidM/U9Pzrm9JsG
        VA54HHGapWkkmu61Pf3GVV23BT/Cv8I/LH45rpAPLARRxjtQBIGSKL3FUJnd+V9aR7oHcjfK3b3qG4n8
        u33gEAg89s0AM8zO/PKryef0rmNRndZS/IHJrRe5MYkyc5HTtWNeNvQZOeMUAON5vALEEEd+1Z15cqnz
        OcAVXaUrKMHgcHFXPDGiT+K9ZWGMMlnGd00novp9aAE0HQtU8W3ey2XyrNT88rfdX/E+1e3eFvDtn4d0
        8W1mMseZJD1c+taGnWNvp1nFbWcSxQxjAVRVmgApKO1FABRQaQUAIKQ+1IDzTqAIzTac4pgyTzQA9Dzz
        UqnioRUqe1ADqWm96dQAUUUUALVPWLFNS0y5s5fuTRlM+noat0UAfPkBkhmeGYbJY2KOp7EcEVtW8yg8
        nJ6AUnxCszZeL7naMJcqs6/iMH9Qay45yqYGM0AdNBcySLtZyFI+6DjP/wBarM5CoTuHHoMACsnTn2up
        bG7qSe1XrxXcIoALv90ZzgepoAq2SefdmRV3BehNbCsVQ55zjr/Sm20AsogDwcc1DcMNyk9OvB70ATMU
        bhyQxz1FDxqCSHBIXkEYyKqowEqk4wehY1aM0bbwz4GAMjnj0FAHOwsBdbe2dx+lbCgSRGQg4J7n9BWD
        d/8AH0WHAz+lbEBD2uS4XA+tACtIzyLEqYPQAd/rVu5Ro4yzFSwwp5xjNZnmuJfkcqWOevP505pGwS4J
        GfmJOcmgC2cpEdpX8BwPxqrA488uTtX1J5NJ5ocHcSAoJAPIzS2oVgXYA+5oAdJIzkjJ4Pcc1Fc4SJWU
        8+pOaR9quSqnn++Oar31wsULEYzwF+tAFNZA13hRnafrzW1HEIlDT581+cY3Y9qydIjGS7qWbqBjI+pr
        eQyGDdmFByMHr+QoAy7yZXYIuAR04wT70y2yRkEc9fapJzuc5PI6HrRbKQQwbDYDZU4oAvwqQvyRgkc7
        yTii4WREZvNSXI5AIOKQzYBUPleDwvNVbmcuCowQD97GKAKzxnPzRleM9KmhAEauAc9CO1ICZDhSWPTn
        tTrlvJhEa8vjH096AMa+YTXD4+6v6GmIgWIp3Iq0kK4YN94/qajkRlVmNAHovwmh8nw+67gR5rYHpzT/
        AIjamwig0mBsPP8APN7Rg8D8T+gNcx8L9cW11OWwuGCxzklGJ4Df/XqGe8bVtXudQY5WVyIx6IOF/Tn6
        k0AaWkoIlCqcVZ1CYwlTu59qS3g8qJXJxmiSIXICt645oArXB+2RZTBKjOBzWVJdSQwtbyEGPPWrUsc2
        l3Acfczzj0rO1N1WXcvML9PagCtMwPGcq36VlX0hXO0/LnirJk+zvl+UxgH0rLuZQxJz8nWgCKOK41G+
        isrNC00xCgCvoHwpokOg6NDaQqocDMjAcs3c1wnwb0QFZ9ZuFBdiY4sjoO5/pXqlABSGjNLQAhoFFFAC
        UUGkoAatOpimnUAIw+X3pmKk60cUAR4xinoadSAc0AOopBS0ALmjNJRQA6ikzRQB5b8XhjV9OIHJhYZ/
        4FXFIeRmvSvi1YmXTrK+X/l3kKOfRXxz+agfjXmwUAZBGOtAGtZSpuG48Dk59a2tKkV5mkkJ3EYWuYhb
        axz6VsadL5asxbBx696ANnUboINrc7P51kRTGZ/l5PcnpUN9cb12oc4PX3q/p0HlWyyOMbjwxHH5d6AH
        qjx4Y9exI4PvUU8rbcCQbe+BxU0k5+YN8oxn3NUpZcxgqgAXk56fj60AZ1xvkPAzySc9a07MMLFyqnAw
        OKqRoFjLsDtJxuxxRpzSSlkQFs9hQA65kHy+WGJHJ4xVja7gNtVSUyWzwfpVaRGRn3Lx7etOeYyrHyGc
        jB6/LjoPegBJgu1QH3HOTjNWLPIkO1wGA7CoWjdCoILZGfu4q/kiD94ItpUkKhGRQBSuH3yDJGe/asuf
        99OOOnQVdY5yR0681W+5J5j4yRuHtQBtadAbUgzhQ7/OucdB+Oe/41PfCPeyojh89XByP6elZdvK1yxA
        AHctj+VTugBA8wlPfv8AhQBDMyljjBPUkZp1q8YIV1LlQSAq5/OpEgRiAnK45JHSrLRQMwKbjCB8zOcY
        OTwBnmgCv542Eo0aHqPWqM0pdgFAz61JdHaNoAwOARUNrhnJbBA60AWbbbGnbzCMkntQ4G7IOT3JqYQl
        1BhQk/eY9Qo9zSmMRSYzlzzntQBXaEocv1qpeZbOOnStKdcEsxJwOc1nXAGAefpQBh7Stw23p04rovD+
        JItpOdvGM1lPFtcnHbJpun3DW92Qp6jJFAHbtM8ajaowO1SrOuEdSMelQ6fJHdQr8oOR1NOECBzHwT6E
        9foaAJb795GXwpXHI7GuSvtsLspJMDcc/wANdPHMIyYWB2MCOea5DWnMVy0bHIIxz6UAZt1MY3ZW5Ujj
        0rPijku54bSAZkmcKB9TSzSbiVc8Dp9K7j4Q6It9qcuqXCEpbnbFnpu/+sKAPU/DemJpGi2tmn/LNACf
        U9zWmaTpR1oAKSlNJQAUZoooASiiigCEGnA0ztTqAH8n8KX+dNB96CcUAPpM0UtABRRRQAUUUUAFLSUU
        Act8S5APC7xH/ltKifkd3/steSiJlBDDJr0/4pSAaXYxAne1xvA9QFYH/wBCFeek/wASkEdwaAIo0yuc
        YNTtP5cWMmoJJ9oAAOe9NQ+ZMnmcJ1oAtWIDSh5MbV6LnGTWk94877dxKrkKi1k3sqceWOB6dKZYvI8h
        8jPTqe2aANKZ5mkMaFzKeoXn8KfDbqq5mfvgIvJNWrALBGcjeM87WwP+BHqakdEkkLQqIB1Oc5PPb9KA
        KF/IyQpGWDYU4UD7v1zTdPkaGMqm1N4wWNOuvKjJBBeRsgDrj3+tFmFWMseOe/Qf40ASKs99Iqqwwo5c
        df8A65qzGq2SvHFHuYjG9gd3169KhlEj7Wh2kHuBjH4mkndoYPmn6qCyk8+mB60ALbv5l0DIXYE5YkjJ
        /E/zouJl2yiNXU/3kY4b6881DZ/M7OGBAGTvI5+gqO5lR23KPw6UAVpGYj5B16Yp2mpELhXuVMgByVB6
        +1LbozSEKzKGHGByfarQijRQsaEc4JJByfr/AEoAtl4GACQhZe+30/Co/wB++5VA2DqCc/SooX/et5Y3
        N1wBnH17VNuZ1kwMNkfLtBz757fQUARM5RtrLjHDe9K0y4JK7OONvWmLAQzZHA6k0yV8A9DxnJ60AV7i
        Z3AQAYH8WMGnW8YGVJB55xzUAVnyxyCat2ylBhWyT154AoAuFpcFFJAOPlXjoO4qVkVQM/NKwycnvTbd
        tuUXJB6n+8aS4c5ZUwT0O09KAIbhuCN2T3A6VQZW3Z7GrzEKGwBjGOfWo8bgWC/LQBmzLwT61kXrGG7R
        h2/lW/MuCT261h36ZYnqaAN7QL4ozKGIrp7n99beag+dBk89RXAaG+2+iVgCHG3FdlaytbTmMk7f7rdq
        AK09350WSRn1rmNel8wIx5YDFXdVk+z35QcI/wDOsTUZP3jZztzmgCpFE97dQWkePMlcLn0zX0R4V0eL
        Q9Ggs4sEqMs2PvE9TXmfwi0FL69l1W6TcsTYiB6bvWvY+nSgBaKSg0ABpKKWgBBQaKSgAooooAgFOFNp
        aAFFKRTRTs0AL0pc5NJmgmgB4opobNKME0ALRSZpaACiiigDzv4p747zS5H/AOPcq6/Rsg/y/lXEyH5i
        yHg9q7r4xBW0nTxn959oJH02nP8ASvKo7mWPCknAoA0ZC5YDHSlSORhzweoplncBgc5JA61cWVWA5Hrg
        0AQrDk/P8/1NX4HEOG2BwOx6VVG3eTip4kJxtwAaANS0vJdzGOQRE8/cUgfpUN1JIZAsU0ruVG9ugHtV
        MFYn5+ZSOO/NWPtBdQojw69xQAyW1ePBeQMp7rnA9uas2MaCT5W+bHGBuJPsKquZHb94WBPXJqSONpG2
        rI3GTgHH6igC7dzxRnGZCy5HzHr9BWZJ58037zKDOPmPOPSrU0Oxcxrgnr3z+NM6YOTkcE9hQBPDbRfK
        JhLKg6rnH5VHNbR7wY4UjXGPvE1YUgDcFEgHJYnH6VBOzM4ESleP4jxQA1DsRsFd3v6VFGfNfEjEk9FU
        e3c/lVq1t2Ks7sNq53KOOPbmiJliJWKMFuvTOKACIvEMRxlcffPUGp5JYwgwyZ6gAGpEjmEbPIqpkjB6
        4FVZQC2EfeD1cjpQAxzJOCxOAOcDvVWYFGBBJ78mrErKAA5wccnrmomZudv3fUigCJYmYHc4zjmpI22r
        gZz0JpqhSpBJL9l6VYRcMof5ePrQA+KV9pXovcnrTkAbocADoKYCZVckncT+Y+tIFKocn6gUANfLjAHC
        nOR3qSJx5RGfl6AYpIySGfBEeO5705EKkbgQoPT3oAqXgKg+3TNZFwm5snvzWxfvnABPPPNZ0igtgYHF
        AFDIhlSQDlTmupv7lGtobyNiRwCT3Brm72PEZwOMVJYXO/T7iBz0G5Qf1oAbr0m+aJ1OfesiYNc3KRIM
        vIwUCrN7Lvht/pW58NNIOqeI1mcZhtvnPue1AHsPhTS00jRLa1QDKrlj6nvWxQoAGB0FGaACgmkpM0AF
        LmkooAKKKKACiiigCv2oziiigBe1KDRRQAuaWiigAFKDRRQA7tmkyaKKAFpTRRQB578XQfI0wnpvkH6C
        vNDEGPsTjpRRQBE8RhJKt07VYgdiUJPBPNFFAFyNSy5B/OrEEW4P8xyBxRRQA9EUfMckjpzxU6KzR78h
        QOwzRRQA2S4JUZzxzgYH9KSCR5Nxj4z1yx5FFFADpC4X5iDzjHOP51ZtYmkTLEMo/hyVA/L60UUAWfLC
        BPKVAcc5FUnk8ubG0ZzwfSiigCV2jMaPJvLdPY09S+12+UInBGOeaKKACSU/Zi0uWUnaOe+P7vSmKJGR
        SGUKwPQdqKKAI5IF8w7OoHU1ExVF+6Dz1PWiigBin5hIOmePap0f90SygmiigCVCI04UFiOp7Uijc2P4
        RyfU0UUASyEFEAGVHPp05ozmMA8k8iiigDMu+ZOOmcfSqyjePoaKKAGagm23fnnGKym/dSfL0KEUUUAV
        pz8ifTNet/Bu1WPQ5Z8DdI5yfpRRQB6HRRRQAlJRRQAGiiigAooooAKQmiigD//Z
</value>
  </data>
  <data name="pbLeftHand.Location" type="System.Drawing.Point, System.Drawing">
    <value>488, 144</value>
  </data>
  <data name="pbLeftHand.Size" type="System.Drawing.Size, System.Drawing">
    <value>136, 104</value>
  </data>
  <data name="pbLeftHand.SizeMode" type="System.Windows.Forms.PictureBoxSizeMode, System.Windows.Forms">
    <value>StretchImage</value>
  </data>
  <data name="pbLeftHand.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="&gt;&gt;pbLeftHand.Name" xml:space="preserve">
    <value>pbLeftHand</value>
  </data>
  <data name="&gt;&gt;pbLeftHand.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pbLeftHand.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pbLeftHand.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="labelFing3.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 12pt, style=Bold</value>
  </data>
  <data name="labelFing3.Location" type="System.Drawing.Point, System.Drawing">
    <value>592, 72</value>
  </data>
  <data name="labelFing3.Size" type="System.Drawing.Size, System.Drawing">
    <value>16, 16</value>
  </data>
  <data name="labelFing3.TabIndex" type="System.Int32, mscorlib">
    <value>41</value>
  </data>
  <data name="labelFing3.Text" xml:space="preserve">
    <value>x</value>
  </data>
  <data name="labelFing3.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="labelFing3.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelFing3.Name" xml:space="preserve">
    <value>labelFing3</value>
  </data>
  <data name="&gt;&gt;labelFing3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelFing3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelFing3.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="pbRightHand.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        /9j/4AAQSkZJRgABAQEAUQBRAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYa
        HSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgo
        KCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAFGAa8DASIAAhEBAxEB/8QA
        HwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIh
        MUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVW
        V1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXG
        x8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQF
        BgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAV
        YnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOE
        hYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq
        8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD6oooooAKKKKACiiigAooooAKKKKAClpKWgAxQKKKACiiigAoo
        ooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAooooAKKKKACiiigAoNFFABR3pDRQAUUUUA
        FFFFABRRRQAUUUUAFIaWigAooooAKKKKACiiigAooooAKKKXFACUtFJigBaKKKACiiigAooooAKKKKAC
        iiigAooooAKKKKACiiigAooooAKKyNc1630obCDLcEZEYPT6ntXLy+IdZuvnhaOBOwVB/wCzZoA7+iuC
        ttZ8QwkOUS5Q9njAJ+mMV0eieIINSfyJY2trwdYX7/Q96ANqiiigAooooAKKKKACg0UhoAKKKKACijFF
        ABRRRQAUUUUAFFFFACUuaKKACikpaACiiigAooooAKKKKACgUUUALRRSYoAWiiigAooooAKKKKACiiig
        AooooAKKKKACiiigAooooAKp6rfJYWpkYgueEX+8auV57r2otqOsbIj+6jJSM/zP4n9MUAR/Zy0xmu2M
        sjnc3uTXRaZYK8fmOoyegx0rmo/MhuMStuVf511+l3KvbegUUAU77To2zg89q5/Uont2UlmynKtnlT6g
        1uX9+qzkA/d61iavdCaPav3m4FAHYeGdTOqaaJJBiaNjHJ9R3/EEVq1y/gG3aGwu2Y/enwPwUf1zXUUA
        FFV7y9t7KPzLqZIl7FjjP09ayx4r0cvtFy318p8fyoA3KKrWV/a3yk2lxHLjqFbJH1HarNABQaKKAAda
        TtRRQAGiiigAooooAKKKKACiiigAooooAKKTvS0AFFFFABRRRQAUUUUAFFFFAC55opKWgAooooAKKKKA
        CiiigAoopGYIpZiAoGST2oAhvruGyt2muG2ov5k+grjrrU9W1aUpZ7oYj0VODj3aoNT1BtYvt6/8e0eR
        Ep4De9bnhvYIAMAHvigDNj0SZU33d64k9QckH6mpoJ7uyGbXUPtW3rDMPvfQ+ta94IyDkjrWRfhY4Cwx
        kd6AOi0nUYtStBNCcEHDL3U+lXa4fwPcP/bFzHn93KhfHupH/wAUa7igAooooAxvFmof2fo8rI2JZf3a
        Y65PU/lmuG07asm487eSKu+N7/7ZrAtkb91bDBx/ePX+gqpZr5cfHLN60AXoSHf95gHscdK37YxLZHYy
        7z05rDjWJVJnY5xnapx+JJ/pVa7k2jfFiL0CknP58UAM1GKeNmZxwxzuzWcJNk3mmQfuxkHtTLm5uJfk
        YjH0o0W2+365aWZ5Rm3Sf7q8n+WPxoA9N8OW5t9GtlcbXZfMYehY7sfrirGp3ken2E11L92Nc49T2H4m
        rVcX42vhcX8Omqw8uMebLz3/AIR+WT+IoAxyJdRlN3fMWLHkjoPYe1acNnB5AIsVf03MR/KobGMb40bG
        3AbGeK6oKotxgCgDjp7WOCX7RDFJaSxjIeJzkfn/ACrrPDmrnUIjDcYF1GoY4GA6/wB4D+Y9azdRC+U3
        TNYekXLW2vWjhvl8zym+jYHP44P4UAekUUUlACmkoNFABRRRQAUUUUAFFFFABRRRQAUUUhNABS0lFAC0
        UUUAFFFFABRRRQAUUUUAFFFFAC0UgpaACiiigAooooAK5jx7ftb6WtrC22W5baT/ALI6/wBB+NdPXnXi
        64N3rzICNsICD69TQBFYRE2fIPfBHY4rX0eWVUVVHPWqVphYgEJGOtK19JBPkoCBx05oAu6vNcKw2oRj
        rxWRfXb+Rtbg981au/EORh4ju+tYWo6gboghAuPSgDo/CShdchUFWItmJKn3X+tdzXnvw5Bm1e7lPIig
        CfizZ/8AZa9CoAKq6pdrYafcXL9IkLfU9hVquN+JF6Y7K2skPzTvuYf7I/8Ar4oA4uFnnlaRzukdizH3
        Jya1rYhWBaQKB+ZrOhTYqhe/FX9hUBgqtt5xQBpyeUU+RySB2H9apE7sh+KkEkckA+UK3r/Sqty+zjn6
        GgCKSNN+VPNa3w3tPN1XUb4j5YwIEPueW/kv51iTSKkbkenGa73wLZ/Y/DVqWH7yfM7f8C5H6YH4UAbz
        sERmY4AGTXkttcnUNSvL1zkTOzjP93oo/LFd943vTY+G7tkOJZR5KeuW4/QZP4V57p0WyNY0HOBzQBp6
        YJEZVYkMp5rskuBHZKrY3Yya5OxaeOUFU8wdMVty6nbNF5c6mOReoIxQBnapdYVgDism2j2LHMTuaRgR
        jtzgfrVjVJbeQkxSZ5zTNJzealY2w5HnK2B6L8x/RaAPTKKWigBKKWkoAKKKKACiiigAooooAKKKKAEo
        paSgAFLSUCgBaKKKACiiigAooooAKKKKACiiigBaBSUtABRQKKACiiigCO5lWG3klc4VFLGvJvO+03Us
        753Oxb869A8Z3P2fQplB+aX5B+PX9K82tm2rknr2oA3oJeVVentU7t9/ameOrVm2MmZMYzx1NajR7oiy
        YHGMHPNAFCeBWGeMgdRWdPAFi3ccir12rfcK4OexrPun2Qsgz9DQB1Xwshxp9/cnrJOE/BRn/wBmNdvX
        MfDeLy/CsL/89ZZH/wDHyP6V09ABXl/jK5F34qkQHK20ax/j1P8AMV6ZcSrBBJLIcIilifYV4xBK97fS
        3D/enkaQ+2T0oA2LS3LqHYgY5FXjBvQeS+SOqkZp1ogMS8EfSrD5HMTjPuOaAMtmCtmRGQ+qjIqrK6SS
        El246CtF5JdvzIretQXLosZbZg49KAM2aDzXjijyXmdY1+rHAr16CJYII4oxhI1CqPYDFeZ+DoGvfFMR
        YDyrZGm/H7o/9Cz+FennigDz/wCJd4HvbCxB+6DOw9+i/wDs1ZmmRFtoGcEZOKz9VuTqvie9nBynmeWn
        PZeP8TW9poWPdznHBNAGrbYSE8OBx90f1rM1dFmYkF/mY8nvWrOzLDhVyMetZczYX5TnHv0oAyJrcRIM
        5HHrWv4BtzLrc855WCLH/AmPH6BqzdQ+bLHtXUfDuHbp95MRy8+0fQKP6k0AdZRRSUAFFFFABRRRQAUU
        UUAFFFFABRRRQAhooooAKKKKAFoxRRQAUUUUAFFFFABRRRQAUUUUAAozRRQAtFJSigAooooA89+I+oEa
        paWI6GIyH8wK5tFyQAeB3rY+JMJPia1mHT7MVP8A31WNBuJAzxQBpQIpGCWHHJzircciRRfLMo7D5qZD
        BuAKMA2Oh5zRLDtI86NcDqVHWgBbiaNvmaQSSYxx0FY17IvO3JHritUWts4yFw+O/FZGpRrLJFZwZDTy
        rCMdixA/rQB6j4SjWLwzpaoMA26N+JGT+prWpkMawwpFGNqIoVR6AU+gDmviHeG18MXCKcPcEQj8ev6Z
        rgNIi+QMRg10HxQuPOvNPsVOQuZXH6D+tUNPhKovH40AbFkuQOwFOu+D8pwD3BqW0AVdynt3qCZhKvI5
        +tAFRN4zgBh9Kr32PLwR2q4AFUkDFUb4nymLGgDY+GcA/wCJlc+rrED9Bk/+hCuo8QXg0/Rb267xxMy+
        5xx+tZfw+hEfhuOTHzTSyOf++io/RRWf8U7sxaJBbA83EwB+g5/nigDidCj2w7ieT3rpkzHGCpPvxwaw
        tOTEMKDqecV0MMRb5XCsccdqALKSGQZDt07Gs+5bseSO4p9xbJyYyyt3FQpDKgJ3ZH+1zQBTmceQ5Y5O
        a73wdB5Hh20z1kBlPvuJI/QivN9QLAOV5IBx7mvXLOEW9pDCvSNFQfgMUAS0d6KSgBTSUGigAooooAKK
        KKACiiigAoopKAAmig0UAFAooFAC0UUUAFFFFABRRRQAUUUUAFFFFABRRRQAdqWkpaACigVDezrb2k0z
        nCohYn6CgDy7xfdm88Q3PzZSLES/h1/WqtupK8AcVRLtNcSSP1dyx/OtWyjPPdj+lAGlZooQ7k3ACgwI
        ZG2uy56DPFKAQoH3c9SGpJFjdwCw/wAKAFlt5EPEm7HQnBqhpSCbxdpUZG798W/75Ut/Sr7ghQFJOPfN
        VvCa7vGtqDzsjkb/AMdx/WgD1CgkAEnoKKwPHOoHT/DlyyNtlmHkp9W4z+AyfwoA8/vLo6vr91eHmMvt
        i/3BwPz6/jW1bjaoABIH4Vi6TEEiXb2rcjUFckNx2xQBfimKJjyyfbIqrM6kk4IPoah8xFBxhX9+KQXG
        eGJ+vUUAKXIXkY54INZ+qNi3c55Aq+5+XdwQe4rF1t9tpLgn7poA9L8KxeV4b0xcYJt0Y/UjJ/U1wvxN
        ufP16ztQciGPcfqx/wDrV6PZRiCygi6CONV/IV4zrNy2oeK9QnBO0SFB9F+X+lAGxo8e9y56LwK3gp28
        IcexrP0SHCjj8+9a88a7OMZPvQBmspBIDMD6EVG4kAO7OPWrRjkBPJ47dahmfKEMBx6GgDKaMXGpWsBG
        RJMiH6FhmvVxXmWhIJfFVgmMgOzn8EYj9cV6bQAUUUUAJVXUb63062M904VBwPUn0AqxNIkUTySsFRFL
        Mx6ADrXmt/eya9qRmY7bdTiJG6Kvqfc//WoA2ZfFl3NIfsdrGqdvMyx/Qirll4nYOF1G38pT/wAtEzgf
        UVRsII1YIi5I6+lXdRiMcGVY56nFAHTI6yIrowZWGQQcginVzHhTUMzvZORyGdB0xgjI/wDHgfzrp6AC
        iiigAoopKACigUHrQACikFLQACloooAKKKKACiiigAooooAKKKKACiiigBaM0UlAC1z/AI5uPI8PTgHD
        S4jH49f0rfrg/iZegPZ2ank5kP8AIf1oA460XLY9K2YMxjJwB6ms7TUJbgE/Sr87srfKu0+p5oAtfaCc
        ASEYH8K037Rg/Nu9iVqASOq/NNtz1BxTJbmTcAZNwHcJQBaeRWQlSufypnhCTb41tun7yORP/Hc/0qBr
        kN97YePTFJ4fHl+LtJmj5BkZSPTMbD+tAHrVebfFm9IutMsxyMtKR+g/ma9Jrx74jy/aPG8MWeIolH55
        NAF/SUC2wd2AIHSrYuFL4BOKrLcRW9qi5zxjgcCq8UzFvl8wr64oA2HEbJ8wPT+7VCaONXykoXHbNWoj
        cLyZDj3FPKJPnzVVm+lAGZ5pD7WPHqKqamokgZR/Fx+dTalC1rIGGQhOOe1DRb5rSMDO+eNfzcCgD0/V
        7pbLS7q5bgRRs/5CvFdBjMrGV+S53E+5r0f4oXRg8LSRKcPcSLEPxOT+gNcj4ftNkKccAd6AOi0+IxoO
        KtNKw4KoQe+aiiYhc56expwlPqp9jQBBOrZ3KcD0zVC5JCE960nkDKeADWVdrhT6etAFjwTF5niZpMcR
        wMfxJUD9M16JXD/DpAb7U5PRI1H5t/gK7g0AFJRSOwRWZiAoGSTQByHxA1MrFDpcDfvJ/ml56IO34n+R
        rCtY44LcksCx4xVKa5Ora5d3rE7GbCZ7IOBV2NwMjHPoaANzR8OcdB94n2o1e7AVlyMVStryKLd5mUJG
        BjpVC8ZHdis24EZ6UAWfDMjP4gtJQCELumfX5D/UCvRK8w8Klj4gs4S+5fOZgB2ARj/OvT6ACiiigApO
        1FLQAlJS5pvU0AKOtLSUtAC0UgpaACiiigAooooAKKKKACiiigApaSigApaO1FABXkHxCufM8WypniKF
        B9MkmvXzXivjYb/GGpdRhY/5GgCSwuVgGVBYkcAUPNPczEL8q9TWXp8+xsHGe2a0UMhkIUqu70oAuCMF
        N7HJHf1otZkmlO9gB05pXTMC+aEfHXDYqBrOKNRcQj5d2CrHNAGpcLbS6fgNH5mCRzz19KxNLmeG9tZu
        SYZ0b8Awz+lasdpE8crR4bjI4z2rG01C2o+WOTJcRoAPdgB/OgD26vEfEci3Hj+8JPyiQJ+S17axwhPt
        Xz3dTmXxHeTDJ3XT/lk0AdXqUqFIlU/KuOlWrAO4VreSNSP4XFUk3TJGEUbR6+taUcckUW5rRHHqDigB
        8lxMvzSw5BPLRnOPwqF72PeMSHj1GKjN3LGGQQyDPOO1MEskzj90me9AE+oXcF5bLCNpOM59Ki8Lj7Vr
        llbvkmKXeT7KCwP5gVW1IRwLv4zjkYrZ+Ftm0z3erSrhSTDFnuOCx/QD86AF+JswuNS0qwU52lp3Hp2H
        9fypdOhVUGB0rE1W6/tDxVfTg5PmfZ4vZU4P65rpLe2SCMGUEvjgZoAdMwUAbSPcVA7kKCV3L6A801vK
        8zHmqhz0zUjgrHxtk9weaAKpuEByMnHY1XuJCykY56/hTLgoZBtYhqZnKMzHPHWgDo/h0n+j6jJ6zBPy
        UH/2auwrmfh/Hs0ed8f6y4ZgfoFH9K6agArmfH+o/YtBkjRsTXJ8lfoev6Zrpq80+IVybrxDb2gOUt49
        xHu3/wBYUAUdItwkIZ8gVqwQbSHdeT0BqPT44xH5twp8tPur/ear9tG10XYHaMUAUb5AzbSeQO1YtwSo
        yv0ravQVkdSeRxWVfgeRnGAOaANLwGhuPEm8j5YIGbj+8SB/LNek1xfwztNtneXrDmaQRr/uqP8AFj+V
        dpQAUUUUAIaSlIzRQAlAFFFABSiigUALRRQKACiiigAooooAKKKKACiiigApRSUooAKKKKADtXinighv
        F+oHOcuB+QAr2mVtsbMewzXgd5OZ9TluDyZJGfP1YmgBxgIkLIPoK1rNleADy9zjsWwf5VWTJw3UHGTV
        wQjCyBSP92gBJBNvXMDD8c1LJK7QskVoyoTkktk0PM0oAXt6nFPhExHDZ3HB4oAmtLiS3iIEO0Ec5PX6
        1T8Iwi68W2yr0E5kb22ruH6gVNqtw4tvKBJf1IAq18MLXPiC4dgcwwEn/eYgD9A1AHpt0wS2lY8AKTXz
        zpSma7eUDOZGbn3Ne+64/l6Pev8A3YWP6V4T4aX9yH9aAOrtIWcKYiP92r9ys3lqNrphuQDkGq1gBuBU
        EjPIrUeJ5GUW29CnUlqAM7dOyjzI2ZV4AFOkn+XasJjOOoFWwlwquZWDHPQ//WqrN334BJ6igDB1pdtu
        8hcnCk4NeobIvDvhIhQAlpbEkjuQMk/ia821GNCoVRvLEDB75PSuw+LN79l8JyRKcNcyLF+Gcn9BQBw/
        hNoluIri7ckL8z57s3NdDJqC3d42JNiE8Zz0rjtIlZJQq8q4AI967e3s7d4E88DJHTigBl2hMQW3SNm7
        ljVRIbqMnaQnGDhqnktYIxmC4KN/dIyKY73KgBTE3+0p60AU74SQruYhsd6lkb/QhJ2x271BqUshhIlR
        fqDUcDMbIjBIK4H1oA9J8IQ+T4csR3dDL/30S39a2KitYlt7aKFBhY0CAewGKloAR2CqSeABmvIFlOpa
        1fXpJKySnaf9kcD9K9A8c6kdO0Cbyzie4/cR465bv+Aya4Cwi8mFYk44AoA07L96eRlV+VR6D1rfgIQJ
        wpUrjj2rO0m3KKSy/KRmrE6K0WTuVsYyp7CgDMvH8+aQpwinGTWFqtyFiZVOVx19a1HjkjVgGDJnJrMs
        LYap4msLQcoZAzj/AGV+Y/yx+NAHqPhmyOnaDZWzjEixgv8A7x5b9Sa06KKACiiigAooFFACGkFONJQA
        UopKUUAFFFFABRRRQAUUUUAFFFFABRRRQAYpaSlFACd6WiigCnrEph0q7kHVImP6V4DAS6w56hF5/Cvd
        fFLbfDuokf8APB/5V4Zp/wA6xH/YUfoKANy1GQuBWpawyOVVBwenPSsyzG0EHPqDjtXQWSYXdvP3emOP
        pQBU2vFkToRnowp1vnhV8z3woxWtIqvCgU5UknaarhFVum0detAGZeRogLEk/wC9Wl8Kfm1XXHPXbCB9
        MyVQ1LMowMbV/DNW/hgdniDUk7PArf8AfLY/9moA7PxhKIfDOoue0Lfyrxvw7GRbJjHI6V6n8S7gQeEr
        wZ5kxGPxNea6Suy3QAEHGc46EUAdJp8iY3ZYt6itFWPJkCjPUnrWTDICAQAoHpWjBgnJbcvv/hQBYkhT
        YMkk8nIfNU7lcLgA/UjNaBt1Ch9qf984NULzAOAMe1AGO0Jn1axgzzJcRrx6bhn9KtfGy5JfSrRepLSE
        fkB/M0/w8vm+MNN3dFZ2x7hGxWd8Sz9r8cQRdVggUn6kk0AUdItVQIWHp+ddhHbiez+b5sDg9xWJYRhU
        XjnvW7atthYxkow7djQBmSWU0bMQC6gcjPNV1ZVPzLIp/wBqtszMpYzowycZHSoJpI5Wwqj1oAzLqJZS
        EjIZR/FnrV7wzAt5r8FrjMVsvnvx6YwPzIP4Vn3sjRqwhUA+tdF8MrVlXUbuQlmd1jyfYZP/AKEKAO4o
        NFZviHU00nSpbp+XHyxp/ec9B/ntmgDivGt39u8QJADmGzXH/A26/kMfmaisLYlwT3qrp1nLMTNMS0kh
        LOfUnkmuisrZo0J2k4oAuWyCOIEdvwqhdvsdthx3wavedhdvb+VZ1++4HOOPegDE1e42R56fSrnwstDN
        f3+oOvEaiFD7nlv5D8657XptqFQa9N8F6YdJ8PW0LjEzjzZf95ucfgMD8KANyiiigAooooASloooADSG
        jqRSMKAFopKUUALRRRQAUUUUAFFFFABRRRQAtFIKM0ALiiigUAFFHejvQBk+Kxnw7qIPTyG/lXhmmHak
        Yxn5R/KvcvFxx4b1E/8ATB/5V4fpQLCHH90fyoA6K2C4T1PBHpWtYvtJUN6KQT1Oazo9vkxlAQ4bByam
        3qzHyskEcsB3oA1vOUSnHTOPl/xpJzv+VDgY5PpVOJjsyNynpkA5P1pVJVcvvxQA26QeUFAKg8kmrvw7
        VU8R3oP3jbDb9Awz/MVRkkV0JJqDR9YXRNba5eAyh4WiABxzlT/Q0AbnxXuAbWwslP7yWXeR/sqP8SK5
        S0jIXhccdKXVNSl8Raqb6RPKTAjjj3ZwAfX1P+FWbZQEPzbZB0z3oAt2qKuA4yrYJBOK2oANhCbSnfAH
        FYoZmGG6AcA1o2ikoFRtmOpx1oAvSSbeFO4+mKzb1wVJY88njt/nirJbaCJX5zxxWZcPHluQQc/5/lQA
        /wAJqW8X2xJ+6kjf+O4/rWPrDG98Z6nc9UWQQr/wEAH9c0+HWl0XUBeeS0r+W6RqOMucYz7dar6dEyw7
        5DukclmbuWPJNAGvB8rDkkdxita2cqmYRvJ5Kmsi1JyWBLY/MVsWyucEOuPc9KAFYAg71CD6cGqk4QYw
        BnHWrN1G5l+YqpBz6iqNySuckenAoAy9RdIkZz0Ar0rwrY/2foVrEy7ZWXzJB/tNyfy6fhXnen2h1PX7
        G16oZBJJ/uryfz6fjXrVABXn3iW6/tjXjbo2bWyO3jo0n8R/Dp+ddf4i1D+y9HubpQDIq4jB7seB+pFc
        DpCeRajcC0jHLE9ST1NAGnBCEGAcD64q8rAR5Vjx6c1jTXbLIsaryM5NWVYNH1yeuRQBLO+48np3FZt7
        Kygjd+NWpSQo3E7iOGrBv7gh2Vj07UAQ6Ra/2p4osrZhuj8ze4/2V5P8sfjXstecfC+yMl/fX7DiNRCh
        PqTlv5L+dej0AFFFFABRRRQAUUUUAFNY04000ALRRRQAtFFFADZJEijZ5GCooyWY4AFc1e+MbGFitsr3
        BHG4fKp/Hr+lYfxE1dnvItMhciNRvmwfvE9B+HX8RXNWsLuQxSQr6hTQB2w8YTORstI1X3cn+lW7XxUG
        x9otioz95Gz+hrmokGzCRuGB7oaiuZliwDIyn34oA9Fs9StLvAhlG7+63Bq5Xl6PNtDAZTGQa2tM8RTW
        4VJz5qdMMfmH0P8AjQB21FVrG+t76PfbuGx95e6/UVZoABRRRQAUCiigDH8ZEDwzqPvCw/MV4tYIUAx0
        HHFeq/Eu68jw60QbDTMFH8680sYyFB60AaW7KKCNvHUHrT0MhfEWM9aEYfdZSFzg+4psX+u+U4A+8aAL
        +1TDh5G39eOQTVkEqgUlF4zwMmqb3OyRfLjLn8gKUuZAx6+oVeB+JoAZKwG84XPqP6Vga9uaWGNsZZtv
        HYVpXN2IW+Q7z0xVK2h+1u0koyWOFx/DQBat4NkShMAAVdUAKN+5cdMHpUcW+IlJV3ADIIHWraEbgyMh
        TOCCKAGs/wA6lmccYBI61dt3kGTlCvvVaTcHYfKARyGp1tI8TYOfyyKALryLIpy21s5FZsxUZyRj171c
        nm8zG3d+VVZ4QwyzHj1oAxb5BLcQLjIB3ZrThiwARg98VnzTBr8AcAAgHtmtBSQDgE/Q0AXbOIlh5iEA
        91zzWku2NAYWbf0Jzwf/AK1UbCVTtKeZtX72eSK0ZEdkOwqI/wCHJoAqytIW+YcDg96z72YFTg1PMTA2
        GcvzyRWZePkEqOtAHQfDSJZtU1K5YZaONI1PoGLE/wDoK16DXGfC6IrpF7KcfvLo4/BVH8812LsERmY4
        AGTQBwPxG1ENqOn6ajZwfPkH6L/X8qghUtGMcZ649K5hrqTWfEl7f5JWSTbH/uDgfyrsrSFlQHb25oAp
        2TeXNNKSAy9BjOagln3O5hbae6+tWtehMLxz238Q+Yev5VkRq0zB1faSaALzTl4zu4Pv2rmNWnZX+Y81
        r30z265fawHQis3QbY+IPFNtbsv7kN5ko7bF5P58D8aAPUfA+nHTvDdqkgxNKPOk+rc4/AYH4VvUDgUU
        AFFFFABRRRQAUUneloATNN5z0pxpM80AIeB0zS0UtABSOcKT6ClpCMqQe9AHhN5eG81e+nkOXa4cDPoD
        j+WKvW4IXIO3+tYeuQvoviq/tJjiNpTIjH0bn/P0rQS7xEATQBuWsxBAMhU1rSyLfWfkykMw6HFcYLhm
        YY6Vq6c7f8tJMD1oA1NPimgDQTZODxz2rUlsYniHADEdqz4LxWRo95fBzn1FaUFz8gVsEdjQBm+XcadO
        s1u7Ky9CP6iuw0LXY9QURT7Y7ofw54b3H+FY02GQjg/Wsi7tmRt6ZyDnIPINAHpVFcx4Y8QfamFlfMBc
        j7jn/lp7fX+ddPQAUUUyVgkTMegGaAPM/iXeG51WK1Q5jgXLf7x/+t/OsKzXEOzAPfPpVO8vmudbu5ZD
        lZJD/Or0QKH5eR6DmgCQIwm2kZVu/vTY3+8QcGlzISFPGT3pqKI3OeSDkDtQBeieFYxuLKD1GM5pFuJp
        sxQoBGx71btLcSxB7zr0Vey0lwY7fHljknOPagCtLp6gkIN2O9RwRCI7RlWz0xzWhDIXR9pGAQWOeSPQ
        U2e0cxmRSu0k8A8g+1ADXUTDKnlBwcUwIVjMkOAerI39KggmI39Ny81NFMA0bk8L1A7f/WoAV5wYSWYg
        Y7jP4U62mAO3oQByAajuo0MQyQGB5Yd/epdPjbOROPTkZoAkknQkBSfcMMAfSoJ4TJGx3/KvYd60Xh81
        GV8FiMhgKoLmLdHjg9v8KAMyS2BiwM7/AOtPhfcoyMuDVkyLuHUDGOaZJD5ZEqAmM8tjt70AXYZhGyuS
        VcfxYyDUzSOMg5MTD+HGBUVs+VKrhuN3Xhh3+hqB2wxEJwOoB/lQAXPBwpyfTGKoXLAA9QKsl2lO1jtI
        6cVXkjbb8x60Adz8M2B8PSLx8lw4/kf61a8f6g1j4cuBESJ58Qx465bqfwGTXKeFtdGgrPFPbs8Mp8zK
        MNwOMdDweg71W1PV5/E2oxyGLybSAnykzkk92NAEXh6yFtGuB2rrYC6x71G7HbNZtrEEUAgYHetKMqsR
        AB3dBz1+lAGfeOGfdGevVcYrMuFUAkAxt6qatakuyXcpIVhyD2NZc8jFM7gQfWgDK1a4xGy5ya7H4S6b
        5dhc6lIvz3DeXH/ur1/M5/KuFvVeedIIRvnlYRoAOpJwK9t0exj0zS7Wzi+5DGEz6nufxOTQBcooooAK
        KKKACkNGaKACiikoADSUtITQAtFJmloABS0lFAHMeNvCFr4ltgxbyb2MYjlA/Q+orxfULTUfDl6bPU4m
        C/wt1BHqp7ivpCsrxHolpr2myWl2gOR8j45Q+ooA8Os5wcEHK561ca9KI2DjPGBWRfWdzoGqy6ffKQyn
        Ct0DDsRRA5d/n6k96AOm0edvODOCVJ5roVdg4jyOnHPWuTtJPLQEHjPQ1rJdFpd4bpg0AdDBK0eC9W5Q
        kiDHX61m+duiD4IBOATU0V0HcJGNwHU+9AGVqlnJCwmjYqV+YEHlSOhru/CesjWNNDSEC6i+WVR69j9D
        /jWBJGJ42WTpWJot22g+KYt5/wBFnPlSegBPB/A/pmgD1WsPxlefYvD91IDhmXaPqeK3K4D4tXnk2NhA
        TgSTjP4CgDza0GWlDH5lbnmt/TpMLg4x0JNc9cEw3gkTlWHPvW9YYmQEHCnnAoAtXOVZmX7gPX1z2qGJ
        j5isTnPSidv3YQENhutQW5G45Y8e9AG0rM2FUMec7mGMfSpCq+ScEnByciq0cwEZ2pKw/vA8D8KnbLwA
        ySR4A6A80AUY2KyvGjEKTxmrUDSHo5O37uDjJrLmkAlbbk9gatRXDMg4GCKACcqJHki69drdqihkZQm0
        EjrjPH+c0XrA4mQ8gYZc81BBLuVUU47qaALErbgSGIBP3av2CtjhVOR24NVmAIAIwo9OasW5CEBGYenH
        FAE8dw/mAZ3Kpxg9cUSqJDlccfez296qlyWLBcOpGQO4qSUsJN4bJI5P1oAY8TACQIRFng+9TgMHHGDj
        g5yCKkSbjnIBG3B/rVflHIJ+U8j1U/4UAJOphy0CExj78ZPQ+3tVR5wcMh257VqO6pGW2kjrx6elY0oB
        vCqnarcigCxIpdQ6HJHBBFTRoeN4wfSi1Hlq29MH1PYVNDEeZOMdR3oAzNbtWlaJl+7uwwHvWlpsKQxq
        oAAA4NF+jNbyDkHqKbYSiWEYwT3HcUAbcR6fxD0qQ/KuQ/HYqeR+FUraQP8AKeo6IeCfpUN04UE5LL7j
        DA0ANvH3qyOwLZODWJdzbI25HPUehq1PMpTIOawNRn64HvQB0fw5sDqPiNruQZhs13D03twP0z+les1y
        vw20z+z/AA3FI4xNdnz2z2B+6PyA/OuqoAKKKSgAzRRRQAUhoNNLUAGaCcVHnmng5NADs0lNOSKcPegB
        aUU2lFACmiiigBaKSjNAHJ/EXw2uv6MzQxqb6D5om7n1XPvXh8ErqzxygiVDhgetfTteJ/FbRF0zX47+
        FdsF398AcBu/50AYtkxZRvPFaUMgA3McKOMVh2UwjcFyMD1rQifzJC5G2POcetAG6s8t55aZARRwK1BK
        LOLy8jd+VY+mT+WDcScBR8i+pq9ZWkt/J50vAPPNAGtYyGWItu5681laxCs4IbB4xzWlGBEqoOnqKjvr
        cmLzBQB1HgXVW1LRxHcNuurU+VIT1Yfwt+I/UGuW+LkPm3OmsWGFZvl/Cq3hW+Ol+JoMtiC7/wBHkz/e
        /hP58f8AAjWL411oat4jbyTm3hzGhHRvU/j/ACoAyplDqMH7tWtLkEVwUxwRkCmpETg/3hipGjAkDR9R
        zmgC9OoCDhizdfQUyNGVsrETjnJHSrCH7Tb8cOOSKiZiAVLFSe1AGnGkhXzPOjU4+5kc/lUNwvy/NH5b
        D+LOQahguDtCH5fw5NTtOSp+cgqPl42k0AZdxkMOcGrVlPHjYRnnqF3H+dRTJhiQwyCMk9TmpbU7X2qw
        A65PH6UAS3EQMZlg5xw4xjj6VkW8oW4KdCDla6G4Mixgt5MgPpg4/A9q53UV8i6EkQYbTyCOQfSgDYiA
        MCsD83qDUsMhVgpYgewqnbzK8QK7cEcVNDsaTc6nP+yOKAFLbbhiGB3cAg1bkJKbm27vQjFU7nEcgZQB
        n05BpXmVCVGSnP0/KgDSETvCdu0ll3bc5zVOOXeDGy5x1z1FQrJJkFdynGAQ3amxyFny78/dJoAuTERI
        Cc7WGODxWXaEG/VT/CwFXNRISDbuBBHWqGk4FyDKTtJ5OM4oA6Lykw+XGe+Bn8M0Bl/5Z5POD9KGmTcw
        DgggZOcAmqm7lm7E44oAuyEvjHOR0HQ1i7fs16Q3ybjwfetS1cIuSeAfWlvrU3UZkXBdcHNAAMGI5I6c
        hv8AGqdxdSD5S5dffrUtqzCDdgKAcMM9DWRqD43NHwR6UAJcyghip61R0y0bVtbtLEAsJpAHx2Qcsfyz
        UVxMZE966v4SWfnave3rDiCMRqfdjn+S/rQB6pGgjRUUYVRgAelOpM0UAFFFFABSUUgoARz2qJjycVI1
        RmgBB2p46+9Rc59qlQYGaAHjpRRTSeaAHUopKWgAziiiigBRRRRQAtcv8RdF/trw5MkYHnxfvI/qO35V
        09DAMpB5BoA+XoZThQ45Q4Oa17SXzXJY4jHXnpVz4iaKuieJJDEpW1uf3i+gPcVh28vzhf4aAOs07E0g
        eQHyx9xK6qA+TDuJHPrXI+HnM0/P3Aea6VpRdy4wSoPA6A0ASPMEjDEhiTwKiLtLHhwOfenpAkkoA2nb
        1xwv/wBeq2rXKW0JCqM9sHGaAOd147ZFQMc5zjPSsiBC1yc1NLKZ712Y5I4qaKL5ycdOaANG1Py4buKn
        WE43pzjrUEGFGc984Par8SkKxViDQBDGApyCVYUy6Ct8yABx1wasLEJCxTAZeuelDRCNf3yFXHDBuPoR
        QBUtpipI4DA9fSrvnICpYRydsDkn8KzJspLjge1XLXbJhXGFznmgBtwyFyqAgBs4YYP5U+AjOFIDdOhJ
        q55MIR1k3KTzHtOVYfnxVZoUViJPlIPAxQBo2RiAzsYOF+baDj6kenTpWbqds0oaaBQUQbGxjr/nHNOR
        NykeYcDpgZqrPcNAxRlXPTIHWgCnZHY5T+E+/StSzdlICEZPfqazUGHLrjbnBGfWrUZCyLnOOmBQBNdc
        yOC4J6mmRBSigSAODxmr9ySYW2rEUzgLnJH0qiscjITgjaem3NAEk2+KKU7Fzx82eDn0qOBxsIIYODwM
        U0zbbcqDgk7SnPK+v/1qdDFI8oCrk9h1JoAk1ZW8tAVOWAIzVK0Zo2Xdleeop925F0qyZAHGD2p7qUaO
        UAj0LDg0AaUMrlRlwW7AjrStG5BYcDuT0/8ArVWjlCIodDwc5HYe1XY5yWxwQONwH86AKH2nyZQCMH37
        1t2VwHiKL0A3DntWPq1ufKSZFOOxHQ/SorO6CooJww5GKALV9Kkd0xU/I3DCsa8kXLAH6fSrepPl9wOQ
        fesmZss3pQBVboQORXpvwgH/ABJr9sAE3OM/8AWvNXUYOWA+lev/AA3sTY+FbcuMPcMZz9D0/QCgDqKK
        SigAzRSZooADSZpc0lADG69abUmKWgCHHNS444owKM4FAC00jnmlphNAElFFFAC0UlLQAtApKXtQAtFJ
        S0Acn8RvD665oblSFuIMyI2PzH414RC5ClSMMvBr6hdQ6lWGQRzXgvxG0RdD8QF4EItbjLqPQ9xQAzRJ
        QlvtU43EV0KXZRBGpHzccd64zTpMZB/hFdD4dlE12ZXwVQ4XPtQB07n7Ja4PEhGSa43Xb4vOctnAz7V0
        MrtdStuJKqM7R2+tcPfuZp5WHQsQMelADtMy+9j1JzWvCvIPqKz9OXYfStiFQCMjjpQAkakHJ6elaERJ
        HDZb0Peq5Gw5YcEcVKTuJwADjjFAFlkDRs0XDjggfrUTtI6srksAu3DckDI4HpUsDluGxvwOD3+lMuG3
        n5sgr0J/lQBl3CZYYIBA7mn287bQjYABzuAyTS3Sl+S3zdwTUA3RuGGTnigDS85f7gKnqT1H1NCF5DtV
        ct6+1RoQ3BwB6r3pwhYSZA56jHf+lAEwMu4JKuVxwA3b2zUiy2y/6y3DsD8+epH40wu6uu0Attx90AD+
        hquzBpipGG6bW4I/E9qAM6eMLNm34B6DP6VLGxABPsPfNWzDHIq/Iwk5KsOD6/jVOTImJYlwD1xjJ96A
        Ne3mjaRRIjnIGXd+T+OcVAJGSclS4bOOO46c1FayohwR1OOg4/PikuP3U53OM+sZyMUAXZohfSbthikA
        zuQHOffJqupmtWaCRkQ54PqPrUhMk0W5JlZsAAA4GPekjLpGWm2qT0yvX8e9AFLUZGmYSOAXH8Qq1bv5
        tvgt8gbJjA5+o7VBeqpZSoyD+P4+9T2vlyndESjd19Pp7UARS25yDbtvzzgcMD9KWCWQkrly4HzL3IrQ
        iEcLkyxLKR0YZA/H9Kq6gjSFWUlJMZG8/N+fegBEvSoMLNujOTtasm4/cynYdyH5h7VDdSsk7ecMMeQB
        0qxavE0JWQY+tACvKJUGDVaaPjoeaaGMe4DlQeDUqz7h0+bNAFZoWcjjA717f4TmE3hrTHHaBV/IY/pX
        jpO0HJ+Y9AK9V+H8iv4VswpJKF0bPY7z/iKAOiPNHakNFABRRRQAUUUUAJmlpDQelABn86ToKQHIpCaA
        EJ5pCeaKQ+1AE9FFFABRRRQAuaSiigBwNFNpRQAtch8TNGGqeHpXVczQDzEOPTtXX1HcRrNC8bDIYYNA
        HzLZyFVI74wa3dCnEFq7sQO/1ql4n0xtG8QXNsf9WW3J9DUdtJ/oYX1cfzoA6t7lbXRpJnJ8yUfmTXLx
        IGHTk1Y1m53tHCpJRFHHoTToI8xnPHpQA62UpyO1a9qMpkjIxk1moBg8fjWpYyAxjOepHHvQBJcPuwM5
        B5GB3pI8riNgOOhpxQ5BIO0cg0kmVYlgSp+6RQA8/K2CcnPB9KZNKzABh2xkD69aRkLY5yfenE7XUbjy
        MMf89aAKsp8zjlSe5pjo8ZyrZwKsOhZW2gMB+FRNjI8s8jt1xQAQpuGckenNWkaSA7SQwPfvVcOefM4H
        XIHWpoyHUhGx/td8fSgC2JIxFlWQkYwMd6rSq0vEkZMg7k449Oakgxn5n8tsYAxwanljnUK6IpGOT/Xm
        gDLMgSQiN8p0IYYPX9amlHmsNxXd/F6VIfKllUyIFI6gcU26t5I5PvK2eee35UAFtbQAkz26uCequQfw
        pJ7ddpCNKgzwpPAp1u+3d5qMzA888VJKcBsgRntg5BoAz7d54nPDOn8RQ9a1baSOVSsRk3nkbPm7dMda
        zyCVPJ3Hn61KkO6PcV/eY+9nbQA28jjMrkODzkFeKijtZChdZQuOgOQT9MUrxsrFGkbj17U2N5V+4WPr
        z2oAtWc8i72NxJHMMdQCDRdXjrKTJtlPQgoAD+VV2uAI/L8v3Y1AoErls4HYUANuMSAkqMH+E81SMRB+
        Q7fTmrkikAFuahGA2cUAV3R14AyBREXHGOhqw8yqMAg9sCqN3c7JMKSD1oA0EZUJZzxj8q9H+Fwc6FcS
        N/qnuGMf0AAP6g14200s2FJOK9t+Ge0eDrMJ1DSbvrvagDqaKKKACiikBoAWkNGcU1moAUnFIaM4ooAT
        FJSk02gANIaUmkoAmBzS0UUAFFFFABS4oooABRRRQAuaKKKAPI/jNaql5ZXIA3NlT715/E2Ix7MKKKAJ
        wPM81mPOf5VtQx5hBB5IoooAacKQv61csffv6eo70UUAaDOAgIHAPT60DasO1uxwMe3vRRQBDypweSOh
        pZSGG8KAQeQOhoooAjlkK4KgD6VXyE+Yjg9hRRQBKuGC4AU+3epIoVUsDww5BFFFAD5t6JukKsucHAqZ
        5SIo2XKxsOBnPGcdD0oooAbKWUkSbSpG7AHamh1SINEXyx70UUAR2rGRwUVQevPSrZiDxkuiM+fp/Kii
        gCndxvGANwK9dp5H+eajUyFRgg57HP8AjRRQBFLMySDzMliOSG6/mKes5Kbfw5AP6jFFFABKrIATtYHn
        nNQtGu4bdwz6miigCO5j2ybVY/jVd1K45oooAozO4XcD3wKjS33nc7ZPWiigCRIwpB/EcV7B8Mc/8IrG
        c8GWQj86KKAOtFITRRQAmaXtRRQAwnNIetFFAC96TNFFACE0h4FFFACHmjjHFFFAH//Z
</value>
  </data>
  <data name="pbRightHand.Location" type="System.Drawing.Point, System.Drawing">
    <value>488, 32</value>
  </data>
  <data name="pbRightHand.Size" type="System.Drawing.Size, System.Drawing">
    <value>136, 104</value>
  </data>
  <data name="pbRightHand.SizeMode" type="System.Windows.Forms.PictureBoxSizeMode, System.Windows.Forms">
    <value>StretchImage</value>
  </data>
  <data name="pbRightHand.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="&gt;&gt;pbRightHand.Name" xml:space="preserve">
    <value>pbRightHand</value>
  </data>
  <data name="&gt;&gt;pbRightHand.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pbRightHand.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pbRightHand.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="labelFing2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 12pt, style=Bold</value>
  </data>
  <data name="labelFing2.Location" type="System.Drawing.Point, System.Drawing">
    <value>576, 48</value>
  </data>
  <data name="labelFing2.Size" type="System.Drawing.Size, System.Drawing">
    <value>16, 16</value>
  </data>
  <data name="labelFing2.TabIndex" type="System.Int32, mscorlib">
    <value>40</value>
  </data>
  <data name="labelFing2.Text" xml:space="preserve">
    <value>x</value>
  </data>
  <data name="labelFing2.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="labelFing2.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelFing2.Name" xml:space="preserve">
    <value>labelFing2</value>
  </data>
  <data name="&gt;&gt;labelFing2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelFing2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelFing2.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="labelFing6.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 12pt, style=Bold</value>
  </data>
  <data name="labelFing6.Location" type="System.Drawing.Point, System.Drawing">
    <value>536, 152</value>
  </data>
  <data name="labelFing6.Size" type="System.Drawing.Size, System.Drawing">
    <value>16, 16</value>
  </data>
  <data name="labelFing6.TabIndex" type="System.Int32, mscorlib">
    <value>34</value>
  </data>
  <data name="labelFing6.Text" xml:space="preserve">
    <value>x</value>
  </data>
  <data name="labelFing6.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="labelFing6.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelFing6.Name" xml:space="preserve">
    <value>labelFing6</value>
  </data>
  <data name="&gt;&gt;labelFing6.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelFing6.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelFing6.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="labelFing7.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 12pt, style=Bold</value>
  </data>
  <data name="labelFing7.Location" type="System.Drawing.Point, System.Drawing">
    <value>520, 160</value>
  </data>
  <data name="labelFing7.Size" type="System.Drawing.Size, System.Drawing">
    <value>16, 16</value>
  </data>
  <data name="labelFing7.TabIndex" type="System.Int32, mscorlib">
    <value>35</value>
  </data>
  <data name="labelFing7.Text" xml:space="preserve">
    <value>x</value>
  </data>
  <data name="labelFing7.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="labelFing7.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelFing7.Name" xml:space="preserve">
    <value>labelFing7</value>
  </data>
  <data name="&gt;&gt;labelFing7.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelFing7.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelFing7.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="labelFingerMarker.Font" type="System.Drawing.Font, System.Drawing">
    <value>Kristen ITC, 12pt, style=Bold</value>
  </data>
  <data name="labelFingerMarker.Location" type="System.Drawing.Point, System.Drawing">
    <value>608, 8</value>
  </data>
  <data name="labelFingerMarker.Size" type="System.Drawing.Size, System.Drawing">
    <value>16, 16</value>
  </data>
  <data name="labelFingerMarker.TabIndex" type="System.Int32, mscorlib">
    <value>43</value>
  </data>
  <data name="labelFingerMarker.Text" xml:space="preserve">
    <value>O</value>
  </data>
  <data name="labelFingerMarker.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleCenter</value>
  </data>
  <data name="&gt;&gt;labelFingerMarker.Name" xml:space="preserve">
    <value>labelFingerMarker</value>
  </data>
  <data name="&gt;&gt;labelFingerMarker.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelFingerMarker.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelFingerMarker.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="toolTip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>100, 17</value>
  </metadata>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>55</value>
  </metadata>
  <data name="$this.AutoScaleBaseSize" type="System.Drawing.Size, System.Drawing">
    <value>5, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>632, 446</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAIAICAQAAAAAADoAgAAJgAAABAQEAAAAAAAKAEAAA4DAAAoAAAAIAAAAEAAAAABAAQAAAAAAAAC
        AAAAAAAAAAAAABAAAAAQAAAAAAAAAAAAgAAAgAAAAICAAIAAAACAAIAAgIAAAICAgADAwMAAAAD/AAD/
        AAAA//8A/wAAAP8A/wD//wAA////AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AA7u7u7u4ADg7gAAAAAAAAAA7gDuAO4A7g7gAAAAAAAAAO4A7gAO4AAO4AAAAAAAAADuAO4ADuAO7uAA
        AAAAAAAA7gDuAA7g7u4AAAAAAAAAAO4A7gAO4O4AAAAAAAAAAADuAO4A7gDuDuAAAAAAAAAO7u7u7uAA
        DuDgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AHAHAAAAAAAAAAAAAAAHAAAAAAAAcAAAAAAAAAAAAHcAd3cAdwAAAAAAAAAAAAB3B3d3cHcAAAAAAAAA
        AAAAdwcHd3B3AAAAAAAAAAAAAHcH4HdwdwAAAAAAAAAAAAAAAHd3AAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAAcAAAAAAAAAAAAAAAAABwBwAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA////////
        /////////////////////////gB0//8zMn//M55//zOYf/8zkP//M5P//zMyf/4AeX//////////////
        /////D///4AB//+AAf//gAH//4IB//+BAf//sA3//7gd//+AAf//yZP///w/////////////////////
        //8oAAAAEAAAACAAAAABAAQAAAAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAgAAAAICAAIAA
        AACAAIAAgIAAAICAgADAwMAAAAD/AAD/AAAA//8A/wAAAP8A/wD//wAA////AAAAAAAAAAAAB3d3d3d3
        d3dERERERERER0////////hHT///////+EdP///////4R0////////hHT///////+EdP///////4R0//
        //////hHT///////+EdIiIiIiIiIR0zMzMzMzMxHxERERERERMAAAAAAAAAAAAAAAAAAAAAA//8AAIAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAD//wAA//8AAA==
</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>CrossMatch Capture Dialog</value>
  </data>
  <data name="&gt;&gt;toolTip1.Name" xml:space="preserve">
    <value>toolTip1</value>
  </data>
  <data name="&gt;&gt;toolTip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolTip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>CrossMatchMain</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>