using System;
using DCSDEV;
//using DCSSDK.CaptureMgt;
using System.Collections;
using System.Windows.Forms;

using System.IO;
using System.Runtime.Serialization.Formatters.Binary;
using System.Runtime.Serialization;

namespace DCSDEV.FIPS
{
	/// <summary>
	/// Summary description for DCSFormula.
	/// </summary>
	internal class FIPS_Tools
	{
		internal FIPS_Tools()
		{
			//
			// TODO: Add constructor logic here
			//
		}
		internal static int Reverse(int val)
		{
			uint v = (uint) val;
			return (int)((v>>24) + ((v>>8) & 0x0000FF00) + ((v<<8) & 0x00FF0000) + ((v<<24) & 0xFF000000));
		}
		internal static short Reverse(short val)
		{
			ushort v = (ushort)val;
			return (short)((v>>8) + (v<<8));
		}
	}

	internal class FIPS_FACESTD
	{
		private const int MAX_HISTORY = 3;	// FIPS maximum might require a lower number to make things fit
		private FileStream m_fs;
		private BinaryWriter m_w;
		private BinaryReader m_r;
		private ArrayList m_arrayFilenames = new ArrayList();
		private int[] m_arrayFileLens = new int[MAX_HISTORY+1];

		// CBEFF header
		private short	CBEFF_format_owner = 0x001b;
		private short	CBEFF_format_type  = 0x0501;

		// facial header
		private int	facial_header_format_identifier		= 0x46414300;
		private int	facial_header_version_number		= 0x30313000;
		private int	facial_header_record_length			= -1;
		private short facial_header_number_images		= -1;
		private int sizeof_facial_header = 14;
		
		// facial info 1-N facial images
		private int facial_block_length					= -1;
		private short facial_number_feature_points		= 0;
		private byte[] facial_gender					= {0};
		private byte[] facial_eye_color					= {0};
		private byte[] facial_hair_color				= {0};
		private byte[] facial_feature_mask				= {0,0,0};
		private byte[] facial_expression				= {0,0};
		private byte[] facial_pose_angles				= {0,0,0};	//yaw pitch roll
		private byte[] facial_pose_angle_uncertainty	= {0,0,0};
		private int sizeof_facial_info = 20;
		
		// features 0-N instances
		private byte[] facial_feature_block				= {0,0,0,0,0,0,0,0};
		private int sizeof_facial_feature_block = 8;
		
		// image info
		private byte[] facial_image_type				= {1};	// syh ? clauses 7,8,9,
		private byte[] image_data_type					= {1};	// jpg
		private short image_width						= -1;
		private short image_height						= -1;
		private byte[] image_color_space				= {1};	// rgb
		private byte[] image_source_type				= {2};	// digital still camera
		private short image_device_type					= 0;
		private short image_quality						= 0;
		private int sizeof_image_info = 12;
		// image data

		internal FIPS_FACESTD()
		{
			DCSDEV.ParameterStore ps = new ParameterStore("DCSSDK_Mgt");
		}

		// SYH note file extensions are assumed to be JPG - there is no check or correction for other types
		internal void Populate(string strPortraitID)
		{
			string strFilename;
			m_arrayFilenames.Clear();

			System.IO.FileInfo fi;
			int i;
			for (i = 0; i <= MAX_HISTORY; i++)
			{
				strFilename = DCSDatabaseIF.GetFullNameOfHistoricImage(strPortraitID, DCSDEV.DCSDatabaseIF.ImageClass.Portrait, 0, i, true);
				if (strFilename == null) break;
				fi = new FileInfo(strFilename);

				m_arrayFilenames.Add(strFilename);
				m_arrayFileLens[i] = (int)fi.Length;
			}

			facial_header_number_images = (short)this.m_arrayFilenames.Count;
			int len = sizeof_facial_header; 
			for (i=0; i<facial_header_number_images; i++)
			{
				len = len 
					+ sizeof_facial_info 
					+ sizeof_facial_feature_block * facial_number_feature_points
					+ sizeof_image_info
					+ this.m_arrayFileLens[i];
			}
			facial_header_record_length = len;
		}

		internal bool Write(string filename)
		{
			m_fs = new FileStream(filename, FileMode.Create);
			// Create the writer for data.
			m_w = new BinaryWriter(m_fs);


			BinaryIO_All(true);

			m_w.Close();
			m_fs.Close();

			return true;
		}

		// SYH note file extensions are assumed to be JPG
		internal bool Read(string filename, string strPortraitID)
		{
			// delete all portrait images - current and historic
			DCSDatabaseIF.DeleteStoredImages(strPortraitID, DCSDatabaseIF.ImageClass.Portrait, -1);

			// Populate array of destination image file names
			string strFilename;
			m_arrayFilenames.Clear();
			int i = 0;
			strFilename = DCSDatabaseIF.GetFullNameOfHistoricImage(strPortraitID, DCSDEV.DCSDatabaseIF.ImageClass.Portrait, 0, 0, false);
			for (i = 0; i <= MAX_HISTORY; i++)
			{
				string strTemp;
				strTemp = Path.Combine(Path.GetDirectoryName(strFilename), Path.GetFileNameWithoutExtension(strFilename) + "_J" + i.ToString() + Path.GetExtension(strFilename)) ;
				m_arrayFilenames.Add(strTemp);
			}

			// Create the reader for data.
			m_fs = new FileStream(filename, FileMode.Open, FileAccess.Read);
			m_r = new BinaryReader(m_fs);

			// Read data from file.
			int numRead = BinaryIO_All(false);

			m_r.Close();
			m_fs.Close();

			// the suffix are now adjusted
			string strRead;
			string strAdj;
			int jAdj;
			strFilename = DCSDatabaseIF.GetFullNameOfHistoricImage(strPortraitID, DCSDEV.DCSDatabaseIF.ImageClass.Portrait, 0, 0, false);
			for (int j = 0; j < numRead; j++)
			{
				strRead = Path.Combine(Path.GetDirectoryName(strFilename), Path.GetFileNameWithoutExtension(strFilename) + "_J" + j.ToString() + Path.GetExtension(strFilename));
				if (j == 0)
					strAdj = strFilename;
				else
				{
					jAdj = numRead - j;
					strAdj = Path.Combine(Path.GetDirectoryName(strFilename), Path.GetFileNameWithoutExtension(strFilename) + "_H" + jAdj.ToString() + Path.GetExtension(strFilename));
				}
				System.IO.File.Move(strRead, strAdj);
			}
			return true;
		}

		// return number of files
		private int BinaryIO_All(bool bWrite)
		{
			int i,k;

			// CBEFF
			this.BinaryIO_16(bWrite, ref CBEFF_format_owner);
			this.BinaryIO_16(bWrite, ref CBEFF_format_type);
			// facial header
			this.BinaryIO_32(bWrite, ref facial_header_format_identifier);
			this.BinaryIO_32(bWrite, ref facial_header_version_number);
			this.BinaryIO_32(bWrite, ref facial_header_record_length);
			this.BinaryIO_16(bWrite, ref facial_header_number_images);

			// facial info 1-N facial images
			for (k=0; k<facial_header_number_images; k++)
			{
				if (bWrite)
				{
					System.Drawing.Bitmap bitmap = new System.Drawing.Bitmap((string)this.m_arrayFilenames[k]);
					image_width = (short)bitmap.Width;
					image_height = (short)bitmap.Height;
					bitmap.Dispose();

					facial_block_length 
						= sizeof_facial_info 
						+ sizeof_facial_feature_block * facial_number_feature_points
						+ sizeof_image_info + this.m_arrayFileLens[k];
				}
				
				this.BinaryIO_32(bWrite, ref facial_block_length);
				this.BinaryIO_16(bWrite, ref facial_number_feature_points);
				this.BinaryIO_Bytes(bWrite, ref facial_gender);
				this.BinaryIO_Bytes(bWrite, ref facial_eye_color);
				this.BinaryIO_Bytes(bWrite, ref facial_hair_color);
				this.BinaryIO_Bytes(bWrite, ref facial_feature_mask);
				this.BinaryIO_Bytes(bWrite, ref facial_expression);
				this.BinaryIO_Bytes(bWrite, ref facial_pose_angles);
				this.BinaryIO_Bytes(bWrite, ref facial_pose_angle_uncertainty);

				// features 0-N instances
				for (i=0; i<facial_number_feature_points; i++)
					this.BinaryIO_Bytes(bWrite, ref facial_feature_block);

				// image info
				this.BinaryIO_Bytes(bWrite, ref facial_image_type);
				this.BinaryIO_Bytes(bWrite, ref image_data_type);
				this.BinaryIO_16(bWrite, ref image_width);
				this.BinaryIO_16(bWrite, ref image_height);
				this.BinaryIO_Bytes(bWrite, ref image_color_space);
				this.BinaryIO_Bytes(bWrite, ref image_source_type);
				this.BinaryIO_16(bWrite, ref image_device_type);
				this.BinaryIO_16(bWrite, ref image_quality);

				// image data
				if (bWrite)
					this.BinaryIO_FileWrite((string)this.m_arrayFilenames[k], this.m_arrayFileLens[k]);
				else
				{
					int len
						= facial_block_length 
						- sizeof_facial_info 
						- sizeof_facial_feature_block * facial_number_feature_points
						- sizeof_image_info;
					this.BinaryIO_FileRead((string)this.m_arrayFilenames[k], len);
				}
			}
			return k;
		}

		private void BinaryIO_16(bool bWrite, ref short val)
		{
			if (bWrite) m_w.Write(FIPS_Tools.Reverse(val));		// Write data to file.
			else val = FIPS_Tools.Reverse(m_r.ReadInt16());		// Read data from file.
		}
		private void BinaryIO_32(bool bWrite, ref int val)
		{
			if (bWrite) m_w.Write(FIPS_Tools.Reverse(val));		// Write data to file.
			else val = FIPS_Tools.Reverse(m_r.ReadInt32());		// Read data from file.
		}
		private void BinaryIO_Bytes(bool bWrite, ref byte[] val)
		{
			int i;
			int len = val.Length;

			if (bWrite)
			{
				for (i=0; i<len; i++)
					m_w.Write(val[i]);		// Write data to file.
			}
			else
			{
				for (i=0; i<len; i++)
					val[i] = m_r.ReadByte();		// Read data from file.
			}
		}
		private void BinaryIO_FileWrite(string strFilename, int len)
		{
			byte[] buf = new byte[len];
			FileStream fs = new FileStream(strFilename, FileMode.Open, FileAccess.Read);
			BinaryReader br = new BinaryReader(fs);
			for (int i=0; i<len; i++)
				buf[i] = br.ReadByte();		// Read data from file.

			m_w.Write(buf, 0, len);
			br.Close();
			fs.Close();
		}
		private void BinaryIO_FileRead(string strFilename, int len)
		{
			byte[] buf = new byte[len];
			for (int i=0; i<len; i++)
				buf[i] = m_r.ReadByte();		// Read data from file.

			// Create the writer for data.
			FileStream fs = new FileStream(strFilename, FileMode.Create);
			BinaryWriter bw = new BinaryWriter(fs);

			bw.Write(buf, 0, len);
			bw.Close();
			fs.Close();
		}
	}
}