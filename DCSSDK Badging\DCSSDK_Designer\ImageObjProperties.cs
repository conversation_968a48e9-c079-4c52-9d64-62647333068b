using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;

using DCSDEV;
namespace DCSDEV.DCSDesigner
{
	/// <summary>
	/// Summary description for ImageObjProperties.
	/// </summary>
    internal class ImageObjProperties : System.Windows.Forms.Form
	{
		private ArrayList m_DesignObjectsSelected;
		private DCSDEV.DCSDesigner.DCSDesignerView m_view;
		private DCSDEV.DCSDesign.DCSDesign m_design;

		private System.Windows.Forms.Button buttonCancel;
		private System.Windows.Forms.Button buttonAccept;
		private System.Windows.Forms.Button buttonApply;
		private System.Windows.Forms.TabControl tabControl1;
		private System.Windows.Forms.TabPage tabPage1;
		private System.Windows.Forms.TabPage tabPage2;
		private System.Windows.Forms.ColorDialog colorDialog1;
		private DCSDEV.DCSDesigner.DCSSourceProperties dcsSourceProperties1;
		private DCSDEV.DCSDesigner.DCSPositionSizeProperties dcsPositionSizeProperties1;
		private System.Windows.Forms.TabPage tabPage3;
		private DCSDEV.DCSDesigner.DCSFrameProperties1 dcsFrameProperties1;
		private System.Windows.Forms.CheckBox checkGray;
		private System.Windows.Forms.ComboBox comboBoxScaling;
		private System.Windows.Forms.Label label1;
		private System.Windows.Forms.TabPage tabPage4;
		private System.Windows.Forms.GroupBox groupBox1;
		private System.Windows.Forms.PictureBox pictureBoxDetectColor;
		private System.Windows.Forms.NumericUpDown numericUpDownRange;
		private System.Windows.Forms.Button buttonChooseColor;
		private System.Windows.Forms.ComboBox comboBoxDetection;
		private System.Windows.Forms.NumericUpDown numericUpDownTransparency;
		private System.Windows.Forms.Label label4;
		private System.Windows.Forms.Label label2;
		private System.Windows.Forms.ImageList imageList1;
		private System.Windows.Forms.Button buttonLockAspectRatio;
		private System.Windows.Forms.TextBox tbRatio;
		private DCSDEV.DCSDesigner.DCSAlignmentProperties dcsAlignmentProperties1;
		private DCSDEV.DCSDesigner.DCSBackGroundProperties dcsBackGroundProperties1;
		private DCSDEV.DCSDesigner.DCSRotationProperties dcsRotationProperties1;
		private System.Windows.Forms.Button buttonRotateCW;
		private System.Windows.Forms.Button buttonRotateCCW;
		private System.Windows.Forms.ToolTip toolTip1;
		private System.Windows.Forms.ComboBox comboBoxFraming;
		private System.Windows.Forms.Label label5;
        private System.Windows.Forms.Label labelRange;
        private ComboBox comboBoxPanel;
        private Label labelPanel;
		private System.ComponentModel.IContainer components;

        public ImageObjProperties(DCSDEV.DCSDesign.DCSDesign activeDoc, DCSDEV.DCSDesigner.DCSDesignerView activeView, ArrayList designObjectsSelected, bool bNew)
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			m_view = activeView;
			m_design = activeDoc;

			if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.PortraitAutoKey, false))
				this.comboBoxDetection.Items.Add("PortraitAuto");

			m_DesignObjectsSelected = designObjectsSelected;
			if (m_DesignObjectsSelected.Count > 0)
			{
				DCSDEV.DCSDesign.DCSDesignObject designObject = (DCSDEV.DCSDesign.DCSDesignObject)m_DesignObjectsSelected[0];
				if (bNew &&designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ImageObj 
					&& designObject.SourceType == DCSDEV.DCSDatatypes.SourceTypes.StaticValue)
					designObject.DesignObjectImageName = "select image";
				this.MoveDataToDlg(designObject);

				this.AdjustVisibilities();

				if (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ImageObj)
					this.Text = "Image Object Properties";
				else if (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Portrait)
					this.Text = "Portrait Object Properties";
				else if (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Signature)
					this.Text = "Signature Object Properties";
				else if (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Fingerprint)
					this.Text = "Fingerprint Object Properties";
			}

            DCSDEV.PrintProperties.PrinterTypeDatum bcDatum = (DCSDEV.PrintProperties.PrinterTypeDatum)this.m_view.m_mainWin.m_printertypeArray[m_design.PrinterTypeIndex];
            this.comboBoxPanel.Visible = this.labelPanel.Visible = false;   // bcDatum.m_IfKpanel;
            
            this.toolTip1.SetToolTip(this.buttonRotateCCW, "Rotate object and its frame 90 degrees counter clockwise");
			this.toolTip1.SetToolTip(this.buttonRotateCW,  "Rotate object and its frame 90 degrees clockwise");
			this.toolTip1.SetToolTip(this.buttonLockAspectRatio, "Toggle preservation of aspect ratio while resizing");
			this.toolTip1.SetToolTip(this.buttonChooseColor, "Select chroma key color in the foreground image where the background will show.");
			this.toolTip1.SetToolTip(this.buttonApply,  "Save changes, apply changes to display, and keep dialog open.");
			this.toolTip1.SetToolTip(this.buttonAccept,  "Save changes, close dialog, and apply changes to display.");
			this.toolTip1.SetToolTip(this.buttonCancel,  "Undo changes and close dialog.");
			// syh seems not to work this.toolTip1.SetToolTip(this.numericUpDownTransparency, "Enter 0 if foreground is opaque; enter number to 99 to indicate percent transparent");
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ImageObjProperties));
            this.buttonCancel = new System.Windows.Forms.Button();
            this.buttonAccept = new System.Windows.Forms.Button();
            this.buttonApply = new System.Windows.Forms.Button();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.dcsRotationProperties1 = new DCSDEV.DCSDesigner.DCSRotationProperties();
            this.dcsAlignmentProperties1 = new DCSDEV.DCSDesigner.DCSAlignmentProperties();
            this.checkGray = new System.Windows.Forms.CheckBox();
            this.comboBoxScaling = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.tabPage4 = new System.Windows.Forms.TabPage();
            this.numericUpDownTransparency = new System.Windows.Forms.NumericUpDown();
            this.label4 = new System.Windows.Forms.Label();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.pictureBoxDetectColor = new System.Windows.Forms.PictureBox();
            this.numericUpDownRange = new System.Windows.Forms.NumericUpDown();
            this.labelRange = new System.Windows.Forms.Label();
            this.buttonChooseColor = new System.Windows.Forms.Button();
            this.comboBoxDetection = new System.Windows.Forms.ComboBox();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.dcsBackGroundProperties1 = new DCSDEV.DCSDesigner.DCSBackGroundProperties();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.label5 = new System.Windows.Forms.Label();
            this.comboBoxFraming = new System.Windows.Forms.ComboBox();
            this.dcsFrameProperties1 = new DCSDEV.DCSDesigner.DCSFrameProperties1();
            this.colorDialog1 = new System.Windows.Forms.ColorDialog();
            this.tbRatio = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.buttonLockAspectRatio = new System.Windows.Forms.Button();
            this.imageList1 = new System.Windows.Forms.ImageList(this.components);
            this.buttonRotateCW = new System.Windows.Forms.Button();
            this.buttonRotateCCW = new System.Windows.Forms.Button();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.comboBoxPanel = new System.Windows.Forms.ComboBox();
            this.labelPanel = new System.Windows.Forms.Label();
            this.dcsSourceProperties1 = new DCSDEV.DCSDesigner.DCSSourceProperties();
            this.dcsPositionSizeProperties1 = new DCSDEV.DCSDesigner.DCSPositionSizeProperties();
            this.tabControl1.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.tabPage4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownTransparency)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBoxDetectColor)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownRange)).BeginInit();
            this.tabPage2.SuspendLayout();
            this.tabPage3.SuspendLayout();
            this.SuspendLayout();
            // 
            // buttonCancel
            // 
            this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
            resources.ApplyResources(this.buttonCancel, "buttonCancel");
            this.buttonCancel.Name = "buttonCancel";
            this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
            // 
            // buttonAccept
            // 
            resources.ApplyResources(this.buttonAccept, "buttonAccept");
            this.buttonAccept.Name = "buttonAccept";
            this.buttonAccept.Click += new System.EventHandler(this.buttonAccept_Click);
            // 
            // buttonApply
            // 
            resources.ApplyResources(this.buttonApply, "buttonApply");
            this.buttonApply.Name = "buttonApply";
            this.buttonApply.Click += new System.EventHandler(this.buttonApply_Click);
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Controls.Add(this.tabPage4);
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Controls.Add(this.tabPage3);
            resources.ApplyResources(this.tabControl1, "tabControl1");
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.dcsRotationProperties1);
            this.tabPage1.Controls.Add(this.dcsAlignmentProperties1);
            this.tabPage1.Controls.Add(this.checkGray);
            this.tabPage1.Controls.Add(this.comboBoxScaling);
            this.tabPage1.Controls.Add(this.label1);
            resources.ApplyResources(this.tabPage1, "tabPage1");
            this.tabPage1.Name = "tabPage1";
            // 
            // dcsRotationProperties1
            // 
            this.dcsRotationProperties1.Cursor = System.Windows.Forms.Cursors.Default;
            resources.ApplyResources(this.dcsRotationProperties1, "dcsRotationProperties1");
            this.dcsRotationProperties1.Name = "dcsRotationProperties1";
            this.dcsRotationProperties1.RotateFlip = System.Drawing.RotateFlipType.RotateNoneFlipNone;
            // 
            // dcsAlignmentProperties1
            // 
            resources.ApplyResources(this.dcsAlignmentProperties1, "dcsAlignmentProperties1");
            this.dcsAlignmentProperties1.Name = "dcsAlignmentProperties1";
            // 
            // checkGray
            // 
            resources.ApplyResources(this.checkGray, "checkGray");
            this.checkGray.Name = "checkGray";
            // 
            // comboBoxScaling
            // 
            this.comboBoxScaling.Items.AddRange(new object[] {
            resources.GetString("comboBoxScaling.Items"),
            resources.GetString("comboBoxScaling.Items1"),
            resources.GetString("comboBoxScaling.Items2")});
            resources.ApplyResources(this.comboBoxScaling, "comboBoxScaling");
            this.comboBoxScaling.Name = "comboBoxScaling";
            this.comboBoxScaling.SelectedIndexChanged += new System.EventHandler(this.comboBoxScaling_SelectedIndexChanged);
            // 
            // label1
            // 
            resources.ApplyResources(this.label1, "label1");
            this.label1.Name = "label1";
            // 
            // tabPage4
            // 
            this.tabPage4.Controls.Add(this.numericUpDownTransparency);
            this.tabPage4.Controls.Add(this.label4);
            this.tabPage4.Controls.Add(this.groupBox1);
            resources.ApplyResources(this.tabPage4, "tabPage4");
            this.tabPage4.Name = "tabPage4";
            // 
            // numericUpDownTransparency
            // 
            resources.ApplyResources(this.numericUpDownTransparency, "numericUpDownTransparency");
            this.numericUpDownTransparency.Name = "numericUpDownTransparency";
            this.numericUpDownTransparency.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
            // 
            // label4
            // 
            resources.ApplyResources(this.label4, "label4");
            this.label4.Name = "label4";
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.pictureBoxDetectColor);
            this.groupBox1.Controls.Add(this.numericUpDownRange);
            this.groupBox1.Controls.Add(this.labelRange);
            this.groupBox1.Controls.Add(this.buttonChooseColor);
            this.groupBox1.Controls.Add(this.comboBoxDetection);
            resources.ApplyResources(this.groupBox1, "groupBox1");
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.TabStop = false;
            // 
            // pictureBoxDetectColor
            // 
            this.pictureBoxDetectColor.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            resources.ApplyResources(this.pictureBoxDetectColor, "pictureBoxDetectColor");
            this.pictureBoxDetectColor.Name = "pictureBoxDetectColor";
            this.pictureBoxDetectColor.TabStop = false;
            // 
            // numericUpDownRange
            // 
            resources.ApplyResources(this.numericUpDownRange, "numericUpDownRange");
            this.numericUpDownRange.Name = "numericUpDownRange";
            // 
            // labelRange
            // 
            resources.ApplyResources(this.labelRange, "labelRange");
            this.labelRange.Name = "labelRange";
            // 
            // buttonChooseColor
            // 
            resources.ApplyResources(this.buttonChooseColor, "buttonChooseColor");
            this.buttonChooseColor.Name = "buttonChooseColor";
            this.buttonChooseColor.Click += new System.EventHandler(this.buttonChooseColor_Click);
            // 
            // comboBoxDetection
            // 
            this.comboBoxDetection.Items.AddRange(new object[] {
            resources.GetString("comboBoxDetection.Items"),
            resources.GetString("comboBoxDetection.Items1"),
            resources.GetString("comboBoxDetection.Items2")});
            resources.ApplyResources(this.comboBoxDetection, "comboBoxDetection");
            this.comboBoxDetection.Name = "comboBoxDetection";
            this.comboBoxDetection.SelectedIndexChanged += new System.EventHandler(this.comboBoxDetection_SelectedIndexChanged);
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.dcsBackGroundProperties1);
            resources.ApplyResources(this.tabPage2, "tabPage2");
            this.tabPage2.Name = "tabPage2";
            // 
            // dcsBackGroundProperties1
            // 
            this.dcsBackGroundProperties1.AllDBFieldNames = null;
            this.dcsBackGroundProperties1.BackFillType = DCSDEV.DCSDatatypes.BackFillTypes.FILL_COLOR;
            this.dcsBackGroundProperties1.Color = System.Drawing.Color.White;
            this.dcsBackGroundProperties1.Color2 = System.Drawing.Color.Yellow;
            this.dcsBackGroundProperties1.ColorCondition1 = null;
            this.dcsBackGroundProperties1.ColorCondition2 = null;
            this.dcsBackGroundProperties1.ConditionalColor1 = System.Drawing.Color.Empty;
            this.dcsBackGroundProperties1.ConditionalColor2 = System.Drawing.Color.Empty;
            this.dcsBackGroundProperties1.ConditionalColor3 = System.Drawing.Color.Empty;
            this.dcsBackGroundProperties1.Filename = "";
            this.dcsBackGroundProperties1.GradientType = System.Drawing.Drawing2D.LinearGradientMode.Horizontal;
            resources.ApplyResources(this.dcsBackGroundProperties1, "dcsBackGroundProperties1");
            this.dcsBackGroundProperties1.Name = "dcsBackGroundProperties1";
            this.dcsBackGroundProperties1.RestrictGradiantAndImage = false;
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.label5);
            this.tabPage3.Controls.Add(this.comboBoxFraming);
            this.tabPage3.Controls.Add(this.dcsFrameProperties1);
            resources.ApplyResources(this.tabPage3, "tabPage3");
            this.tabPage3.Name = "tabPage3";
            // 
            // label5
            // 
            resources.ApplyResources(this.label5, "label5");
            this.label5.Name = "label5";
            // 
            // comboBoxFraming
            // 
            this.comboBoxFraming.Items.AddRange(new object[] {
            resources.GetString("comboBoxFraming.Items"),
            resources.GetString("comboBoxFraming.Items1"),
            resources.GetString("comboBoxFraming.Items2"),
            resources.GetString("comboBoxFraming.Items3"),
            resources.GetString("comboBoxFraming.Items4"),
            resources.GetString("comboBoxFraming.Items5")});
            resources.ApplyResources(this.comboBoxFraming, "comboBoxFraming");
            this.comboBoxFraming.Name = "comboBoxFraming";
            // 
            // dcsFrameProperties1
            // 
            this.dcsFrameProperties1.Color = System.Drawing.Color.Gray;
            resources.ApplyResources(this.dcsFrameProperties1, "dcsFrameProperties1");
            this.dcsFrameProperties1.Name = "dcsFrameProperties1";
            this.dcsFrameProperties1.Radius = 0;
            this.dcsFrameProperties1.Thickness = 3;
            // 
            // tbRatio
            // 
            resources.ApplyResources(this.tbRatio, "tbRatio");
            this.tbRatio.Name = "tbRatio";
            this.tbRatio.ReadOnly = true;
            // 
            // label2
            // 
            resources.ApplyResources(this.label2, "label2");
            this.label2.Name = "label2";
            // 
            // buttonLockAspectRatio
            // 
            resources.ApplyResources(this.buttonLockAspectRatio, "buttonLockAspectRatio");
            this.buttonLockAspectRatio.ImageList = this.imageList1;
            this.buttonLockAspectRatio.Name = "buttonLockAspectRatio";
            this.buttonLockAspectRatio.Click += new System.EventHandler(this.buttonLockAspectRatio_Click);
            // 
            // imageList1
            // 
            this.imageList1.ImageStream = ((System.Windows.Forms.ImageListStreamer)(resources.GetObject("imageList1.ImageStream")));
            this.imageList1.TransparentColor = System.Drawing.Color.Transparent;
            this.imageList1.Images.SetKeyName(0, "");
            this.imageList1.Images.SetKeyName(1, "");
            // 
            // buttonRotateCW
            // 
            resources.ApplyResources(this.buttonRotateCW, "buttonRotateCW");
            this.buttonRotateCW.Name = "buttonRotateCW";
            this.buttonRotateCW.Click += new System.EventHandler(this.buttonRotate_Click);
            // 
            // buttonRotateCCW
            // 
            resources.ApplyResources(this.buttonRotateCCW, "buttonRotateCCW");
            this.buttonRotateCCW.Name = "buttonRotateCCW";
            this.buttonRotateCCW.Click += new System.EventHandler(this.buttonRotate_Click);
            // 
            // comboBoxPanel
            // 
            this.comboBoxPanel.FormattingEnabled = true;
            this.comboBoxPanel.Items.AddRange(new object[] {
            resources.GetString("comboBoxPanel.Items"),
            resources.GetString("comboBoxPanel.Items1")});
            resources.ApplyResources(this.comboBoxPanel, "comboBoxPanel");
            this.comboBoxPanel.Name = "comboBoxPanel";
            // 
            // labelPanel
            // 
            resources.ApplyResources(this.labelPanel, "labelPanel");
            this.labelPanel.Name = "labelPanel";
            // 
            // dcsSourceProperties1
            // 
            this.dcsSourceProperties1.Cursor = System.Windows.Forms.Cursors.Default;
            this.dcsSourceProperties1.DBField = "";
            this.dcsSourceProperties1.DesignObjectType = DCSDEV.DCSDatatypes.DCSDesignObjectTypes.TextObj;
            this.dcsSourceProperties1.ForegroundImageName = "";
            this.dcsSourceProperties1.Formula = "";
            this.dcsSourceProperties1.Instance = 0;
            resources.ApplyResources(this.dcsSourceProperties1, "dcsSourceProperties1");
            this.dcsSourceProperties1.MaxInstances = 10;
            this.dcsSourceProperties1.Name = "dcsSourceProperties1";
            this.dcsSourceProperties1.SourceType = -1;
            this.dcsSourceProperties1.StaticText = "";
            this.dcsSourceProperties1.VisibleIf = false;
            this.dcsSourceProperties1.VisibleIfCondition = null;
            // 
            // dcsPositionSizeProperties1
            // 
            this.dcsPositionSizeProperties1.DisplayBounds = new System.Drawing.Rectangle(0, 0, 0, 0);
            resources.ApplyResources(this.dcsPositionSizeProperties1, "dcsPositionSizeProperties1");
            this.dcsPositionSizeProperties1.Name = "dcsPositionSizeProperties1";
            this.dcsPositionSizeProperties1.Units = 0;
            // 
            // ImageObjProperties
            // 
            this.AcceptButton = this.buttonAccept;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.CancelButton = this.buttonCancel;
            resources.ApplyResources(this, "$this");
            this.Controls.Add(this.labelPanel);
            this.Controls.Add(this.comboBoxPanel);
            this.Controls.Add(this.buttonRotateCW);
            this.Controls.Add(this.buttonRotateCCW);
            this.Controls.Add(this.buttonLockAspectRatio);
            this.Controls.Add(this.tbRatio);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.dcsPositionSizeProperties1);
            this.Controls.Add(this.dcsSourceProperties1);
            this.Controls.Add(this.tabControl1);
            this.Controls.Add(this.buttonApply);
            this.Controls.Add(this.buttonCancel);
            this.Controls.Add(this.buttonAccept);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "ImageObjProperties";
            this.ShowInTaskbar = false;
            this.tabControl1.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.tabPage4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownTransparency)).EndInit();
            this.groupBox1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.pictureBoxDetectColor)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownRange)).EndInit();
            this.tabPage2.ResumeLayout(false);
            this.tabPage3.ResumeLayout(false);
            this.ResumeLayout(false);
            this.PerformLayout();

		}
		#endregion

		private bool ApplyData()
		{
			// check validity of the data
			if (this.dcsBackGroundProperties1.BackFillType == DCSDEV.DCSDatatypes.BackFillTypes.FILL_IMAGE)
			{
				string strFullname = DCSDEV.DCSDesignDataAccess.ExpandImageName(this.dcsBackGroundProperties1.Filename, true);
				if (strFullname == null)
				{
					DCSDEV.DCSMsg.Show(String.Format("Background image file '{0}' does not exist", this.dcsBackGroundProperties1.Filename));
					return false;
				}
			}
			if (this.dcsSourceProperties1.DesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ImageObj
				&& this.dcsSourceProperties1.SourceType == (int)DCSDEV.DCSDatatypes.SourceTypes.StaticValue)
			{
				// image must be specified and must exist
				if (this.dcsSourceProperties1.ForegroundImageName == null || this.dcsSourceProperties1.ForegroundImageName.Length == 0
					|| DCSDEV.DCSDesignDataAccess.ExpandImageName(this.dcsSourceProperties1.ForegroundImageName, true) == null)
				{
					DCSDEV.DCSMsg.Show(String.Format("Foreground image file '{0}' does not exist", this.dcsSourceProperties1.ForegroundImageName));
					return false;
				}
			}

			// All check pass .  It should be safe to procede
			DCSDEV.DCSDesign.DCSDesignObject designObject = (DCSDEV.DCSDesign.DCSDesignObject)m_DesignObjectsSelected[0];
			this.MoveDataFromDlg(designObject);
			
			if (designObject.BackImage != null) designObject.BackImage.Dispose();	// FILL_IMAGE is the only case where a back image should exist
			if (designObject.BackFillType == DCSDEV.DCSDatatypes.BackFillTypes.FILL_IMAGE)
			{
				try
				{
					designObject.BackImage = (Bitmap)DCSDEV.DCSDesignDataAccess.GetImage(designObject.BackImageName, false);
				}
				catch (Exception ex)
				{
					DCSDEV.DCSMsg.Show(ex);
				}
			}
			if (designObject.DesignObjectImage != null) designObject.DesignObjectImage.Dispose();
			if (designObject.DesignObjectImageName != null && designObject.DesignObjectImageName.Length != 0)
			{
				try
				{
					designObject.DesignObjectImage = (Bitmap)DCSDEV.DCSDesignDataAccess.GetImage(designObject.DesignObjectImageName, false);
				}
				catch (Exception ex)
				{
					DCSDEV.DCSMsg.Show(ex);
					return false;
				}
			}
			return true;
		}

		private void AdjustVisibilities()
		{
			this.pictureBoxDetectColor.Visible = (this.comboBoxDetection.SelectedIndex == 1);
			this.buttonChooseColor.Visible = (this.comboBoxDetection.SelectedIndex == 1);

			this.labelRange.Visible = this.numericUpDownRange.Visible = (this.comboBoxDetection.SelectedIndex != 0);

			if (this.comboBoxScaling.SelectedIndex == (int)DCSDEV.DCSDatatypes.ScaleMode.ScaleToFit)
				this.dcsAlignmentProperties1.Visible = false;
			else
				this.dcsAlignmentProperties1.Visible = true;
		}

		private void MoveDataToDlg(DCSDEV.DCSDesign.DCSDesignObject designObject)
		{
			// source
			this.dcsSourceProperties1.DesignObjectType = designObject.DCSDesignObjectType;
			this.dcsSourceProperties1.SourceType = (int)designObject.SourceType;
			//this.dcsSourceProperties1.StaticText = designObject.Text;
			this.dcsSourceProperties1.DBField = designObject.SourceName;
			this.dcsSourceProperties1.ForegroundImageName = designObject.DesignObjectImageName;
			this.dcsSourceProperties1.AllDBFieldNames = this.m_view.m_mainWin.m_AllDBFieldNames;
			this.dcsSourceProperties1.Instance = designObject.ObjectTypeInstance;
			this.dcsSourceProperties1.Formula = designObject.Formula;
			this.dcsSourceProperties1.VisibleIf = designObject.VisibleIf;
			this.dcsSourceProperties1.VisibleIfCondition = designObject.VisibleIfCondition;

			DCSDEV.ParameterStore ps = new DCSDEV.ParameterStore("DCSSDK_Mgt");
			switch(designObject.DCSDesignObjectType)
			{
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Portrait:
					this.dcsSourceProperties1.MaxInstances = ps.GetIntParameter("PortraitInstances", 1);
					break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Signature:
					this.dcsSourceProperties1.MaxInstances = ps.GetIntParameter("SignatureInstances", 1);
					break;
				case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Fingerprint:
					this.dcsSourceProperties1.MaxInstances = ps.GetIntParameter("FingerprintInstances", 1);
					break;
			}

			// position size
			this.dcsPositionSizeProperties1.DisplayBounds = designObject.Bounds;
			this.dcsPositionSizeProperties1.Units = DCSDEV.DCSDesignDataAccess.GetUnits();

			// background detection
			if (!designObject.BackDetectEnabled)
				this.comboBoxDetection.SelectedIndex = 0;
			else if (designObject.AutoKey)
				this.comboBoxDetection.SelectedIndex = 2;
			else if (designObject.PortraitAutoKey)
			{
				if (DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.PortraitAutoKey, false))
					this.comboBoxDetection.SelectedIndex = 3;
				else
				{
					designObject.PortraitAutoKey = false;
					designObject.AutoKey = true;
					this.comboBoxDetection.SelectedIndex = 2;
				}
			}
			else
				this.comboBoxDetection.SelectedIndex = 1;

			this.pictureBoxDetectColor.BackColor = designObject.ColorToDetect;
			this.numericUpDownRange.Value = designObject.ColorDetectThreshold;
			
			// background properties
			this.dcsBackGroundProperties1.BackFillType = designObject.BackFillType;
			this.dcsBackGroundProperties1.Filename = designObject.BackImageName;

			this.dcsBackGroundProperties1.Color = designObject.BackColor;
			this.dcsBackGroundProperties1.Color2 = designObject.BackColor2;
			this.dcsBackGroundProperties1.GradientType = designObject.BackGradientType;
			this.dcsBackGroundProperties1.AllDBFieldNames = this.m_view.m_mainWin.m_AllDBFieldNames;
			this.dcsBackGroundProperties1.ConditionalColor1 = designObject.ColorChoice1;
			this.dcsBackGroundProperties1.ConditionalColor2 = designObject.ColorChoice2;
			this.dcsBackGroundProperties1.ConditionalColor3 = designObject.ColorChoice3;
			this.dcsBackGroundProperties1.ColorCondition1 = designObject.ColorCondition1;
			this.dcsBackGroundProperties1.ColorCondition2 = designObject.ColorCondition2;

			// frame properties
			this.dcsFrameProperties1.Thickness = designObject.LineWidth;
			this.dcsFrameProperties1.Radius = designObject.Radius;
			this.dcsFrameProperties1.Color = designObject.LineColor;

			// alignment / rotation
			this.dcsAlignmentProperties1.SetAlignment(designObject.Justification, designObject.Alignment);
			this.dcsRotationProperties1.RotateFlip = designObject.RotateFlip;

			// other
			this.checkGray.Checked = designObject.GrayScale;
			this.comboBoxScaling.SelectedIndex = (int)designObject.Scaling;
			this.comboBoxFraming.SelectedIndex = (int)designObject.Framing;
			this.numericUpDownTransparency.Value = designObject.Transparency;
			this.buttonLockAspectRatio.ImageIndex = designObject.IfLockAspect ? 1 : 0;
            this.comboBoxPanel.Text = designObject.SpecialKPanel == 0 ? "default" : "K panel";
			this.tbRatio.Text = ((double)designObject.Bounds.Width / (double)designObject.Bounds.Height).ToString("0.000");
		}
		private void MoveDataFromDlg(DCSDEV.DCSDesign.DCSDesignObject designObject)
		{
			// source
			//designObject.DCSDesignObjectType = this.dcsSourceProperties1.DesignObjectType; not editable
			designObject.SourceType = (DCSDEV.DCSDatatypes.SourceTypes)this.dcsSourceProperties1.SourceType;
			if (this.dcsSourceProperties1.MaxInstances > 1)
				designObject.ObjectTypeInstance = this.dcsSourceProperties1.Instance;
			else
				designObject.ObjectTypeInstance = 0;
			//designObject.Text = this.dcsSourceProperties1.StaticText;
			designObject.SourceName = this.dcsSourceProperties1.DBField;
			designObject.Formula = this.dcsSourceProperties1.Formula;
			designObject.VisibleIf = this.dcsSourceProperties1.VisibleIf;
			designObject.VisibleIfCondition = this.dcsSourceProperties1.VisibleIfCondition;
			if (designObject.DesignObjectImageName != this.dcsSourceProperties1.ForegroundImageName)
			{
				designObject.DesignObjectImageName = this.dcsSourceProperties1.ForegroundImageName;
				if (designObject.DesignObjectImage != null)
				{
					designObject.DesignObjectImage.Dispose();
					designObject.DesignObjectImage = null;
				}
			}

			// position size
			designObject.Bounds = this.dcsPositionSizeProperties1.DisplayBounds;
			DCSDEV.DCSDesignDataAccess.SetUnits(this.dcsPositionSizeProperties1.Units);

			// background detection
			designObject.BackDetectEnabled = (this.comboBoxDetection.SelectedIndex != 0);
			designObject.AutoKey = (this.comboBoxDetection.SelectedIndex == 2);
			designObject.PortraitAutoKey = (this.comboBoxDetection.SelectedIndex == 3);
			designObject.ColorToDetect = this.pictureBoxDetectColor.BackColor;
			designObject.ColorDetectThreshold = (int)this.numericUpDownRange.Value;

			// background properties
			designObject.BackFillType	= this.dcsBackGroundProperties1.BackFillType;
			designObject.BackImageName = this.dcsBackGroundProperties1.Filename;
			designObject.BackColor		= this.dcsBackGroundProperties1.Color;

			designObject.BackColor2 = this.dcsBackGroundProperties1.Color2;
			designObject.BackGradientType   = this.dcsBackGroundProperties1.GradientType;
			designObject.ColorChoice1 = this.dcsBackGroundProperties1.ConditionalColor1;
			designObject.ColorChoice2 = this.dcsBackGroundProperties1.ConditionalColor2;
			designObject.ColorChoice3 = this.dcsBackGroundProperties1.ConditionalColor3;
			designObject.ColorCondition1 = this.dcsBackGroundProperties1.ColorCondition1;
			designObject.ColorCondition2 = this.dcsBackGroundProperties1.ColorCondition2;

			// frame properties
			designObject.LineWidth		= this.dcsFrameProperties1.Thickness;
			designObject.Radius			= this.dcsFrameProperties1.Radius;
			designObject.LineColor		= this.dcsFrameProperties1.Color;

			// alignment / rotation
			int hj, vj;
			this.dcsAlignmentProperties1.GetAlignment(out hj, out vj);
			designObject.Justification = (DCSDEV.DCSDatatypes.Justifications)vj;
			designObject.Alignment = (DCSDEV.DCSDatatypes.Alignments)hj;
			designObject.RotateFlip = this.dcsRotationProperties1.RotateFlip;

			// other
			designObject.GrayScale = this.checkGray.Checked;
			designObject.Scaling = (DCSDEV.DCSDatatypes.ScaleMode)this.comboBoxScaling.SelectedIndex;
			designObject.Framing = (DCSDEV.DCSDatatypes.FramingMode)this.comboBoxFraming.SelectedIndex;
			designObject.Transparency = (int)this.numericUpDownTransparency.Value;
			designObject.IfLockAspect = (this.buttonLockAspectRatio.ImageIndex == 1);
            designObject.SpecialKPanel = (this.comboBoxPanel.Text == "default") ? 0 : 1;
		}

		private void buttonAccept_Click(object sender, System.EventArgs e)
		{
			bool bRet = this.ApplyData();
			if (bRet) 
			{
				this.DialogResult = System.Windows.Forms.DialogResult.OK;
				this.Close();
			}
		}

		private void buttonCancel_Click(object sender, System.EventArgs e)
		{
			this.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.Close();
		}

		private void buttonApply_Click(object sender, System.EventArgs e)
		{
			this.ApplyData();
			m_design.m_isDirty = true;
			m_design.m_isViewDirty = true;
			m_view.Invalidate(true);
		}

		private void buttonChooseColor_Click(object sender, System.EventArgs e)
		{
			this.colorDialog1.Color = this.pictureBoxDetectColor.BackColor;
			System.Windows.Forms.DialogResult result = this.colorDialog1.ShowDialog(this);
			if (result != DialogResult.OK) return;
			this.pictureBoxDetectColor.BackColor = this.colorDialog1.Color;
		}

		private void comboBoxDetection_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			this.AdjustVisibilities();
		}

		private void buttonLockAspectRatio_Click(object sender, System.EventArgs e)
		{
			this.buttonLockAspectRatio.ImageIndex = (this.buttonLockAspectRatio.ImageIndex+1) % 2;		
		}

		private void comboBoxScaling_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			this.AdjustVisibilities();
		}

		private void buttonRotate_Click(object sender, System.EventArgs e)
		{
			Rectangle rect = this.dcsPositionSizeProperties1.DisplayBounds;
			int width = rect.Width;
			rect.Width = rect.Height;
			rect.Height = width;
			this.dcsPositionSizeProperties1.DisplayBounds = rect;
			if (sender == this.buttonRotateCCW)
				this.dcsRotationProperties1.Rotate270();
			else
				this.dcsRotationProperties1.Rotate90();
			this.dcsRotationProperties1.Invalidate();
			this.tabControl1.SelectedIndex = 0;
		}

		public int LastTab
		{
			get { return this.tabControl1.SelectedIndex; }
			set { this.tabControl1.SelectedIndex = value; }
		}
	}
}
