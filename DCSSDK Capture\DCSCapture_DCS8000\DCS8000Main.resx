<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonFromFile.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="buttonFromFile.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonFromFile.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="buttonFromFile.Location" type="System.Drawing.Point, System.Drawing">
    <value>464, 376</value>
  </data>
  <data name="buttonFromFile.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 24</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonFromFile.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="buttonFromFile.Text" xml:space="preserve">
    <value>Get from &amp;File</value>
  </data>
  <data name="&gt;&gt;buttonFromFile.Name" xml:space="preserve">
    <value>buttonFromFile</value>
  </data>
  <data name="&gt;&gt;buttonFromFile.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonFromFile.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonFromFile.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="buttonAcquire.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="buttonAcquire.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="buttonAcquire.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonAcquire.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonAcquire.Location" type="System.Drawing.Point, System.Drawing">
    <value>464, 408</value>
  </data>
  <data name="buttonAcquire.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 24</value>
  </data>
  <data name="buttonAcquire.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="buttonAcquire.Text" xml:space="preserve">
    <value>&amp;Take</value>
  </data>
  <data name="&gt;&gt;buttonAcquire.Name" xml:space="preserve">
    <value>buttonAcquire</value>
  </data>
  <data name="&gt;&gt;buttonAcquire.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAcquire.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAcquire.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="buttonClose.AccessibleDescription" xml:space="preserve">
    <value>shut down camera and exit</value>
  </data>
  <data name="buttonClose.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="buttonClose.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonClose.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonClose.Location" type="System.Drawing.Point, System.Drawing">
    <value>560, 408</value>
  </data>
  <data name="buttonClose.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 24</value>
  </data>
  <data name="buttonClose.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="buttonClose.Text" xml:space="preserve">
    <value>&amp;Cancel</value>
  </data>
  <data name="&gt;&gt;buttonClose.Name" xml:space="preserve">
    <value>buttonClose</value>
  </data>
  <data name="&gt;&gt;buttonClose.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonClose.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonClose.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="tbCameraStatus.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 12pt, style=Bold</value>
  </data>
  <data name="tbCameraStatus.Location" type="System.Drawing.Point, System.Drawing">
    <value>232, 16</value>
  </data>
  <data name="tbCameraStatus.Size" type="System.Drawing.Size, System.Drawing">
    <value>280, 19</value>
  </data>
  <data name="tbCameraStatus.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="tbCameraStatus.Text" xml:space="preserve">
    <value>Uninitialized</value>
  </data>
  <data name="&gt;&gt;tbCameraStatus.Name" xml:space="preserve">
    <value>tbCameraStatus</value>
  </data>
  <data name="&gt;&gt;tbCameraStatus.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbCameraStatus.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbCameraStatus.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="labelCameraType.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 12pt</value>
  </data>
  <data name="labelCameraType.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelCameraType.Location" type="System.Drawing.Point, System.Drawing">
    <value>32, 16</value>
  </data>
  <data name="labelCameraType.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 16</value>
  </data>
  <data name="labelCameraType.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="labelCameraType.Text" xml:space="preserve">
    <value>DCS800x:</value>
  </data>
  <data name="&gt;&gt;labelCameraType.Name" xml:space="preserve">
    <value>labelCameraType</value>
  </data>
  <data name="&gt;&gt;labelCameraType.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelCameraType.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelCameraType.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="buttonAbout.AccessibleDescription" xml:space="preserve">
    <value>shut down camera and exit</value>
  </data>
  <data name="buttonAbout.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="buttonAbout.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonAbout.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonAbout.Location" type="System.Drawing.Point, System.Drawing">
    <value>464, 344</value>
  </data>
  <data name="buttonAbout.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 24</value>
  </data>
  <data name="buttonAbout.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="buttonAbout.Text" xml:space="preserve">
    <value>&amp;About</value>
  </data>
  <data name="&gt;&gt;buttonAbout.Name" xml:space="preserve">
    <value>buttonAbout</value>
  </data>
  <data name="&gt;&gt;buttonAbout.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAbout.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAbout.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="pictureBox1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="pictureBox1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="pictureBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 48</value>
  </data>
  <data name="pictureBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>432, 360</value>
  </data>
  <data name="pictureBox1.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="&gt;&gt;pictureBox1.Name" xml:space="preserve">
    <value>pictureBox1</value>
  </data>
  <data name="&gt;&gt;pictureBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pictureBox1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pictureBox1.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="buttonDefaults.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="buttonDefaults.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonDefaults.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonDefaults.Location" type="System.Drawing.Point, System.Drawing">
    <value>560, 344</value>
  </data>
  <data name="buttonDefaults.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 24</value>
  </data>
  <data name="buttonDefaults.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="buttonDefaults.Text" xml:space="preserve">
    <value>Defaults</value>
  </data>
  <data name="&gt;&gt;buttonDefaults.Name" xml:space="preserve">
    <value>buttonDefaults</value>
  </data>
  <data name="&gt;&gt;buttonDefaults.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonDefaults.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonDefaults.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="buttonReinit.AccessibleDescription" xml:space="preserve">
    <value />
  </data>
  <data name="buttonReinit.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="buttonReinit.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonReinit.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonReinit.Location" type="System.Drawing.Point, System.Drawing">
    <value>560, 376</value>
  </data>
  <data name="buttonReinit.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 24</value>
  </data>
  <data name="buttonReinit.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="buttonReinit.Text" xml:space="preserve">
    <value>&amp;Reinit</value>
  </data>
  <data name="&gt;&gt;buttonReinit.Name" xml:space="preserve">
    <value>buttonReinit</value>
  </data>
  <data name="&gt;&gt;buttonReinit.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonReinit.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonReinit.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <metadata name="timer1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="buttonStartTracker.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="buttonStartTracker.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonStartTracker.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonStartTracker.Location" type="System.Drawing.Point, System.Drawing">
    <value>680, 368</value>
  </data>
  <data name="buttonStartTracker.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 32</value>
  </data>
  <data name="buttonStartTracker.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="buttonStartTracker.Text" xml:space="preserve">
    <value>Start Tracker</value>
  </data>
  <data name="&gt;&gt;buttonStartTracker.Name" xml:space="preserve">
    <value>buttonStartTracker</value>
  </data>
  <data name="&gt;&gt;buttonStartTracker.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonStartTracker.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonStartTracker.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="buttonReadTrackerZoom.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="buttonReadTrackerZoom.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonReadTrackerZoom.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonReadTrackerZoom.Location" type="System.Drawing.Point, System.Drawing">
    <value>672, 152</value>
  </data>
  <data name="buttonReadTrackerZoom.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 32</value>
  </data>
  <data name="buttonReadTrackerZoom.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="buttonReadTrackerZoom.Text" xml:space="preserve">
    <value>Save as "Home" zoom</value>
  </data>
  <data name="&gt;&gt;buttonReadTrackerZoom.Name" xml:space="preserve">
    <value>buttonReadTrackerZoom</value>
  </data>
  <data name="&gt;&gt;buttonReadTrackerZoom.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonReadTrackerZoom.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonReadTrackerZoom.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="buttonApplyTrackerZoom.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="buttonApplyTrackerZoom.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonApplyTrackerZoom.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonApplyTrackerZoom.Location" type="System.Drawing.Point, System.Drawing">
    <value>672, 184</value>
  </data>
  <data name="buttonApplyTrackerZoom.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 32</value>
  </data>
  <data name="buttonApplyTrackerZoom.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="buttonApplyTrackerZoom.Text" xml:space="preserve">
    <value>Go to home zoom</value>
  </data>
  <data name="&gt;&gt;buttonApplyTrackerZoom.Name" xml:space="preserve">
    <value>buttonApplyTrackerZoom</value>
  </data>
  <data name="&gt;&gt;buttonApplyTrackerZoom.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonApplyTrackerZoom.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonApplyTrackerZoom.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="buttonStopTracker.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="buttonStopTracker.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="buttonStopTracker.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonStopTracker.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonStopTracker.Location" type="System.Drawing.Point, System.Drawing">
    <value>680, 400</value>
  </data>
  <data name="buttonStopTracker.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 32</value>
  </data>
  <data name="buttonStopTracker.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="buttonStopTracker.Text" xml:space="preserve">
    <value>Stop Tracker</value>
  </data>
  <data name="&gt;&gt;buttonStopTracker.Name" xml:space="preserve">
    <value>buttonStopTracker</value>
  </data>
  <data name="&gt;&gt;buttonStopTracker.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonStopTracker.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonStopTracker.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="textBoxStatusCount.Font" type="System.Drawing.Font, System.Drawing">
    <value>Arial, 9.75pt</value>
  </data>
  <data name="textBoxStatusCount.Location" type="System.Drawing.Point, System.Drawing">
    <value>168, 16</value>
  </data>
  <data name="textBoxStatusCount.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 15</value>
  </data>
  <data name="textBoxStatusCount.TabIndex" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="textBoxStatusCount.Text" xml:space="preserve">
    <value>-</value>
  </data>
  <data name="textBoxStatusCount.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="&gt;&gt;textBoxStatusCount.Name" xml:space="preserve">
    <value>textBoxStatusCount</value>
  </data>
  <data name="&gt;&gt;textBoxStatusCount.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxStatusCount.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBoxStatusCount.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="groupBoxPanTilt.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;buttonPanH.Name" xml:space="preserve">
    <value>buttonPanH</value>
  </data>
  <data name="&gt;&gt;buttonPanH.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPanH.Parent" xml:space="preserve">
    <value>groupBoxPanTilt</value>
  </data>
  <data name="&gt;&gt;buttonPanH.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;buttonPanD.Name" xml:space="preserve">
    <value>buttonPanD</value>
  </data>
  <data name="&gt;&gt;buttonPanD.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPanD.Parent" xml:space="preserve">
    <value>groupBoxPanTilt</value>
  </data>
  <data name="&gt;&gt;buttonPanD.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;buttonPanU.Name" xml:space="preserve">
    <value>buttonPanU</value>
  </data>
  <data name="&gt;&gt;buttonPanU.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPanU.Parent" xml:space="preserve">
    <value>groupBoxPanTilt</value>
  </data>
  <data name="&gt;&gt;buttonPanU.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;buttonPanL.Name" xml:space="preserve">
    <value>buttonPanL</value>
  </data>
  <data name="&gt;&gt;buttonPanL.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPanL.Parent" xml:space="preserve">
    <value>groupBoxPanTilt</value>
  </data>
  <data name="&gt;&gt;buttonPanL.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;buttonPanR.Name" xml:space="preserve">
    <value>buttonPanR</value>
  </data>
  <data name="&gt;&gt;buttonPanR.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPanR.Parent" xml:space="preserve">
    <value>groupBoxPanTilt</value>
  </data>
  <data name="&gt;&gt;buttonPanR.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="groupBoxPanTilt.Location" type="System.Drawing.Point, System.Drawing">
    <value>656, 32</value>
  </data>
  <data name="groupBoxPanTilt.Size" type="System.Drawing.Size, System.Drawing">
    <value>136, 96</value>
  </data>
  <data name="groupBoxPanTilt.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;groupBoxPanTilt.Name" xml:space="preserve">
    <value>groupBoxPanTilt</value>
  </data>
  <data name="&gt;&gt;groupBoxPanTilt.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBoxPanTilt.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBoxPanTilt.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="buttonPanH.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonPanH.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonPanH.Location" type="System.Drawing.Point, System.Drawing">
    <value>56, 40</value>
  </data>
  <data name="buttonPanH.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 24</value>
  </data>
  <data name="buttonPanH.TabIndex" type="System.Int32, mscorlib">
    <value>38</value>
  </data>
  <data name="buttonPanH.Text" xml:space="preserve">
    <value>H</value>
  </data>
  <data name="&gt;&gt;buttonPanH.Name" xml:space="preserve">
    <value>buttonPanH</value>
  </data>
  <data name="&gt;&gt;buttonPanH.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPanH.Parent" xml:space="preserve">
    <value>groupBoxPanTilt</value>
  </data>
  <data name="&gt;&gt;buttonPanH.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="buttonPanD.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonPanD.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonPanD.Location" type="System.Drawing.Point, System.Drawing">
    <value>48, 72</value>
  </data>
  <data name="buttonPanD.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 24</value>
  </data>
  <data name="buttonPanD.TabIndex" type="System.Int32, mscorlib">
    <value>37</value>
  </data>
  <data name="buttonPanD.Text" xml:space="preserve">
    <value>Down</value>
  </data>
  <data name="&gt;&gt;buttonPanD.Name" xml:space="preserve">
    <value>buttonPanD</value>
  </data>
  <data name="&gt;&gt;buttonPanD.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPanD.Parent" xml:space="preserve">
    <value>groupBoxPanTilt</value>
  </data>
  <data name="&gt;&gt;buttonPanD.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="buttonPanU.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonPanU.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonPanU.Location" type="System.Drawing.Point, System.Drawing">
    <value>48, 8</value>
  </data>
  <data name="buttonPanU.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 24</value>
  </data>
  <data name="buttonPanU.TabIndex" type="System.Int32, mscorlib">
    <value>36</value>
  </data>
  <data name="buttonPanU.Text" xml:space="preserve">
    <value>Up</value>
  </data>
  <data name="&gt;&gt;buttonPanU.Name" xml:space="preserve">
    <value>buttonPanU</value>
  </data>
  <data name="&gt;&gt;buttonPanU.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPanU.Parent" xml:space="preserve">
    <value>groupBoxPanTilt</value>
  </data>
  <data name="&gt;&gt;buttonPanU.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="buttonPanL.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonPanL.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonPanL.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 40</value>
  </data>
  <data name="buttonPanL.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 24</value>
  </data>
  <data name="buttonPanL.TabIndex" type="System.Int32, mscorlib">
    <value>35</value>
  </data>
  <data name="buttonPanL.Text" xml:space="preserve">
    <value>Left</value>
  </data>
  <data name="&gt;&gt;buttonPanL.Name" xml:space="preserve">
    <value>buttonPanL</value>
  </data>
  <data name="&gt;&gt;buttonPanL.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPanL.Parent" xml:space="preserve">
    <value>groupBoxPanTilt</value>
  </data>
  <data name="&gt;&gt;buttonPanL.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="buttonPanR.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonPanR.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonPanR.Location" type="System.Drawing.Point, System.Drawing">
    <value>88, 40</value>
  </data>
  <data name="buttonPanR.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 24</value>
  </data>
  <data name="buttonPanR.TabIndex" type="System.Int32, mscorlib">
    <value>34</value>
  </data>
  <data name="buttonPanR.Text" xml:space="preserve">
    <value>Right</value>
  </data>
  <data name="&gt;&gt;buttonPanR.Name" xml:space="preserve">
    <value>buttonPanR</value>
  </data>
  <data name="&gt;&gt;buttonPanR.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPanR.Parent" xml:space="preserve">
    <value>groupBoxPanTilt</value>
  </data>
  <data name="&gt;&gt;buttonPanR.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="groupBoxCamera.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="FlashIris_textbox.Location" type="System.Drawing.Point, System.Drawing">
    <value>120, 64</value>
  </data>
  <data name="FlashIris_textbox.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 13</value>
  </data>
  <data name="FlashIris_textbox.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="FlashIris_textbox.Text" xml:space="preserve">
    <value>dist</value>
  </data>
  <data name="&gt;&gt;FlashIris_textbox.Name" xml:space="preserve">
    <value>FlashIris_textbox</value>
  </data>
  <data name="&gt;&gt;FlashIris_textbox.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;FlashIris_textbox.Parent" xml:space="preserve">
    <value>groupBoxCamera</value>
  </data>
  <data name="&gt;&gt;FlashIris_textbox.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="textBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>96, 8</value>
  </data>
  <data name="textBox2.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 13</value>
  </data>
  <data name="textBox2.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="textBox2.Text" xml:space="preserve">
    <value>sharp</value>
  </data>
  <data name="&gt;&gt;textBox2.Name" xml:space="preserve">
    <value>textBox2</value>
  </data>
  <data name="&gt;&gt;textBox2.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox2.Parent" xml:space="preserve">
    <value>groupBoxCamera</value>
  </data>
  <data name="&gt;&gt;textBox2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="checkBoxFlash.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBoxFlash.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 128</value>
  </data>
  <data name="checkBoxFlash.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 24</value>
  </data>
  <data name="checkBoxFlash.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="checkBoxFlash.Text" xml:space="preserve">
    <value>Use Flash</value>
  </data>
  <data name="&gt;&gt;checkBoxFlash.Name" xml:space="preserve">
    <value>checkBoxFlash</value>
  </data>
  <data name="&gt;&gt;checkBoxFlash.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBoxFlash.Parent" xml:space="preserve">
    <value>groupBoxCamera</value>
  </data>
  <data name="&gt;&gt;checkBoxFlash.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="labelFlash2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelFlash2.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 104</value>
  </data>
  <data name="labelFlash2.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 16</value>
  </data>
  <data name="labelFlash2.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="labelFlash2.Text" xml:space="preserve">
    <value>(darker)</value>
  </data>
  <data name="labelFlash2.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>TopCenter</value>
  </data>
  <data name="&gt;&gt;labelFlash2.Name" xml:space="preserve">
    <value>labelFlash2</value>
  </data>
  <data name="&gt;&gt;labelFlash2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelFlash2.Parent" xml:space="preserve">
    <value>groupBoxCamera</value>
  </data>
  <data name="&gt;&gt;labelFlash2.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="labelFlash1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelFlash1.Location" type="System.Drawing.Point, System.Drawing">
    <value>136, 104</value>
  </data>
  <data name="labelFlash1.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 16</value>
  </data>
  <data name="labelFlash1.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="labelFlash1.Text" xml:space="preserve">
    <value>(lighter)</value>
  </data>
  <data name="labelFlash1.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>TopCenter</value>
  </data>
  <data name="&gt;&gt;labelFlash1.Name" xml:space="preserve">
    <value>labelFlash1</value>
  </data>
  <data name="&gt;&gt;labelFlash1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelFlash1.Parent" xml:space="preserve">
    <value>groupBoxCamera</value>
  </data>
  <data name="&gt;&gt;labelFlash1.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="IrisDown.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="IrisDown.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="IrisDown.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 160</value>
  </data>
  <data name="IrisDown.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 24</value>
  </data>
  <data name="IrisDown.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="IrisDown.Text" xml:space="preserve">
    <value>Live &amp;Darker</value>
  </data>
  <data name="&gt;&gt;IrisDown.Name" xml:space="preserve">
    <value>IrisDown</value>
  </data>
  <data name="&gt;&gt;IrisDown.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;IrisDown.Parent" xml:space="preserve">
    <value>groupBoxCamera</value>
  </data>
  <data name="&gt;&gt;IrisDown.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="IrisUp.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="IrisUp.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="IrisUp.Location" type="System.Drawing.Point, System.Drawing">
    <value>112, 160</value>
  </data>
  <data name="IrisUp.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 24</value>
  </data>
  <data name="IrisUp.TabIndex" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="IrisUp.Text" xml:space="preserve">
    <value>Live &amp;Lighter</value>
  </data>
  <data name="&gt;&gt;IrisUp.Name" xml:space="preserve">
    <value>IrisUp</value>
  </data>
  <data name="&gt;&gt;IrisUp.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;IrisUp.Parent" xml:space="preserve">
    <value>groupBoxCamera</value>
  </data>
  <data name="&gt;&gt;IrisUp.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="buttonDistUp.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonDistUp.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 12pt, style=Bold</value>
  </data>
  <data name="buttonDistUp.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonDistUp.Location" type="System.Drawing.Point, System.Drawing">
    <value>168, 88</value>
  </data>
  <data name="buttonDistUp.Size" type="System.Drawing.Size, System.Drawing">
    <value>16, 16</value>
  </data>
  <data name="buttonDistUp.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="buttonDistUp.Text" xml:space="preserve">
    <value>+</value>
  </data>
  <data name="&gt;&gt;buttonDistUp.Name" xml:space="preserve">
    <value>buttonDistUp</value>
  </data>
  <data name="&gt;&gt;buttonDistUp.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonDistUp.Parent" xml:space="preserve">
    <value>groupBoxCamera</value>
  </data>
  <data name="&gt;&gt;buttonDistUp.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="buttonDistDown.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonDistDown.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 12pt, style=Bold</value>
  </data>
  <data name="buttonDistDown.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonDistDown.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 88</value>
  </data>
  <data name="buttonDistDown.Size" type="System.Drawing.Size, System.Drawing">
    <value>16, 16</value>
  </data>
  <data name="buttonDistDown.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="buttonDistDown.Text" xml:space="preserve">
    <value>-</value>
  </data>
  <data name="&gt;&gt;buttonDistDown.Name" xml:space="preserve">
    <value>buttonDistDown</value>
  </data>
  <data name="&gt;&gt;buttonDistDown.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonDistDown.Parent" xml:space="preserve">
    <value>groupBoxCamera</value>
  </data>
  <data name="&gt;&gt;buttonDistDown.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="trackBarFlashDist.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="trackBarFlashDist.Location" type="System.Drawing.Point, System.Drawing">
    <value>32, 80</value>
  </data>
  <data name="trackBarFlashDist.Size" type="System.Drawing.Size, System.Drawing">
    <value>136, 45</value>
  </data>
  <data name="trackBarFlashDist.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;trackBarFlashDist.Name" xml:space="preserve">
    <value>trackBarFlashDist</value>
  </data>
  <data name="&gt;&gt;trackBarFlashDist.Type" xml:space="preserve">
    <value>System.Windows.Forms.TrackBar, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;trackBarFlashDist.Parent" xml:space="preserve">
    <value>groupBoxCamera</value>
  </data>
  <data name="&gt;&gt;trackBarFlashDist.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="labelFlashIrisLevel.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelFlashIrisLevel.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 64</value>
  </data>
  <data name="labelFlashIrisLevel.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 11</value>
  </data>
  <data name="labelFlashIrisLevel.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="labelFlashIrisLevel.Text" xml:space="preserve">
    <value>Flash distance:</value>
  </data>
  <data name="&gt;&gt;labelFlashIrisLevel.Name" xml:space="preserve">
    <value>labelFlashIrisLevel</value>
  </data>
  <data name="&gt;&gt;labelFlashIrisLevel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelFlashIrisLevel.Parent" xml:space="preserve">
    <value>groupBoxCamera</value>
  </data>
  <data name="&gt;&gt;labelFlashIrisLevel.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="label8.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label8.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 8</value>
  </data>
  <data name="label8.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 16</value>
  </data>
  <data name="label8.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="label8.Text" xml:space="preserve">
    <value>Sharpness:</value>
  </data>
  <data name="&gt;&gt;label8.Name" xml:space="preserve">
    <value>label8</value>
  </data>
  <data name="&gt;&gt;label8.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label8.Parent" xml:space="preserve">
    <value>groupBoxCamera</value>
  </data>
  <data name="&gt;&gt;label8.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="SharpnessTrack.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="SharpnessTrack.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 24</value>
  </data>
  <data name="SharpnessTrack.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 45</value>
  </data>
  <data name="SharpnessTrack.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;SharpnessTrack.Name" xml:space="preserve">
    <value>SharpnessTrack</value>
  </data>
  <data name="&gt;&gt;SharpnessTrack.Type" xml:space="preserve">
    <value>System.Windows.Forms.TrackBar, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;SharpnessTrack.Parent" xml:space="preserve">
    <value>groupBoxCamera</value>
  </data>
  <data name="&gt;&gt;SharpnessTrack.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="groupBoxCamera.Location" type="System.Drawing.Point, System.Drawing">
    <value>456, 32</value>
  </data>
  <data name="groupBoxCamera.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 192</value>
  </data>
  <data name="groupBoxCamera.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;groupBoxCamera.Name" xml:space="preserve">
    <value>groupBoxCamera</value>
  </data>
  <data name="&gt;&gt;groupBoxCamera.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBoxCamera.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBoxCamera.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="groupBoxZoomGroup.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;textBoxScale.Name" xml:space="preserve">
    <value>textBoxScale</value>
  </data>
  <data name="&gt;&gt;textBoxScale.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxScale.Parent" xml:space="preserve">
    <value>groupBoxZoomGroup</value>
  </data>
  <data name="&gt;&gt;textBoxScale.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;textBoxZoomHex.Name" xml:space="preserve">
    <value>textBoxZoomHex</value>
  </data>
  <data name="&gt;&gt;textBoxZoomHex.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxZoomHex.Parent" xml:space="preserve">
    <value>groupBoxZoomGroup</value>
  </data>
  <data name="&gt;&gt;textBoxZoomHex.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;groupBoxZoom.Name" xml:space="preserve">
    <value>groupBoxZoom</value>
  </data>
  <data name="&gt;&gt;groupBoxZoom.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBoxZoom.Parent" xml:space="preserve">
    <value>groupBoxZoomGroup</value>
  </data>
  <data name="&gt;&gt;groupBoxZoom.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;btnZoomOut.Name" xml:space="preserve">
    <value>btnZoomOut</value>
  </data>
  <data name="&gt;&gt;btnZoomOut.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnZoomOut.Parent" xml:space="preserve">
    <value>groupBoxZoomGroup</value>
  </data>
  <data name="&gt;&gt;btnZoomOut.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;btnZoomIn.Name" xml:space="preserve">
    <value>btnZoomIn</value>
  </data>
  <data name="&gt;&gt;btnZoomIn.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnZoomIn.Parent" xml:space="preserve">
    <value>groupBoxZoomGroup</value>
  </data>
  <data name="&gt;&gt;btnZoomIn.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="groupBoxZoomGroup.Location" type="System.Drawing.Point, System.Drawing">
    <value>456, 224</value>
  </data>
  <data name="groupBoxZoomGroup.Size" type="System.Drawing.Size, System.Drawing">
    <value>184, 104</value>
  </data>
  <data name="groupBoxZoomGroup.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;groupBoxZoomGroup.Name" xml:space="preserve">
    <value>groupBoxZoomGroup</value>
  </data>
  <data name="&gt;&gt;groupBoxZoomGroup.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBoxZoomGroup.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBoxZoomGroup.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="textBoxScale.Location" type="System.Drawing.Point, System.Drawing">
    <value>48, 80</value>
  </data>
  <data name="textBoxScale.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 20</value>
  </data>
  <data name="textBoxScale.TabIndex" type="System.Int32, mscorlib">
    <value>51</value>
  </data>
  <data name="textBoxScale.Text" xml:space="preserve">
    <value>-1</value>
  </data>
  <data name="&gt;&gt;textBoxScale.Name" xml:space="preserve">
    <value>textBoxScale</value>
  </data>
  <data name="&gt;&gt;textBoxScale.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxScale.Parent" xml:space="preserve">
    <value>groupBoxZoomGroup</value>
  </data>
  <data name="&gt;&gt;textBoxScale.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="textBoxZoomHex.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 80</value>
  </data>
  <data name="textBoxZoomHex.Size" type="System.Drawing.Size, System.Drawing">
    <value>32, 20</value>
  </data>
  <data name="textBoxZoomHex.TabIndex" type="System.Int32, mscorlib">
    <value>50</value>
  </data>
  <data name="textBoxZoomHex.Text" xml:space="preserve">
    <value>-1</value>
  </data>
  <data name="&gt;&gt;textBoxZoomHex.Name" xml:space="preserve">
    <value>textBoxZoomHex</value>
  </data>
  <data name="&gt;&gt;textBoxZoomHex.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxZoomHex.Parent" xml:space="preserve">
    <value>groupBoxZoomGroup</value>
  </data>
  <data name="&gt;&gt;textBoxZoomHex.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;radioButtonFast.Name" xml:space="preserve">
    <value>radioButtonFast</value>
  </data>
  <data name="&gt;&gt;radioButtonFast.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButtonFast.Parent" xml:space="preserve">
    <value>groupBoxZoom</value>
  </data>
  <data name="&gt;&gt;radioButtonFast.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;radioButtonPTZMed.Name" xml:space="preserve">
    <value>radioButtonPTZMed</value>
  </data>
  <data name="&gt;&gt;radioButtonPTZMed.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButtonPTZMed.Parent" xml:space="preserve">
    <value>groupBoxZoom</value>
  </data>
  <data name="&gt;&gt;radioButtonPTZMed.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;radioButtonPTZSlow.Name" xml:space="preserve">
    <value>radioButtonPTZSlow</value>
  </data>
  <data name="&gt;&gt;radioButtonPTZSlow.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButtonPTZSlow.Parent" xml:space="preserve">
    <value>groupBoxZoom</value>
  </data>
  <data name="&gt;&gt;radioButtonPTZSlow.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="groupBoxZoom.Location" type="System.Drawing.Point, System.Drawing">
    <value>104, 8</value>
  </data>
  <data name="groupBoxZoom.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 96</value>
  </data>
  <data name="groupBoxZoom.TabIndex" type="System.Int32, mscorlib">
    <value>48</value>
  </data>
  <data name="groupBoxZoom.Text" xml:space="preserve">
    <value>Zoom Speed</value>
  </data>
  <data name="&gt;&gt;groupBoxZoom.Name" xml:space="preserve">
    <value>groupBoxZoom</value>
  </data>
  <data name="&gt;&gt;groupBoxZoom.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBoxZoom.Parent" xml:space="preserve">
    <value>groupBoxZoomGroup</value>
  </data>
  <data name="&gt;&gt;groupBoxZoom.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="radioButtonFast.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="radioButtonFast.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="radioButtonFast.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 64</value>
  </data>
  <data name="radioButtonFast.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 24</value>
  </data>
  <data name="radioButtonFast.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="radioButtonFast.Text" xml:space="preserve">
    <value>Fast</value>
  </data>
  <data name="&gt;&gt;radioButtonFast.Name" xml:space="preserve">
    <value>radioButtonFast</value>
  </data>
  <data name="&gt;&gt;radioButtonFast.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButtonFast.Parent" xml:space="preserve">
    <value>groupBoxZoom</value>
  </data>
  <data name="&gt;&gt;radioButtonFast.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="radioButtonPTZMed.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="radioButtonPTZMed.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="radioButtonPTZMed.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 40</value>
  </data>
  <data name="radioButtonPTZMed.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 24</value>
  </data>
  <data name="radioButtonPTZMed.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="radioButtonPTZMed.Text" xml:space="preserve">
    <value>Med</value>
  </data>
  <data name="&gt;&gt;radioButtonPTZMed.Name" xml:space="preserve">
    <value>radioButtonPTZMed</value>
  </data>
  <data name="&gt;&gt;radioButtonPTZMed.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButtonPTZMed.Parent" xml:space="preserve">
    <value>groupBoxZoom</value>
  </data>
  <data name="&gt;&gt;radioButtonPTZMed.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="radioButtonPTZSlow.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="radioButtonPTZSlow.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="radioButtonPTZSlow.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 16</value>
  </data>
  <data name="radioButtonPTZSlow.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 24</value>
  </data>
  <data name="radioButtonPTZSlow.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="radioButtonPTZSlow.Text" xml:space="preserve">
    <value>Slow</value>
  </data>
  <data name="&gt;&gt;radioButtonPTZSlow.Name" xml:space="preserve">
    <value>radioButtonPTZSlow</value>
  </data>
  <data name="&gt;&gt;radioButtonPTZSlow.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButtonPTZSlow.Parent" xml:space="preserve">
    <value>groupBoxZoom</value>
  </data>
  <data name="&gt;&gt;radioButtonPTZSlow.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="btnZoomOut.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="btnZoomOut.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnZoomOut.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 48</value>
  </data>
  <data name="btnZoomOut.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 24</value>
  </data>
  <data name="btnZoomOut.TabIndex" type="System.Int32, mscorlib">
    <value>49</value>
  </data>
  <data name="btnZoomOut.Text" xml:space="preserve">
    <value>&amp;Zoom Out</value>
  </data>
  <data name="&gt;&gt;btnZoomOut.Name" xml:space="preserve">
    <value>btnZoomOut</value>
  </data>
  <data name="&gt;&gt;btnZoomOut.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnZoomOut.Parent" xml:space="preserve">
    <value>groupBoxZoomGroup</value>
  </data>
  <data name="&gt;&gt;btnZoomOut.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="btnZoomIn.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="btnZoomIn.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnZoomIn.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 16</value>
  </data>
  <data name="btnZoomIn.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 24</value>
  </data>
  <data name="btnZoomIn.TabIndex" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="btnZoomIn.Text" xml:space="preserve">
    <value>Zoom &amp;In</value>
  </data>
  <data name="&gt;&gt;btnZoomIn.Name" xml:space="preserve">
    <value>btnZoomIn</value>
  </data>
  <data name="&gt;&gt;btnZoomIn.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnZoomIn.Parent" xml:space="preserve">
    <value>groupBoxZoomGroup</value>
  </data>
  <data name="&gt;&gt;btnZoomIn.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>45</value>
  </metadata>
  <data name="$this.AutoScaleBaseSize" type="System.Drawing.Size, System.Drawing">
    <value>5, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>800, 446</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAIAICAQAAAAAADoAgAAJgAAABAQEAAAAAAAKAEAAA4DAAAoAAAAIAAAAEAAAAABAAQAAAAAAAAC
        AAAAAAAAAAAAABAAAAAQAAAAAAAAAAAAgAAAgAAAAICAAIAAAACAAIAAgIAAAICAgADAwMAAAAD/AAD/
        AAAA//8A/wAAAP8A/wD//wAA////AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AA7u7u7u4ADg7gAAAAAAAAAA7gDuAO4A7g7gAAAAAAAAAO4A7gAO4AAO4AAAAAAAAADuAO4ADuAO7uAA
        AAAAAAAA7gDuAA7g7u4AAAAAAAAAAO4A7gAO4O4AAAAAAAAAAADuAO4A7gDuDuAAAAAAAAAO7u7u7uAA
        DuDgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AHAHAAAAAAAAAAAAAAAHAAAAAAAAcAAAAAAAAAAAAHcAd3cAdwAAAAAAAAAAAAB3B3d3cHcAAAAAAAAA
        AAAAdwcHd3B3AAAAAAAAAAAAAHcH4HdwdwAAAAAAAAAAAAAAAHd3AAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAAcAAAAAAAAAAAAAAAAABwBwAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA////////
        /////////////////////////gB0//8zMn//M55//zOYf/8zkP//M5P//zMyf/4AeX//////////////
        /////D///4AB//+AAf//gAH//4IB//+BAf//sA3//7gd//+AAf//yZP///w/////////////////////
        //8oAAAAEAAAACAAAAABAAQAAAAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAgAAAAICAAIAA
        AACAAIAAgIAAAICAgADAwMAAAAD/AAD/AAAA//8A/wAAAP8A/wD//wAA////AAAAAAAAAAAAB3d3d3d3
        d3dERERERERER0////////hHT///////+EdP///////4R0////////hHT///////+EdP///////4R0//
        //////hHT///////+EdIiIiIiIiIR0zMzMzMzMxHxERERERERMAAAAAAAAAAAAAAAAAAAAAA//8AAIAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAD//wAA//8AAA==
</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>DCS8000 Capture Dialog</value>
  </data>
  <data name="&gt;&gt;timer1.Name" xml:space="preserve">
    <value>timer1</value>
  </data>
  <data name="&gt;&gt;timer1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Timer, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>DCS8000Main</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>