using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Collections;
using System.IO;
using System.Runtime.Serialization.Formatters.Binary;
using System.Windows.Forms;
using System.Drawing.Text;

namespace DCSDEV.DCSDesign
{
	/// <summary>
	/// Summary description for DCSDesignSide.
	/// </summary>
    public class DCSDesignSide : IDisposable
	{
		public ArrayList m_DCSDesignObjects;

		public DCSDEV.DCSDatatypes.BackFillTypes SideFillType;
		public String SideBackImageName;
		public Image SideBackImage; //background image in text

		public Color SideBackColor = Color.White;
		public Color SideBackColor2 = Color.White;
		public System.Drawing.Drawing2D.LinearGradientMode SideBackGradientType;
		public Color SideColorChoice1;
		public Color SideColorChoice2;
		public Color SideColorChoice3;
		public string SideColorCondition1;
		public string SideColorCondition2;
		
		public Color SideColorEval;			// dynamically evalued color
		
		public bool SideIsLandscape;
		
		public DCSDesignSide()
		{
			//
			// TODO: Add constructor logic here
			//
			m_DCSDesignObjects = new ArrayList();
			SideFillType = DCSDEV.DCSDatatypes.BackFillTypes.FILL_CLEAR;
			SideBackColor = Color.White;
			SideBackColor2 = System.Drawing.Color.PowderBlue;
			SideBackGradientType = System.Drawing.Drawing2D.LinearGradientMode.Horizontal;
			SideColorChoice1 = Color.White;
			SideColorChoice2 = Color.Black;
			SideColorChoice3 = Color.Gray;
			SideColorCondition1 = "";
			SideColorCondition2 = "";
			SideColorEval = Color.LightSeaGreen;
			SideIsLandscape = true;
		}

		// Implement IDisposable.
		// Do not make this method virtual.
		// A derived class should not be able to override this method.
		public void Dispose()
		{
			if (m_DCSDesignObjects != null)
			{
				foreach (DCSDEV.DCSDesign.DCSDesignObject designObject in m_DCSDesignObjects)
					designObject.Dispose();
				m_DCSDesignObjects.Clear();
			}
			if (this.SideBackImage != null)
				this.SideBackImage.Dispose();
		}

		// merge Badge data into badge design //
		public void MergeSideWithDB(DCSDEV.DCSDesign.DCSBadgeDataset badgeData)
		{
			string strDBValue;
			string strEval;

			if (this.SideFillType == DCSDatatypes.BackFillTypes.CONDITIONAL_COLOR)
			{
				if (this.SideColorCondition1 != "")
				{
					strEval = DCSDEV.DCSDesign.DCSFormula.FormulaEval(this.SideColorCondition1, badgeData);
					if (strEval != "0") this.SideColorEval = this.SideColorChoice1;
					else if (this.SideColorCondition2 != "")
					{
						strEval = DCSDEV.DCSDesign.DCSFormula.FormulaEval(this.SideColorCondition2, badgeData);
						if (strEval != "0") this.SideColorEval = this.SideColorChoice2;
						else this.SideColorEval = this.SideColorChoice3;
					}
					else this.SideColorEval = this.SideColorChoice3;
				}
				else if (this.SideColorCondition2 != "")
				{
					strEval = DCSDEV.DCSDesign.DCSFormula.FormulaEval(this.SideColorCondition2, badgeData);
					if (strEval != "0") this.SideColorEval = this.SideColorChoice2;
					else this.SideColorEval = this.SideColorChoice3;
				}
				else this.SideColorEval = this.SideColorChoice3;
			}

			foreach(DCSDesignObject designObject in m_DCSDesignObjects) 
			{
				if (designObject.VisibleIf && designObject.VisibleIfCondition != "")
				{
					strEval = DCSDEV.DCSDesign.DCSFormula.FormulaEval(designObject.VisibleIfCondition, badgeData);
					designObject.VisibleIfEval = (strEval != "0");
				}

				if (designObject.BackFillType == DCSDatatypes.BackFillTypes.CONDITIONAL_COLOR)
				{
					if (designObject.ColorCondition1 != "")
					{
						strEval = DCSDEV.DCSDesign.DCSFormula.FormulaEval(designObject.ColorCondition1, badgeData);
						if (strEval != "0") designObject.ColorEval = designObject.ColorChoice1;
						else if (designObject.ColorCondition2 != "")
						{
							strEval = DCSDEV.DCSDesign.DCSFormula.FormulaEval(designObject.ColorCondition2, badgeData);
							if (strEval != "0") designObject.ColorEval = designObject.ColorChoice2;
							else designObject.ColorEval = designObject.ColorChoice3;
						}
						else designObject.ColorEval = designObject.ColorChoice3;
					}
					else if (designObject.ColorCondition2 != "")
					{
						strEval = DCSDEV.DCSDesign.DCSFormula.FormulaEval(designObject.ColorCondition2, badgeData);
						if (strEval != "0") designObject.ColorEval = designObject.ColorChoice2;
						else designObject.ColorEval = designObject.ColorChoice3;
					}
					else designObject.ColorEval = designObject.ColorChoice3;
				}

				strDBValue = null;
				switch (designObject.DCSDesignObjectType)
				{
					case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Portrait:
					case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Signature:
					case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Fingerprint:
					{
                        // SYH Note: Finger mapping should be applied here as well as for display. See DoDisplayClass
						
						/****************************************************************
						string strImageClassEx = designObject.DCSDesignObjectType.ToString();
						if (designObject.ObjectTypeInstance > 0) 
							strImageClassEx = strImageClassEx + designObject.ObjectTypeInstance.ToString();
						if (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Fingerprint)
						{
							DCSDEV.ParameterStore ps0 = new DCSDEV.ParameterStore("DCSSDK_Mgt");
							bool bFingerMapping = ps0.GetBoolParameter("FingerMapping", true);
							string strFingerMap = ps0.GetStringParameter("FingerMapString", "0549");
							if (strFingerMap.Length < 1) bFingerMapping = false;
							 *
							 * !!! this does not account for missing fingers
							int nFingerprintInstances = m_ps.GetIntParameter("FingerprintInstances", 1);
							bFingerMapping = bFingerMapping && (nFingerprintInstances != 10);
							if (bFingerMapping) strImageClassEx = strImageClassEx + (strFingerMap[designObject.ObjectTypeInstance] - '0').ToString();
							else strImageClassEx = strImageClassEx + designObject.ObjectTypeInstance.ToString();
						}
						* ****************************************************************/

						string strImageID;
						DCSDatabaseIF.ImageClass imageClass;
						switch (designObject.DCSDesignObjectType)
						{
							default:
							case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Portrait:
								strImageID = badgeData.m_strPortraitName;
								imageClass = DCSDatabaseIF.ImageClass.Portrait;
								break;
							case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Signature:
								strImageID = badgeData.m_strSignatureName;
								imageClass = DCSDatabaseIF.ImageClass.Signature;
								break;
							case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Fingerprint:
								strImageID = badgeData.m_strFingerprintName;
								imageClass = DCSDatabaseIF.ImageClass.Fingerprint;
								break;
						}
						if (strImageID != null && strImageID.Length != 0)
						{
							designObject.DesignObjectImage = DCSDatabaseIF.GetStoredImage(strImageID, imageClass, designObject.ObjectTypeInstance);
						}
						if (designObject.DesignObjectImage == null) 
							designObject.DesignObjectImageName = null;
					}
						break;
					case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ICAOMRZ:
					{
						string[] values = designObject.Text.Split("|".ToCharArray(),11);
						string[] sources = designObject.SourceName.Split("|".ToCharArray(),11);

						int len = Math.Min(values.Length, sources.Length); // the lengths should be the same
						int i;
						for (i=0; i<len; i++)
						{
							foreach(DCSDEV.DCSDesign.FieldInfo fi in badgeData.m_lFieldNamesAndValues) 
							{
								if (sources[i] == fi.fieldName)
								{
									if (fi.FieldValue.ToString() != "") 
										values[i] = fi.FieldValue.ToString();
									break;
								}
							}
						}
						// merge all values
						string str = null;
						for (i=0; i<len; i++)
						{
							if (i==0) str = values[i];
							else str = str + "|" + values[i];
						}
						designObject.Text = str;
					}
						break;
					case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.TextObj:
					case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode:
					case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Barcode2D:
					case DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ImageObj:
						// get the database value - skip if its static, evaluate formula if its formula, lookup if DB
						switch (designObject.SourceType)
						{
							case DCSDEV.DCSDatatypes.SourceTypes.StaticValue:
								continue;
							case DCSDEV.DCSDatatypes.SourceTypes.Formula:
								strDBValue = DCSDEV.DCSDesign.DCSFormula.FormulaEval(designObject.Formula, badgeData);
								break;
							case DCSDEV.DCSDatatypes.SourceTypes.Database:
								foreach(DCSDEV.DCSDesign.FieldInfo fi in badgeData.m_lFieldNamesAndValues)
								{
									if (designObject.SourceName == fi.fieldName)
									{
										strDBValue = fi.FieldValue.ToString();
										break;
									}
								}
								break;
							default:
								DCSMsg.Show("ERROR: Unknown SourceType");	// do not translate message that begin with "ERROR"
								continue;
						}
						if (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ImageObj)
						{
							// find the image file based on various interpretations of the path and extension
							if (strDBValue != null && strDBValue != "")
							{
								string existingname;
								// try using full path if thats whats provided. If this doesnt work or 
								// if the path is partial use the Badge Data Path.
								if (!System.IO.Path.IsPathRooted(strDBValue) || (existingname = DCSDEV.DCSDatabaseIF.GetExistingImageType(strDBValue)) == null)
								{
									string fullname = DCSDEV.DCSDesignDataAccess.ExpandImageName(strDBValue, false);
									existingname = DCSDEV.DCSDatabaseIF.GetExistingImageType(fullname);
								}
								designObject.DesignObjectImageName = existingname;
								if (designObject.DesignObjectImageName != null)
								{
									//designObject.DesignObjectImage = (Bitmap)DCSDEV.DCSDesignDataAccess.GetImage(designObject.DesignObjectImageName, false);
									// removed call to GetImage, since the path and file are already fully expanded and know to exist

									designObject.DesignObjectImage = DCSDEV.DCSDatabaseIF.OpenImageFile(designObject.DesignObjectImageName);
								}
							}
							else
							{
								designObject.DesignObjectImageName = null;
								designObject.DesignObjectImage = null;
							}
						}
						else	//  non image
						{
							if (strDBValue != null)
								designObject.Text = strDBValue;
							else
								designObject.Text = "";
						}
						break;
					default:
						// e.g. graphics design object types do not merge with DB data
						continue;
				}
			}
		}
		
		public void MergeSideWithSampleImages()
		{
			foreach(DCSDesignObject designObject in m_DCSDesignObjects) 
			{
				if (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Portrait
					|| designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Signature
					|| designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.Fingerprint
					|| (designObject.DCSDesignObjectType == DCSDEV.DCSDatatypes.DCSDesignObjectTypes.ImageObj
					&& designObject.SourceType != DCSDEV.DCSDatatypes.SourceTypes.StaticValue) )
				{
					if (designObject.DesignObjectImageName != null && designObject.DesignObjectImageName.Length != 0)
					{
						try
						{
							if (designObject.DesignObjectImage != null) designObject.DesignObjectImage.Dispose();
							designObject.DesignObjectImage = (Bitmap)DCSDEV.DCSDesignDataAccess.GetImage(designObject.DesignObjectImageName, false);
						}
						catch (Exception ex)
						{
							DCSDEV.DCSMsg.Show(ex);
						}
					}
				}
			}
		}
	}
}
