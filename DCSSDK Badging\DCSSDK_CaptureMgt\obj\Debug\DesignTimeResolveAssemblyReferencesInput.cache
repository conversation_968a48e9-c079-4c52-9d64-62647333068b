   .winmd.dll.exe    BD:\repos_D\SDS Collection\DCSSDK Badging\DCSSDK_CaptureMgt\App.icoRD:\repos_D\SDS Collection\DCSSDK Badging\Referenced Files\AgoraDlls\AgrFeature.DLLbD:\repos_D\SDS Collection\DCSSDK Badging\Referenced Files\AgoraDlls\Antheus.Display.FingerView.dllXD:\repos_D\SDS Collection\DCSSDK Capture\DCSCapture_Canon\bin\Debug\DCSCapture_Canon.dllbD:\repos_D\SDS Collection\DCSSDK Capture\DCSCapture_CrossMatch\bin\Debug\DCSCapture_CrossMatch.dll\D:\repos_D\SDS Collection\DCSSDK Capture\DCSCapture_DCS8000\bin\Debug\DCSCapture_DCS8000.dllXD:\repos_D\SDS Collection\DCSSDK Capture\DCSCapture_FDU04\bin\Debug\DCSCapture_FDU04.dll^D:\repos_D\SDS Collection\DCSSDK Capture\DCSCapture_FromFile\bin\Debug\DCSCapture_FromFile.dll\D:\repos_D\SDS Collection\DCSSDK Badging\DCSCapture_Scanner\bin\Debug\DCSCapture_Scanner.dllXD:\repos_D\SDS Collection\DCSSDK Capture\DCSCapture_Topaz\bin\Debug\DCSCapture_Topaz.dllXD:\repos_D\SDS Collection\DCSSDK Capture\DCSCapture_Twain\bin\Debug\DCSCapture_Twain.dllXD:\repos_D\SDS Collection\DCSSDK Badging\DCSInnovatricsIF\bin\Debug\DCSInnovatricsIF.dllRD:\repos_D\SDS Collection\DCSSDK Badging\DCSSDK_ChipIF\bin\Debug\DCSSDK_ChipIF.dllVD:\repos_D\SDS Collection\DCSSDK Capture\DCSSDK_Finisher\bin\Debug\DCSSDK_Finisher.dlljD:\repos_D\SDS Collection\DCSSDK Capture\DCSSDK_FinisherProperties\bin\Debug\DCSSDK_FinisherProperties.dllXD:\repos_D\SDS Collection\DCSSDK Capture\DCSSDK_Utilities\bin\Debug\DCSSDK_Utilities.dllcD:\repos_D\SDS Collection\DCSSDK Badging\Referenced Files\Innovatrics IDKit\Innovatrics.IEngine.dll:C:\Windows\Microsoft.NET\Framework\v2.0.50727\mscorlib.dll?C:\Windows\Microsoft.NET\Framework64\v2.0.50727\System.Data.dll:C:\Windows\Microsoft.NET\Framework64\v2.0.50727\System.dllBC:\Windows\Microsoft.NET\Framework64\v2.0.50727\System.Drawing.dllHC:\Windows\Microsoft.NET\Framework64\v2.0.50727\System.Windows.Forms.dll>C:\Windows\Microsoft.NET\Framework64\v2.0.50727\System.Xml.dll       0C:\Windows\Microsoft.NET\Framework64\v2.0.50727\   Full                 {CandidateAssemblyFiles}C:\DCS.SDK\Lead.Net\*C:\DCS.SDK\Capture\Referenced Files\Liska\*C:\DCS.SDK\Badging\Referenced Files\Liska\2C:\DCS.SDK\Capture\DCSSDK_IF_FromFile\bin\Release\5C:\DCS.SDK\Capture\DCSSDK_IF_CanonCamera\bin\Release\4C:\DCS.SDK\Capture\DCSSDK_IF_CrossMatch\bin\Release\1C:\DCS.SDK\Capture\DCSSDK_IF_DCS8000\bin\Release\/C:\DCS.SDK\Capture\DCSSDK_IF_Topaz\bin\Release\/C:\DCS.SDK\Capture\DCSSDK_IF_Twain\bin\Release\{HintPathFromItem}{TargetFrameworkDirectory}B{Registry:Software\Microsoft\.NETFramework,v2.0,AssemblyFoldersEx}
{RawFileName}ED:\repos_D\SDS Collection\DCSSDK Badging\DCSSDK_CaptureMgt\bin\Debug\     B{Registry:Software\Microsoft\.NETFramework,v2.0,AssemblyFoldersEx}nD:\repos_D\SDS Collection\DCSSDK Badging\DCSSDK_CaptureMgt\obj\Debug\DesignTimeResolveAssemblyReferences.cache   0C:\Windows\Microsoft.NET\Framework64\v2.0.50727\.NETFramework,Version=v2.0.NETFramework v2.0v2.0x86
v2.0.50727         