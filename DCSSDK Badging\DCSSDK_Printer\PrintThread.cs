using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Drawing;
using System.Data;
using System.Drawing.Printing;
using System.Windows.Forms;

using DCSDEV;
using DCSDEV.DCSDesign;

namespace DCSDEV.DCSPrinter
{
	public class PrintThread
	{
		// variables set by caller
		public int m_iBadgesPerSheet = 1;
		public ArrayList m_arrayQueueSheet = null;
		public string m_strBadgeDataFile;
		public bool m_bCardPrintOK;

		// variables for smart chip IO - used to pass back info from chip
		public SmartChipIO.ChipStatus m_ePrintChipStatus;
		public string m_strChipID = null;
		// private chip variables
		private DCSDEV.DCSChipIF.DCSDEV_ChipIF.ChipIFType m_eChipIFType;
		private string m_strChipInputName ;

		private int m_sidesInSheetBadgeArray = 1;
		private DCSDEV.PrintProperties.PrinterTypeDatum m_bcDatum = null;
		private System.Drawing.Printing.PrinterSettings m_printersettingsChosen = null;
		private Rectangle m_rectPrinterBounds;
		private int m_iCurrentPage;

		private ArrayList m_sheetBadgeArray = new ArrayList();
		private bool m_bUseSheetPrinter;

		private int m_nColumns = 1;
		private int m_nRows = 1;
		private int m_iFirstX = 0;
		private int m_iFirstY = 0;
		private int m_iDeltaX = 400;
		private int m_iDeltaY = 400;
		private int m_iBadgeW = 400;
		private int m_iBadgeH = 400;

		public void Thread_PrintOneCard()
		{
			bool bRet = this.PrintBadge();			// uses m_strBadgeDataFile
			this.m_bCardPrintOK = bRet;				// status is false if cancelled
		}

		internal void Thread_PrintSheetFromArray()
		{
			bool bRet;
			int i;
			string strEnq;
			string strXnq;
			QueueEntry q;

			string[] strBadgeDatNames = new string[m_iBadgesPerSheet];

			for (i = 0; i < m_iBadgesPerSheet; i++)
			{
				if (m_arrayQueueSheet.Count > 0)
				{
					q = (QueueEntry)m_arrayQueueSheet[0];
					m_arrayQueueSheet.RemoveAt(0);
					strEnq = q.strDatFile;
					strXnq = strEnq.Substring(0, strEnq.Length - 3) + "XNQ";

					if (System.IO.File.Exists(strXnq))
						System.IO.File.Delete(strXnq);

					System.IO.File.Move(strEnq, strXnq);
					strBadgeDatNames[i] = strXnq;
				}
				else strBadgeDatNames[i] = null;	// missing badges have nulls - allows for unfinished sheets
			}

			bRet = this.PrintBadgeSheet(strBadgeDatNames);
			this.m_bCardPrintOK = bRet;            // status is false if cancelled
			if (bRet)
			{
				// if successful remove all badge dats from the queue
				foreach (string str in strBadgeDatNames)
					if (str != null) System.IO.File.Delete(str);
			}
			else
			{
				if (DCSMsg.ShowYN("Do you want to restore the interrupted sheet to the queue?") == DialogResult.Yes)
				{
					// restore all badge dats to the queue
					foreach (string str in strBadgeDatNames)
						if (str != null)
						{
							strXnq = str;
							strEnq = strXnq.Substring(0, strXnq.Length - 3) + "ENQ";
							System.IO.File.Move(strXnq, strEnq);	// src, dst
						}
				}
				else
				{
					// if successful remove all badge dats from the queue
					foreach (string str in strBadgeDatNames)
						if (str != null) System.IO.File.Delete(str);
				}
			}
		}

		// print one badge given a badge dot dat file name
		// return false if cancelled
		private bool PrintBadge()
		{
			bool bRet;

			m_bUseSheetPrinter = false;

			// Load one badge design and the badge data and merge the two
			// Put into m_sheetBadgeArray and SendToPrinter.
			foreach (DCSDEV.DCSDesign.DCSDesign design in m_sheetBadgeArray)
				design.Dispose();
			m_sheetBadgeArray.Clear();
			try
			{
				DCSDEV.DCSDesign.DCSDesign design;
				design = new DCSDEV.DCSDesign.DCSDesign();
				// Load badge design and the badge data and merge the two
				bRet = design.LoadBadgeDesignAndData(m_strBadgeDataFile);
				if (!bRet)
				{
					design.Dispose();
					return false;
				}

				// put design into array
				m_sheetBadgeArray.Add(design);
				//  and send to printer
				bRet = this.SendToPrinter(design.m_strDocumentName, true);
			}
			catch (Exception ex)
			{
				bRet = false;
				DCSMsg.Show(ex);
			}

			foreach (DCSDEV.DCSDesign.DCSDesign design in m_sheetBadgeArray)
				design.Dispose();
			m_sheetBadgeArray.Clear();
			return bRet;
		}

		// print a sheet of badges given an array of badge dot dat file names
		// called if sheet printing is in use
		private bool PrintBadgeSheet(string[] strBadgeDotDatNameArray)
		{
			bool bRet;

			if (!DCSDEV.DCSLicensing.IsLicensedOK(DCSDEV.LicensedFeatures.Print, true)) return false;

			m_bUseSheetPrinter = true;
			foreach (DCSDEV.DCSDesign.DCSDesign design in m_sheetBadgeArray)
				design.Dispose();
			m_sheetBadgeArray.Clear();

			// load sheet params
			DCSDEV.ParameterStore ps = new ParameterStore("SheetLayout");
			m_nColumns = ps.GetIntParameter("Columns", m_nColumns);
			m_nRows = ps.GetIntParameter("Rows", m_nRows);
			m_iFirstX = ps.GetIntParameter("FirstX", m_iFirstX);
			m_iFirstY = ps.GetIntParameter("FirstY", m_iFirstY);
			m_iDeltaX = ps.GetIntParameter("SpaceX", m_iDeltaX);
			m_iDeltaY = ps.GetIntParameter("SpaceY", m_iDeltaY);
			m_iBadgeW = ps.GetIntParameter("BadgeW", m_iBadgeW);
			m_iBadgeH = ps.GetIntParameter("BadgeH", m_iBadgeH);

			// Load the badge designs and the badge data and merge the two
			// Do all that fit into a multi-badge sheet and call SendToPrinter.
			// In the loop figure how many sides are used.
			m_sidesInSheetBadgeArray = 1;
			int iNumOnSheet = 0;
			foreach (string strBadgeDotDatName in strBadgeDotDatNameArray)
			{
				if (strBadgeDotDatName == null)
					continue;

				// Load badge design and the badge data and merge the two
				DCSDEV.DCSDesign.DCSDesign design = new DCSDEV.DCSDesign.DCSDesign();
				bRet = design.LoadBadgeDesignAndData(strBadgeDotDatName);
				if (!bRet)
				{
					design.Dispose();
					return false;
				}

				// put design into array
				m_sheetBadgeArray.Add(design);

				// figure sides
				if (design.m_designSides.Count > m_sidesInSheetBadgeArray)
					m_sidesInSheetBadgeArray = design.m_designSides.Count;
				iNumOnSheet++;
			}

			// send to printer
			bRet = this.SendToPrinter(String.Format("Sheet-{0}/{1} {2}", iNumOnSheet.ToString(), strBadgeDotDatNameArray.Length.ToString(), ((DCSDEV.DCSDesign.DCSDesign)m_sheetBadgeArray[0]).m_strDocumentName), false);

			// clean up
			foreach (DCSDEV.DCSDesign.DCSDesign design in m_sheetBadgeArray)
				design.Dispose();
			m_sheetBadgeArray.Clear();

			return bRet;
		}
		// return false if cancelled
		private bool SendToPrinter(string strNameForPrintManager, bool bIsSingle)
		{
			// get badge class
			DCSDEV.DCSDesign.DCSDesign design = (DCSDEV.DCSDesign.DCSDesign)m_sheetBadgeArray[0];
			int idxClass;
			if (m_bUseSheetPrinter)
				idxClass = 2;
			else
				idxClass = design.PrinterTypeIndex;

			// get parameters for that class
			m_bcDatum = new DCSDEV.PrintProperties.PrinterTypeDatum("");
			m_bcDatum.LoadPrinterTypeData(idxClass);

			if (m_bUseSheetPrinter && !m_bcDatum.m_IfMultiPerSheet)
			{
				DCSMsg.Show("ERROR: sheet printing setup is wrong");
				return false;
			}

			try
			{
				using (System.Drawing.Printing.PrintDocument pdoc = new System.Drawing.Printing.PrintDocument())
				{
					if (m_bcDatum.m_SelectedPrinterName == null || m_bcDatum.m_SelectedPrinterName == "Always Ask")
					{
						if (m_printersettingsChosen == null)
						{
							// Create a host form that is a TopMost window which will be the 
							// parent of the MessageBox.
							Form topmostForm = new Form();
							// We do not want anyone to see this window so position it off the 
							// visible screen and make it as small as possible
							topmostForm.Size = new System.Drawing.Size(1, 1);
							topmostForm.StartPosition = FormStartPosition.Manual;
							System.Drawing.Rectangle rect = SystemInformation.VirtualScreen;
							topmostForm.Location = new System.Drawing.Point(rect.Bottom + 10, rect.Right + 10);
							topmostForm.Show();
							// Make this form the active form and make it TopMost
							topmostForm.Focus();
							topmostForm.BringToFront();
							topmostForm.TopMost = true;

							// Finally show the MessageBox with the form just created as its owner
							System.Windows.Forms.PrintDialog pdlg = new System.Windows.Forms.PrintDialog();
							pdlg.PrinterSettings = new System.Drawing.Printing.PrinterSettings();
							DialogResult result = pdlg.ShowDialog();
							if (result == DialogResult.Cancel)
								return false;
							// remember the settings so it is not asked again
							m_printersettingsChosen = (System.Drawing.Printing.PrinterSettings)pdlg.PrinterSettings.Clone(); // syh add clone 12/11/07

							topmostForm.Dispose(); // clean it up all the way
						}
						pdoc.PrinterSettings = m_printersettingsChosen;
						m_printersettingsChosen = null;	// NOTE: set to null causes "Always Ask"; otherwise answer will be remembered. 
					}
					else if (m_bcDatum.m_SelectedPrinterName == "Windows Default Printer")
					{
						pdoc.PrinterSettings.PrinterName = null;
					}
					else // printer config specifes a printer
					{
						pdoc.PrinterSettings.PrinterName = (string)m_bcDatum.m_SelectedPrinterName.Clone();
					}

					// The default media orientation is determined by the printer type's settings.
					pdoc.DefaultPageSettings.Landscape = m_bcDatum.m_IfLandscape;
					pdoc.PrinterSettings.DefaultPageSettings.Landscape = m_bcDatum.m_IfLandscape;

					// But if the document's own orientation matters; ie it wont fit if turned the wrong way,
					// then set the printer settings to the same orientation as the document.
					// If the document is smaller than the media and will fit using either Portrait or Landscape,
					// let the printer settings be those set by the Printer type.
					bool bLandscape;
					Rectangle rectMod = design.Bounds;
					if (design.Bounds.Width > design.Bounds.Height)
						rectMod.Height = design.Bounds.Width;
					else
						rectMod.Width = design.Bounds.Height;
					// See if a squared up doc will fit - ie orientation does not matter.
					if (!pdoc.PrinterSettings.DefaultPageSettings.Bounds.Contains(rectMod))
					{
						// If orientation matters, set printer to the orientation of the document.
						if (design.Bounds.Width > design.Bounds.Height) bLandscape = true;
						else bLandscape = false;
						pdoc.DefaultPageSettings.Landscape = bLandscape;
						pdoc.PrinterSettings.DefaultPageSettings.Landscape = bLandscape;
					}

					pdoc.DocumentName = strNameForPrintManager;
					// syh test printer unreproducable problem
					try
					{
						m_rectPrinterBounds = pdoc.PrinterSettings.DefaultPageSettings.Bounds;	// used by eh_PrintPage print handler
					}
					catch (Exception ex)
					{
						DCSDEV.DCSMsg.Show("ERROR reading pdoc.PrinterSettings.DefaultPageSettings.Bounds", ex);
					}

					// test margins - for some unknown reason this operation will fail if improper
					try
					{
						string x;
						x = pdoc.DefaultPageSettings.HardMarginY.ToString();
					}
					catch (Exception ex)
					{
						DCSDEV.DCSMsg.Show("The selected printing margins are improper for use with Dot Net 2.0.", ex);
						return false;
					}

					/////////////////////////////////////////////////////////////////////////
					// Determine whether chip encoding is requested and prepare parameters 
					// for eh_PrintPage.
					/////////////////////////////////////////////////////////////////////////
					m_eChipIFType = (DCSDEV.DCSChipIF.DCSDEV_ChipIF.ChipIFType)0;
					m_strChipID = null;
					m_strChipInputName = null;	// a null value will indicate design does not require encoding.
					if (bIsSingle && design.HasChip && m_bcDatum.m_bPrinterHasChipEncoder)
					{
						DCSDEV.ParameterStore ps = new ParameterStore("DCSSDK_Mgt");
						m_eChipIFType = (DCSDEV.DCSChipIF.DCSDEV_ChipIF.ChipIFType)ps.GetIntParameter("ChipIFType", 0);
						if (m_eChipIFType == DCSDEV.DCSChipIF.DCSDEV_ChipIF.ChipIFType.MIFARE_RFID_CHIP_FORMULA)
						{
							string strFormula = design.FormulaChip;
							if (strFormula == null || strFormula == "")
								DCSMsg.Show(String.Format("Chip encoding formula is not specified in document design '{0}'", design.m_BadgeData.m_strDesignName), MessageBoxIcon.Error);
							string strFormulaEval = DCSDEV.DCSDesign.DCSFormula.FormulaEval(strFormula, design.m_BadgeData);
							// write the data to encode on the chip temporarily to a file
							string strTempFile = System.IO.Path.Combine(ps.m_strDCSInstallDirectory, "_ChipWrite.dat");
							using (System.IO.StreamWriter stream = new System.IO.StreamWriter(strTempFile))
							{
								stream.Write(strFormulaEval);
								stream.Close();
							}
							m_strChipInputName = strTempFile;
						}
						else
						{
							m_strChipInputName = m_strBadgeDataFile;
						}
					}

					/////////////////////////////////////////////////////////////////////////
					// Print document
					/////////////////////////////////////////////////////////////////////////
					try
					{
						pdoc.BeginPrint += new PrintEventHandler(eh_BeginPrint);
						pdoc.PrintPage += new PrintPageEventHandler(eh_PrintPage);
						pdoc.EndPrint += new PrintEventHandler(eh_EndPrint);
						m_iCurrentPage = 0;
						pdoc.Print();
					}
					catch (System.ComponentModel.Win32Exception exc) //Exception ex)
					{
						m_printersettingsChosen = null;
						if ((exc.ErrorCode & 0xFFFF) == 0x4005)
							DCSDEV.DCSMsg.Show("The selected printer is not available.", exc);
						else
							DCSDEV.DCSMsg.Show(exc);
						return false;
					}
					catch (Exception ex)
					{
						DCSDEV.DCSMsg.Show("ERROR calling pdoc.Print", ex);
						return false;
					}
					if (m_ePrintChipStatus == SmartChipIO.ChipStatus.CANCEL) return false;
					else return true;
				}
			}
			catch (Exception ex)
			{
				DCSDEV.DCSMsg.Show("Printer error.", ex);
				return false;
			}
		}

		private void eh_BeginPrint(object sender, PrintEventArgs ev)
		{

			SmartChipIO.ChipIOMode eChipIOMode;
			bool bIfWriteToChip = false;
			bool bGetChipID = false;
			DCSDEV.SmartChipIO dlgChip;

			if (!((DCSDEV.DCSDesign.DCSDesign)m_sheetBadgeArray[0]).HasChip) return;

			if (!m_bcDatum.m_bPrinterHasChipEncoder) return;

			bIfWriteToChip = (m_bcDatum.m_bEncodeChipWhenPrinting) && m_strChipInputName != null;
			bGetChipID = (m_bcDatum.m_bScanChipIDWhenPrinting);
			if (!bIfWriteToChip && !bGetChipID) return;

			if (bIfWriteToChip) eChipIOMode = SmartChipIO.ChipIOMode.ENCODE;
			else eChipIOMode = SmartChipIO.ChipIOMode.READ_DATA;

			// encode chip
			try
			{
				dlgChip = new DCSDEV.SmartChipIO(eChipIOMode, m_strChipInputName, false);		// true=stand-alone device; false = in printer
				this.AdvanceToEncodeStation();

				if (bIfWriteToChip)
					dlgChip.OK_WriteToChip();
				else
					dlgChip.OK_ReadFromChip();

				if (dlgChip.m_eChipStatus == SmartChipIO.ChipStatus.DEVICE_ERROR)
				{
					m_ePrintChipStatus = SmartChipIO.ChipStatus.CANCEL;
					ev.Cancel = true;
					this.EjectCard();
					return;
				}
				else
				{
					if (bGetChipID) m_strChipID = dlgChip.GetChipID;
					m_ePrintChipStatus = SmartChipIO.ChipStatus.OK;
				}
			}
			catch (Exception ex)
			{
				DCSDEV.DCSMsg.Show("ERROR encoding device", ex);
			}
			// advance to print station
			this.AdvanceToPrintStation();
			return;
		}

		private void eh_EndPrint(object sender, PrintEventArgs ev)
		{
			Application.DoEvents();
			//DCSMsg.Show("end print");
		}

		private void eh_PrintPage(object sender, PrintPageEventArgs ev)
		{
			// syh - add DoEvents here and in eh_EndPrint eliminates an error which otherwise 
			// only occurs in HASP protected versions of the DLL. Dont know why this is so.

			//DCSMsg.Show("print");

			/***************
			DCSMsg.Show("print page " + m_iCurrentPage.ToString() 
				+ "\n PageBounds = " + ev.PageBounds.ToString() 
				+ "\n MarginBounds = " + ev.MarginBounds.Location.ToString()
				+ "\n Resolution = " + ev.Graphics.DpiX.ToString() + "," + ev.Graphics.DpiY.ToString()
				+ "\n ev.HasMorePages = " + ev.HasMorePages.ToString()
				);
			**********************/

			// erase sheet
			// syh ev.Graphics.Clear(System.Drawing.Color.PowderBlue);

			// get printer scale
			double dScale = ev.Graphics.DpiX / 100.0;

			int idx, xLoc, yLoc;
			try
			{
				if (m_bUseSheetPrinter)
				{
					// loop over all badges for this sheet
					for (int iy = 0; iy < m_nRows; iy++)
						for (int ix = 0; ix < m_nColumns; ix++)
						{
							Application.DoEvents();
							idx = iy * m_nColumns + ix;
							if (idx >= m_sheetBadgeArray.Count) break;

							DCSDEV.DCSDesign.DCSDesign design;
							design = (DCSDEV.DCSDesign.DCSDesign)m_sheetBadgeArray[idx];
							if (design == null)
								continue;	// some sheets have fewer than max badges
							if (design.m_designSides.Count - 1 < m_iCurrentPage)
								continue;	// some badges may have fewer than max sides

							// get badge size scaled and make a bmTemp
							int width, height;
							width = DCSMath.IntTimesDouble(design.Bounds.Width, dScale);
							height = DCSMath.IntTimesDouble(design.Bounds.Height, dScale);

							// if back side orientation is not same as front swap dimensions
							// syh note: user should have choice of rotate left or right
							bool bBackOrientationDiffers = false;
							if (m_iCurrentPage != 0)
							{
								DCSDesignSide designSide0 = (DCSDesignSide)design.m_designSides[0];
								DCSDesignSide designSide1 = (DCSDesignSide)design.m_designSides[1];
								if (designSide0.SideIsLandscape != designSide1.SideIsLandscape) bBackOrientationDiffers = true;
							}
							/*****************************************************************
							* OLD WAY without Format24bppRgb CAUSES POSTSCRIPT SPEED PROBLEMS   
							* NEW WAY using Format24bppRgb ELIMINATES ALPHA CHANNEL
							******************************************************************/
							Bitmap bitmap;
							if (!bBackOrientationDiffers) bitmap = new Bitmap(width, height, System.Drawing.Imaging.PixelFormat.Format24bppRgb);
							else bitmap = new Bitmap(height, width, System.Drawing.Imaging.PixelFormat.Format24bppRgb);

							Graphics gr = Graphics.FromImage(bitmap);
							gr.Clear(System.Drawing.Color.White);
							/**********************************************/

							// build bmTemp for the current side
							design.RipSideToGDI(m_iCurrentPage, gr, dScale, DCSDesign.DCSDesign.RipMode.RIPMODE_PRINT, m_bcDatum.m_IfPrintBackground, true, true /* back fore text*/);
							gr.Dispose();
							Application.DoEvents();

							// syh note: user should have choice of rotate left or right
							if (bBackOrientationDiffers)
								bitmap.RotateFlip(System.Drawing.RotateFlipType.Rotate90FlipNone);

							if (m_bcDatum.m_IfMirror)
							{
								bitmap.RotateFlip(RotateFlipType.RotateNoneFlipX);
							}

							// if badge orientation is not same as sheets badge orientation apply a 90 degree rotation
							bool bRotateForSheetAlignment = false;
							int designBoundsWidth;
							int designBoundsHeight;
							if (m_iBadgeW > m_iBadgeH && design.Bounds.Width < design.Bounds.Height) bRotateForSheetAlignment = true;
							else if (m_iBadgeW < m_iBadgeH && design.Bounds.Width > design.Bounds.Height) bRotateForSheetAlignment = true;
							if (!bRotateForSheetAlignment)
							{
								designBoundsWidth = design.Bounds.Width;
								designBoundsHeight = design.Bounds.Height;
							}
							else
							{
								bitmap.RotateFlip(System.Drawing.RotateFlipType.Rotate270FlipNone);
								designBoundsWidth = design.Bounds.Height;
								designBoundsHeight = design.Bounds.Width;
							}

							xLoc = m_iFirstX + ix * m_iDeltaX + design.Bounds.X;
							yLoc = m_iFirstY + iy * m_iDeltaY + design.Bounds.Y;
							if (m_iCurrentPage > 0)
							{
								// Only do this if m_rectPrinterBounds.Width is not rediculous.
								// A necessay test because values from XID printer are not reliable
								if (m_rectPrinterBounds.Width >= 200 && m_rectPrinterBounds.Width < 20000)
								{
									xLoc = m_rectPrinterBounds.Width - xLoc - designBoundsWidth;
								}
							}
							Rectangle rectDst = new Rectangle(xLoc, yLoc, designBoundsWidth, designBoundsHeight);
							Rectangle rectSrc = new Rectangle(0, 0, bitmap.Width, bitmap.Height);

							// put bmTemp for current side onto sheet
							ev.Graphics.DrawImage(bitmap, rectDst, rectSrc, System.Drawing.GraphicsUnit.Pixel);
							bitmap.Dispose();
						}
					m_iCurrentPage++;
					ev.HasMorePages = (m_iCurrentPage < m_sidesInSheetBadgeArray);
				}
				else	// not sheet printer
				{
					Application.DoEvents();
					DCSDEV.DCSDesign.DCSDesign design = (DCSDEV.DCSDesign.DCSDesign)m_sheetBadgeArray[0];

					////////////////////////////////////////
					// output the mag stripe text strings //
					////////////////////////////////////////
					if (m_iCurrentPage == 0)
					{
						try
						{
							// output mag tracks if configured to do so
							design.SendMagTracksToDevice(ev.Graphics);
						}
						catch (Exception ex)
						{
							DCSDEV.DCSMsg.Show("ERROR in SendMagTracksToDevice()", ex);
						}
					}

					/////////////////////////////////////////////
					// get badge size scaled and make a bmTemp //
					/////////////////////////////////////////////
					int width, height;
					width = DCSMath.IntTimesDouble(design.Bounds.Width, dScale);
					height = DCSMath.IntTimesDouble(design.Bounds.Height, dScale);

					xLoc = m_bcDatum.m_OffsetX + design.Bounds.X;
					yLoc = m_bcDatum.m_OffsetY + design.Bounds.Y;

					// if back side orientation is not same as front swap dimensions
					// syh note: user should have choice of rotate left or right
					bool bBackOrientationDiffers = false;
					if (m_iCurrentPage != 0)
					{
						Application.DoEvents();
						DCSDesignSide designSide0 = (DCSDesignSide)design.m_designSides[0];
						DCSDesignSide designSide1 = (DCSDesignSide)design.m_designSides[1];
						if (designSide0.SideIsLandscape != designSide1.SideIsLandscape) bBackOrientationDiffers = true;
					}
					// check validity of Kpanel option combinations
					bool bIfDoKpanel = m_bcDatum.m_IfKpanel;
					if (bIfDoKpanel)
					{
						if (bBackOrientationDiffers)
						{
							DCSDEV.DCSMsg.Show("Special K panel handling requires back orientation be the same as front.");
							bIfDoKpanel = false;
						}
						if (m_bcDatum.m_IfMirror)
						{
							DCSDEV.DCSMsg.Show("Special K panel handling is incompatible with mirror image printing.");
							bIfDoKpanel = false;
						}
					}

					// make bmTemp
					Bitmap bitmap;
					/*****************************************************************
					* OLD WAY without Format24bppRgb CAUSES POSTSCRIPT SPEED PROBLEMS   
					* NEW WAY using Format24bppRgb ELIMINATES ALPHA CHANNEL
					******************************************************************/
					if (!bBackOrientationDiffers) bitmap = new Bitmap(width, height, System.Drawing.Imaging.PixelFormat.Format24bppRgb);
					else bitmap = new Bitmap(height, width, System.Drawing.Imaging.PixelFormat.Format24bppRgb);

					Graphics gr = Graphics.FromImage(bitmap);
					gr.Clear(System.Drawing.Color.White);

					// build bmTemp for the current side
					if (!bIfDoKpanel)
						design.RipSideToGDI(m_iCurrentPage, gr, dScale, DCSDesign.DCSDesign.RipMode.RIPMODE_PRINT, m_bcDatum.m_IfPrintBackground, true, true /* back fore text*/);
					else
						design.RipSideToGDI(m_iCurrentPage, gr, dScale, DCSDesign.DCSDesign.RipMode.RIPMODE_PRINT_NONK, m_bcDatum.m_IfPrintBackground, true, true /* back fore text*/);
					gr.Dispose();
					Application.DoEvents();

					// k panel printing is disabled if either bBackOrientationDiffers or m_IfMirror
					// syh note: user should have choice of rotate left or right
					if (bBackOrientationDiffers)
						bitmap.RotateFlip(System.Drawing.RotateFlipType.Rotate90FlipNone);
					if (m_bcDatum.m_IfMirror)
						bitmap.RotateFlip(RotateFlipType.RotateNoneFlipX);

					////////////////////////////////////////////
					// put bmTemp for current side onto media //
					////////////////////////////////////////////
					Rectangle rectDst = new Rectangle(new Point(xLoc, yLoc), design.Bounds.Size);
					Rectangle rectSrc = new Rectangle(0, 0, bitmap.Width, bitmap.Height);

					ev.Graphics.DrawImage(bitmap, rectDst, rectSrc, System.Drawing.GraphicsUnit.Pixel);
					bitmap.Dispose();

					if (bIfDoKpanel)
					{
						ev.Graphics.TranslateTransform(xLoc, yLoc);
						design.RipSideToGDI(m_iCurrentPage, ev.Graphics, 1.0, DCSDesign.DCSDesign.RipMode.RIPMODE_PRINT_KIMAGE, false, true, false /* back fore text*/);
						design.RipSideToGDI(m_iCurrentPage, ev.Graphics, 1.0, DCSDesign.DCSDesign.RipMode.RIPMODE_PRINT_KTEXT, false, false, true /* back fore text*/);
						ev.Graphics.ResetTransform();
					}

					// test for more pages
					m_iCurrentPage++;
					ev.HasMorePages = (m_iCurrentPage < design.m_designSides.Count);
				}
			}
			catch (Exception ex)
			{
				DCSDEV.DCSMsg.Show(ex);
			}
		}

		// Return DialogResult.OK, Cancel, Retry
		private DialogResult AdvanceToEncodeStation()
		{
			DCSDEV.DCSMsg.Show("SYH TODO: AdvanceToEncodeStation");

			/**************************************************************
			0 - ELTRON/POLAROID_CONTACT encoding station
			1 - ELTRON/POLAROID_CONTACTLESS
			2 - FARGO_CONTACT encoding station
			3 - FARGO_CONTACTLESS

			"\x06\x00\x1bMS\x0d",
			"\x01\x01\x1bMI\x0d\x1bMM 1700 0\x0d",
			"\x0d\x00\x1b\x00\x02S1\x1b\x00\x02i",
			"\x0d\x00\x1b\x00\x02P1\x1b\x00\x02i"

			"\x06\x00\x1bMI\x0d",	// not needed 
			"\x0d\x00\x1bMM 1700 1\x0d",
			"",
			""
			
			******************************************************/
			return DialogResult.OK;
		}
		private DialogResult AdvanceToPrintStation()
		{
			DCSDEV.DCSMsg.Show("SYH TODO: AdvanceToPrintStation");
			return DialogResult.OK;
		}
		private void EjectCard()
		{
			/*************************************
			0 - ELTRON/POLAROID_CONTACT encoding station
			1 - ELTRON/POLAROID_CONTACTLESS
			2 - FARGO_CONTACT encoding station
			3 - FARGO_CONTACTLESS

			"\x06\x00\x1bMO\x0d",
			"\x06\x00\x1bME\x0d",
			"\x06\x00\x1b\x00\x02e",
			"\x06\x00\x1b\x00\x02e"
			***************************************/

			DCSMsg.Show("SYH TODO: Eject card");
		}
	}
}
