using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using System.Windows.Forms;

namespace DCSDEV
{
	/// <summary>
	/// Summary description for ScanBarcode.
	/// </summary>
	public class ScanBarcode : System.Windows.Forms.Form
	{
		string m_strPort;
#if CharonSerial
		Charon.Communications.SerialConnection m_cc = null;
#endif
		private System.Windows.Forms.Button buttonOK;
		private System.Windows.Forms.Button buttonCancel;
		private System.Windows.Forms.Button buttonReInitialize;
		private System.Windows.Forms.TextBox textBoxScanner;
		private System.Windows.Forms.Label labelMain;
		private System.Windows.Forms.Button buttonScan;
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.Container components = null;

		public ScanBarcode(string strPort)
		{
			//
			// Required for Windows Form Designer support
			//
			InitializeComponent();

			//
			// TODO: Add any constructor code after InitializeComponent call
			//
			m_strPort = strPort;
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if( disposing )
			{
				if(components != null)
				{
					components.Dispose();
				}
#if CharonSerial
				if (m_cc != null && m_cc.IsOpen) m_cc.Close();
#endif
			}
			base.Dispose( disposing );
		}

		#region Windows Form Designer generated code
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ScanBarcode));
			this.buttonOK = new System.Windows.Forms.Button();
			this.buttonCancel = new System.Windows.Forms.Button();
			this.buttonReInitialize = new System.Windows.Forms.Button();
			this.textBoxScanner = new System.Windows.Forms.TextBox();
			this.labelMain = new System.Windows.Forms.Label();
			this.buttonScan = new System.Windows.Forms.Button();
			this.SuspendLayout();
			// 
			// buttonOK
			// 
			resources.ApplyResources(this.buttonOK, "buttonOK");
			this.buttonOK.DialogResult = System.Windows.Forms.DialogResult.OK;
			this.buttonOK.Name = "buttonOK";
			this.buttonOK.Click += new System.EventHandler(this.buttonOK_Click);
			// 
			// buttonCancel
			// 
			resources.ApplyResources(this.buttonCancel, "buttonCancel");
			this.buttonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.buttonCancel.Name = "buttonCancel";
			this.buttonCancel.Click += new System.EventHandler(this.buttonCancel_Click);
			// 
			// buttonReInitialize
			// 
			resources.ApplyResources(this.buttonReInitialize, "buttonReInitialize");
			this.buttonReInitialize.Name = "buttonReInitialize";
			this.buttonReInitialize.Click += new System.EventHandler(this.buttonReInitialize_Click);
			// 
			// textBoxScanner
			// 
			resources.ApplyResources(this.textBoxScanner, "textBoxScanner");
			this.textBoxScanner.Name = "textBoxScanner";
			this.textBoxScanner.Leave += new System.EventHandler(this.textBoxScanner_Leave);
			this.textBoxScanner.Enter += new System.EventHandler(this.textBoxScanner_Enter);
			// 
			// labelMain
			// 
			resources.ApplyResources(this.labelMain, "labelMain");
			this.labelMain.Name = "labelMain";
			// 
			// buttonScan
			// 
			resources.ApplyResources(this.buttonScan, "buttonScan");
			this.buttonScan.Name = "buttonScan";
			this.buttonScan.Click += new System.EventHandler(this.buttonScan_Click);
			// 
			// ScanBarcode
			// 
			this.AcceptButton = this.buttonOK;
			resources.ApplyResources(this, "$this");
			this.CancelButton = this.buttonCancel;
			this.Controls.Add(this.buttonScan);
			this.Controls.Add(this.labelMain);
			this.Controls.Add(this.textBoxScanner);
			this.Controls.Add(this.buttonReInitialize);
			this.Controls.Add(this.buttonCancel);
			this.Controls.Add(this.buttonOK);
			this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
			this.MaximizeBox = false;
			this.MinimizeBox = false;
			this.Name = "ScanBarcode";
			this.TopMost = true;
			this.ResumeLayout(false);
			this.PerformLayout();

		}
		#endregion

		private void buttonOK_Click(object sender, System.EventArgs e)
		{
#if CharonSerial
			if (m_cc != null && m_cc.IsOpen) m_cc.Close();
#endif
			this.Close();
		}

		private void buttonCancel_Click(object sender, System.EventArgs e)
		{
			this.textBoxScanner.Text = "";
#if CharonSerial
			if (m_cc != null && m_cc.IsOpen) m_cc.Close();
#endif
			this.Close();
		}

		private void buttonReInitialize_Click(object sender, System.EventArgs e)
		{
			this.InitSerialConnection();
			this.textBoxScanner.Text = "";
			this.textBoxScanner.Focus();
		}

		private void textBoxScanner_Enter(object sender, System.EventArgs e)
		{
			this.labelMain.Text = "Ready to scan the barcode";
		}

		private void textBoxScanner_Leave(object sender, System.EventArgs e)
		{
			this.labelMain.Text = "Hit Reset to scan again";
		}

		protected override void OnVisibleChanged(EventArgs e)
		{
			base.OnVisibleChanged (e);
			if (this.Visible) this.textBoxScanner.Focus();
		}

		private void buttonScan_Click(object sender, System.EventArgs e)
		{
			this.InitSerialConnection();
            this.textBoxScanner.Text = "";
            this.textBoxScanner.Focus();
        }
	
		private void InitSerialConnection()
		{
#if CharonSerial
			try
			{
				if (m_cc == null)
				{
					m_cc = new Charon.Communications.SerialConnection();
					// "COM2", Charon.Communications.Parity.None, 8, Charon.Communications.StopBits.One, Charon.Communications.FlowControl.None, true, true
					m_cc.PortName = m_strPort;
					m_cc.DataAvailable += new System.EventHandler(this.SerialDataAvailable);
				}
				try
				{
					if (!m_cc.IsOpen) m_cc.Open();
				}
				catch (Exception ex)
				{
					DCSMsg.Show("Cannot open port " + m_cc.PortName, ex);
					return;
				}
			}
			catch (Exception ex)
			{
				DCSMsg.Show(ex);
			}
#endif
		}

#if CharonSerial
		private void SerialDataAvailable(object sender, System.EventArgs e)
		{
			this.textBoxScanner.Text = "";
			this.textBoxScanner.Text = m_cc.ReadToEnd();
			Refresh();
		}
#endif

		public string ScannedText
		{
			get { return this.textBoxScanner.Text; }
		}
	}
}
