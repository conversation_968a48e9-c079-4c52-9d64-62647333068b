using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;

namespace DCSDEV.DCSDesign
{
	class DCSIcaoNameBuilder
	{
		private string[] m_strPrefixes = { "DR", "MR", "MRS" };
		private string[] m_strSuffixes = { "PHD", "MD" };
		private string[] m_strETCSuffixes = null;	//{ "SR", "JR", "II", "III", "IV", "V" };
		private string[] m_strParticles = { "LOS", "LA", "DE" };
 
		private int m_iMax = 39;
		private ArrayList m_aSur;
		private ArrayList m_aGiv;

		internal DCSIcaoNameBuilder(string strSurnames, string strGivenNames, DCSDEV.DCSDesign.DCSIcaoBuilder.IcaoMrzType mrztype)
		{
			System.IO.StreamReader sr;
			string strSR;
			char[] Separator = { '\r', '\n', ' ' };
			ParameterStore ps = new ParameterStore("DCSSDK_Mgt");
			
			string strFile = System.IO.Path.Combine(ps.m_strDCSInstallDirectory, "ICAOPrefixes.Txt");
			if (System.IO.File.Exists(strFile))
			{
				sr = new System.IO.StreamReader(strFile);
				strSR = sr.ReadToEnd();
				sr.Close();
				m_strPrefixes = strSR.Split(Separator);
			}
			else DCSMsg.Show(String.Format("Prefixes file '{0}' does not exist.", strFile));

			strFile = System.IO.Path.Combine(ps.m_strDCSInstallDirectory, "ICAOSuffixes.Txt");
			if (System.IO.File.Exists(strFile))
			{
				sr = new System.IO.StreamReader(strFile);
				strSR = sr.ReadToEnd();
				sr.Close();
				m_strSuffixes = strSR.Split(Separator);
			}
			else DCSMsg.Show(String.Format("Suffixes file '{0}' does not exist.", strFile));

			strFile = System.IO.Path.Combine(ps.m_strDCSInstallDirectory, "ETCSuffixes.Txt");
			if (System.IO.File.Exists(strFile))
			{
				sr = new System.IO.StreamReader(strFile);
				strSR = sr.ReadToEnd();
				sr.Close();
				m_strETCSuffixes = strSR.Split(Separator);
			}

			strFile = System.IO.Path.Combine(ps.m_strDCSInstallDirectory, "ICAOParticles.Txt");
			if (System.IO.File.Exists(strFile))
			{
				sr = new System.IO.StreamReader(strFile);
				strSR = sr.ReadToEnd();
				sr.Close();
				m_strParticles = strSR.Split(Separator);
			}
			else DCSMsg.Show(String.Format("Name particles file '{0}' does not exist.", strFile));

			m_aSur = new ArrayList();
			m_aGiv = new ArrayList();

			if (mrztype == DCSIcaoBuilder.IcaoMrzType.PASSPORT) m_iMax = 39;
			else if (mrztype == DCSIcaoBuilder.IcaoMrzType.TRAVELDOC2) m_iMax = 31;
			else if (mrztype == DCSIcaoBuilder.IcaoMrzType.TRAVELDOC3) m_iMax = 30;

			// Canonize - but first period is converted to space
			string strSur = strSurnames;
			string strGiv = strGivenNames;
			strSur = strSur.Replace(".", " ");
			strSur = DCSIcaoBuilder.Canonize(strSur);
			strGiv = strGiv.Replace(".", " ");
			strGiv = DCSIcaoBuilder.Canonize(strGiv);

			// split names into array of parts on space
			this.SplitNames(strSur, m_aSur);
			this.SplitNames(strGiv, m_aGiv);

			// remove empty parts
			this.RemoveNulls(m_aSur);
			this.RemoveNulls(m_aGiv);

			// remove prefixes and suffixes from surnames and given names according to list
			this.RemoveFromFront(m_aSur, m_strPrefixes);
			this.RemoveFromEnd(m_aSur, m_strSuffixes);
			this.RemoveFromFront(m_aGiv, m_strPrefixes);
			this.RemoveFromEnd(m_aGiv, m_strSuffixes);

			// if names fit -> done
			if (ComputeLen() <= m_iMax) return; 

			// remove particles from given names
			this.RemoveFromFront(m_aGiv, m_strParticles);
			// if names fit -> done AND surnames can be no longer than 25 chars
			if (ComputeLen() <= m_iMax && ComputeLen(m_aSur) <= 25) return;

			// remove particles from surnames
			this.RemoveFromFront(m_aSur, m_strParticles);
			// if names fit -> done AND surnames can be no longer than 25 chars
			if (ComputeLen() <= m_iMax && ComputeLen(m_aSur) <= 25) return;

			// make sure surname is no longer then 25 characters
			this.AbbrevNames(m_aSur, 25);

			// determine if special PYT ETC suffixes are handled
			string strETCSuffix = null;
			if (m_strETCSuffixes != null)
			{
				if (m_aGiv.Count > 0)
				{
					string strLastGiven = (string)m_aGiv[m_aGiv.Count - 1];
					foreach (string str in m_strETCSuffixes)
					{
						if (str == strLastGiven)
						{
							strETCSuffix = strLastGiven;
							break;
						}
					}
				}
			}

			// compute characters available for given names
			int lenSur = this.ComputeLen(m_aSur);
			int lenGiv = this.ComputeLen(m_aGiv);
			int lenSurAvail = m_iMax;
			int lenGivAvail = 0;
			if (lenGiv > 0)
			{
				lenGivAvail = Math.Max(1, m_iMax - this.ComputeLen(m_aSur) - 2);
				if (lenGivAvail < lenGiv)
				{
					if (strETCSuffix == null)
					{
						// abbreviate and/or truncate given names - to fit into available characters
						this.AbbrevNames(m_aGiv, lenGivAvail);
						lenGiv = this.ComputeLen(m_aGiv);
					}
					else
					{
						if (lenGivAvail > strETCSuffix.Length+1)
						{
							// temporarily remove last given name suffix - put it back later so reserve space
							m_aGiv.RemoveAt(m_aGiv.Count - 1);
							lenGivAvail = lenGivAvail - strETCSuffix.Length - 1;

							// abbreviate and/or truncate given names - to fit into available characters
							this.AbbrevNames(m_aGiv, lenGivAvail);
							m_aGiv.Add(strETCSuffix);
						}
						else if (lenGivAvail >= strETCSuffix.Length)
						{
							// room for nothing but the suffix
							m_aGiv.Clear();
							m_aGiv.Add(strETCSuffix);
						}
						else
						{
							// insufficient room for the entire suffix
							// remove suffix and deal with whatever is left
							m_aGiv.RemoveAt(m_aGiv.Count - 1);
							// abbreviate and/or truncate given names - to fit into available characters
							this.AbbrevNames(m_aGiv, lenGivAvail);
						}
						lenGiv = this.ComputeLen(m_aGiv);
					}
				}
				lenSurAvail = m_iMax - lenGiv - 2;
			}
			// if surnames > iNameMax - GivenNames - 2 > truncate surnames to an initial
			this.AbbrevNames(m_aSur, lenSurAvail);
		}

		// if givenNameCount*2 - 1 >= avail use all initials and add chars to each given name starting from the first until avail chars are used
		private void AbbrevNames(ArrayList aNames, int lenAvail)
		{
			if (aNames.Count < 1 || lenAvail < 1) DCSMsg.Show("ERROR 001 in ICAONameBuilder");

			// remove parts if the initial letters will not fit
			while ((aNames.Count * 2) - 1 > lenAvail)
			{
				aNames.RemoveAt(aNames.Count - 1);
			}
			if ((aNames.Count * 2) - 1 > lenAvail) DCSMsg.Show("ERROR 002 in ICAONameBuilder");

			// distribute the available letters among the names
			// starting with first part reserve space for remaining initials
			// and make the part as big as possible
			int lenAvailRemaining = lenAvail;
			int lenAvailThisPart;
			int iPartsRemaining;
			for (int i = 0; i < aNames.Count; i++)
			{
				// remaining parts
				iPartsRemaining = aNames.Count-1-i;
				lenAvailThisPart = lenAvailRemaining - iPartsRemaining * 2; // -(iPartsRemaining > 0 ? 1 : 0);
				if (((string)aNames[i]).Length > lenAvailThisPart) 
					aNames[i] = ((string)aNames[i]).Substring(0, lenAvailThisPart);
				lenAvailRemaining = lenAvailRemaining - ((string)aNames[i]).Length - 1;
			}
		}
		
		private void SplitNames(string strNames, ArrayList aNames)
		{
			int i;
			int iStart = 0;
			aNames.Clear();
			for (i = 0; i < strNames.Length; i++)
			{
				if (strNames[i] == '<')
				{
					if (i == iStart)
					{
						iStart = i + 1;
						continue;
					}
					aNames.Add(strNames.Substring(iStart, i - iStart));
					iStart = i + 1;
					continue;
				}
				else continue;
			}
			if (iStart < i) aNames.Add(strNames.Substring(iStart, i - iStart));
		}
		private void RemoveNulls(ArrayList aNames)
		{
			// scan name particles starting with the first - look for empty strings
			for (int i = 0; i < aNames.Count; i++)
			{
				if (((string)aNames[i]).Length == 0)
				{
					aNames.RemoveAt(i);
				}
			}
		}
		private void RemoveFromFront(ArrayList aNames, string [] parts)
		{
			bool bRemove;
			// scan name particles starting with the first - look for prefix strings
			for (int i=0; (i<aNames.Count) && (aNames.Count>1); i++)
			{
				bRemove = false;
				for (int j = 0; j < parts.Length; j++)
				{
					if ((string)aNames[i] == parts[j])
					{
						bRemove = true;
						break;
					}
				}
				if (bRemove)
				{
					aNames.RemoveAt(i);
					i--;
				}
				else break;	// stop when first non-prefix is reached
			}
		}
		private void RemoveFromEnd(ArrayList aNames, string [] parts)
		{
			bool bRemove;
			// scan name particles starting with the first - look for prefix strings
			for (int i = aNames.Count - 1; (i >= 0) && (aNames.Count > 1); i--)
			{
				bRemove = false;
				for (int j = 0; j < parts.Length; j++)
				{
					if ((string)aNames[i] == parts[j])
					{
						bRemove = true;
						break;
					}
				}
				if (bRemove)
				{
					aNames.RemoveAt(i);
				}
				else break;	// stop when first non-suffix is reached
			}
		}
		private int ComputeLen()
		{
			return this.ComputeLen(m_aSur) + this.ComputeLen(m_aGiv) + 2;
		}
		private int ComputeLen(ArrayList aNames)
		{
			int iLen = 0;
			// scan name particles starting with the first - look for empty strings
			for (int i = 0; i < aNames.Count; i++)
			{
				if (iLen > 0) iLen++;
				iLen += ((string)aNames[i]).Length;
			}
			return iLen;
		}
		private string BuildICAOString()
		{
			string str = "";
			for (int i = 0; i < m_aSur.Count; i++)
			{
				if (i > 0) str = str + "<";
				str = str + (string)m_aSur[i];
			}
			str = str + "<<";
			for (int i = 0; i < m_aGiv.Count; i++)
			{
				if (i > 0) str = str + "<";
				str = str + (string)m_aGiv[i];
			}
			return str;
		}
		internal string ICAOName
		{
			get { return DCSIcaoBuilder.KeepSize(this.BuildICAOString(), m_iMax); }
		}
	}
}
