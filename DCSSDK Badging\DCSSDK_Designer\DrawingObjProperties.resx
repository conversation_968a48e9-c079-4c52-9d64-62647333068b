<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonCancel.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="buttonCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>480, 400</value>
  </data>
  <data name="buttonCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 24</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="buttonCancel.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="buttonCancel.Text" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Name" xml:space="preserve">
    <value>buttonCancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonCancel.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="buttonAccept.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonAccept.Location" type="System.Drawing.Point, System.Drawing">
    <value>480, 360</value>
  </data>
  <data name="buttonAccept.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 24</value>
  </data>
  <data name="buttonAccept.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="buttonAccept.Text" xml:space="preserve">
    <value>&amp;OK- apply and close</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Name" xml:space="preserve">
    <value>buttonAccept</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonAccept.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonAccept.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="buttonApply.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonApply.Location" type="System.Drawing.Point, System.Drawing">
    <value>480, 320</value>
  </data>
  <data name="buttonApply.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 24</value>
  </data>
  <data name="buttonApply.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="buttonApply.Text" xml:space="preserve">
    <value>&amp;Apply</value>
  </data>
  <data name="&gt;&gt;buttonApply.Name" xml:space="preserve">
    <value>buttonApply</value>
  </data>
  <data name="&gt;&gt;buttonApply.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonApply.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonApply.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <metadata name="colorDialog1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="dcsPositionSizeProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>408, 24</value>
  </data>
  <data name="dcsPositionSizeProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 128</value>
  </data>
  <data name="dcsPositionSizeProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;dcsPositionSizeProperties1.Name" xml:space="preserve">
    <value>dcsPositionSizeProperties1</value>
  </data>
  <data name="&gt;&gt;dcsPositionSizeProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSPositionSizeProperties, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsPositionSizeProperties1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;dcsPositionSizeProperties1.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>40, 32</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 16</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>Field type</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="tbObjectType.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 32</value>
  </data>
  <data name="tbObjectType.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 20</value>
  </data>
  <data name="tbObjectType.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="tbObjectType.Text" xml:space="preserve">
    <value>unknown</value>
  </data>
  <data name="&gt;&gt;tbObjectType.Name" xml:space="preserve">
    <value>tbObjectType</value>
  </data>
  <data name="&gt;&gt;tbObjectType.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tbObjectType.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tbObjectType.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="dcsBackGroundProperties1.Location" type="System.Drawing.Point, System.Drawing">
    <value>32, 123</value>
  </data>
  <data name="dcsBackGroundProperties1.Size" type="System.Drawing.Size, System.Drawing">
    <value>207, 118</value>
  </data>
  <data name="dcsBackGroundProperties1.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;dcsBackGroundProperties1.Name" xml:space="preserve">
    <value>dcsBackGroundProperties1</value>
  </data>
  <data name="&gt;&gt;dcsBackGroundProperties1.Type" xml:space="preserve">
    <value>DCSDEV.DCSDesigner.DCSBackGroundProperties, DCSSDK_Designer, Version=1.9.1015.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;dcsBackGroundProperties1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;dcsBackGroundProperties1.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="numericUpDownTransparency.Location" type="System.Drawing.Point, System.Drawing">
    <value>40, 296</value>
  </data>
  <data name="numericUpDownTransparency.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="numericUpDownTransparency.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="numericUpDownTransparency.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;numericUpDownTransparency.Name" xml:space="preserve">
    <value>numericUpDownTransparency</value>
  </data>
  <data name="&gt;&gt;numericUpDownTransparency.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;numericUpDownTransparency.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;numericUpDownTransparency.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>104, 296</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 24</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>Transparency</value>
  </data>
  <data name="label4.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="label4.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="buttonEditVisibleIf.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>System</value>
  </data>
  <data name="buttonEditVisibleIf.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 8.25pt, style=Bold</value>
  </data>
  <data name="buttonEditVisibleIf.Location" type="System.Drawing.Point, System.Drawing">
    <value>336, 76</value>
  </data>
  <data name="buttonEditVisibleIf.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 20</value>
  </data>
  <data name="buttonEditVisibleIf.TabIndex" type="System.Int32, mscorlib">
    <value>62</value>
  </data>
  <data name="buttonEditVisibleIf.Text" xml:space="preserve">
    <value>&gt;</value>
  </data>
  <data name="&gt;&gt;buttonEditVisibleIf.Name" xml:space="preserve">
    <value>buttonEditVisibleIf</value>
  </data>
  <data name="&gt;&gt;buttonEditVisibleIf.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonEditVisibleIf.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonEditVisibleIf.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="textBoxVisibleIf.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 76</value>
  </data>
  <data name="textBoxVisibleIf.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 20</value>
  </data>
  <data name="textBoxVisibleIf.TabIndex" type="System.Int32, mscorlib">
    <value>61</value>
  </data>
  <data name="textBoxVisibleIf.Text" xml:space="preserve">
    <value>specify condition</value>
  </data>
  <data name="&gt;&gt;textBoxVisibleIf.Name" xml:space="preserve">
    <value>textBoxVisibleIf</value>
  </data>
  <data name="&gt;&gt;textBoxVisibleIf.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxVisibleIf.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBoxVisibleIf.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="checkBoxVisibleIf.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBoxVisibleIf.Location" type="System.Drawing.Point, System.Drawing">
    <value>40, 76</value>
  </data>
  <data name="checkBoxVisibleIf.Size" type="System.Drawing.Size, System.Drawing">
    <value>77, 17</value>
  </data>
  <data name="checkBoxVisibleIf.TabIndex" type="System.Int32, mscorlib">
    <value>60</value>
  </data>
  <data name="checkBoxVisibleIf.Text" xml:space="preserve">
    <value>Visible If ...</value>
  </data>
  <data name="&gt;&gt;checkBoxVisibleIf.Name" xml:space="preserve">
    <value>checkBoxVisibleIf</value>
  </data>
  <data name="&gt;&gt;checkBoxVisibleIf.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBoxVisibleIf.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;checkBoxVisibleIf.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>45</value>
  </metadata>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>633, 447</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterParent</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Drawing Object Properties ---</value>
  </data>
  <data name="&gt;&gt;colorDialog1.Name" xml:space="preserve">
    <value>colorDialog1</value>
  </data>
  <data name="&gt;&gt;colorDialog1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColorDialog, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>DrawingObjProperties</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>