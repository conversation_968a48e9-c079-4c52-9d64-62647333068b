using System;
using System.Collections.Generic;
using System.Windows.Forms;

///added 6/15/2012
///
using System.Runtime.InteropServices;
using System.Diagnostics;
////

namespace DCSDEV.DDEServer
{
    static class Program
    {

///added 6/15/2012
///
        [DllImport("User32.dll")]
        static extern IntPtr SetForegroundWindow(IntPtr hWnd);

        [DllImport("User32.dll")]
        static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);
///
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
///added 6/15/2012
///

            Process procThis = Process.GetCurrentProcess();	 	// get this process's name. "ID Services" is normal but it might be changed.
            Process[] procs = Process.GetProcessesByName(procThis.ProcessName);	//Application.ProductName);
            if (procs.Length > 1) // This code should prevent the number from ever getting above 2.
            {
                // the previously running instance will be at either index 0 or 1
                int index;
                if ((int)procs[0].MainWindowHandle != 0) index = 0;
                else index = 1;
                SetForegroundWindow(procs[index].MainWindowHandle);
                //ShowWindow is necessary to restore a minimized window - I think I want to leave it the way the opr left it.
                //ShowWindow(procs[index].MainWindowHandle, 9);
                ShowWindow(procs[index].MainWindowHandle, 1);
                return;
            }
//////////////////////
            Application.Run(new Form1());
        }
    }
}